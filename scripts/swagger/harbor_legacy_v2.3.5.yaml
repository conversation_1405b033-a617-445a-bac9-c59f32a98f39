swagger: '2.0'
info:
  title: Harbor API
  description: These APIs provide services for manipulating Harbor project.
  version: '2.0'
host: localhost
schemes:
  - http
  - https
basePath: /api/v2.0
produces:
  - application/json
  - text/plain
consumes:
  - application/json
securityDefinitions:
  basicAuth:
    type: basic
security:
  - basicAuth: []
paths:
  /email/ping:
    post:
      summary: Test connection and authentication with email server.
      description: |
        Test connection and authentication with email server.
      parameters:
        - name: settings
          in: body
          description: 'Email server settings, if some of the settings are not assigned, they will be read from system configuration.'
          required: false
          schema:
            $ref: '#/definitions/EmailServerSetting'
      tags:
        - Products
      responses:
        '200':
          description: Ping email server successfully.
        '400':
          description: Inviald email server settings.
        '401':
          description: User need to login first.
        '403':
          description: Only admin has this authority.
        '415':
          $ref: '#/responses/UnsupportedMediaType'
        '500':
          description: Unexpected internal errors.
  /chartrepo/{repo}/charts/{name}/{version}/labels:
    get:
      summary: Return the attahced labels of chart.
      description: Return the attahced labels of the specified chart version.
      tags:
        - Products
        - Chart Repository
        - Label
      parameters:
        - name: repo
          in: path
          type: string
          required: true
          description: The project name
        - name: name
          in: path
          type: string
          required: true
          description: The chart name
        - name: version
          in: path
          type: string
          required: true
          description: The chart version
      responses:
        '200':
          $ref: '#/definitions/Labels'
        '401':
          $ref: '#/definitions/UnauthorizedChartAPIError'
        '403':
          $ref: '#/definitions/ForbiddenChartAPIError'
        '404':
          $ref: '#/definitions/NotFoundChartAPIError'
        '500':
          $ref: '#/definitions/InternalChartAPIError'
    post:
      summary: Mark label to chart.
      description: Mark label to the specified chart version.
      tags:
        - Products
        - Chart Repository
        - Label
      parameters:
        - name: repo
          in: path
          type: string
          required: true
          description: The project name
        - name: name
          in: path
          type: string
          required: true
          description: The chart name
        - name: version
          in: path
          type: string
          required: true
          description: The chart version
        - name: label
          in: body
          required: true
          schema:
            $ref: '#/definitions/Label'
          description: 'The label being marked to the chart version'
      responses:
        '200':
          description: The label is successfully marked to the chart version.
        '400':
          $ref: '#/definitions/BadRequestFormatedError'
        '401':
          $ref: '#/definitions/UnauthorizedChartAPIError'
        '403':
          $ref: '#/definitions/ForbiddenChartAPIError'
        '404':
          $ref: '#/definitions/NotFoundChartAPIError'
        '409':
          $ref: '#/definitions/ConflictFormatedError'
        '500':
          $ref: '#/definitions/InternalChartAPIError'
  /chartrepo/{repo}/charts/{name}/{version}/labels/{id}:
    delete:
      summary: Remove label from chart.
      description: Remove label from the specified chart version.
      tags:
        - Products
        - Chart Repository
        - Label
      parameters:
        - name: repo
          in: path
          type: string
          required: true
          description: The project name
        - name: name
          in: path
          type: string
          required: true
          description: The chart name
        - name: version
          in: path
          type: string
          required: true
          description: The chart version
        - name: id
          in: path
          type: integer
          required: true
          description: The label ID
      responses:
        '200':
          description: The label is successfully unmarked from the chart version.
        '400':
          $ref: '#/definitions/BadRequestFormatedError'
        '401':
          $ref: '#/definitions/UnauthorizedChartAPIError'
        '403':
          $ref: '#/definitions/ForbiddenChartAPIError'
        '404':
          $ref: '#/definitions/NotFoundChartAPIError'
        '500':
          $ref: '#/definitions/InternalChartAPIError'
responses:
  OK:
    description: 'Success'
  Created:
    description: 'Created'
    headers:
      Location:
        type: string
        description: The URL of the created resource
  BadRequest:
    description: 'Bad Request'
  Unauthorized:
    description: 'Unauthorized'
  Forbidden:
    description: 'Forbidden'
  NotFound:
    description: 'Not Found'
  Conflict:
    description: 'Conflict'
  PreconditionFailed:
    description: 'Precondition Failed'
  UnsupportedMediaType:
    description: 'The Media Type of the request is not supported, it has to be "application/json"'
  InternalServerError:
    description: 'Internal Server Error'
definitions:
  EmailServerSetting:
    type: object
    properties:
      email_host:
        type: string
        description: The host of email server.
      email_port:
        type: integer
        description: The port of email server.
      email_username:
        type: string
        description: The username of email server.
      email_password:
        type: string
        description: The password of email server.
      email_ssl:
        type: boolean
        description: Use ssl/tls or not.
      email_identity:
        type: string
        description: The dentity of email server.
  ComponentOverviewEntry:
    type: object
    properties:
      severity:
        type: integer
        description: '1-None/Negligible, 2-Unknown, 3-Low, 4-Medium, 5-High'
      count:
        type: integer
        description: number of the components with certain severity.
  Label:
    type: object
    properties:
      id:
        type: integer
        description: The ID of label.
      name:
        type: string
        description: The name of label.
      description:
        type: string
        description: The description of label.
      color:
        type: string
        description: The color of label.
      scope:
        type: string
        description: 'The scope of label, g for global labels and p for project labels.'
      project_id:
        type: integer
        description: The project ID if the label is a project label.
      creation_time:
        type: string
        description: The creation time of label.
      update_time:
        type: string
        description: The update time of label.
      deleted:
        type: boolean
        description: The label is deleted or not.
  ChartAPIError:
    description: The error object returned by chart repository API
    type: object
    required:
      - error
    properties:
      error:
        type: string
        description: The error message returned by the chart API
  UnauthorizedChartAPIError:
    description: Unauthorized
    type: object
    allOf:
      - $ref: '#/definitions/ChartAPIError'
  ForbiddenChartAPIError:
    description: Operation is forbidden or quota exceeded
    type: object
    allOf:
      - $ref: '#/definitions/ChartAPIError'
  InternalChartAPIError:
    description: Internal server error occurred
    type: object
    allOf:
      - $ref: '#/definitions/ChartAPIError'
  NotFoundChartAPIError:
    description: Not found
    type: object
    allOf:
      - $ref: '#/definitions/ChartAPIError'
  InsufficientStorageChartAPIError:
    description: Insufficient storage
    type: object
    allOf:
      - $ref: '#/definitions/ChartAPIError'
  BadRequestFormatedError:
    description: Bad request
    type: object
    allOf:
      - $ref: '#/definitions/ChartAPIError'
  ConflictFormatedError:
    description: Conflicts
    type: object
    allOf:
      - $ref: '#/definitions/ChartAPIError'
  ChartMetadata:
    type: object
    description: The metadata of chart version
    required:
      - name
      - version
      - engine
      - icon
      - apiVersion
      - appVersion
    properties:
      name:
        type: string
        description: The name of the chart
      home:
        type: string
        description: The URL to the relevant project page
      sources:
        type: array
        description: The URL to the source code of chart
        items:
          type: string
      version:
        type: string
        description: A SemVer 2 version of chart
      description:
        type: string
        description: A one-sentence description of chart
      keywords:
        type: array
        description: A list of string keywords
        items:
          type: string
      engine:
        type: string
        description: The name of template engine
      icon:
        type: string
        description: The URL to an icon file
      apiVersion:
        type: string
        description: The API version of this chart
      appVersion:
        type: string
        description: The version of the application enclosed in the chart
      deprecated:
        type: boolean
        description: Whether or not this chart is deprecated
  ChartVersion:
    type: object
    description: A specified chart entry
    allOf:
      - $ref: '#/definitions/ChartMetadata'
      - type: object
        properties:
          created:
            type: string
            description: The created time of the chart entry
          removed:
            type: boolean
            description: A flag to indicate if the chart entry is removed
          digest:
            type: string
            description: The digest value of the chart entry
          urls:
            type: array
            description: The urls of the chart entry
            items:
              type: string
    properties:
      labels:
        $ref: '#/definitions/Labels'
  Labels:
    type: array
    description: A list of label
    items:
      $ref: '#/definitions/Label'
  Permission:
    type: object
    description: The permission
    properties:
      resource:
        type: string
        description: The permission resoruce
      action:
        type: string
        description: The permission action
  Namespace:
    type: object
    description: The namespace of registry
    properties:
      name:
        type: string
        description: The name of namespace
      metadata:
        type: object
        description: The metadata of namespace
