// Code generated by go-swagger; DO NOT EDIT.


{{ if .Copyright -}}// {{ comment .Copyright -}}{{ end }}


package {{ .Name }}

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
  "fmt"
  "io"
  "net/http"

  "github.com/go-openapi/errors"
  "github.com/go-openapi/runtime"
  "github.com/go-openapi/strfmt"
  "github.com/go-openapi/swag"
  "github.com/go-openapi/validate"

  {{ imports .DefaultImports }}
  {{ imports .Imports }}
)

// New creates a new {{ humanize .Name }} API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
  return &Client{transport: transport, formats: formats}
}

/*
Client {{ if .Summary }}{{ .Summary }}{{ if .Description }}

{{ blockcomment .Description }}{{ end }}{{ else if .Description}}{{ blockcomment .Description }}{{ else }}for {{ humanize .Name }} API{{ end }}
*/
type Client struct {
  transport runtime.ClientTransport
  formats strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

//go:generate mockery --name ClientService --structname Mock{{ pascalize .Name }}ClientService

// ClientService is the interface for Client methods
type ClientService interface {
	{{ range .Operations }}
	{{ pascalize .Name }}(params *{{ pascalize .Name }}Params{{ if .Authorized }}, authInfo runtime.ClientAuthInfoWriter{{end}}{{ if .HasStreamingResponse }}, writer io.Writer{{ end }}, opts ...ClientOption) {{ if .SuccessResponse }}({{ range .SuccessResponses }}*{{ pascalize .Name }}, {{ end }}{{ end }}error{{ if .SuccessResponse }}){{ end }}
	{{ end }}

	SetTransport(transport runtime.ClientTransport)
}

{{ range .Operations }}
/*
  {{ pascalize .Name }} {{ if .Summary }}{{ pluralizeFirstWord (humanize .Summary) }}{{ if .Description }}

  {{ blockcomment .Description }}{{ end }}{{ else if .Description}}{{ blockcomment .Description }}{{ else }}{{ humanize .Name }} API{{ end }}
*/
func (a *Client) {{ pascalize .Name }}(params *{{ pascalize .Name }}Params{{ if .Authorized }}, authInfo runtime.ClientAuthInfoWriter{{end}}{{ if .HasStreamingResponse }}, writer io.Writer{{ end }}, opts ...ClientOption) {{ if .SuccessResponse }}({{ range .SuccessResponses }}*{{ pascalize .Name }}, {{ end }}{{ end }}error{{ if .SuccessResponse }}){{ end }} {
  // TODO: Validate the params before sending
  if params == nil {
    params = New{{ pascalize .Name }}Params()
  }
  op := &runtime.ClientOperation{
    ID: {{ printf "%q" .Name }},
    Method: {{ printf "%q" .Method }},
    PathPattern: {{ printf "%q" .Path }},
    ProducesMediaTypes: {{ printf "%#v" .ProducesMediaTypes }},
    ConsumesMediaTypes: {{ printf "%#v" .ConsumesMediaTypes }},
    Schemes: {{ printf "%#v" .Schemes }},
    Params: params,
    Reader: &{{ pascalize .Name }}Reader{formats: a.formats{{ if .HasStreamingResponse }}, writer: writer{{ end }}},{{ if .Authorized }}
    AuthInfo: authInfo,{{ end}}
    Context: params.Context,
    Client: params.HTTPClient,
  }
	for _, opt := range opts {
		opt(op)
	}
  {{ $length := len .SuccessResponses }}
  {{ if .SuccessResponse }}result{{else}}_{{ end }}, err := a.transport.Submit(op)
  if err != nil {
    return {{ if .SuccessResponse }}{{ padSurround "nil" "nil" 0 $length }}, {{ end }}err
  }
  {{- if .SuccessResponse }}
    {{- if eq $length 1 }}
  success, ok := result.(*{{ pascalize .SuccessResponse.Name }})
  if ok {
    return success,nil
  }
  // unexpected success response
      {{- if .DefaultResponse }}{{/* if a default response is provided, fill this and return an error */}}
  unexpectedSuccess := result.(*{{ pascalize .DefaultResponse.Name }})
  return nil, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
      {{- else }}
  // safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
  msg := fmt.Sprintf("unexpected success response for {{ .Name }}: API contract not enforced by server. Client expected to get an error, but got: %T", result)
  panic(msg)
      {{- end }}
    {{- else }}{{/* several possible success responses */}}
  switch value := result.(type) {
      {{- range $i, $v := .SuccessResponses }}
  case *{{ pascalize $v.Name }}:
    return {{ padSurround "value" "nil" $i $length }}, nil
      {{- end }}
  }
      {{- if .DefaultResponse }}{{/* if a default response is provided, fill this and return an error */}}
  // unexpected success response
  unexpectedSuccess := result.(*{{ pascalize .DefaultResponse.Name }})
  return {{ padSurround "nil" "nil" 0 $length }}, runtime.NewAPIError("unexpected success response: content available as default response in error", unexpectedSuccess, unexpectedSuccess.Code())
      {{- else }}
  // safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
  msg := fmt.Sprintf("unexpected success response for {{ $.Name }}: API contract not enforced by server. Client expected to get an error, but got: %T", result)
  panic(msg)
      {{- end }}
    {{- end }}
  {{- else }}
  return nil
  {{- end }}
}
{{- end }}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
  a.transport = transport
}