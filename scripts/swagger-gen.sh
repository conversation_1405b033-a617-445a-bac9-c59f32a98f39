#!/usr/bin/env bash
GOSWAGGER_IMAGE="registry.baidubce.com/goswagger/swagger:${1}"
# go-swaggers documentation on swagger operations at the moment is really sparse,
# so here is a bit of explanation from code observations.
# go-swagger accepts operation names from command line "--operation" flag, to filter which operations to generate.
# It tries to match by operationId of a swagger path, if set.
# If this doesn't match, it tries a match on a generated name using the operation method + path.
# This dynamically constructed name is a golint-compatible method name, which is best explained
# by looking at some examples:
#
# GET  /projects                --> GetProjects
# PUT  /projects/{project_id}   --> PutProjectsProjectID
# POST /chartrepo/{repo}/charts --> PostChartrepoRepoCharts
#
# WARNING: When adding new operations, make sure they really get created, since go-swagger DOES NOT complain about
# not finding the operation.
# Also it is not possible at the moment to list dynamically generated names from a swagger-file with go-swagger.
#
# see also: https://swagger.io/docs/specification/paths-and-operations/


HARBOR_LEGACY_FILE="scripts/swagger/harbor_legacy_v2.3.5.yaml"
HARBOR_FILE="scripts/swagger/harbor_v2.3.5.yaml"
HARBOR_CHART_FILE="scripts/swagger/harbor_chart.yaml"
HARBOR_ADDON_FILE="services/harbor-addon/docs/swagger.yaml"
echo "generating harbor client API"

# Generate client using the Harbor v2 legacy API
echo "generate client using the Harbor v2 legacy API"
docker run --platform=linux/amd64 --rm -v "$(pwd):/go/src/ccr-stack" -w "/go/src/ccr-stack" "${GOSWAGGER_IMAGE}" \
generate client \
-q \
--skip-validation \
--template-dir="scripts/swagger/templates" \
--model-package="pkg/harbor/model/legacy" \
--name="harbor" \
--client-package="pkg/harbor/legacyapi/client" \
--spec="${HARBOR_LEGACY_FILE}"

# Generate client using the Harbor v2 API
echo "generate client using the Harbor v2 API"
docker run --platform=linux/amd64 --rm -v "$(pwd):/go/src/ccr-stack" -w "/go/src/ccr-stack" "${GOSWAGGER_IMAGE}" \
generate client \
-q \
--skip-validation \
--template-dir="scripts/swagger/templates" \
--model-package="pkg/harbor/model/" \
--name="harbor" \
--client-package="pkg/harbor/api/client" \
--spec="${HARBOR_FILE}"


# Generate client using the Harbor chart API
echo "generate client using the Harbor chart API"
docker run --platform=linux/amd64 --rm -v "$(pwd):/go/src/ccr-stack" -w "/go/src/ccr-stack" "${GOSWAGGER_IMAGE}" \
generate client \
-q \
--skip-validation \
--template-dir="scripts/swagger/templates" \
--model-package="pkg/harbor/model/" \
--name="harbor" \
--client-package="pkg/harbor/chart/client" \
--spec="${HARBOR_CHART_FILE}"


# Generate client using the Harbor addon API
echo "generate client using the Harbor addon API"
docker run --platform=linux/amd64 --rm -v "$(pwd):/go/src/ccr-stack" -w "/go/src/ccr-stack" "${GOSWAGGER_IMAGE}" \
generate client \
-q \
--skip-validation \
--template-dir="scripts/swagger/templates" \
--model-package="pkg/harbor/model/" \
--name="harbor" \
--client-package="pkg/harbor/addon/client" \
--spec="${HARBOR_ADDON_FILE}"

echo "generating harbor client API completed"