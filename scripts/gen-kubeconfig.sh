#!/bin/bash

configFile=$2
nameSpace=$1
#if [ ! -f $configFile ]; then
#  configFile=="/root/.kube/config"
#fi
echo "ns "
echo ${nameSpace}
echo 'apiVersion: v1
kind: ServiceAccount
metadata:
  name: eks-user-kubeconfig-use
  namespace: '${nameSpace}'
---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: eks-user-kubeconfig-use
  namespace: '${nameSpace}'
rules:
- apiGroups:
  - "*"
  resources:
  - "*"
  verbs:
  - "*"
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: eks-user-kubeconfig-use
  namespace: '${nameSpace}'
subjects:
- kind: ServiceAccount
  name: eks-user-kubeconfig-use
  namespace: '${nameSpace}'
roleRef:
  kind: Role
  name: eks-user-kubeconfig-use
  apiGroup: rbac.authorization.k8s.io' | kubectl --kubeconfig ${configFile} apply -f -

echo "start get server address"
server=$(echo $(grep 'server: https://' ${configFile}))
server=${server: 8}
echo ${server}

# 获取secret名称
echo "start get secret name"
secret_name=$(kubectl --kubeconfig ${configFile} get secret -n ${nameSpace} | grep eks-user-kubeconfig-use | cut -d ' ' -f 1)
echo ${secret_name}

echo "start get ca and token"
ca=$(kubectl --kubeconfig ${configFile} get secrets ${secret_name} -n ${nameSpace} -o jsonpath='{.data.ca\.crt}')
token=$(kubectl --kubeconfig ${configFile} get secrets ${secret_name} -n ${nameSpace} -o jsonpath='{.data.token}' | base64 -d)

# 生成kubeconfig
cat << EOF > ${1}.kc
apiVersion: v1
kind: Config
clusters:
- name: kubernetes
  cluster:
    certificate-authority-data: ${ca}
    server: ${server}
contexts:
- name: eks-user-kubeconfig-use
  context:
    cluster: kubernetes
    user: eks-user-kubeconfig-use
current-context: eks-user-kubeconfig-use
users:
- name: eks-user-kubeconfig-use
  user:
    token: ${token}
EOF