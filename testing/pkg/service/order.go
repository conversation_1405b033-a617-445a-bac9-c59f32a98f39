// Code generated by mockery v2.28.1. DO NOT EDIT.

package service

import (
	context "context"

	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"

	mock "github.com/stretchr/testify/mock"
)

// OrderService is an autogenerated mock type for the OrderServiceInterface type
type OrderService struct {
	mock.Mock
}

// CheckDilatation provides a mock function with given fields: ctx, order
func (_m *OrderService) CheckDilatation(ctx context.Context, order *billing.GetOrderDetail) error {
	ret := _m.Called(ctx, order)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *billing.GetOrderDetail) error); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CheckNew provides a mock function with given fields: ctx, order
func (_m *OrderService) CheckNew(ctx context.Context, order *billing.GetOrderDetail) error {
	ret := _m.Called(ctx, order)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *billing.GetOrderDetail) error); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CheckReNew provides a mock function with given fields: ctx, order
func (_m *OrderService) CheckReNew(ctx context.Context, order *billing.GetOrderDetail) error {
	ret := _m.Called(ctx, order)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *billing.GetOrderDetail) error); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Dilatation provides a mock function with given fields: ctx, order
func (_m *OrderService) Dilatation(ctx context.Context, order *billing.GetOrderDetail) error {
	ret := _m.Called(ctx, order)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *billing.GetOrderDetail) error); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// New provides a mock function with given fields: ctx, order
func (_m *OrderService) New(ctx context.Context, order *billing.GetOrderDetail) error {
	ret := _m.Called(ctx, order)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *billing.GetOrderDetail) error); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Renew provides a mock function with given fields: ctx, order
func (_m *OrderService) Renew(ctx context.Context, order *billing.GetOrderDetail) error {
	ret := _m.Called(ctx, order)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *billing.GetOrderDetail) error); ok {
		r0 = rf(ctx, order)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewOrderService interface {
	mock.TestingT
	Cleanup(func())
}

// NewOrderService creates a new instance of OrderService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewOrderService(t mockConstructorTestingTNewOrderService) *OrderService {
	mock := &OrderService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
