// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/registry (interfaces: RegistryTypeServiceInterface)

// Package registry is a generated GoMock package.
package registry

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	model "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// MockRegistryTypeServiceInterface is a mock of RegistryTypeServiceInterface interface.
type MockRegistryTypeServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockRegistryTypeServiceInterfaceMockRecorder
}

// MockRegistryTypeServiceInterfaceMockRecorder is the mock recorder for MockRegistryTypeServiceInterface.
type MockRegistryTypeServiceInterfaceMockRecorder struct {
	mock *MockRegistryTypeServiceInterface
}

// NewMockRegistryTypeServiceInterface creates a new mock instance.
func NewMockRegistryTypeServiceInterface(ctrl *gomock.Controller) *MockRegistryTypeServiceInterface {
	mock := &MockRegistryTypeServiceInterface{ctrl: ctrl}
	mock.recorder = &MockRegistryTypeServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRegistryTypeServiceInterface) EXPECT() *MockRegistryTypeServiceInterfaceMockRecorder {
	return m.recorder
}

// ListRegistryTypes mocks base method.
func (m *MockRegistryTypeServiceInterface) ListRegistryTypes(arg0 context.Context) map[model.RegistryType]*model.EndpointPattern {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRegistryTypes", arg0)
	ret0, _ := ret[0].(map[model.RegistryType]*model.EndpointPattern)
	return ret0
}

// ListRegistryTypes indicates an expected call of ListRegistryTypes.
func (mr *MockRegistryTypeServiceInterfaceMockRecorder) ListRegistryTypes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRegistryTypes", reflect.TypeOf((*MockRegistryTypeServiceInterface)(nil).ListRegistryTypes), arg0)
}
