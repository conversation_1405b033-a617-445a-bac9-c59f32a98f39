// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/registry (interfaces: RegistryServiceInterface)

// Package registry is a generated GoMock package.
package registry

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	model "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	model0 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// MockRegistryServiceInterface is a mock of RegistryServiceInterface interface.
type MockRegistryServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockRegistryServiceInterfaceMockRecorder
}

// MockRegistryServiceInterfaceMockRecorder is the mock recorder for MockRegistryServiceInterface.
type MockRegistryServiceInterfaceMockRecorder struct {
	mock *MockRegistryServiceInterface
}

// NewMockRegistryServiceInterface creates a new mock instance.
func NewMockRegistryServiceInterface(ctrl *gomock.Controller) *MockRegistryServiceInterface {
	mock := &MockRegistryServiceInterface{ctrl: ctrl}
	mock.recorder = &MockRegistryServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRegistryServiceInterface) EXPECT() *MockRegistryServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateRegistry mocks base method.
func (m *MockRegistryServiceInterface) CreateRegistry(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6, arg7 string, arg8 bool) (*model.Registry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRegistry", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	ret0, _ := ret[0].(*model.Registry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRegistry indicates an expected call of CreateRegistry.
func (mr *MockRegistryServiceInterfaceMockRecorder) CreateRegistry(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRegistry", reflect.TypeOf((*MockRegistryServiceInterface)(nil).CreateRegistry), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
}

// DeleteRegistry mocks base method.
func (m *MockRegistryServiceInterface) DeleteRegistry(arg0 context.Context, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRegistry", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRegistry indicates an expected call of DeleteRegistry.
func (mr *MockRegistryServiceInterfaceMockRecorder) DeleteRegistry(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRegistry", reflect.TypeOf((*MockRegistryServiceInterface)(nil).DeleteRegistry), arg0, arg1)
}

// GetRegistry mocks base method.
func (m *MockRegistryServiceInterface) GetRegistry(arg0 context.Context, arg1 int64) (*model0.RegistryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRegistry", arg0, arg1)
	ret0, _ := ret[0].(*model0.RegistryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRegistry indicates an expected call of GetRegistry.
func (mr *MockRegistryServiceInterfaceMockRecorder) GetRegistry(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRegistry", reflect.TypeOf((*MockRegistryServiceInterface)(nil).GetRegistry), arg0, arg1)
}

// GetRegistryByName mocks base method.
func (m *MockRegistryServiceInterface) GetRegistryByName(arg0 context.Context, arg1, arg2 string) (*model0.RegistryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRegistryByName", arg0, arg1, arg2)
	ret0, _ := ret[0].(*model0.RegistryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRegistryByName indicates an expected call of GetRegistryByName.
func (mr *MockRegistryServiceInterfaceMockRecorder) GetRegistryByName(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRegistryByName", reflect.TypeOf((*MockRegistryServiceInterface)(nil).GetRegistryByName), arg0, arg1, arg2)
}

// ListRegistries mocks base method.
func (m *MockRegistryServiceInterface) ListRegistries(arg0 context.Context, arg1, arg2 string, arg3, arg4 int64) ([]*model0.RegistryResult, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRegistries", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*model0.RegistryResult)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListRegistries indicates an expected call of ListRegistries.
func (mr *MockRegistryServiceInterfaceMockRecorder) ListRegistries(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRegistries", reflect.TypeOf((*MockRegistryServiceInterface)(nil).ListRegistries), arg0, arg1, arg2, arg3, arg4)
}

// PostRegistriesPing mocks base method.
func (m *MockRegistryServiceInterface) PostRegistriesPing(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 string, arg7 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PostRegistriesPing", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(error)
	return ret0
}

// PostRegistriesPing indicates an expected call of PostRegistriesPing.
func (mr *MockRegistryServiceInterfaceMockRecorder) PostRegistriesPing(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostRegistriesPing", reflect.TypeOf((*MockRegistryServiceInterface)(nil).PostRegistriesPing), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// UpdateRegistry mocks base method.
func (m *MockRegistryServiceInterface) UpdateRegistry(arg0 context.Context, arg1 int64, arg2, arg3, arg4, arg5, arg6, arg7 string, arg8 bool) (*model.Registry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRegistry", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	ret0, _ := ret[0].(*model.Registry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRegistry indicates an expected call of UpdateRegistry.
func (mr *MockRegistryServiceInterfaceMockRecorder) UpdateRegistry(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRegistry", reflect.TypeOf((*MockRegistryServiceInterface)(nil).UpdateRegistry), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
}
