// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/gc (interfaces: GCServiceInterface)

// Package gc is a generated GoMock package.
package gc

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	model "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// MockGCServiceInterface is a mock of GCServiceInterface interface.
type MockGCServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockGCServiceInterfaceMockRecorder
}

// MockGCServiceInterfaceMockRecorder is the mock recorder for MockGCServiceInterface.
type MockGCServiceInterfaceMockRecorder struct {
	mock *MockGCServiceInterface
}

// NewMockGCServiceInterface creates a new mock instance.
func NewMockGCServiceInterface(ctrl *gomock.Controller) *MockGCServiceInterface {
	mock := &MockGCServiceInterface{ctrl: ctrl}
	mock.recorder = &MockGCServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGCServiceInterface) EXPECT() *MockGCServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateGCSchedule mocks base method.
func (m *MockGCServiceInterface) CreateGCSchedule(arg0 context.Context, arg1 *model.CreateGCRequest) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGCSchedule", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGCSchedule indicates an expected call of CreateGCSchedule.
func (mr *MockGCServiceInterfaceMockRecorder) CreateGCSchedule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGCSchedule", reflect.TypeOf((*MockGCServiceInterface)(nil).CreateGCSchedule), arg0, arg1)
}

// GetGCLog mocks base method.
func (m *MockGCServiceInterface) GetGCLog(arg0 context.Context, arg1 int64) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGCLog", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGCLog indicates an expected call of GetGCLog.
func (mr *MockGCServiceInterfaceMockRecorder) GetGCLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGCLog", reflect.TypeOf((*MockGCServiceInterface)(nil).GetGCLog), arg0, arg1)
}

// ListGCHistory mocks base method.
func (m *MockGCServiceInterface) ListGCHistory(arg0 context.Context, arg1, arg2 *int64) ([]*model.GCHistoryResult, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListGCHistory", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.GCHistoryResult)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListGCHistory indicates an expected call of ListGCHistory.
func (mr *MockGCServiceInterfaceMockRecorder) ListGCHistory(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGCHistory", reflect.TypeOf((*MockGCServiceInterface)(nil).ListGCHistory), arg0, arg1, arg2)
}
