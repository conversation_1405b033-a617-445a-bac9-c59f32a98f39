// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/immutable (interfaces: Interface)

// Package immutable is a generated GoMock package.
package immutable

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	model "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	model0 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// BatchDeleteImmutableRule mocks base method.
func (m *MockInterface) BatchDeleteImmutableRule(arg0 context.Context, arg1 []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteImmutableRule", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteImmutableRule indicates an expected call of BatchDeleteImmutableRule.
func (mr *MockInterfaceMockRecorder) BatchDeleteImmutableRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteImmutableRule", reflect.TypeOf((*MockInterface)(nil).BatchDeleteImmutableRule), arg0, arg1)
}

// CreateImmutableRule mocks base method.
func (m *MockInterface) CreateImmutableRule(arg0 context.Context, arg1 int64, arg2 *model.ImmutableRule) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateImmutableRule", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateImmutableRule indicates an expected call of CreateImmutableRule.
func (mr *MockInterfaceMockRecorder) CreateImmutableRule(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateImmutableRule", reflect.TypeOf((*MockInterface)(nil).CreateImmutableRule), arg0, arg1, arg2)
}

// DeleteImmutableRule mocks base method.
func (m *MockInterface) DeleteImmutableRule(arg0 context.Context, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteImmutableRule", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteImmutableRule indicates an expected call of DeleteImmutableRule.
func (mr *MockInterfaceMockRecorder) DeleteImmutableRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteImmutableRule", reflect.TypeOf((*MockInterface)(nil).DeleteImmutableRule), arg0, arg1)
}

// GetImmutableRule mocks base method.
func (m *MockInterface) GetImmutableRule(arg0 context.Context, arg1 int64) (*model0.ImmutableRuleResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImmutableRule", arg0, arg1)
	ret0, _ := ret[0].(*model0.ImmutableRuleResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImmutableRule indicates an expected call of GetImmutableRule.
func (mr *MockInterfaceMockRecorder) GetImmutableRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImmutableRule", reflect.TypeOf((*MockInterface)(nil).GetImmutableRule), arg0, arg1)
}

// ImmutableRuleExists mocks base method.
func (m *MockInterface) ImmutableRuleExists(arg0 context.Context, arg1 int64) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImmutableRuleExists", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImmutableRuleExists indicates an expected call of ImmutableRuleExists.
func (mr *MockInterfaceMockRecorder) ImmutableRuleExists(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImmutableRuleExists", reflect.TypeOf((*MockInterface)(nil).ImmutableRuleExists), arg0, arg1)
}

// ListImmutableRule mocks base method.
func (m *MockInterface) ListImmutableRule(arg0 context.Context, arg1, arg2 *int64) ([]*model0.ImmutableRuleResult, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListImmutableRule", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model0.ImmutableRuleResult)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListImmutableRule indicates an expected call of ListImmutableRule.
func (mr *MockInterfaceMockRecorder) ListImmutableRule(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListImmutableRule", reflect.TypeOf((*MockInterface)(nil).ListImmutableRule), arg0, arg1, arg2)
}

// ProjectExistsImmutableRule mocks base method.
func (m *MockInterface) ProjectExistsImmutableRule(arg0 context.Context, arg1 int64) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProjectExistsImmutableRule", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProjectExistsImmutableRule indicates an expected call of ProjectExistsImmutableRule.
func (mr *MockInterfaceMockRecorder) ProjectExistsImmutableRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProjectExistsImmutableRule", reflect.TypeOf((*MockInterface)(nil).ProjectExistsImmutableRule), arg0, arg1)
}

// UpdateImmutableRule mocks base method.
func (m *MockInterface) UpdateImmutableRule(arg0 context.Context, arg1 int64, arg2 *model.ImmutableRule) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateImmutableRule", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateImmutableRule indicates an expected call of UpdateImmutableRule.
func (mr *MockInterfaceMockRecorder) UpdateImmutableRule(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateImmutableRule", reflect.TypeOf((*MockInterface)(nil).UpdateImmutableRule), arg0, arg1, arg2)
}
