// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/repository (interfaces: Interface)

// Package repository is a generated GoMock package.
package repository

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	model "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// BatchDeleteRepository mocks base method.
func (m *MockInterface) BatchDeleteRepository(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteRepository", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteRepository indicates an expected call of BatchDeleteRepository.
func (mr *MockInterfaceMockRecorder) BatchDeleteRepository(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteRepository", reflect.TypeOf((*MockInterface)(nil).BatchDeleteRepository), arg0, arg1, arg2)
}

// DeleteRepository mocks base method.
func (m *MockInterface) DeleteRepository(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRepository", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRepository indicates an expected call of DeleteRepository.
func (mr *MockInterfaceMockRecorder) DeleteRepository(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRepository", reflect.TypeOf((*MockInterface)(nil).DeleteRepository), arg0, arg1, arg2)
}

// GetRepository mocks base method.
func (m *MockInterface) GetRepository(arg0 context.Context, arg1, arg2 string, arg3 bool) (*model.RepositoryResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRepository", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*model.RepositoryResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRepository indicates an expected call of GetRepository.
func (mr *MockInterfaceMockRecorder) GetRepository(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRepository", reflect.TypeOf((*MockInterface)(nil).GetRepository), arg0, arg1, arg2, arg3)
}

// ListAllRepositories mocks base method.
func (m *MockInterface) ListAllRepositories(arg0 context.Context, arg1 string, arg2, arg3 int64) ([]*model.RepositoryResult, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllRepositories", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*model.RepositoryResult)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListAllRepositories indicates an expected call of ListAllRepositories.
func (mr *MockInterfaceMockRecorder) ListAllRepositories(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllRepositories", reflect.TypeOf((*MockInterface)(nil).ListAllRepositories), arg0, arg1, arg2, arg3)
}

// ListRepositories mocks base method.
func (m *MockInterface) ListRepositories(arg0 context.Context, arg1, arg2 string, arg3, arg4 *int64, arg5 bool) ([]*model.RepositoryResult, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRepositories", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*model.RepositoryResult)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListRepositories indicates an expected call of ListRepositories.
func (mr *MockInterfaceMockRecorder) ListRepositories(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRepositories", reflect.TypeOf((*MockInterface)(nil).ListRepositories), arg0, arg1, arg2, arg3, arg4, arg5)
}

// UpdateRepository mocks base method.
func (m *MockInterface) UpdateRepository(arg0 context.Context, arg1, arg2, arg3 string, arg4 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRepository", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRepository indicates an expected call of UpdateRepository.
func (mr *MockInterfaceMockRecorder) UpdateRepository(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRepository", reflect.TypeOf((*MockInterface)(nil).UpdateRepository), arg0, arg1, arg2, arg3, arg4)
}
