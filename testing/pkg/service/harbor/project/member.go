// Code generated by mockery v2.28.1. DO NOT EDIT.

package project

import (
	"context"

	"github.com/stretchr/testify/mock"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// MockMemberService is an autogenerated mock type for the MemberService type
type MockMemberService struct {
	mock.Mock
}

func (_m *MockMemberService) NewProjectMember(ctx context.Context, mr *model.CreateProjectMemberRequest, projectNameOrID string, isResourceName bool) error {
	ret := _m.Called(ctx, mr)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.CreateProjectMemberRequest, string, bool) error); ok {
		r0 = rf(ctx, mr, projectNameOrID, isResourceName)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteProjectMember provides a mock function with given fields: ctx, projectNameOrID, projectNameOrID
func (_m *MockMemberService) DeleteProjectMember(ctx context.Context, projectNameOrID string, memberId int64, isResourceName bool) error {
	ret := _m.Called(ctx, projectNameOrID, memberId)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, bool) error); ok {
		r0 = rf(ctx, projectNameOrID, memberId, isResourceName)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ListProjectMember provides a mock function with given fields: ctx, projectNameOrID, isResourceName, pageNo, pageSize
func (_m *MockMemberService) ListProjectMember(ctx context.Context, projectNameOrId string, isResourceName bool, args ...int64) ([]*model.ProjectMemberResult, int64, error) {
	//var pageNo, pageSize int64
	//if len(args) == 2 {
	//	pageNo = args[0]
	//	pageSize = args[1]
	//}
	ret := _m.Called(ctx, projectNameOrId, isResourceName)

	var r0 []*model.ProjectMemberResult
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) ([]*model.ProjectMemberResult, int64, error)); ok {
		return rf(ctx, projectNameOrId, isResourceName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) []*model.ProjectMemberResult); ok {
		r0 = rf(ctx, projectNameOrId, isResourceName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.ProjectMemberResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, bool) int64); ok {
		r1 = rf(ctx, projectNameOrId, isResourceName)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, bool) error); ok {
		r2 = rf(ctx, projectNameOrId, isResourceName)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}
