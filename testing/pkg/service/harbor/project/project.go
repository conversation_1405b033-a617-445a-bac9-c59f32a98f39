// Code generated by mockery v2.28.1. DO NOT EDIT.

package project

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	model "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// MockProjectService is an autogenerated mock type for the ProjectServiceInterface type
type MockProjectService struct {
	mock.Mock
}

// BatchDeleteProject provides a mock function with given fields: ctx, projectNames
func (_m *MockProjectService) BatchDeleteProject(ctx context.Context, projectNames []string) error {
	ret := _m.Called(ctx, projectNames)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) error); ok {
		r0 = rf(ctx, projectNames)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteProject provides a mock function with given fields: ctx, projectName
func (_m *MockProjectService) DeleteProject(ctx context.Context, projectName string) error {
	ret := _m.Called(ctx, projectName)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, projectName)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetProject provides a mock function with given fields: ctx, projectName, useSim
func (_m *MockProjectService) GetProject(ctx context.Context, projectName string, useSim bool) (*model.ProjectResult, error) {
	ret := _m.Called(ctx, projectName, useSim)

	var r0 *model.ProjectResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) (*model.ProjectResult, error)); ok {
		return rf(ctx, projectName, useSim)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) *model.ProjectResult); ok {
		r0 = rf(ctx, projectName, useSim)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.ProjectResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, bool) error); ok {
		r1 = rf(ctx, projectName, useSim)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HeadProject provides a mock function with given fields: ctx, projectName
func (_m *MockProjectService) HeadProject(ctx context.Context, projectName string) error {
	ret := _m.Called(ctx, projectName)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, projectName)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ListProjects provides a mock function with given fields: ctx, projectName, pageNo, pageSize
func (_m *MockProjectService) ListProjects(ctx context.Context, projectName string, pageNo *int64, pageSize *int64) ([]*model.ProjectResult, int64, error) {
	ret := _m.Called(ctx, projectName, pageNo, pageSize)

	var r0 []*model.ProjectResult
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *int64, *int64) ([]*model.ProjectResult, int64, error)); ok {
		return rf(ctx, projectName, pageNo, pageSize)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *int64, *int64) []*model.ProjectResult); ok {
		r0 = rf(ctx, projectName, pageNo, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.ProjectResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *int64, *int64) int64); ok {
		r1 = rf(ctx, projectName, pageNo, pageSize)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, *int64, *int64) error); ok {
		r2 = rf(ctx, projectName, pageNo, pageSize)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// NewProject provides a mock function with given fields: ctx, pr
func (_m *MockProjectService) NewProject(ctx context.Context, pr *model.ProjectReq) error {
	ret := _m.Called(ctx, pr)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.ProjectReq) error); ok {
		r0 = rf(ctx, pr)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TotalProjects provides a mock function with given fields: ctx
func (_m *MockProjectService) TotalProjects(ctx context.Context) (int64, error) {
	ret := _m.Called(ctx)

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (int64, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) int64); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateProject provides a mock function with given fields: ctx, pReq
func (_m *MockProjectService) UpdateProject(ctx context.Context, pReq *model.ProjectReq) error {
	ret := _m.Called(ctx, pReq)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.ProjectReq) error); ok {
		r0 = rf(ctx, pReq)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewMockProjectService interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockProjectService creates a new instance of MockProjectService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockProjectService(t mockConstructorTestingTNewMockProjectService) *MockProjectService {
	mock := &MockProjectService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
