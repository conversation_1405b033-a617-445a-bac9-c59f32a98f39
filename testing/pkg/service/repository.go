// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service (interfaces: RepositoryServiceInterface)

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	listers "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	model "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// MockRepositoryServiceInterface is a mock of RepositoryServiceInterface interface.
type MockRepositoryServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryServiceInterfaceMockRecorder
}

// MockRepositoryServiceInterfaceMockRecorder is the mock recorder for MockRepositoryServiceInterface.
type MockRepositoryServiceInterfaceMockRecorder struct {
	mock *MockRepositoryServiceInterface
}

// NewMockRepositoryServiceInterface creates a new mock instance.
func NewMockRepositoryServiceInterface(ctrl *gomock.Controller) *MockRepositoryServiceInterface {
	mock := &MockRepositoryServiceInterface{ctrl: ctrl}
	mock.recorder = &MockRepositoryServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepositoryServiceInterface) EXPECT() *MockRepositoryServiceInterfaceMockRecorder {
	return m.recorder
}

// BatchDeleteBuildRepositoryTask mocks base method.
func (m *MockRepositoryServiceInterface) BatchDeleteBuildRepositoryTask(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteBuildRepositoryTask", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteBuildRepositoryTask indicates an expected call of BatchDeleteBuildRepositoryTask.
func (mr *MockRepositoryServiceInterfaceMockRecorder) BatchDeleteBuildRepositoryTask(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteBuildRepositoryTask", reflect.TypeOf((*MockRepositoryServiceInterface)(nil).BatchDeleteBuildRepositoryTask), arg0, arg1, arg2)
}

// CreateBuildRepositoryTask mocks base method.
func (m *MockRepositoryServiceInterface) CreateBuildRepositoryTask(arg0 context.Context, arg1, arg2, arg3, arg4 string, arg5 model.FromType, arg6, arg7, arg8, arg9 string, arg10 *listers.InstanceInfo) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBuildRepositoryTask", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBuildRepositoryTask indicates an expected call of CreateBuildRepositoryTask.
func (mr *MockRepositoryServiceInterfaceMockRecorder) CreateBuildRepositoryTask(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBuildRepositoryTask", reflect.TypeOf((*MockRepositoryServiceInterface)(nil).CreateBuildRepositoryTask), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10)
}

// CreateKanikoRegistrySecret mocks base method.
func (m *MockRepositoryServiceInterface) CreateKanikoRegistrySecret(arg0 context.Context, arg1 *listers.InstanceInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateKanikoRegistrySecret", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateKanikoRegistrySecret indicates an expected call of CreateKanikoRegistrySecret.
func (mr *MockRepositoryServiceInterfaceMockRecorder) CreateKanikoRegistrySecret(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateKanikoRegistrySecret", reflect.TypeOf((*MockRepositoryServiceInterface)(nil).CreateKanikoRegistrySecret), arg0, arg1)
}

// DeleteBuildRepositoryTask mocks base method.
func (m *MockRepositoryServiceInterface) DeleteBuildRepositoryTask(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBuildRepositoryTask", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBuildRepositoryTask indicates an expected call of DeleteBuildRepositoryTask.
func (mr *MockRepositoryServiceInterfaceMockRecorder) DeleteBuildRepositoryTask(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBuildRepositoryTask", reflect.TypeOf((*MockRepositoryServiceInterface)(nil).DeleteBuildRepositoryTask), arg0, arg1, arg2)
}

// GetBuildRepositoryTask mocks base method.
func (m *MockRepositoryServiceInterface) GetBuildRepositoryTask(arg0 context.Context, arg1, arg2, arg3, arg4 string) (*model.BuildRepositoryTaskResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBuildRepositoryTask", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*model.BuildRepositoryTaskResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBuildRepositoryTask indicates an expected call of GetBuildRepositoryTask.
func (mr *MockRepositoryServiceInterfaceMockRecorder) GetBuildRepositoryTask(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBuildRepositoryTask", reflect.TypeOf((*MockRepositoryServiceInterface)(nil).GetBuildRepositoryTask), arg0, arg1, arg2, arg3, arg4)
}

// ListBuildRepositoryTask mocks base method.
func (m *MockRepositoryServiceInterface) ListBuildRepositoryTask(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 string) ([]*model.BuildRepositoryTaskResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBuildRepositoryTask", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*model.BuildRepositoryTaskResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBuildRepositoryTask indicates an expected call of ListBuildRepositoryTask.
func (mr *MockRepositoryServiceInterfaceMockRecorder) ListBuildRepositoryTask(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBuildRepositoryTask", reflect.TypeOf((*MockRepositoryServiceInterface)(nil).ListBuildRepositoryTask), arg0, arg1, arg2, arg3, arg4, arg5)
}
