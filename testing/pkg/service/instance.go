// Code generated by mockery v2.43.0. DO NOT EDIT.

package service

import (
	context "context"

	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"

	mock "github.com/stretchr/testify/mock"

	model "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// InstanceService is an autogenerated mock type for the InstanceServiceInterface type
type InstanceService struct {
	mock.Mock
}

// CheckInstanceQuota provides a mock function with given fields: ctx, accountId
func (_m *InstanceService) CheckInstanceQuota(ctx context.Context, accountId string) error {
	ret := _m.Called(ctx, accountId)

	if len(ret) == 0 {
		panic("no return value specified for CheckInstanceQuota")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, accountId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateNewFacadeOrder provides a mock function with given fields: ctx, accountId, userId, instanceId, instanceType, extra, timeUnit, time, paymentMethod
func (_m *InstanceService) CreateNewFacadeOrder(ctx context.Context, accountId string, userId string, instanceId string, instanceType string, extra string, timeUnit string, time int, paymentMethod []model.PaymentMethod) (string, error) {
	ret := _m.Called(ctx, accountId, userId, instanceId, instanceType, extra, timeUnit, time, paymentMethod)

	if len(ret) == 0 {
		panic("no return value specified for CreateNewFacadeOrder")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, string, string, int, []model.PaymentMethod) (string, error)); ok {
		return rf(ctx, accountId, userId, instanceId, instanceType, extra, timeUnit, time, paymentMethod)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, string, string, int, []model.PaymentMethod) string); ok {
		r0 = rf(ctx, accountId, userId, instanceId, instanceType, extra, timeUnit, time, paymentMethod)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string, string, string, int, []model.PaymentMethod) error); ok {
		r1 = rf(ctx, accountId, userId, instanceId, instanceType, extra, timeUnit, time, paymentMethod)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateRenewFacadeOrder provides a mock function with given fields: ctx, req
func (_m *InstanceService) CreateRenewFacadeOrder(ctx context.Context, req *model.ConfirmOrderRequest) (string, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateRenewFacadeOrder")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.ConfirmOrderRequest) (string, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.ConfirmOrderRequest) string); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.ConfirmOrderRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateResizeFacadeOrder provides a mock function with given fields: ctx, accountId, userId, resourceUuid, instanceType, extra, paymentMethod
func (_m *InstanceService) CreateResizeFacadeOrder(ctx context.Context, accountId string, userId string, resourceUuid string, instanceType string, extra string, paymentMethod []model.PaymentMethod) (string, error) {
	ret := _m.Called(ctx, accountId, userId, resourceUuid, instanceType, extra, paymentMethod)

	if len(ret) == 0 {
		panic("no return value specified for CreateResizeFacadeOrder")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, string, []model.PaymentMethod) (string, error)); ok {
		return rf(ctx, accountId, userId, resourceUuid, instanceType, extra, paymentMethod)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, string, []model.PaymentMethod) string); ok {
		r0 = rf(ctx, accountId, userId, resourceUuid, instanceType, extra, paymentMethod)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string, string, []model.PaymentMethod) error); ok {
		r1 = rf(ctx, accountId, userId, resourceUuid, instanceType, extra, paymentMethod)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GenerateInstanceId provides a mock function with given fields: ctx
func (_m *InstanceService) GenerateInstanceId(ctx context.Context) (string, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GenerateInstanceId")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (string, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetResourceDetail provides a mock function with given fields: ctx, accountId, instanceId
func (_m *InstanceService) GetResourceDetail(ctx context.Context, accountId string, instanceId string) (*billing.GetResourceDetailResponse, error) {
	ret := _m.Called(ctx, accountId, instanceId)

	if len(ret) == 0 {
		panic("no return value specified for GetResourceDetail")
	}

	var r0 *billing.GetResourceDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*billing.GetResourceDetailResponse, error)); ok {
		return rf(ctx, accountId, instanceId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *billing.GetResourceDetailResponse); ok {
		r0 = rf(ctx, accountId, instanceId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*billing.GetResourceDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, accountId, instanceId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetResourceGroupLinkMap provides a mock function with given fields: ctx, instanceIDs
func (_m *InstanceService) GetResourceGroupLinkMap(ctx context.Context, instanceIDs []string) (map[string][]string, error) {
	ret := _m.Called(ctx, instanceIDs)

	if len(ret) == 0 {
		panic("no return value specified for GetResourceGroupLinkMap")
	}

	var r0 map[string][]string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) (map[string][]string, error)); ok {
		return rf(ctx, instanceIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) map[string][]string); ok {
		r0 = rf(ctx, instanceIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string][]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, instanceIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetResourceGroupMap provides a mock function with given fields: ctx, instanceIDs
func (_m *InstanceService) GetResourceGroupMap(ctx context.Context, instanceIDs []string) (map[string][]string, error) {
	ret := _m.Called(ctx, instanceIDs)

	if len(ret) == 0 {
		panic("no return value specified for GetResourceGroupMap")
	}

	var r0 map[string][]string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) (map[string][]string, error)); ok {
		return rf(ctx, instanceIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) map[string][]string); ok {
		r0 = rf(ctx, instanceIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string][]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, instanceIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetResourceTagMap provides a mock function with given fields: ctx, instanceIDs
func (_m *InstanceService) GetResourceTagMap(ctx context.Context, instanceIDs []string) (map[string][]model.Tag, error) {
	ret := _m.Called(ctx, instanceIDs)

	if len(ret) == 0 {
		panic("no return value specified for GetResourceTagMap")
	}

	var r0 map[string][]model.Tag
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) (map[string][]model.Tag, error)); ok {
		return rf(ctx, instanceIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) map[string][]model.Tag); ok {
		r0 = rf(ctx, instanceIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string][]model.Tag)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, instanceIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListInstanceLocal provides a mock function with given fields: ctx, accountId, keywordType, keyword
func (_m *InstanceService) ListInstanceLocal(ctx context.Context, accountId string, keywordType string, keyword string) ([]*model.InstanceInfo, error) {
	ret := _m.Called(ctx, accountId, keywordType, keyword)

	if len(ret) == 0 {
		panic("no return value specified for ListInstanceLocal")
	}

	var r0 []*model.InstanceInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) ([]*model.InstanceInfo, error)); ok {
		return rf(ctx, accountId, keywordType, keyword)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) []*model.InstanceInfo); ok {
		r0 = rf(ctx, accountId, keywordType, keyword)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.InstanceInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, accountId, keywordType, keyword)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListInstanceRemote provides a mock function with given fields: ak, sk, sessionToken, ep, keywordType, keyword
func (_m *InstanceService) ListInstanceRemote(ak string, sk string, sessionToken string, ep string, keywordType string, keyword string) ([]*model.InstanceInfo, error) {
	ret := _m.Called(ak, sk, sessionToken, ep, keywordType, keyword)

	if len(ret) == 0 {
		panic("no return value specified for ListInstanceRemote")
	}

	var r0 []*model.InstanceInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string, string, string, string) ([]*model.InstanceInfo, error)); ok {
		return rf(ak, sk, sessionToken, ep, keywordType, keyword)
	}
	if rf, ok := ret.Get(0).(func(string, string, string, string, string, string) []*model.InstanceInfo); ok {
		r0 = rf(ak, sk, sessionToken, ep, keywordType, keyword)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.InstanceInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string, string, string, string) error); ok {
		r1 = rf(ak, sk, sessionToken, ep, keywordType, keyword)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListInstances provides a mock function with given fields: ctx, accountId, userId, keywordType, keyword
func (_m *InstanceService) ListInstances(ctx context.Context, accountId string, userId string, keywordType string, keyword string) ([]*model.InstanceInfo, error) {
	ret := _m.Called(ctx, accountId, userId, keywordType, keyword)

	if len(ret) == 0 {
		panic("no return value specified for ListInstances")
	}

	var r0 []*model.InstanceInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) ([]*model.InstanceInfo, error)); ok {
		return rf(ctx, accountId, userId, keywordType, keyword)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) []*model.InstanceInfo); ok {
		r0 = rf(ctx, accountId, userId, keywordType, keyword)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.InstanceInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) error); ok {
		r1 = rf(ctx, accountId, userId, keywordType, keyword)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ValidType provides a mock function with given fields: expected, actual
func (_m *InstanceService) ValidType(expected string, actual string) bool {
	ret := _m.Called(expected, actual)

	if len(ret) == 0 {
		panic("no return value specified for ValidType")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(string, string) bool); ok {
		r0 = rf(expected, actual)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// NewInstanceService creates a new instance of InstanceService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewInstanceService(t interface {
	mock.TestingT
	Cleanup(func())
}) *InstanceService {
	mock := &InstanceService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
