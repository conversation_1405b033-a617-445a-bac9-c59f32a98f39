// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service (interfaces: InstanceServiceInterface)

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	model "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// MockInstanceServiceInterface is a mock of InstanceServiceInterface interface.
type MockInstanceServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInstanceServiceInterfaceMockRecorder
}

// MockInstanceServiceInterfaceMockRecorder is the mock recorder for MockInstanceServiceInterface.
type MockInstanceServiceInterfaceMockRecorder struct {
	mock *MockInstanceServiceInterface
}

// NewMockInstanceServiceInterface creates a new mock instance.
func NewMockInstanceServiceInterface(ctrl *gomock.Controller) *MockInstanceServiceInterface {
	mock := &MockInstanceServiceInterface{ctrl: ctrl}
	mock.recorder = &MockInstanceServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInstanceServiceInterface) EXPECT() *MockInstanceServiceInterfaceMockRecorder {
	return m.recorder
}

// CheckInstanceQuota mocks base method.
func (m *MockInstanceServiceInterface) CheckInstanceQuota(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInstanceQuota", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckInstanceQuota indicates an expected call of CheckInstanceQuota.
func (mr *MockInstanceServiceInterfaceMockRecorder) CheckInstanceQuota(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInstanceQuota", reflect.TypeOf((*MockInstanceServiceInterface)(nil).CheckInstanceQuota), arg0, arg1)
}

// CreateNewFacadeOrder mocks base method.
func (m *MockInstanceServiceInterface) CreateNewFacadeOrder(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 string, arg7 int, arg8 []model.PaymentMethod) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNewFacadeOrder", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNewFacadeOrder indicates an expected call of CreateNewFacadeOrder.
func (mr *MockInstanceServiceInterfaceMockRecorder) CreateNewFacadeOrder(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNewFacadeOrder", reflect.TypeOf((*MockInstanceServiceInterface)(nil).CreateNewFacadeOrder), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
}

// CreateRenewFacadeOrder mocks base method.
func (m *MockInstanceServiceInterface) CreateRenewFacadeOrder(arg0 context.Context, arg1 *model.ConfirmOrderRequest) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRenewFacadeOrder", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRenewFacadeOrder indicates an expected call of CreateRenewFacadeOrder.
func (mr *MockInstanceServiceInterfaceMockRecorder) CreateRenewFacadeOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRenewFacadeOrder", reflect.TypeOf((*MockInstanceServiceInterface)(nil).CreateRenewFacadeOrder), arg0, arg1)
}

// CreateResizeFacadeOrder mocks base method.
func (m *MockInstanceServiceInterface) CreateResizeFacadeOrder(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 string, arg6 []model.PaymentMethod) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateResizeFacadeOrder", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateResizeFacadeOrder indicates an expected call of CreateResizeFacadeOrder.
func (mr *MockInstanceServiceInterfaceMockRecorder) CreateResizeFacadeOrder(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateResizeFacadeOrder", reflect.TypeOf((*MockInstanceServiceInterface)(nil).CreateResizeFacadeOrder), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// GenerateInstanceId mocks base method.
func (m *MockInstanceServiceInterface) GenerateInstanceId(arg0 context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateInstanceId", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateInstanceId indicates an expected call of GenerateInstanceId.
func (mr *MockInstanceServiceInterfaceMockRecorder) GenerateInstanceId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateInstanceId", reflect.TypeOf((*MockInstanceServiceInterface)(nil).GenerateInstanceId), arg0)
}

// GetResourceDetail mocks base method.
func (m *MockInstanceServiceInterface) GetResourceDetail(arg0 context.Context, arg1, arg2 string) (*billing.GetResourceDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResourceDetail", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billing.GetResourceDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResourceDetail indicates an expected call of GetResourceDetail.
func (mr *MockInstanceServiceInterfaceMockRecorder) GetResourceDetail(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourceDetail", reflect.TypeOf((*MockInstanceServiceInterface)(nil).GetResourceDetail), arg0, arg1, arg2)
}

// GetResourceGroupLinkMap mocks base method.
func (m *MockInstanceServiceInterface) GetResourceGroupLinkMap(arg0 context.Context, arg1 []string) (map[string][]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResourceGroupLinkMap", arg0, arg1)
	ret0, _ := ret[0].(map[string][]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResourceGroupLinkMap indicates an expected call of GetResourceGroupLinkMap.
func (mr *MockInstanceServiceInterfaceMockRecorder) GetResourceGroupLinkMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourceGroupLinkMap", reflect.TypeOf((*MockInstanceServiceInterface)(nil).GetResourceGroupLinkMap), arg0, arg1)
}

// GetResourceGroupMap mocks base method.
func (m *MockInstanceServiceInterface) GetResourceGroupMap(arg0 context.Context, arg1 []string) (map[string][]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResourceGroupMap", arg0, arg1)
	ret0, _ := ret[0].(map[string][]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResourceGroupMap indicates an expected call of GetResourceGroupMap.
func (mr *MockInstanceServiceInterfaceMockRecorder) GetResourceGroupMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourceGroupMap", reflect.TypeOf((*MockInstanceServiceInterface)(nil).GetResourceGroupMap), arg0, arg1)
}

// GetResourceTagMap mocks base method.
func (m *MockInstanceServiceInterface) GetResourceTagMap(arg0 context.Context, arg1 []string) (map[string][]model.Tag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResourceTagMap", arg0, arg1)
	ret0, _ := ret[0].(map[string][]model.Tag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResourceTagMap indicates an expected call of GetResourceTagMap.
func (mr *MockInstanceServiceInterfaceMockRecorder) GetResourceTagMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourceTagMap", reflect.TypeOf((*MockInstanceServiceInterface)(nil).GetResourceTagMap), arg0, arg1)
}

// ListInstanceLocal mocks base method.
func (m *MockInstanceServiceInterface) ListInstanceLocal(arg0 context.Context, arg1, arg2, arg3 string) ([]*model.InstanceInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceLocal", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*model.InstanceInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceLocal indicates an expected call of ListInstanceLocal.
func (mr *MockInstanceServiceInterfaceMockRecorder) ListInstanceLocal(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceLocal", reflect.TypeOf((*MockInstanceServiceInterface)(nil).ListInstanceLocal), arg0, arg1, arg2, arg3)
}

// ListInstanceRemote mocks base method.
func (m *MockInstanceServiceInterface) ListInstanceRemote(arg0, arg1, arg2, arg3, arg4, arg5 string) ([]*model.InstanceInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceRemote", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*model.InstanceInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceRemote indicates an expected call of ListInstanceRemote.
func (mr *MockInstanceServiceInterfaceMockRecorder) ListInstanceRemote(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceRemote", reflect.TypeOf((*MockInstanceServiceInterface)(nil).ListInstanceRemote), arg0, arg1, arg2, arg3, arg4, arg5)
}

// ListInstances mocks base method.
func (m *MockInstanceServiceInterface) ListInstances(arg0 context.Context, arg1, arg2, arg3, arg4 string) ([]*model.InstanceInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstances", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*model.InstanceInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstances indicates an expected call of ListInstances.
func (mr *MockInstanceServiceInterfaceMockRecorder) ListInstances(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstances", reflect.TypeOf((*MockInstanceServiceInterface)(nil).ListInstances), arg0, arg1, arg2, arg3, arg4)
}

// ValidType mocks base method.
func (m *MockInstanceServiceInterface) ValidType(arg0, arg1 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidType", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// ValidType indicates an expected call of ValidType.
func (mr *MockInstanceServiceInterfaceMockRecorder) ValidType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidType", reflect.TypeOf((*MockInstanceServiceInterface)(nil).ValidType), arg0, arg1)
}
