// Code generated by mockery v2.43.0. DO NOT EDIT.

package service

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// ResourceService is an autogenerated mock type for the ResourceServiceInterface type
type ResourceService struct {
	mock.Mock
}

// Deleted provides a mock function with given fields: ctx, resourceId
func (_m *ResourceService) Deleted(ctx context.Context, resourceId string) error {
	ret := _m.Called(ctx, resourceId)

	if len(ret) == 0 {
		panic("no return value specified for Deleted")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, resourceId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Start provides a mock function with given fields: ctx, resourceId, accountId
func (_m *ResourceService) Start(ctx context.Context, resourceId string, accountId string) error {
	ret := _m.Called(ctx, resourceId, accountId)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, resourceId, accountId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Stop provides a mock function with given fields: ctx, resourceId
func (_m *ResourceService) Stop(ctx context.Context, resourceId string) error {
	ret := _m.Called(ctx, resourceId)

	if len(ret) == 0 {
		panic("no return value specified for Stop")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, resourceId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewResourceService creates a new instance of ResourceService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewResourceService(t interface {
	mock.TestingT
	Cleanup(func())
}) *ResourceService {
	mock := &ResourceService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
