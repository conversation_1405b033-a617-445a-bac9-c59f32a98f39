// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service (interfaces: DomainServiceInterface)

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	model "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

// MockDomainServiceInterface is a mock of DomainServiceInterface interface.
type MockDomainServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDomainServiceInterfaceMockRecorder
}

// MockDomainServiceInterfaceMockRecorder is the mock recorder for MockDomainServiceInterface.
type MockDomainServiceInterfaceMockRecorder struct {
	mock *MockDomainServiceInterface
}

// NewMockDomainServiceInterface creates a new mock instance.
func NewMockDomainServiceInterface(ctrl *gomock.Controller) *MockDomainServiceInterface {
	mock := &MockDomainServiceInterface{ctrl: ctrl}
	mock.recorder = &MockDomainServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDomainServiceInterface) EXPECT() *MockDomainServiceInterfaceMockRecorder {
	return m.recorder
}

// CheckDomainIcp mocks base method.
func (m *MockDomainServiceInterface) CheckDomainIcp(arg0 context.Context, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckDomainIcp", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckDomainIcp indicates an expected call of CheckDomainIcp.
func (mr *MockDomainServiceInterfaceMockRecorder) CheckDomainIcp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckDomainIcp", reflect.TypeOf((*MockDomainServiceInterface)(nil).CheckDomainIcp), arg0, arg1)
}

// ListUserCert mocks base method.
func (m *MockDomainServiceInterface) ListUserCert(arg0 context.Context, arg1, arg2 string) (*model.ListCertResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUserCert", arg0, arg1, arg2)
	ret0, _ := ret[0].(*model.ListCertResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUserCert indicates an expected call of ListUserCert.
func (mr *MockDomainServiceInterfaceMockRecorder) ListUserCert(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserCert", reflect.TypeOf((*MockDomainServiceInterface)(nil).ListUserCert), arg0, arg1, arg2)
}
