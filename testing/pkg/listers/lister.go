// Code generated by mockery v2.28.1. DO NOT EDIT.

package listers

import (
	mock "github.com/stretchr/testify/mock"
	listers "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
)

// MockLister is an autogenerated mock type for the ListerInterface type
type Mock<PERSON>ister struct {
	mock.Mock
}

// GetInstanceOwner provides a mock function with given fields: instanceId
func (_m *MockLister) GetInstanceOwner(instanceId string) (string, error) {
	ret := _m.Called(instanceId)

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (string, error)); ok {
		return rf(instanceId)
	}
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(instanceId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(string)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(instanceId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInstanceInfo provides a mock function with given fields: instanceId
func (_m *MockLister) GetInstanceInfo(instanceId string) (*listers.InstanceInfo, error) {
	ret := _m.Called(instanceId)

	var r0 *listers.InstanceInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*listers.InstanceInfo, error)); ok {
		return rf(instanceId)
	}
	if rf, ok := ret.Get(0).(func(string) *listers.InstanceInfo); ok {
		r0 = rf(instanceId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*listers.InstanceInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(instanceId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockLister interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockLister creates a new instance of MockLister. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockLister(t mockConstructorTestingTNewMockLister) *MockLister {
	mock := &MockLister{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
