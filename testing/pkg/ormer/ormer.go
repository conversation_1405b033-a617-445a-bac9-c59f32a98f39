// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/dao (interfaces: Orm)

// Package mock is a generated GoMock package.
package ormer

import (
	context "context"
	sql "database/sql"
	reflect "reflect"

	orm "github.com/astaxie/beego/orm"
	gomock "github.com/golang/mock/gomock"
)

// MockOrm is a mock of Orm interface.
type MockOrm struct {
	ctrl     *gomock.Controller
	recorder *MockOrmMockRecorder
}

// MockOrmMockRecorder is the mock recorder for MockOrm.
type MockOrmMockRecorder struct {
	mock *MockOrm
}

// NewMockOrm creates a new mock instance.
func NewMockOrm(ctrl *gomock.Controller) *MockOrm {
	mock := &MockOrm{ctrl: ctrl}
	mock.recorder = &MockOrmMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrm) EXPECT() *MockOrmMockRecorder {
	return m.recorder
}

// Begin mocks base method.
func (m *MockOrm) Begin() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Begin")
	ret0, _ := ret[0].(error)
	return ret0
}

// Begin indicates an expected call of Begin.
func (mr *MockOrmMockRecorder) Begin() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Begin", reflect.TypeOf((*MockOrm)(nil).Begin))
}

// BeginTx mocks base method.
func (m *MockOrm) BeginTx(arg0 context.Context, arg1 *sql.TxOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginTx", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BeginTx indicates an expected call of BeginTx.
func (mr *MockOrmMockRecorder) BeginTx(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginTx", reflect.TypeOf((*MockOrm)(nil).BeginTx), arg0, arg1)
}

// Commit mocks base method.
func (m *MockOrm) Commit() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Commit")
	ret0, _ := ret[0].(error)
	return ret0
}

// Commit indicates an expected call of Commit.
func (mr *MockOrmMockRecorder) Commit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Commit", reflect.TypeOf((*MockOrm)(nil).Commit))
}

// DBStats mocks base method.
func (m *MockOrm) DBStats() *sql.DBStats {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DBStats")
	ret0, _ := ret[0].(*sql.DBStats)
	return ret0
}

// DBStats indicates an expected call of DBStats.
func (mr *MockOrmMockRecorder) DBStats() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DBStats", reflect.TypeOf((*MockOrm)(nil).DBStats))
}

// Delete mocks base method.
func (m *MockOrm) Delete(arg0 interface{}, arg1 ...string) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Delete", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockOrmMockRecorder) Delete(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockOrm)(nil).Delete), varargs...)
}

// Driver mocks base method.
func (m *MockOrm) Driver() orm.Driver {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Driver")
	ret0, _ := ret[0].(orm.Driver)
	return ret0
}

// Driver indicates an expected call of Driver.
func (mr *MockOrmMockRecorder) Driver() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Driver", reflect.TypeOf((*MockOrm)(nil).Driver))
}

// Insert mocks base method.
func (m *MockOrm) Insert(arg0 interface{}) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockOrmMockRecorder) Insert(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockOrm)(nil).Insert), arg0)
}

// InsertMulti mocks base method.
func (m *MockOrm) InsertMulti(arg0 int, arg1 interface{}) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertMulti", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertMulti indicates an expected call of InsertMulti.
func (mr *MockOrmMockRecorder) InsertMulti(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertMulti", reflect.TypeOf((*MockOrm)(nil).InsertMulti), arg0, arg1)
}

// InsertOrUpdate mocks base method.
func (m *MockOrm) InsertOrUpdate(arg0 interface{}, arg1 ...string) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InsertOrUpdate", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertOrUpdate indicates an expected call of InsertOrUpdate.
func (mr *MockOrmMockRecorder) InsertOrUpdate(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertOrUpdate", reflect.TypeOf((*MockOrm)(nil).InsertOrUpdate), varargs...)
}

// LoadRelated mocks base method.
func (m *MockOrm) LoadRelated(arg0 interface{}, arg1 string, arg2 ...interface{}) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadRelated", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadRelated indicates an expected call of LoadRelated.
func (mr *MockOrmMockRecorder) LoadRelated(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadRelated", reflect.TypeOf((*MockOrm)(nil).LoadRelated), varargs...)
}

// QueryM2M mocks base method.
func (m *MockOrm) QueryM2M(arg0 interface{}, arg1 string) orm.QueryM2Mer {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryM2M", arg0, arg1)
	ret0, _ := ret[0].(orm.QueryM2Mer)
	return ret0
}

// QueryM2M indicates an expected call of QueryM2M.
func (mr *MockOrmMockRecorder) QueryM2M(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryM2M", reflect.TypeOf((*MockOrm)(nil).QueryM2M), arg0, arg1)
}

// QueryTable mocks base method.
func (m *MockOrm) QueryTable(arg0 interface{}) orm.QuerySeter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryTable", arg0)
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// QueryTable indicates an expected call of QueryTable.
func (mr *MockOrmMockRecorder) QueryTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryTable", reflect.TypeOf((*MockOrm)(nil).QueryTable), arg0)
}

// Raw mocks base method.
func (m *MockOrm) Raw(arg0 string, arg1 ...interface{}) orm.RawSeter {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Raw", varargs...)
	ret0, _ := ret[0].(orm.RawSeter)
	return ret0
}

// Raw indicates an expected call of Raw.
func (mr *MockOrmMockRecorder) Raw(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Raw", reflect.TypeOf((*MockOrm)(nil).Raw), varargs...)
}

// Read mocks base method.
func (m *MockOrm) Read(arg0 interface{}, arg1 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Read", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Read indicates an expected call of Read.
func (mr *MockOrmMockRecorder) Read(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Read", reflect.TypeOf((*MockOrm)(nil).Read), varargs...)
}

// ReadForUpdate mocks base method.
func (m *MockOrm) ReadForUpdate(arg0 interface{}, arg1 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReadForUpdate", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReadForUpdate indicates an expected call of ReadForUpdate.
func (mr *MockOrmMockRecorder) ReadForUpdate(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadForUpdate", reflect.TypeOf((*MockOrm)(nil).ReadForUpdate), varargs...)
}

// ReadOrCreate mocks base method.
func (m *MockOrm) ReadOrCreate(arg0 interface{}, arg1 string, arg2 ...string) (bool, int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReadOrCreate", varargs...)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ReadOrCreate indicates an expected call of ReadOrCreate.
func (mr *MockOrmMockRecorder) ReadOrCreate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadOrCreate", reflect.TypeOf((*MockOrm)(nil).ReadOrCreate), varargs...)
}

// Rollback mocks base method.
func (m *MockOrm) Rollback() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Rollback")
	ret0, _ := ret[0].(error)
	return ret0
}

// Rollback indicates an expected call of Rollback.
func (mr *MockOrmMockRecorder) Rollback() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Rollback", reflect.TypeOf((*MockOrm)(nil).Rollback))
}

// Update mocks base method.
func (m *MockOrm) Update(arg0 interface{}, arg1 ...string) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Update", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockOrmMockRecorder) Update(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockOrm)(nil).Update), varargs...)
}

// Using mocks base method.
func (m *MockOrm) Using(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Using", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Using indicates an expected call of Using.
func (mr *MockOrmMockRecorder) Using(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Using", reflect.TypeOf((*MockOrm)(nil).Using), arg0)
}
