// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/dao (interfaces: RawSeter)

// Package ormer is a generated GoMock package.
package ormer

import (
	sql "database/sql"
	reflect "reflect"

	orm "github.com/astaxie/beego/orm"
	gomock "github.com/golang/mock/gomock"
)

// MockRawSeter is a mock of RawSeter interface.
type MockRawSeter struct {
	ctrl     *gomock.Controller
	recorder *MockRawSeterMockRecorder
}

// MockRawSeterMockRecorder is the mock recorder for MockRawSeter.
type MockRawSeterMockRecorder struct {
	mock *MockRawSeter
}

// NewMockRawSeter creates a new mock instance.
func NewMockRawSeter(ctrl *gomock.Controller) *MockRawSeter {
	mock := &MockRawSeter{ctrl: ctrl}
	mock.recorder = &MockRawSeterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRawSeter) EXPECT() *MockRawSeterMockRecorder {
	return m.recorder
}

// Exec mocks base method.
func (m *MockRawSeter) Exec() (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exec")
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Exec indicates an expected call of Exec.
func (mr *MockRawSeterMockRecorder) Exec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exec", reflect.TypeOf((*MockRawSeter)(nil).Exec))
}

// Prepare mocks base method.
func (m *MockRawSeter) Prepare() (orm.RawPreparer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Prepare")
	ret0, _ := ret[0].(orm.RawPreparer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Prepare indicates an expected call of Prepare.
func (mr *MockRawSeterMockRecorder) Prepare() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Prepare", reflect.TypeOf((*MockRawSeter)(nil).Prepare))
}

// QueryRow mocks base method.
func (m *MockRawSeter) QueryRow(arg0 ...interface{}) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range arg0 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryRow", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// QueryRow indicates an expected call of QueryRow.
func (mr *MockRawSeterMockRecorder) QueryRow(arg0 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryRow", reflect.TypeOf((*MockRawSeter)(nil).QueryRow), arg0...)
}

// QueryRows mocks base method.
func (m *MockRawSeter) QueryRows(arg0 ...interface{}) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range arg0 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryRows", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryRows indicates an expected call of QueryRows.
func (mr *MockRawSeterMockRecorder) QueryRows(arg0 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryRows", reflect.TypeOf((*MockRawSeter)(nil).QueryRows), arg0...)
}

// RowsToMap mocks base method.
func (m *MockRawSeter) RowsToMap(arg0 *orm.Params, arg1, arg2 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RowsToMap", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RowsToMap indicates an expected call of RowsToMap.
func (mr *MockRawSeterMockRecorder) RowsToMap(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RowsToMap", reflect.TypeOf((*MockRawSeter)(nil).RowsToMap), arg0, arg1, arg2)
}

// RowsToStruct mocks base method.
func (m *MockRawSeter) RowsToStruct(arg0 interface{}, arg1, arg2 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RowsToStruct", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RowsToStruct indicates an expected call of RowsToStruct.
func (mr *MockRawSeterMockRecorder) RowsToStruct(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RowsToStruct", reflect.TypeOf((*MockRawSeter)(nil).RowsToStruct), arg0, arg1, arg2)
}

// SetArgs mocks base method.
func (m *MockRawSeter) SetArgs(arg0 ...interface{}) orm.RawSeter {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range arg0 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetArgs", varargs...)
	ret0, _ := ret[0].(orm.RawSeter)
	return ret0
}

// SetArgs indicates an expected call of SetArgs.
func (mr *MockRawSeterMockRecorder) SetArgs(arg0 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetArgs", reflect.TypeOf((*MockRawSeter)(nil).SetArgs), arg0...)
}

// Values mocks base method.
func (m *MockRawSeter) Values(arg0 *[]orm.Params, arg1 ...string) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Values", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Values indicates an expected call of Values.
func (mr *MockRawSeterMockRecorder) Values(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Values", reflect.TypeOf((*MockRawSeter)(nil).Values), varargs...)
}

// ValuesFlat mocks base method.
func (m *MockRawSeter) ValuesFlat(arg0 *orm.ParamsList, arg1 ...string) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValuesFlat", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValuesFlat indicates an expected call of ValuesFlat.
func (mr *MockRawSeterMockRecorder) ValuesFlat(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValuesFlat", reflect.TypeOf((*MockRawSeter)(nil).ValuesFlat), varargs...)
}

// ValuesList mocks base method.
func (m *MockRawSeter) ValuesList(arg0 *[]orm.ParamsList, arg1 ...string) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValuesList", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValuesList indicates an expected call of ValuesList.
func (mr *MockRawSeterMockRecorder) ValuesList(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValuesList", reflect.TypeOf((*MockRawSeter)(nil).ValuesList), varargs...)
}
