// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/dao (interfaces: QuerySeter)

// Package ormer is a generated GoMock package.
package ormer

import (
	reflect "reflect"

	orm "github.com/astaxie/beego/orm"
	gomock "github.com/golang/mock/gomock"
)

// MockQuerySeter is a mock of QuerySeter interface.
type MockQuerySeter struct {
	ctrl     *gomock.Controller
	recorder *MockQuerySeterMockRecorder
}

// MockQuerySeterMockRecorder is the mock recorder for MockQuerySeter.
type MockQuerySeterMockRecorder struct {
	mock *MockQuerySeter
}

// NewMockQuerySeter creates a new mock instance.
func NewMockQuerySeter(ctrl *gomock.Controller) *MockQuerySeter {
	mock := &MockQuerySeter{ctrl: ctrl}
	mock.recorder = &MockQuerySeterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuerySeter) EXPECT() *MockQuerySeterMockRecorder {
	return m.recorder
}

// All mocks base method.
func (m *MockQuerySeter) All(arg0 interface{}, arg1 ...string) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "All", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// All indicates an expected call of All.
func (mr *MockQuerySeterMockRecorder) All(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "All", reflect.TypeOf((*MockQuerySeter)(nil).All), varargs...)
}

// Count mocks base method.
func (m *MockQuerySeter) Count() (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Count")
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockQuerySeterMockRecorder) Count() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockQuerySeter)(nil).Count))
}

// Delete mocks base method.
func (m *MockQuerySeter) Delete() (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete")
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockQuerySeterMockRecorder) Delete() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockQuerySeter)(nil).Delete))
}

// Distinct mocks base method.
func (m *MockQuerySeter) Distinct() orm.QuerySeter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Distinct")
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// Distinct indicates an expected call of Distinct.
func (mr *MockQuerySeterMockRecorder) Distinct() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Distinct", reflect.TypeOf((*MockQuerySeter)(nil).Distinct))
}

// Exclude mocks base method.
func (m *MockQuerySeter) Exclude(arg0 string, arg1 ...interface{}) orm.QuerySeter {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Exclude", varargs...)
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// Exclude indicates an expected call of Exclude.
func (mr *MockQuerySeterMockRecorder) Exclude(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exclude", reflect.TypeOf((*MockQuerySeter)(nil).Exclude), varargs...)
}

// Exist mocks base method.
func (m *MockQuerySeter) Exist() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exist")
	ret0, _ := ret[0].(bool)
	return ret0
}

// Exist indicates an expected call of Exist.
func (mr *MockQuerySeterMockRecorder) Exist() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exist", reflect.TypeOf((*MockQuerySeter)(nil).Exist))
}

// Filter mocks base method.
func (m *MockQuerySeter) Filter(arg0 string, arg1 ...interface{}) orm.QuerySeter {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Filter", varargs...)
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// Filter indicates an expected call of Filter.
func (mr *MockQuerySeterMockRecorder) Filter(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Filter", reflect.TypeOf((*MockQuerySeter)(nil).Filter), varargs...)
}

// FilterRaw mocks base method.
func (m *MockQuerySeter) FilterRaw(arg0, arg1 string) orm.QuerySeter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterRaw", arg0, arg1)
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// FilterRaw indicates an expected call of FilterRaw.
func (mr *MockQuerySeterMockRecorder) FilterRaw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterRaw", reflect.TypeOf((*MockQuerySeter)(nil).FilterRaw), arg0, arg1)
}

// ForUpdate mocks base method.
func (m *MockQuerySeter) ForUpdate() orm.QuerySeter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ForUpdate")
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// ForUpdate indicates an expected call of ForUpdate.
func (mr *MockQuerySeterMockRecorder) ForUpdate() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForUpdate", reflect.TypeOf((*MockQuerySeter)(nil).ForUpdate))
}

// GetCond mocks base method.
func (m *MockQuerySeter) GetCond() *orm.Condition {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCond")
	ret0, _ := ret[0].(*orm.Condition)
	return ret0
}

// GetCond indicates an expected call of GetCond.
func (mr *MockQuerySeterMockRecorder) GetCond() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCond", reflect.TypeOf((*MockQuerySeter)(nil).GetCond))
}

// GroupBy mocks base method.
func (m *MockQuerySeter) GroupBy(arg0 ...string) orm.QuerySeter {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range arg0 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GroupBy", varargs...)
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// GroupBy indicates an expected call of GroupBy.
func (mr *MockQuerySeterMockRecorder) GroupBy(arg0 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GroupBy", reflect.TypeOf((*MockQuerySeter)(nil).GroupBy), arg0...)
}

// Limit mocks base method.
func (m *MockQuerySeter) Limit(arg0 interface{}, arg1 ...interface{}) orm.QuerySeter {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Limit", varargs...)
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// Limit indicates an expected call of Limit.
func (mr *MockQuerySeterMockRecorder) Limit(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Limit", reflect.TypeOf((*MockQuerySeter)(nil).Limit), varargs...)
}

// Offset mocks base method.
func (m *MockQuerySeter) Offset(arg0 interface{}) orm.QuerySeter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Offset", arg0)
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// Offset indicates an expected call of Offset.
func (mr *MockQuerySeterMockRecorder) Offset(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Offset", reflect.TypeOf((*MockQuerySeter)(nil).Offset), arg0)
}

// One mocks base method.
func (m *MockQuerySeter) One(arg0 interface{}, arg1 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "One", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// One indicates an expected call of One.
func (mr *MockQuerySeterMockRecorder) One(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "One", reflect.TypeOf((*MockQuerySeter)(nil).One), varargs...)
}

// OrderBy mocks base method.
func (m *MockQuerySeter) OrderBy(arg0 ...string) orm.QuerySeter {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range arg0 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OrderBy", varargs...)
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// OrderBy indicates an expected call of OrderBy.
func (mr *MockQuerySeterMockRecorder) OrderBy(arg0 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderBy", reflect.TypeOf((*MockQuerySeter)(nil).OrderBy), arg0...)
}

// PrepareInsert mocks base method.
func (m *MockQuerySeter) PrepareInsert() (orm.Inserter, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrepareInsert")
	ret0, _ := ret[0].(orm.Inserter)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrepareInsert indicates an expected call of PrepareInsert.
func (mr *MockQuerySeterMockRecorder) PrepareInsert() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrepareInsert", reflect.TypeOf((*MockQuerySeter)(nil).PrepareInsert))
}

// RelatedSel mocks base method.
func (m *MockQuerySeter) RelatedSel(arg0 ...interface{}) orm.QuerySeter {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range arg0 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RelatedSel", varargs...)
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// RelatedSel indicates an expected call of RelatedSel.
func (mr *MockQuerySeterMockRecorder) RelatedSel(arg0 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RelatedSel", reflect.TypeOf((*MockQuerySeter)(nil).RelatedSel), arg0...)
}

// RowsToMap mocks base method.
func (m *MockQuerySeter) RowsToMap(arg0 *orm.Params, arg1, arg2 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RowsToMap", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RowsToMap indicates an expected call of RowsToMap.
func (mr *MockQuerySeterMockRecorder) RowsToMap(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RowsToMap", reflect.TypeOf((*MockQuerySeter)(nil).RowsToMap), arg0, arg1, arg2)
}

// RowsToStruct mocks base method.
func (m *MockQuerySeter) RowsToStruct(arg0 interface{}, arg1, arg2 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RowsToStruct", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RowsToStruct indicates an expected call of RowsToStruct.
func (mr *MockQuerySeterMockRecorder) RowsToStruct(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RowsToStruct", reflect.TypeOf((*MockQuerySeter)(nil).RowsToStruct), arg0, arg1, arg2)
}

// SetCond mocks base method.
func (m *MockQuerySeter) SetCond(arg0 *orm.Condition) orm.QuerySeter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCond", arg0)
	ret0, _ := ret[0].(orm.QuerySeter)
	return ret0
}

// SetCond indicates an expected call of SetCond.
func (mr *MockQuerySeterMockRecorder) SetCond(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCond", reflect.TypeOf((*MockQuerySeter)(nil).SetCond), arg0)
}

// Update mocks base method.
func (m *MockQuerySeter) Update(arg0 orm.Params) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockQuerySeterMockRecorder) Update(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockQuerySeter)(nil).Update), arg0)
}

// Values mocks base method.
func (m *MockQuerySeter) Values(arg0 *[]orm.Params, arg1 ...string) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Values", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Values indicates an expected call of Values.
func (mr *MockQuerySeterMockRecorder) Values(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Values", reflect.TypeOf((*MockQuerySeter)(nil).Values), varargs...)
}

// ValuesFlat mocks base method.
func (m *MockQuerySeter) ValuesFlat(arg0 *orm.ParamsList, arg1 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValuesFlat", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValuesFlat indicates an expected call of ValuesFlat.
func (mr *MockQuerySeterMockRecorder) ValuesFlat(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValuesFlat", reflect.TypeOf((*MockQuerySeter)(nil).ValuesFlat), arg0, arg1)
}

// ValuesList mocks base method.
func (m *MockQuerySeter) ValuesList(arg0 *[]orm.ParamsList, arg1 ...string) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValuesList", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValuesList indicates an expected call of ValuesList.
func (mr *MockQuerySeterMockRecorder) ValuesList(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValuesList", reflect.TypeOf((*MockQuerySeter)(nil).ValuesList), varargs...)
}
