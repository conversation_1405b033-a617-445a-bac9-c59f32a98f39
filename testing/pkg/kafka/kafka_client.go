// Code generated by mockery v2.43.0. DO NOT EDIT.

package kafka

import (
	mock "github.com/stretchr/testify/mock"
	kafka "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/kafka"
)

// KafkaClient is an autogenerated mock type for the ProducerInterface type
type KafkaClient struct {
	mock.Mock
}

// Close provides a mock function with given fields:
func (_m *KafkaClient) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SendMessage provides a mock function with given fields: topic, message
func (_m *KafkaClient) SendMessage(topic string, message string) (int32, int64, error) {
	ret := _m.Called(topic, message)

	if len(ret) == 0 {
		panic("no return value specified for SendMessage")
	}

	var r0 int32
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(string, string) (int32, int64, error)); ok {
		return rf(topic, message)
	}
	if rf, ok := ret.Get(0).(func(string, string) int32); ok {
		r0 = rf(topic, message)
	} else {
		r0 = ret.Get(0).(int32)
	}

	if rf, ok := ret.Get(1).(func(string, string) int64); ok {
		r1 = rf(topic, message)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(string, string) error); ok {
		r2 = rf(topic, message)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// StartProducer provides a mock function with given fields: o
func (_m *KafkaClient) StartProducer(o *kafka.Options) error {
	ret := _m.Called(o)

	if len(ret) == 0 {
		panic("no return value specified for StartProducer")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*kafka.Options) error); ok {
		r0 = rf(o)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewKafkaClient creates a new instance of KafkaClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewKafkaClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *KafkaClient {
	mock := &KafkaClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
