// Code generated by mockery v2.28.1. DO NOT EDIT.

package certificate

import (
	mock "github.com/stretchr/testify/mock"
	certificate "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/certificate"
)

// CertClient is an autogenerated mock type for the Interface type
type CertClient struct {
	mock.Mock
}

// AddServiceUseCert provides a mock function with given fields: certID, serviceName, resourceID
func (_m *CertClient) AddServiceUseCert(certID string, serviceName string, resourceID string) (*certificate.ServiceUseCertResponse, error) {
	ret := _m.Called(certID, serviceName, resourceID)

	var r0 *certificate.ServiceUseCertResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string) (*certificate.ServiceUseCertResponse, error)); ok {
		return rf(certID, serviceName, resourceID)
	}
	if rf, ok := ret.Get(0).(func(string, string, string) *certificate.ServiceUseCertResponse); ok {
		r0 = rf(certID, serviceName, resourceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*certificate.ServiceUseCertResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(certID, serviceName, resourceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCertDetail provides a mock function with given fields: certID
func (_m *CertClient) GetCertDetail(certID string) (*certificate.Cert, error) {
	ret := _m.Called(certID)

	var r0 *certificate.Cert
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*certificate.Cert, error)); ok {
		return rf(certID)
	}
	if rf, ok := ret.Get(0).(func(string) *certificate.Cert); ok {
		r0 = rf(certID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*certificate.Cert)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(certID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCertDetailContent provides a mock function with given fields: certID
func (_m *CertClient) GetCertDetailContent(certID string) (*certificate.CertContent, error) {
	ret := _m.Called(certID)

	var r0 *certificate.CertContent
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*certificate.CertContent, error)); ok {
		return rf(certID)
	}
	if rf, ok := ret.Get(0).(func(string) *certificate.CertContent); ok {
		r0 = rf(certID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*certificate.CertContent)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(certID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListCert provides a mock function with given fields:
func (_m *CertClient) ListCert() (*certificate.ListCertsResult, error) {
	ret := _m.Called()

	var r0 *certificate.ListCertsResult
	var r1 error
	if rf, ok := ret.Get(0).(func() (*certificate.ListCertsResult, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *certificate.ListCertsResult); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*certificate.ListCertsResult)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RemoveServiceUseCert provides a mock function with given fields: certID, serviceName, resourceID
func (_m *CertClient) RemoveServiceUseCert(certID string, serviceName string, resourceID string) error {
	ret := _m.Called(certID, serviceName, resourceID)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, string) error); ok {
		r0 = rf(certID, serviceName, resourceID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewCertClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewCertClient creates a new instance of CertClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewCertClient(t mockConstructorTestingTNewCertClient) *CertClient {
	mock := &CertClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
