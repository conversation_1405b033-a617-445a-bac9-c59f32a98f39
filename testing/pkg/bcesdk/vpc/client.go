// Code generated by mockery v2.28.1. DO NOT EDIT.

package vpc

import (
	servicesvpc "github.com/baidubce/bce-sdk-go/services/vpc"
	mock "github.com/stretchr/testify/mock"

	vpc "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
)

// Client is an autogenerated mock type for the ClientInterface type
type Client struct {
	mock.Mock
}

// CreateServiceNetwork provides a mock function with given fields: hexKey, resourceSourceName, accountId, args
func (_m *Client) CreateServiceNetwork(hexKey string, resourceSourceName string, accountId string, args *vpc.CreateServiceNetworkArgs) (*vpc.CreateServiceNetworkResponse, error) {
	ret := _m.Called(hexKey, resourceSourceName, accountId, args)

	var r0 *vpc.CreateServiceNetworkResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string, *vpc.CreateServiceNetworkArgs) (*vpc.CreateServiceNetworkResponse, error)); ok {
		return rf(hexKey, resourceSourceName, accountId, args)
	}
	if rf, ok := ret.Get(0).(func(string, string, string, *vpc.CreateServiceNetworkArgs) *vpc.CreateServiceNetworkResponse); ok {
		r0 = rf(hexKey, resourceSourceName, accountId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vpc.CreateServiceNetworkResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string, *vpc.CreateServiceNetworkArgs) error); ok {
		r1 = rf(hexKey, resourceSourceName, accountId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteServiceNetwork provides a mock function with given fields: id, resourceSourceName, clientToken, hexKey, accountId
func (_m *Client) DeleteServiceNetwork(id string, resourceSourceName string, clientToken string, hexKey string, accountId string) error {
	ret := _m.Called(id, resourceSourceName, clientToken, hexKey, accountId)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, string, string, string) error); ok {
		r0 = rf(id, resourceSourceName, clientToken, hexKey, accountId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetOccupiedIPs provides a mock function with given fields: subnetId
func (_m *Client) GetOccupiedIPs(subnetId string) ([]string, error) {
	ret := _m.Called(subnetId)

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]string, error)); ok {
		return rf(subnetId)
	}
	if rf, ok := ret.Get(0).(func(string) []string); ok {
		r0 = rf(subnetId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(subnetId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetVPCDetail provides a mock function with given fields: vpcId
func (_m *Client) GetVPCDetail(vpcId string) (*servicesvpc.GetVPCDetailResult, error) {
	ret := _m.Called(vpcId)

	var r0 *servicesvpc.GetVPCDetailResult
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*servicesvpc.GetVPCDetailResult, error)); ok {
		return rf(vpcId)
	}
	if rf, ok := ret.Get(0).(func(string) *servicesvpc.GetVPCDetailResult); ok {
		r0 = rf(vpcId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*servicesvpc.GetVPCDetailResult)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(vpcId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListServiceNetwork provides a mock function with given fields: vpcId, resourceSourceName, subnetId, name, marker, hexKey, accountId, maxKey
func (_m *Client) ListServiceNetwork(vpcId string, resourceSourceName string, subnetId string, name string, marker string, hexKey string, accountId string, maxKey int) (*vpc.ListServiceNetworkResponse, error) {
	ret := _m.Called(vpcId, resourceSourceName, subnetId, name, marker, hexKey, accountId, maxKey)

	var r0 *vpc.ListServiceNetworkResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string, string, string, string, string, int) (*vpc.ListServiceNetworkResponse, error)); ok {
		return rf(vpcId, resourceSourceName, subnetId, name, marker, hexKey, accountId, maxKey)
	}
	if rf, ok := ret.Get(0).(func(string, string, string, string, string, string, string, int) *vpc.ListServiceNetworkResponse); ok {
		r0 = rf(vpcId, resourceSourceName, subnetId, name, marker, hexKey, accountId, maxKey)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vpc.ListServiceNetworkResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string, string, string, string, string, int) error); ok {
		r1 = rf(vpcId, resourceSourceName, subnetId, name, marker, hexKey, accountId, maxKey)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MapByShortIds provides a mock function with given fields: shortIds
func (_m *Client) MapByShortIds(shortIds []string) (map[string]string, error) {
	ret := _m.Called(shortIds)

	var r0 map[string]string
	var r1 error
	if rf, ok := ret.Get(0).(func([]string) (map[string]string, error)); ok {
		return rf(shortIds)
	}
	if rf, ok := ret.Get(0).(func([]string) map[string]string); ok {
		r0 = rf(shortIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(shortIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewClient(t mockConstructorTestingTNewClient) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
