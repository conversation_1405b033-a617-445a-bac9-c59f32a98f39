// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/resourcegroup (interfaces: Client)

// Package uic is a generated GoMock package.
package uic

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	resourcegroup "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/resourcegroup"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// BindResourceToGroup mocks base method.
func (m *MockClient) BindResourceToGroup(arg0 string, arg1 bool, arg2 *resourcegroup.BindResourceToGroupArgs) (*resourcegroup.BindResourceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindResourceToGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(*resourcegroup.BindResourceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BindResourceToGroup indicates an expected call of BindResourceToGroup.
func (mr *MockClientMockRecorder) BindResourceToGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindResourceToGroup", reflect.TypeOf((*MockClient)(nil).BindResourceToGroup), arg0, arg1, arg2)
}

// ChangeResourceGroup mocks base method.
func (m *MockClient) ChangeResourceGroup(arg0 string, arg1 bool, arg2 *resourcegroup.ChangeResourceGroupArgs) (*resourcegroup.BindResourceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeResourceGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(*resourcegroup.BindResourceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeResourceGroup indicates an expected call of ChangeResourceGroup.
func (mr *MockClientMockRecorder) ChangeResourceGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeResourceGroup", reflect.TypeOf((*MockClient)(nil).ChangeResourceGroup), arg0, arg1, arg2)
}

// GetResGroupBatch mocks base method.
func (m *MockClient) GetResGroupBatch(arg0 string, arg1 *resourcegroup.ResGroupDetailRequest) (*resourcegroup.ResGroupDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResGroupBatch", arg0, arg1)
	ret0, _ := ret[0].(*resourcegroup.ResGroupDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResGroupBatch indicates an expected call of GetResGroupBatch.
func (mr *MockClientMockRecorder) GetResGroupBatch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResGroupBatch", reflect.TypeOf((*MockClient)(nil).GetResGroupBatch), arg0, arg1)
}

// GetResGroupLinkListBatch mocks base method.
func (m *MockClient) GetResGroupLinkListBatch(arg0 string, arg1 *resourcegroup.ResGroupDetailRequest) (*resourcegroup.ResGroupListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResGroupLinkListBatch", arg0, arg1)
	ret0, _ := ret[0].(*resourcegroup.ResGroupListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResGroupLinkListBatch indicates an expected call of GetResGroupLinkListBatch.
func (mr *MockClientMockRecorder) GetResGroupLinkListBatch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResGroupLinkListBatch", reflect.TypeOf((*MockClient)(nil).GetResGroupLinkListBatch), arg0, arg1)
}

// QueryGroupList mocks base method.
func (m *MockClient) QueryGroupList(arg0, arg1 string) (*resourcegroup.GroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryGroupList", arg0, arg1)
	ret0, _ := ret[0].(*resourcegroup.GroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryGroupList indicates an expected call of QueryGroupList.
func (mr *MockClientMockRecorder) QueryGroupList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryGroupList", reflect.TypeOf((*MockClient)(nil).QueryGroupList), arg0, arg1)
}

// UnbindResourceFromGroup mocks base method.
func (m *MockClient) UnbindResourceFromGroup(arg0 string, arg1 bool, arg2 *resourcegroup.BindResourceToGroupArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindResourceFromGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindResourceFromGroup indicates an expected call of UnbindResourceFromGroup.
func (mr *MockClientMockRecorder) UnbindResourceFromGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindResourceFromGroup", reflect.TypeOf((*MockClient)(nil).UnbindResourceFromGroup), arg0, arg1, arg2)
}
