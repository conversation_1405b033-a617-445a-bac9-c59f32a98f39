// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag (interfaces: Client)

// Package uic is a generated GoMock package.
package uic

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	logictag "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// CheckTags mocks base method.
func (m *MockClient) CheckTags(arg0 string, arg1 *logictag.TagsArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckTags", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckTags indicates an expected call of CheckTags.
func (mr *MockClientMockRecorder) CheckTags(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTags", reflect.TypeOf((*MockClient)(nil).CheckTags), arg0, arg1)
}

// CreateAndAssignTag mocks base method.
func (m *MockClient) CreateAndAssignTag(arg0 string, arg1 *logictag.CreateAndAssignArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAndAssignTag", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAndAssignTag indicates an expected call of CreateAndAssignTag.
func (mr *MockClientMockRecorder) CreateAndAssignTag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAndAssignTag", reflect.TypeOf((*MockClient)(nil).CreateAndAssignTag), arg0, arg1)
}

// CreateTags mocks base method.
func (m *MockClient) CreateTags(arg0 string, arg1 *logictag.TagsArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTags", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTags indicates an expected call of CreateTags.
func (mr *MockClientMockRecorder) CreateTags(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTags", reflect.TypeOf((*MockClient)(nil).CreateTags), arg0, arg1)
}

// DeleteTagAssociation mocks base method.
func (m *MockClient) DeleteTagAssociation(arg0 string, arg1 *logictag.DeleteTagAssociationsArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTagAssociation", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTagAssociation indicates an expected call of DeleteTagAssociation.
func (mr *MockClientMockRecorder) DeleteTagAssociation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTagAssociation", reflect.TypeOf((*MockClient)(nil).DeleteTagAssociation), arg0, arg1)
}

// DeleteTags mocks base method.
func (m *MockClient) DeleteTags(arg0 string, arg1 *logictag.TagsArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTags", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTags indicates an expected call of DeleteTags.
func (mr *MockClientMockRecorder) DeleteTags(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTags", reflect.TypeOf((*MockClient)(nil).DeleteTags), arg0, arg1)
}

// DeltaAssignTags mocks base method.
func (m *MockClient) DeltaAssignTags(arg0 string, arg1 *logictag.DeltaAssignTagsArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeltaAssignTags", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeltaAssignTags indicates an expected call of DeltaAssignTags.
func (mr *MockClientMockRecorder) DeltaAssignTags(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeltaAssignTags", reflect.TypeOf((*MockClient)(nil).DeltaAssignTags), arg0, arg1)
}

// ListTags mocks base method.
func (m *MockClient) ListTags(arg0 string, arg1 bool, arg2 *logictag.ListTagsArgs) (*logictag.ListTagsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTags", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logictag.ListTagsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTags indicates an expected call of ListTags.
func (mr *MockClientMockRecorder) ListTags(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTags", reflect.TypeOf((*MockClient)(nil).ListTags), arg0, arg1, arg2)
}

// ListTagsV2 mocks base method.
func (m *MockClient) ListTagsV2(arg0 string, arg1 bool, arg2 *logictag.ListTagsArgs) (*logictag.ListTagsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTagsV2", arg0, arg1, arg2)
	ret0, _ := ret[0].(*logictag.ListTagsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTagsV2 indicates an expected call of ListTagsV2.
func (mr *MockClientMockRecorder) ListTagsV2(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTagsV2", reflect.TypeOf((*MockClient)(nil).ListTagsV2), arg0, arg1, arg2)
}
