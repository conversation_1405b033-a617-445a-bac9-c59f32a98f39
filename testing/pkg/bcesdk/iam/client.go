// Code generated by mockery v2.28.1. DO NOT EDIT.

package iam

import (
	http "net/http"

	bcesdkiam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"

	iam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	mock "github.com/stretchr/testify/mock"
)

// Client is an autogenerated mock type for the ClientInterface type
type Client struct {
	mock.Mock
}

// AuthAndVerify provides a mock function with given fields: req, permissions
func (_m *Client) AuthAndVerify(req *http.Request, permissions *iam.PermissionRequest) (*iam.TokenAndVerifyResult, error) {
	ret := _m.Called(req, permissions)

	var r0 *iam.TokenAndVerifyResult
	var r1 error
	if rf, ok := ret.Get(0).(func(*http.Request, *iam.PermissionRequest) (*iam.TokenAndVerifyResult, error)); ok {
		return rf(req, permissions)
	}
	if rf, ok := ret.Get(0).(func(*http.Request, *iam.PermissionRequest) *iam.TokenAndVerifyResult); ok {
		r0 = rf(req, permissions)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iam.TokenAndVerifyResult)
		}
	}

	if rf, ok := ret.Get(1).(func(*http.Request, *iam.PermissionRequest) error); ok {
		r1 = rf(req, permissions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BatchVerifyUserPermission provides a mock function with given fields: userID, batchPermissions
func (_m *Client) BatchVerifyUserPermission(userID string, batchPermissions *bcesdkiam.BatchPermissionRequest) (*bcesdkiam.BatchVerifyResponse, error) {
	ret := _m.Called(userID, batchPermissions)

	var r0 *bcesdkiam.BatchVerifyResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *bcesdkiam.BatchPermissionRequest) (*bcesdkiam.BatchVerifyResponse, error)); ok {
		return rf(userID, batchPermissions)
	}
	if rf, ok := ret.Get(0).(func(string, *bcesdkiam.BatchPermissionRequest) *bcesdkiam.BatchVerifyResponse); ok {
		r0 = rf(userID, batchPermissions)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bcesdkiam.BatchVerifyResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *bcesdkiam.BatchPermissionRequest) error); ok {
		r1 = rf(userID, batchPermissions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUser provides a mock function with given fields: accountId, userId
func (_m *Client) GetUser(accountId string, userId string) (*bcesdkiam.UserInfo, error) {
	ret := _m.Called(accountId, userId)

	var r0 *bcesdkiam.UserInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*bcesdkiam.UserInfo, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) *bcesdkiam.UserInfo); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bcesdkiam.UserInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUsers provides a mock function with given fields: req
func (_m *Client) GetUsers(req *bcesdkiam.UserListRequest) (*bcesdkiam.UserListResponse, error) {
	ret := _m.Called(req)

	var r0 *bcesdkiam.UserListResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(*bcesdkiam.UserListRequest) (*bcesdkiam.UserListResponse, error)); ok {
		return rf(req)
	}
	if rf, ok := ret.Get(0).(func(*bcesdkiam.UserListRequest) *bcesdkiam.UserListResponse); ok {
		r0 = rf(req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bcesdkiam.UserListResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(*bcesdkiam.UserListRequest) error); ok {
		r1 = rf(req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ValidatorRequest provides a mock function with given fields: req
func (_m *Client) ValidatorRequest(req *http.Request) (*iam.Token, error) {
	ret := _m.Called(req)

	var r0 *iam.Token
	var r1 error
	if rf, ok := ret.Get(0).(func(*http.Request) (*iam.Token, error)); ok {
		return rf(req)
	}
	if rf, ok := ret.Get(0).(func(*http.Request) *iam.Token); ok {
		r0 = rf(req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iam.Token)
		}
	}

	if rf, ok := ret.Get(1).(func(*http.Request) error); ok {
		r1 = rf(req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VerifyUserPermission provides a mock function with given fields: userID, permissions
func (_m *Client) VerifyUserPermission(userID string, permissions *iam.PermissionRequest) (*bcesdkiam.VerifyResult, error) {
	ret := _m.Called(userID, permissions)

	var r0 *bcesdkiam.VerifyResult
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *iam.PermissionRequest) (*bcesdkiam.VerifyResult, error)); ok {
		return rf(userID, permissions)
	}
	if rf, ok := ret.Get(0).(func(string, *iam.PermissionRequest) *bcesdkiam.VerifyResult); ok {
		r0 = rf(userID, permissions)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bcesdkiam.VerifyResult)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *iam.PermissionRequest) error); ok {
		r1 = rf(userID, permissions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewClient(t mockConstructorTestingTNewClient) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
