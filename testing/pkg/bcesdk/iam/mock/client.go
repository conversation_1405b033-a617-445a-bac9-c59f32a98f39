// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam (interfaces: ClientInterface)

// Package iam is a generated GoMock package.
package iam

import (
	http "net/http"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	iam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	iam0 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
)

// MockClientInterface is a mock of ClientInterface interface.
type MockClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClientInterfaceMockRecorder
}

// MockClientInterfaceMockRecorder is the mock recorder for MockClientInterface.
type MockClientInterfaceMockRecorder struct {
	mock *MockClientInterface
}

// NewMockClientInterface creates a new mock instance.
func NewMockClientInterface(ctrl *gomock.Controller) *MockClientInterface {
	mock := &MockClientInterface{ctrl: ctrl}
	mock.recorder = &MockClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientInterface) EXPECT() *MockClientInterfaceMockRecorder {
	return m.recorder
}

// AuthAndVerify mocks base method.
func (m *MockClientInterface) AuthAndVerify(arg0 *http.Request, arg1 *iam.PermissionRequest) (*iam.TokenAndVerifyResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthAndVerify", arg0, arg1)
	ret0, _ := ret[0].(*iam.TokenAndVerifyResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AuthAndVerify indicates an expected call of AuthAndVerify.
func (mr *MockClientInterfaceMockRecorder) AuthAndVerify(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthAndVerify", reflect.TypeOf((*MockClientInterface)(nil).AuthAndVerify), arg0, arg1)
}

// BatchVerifyUserPermission mocks base method.
func (m *MockClientInterface) BatchVerifyUserPermission(arg0 string, arg1 *iam0.BatchPermissionRequest) (*iam0.BatchVerifyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchVerifyUserPermission", arg0, arg1)
	ret0, _ := ret[0].(*iam0.BatchVerifyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchVerifyUserPermission indicates an expected call of BatchVerifyUserPermission.
func (mr *MockClientInterfaceMockRecorder) BatchVerifyUserPermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchVerifyUserPermission", reflect.TypeOf((*MockClientInterface)(nil).BatchVerifyUserPermission), arg0, arg1)
}

// GetUser mocks base method.
func (m *MockClientInterface) GetUser(arg0, arg1 string) (*iam0.UserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUser", arg0, arg1)
	ret0, _ := ret[0].(*iam0.UserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUser indicates an expected call of GetUser.
func (mr *MockClientInterfaceMockRecorder) GetUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUser", reflect.TypeOf((*MockClientInterface)(nil).GetUser), arg0, arg1)
}

// GetUsers mocks base method.
func (m *MockClientInterface) GetUsers(arg0 *iam0.UserListRequest) (*iam0.UserListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsers", arg0)
	ret0, _ := ret[0].(*iam0.UserListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsers indicates an expected call of GetUsers.
func (mr *MockClientInterfaceMockRecorder) GetUsers(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsers", reflect.TypeOf((*MockClientInterface)(nil).GetUsers), arg0)
}

// ValidatorRequest mocks base method.
func (m *MockClientInterface) ValidatorRequest(arg0 *http.Request) (*iam.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidatorRequest", arg0)
	ret0, _ := ret[0].(*iam.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidatorRequest indicates an expected call of ValidatorRequest.
func (mr *MockClientInterfaceMockRecorder) ValidatorRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidatorRequest", reflect.TypeOf((*MockClientInterface)(nil).ValidatorRequest), arg0)
}

// VerifyUserPermission mocks base method.
func (m *MockClientInterface) VerifyUserPermission(arg0 string, arg1 *iam.PermissionRequest) (*iam0.VerifyResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyUserPermission", arg0, arg1)
	ret0, _ := ret[0].(*iam0.VerifyResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyUserPermission indicates an expected call of VerifyUserPermission.
func (mr *MockClientInterfaceMockRecorder) VerifyUserPermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyUserPermission", reflect.TypeOf((*MockClientInterface)(nil).VerifyUserPermission), arg0, arg1)
}
