// Code generated by mockery v2.28.1. DO NOT EDIT.

package bcd

import mock "github.com/stretchr/testify/mock"

// BcdClient is an autogenerated mock type for the Interface type
type BcdClient struct {
	mock.Mock
}

// CheckDomainICP provides a mock function with given fields: domainName
func (_m *BcdClient) CheckDomainICP(domainName string) (bool, error) {
	ret := _m.Called(domainName)

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (bool, error)); ok {
		return rf(domainName)
	}
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(domainName)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(domainName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewBcdClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewBcdClient creates a new instance of BcdClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewBcdClient(t mockConstructorTestingTNewBcdClient) *BcdClient {
	mock := &BcdClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
