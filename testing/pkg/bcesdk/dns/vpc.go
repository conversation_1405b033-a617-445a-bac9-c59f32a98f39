// Code generated by mockery v2.14.0. DO NOT EDIT.

package dns

import (
	mock "github.com/stretchr/testify/mock"
	dns "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"
)

// VpcDnsClient is an autogenerated mock type for the VpcDnsClientInterface type
type VpcDnsClient struct {
	mock.Mock
}

// CreateVpcRecord provides a mock function with given fields: requestId, hexKey, accountId, args
func (_m *VpcDnsClient) CreateVpcRecord(requestId string, hexKey string, accountId string, args *dns.CreateVPCDNSRecordRequest) (*dns.CreateVPCDNSRecordResponse, error) {
	ret := _m.Called(requestId, hexKey, accountId, args)

	var r0 *dns.CreateVPCDNSRecordResponse
	if rf, ok := ret.Get(0).(func(string, string, string, *dns.CreateVPCDNSRecordRequest) *dns.CreateVPCDNSRecordResponse); ok {
		r0 = rf(requestId, hexKey, accountId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dns.CreateVPCDNSRecordResponse)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string, *dns.CreateVPCDNSRecordRequest) error); ok {
		r1 = rf(requestId, hexKey, accountId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteVpcRecord provides a mock function with given fields: requestId, hexKey, accountId, recordId
func (_m *VpcDnsClient) DeleteVpcRecord(requestId string, hexKey string, accountId string, recordId string) error {
	ret := _m.Called(requestId, hexKey, accountId, recordId)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, string, string) error); ok {
		r0 = rf(requestId, hexKey, accountId, recordId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetVpcRecord provides a mock function with given fields: requestId, hexKey, accountId, recordId
func (_m *VpcDnsClient) GetVpcRecord(requestId string, hexKey string, accountId string, recordId string) (*dns.GetVpcDnsRecordResponse, error) {
	ret := _m.Called(requestId, hexKey, accountId, recordId)

	var r0 *dns.GetVpcDnsRecordResponse
	if rf, ok := ret.Get(0).(func(string, string, string, string) *dns.GetVpcDnsRecordResponse); ok {
		r0 = rf(requestId, hexKey, accountId, recordId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dns.GetVpcDnsRecordResponse)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string, string) error); ok {
		r1 = rf(requestId, hexKey, accountId, recordId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListVpcRecord provides a mock function with given fields: requestId, hexKey, accountId, vpcId, name, value, marker, maxKeys
func (_m *VpcDnsClient) ListVpcRecord(requestId string, hexKey string, accountId string, vpcId string, name string, value string, marker string, maxKeys string) (*dns.ListVpcDnsRecordResponse, error) {
	ret := _m.Called(requestId, hexKey, accountId, vpcId, name, value, marker, maxKeys)

	var r0 *dns.ListVpcDnsRecordResponse
	if rf, ok := ret.Get(0).(func(string, string, string, string, string, string, string, string) *dns.ListVpcDnsRecordResponse); ok {
		r0 = rf(requestId, hexKey, accountId, vpcId, name, value, marker, maxKeys)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dns.ListVpcDnsRecordResponse)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string, string, string, string, string, string) error); ok {
		r1 = rf(requestId, hexKey, accountId, vpcId, name, value, marker, maxKeys)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateVpcRecord provides a mock function with given fields: requestId, hexKey, accountId, recordId, args
func (_m *VpcDnsClient) UpdateVpcRecord(requestId string, hexKey string, accountId string, recordId string, args *dns.UpdateVPCDNSRecordResquest) error {
	ret := _m.Called(requestId, hexKey, accountId, recordId, args)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, string, string, *dns.UpdateVPCDNSRecordResquest) error); ok {
		r0 = rf(requestId, hexKey, accountId, recordId, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewVpcDnsClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewVpcDnsClient creates a new instance of VpcDnsClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewVpcDnsClient(t mockConstructorTestingTNewVpcDnsClient) *VpcDnsClient {
	mock := &VpcDnsClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
