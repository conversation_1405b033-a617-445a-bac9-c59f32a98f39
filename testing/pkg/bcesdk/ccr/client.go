// Code generated by mockery v2.43.0. DO NOT EDIT.

package ccr

import (
	mock "github.com/stretchr/testify/mock"
	ccr "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr"
)

// Client is an autogenerated mock type for the ClientInterface type
type Client struct {
	mock.Mock
}

// CreateTemporaryPassword provides a mock function with given fields: instanceID, args
func (_m *Client) CreateTemporaryPassword(instanceID string, args *ccr.TemporaryPasswordArgs) (*ccr.TemporaryPasswordResponse, error) {
	ret := _m.Called(instanceID, args)

	if len(ret) == 0 {
		panic("no return value specified for CreateTemporaryPassword")
	}

	var r0 *ccr.TemporaryPasswordResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *ccr.TemporaryPasswordArgs) (*ccr.TemporaryPasswordResponse, error)); ok {
		return rf(instanceID, args)
	}
	if rf, ok := ret.Get(0).(func(string, *ccr.TemporaryPasswordArgs) *ccr.TemporaryPasswordResponse); ok {
		r0 = rf(instanceID, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ccr.TemporaryPasswordResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *ccr.TemporaryPasswordArgs) error); ok {
		r1 = rf(instanceID, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListInstances provides a mock function with given fields: args
func (_m *Client) ListInstances(args *ccr.ListInstanceRequest) (*ccr.ListInstanceResponse, error) {
	ret := _m.Called(args)

	if len(ret) == 0 {
		panic("no return value specified for ListInstances")
	}

	var r0 *ccr.ListInstanceResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(*ccr.ListInstanceRequest) (*ccr.ListInstanceResponse, error)); ok {
		return rf(args)
	}
	if rf, ok := ret.Get(0).(func(*ccr.ListInstanceRequest) *ccr.ListInstanceResponse); ok {
		r0 = rf(args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ccr.ListInstanceResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(*ccr.ListInstanceRequest) error); ok {
		r1 = rf(args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
