// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr (interfaces: ClientInterface)

// Package ccr is a generated GoMock package.
package ccr

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	ccr "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr"
)

// MockClientInterface is a mock of ClientInterface interface.
type MockClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClientInterfaceMockRecorder
}

// MockClientInterfaceMockRecorder is the mock recorder for MockClientInterface.
type MockClientInterfaceMockRecorder struct {
	mock *MockClientInterface
}

// NewMockClientInterface creates a new mock instance.
func NewMockClientInterface(ctrl *gomock.Controller) *MockClientInterface {
	mock := &MockClientInterface{ctrl: ctrl}
	mock.recorder = &MockClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientInterface) EXPECT() *MockClientInterfaceMockRecorder {
	return m.recorder
}

// CreateTemporaryPassword mocks base method.
func (m *MockClientInterface) CreateTemporaryPassword(arg0 string, arg1 *ccr.TemporaryPasswordArgs) (*ccr.TemporaryPasswordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTemporaryPassword", arg0, arg1)
	ret0, _ := ret[0].(*ccr.TemporaryPasswordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTemporaryPassword indicates an expected call of CreateTemporaryPassword.
func (mr *MockClientInterfaceMockRecorder) CreateTemporaryPassword(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTemporaryPassword", reflect.TypeOf((*MockClientInterface)(nil).CreateTemporaryPassword), arg0, arg1)
}

// ListInstances mocks base method.
func (m *MockClientInterface) ListInstances(arg0 *ccr.ListInstanceRequest) (*ccr.ListInstanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstances", arg0)
	ret0, _ := ret[0].(*ccr.ListInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstances indicates an expected call of ListInstances.
func (mr *MockClientInterfaceMockRecorder) ListInstances(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstances", reflect.TypeOf((*MockClientInterface)(nil).ListInstances), arg0)
}
