// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr/personal (interfaces: Interface)

// Package personal is a generated GoMock package.
package personal

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	personal "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr/personal"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CreatePersonalTemporaryPassword mocks base method.
func (m *MockInterface) CreatePersonalTemporaryPassword(arg0 *personal.TemporaryPasswordArgs) (*personal.TemporaryPasswordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePersonalTemporaryPassword", arg0)
	ret0, _ := ret[0].(*personal.TemporaryPasswordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePersonalTemporaryPassword indicates an expected call of CreatePersonalTemporaryPassword.
func (mr *MockInterfaceMockRecorder) CreatePersonalTemporaryPassword(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePersonalTemporaryPassword", reflect.TypeOf((*MockInterface)(nil).CreatePersonalTemporaryPassword), arg0)
}
