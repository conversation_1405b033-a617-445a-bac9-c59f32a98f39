// Code generated by mockery v2.28.1. DO NOT EDIT.

package billing

import (
	mock "github.com/stretchr/testify/mock"
	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
)

// FacadeClient is an autogenerated mock type for the FacadeClientInterface type
type FacadeClient struct {
	mock.Mock
}

// CreateFacadeOrder provides a mock function with given fields: requestId, args
func (_m *FacadeClient) CreateFacadeOrder(requestId string, args *billing.OrderFacadeRequest) (*billing.OrderFacadeResponse, error) {
	ret := _m.Called(requestId, args)

	var r0 *billing.OrderFacadeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *billing.OrderFacadeRequest) (*billing.OrderFacadeResponse, error)); ok {
		return rf(requestId, args)
	}
	if rf, ok := ret.Get(0).(func(string, *billing.OrderFacadeRequest) *billing.OrderFacadeResponse); ok {
		r0 = rf(requestId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*billing.OrderFacadeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *billing.OrderFacadeRequest) error); ok {
		r1 = rf(requestId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewFacadeClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewFacadeClient creates a new instance of FacadeClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewFacadeClient(t mockConstructorTestingTNewFacadeClient) *FacadeClient {
	mock := &FacadeClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
