// Code generated by mockery v2.28.1. DO NOT EDIT.

package billing

import (
	mock "github.com/stretchr/testify/mock"
	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
)

// MockAutoRenewClient is an autogenerated mock type for the AutoRenewClientInterface type
type MockAutoRenewClient struct {
	mock.Mock
}

// CreateAutoRenewRule provides a mock function with given fields: requestId, args
func (_m *MockAutoRenewClient) CreateAutoRenewRule(requestId string, args *billing.CreateAutoRenewRule) error {
	ret := _m.Called(requestId, args)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, *billing.CreateAutoRenewRule) error); ok {
		r0 = rf(requestId, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ListAutoRenewRule provides a mock function with given fields: requestId, args
func (_m *MockAutoRenewClient) ListAutoRenewRule(requestId string, args *billing.ListAutoRenewRule) ([]*billing.ListAutoRenewRuleResponse, error) {
	ret := _m.Called(requestId, args)

	var r0 []*billing.ListAutoRenewRuleResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *billing.ListAutoRenewRule) ([]*billing.ListAutoRenewRuleResponse, error)); ok {
		return rf(requestId, args)
	}
	if rf, ok := ret.Get(0).(func(string, *billing.ListAutoRenewRule) []*billing.ListAutoRenewRuleResponse); ok {
		r0 = rf(requestId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*billing.ListAutoRenewRuleResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *billing.ListAutoRenewRule) error); ok {
		r1 = rf(requestId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockAutoRenewClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockAutoRenewClient creates a new instance of MockAutoRenewClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockAutoRenewClient(t mockConstructorTestingTNewMockAutoRenewClient) *MockAutoRenewClient {
	mock := &MockAutoRenewClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
