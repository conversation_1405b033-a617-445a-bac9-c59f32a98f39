// Code generated by mockery v2.14.0. DO NOT EDIT.

package billing

import (
	mock "github.com/stretchr/testify/mock"
	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
)

// AutoRenewClient is an autogenerated mock type for the AutoRenewClientInterface type
type AutoRenewClient struct {
	mock.Mock
}

// CreateAutoRenewRule provides a mock function with given fields: requestId, args
func (_m *AutoRenewClient) CreateAutoRenewRule(requestId string, args *billing.CreateAutoRenewRule) error {
	ret := _m.Called(requestId, args)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, *billing.CreateAutoRenewRule) error); ok {
		r0 = rf(requestId, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ListAutoRenewRule provides a mock function with given fields: requestId, args
func (_m *AutoRenewClient) ListAutoRenewRule(requestId string, args *billing.ListAutoRenewRule) ([]*billing.ListAutoRenewRuleResponse, error) {
	ret := _m.Called(requestId, args)

	var r0 []*billing.ListAutoRenewRuleResponse
	if rf, ok := ret.Get(0).(func(string, *billing.ListAutoRenewRule) []*billing.ListAutoRenewRuleResponse); ok {
		r0 = rf(requestId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*billing.ListAutoRenewRuleResponse)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, *billing.ListAutoRenewRule) error); ok {
		r1 = rf(requestId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewAutoRenewClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewAutoRenewClient creates a new instance of AutoRenewClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewAutoRenewClient(t mockConstructorTestingTNewAutoRenewClient) *AutoRenewClient {
	mock := &AutoRenewClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
