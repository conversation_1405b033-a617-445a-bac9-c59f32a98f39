// Code generated by mockery v2.28.1. DO NOT EDIT.

package billing

import (
	mock "github.com/stretchr/testify/mock"
	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
)

// OrderClient is an autogenerated mock type for the OrderClientInterface type
type OrderClient struct {
	mock.Mock
}

// GetOrderDetail provides a mock function with given fields: requestId, orderId
func (_m *OrderClient) GetOrderDetail(requestId string, orderId string) (*billing.GetOrderDetail, error) {
	ret := _m.Called(requestId, orderId)

	var r0 *billing.GetOrderDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*billing.GetOrderDetail, error)); ok {
		return rf(requestId, orderId)
	}
	if rf, ok := ret.Get(0).(func(string, string) *billing.GetOrderDetail); ok {
		r0 = rf(requestId, orderId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*billing.GetOrderDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(requestId, orderId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateOrder provides a mock function with given fields: requestId, orderId, args
func (_m *OrderClient) UpdateOrder(requestId string, orderId string, args *billing.UpdateOrderRequest) error {
	ret := _m.Called(requestId, orderId, args)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, *billing.UpdateOrderRequest) error); ok {
		r0 = rf(requestId, orderId, args)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewOrderClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewOrderClient creates a new instance of OrderClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewOrderClient(t mockConstructorTestingTNewOrderClient) *OrderClient {
	mock := &OrderClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
