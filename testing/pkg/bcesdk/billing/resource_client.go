// Code generated by mockery v2.28.1. DO NOT EDIT.

package billing

import (
	mock "github.com/stretchr/testify/mock"
	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
)

// ResourceClient is an autogenerated mock type for the ResourceClientInterface type
type ResourceClient struct {
	mock.Mock
}

// GetResourceDetail provides a mock function with given fields: requestId, args
func (_m *ResourceClient) GetResourceDetail(requestId string, args *billing.GetResourceDetailRequest) (*billing.GetResourceDetailResponse, error) {
	ret := _m.Called(requestId, args)

	var r0 *billing.GetResourceDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *billing.GetResourceDetailRequest) (*billing.GetResourceDetailResponse, error)); ok {
		return rf(requestId, args)
	}
	if rf, ok := ret.Get(0).(func(string, *billing.GetResourceDetailRequest) *billing.GetResourceDetailResponse); ok {
		r0 = rf(requestId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*billing.GetResourceDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *billing.GetResourceDetailRequest) error); ok {
		r1 = rf(requestId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListResourceDetail provides a mock function with given fields: requestId, args
func (_m *ResourceClient) ListResourceDetail(requestId string, args *billing.ListResourceDetailRequest) (*billing.ListResourceDetailResponse, error) {
	ret := _m.Called(requestId, args)

	var r0 *billing.ListResourceDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *billing.ListResourceDetailRequest) (*billing.ListResourceDetailResponse, error)); ok {
		return rf(requestId, args)
	}
	if rf, ok := ret.Get(0).(func(string, *billing.ListResourceDetailRequest) *billing.ListResourceDetailResponse); ok {
		r0 = rf(requestId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*billing.ListResourceDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *billing.ListResourceDetailRequest) error); ok {
		r1 = rf(requestId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewResourceClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewResourceClient creates a new instance of ResourceClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewResourceClient(t mockConstructorTestingTNewResourceClient) *ResourceClient {
	mock := &ResourceClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
