// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/uic (interfaces: Interface)

// Package uic is a generated GoMock package.
package uic

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	uic "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/uic"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CreateOrUpdateProjectAccount mocks base method.
func (m *MockInterface) CreateOrUpdateProjectAccount(arg0 *uic.ProjectAccountRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateProjectAccount", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrUpdateProjectAccount indicates an expected call of CreateOrUpdateProjectAccount.
func (mr *MockInterfaceMockRecorder) CreateOrUpdateProjectAccount(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateProjectAccount", reflect.TypeOf((*MockInterface)(nil).CreateOrUpdateProjectAccount), arg0)
}

// GetAccountsTree mocks base method.
func (m *MockInterface) GetAccountsTree(arg0 string) ([]uic.AccountTree, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountsTree", arg0)
	ret0, _ := ret[0].([]uic.AccountTree)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountsTree indicates an expected call of GetAccountsTree.
func (mr *MockInterfaceMockRecorder) GetAccountsTree(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountsTree", reflect.TypeOf((*MockInterface)(nil).GetAccountsTree), arg0)
}

// GetEmailGroupInfo mocks base method.
func (m *MockInterface) GetEmailGroupInfo(arg0 string) (*uic.UicEmailGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmailGroupInfo", arg0)
	ret0, _ := ret[0].(*uic.UicEmailGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmailGroupInfo indicates an expected call of GetEmailGroupInfo.
func (mr *MockInterfaceMockRecorder) GetEmailGroupInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmailGroupInfo", reflect.TypeOf((*MockInterface)(nil).GetEmailGroupInfo), arg0)
}

// GetLuoshuAccount mocks base method.
func (m *MockInterface) GetLuoshuAccount(arg0 string) ([]uic.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLuoshuAccount", arg0)
	ret0, _ := ret[0].([]uic.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLuoshuAccount indicates an expected call of GetLuoshuAccount.
func (mr *MockInterfaceMockRecorder) GetLuoshuAccount(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLuoshuAccount", reflect.TypeOf((*MockInterface)(nil).GetLuoshuAccount), arg0)
}

// GetUicUser mocks base method.
func (m *MockInterface) GetUicUser(arg0 string) (*uic.UicUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUicUser", arg0)
	ret0, _ := ret[0].(*uic.UicUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUicUser indicates an expected call of GetUicUser.
func (mr *MockInterfaceMockRecorder) GetUicUser(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUicUser", reflect.TypeOf((*MockInterface)(nil).GetUicUser), arg0)
}

// GetUserEmailGroup mocks base method.
func (m *MockInterface) GetUserEmailGroup(arg0 string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserEmailGroup", arg0)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserEmailGroup indicates an expected call of GetUserEmailGroup.
func (mr *MockInterfaceMockRecorder) GetUserEmailGroup(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserEmailGroup", reflect.TypeOf((*MockInterface)(nil).GetUserEmailGroup), arg0)
}

// PatchUicUser mocks base method.
func (m *MockInterface) PatchUicUser(arg0 *uic.UicUser, arg1 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PatchUicUser", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PatchUicUser indicates an expected call of PatchUicUser.
func (mr *MockInterfaceMockRecorder) PatchUicUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PatchUicUser", reflect.TypeOf((*MockInterface)(nil).PatchUicUser), arg0, arg1)
}
