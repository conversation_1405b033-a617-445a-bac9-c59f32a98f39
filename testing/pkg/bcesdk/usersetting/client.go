// Code generated by mockery v2.43.0. DO NOT EDIT.

package usersetting

import (
	mock "github.com/stretchr/testify/mock"
	usersetting "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
)

// MockClient is an autogenerated mock type for the ClientInterface type
type MockClient struct {
	mock.Mock
}

// CheckFeatureACL provides a mock function with given fields: requestId, args
func (_m *MockClient) CheckFeatureACL(requestId string, args *usersetting.FeatureAclRequest) (*usersetting.FeatureAclResponse, error) {
	ret := _m.Called(requestId, args)

	if len(ret) == 0 {
		panic("no return value specified for CheckFeatureACL")
	}

	var r0 *usersetting.FeatureAclResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *usersetting.FeatureAclRequest) (*usersetting.FeatureAclResponse, error)); ok {
		return rf(requestId, args)
	}
	if rf, ok := ret.Get(0).(func(string, *usersetting.FeatureAclRequest) *usersetting.FeatureAclResponse); ok {
		r0 = rf(requestId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*usersetting.FeatureAclResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *usersetting.FeatureAclRequest) error); ok {
		r1 = rf(requestId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserQuota provides a mock function with given fields: requestId, args
func (_m *MockClient) GetUserQuota(requestId string, args *usersetting.GetQuotaRequest) (*usersetting.GetQuotaResponse, error) {
	ret := _m.Called(requestId, args)

	if len(ret) == 0 {
		panic("no return value specified for GetUserQuota")
	}

	var r0 *usersetting.GetQuotaResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *usersetting.GetQuotaRequest) (*usersetting.GetQuotaResponse, error)); ok {
		return rf(requestId, args)
	}
	if rf, ok := ret.Get(0).(func(string, *usersetting.GetQuotaRequest) *usersetting.GetQuotaResponse); ok {
		r0 = rf(requestId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*usersetting.GetQuotaResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *usersetting.GetQuotaRequest) error); ok {
		r1 = rf(requestId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListUserQuotas provides a mock function with given fields: requestId, args
func (_m *MockClient) ListUserQuotas(requestId string, args *usersetting.ListQuotasRequest) (*usersetting.ListQuotaResponse, error) {
	ret := _m.Called(requestId, args)

	if len(ret) == 0 {
		panic("no return value specified for ListUserQuotas")
	}

	var r0 *usersetting.ListQuotaResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *usersetting.ListQuotasRequest) (*usersetting.ListQuotaResponse, error)); ok {
		return rf(requestId, args)
	}
	if rf, ok := ret.Get(0).(func(string, *usersetting.ListQuotasRequest) *usersetting.ListQuotaResponse); ok {
		r0 = rf(requestId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*usersetting.ListQuotaResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *usersetting.ListQuotasRequest) error); ok {
		r1 = rf(requestId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockClient creates a new instance of MockClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClient {
	mock := &MockClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
