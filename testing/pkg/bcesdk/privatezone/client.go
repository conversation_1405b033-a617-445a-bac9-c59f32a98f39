// Code generated by mockery v2.28.1. DO NOT EDIT.

package privatezone

import (
	mock "github.com/stretchr/testify/mock"
	privatezone "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/privatezone"
)

// PrivateZoneClient is an autogenerated mock type for the Interface type
type PrivateZoneClient struct {
	mock.Mock
}

// AddRecord provides a mock function with given fields: requestId, zoneId, args
func (_m *PrivateZoneClient) AddRecord(requestId string, zoneId string, args *privatezone.AddRecordArgs) (*privatezone.AddRecordResponse, error) {
	ret := _m.Called(requestId, zoneId, args)

	var r0 *privatezone.AddRecordResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, *privatezone.AddRecordArgs) (*privatezone.AddRecordResponse, error)); ok {
		return rf(requestId, zoneId, args)
	}
	if rf, ok := ret.Get(0).(func(string, string, *privatezone.AddRecordArgs) *privatezone.AddRecordResponse); ok {
		r0 = rf(requestId, zoneId, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*privatezone.AddRecordResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, *privatezone.AddRecordArgs) error); ok {
		r1 = rf(requestId, zoneId, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BindVPC provides a mock function with given fields: requestId, zoneId, arg
func (_m *PrivateZoneClient) BindVPC(requestId string, zoneId string, arg *privatezone.BindVPCArgs) error {
	ret := _m.Called(requestId, zoneId, arg)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, *privatezone.BindVPCArgs) error); ok {
		r0 = rf(requestId, zoneId, arg)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateZone provides a mock function with given fields: requestId, arg
func (_m *PrivateZoneClient) CreateZone(requestId string, arg *privatezone.CreateZoneArgs) (*privatezone.CreateZoneResponse, error) {
	ret := _m.Called(requestId, arg)

	var r0 *privatezone.CreateZoneResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, *privatezone.CreateZoneArgs) (*privatezone.CreateZoneResponse, error)); ok {
		return rf(requestId, arg)
	}
	if rf, ok := ret.Get(0).(func(string, *privatezone.CreateZoneArgs) *privatezone.CreateZoneResponse); ok {
		r0 = rf(requestId, arg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*privatezone.CreateZoneResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, *privatezone.CreateZoneArgs) error); ok {
		r1 = rf(requestId, arg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteRecord provides a mock function with given fields: requestId, recordId, clientToken
func (_m *PrivateZoneClient) DeleteRecord(requestId string, recordId string, clientToken string) error {
	ret := _m.Called(requestId, recordId, clientToken)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, string) error); ok {
		r0 = rf(requestId, recordId, clientToken)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteZone provides a mock function with given fields: requestId, zoneId, clientToken
func (_m *PrivateZoneClient) DeleteZone(requestId string, zoneId string, clientToken string) error {
	ret := _m.Called(requestId, zoneId, clientToken)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, string) error); ok {
		r0 = rf(requestId, zoneId, clientToken)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetZone provides a mock function with given fields: requestId, zoneId
func (_m *PrivateZoneClient) GetZone(requestId string, zoneId string) (*privatezone.ZoneInfo, error) {
	ret := _m.Called(requestId, zoneId)

	var r0 *privatezone.ZoneInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*privatezone.ZoneInfo, error)); ok {
		return rf(requestId, zoneId)
	}
	if rf, ok := ret.Get(0).(func(string, string) *privatezone.ZoneInfo); ok {
		r0 = rf(requestId, zoneId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*privatezone.ZoneInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(requestId, zoneId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListRecord provides a mock function with given fields: requestId, zoneId, marker, maxKeys
func (_m *PrivateZoneClient) ListRecord(requestId string, zoneId string, marker string, maxKeys int64) (*privatezone.ListResolveResponse, error) {
	ret := _m.Called(requestId, zoneId, marker, maxKeys)

	var r0 *privatezone.ListResolveResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string, int64) (*privatezone.ListResolveResponse, error)); ok {
		return rf(requestId, zoneId, marker, maxKeys)
	}
	if rf, ok := ret.Get(0).(func(string, string, string, int64) *privatezone.ListResolveResponse); ok {
		r0 = rf(requestId, zoneId, marker, maxKeys)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*privatezone.ListResolveResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string, int64) error); ok {
		r1 = rf(requestId, zoneId, marker, maxKeys)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListZone provides a mock function with given fields: requestId, marker, maxKeys
func (_m *PrivateZoneClient) ListZone(requestId string, marker string, maxKeys int64) (*privatezone.ListZoneResponse, error) {
	ret := _m.Called(requestId, marker, maxKeys)

	var r0 *privatezone.ListZoneResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, int64) (*privatezone.ListZoneResponse, error)); ok {
		return rf(requestId, marker, maxKeys)
	}
	if rf, ok := ret.Get(0).(func(string, string, int64) *privatezone.ListZoneResponse); ok {
		r0 = rf(requestId, marker, maxKeys)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*privatezone.ListZoneResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, int64) error); ok {
		r1 = rf(requestId, marker, maxKeys)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UnbindVPC provides a mock function with given fields: requestId, zoneId, arg
func (_m *PrivateZoneClient) UnbindVPC(requestId string, zoneId string, arg *privatezone.BindVPCArgs) error {
	ret := _m.Called(requestId, zoneId, arg)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, *privatezone.BindVPCArgs) error); ok {
		r0 = rf(requestId, zoneId, arg)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewPrivateZoneClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewPrivateZoneClient creates a new instance of PrivateZoneClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewPrivateZoneClient(t mockConstructorTestingTNewPrivateZoneClient) *PrivateZoneClient {
	mock := &PrivateZoneClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
