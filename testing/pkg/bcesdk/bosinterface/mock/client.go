// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bosinterface (interfaces: BosInterface)

// Package bosinterface is a generated GoMock package.
package bosinterface

import (
	io "io"
	reflect "reflect"

	bce "github.com/baidubce/bce-sdk-go/bce"
	bos "github.com/baidubce/bce-sdk-go/services/bos"
	api "github.com/baidubce/bce-sdk-go/services/bos/api"
	gomock "github.com/golang/mock/gomock"
)

// MockBosInterface is a mock of BosInterface interface.
type MockBosInterface struct {
	ctrl     *gomock.Controller
	recorder *MockBosInterfaceMockRecorder
}

// MockBosInterfaceMockRecorder is the mock recorder for MockBosInterface.
type MockBosInterfaceMockRecorder struct {
	mock *MockBosInterface
}

// NewMockBosInterface creates a new mock instance.
func NewMockBosInterface(ctrl *gomock.Controller) *MockBosInterface {
	mock := &MockBosInterface{ctrl: ctrl}
	mock.recorder = &MockBosInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBosInterface) EXPECT() *MockBosInterfaceMockRecorder {
	return m.recorder
}

// AbortMultipartUpload mocks base method.
func (m *MockBosInterface) AbortMultipartUpload(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AbortMultipartUpload", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AbortMultipartUpload indicates an expected call of AbortMultipartUpload.
func (mr *MockBosInterfaceMockRecorder) AbortMultipartUpload(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AbortMultipartUpload", reflect.TypeOf((*MockBosInterface)(nil).AbortMultipartUpload), arg0, arg1, arg2)
}

// AppendObject mocks base method.
func (m *MockBosInterface) AppendObject(arg0, arg1 string, arg2 *bce.Body, arg3 *api.AppendObjectArgs) (*api.AppendObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AppendObject", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.AppendObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AppendObject indicates an expected call of AppendObject.
func (mr *MockBosInterfaceMockRecorder) AppendObject(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AppendObject", reflect.TypeOf((*MockBosInterface)(nil).AppendObject), arg0, arg1, arg2, arg3)
}

// BasicCopyObject mocks base method.
func (m *MockBosInterface) BasicCopyObject(arg0, arg1, arg2, arg3 string) (*api.CopyObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicCopyObject", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.CopyObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BasicCopyObject indicates an expected call of BasicCopyObject.
func (mr *MockBosInterfaceMockRecorder) BasicCopyObject(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicCopyObject", reflect.TypeOf((*MockBosInterface)(nil).BasicCopyObject), arg0, arg1, arg2, arg3)
}

// BasicFetchObject mocks base method.
func (m *MockBosInterface) BasicFetchObject(arg0, arg1, arg2 string) (*api.FetchObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicFetchObject", arg0, arg1, arg2)
	ret0, _ := ret[0].(*api.FetchObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BasicFetchObject indicates an expected call of BasicFetchObject.
func (mr *MockBosInterfaceMockRecorder) BasicFetchObject(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicFetchObject", reflect.TypeOf((*MockBosInterface)(nil).BasicFetchObject), arg0, arg1, arg2)
}

// BasicGeneratePresignedUrl mocks base method.
func (m *MockBosInterface) BasicGeneratePresignedUrl(arg0, arg1 string, arg2 int) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicGeneratePresignedUrl", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	return ret0
}

// BasicGeneratePresignedUrl indicates an expected call of BasicGeneratePresignedUrl.
func (mr *MockBosInterfaceMockRecorder) BasicGeneratePresignedUrl(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicGeneratePresignedUrl", reflect.TypeOf((*MockBosInterface)(nil).BasicGeneratePresignedUrl), arg0, arg1, arg2)
}

// BasicGetObject mocks base method.
func (m *MockBosInterface) BasicGetObject(arg0, arg1 string) (*api.GetObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicGetObject", arg0, arg1)
	ret0, _ := ret[0].(*api.GetObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BasicGetObject indicates an expected call of BasicGetObject.
func (mr *MockBosInterfaceMockRecorder) BasicGetObject(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicGetObject", reflect.TypeOf((*MockBosInterface)(nil).BasicGetObject), arg0, arg1)
}

// BasicGetObjectToFile mocks base method.
func (m *MockBosInterface) BasicGetObjectToFile(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicGetObjectToFile", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BasicGetObjectToFile indicates an expected call of BasicGetObjectToFile.
func (mr *MockBosInterfaceMockRecorder) BasicGetObjectToFile(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicGetObjectToFile", reflect.TypeOf((*MockBosInterface)(nil).BasicGetObjectToFile), arg0, arg1, arg2)
}

// BasicInitiateMultipartUpload mocks base method.
func (m *MockBosInterface) BasicInitiateMultipartUpload(arg0, arg1 string) (*api.InitiateMultipartUploadResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicInitiateMultipartUpload", arg0, arg1)
	ret0, _ := ret[0].(*api.InitiateMultipartUploadResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BasicInitiateMultipartUpload indicates an expected call of BasicInitiateMultipartUpload.
func (mr *MockBosInterfaceMockRecorder) BasicInitiateMultipartUpload(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicInitiateMultipartUpload", reflect.TypeOf((*MockBosInterface)(nil).BasicInitiateMultipartUpload), arg0, arg1)
}

// BasicListMultipartUploads mocks base method.
func (m *MockBosInterface) BasicListMultipartUploads(arg0 string) (*api.ListMultipartUploadsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicListMultipartUploads", arg0)
	ret0, _ := ret[0].(*api.ListMultipartUploadsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BasicListMultipartUploads indicates an expected call of BasicListMultipartUploads.
func (mr *MockBosInterfaceMockRecorder) BasicListMultipartUploads(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicListMultipartUploads", reflect.TypeOf((*MockBosInterface)(nil).BasicListMultipartUploads), arg0)
}

// BasicListParts mocks base method.
func (m *MockBosInterface) BasicListParts(arg0, arg1, arg2 string) (*api.ListPartsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicListParts", arg0, arg1, arg2)
	ret0, _ := ret[0].(*api.ListPartsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BasicListParts indicates an expected call of BasicListParts.
func (mr *MockBosInterfaceMockRecorder) BasicListParts(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicListParts", reflect.TypeOf((*MockBosInterface)(nil).BasicListParts), arg0, arg1, arg2)
}

// BasicPutObject mocks base method.
func (m *MockBosInterface) BasicPutObject(arg0, arg1 string, arg2 *bce.Body) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicPutObject", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BasicPutObject indicates an expected call of BasicPutObject.
func (mr *MockBosInterfaceMockRecorder) BasicPutObject(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicPutObject", reflect.TypeOf((*MockBosInterface)(nil).BasicPutObject), arg0, arg1, arg2)
}

// BasicUploadPart mocks base method.
func (m *MockBosInterface) BasicUploadPart(arg0, arg1, arg2 string, arg3 int, arg4 *bce.Body) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicUploadPart", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BasicUploadPart indicates an expected call of BasicUploadPart.
func (mr *MockBosInterfaceMockRecorder) BasicUploadPart(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicUploadPart", reflect.TypeOf((*MockBosInterface)(nil).BasicUploadPart), arg0, arg1, arg2, arg3, arg4)
}

// BasicUploadPartCopy mocks base method.
func (m *MockBosInterface) BasicUploadPartCopy(arg0, arg1, arg2, arg3, arg4 string, arg5 int) (*api.CopyObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BasicUploadPartCopy", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*api.CopyObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BasicUploadPartCopy indicates an expected call of BasicUploadPartCopy.
func (mr *MockBosInterfaceMockRecorder) BasicUploadPartCopy(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BasicUploadPartCopy", reflect.TypeOf((*MockBosInterface)(nil).BasicUploadPartCopy), arg0, arg1, arg2, arg3, arg4, arg5)
}

// CompleteMultipartUpload mocks base method.
func (m *MockBosInterface) CompleteMultipartUpload(arg0, arg1, arg2 string, arg3 *bce.Body, arg4 *api.CompleteMultipartUploadArgs) (*api.CompleteMultipartUploadResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompleteMultipartUpload", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*api.CompleteMultipartUploadResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompleteMultipartUpload indicates an expected call of CompleteMultipartUpload.
func (mr *MockBosInterfaceMockRecorder) CompleteMultipartUpload(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompleteMultipartUpload", reflect.TypeOf((*MockBosInterface)(nil).CompleteMultipartUpload), arg0, arg1, arg2, arg3, arg4)
}

// CompleteMultipartUploadFromStruct mocks base method.
func (m *MockBosInterface) CompleteMultipartUploadFromStruct(arg0, arg1, arg2 string, arg3 *api.CompleteMultipartUploadArgs) (*api.CompleteMultipartUploadResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompleteMultipartUploadFromStruct", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.CompleteMultipartUploadResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompleteMultipartUploadFromStruct indicates an expected call of CompleteMultipartUploadFromStruct.
func (mr *MockBosInterfaceMockRecorder) CompleteMultipartUploadFromStruct(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompleteMultipartUploadFromStruct", reflect.TypeOf((*MockBosInterface)(nil).CompleteMultipartUploadFromStruct), arg0, arg1, arg2, arg3)
}

// CopyObject mocks base method.
func (m *MockBosInterface) CopyObject(arg0, arg1, arg2, arg3 string, arg4 *api.CopyObjectArgs) (*api.CopyObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyObject", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*api.CopyObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CopyObject indicates an expected call of CopyObject.
func (mr *MockBosInterfaceMockRecorder) CopyObject(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyObject", reflect.TypeOf((*MockBosInterface)(nil).CopyObject), arg0, arg1, arg2, arg3, arg4)
}

// DeleteBucket mocks base method.
func (m *MockBosInterface) DeleteBucket(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucket", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucket indicates an expected call of DeleteBucket.
func (mr *MockBosInterfaceMockRecorder) DeleteBucket(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucket", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucket), arg0)
}

// DeleteBucketCopyrightProtection mocks base method.
func (m *MockBosInterface) DeleteBucketCopyrightProtection(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucketCopyrightProtection", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucketCopyrightProtection indicates an expected call of DeleteBucketCopyrightProtection.
func (mr *MockBosInterfaceMockRecorder) DeleteBucketCopyrightProtection(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucketCopyrightProtection", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucketCopyrightProtection), arg0)
}

// DeleteBucketCors mocks base method.
func (m *MockBosInterface) DeleteBucketCors(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucketCors", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucketCors indicates an expected call of DeleteBucketCors.
func (mr *MockBosInterfaceMockRecorder) DeleteBucketCors(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucketCors", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucketCors), arg0)
}

// DeleteBucketEncryption mocks base method.
func (m *MockBosInterface) DeleteBucketEncryption(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucketEncryption", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucketEncryption indicates an expected call of DeleteBucketEncryption.
func (mr *MockBosInterfaceMockRecorder) DeleteBucketEncryption(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucketEncryption", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucketEncryption), arg0)
}

// DeleteBucketLifecycle mocks base method.
func (m *MockBosInterface) DeleteBucketLifecycle(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucketLifecycle", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucketLifecycle indicates an expected call of DeleteBucketLifecycle.
func (mr *MockBosInterfaceMockRecorder) DeleteBucketLifecycle(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucketLifecycle", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucketLifecycle), arg0)
}

// DeleteBucketLogging mocks base method.
func (m *MockBosInterface) DeleteBucketLogging(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucketLogging", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucketLogging indicates an expected call of DeleteBucketLogging.
func (mr *MockBosInterfaceMockRecorder) DeleteBucketLogging(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucketLogging", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucketLogging), arg0)
}

// DeleteBucketMirror mocks base method.
func (m *MockBosInterface) DeleteBucketMirror(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucketMirror", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucketMirror indicates an expected call of DeleteBucketMirror.
func (mr *MockBosInterfaceMockRecorder) DeleteBucketMirror(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucketMirror", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucketMirror), arg0)
}

// DeleteBucketNotification mocks base method.
func (m *MockBosInterface) DeleteBucketNotification(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucketNotification", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucketNotification indicates an expected call of DeleteBucketNotification.
func (mr *MockBosInterfaceMockRecorder) DeleteBucketNotification(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucketNotification", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucketNotification), arg0)
}

// DeleteBucketReplication mocks base method.
func (m *MockBosInterface) DeleteBucketReplication(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucketReplication", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucketReplication indicates an expected call of DeleteBucketReplication.
func (mr *MockBosInterfaceMockRecorder) DeleteBucketReplication(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucketReplication", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucketReplication), arg0, arg1)
}

// DeleteBucketStaticWebsite mocks base method.
func (m *MockBosInterface) DeleteBucketStaticWebsite(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucketStaticWebsite", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucketStaticWebsite indicates an expected call of DeleteBucketStaticWebsite.
func (mr *MockBosInterfaceMockRecorder) DeleteBucketStaticWebsite(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucketStaticWebsite", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucketStaticWebsite), arg0)
}

// DeleteBucketTrash mocks base method.
func (m *MockBosInterface) DeleteBucketTrash(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBucketTrash", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBucketTrash indicates an expected call of DeleteBucketTrash.
func (mr *MockBosInterfaceMockRecorder) DeleteBucketTrash(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBucketTrash", reflect.TypeOf((*MockBosInterface)(nil).DeleteBucketTrash), arg0)
}

// DeleteMultipleObjects mocks base method.
func (m *MockBosInterface) DeleteMultipleObjects(arg0 string, arg1 *bce.Body) (*api.DeleteMultipleObjectsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMultipleObjects", arg0, arg1)
	ret0, _ := ret[0].(*api.DeleteMultipleObjectsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteMultipleObjects indicates an expected call of DeleteMultipleObjects.
func (mr *MockBosInterfaceMockRecorder) DeleteMultipleObjects(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMultipleObjects", reflect.TypeOf((*MockBosInterface)(nil).DeleteMultipleObjects), arg0, arg1)
}

// DeleteMultipleObjectsFromKeyList mocks base method.
func (m *MockBosInterface) DeleteMultipleObjectsFromKeyList(arg0 string, arg1 []string) (*api.DeleteMultipleObjectsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMultipleObjectsFromKeyList", arg0, arg1)
	ret0, _ := ret[0].(*api.DeleteMultipleObjectsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteMultipleObjectsFromKeyList indicates an expected call of DeleteMultipleObjectsFromKeyList.
func (mr *MockBosInterfaceMockRecorder) DeleteMultipleObjectsFromKeyList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMultipleObjectsFromKeyList", reflect.TypeOf((*MockBosInterface)(nil).DeleteMultipleObjectsFromKeyList), arg0, arg1)
}

// DeleteMultipleObjectsFromString mocks base method.
func (m *MockBosInterface) DeleteMultipleObjectsFromString(arg0, arg1 string) (*api.DeleteMultipleObjectsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMultipleObjectsFromString", arg0, arg1)
	ret0, _ := ret[0].(*api.DeleteMultipleObjectsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteMultipleObjectsFromString indicates an expected call of DeleteMultipleObjectsFromString.
func (mr *MockBosInterfaceMockRecorder) DeleteMultipleObjectsFromString(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMultipleObjectsFromString", reflect.TypeOf((*MockBosInterface)(nil).DeleteMultipleObjectsFromString), arg0, arg1)
}

// DeleteMultipleObjectsFromStruct mocks base method.
func (m *MockBosInterface) DeleteMultipleObjectsFromStruct(arg0 string, arg1 *api.DeleteMultipleObjectsArgs) (*api.DeleteMultipleObjectsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMultipleObjectsFromStruct", arg0, arg1)
	ret0, _ := ret[0].(*api.DeleteMultipleObjectsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteMultipleObjectsFromStruct indicates an expected call of DeleteMultipleObjectsFromStruct.
func (mr *MockBosInterfaceMockRecorder) DeleteMultipleObjectsFromStruct(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMultipleObjectsFromStruct", reflect.TypeOf((*MockBosInterface)(nil).DeleteMultipleObjectsFromStruct), arg0, arg1)
}

// DeleteObject mocks base method.
func (m *MockBosInterface) DeleteObject(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteObject", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteObject indicates an expected call of DeleteObject.
func (mr *MockBosInterfaceMockRecorder) DeleteObject(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteObject", reflect.TypeOf((*MockBosInterface)(nil).DeleteObject), arg0, arg1)
}

// DeleteObjectAcl mocks base method.
func (m *MockBosInterface) DeleteObjectAcl(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteObjectAcl", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteObjectAcl indicates an expected call of DeleteObjectAcl.
func (mr *MockBosInterfaceMockRecorder) DeleteObjectAcl(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteObjectAcl", reflect.TypeOf((*MockBosInterface)(nil).DeleteObjectAcl), arg0, arg1)
}

// DoesBucketExist mocks base method.
func (m *MockBosInterface) DoesBucketExist(arg0 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DoesBucketExist", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DoesBucketExist indicates an expected call of DoesBucketExist.
func (mr *MockBosInterfaceMockRecorder) DoesBucketExist(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoesBucketExist", reflect.TypeOf((*MockBosInterface)(nil).DoesBucketExist), arg0)
}

// DownloadSuperFile mocks base method.
func (m *MockBosInterface) DownloadSuperFile(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadSuperFile", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DownloadSuperFile indicates an expected call of DownloadSuperFile.
func (mr *MockBosInterfaceMockRecorder) DownloadSuperFile(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadSuperFile", reflect.TypeOf((*MockBosInterface)(nil).DownloadSuperFile), arg0, arg1, arg2)
}

// FetchObject mocks base method.
func (m *MockBosInterface) FetchObject(arg0, arg1, arg2 string, arg3 *api.FetchObjectArgs) (*api.FetchObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchObject", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.FetchObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchObject indicates an expected call of FetchObject.
func (mr *MockBosInterfaceMockRecorder) FetchObject(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchObject", reflect.TypeOf((*MockBosInterface)(nil).FetchObject), arg0, arg1, arg2, arg3)
}

// GeneratePresignedUrl mocks base method.
func (m *MockBosInterface) GeneratePresignedUrl(arg0, arg1 string, arg2 int, arg3 string, arg4, arg5 map[string]string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeneratePresignedUrl", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(string)
	return ret0
}

// GeneratePresignedUrl indicates an expected call of GeneratePresignedUrl.
func (mr *MockBosInterfaceMockRecorder) GeneratePresignedUrl(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePresignedUrl", reflect.TypeOf((*MockBosInterface)(nil).GeneratePresignedUrl), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GeneratePresignedUrlPathStyle mocks base method.
func (m *MockBosInterface) GeneratePresignedUrlPathStyle(arg0, arg1 string, arg2 int, arg3 string, arg4, arg5 map[string]string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeneratePresignedUrlPathStyle", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(string)
	return ret0
}

// GeneratePresignedUrlPathStyle indicates an expected call of GeneratePresignedUrlPathStyle.
func (mr *MockBosInterfaceMockRecorder) GeneratePresignedUrlPathStyle(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePresignedUrlPathStyle", reflect.TypeOf((*MockBosInterface)(nil).GeneratePresignedUrlPathStyle), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetBucketAcl mocks base method.
func (m *MockBosInterface) GetBucketAcl(arg0 string) (*api.GetBucketAclResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketAcl", arg0)
	ret0, _ := ret[0].(*api.GetBucketAclResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketAcl indicates an expected call of GetBucketAcl.
func (mr *MockBosInterfaceMockRecorder) GetBucketAcl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketAcl", reflect.TypeOf((*MockBosInterface)(nil).GetBucketAcl), arg0)
}

// GetBucketCopyrightProtection mocks base method.
func (m *MockBosInterface) GetBucketCopyrightProtection(arg0 string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketCopyrightProtection", arg0)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketCopyrightProtection indicates an expected call of GetBucketCopyrightProtection.
func (mr *MockBosInterfaceMockRecorder) GetBucketCopyrightProtection(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketCopyrightProtection", reflect.TypeOf((*MockBosInterface)(nil).GetBucketCopyrightProtection), arg0)
}

// GetBucketCors mocks base method.
func (m *MockBosInterface) GetBucketCors(arg0 string) (*api.GetBucketCorsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketCors", arg0)
	ret0, _ := ret[0].(*api.GetBucketCorsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketCors indicates an expected call of GetBucketCors.
func (mr *MockBosInterfaceMockRecorder) GetBucketCors(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketCors", reflect.TypeOf((*MockBosInterface)(nil).GetBucketCors), arg0)
}

// GetBucketEncryption mocks base method.
func (m *MockBosInterface) GetBucketEncryption(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketEncryption", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketEncryption indicates an expected call of GetBucketEncryption.
func (mr *MockBosInterfaceMockRecorder) GetBucketEncryption(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketEncryption", reflect.TypeOf((*MockBosInterface)(nil).GetBucketEncryption), arg0)
}

// GetBucketLifecycle mocks base method.
func (m *MockBosInterface) GetBucketLifecycle(arg0 string) (*api.GetBucketLifecycleResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketLifecycle", arg0)
	ret0, _ := ret[0].(*api.GetBucketLifecycleResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketLifecycle indicates an expected call of GetBucketLifecycle.
func (mr *MockBosInterfaceMockRecorder) GetBucketLifecycle(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketLifecycle", reflect.TypeOf((*MockBosInterface)(nil).GetBucketLifecycle), arg0)
}

// GetBucketLocation mocks base method.
func (m *MockBosInterface) GetBucketLocation(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketLocation", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketLocation indicates an expected call of GetBucketLocation.
func (mr *MockBosInterfaceMockRecorder) GetBucketLocation(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketLocation", reflect.TypeOf((*MockBosInterface)(nil).GetBucketLocation), arg0)
}

// GetBucketLogging mocks base method.
func (m *MockBosInterface) GetBucketLogging(arg0 string) (*api.GetBucketLoggingResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketLogging", arg0)
	ret0, _ := ret[0].(*api.GetBucketLoggingResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketLogging indicates an expected call of GetBucketLogging.
func (mr *MockBosInterfaceMockRecorder) GetBucketLogging(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketLogging", reflect.TypeOf((*MockBosInterface)(nil).GetBucketLogging), arg0)
}

// GetBucketMirror mocks base method.
func (m *MockBosInterface) GetBucketMirror(arg0 string) (*api.PutBucketMirrorArgs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketMirror", arg0)
	ret0, _ := ret[0].(*api.PutBucketMirrorArgs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketMirror indicates an expected call of GetBucketMirror.
func (mr *MockBosInterfaceMockRecorder) GetBucketMirror(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketMirror", reflect.TypeOf((*MockBosInterface)(nil).GetBucketMirror), arg0)
}

// GetBucketNotification mocks base method.
func (m *MockBosInterface) GetBucketNotification(arg0 string) (*api.PutBucketNotificationReq, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketNotification", arg0)
	ret0, _ := ret[0].(*api.PutBucketNotificationReq)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketNotification indicates an expected call of GetBucketNotification.
func (mr *MockBosInterfaceMockRecorder) GetBucketNotification(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketNotification", reflect.TypeOf((*MockBosInterface)(nil).GetBucketNotification), arg0)
}

// GetBucketReplication mocks base method.
func (m *MockBosInterface) GetBucketReplication(arg0, arg1 string) (*api.GetBucketReplicationResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketReplication", arg0, arg1)
	ret0, _ := ret[0].(*api.GetBucketReplicationResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketReplication indicates an expected call of GetBucketReplication.
func (mr *MockBosInterfaceMockRecorder) GetBucketReplication(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketReplication", reflect.TypeOf((*MockBosInterface)(nil).GetBucketReplication), arg0, arg1)
}

// GetBucketReplicationProgress mocks base method.
func (m *MockBosInterface) GetBucketReplicationProgress(arg0, arg1 string) (*api.GetBucketReplicationProgressResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketReplicationProgress", arg0, arg1)
	ret0, _ := ret[0].(*api.GetBucketReplicationProgressResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketReplicationProgress indicates an expected call of GetBucketReplicationProgress.
func (mr *MockBosInterfaceMockRecorder) GetBucketReplicationProgress(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketReplicationProgress", reflect.TypeOf((*MockBosInterface)(nil).GetBucketReplicationProgress), arg0, arg1)
}

// GetBucketStaticWebsite mocks base method.
func (m *MockBosInterface) GetBucketStaticWebsite(arg0 string) (*api.GetBucketStaticWebsiteResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketStaticWebsite", arg0)
	ret0, _ := ret[0].(*api.GetBucketStaticWebsiteResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketStaticWebsite indicates an expected call of GetBucketStaticWebsite.
func (mr *MockBosInterfaceMockRecorder) GetBucketStaticWebsite(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketStaticWebsite", reflect.TypeOf((*MockBosInterface)(nil).GetBucketStaticWebsite), arg0)
}

// GetBucketStorageclass mocks base method.
func (m *MockBosInterface) GetBucketStorageclass(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketStorageclass", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketStorageclass indicates an expected call of GetBucketStorageclass.
func (mr *MockBosInterfaceMockRecorder) GetBucketStorageclass(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketStorageclass", reflect.TypeOf((*MockBosInterface)(nil).GetBucketStorageclass), arg0)
}

// GetBucketTrash mocks base method.
func (m *MockBosInterface) GetBucketTrash(arg0 string) (*api.GetBucketTrashResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucketTrash", arg0)
	ret0, _ := ret[0].(*api.GetBucketTrashResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBucketTrash indicates an expected call of GetBucketTrash.
func (mr *MockBosInterfaceMockRecorder) GetBucketTrash(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucketTrash", reflect.TypeOf((*MockBosInterface)(nil).GetBucketTrash), arg0)
}

// GetObject mocks base method.
func (m *MockBosInterface) GetObject(arg0, arg1 string, arg2 map[string]string, arg3 ...int64) (*api.GetObjectResult, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetObject", varargs...)
	ret0, _ := ret[0].(*api.GetObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObject indicates an expected call of GetObject.
func (mr *MockBosInterfaceMockRecorder) GetObject(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObject", reflect.TypeOf((*MockBosInterface)(nil).GetObject), varargs...)
}

// GetObjectAcl mocks base method.
func (m *MockBosInterface) GetObjectAcl(arg0, arg1 string) (*api.GetObjectAclResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObjectAcl", arg0, arg1)
	ret0, _ := ret[0].(*api.GetObjectAclResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObjectAcl indicates an expected call of GetObjectAcl.
func (mr *MockBosInterfaceMockRecorder) GetObjectAcl(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObjectAcl", reflect.TypeOf((*MockBosInterface)(nil).GetObjectAcl), arg0, arg1)
}

// GetObjectMeta mocks base method.
func (m *MockBosInterface) GetObjectMeta(arg0, arg1 string) (*api.GetObjectMetaResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObjectMeta", arg0, arg1)
	ret0, _ := ret[0].(*api.GetObjectMetaResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObjectMeta indicates an expected call of GetObjectMeta.
func (mr *MockBosInterfaceMockRecorder) GetObjectMeta(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObjectMeta", reflect.TypeOf((*MockBosInterface)(nil).GetObjectMeta), arg0, arg1)
}

// GetSymlink mocks base method.
func (m *MockBosInterface) GetSymlink(arg0, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSymlink", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSymlink indicates an expected call of GetSymlink.
func (mr *MockBosInterfaceMockRecorder) GetSymlink(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSymlink", reflect.TypeOf((*MockBosInterface)(nil).GetSymlink), arg0, arg1)
}

// HeadBucket mocks base method.
func (m *MockBosInterface) HeadBucket(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HeadBucket", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// HeadBucket indicates an expected call of HeadBucket.
func (mr *MockBosInterfaceMockRecorder) HeadBucket(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HeadBucket", reflect.TypeOf((*MockBosInterface)(nil).HeadBucket), arg0)
}

// InitiateMultipartUpload mocks base method.
func (m *MockBosInterface) InitiateMultipartUpload(arg0, arg1, arg2 string, arg3 *api.InitiateMultipartUploadArgs) (*api.InitiateMultipartUploadResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateMultipartUpload", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.InitiateMultipartUploadResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateMultipartUpload indicates an expected call of InitiateMultipartUpload.
func (mr *MockBosInterfaceMockRecorder) InitiateMultipartUpload(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateMultipartUpload", reflect.TypeOf((*MockBosInterface)(nil).InitiateMultipartUpload), arg0, arg1, arg2, arg3)
}

// IsNsBucket mocks base method.
func (m *MockBosInterface) IsNsBucket(arg0 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsNsBucket", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsNsBucket indicates an expected call of IsNsBucket.
func (mr *MockBosInterfaceMockRecorder) IsNsBucket(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsNsBucket", reflect.TypeOf((*MockBosInterface)(nil).IsNsBucket), arg0)
}

// ListBucketReplication mocks base method.
func (m *MockBosInterface) ListBucketReplication(arg0 string) (*api.ListBucketReplicationResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBucketReplication", arg0)
	ret0, _ := ret[0].(*api.ListBucketReplicationResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBucketReplication indicates an expected call of ListBucketReplication.
func (mr *MockBosInterfaceMockRecorder) ListBucketReplication(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBucketReplication", reflect.TypeOf((*MockBosInterface)(nil).ListBucketReplication), arg0)
}

// ListBuckets mocks base method.
func (m *MockBosInterface) ListBuckets() (*api.ListBucketsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBuckets")
	ret0, _ := ret[0].(*api.ListBucketsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBuckets indicates an expected call of ListBuckets.
func (mr *MockBosInterfaceMockRecorder) ListBuckets() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBuckets", reflect.TypeOf((*MockBosInterface)(nil).ListBuckets))
}

// ListMultipartUploads mocks base method.
func (m *MockBosInterface) ListMultipartUploads(arg0 string, arg1 *api.ListMultipartUploadsArgs) (*api.ListMultipartUploadsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMultipartUploads", arg0, arg1)
	ret0, _ := ret[0].(*api.ListMultipartUploadsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMultipartUploads indicates an expected call of ListMultipartUploads.
func (mr *MockBosInterfaceMockRecorder) ListMultipartUploads(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMultipartUploads", reflect.TypeOf((*MockBosInterface)(nil).ListMultipartUploads), arg0, arg1)
}

// ListObjects mocks base method.
func (m *MockBosInterface) ListObjects(arg0 string, arg1 *api.ListObjectsArgs) (*api.ListObjectsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListObjects", arg0, arg1)
	ret0, _ := ret[0].(*api.ListObjectsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListObjects indicates an expected call of ListObjects.
func (mr *MockBosInterfaceMockRecorder) ListObjects(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjects", reflect.TypeOf((*MockBosInterface)(nil).ListObjects), arg0, arg1)
}

// ListParts mocks base method.
func (m *MockBosInterface) ListParts(arg0, arg1, arg2 string, arg3 *api.ListPartsArgs) (*api.ListPartsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListParts", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.ListPartsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListParts indicates an expected call of ListParts.
func (mr *MockBosInterfaceMockRecorder) ListParts(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListParts", reflect.TypeOf((*MockBosInterface)(nil).ListParts), arg0, arg1, arg2, arg3)
}

// ParallelCopy mocks base method.
func (m *MockBosInterface) ParallelCopy(arg0, arg1, arg2, arg3 string, arg4 *api.MultiCopyObjectArgs, arg5 *bos.Client) (*api.CompleteMultipartUploadResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParallelCopy", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*api.CompleteMultipartUploadResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParallelCopy indicates an expected call of ParallelCopy.
func (mr *MockBosInterfaceMockRecorder) ParallelCopy(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParallelCopy", reflect.TypeOf((*MockBosInterface)(nil).ParallelCopy), arg0, arg1, arg2, arg3, arg4, arg5)
}

// ParallelUpload mocks base method.
func (m *MockBosInterface) ParallelUpload(arg0, arg1, arg2, arg3 string, arg4 *api.InitiateMultipartUploadArgs) (*api.CompleteMultipartUploadResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParallelUpload", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*api.CompleteMultipartUploadResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParallelUpload indicates an expected call of ParallelUpload.
func (mr *MockBosInterfaceMockRecorder) ParallelUpload(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParallelUpload", reflect.TypeOf((*MockBosInterface)(nil).ParallelUpload), arg0, arg1, arg2, arg3, arg4)
}

// PutBucket mocks base method.
func (m *MockBosInterface) PutBucket(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucket", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutBucket indicates an expected call of PutBucket.
func (mr *MockBosInterfaceMockRecorder) PutBucket(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucket", reflect.TypeOf((*MockBosInterface)(nil).PutBucket), arg0)
}

// PutBucketAcl mocks base method.
func (m *MockBosInterface) PutBucketAcl(arg0 string, arg1 *bce.Body) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketAcl", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketAcl indicates an expected call of PutBucketAcl.
func (mr *MockBosInterfaceMockRecorder) PutBucketAcl(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketAcl", reflect.TypeOf((*MockBosInterface)(nil).PutBucketAcl), arg0, arg1)
}

// PutBucketAclFromCanned mocks base method.
func (m *MockBosInterface) PutBucketAclFromCanned(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketAclFromCanned", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketAclFromCanned indicates an expected call of PutBucketAclFromCanned.
func (mr *MockBosInterfaceMockRecorder) PutBucketAclFromCanned(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketAclFromCanned", reflect.TypeOf((*MockBosInterface)(nil).PutBucketAclFromCanned), arg0, arg1)
}

// PutBucketAclFromFile mocks base method.
func (m *MockBosInterface) PutBucketAclFromFile(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketAclFromFile", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketAclFromFile indicates an expected call of PutBucketAclFromFile.
func (mr *MockBosInterfaceMockRecorder) PutBucketAclFromFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketAclFromFile", reflect.TypeOf((*MockBosInterface)(nil).PutBucketAclFromFile), arg0, arg1)
}

// PutBucketAclFromString mocks base method.
func (m *MockBosInterface) PutBucketAclFromString(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketAclFromString", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketAclFromString indicates an expected call of PutBucketAclFromString.
func (mr *MockBosInterfaceMockRecorder) PutBucketAclFromString(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketAclFromString", reflect.TypeOf((*MockBosInterface)(nil).PutBucketAclFromString), arg0, arg1)
}

// PutBucketAclFromStruct mocks base method.
func (m *MockBosInterface) PutBucketAclFromStruct(arg0 string, arg1 *api.PutBucketAclArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketAclFromStruct", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketAclFromStruct indicates an expected call of PutBucketAclFromStruct.
func (mr *MockBosInterfaceMockRecorder) PutBucketAclFromStruct(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketAclFromStruct", reflect.TypeOf((*MockBosInterface)(nil).PutBucketAclFromStruct), arg0, arg1)
}

// PutBucketCopyrightProtection mocks base method.
func (m *MockBosInterface) PutBucketCopyrightProtection(arg0 string, arg1 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutBucketCopyrightProtection", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketCopyrightProtection indicates an expected call of PutBucketCopyrightProtection.
func (mr *MockBosInterfaceMockRecorder) PutBucketCopyrightProtection(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketCopyrightProtection", reflect.TypeOf((*MockBosInterface)(nil).PutBucketCopyrightProtection), varargs...)
}

// PutBucketCors mocks base method.
func (m *MockBosInterface) PutBucketCors(arg0 string, arg1 *bce.Body) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketCors", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketCors indicates an expected call of PutBucketCors.
func (mr *MockBosInterfaceMockRecorder) PutBucketCors(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketCors", reflect.TypeOf((*MockBosInterface)(nil).PutBucketCors), arg0, arg1)
}

// PutBucketCorsFromFile mocks base method.
func (m *MockBosInterface) PutBucketCorsFromFile(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketCorsFromFile", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketCorsFromFile indicates an expected call of PutBucketCorsFromFile.
func (mr *MockBosInterfaceMockRecorder) PutBucketCorsFromFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketCorsFromFile", reflect.TypeOf((*MockBosInterface)(nil).PutBucketCorsFromFile), arg0, arg1)
}

// PutBucketCorsFromString mocks base method.
func (m *MockBosInterface) PutBucketCorsFromString(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketCorsFromString", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketCorsFromString indicates an expected call of PutBucketCorsFromString.
func (mr *MockBosInterfaceMockRecorder) PutBucketCorsFromString(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketCorsFromString", reflect.TypeOf((*MockBosInterface)(nil).PutBucketCorsFromString), arg0, arg1)
}

// PutBucketCorsFromStruct mocks base method.
func (m *MockBosInterface) PutBucketCorsFromStruct(arg0 string, arg1 *api.PutBucketCorsArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketCorsFromStruct", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketCorsFromStruct indicates an expected call of PutBucketCorsFromStruct.
func (mr *MockBosInterfaceMockRecorder) PutBucketCorsFromStruct(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketCorsFromStruct", reflect.TypeOf((*MockBosInterface)(nil).PutBucketCorsFromStruct), arg0, arg1)
}

// PutBucketEncryption mocks base method.
func (m *MockBosInterface) PutBucketEncryption(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketEncryption", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketEncryption indicates an expected call of PutBucketEncryption.
func (mr *MockBosInterfaceMockRecorder) PutBucketEncryption(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketEncryption", reflect.TypeOf((*MockBosInterface)(nil).PutBucketEncryption), arg0, arg1)
}

// PutBucketLifecycle mocks base method.
func (m *MockBosInterface) PutBucketLifecycle(arg0 string, arg1 *bce.Body) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketLifecycle", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketLifecycle indicates an expected call of PutBucketLifecycle.
func (mr *MockBosInterfaceMockRecorder) PutBucketLifecycle(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketLifecycle", reflect.TypeOf((*MockBosInterface)(nil).PutBucketLifecycle), arg0, arg1)
}

// PutBucketLifecycleFromString mocks base method.
func (m *MockBosInterface) PutBucketLifecycleFromString(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketLifecycleFromString", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketLifecycleFromString indicates an expected call of PutBucketLifecycleFromString.
func (mr *MockBosInterfaceMockRecorder) PutBucketLifecycleFromString(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketLifecycleFromString", reflect.TypeOf((*MockBosInterface)(nil).PutBucketLifecycleFromString), arg0, arg1)
}

// PutBucketLogging mocks base method.
func (m *MockBosInterface) PutBucketLogging(arg0 string, arg1 *bce.Body) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketLogging", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketLogging indicates an expected call of PutBucketLogging.
func (mr *MockBosInterfaceMockRecorder) PutBucketLogging(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketLogging", reflect.TypeOf((*MockBosInterface)(nil).PutBucketLogging), arg0, arg1)
}

// PutBucketLoggingFromString mocks base method.
func (m *MockBosInterface) PutBucketLoggingFromString(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketLoggingFromString", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketLoggingFromString indicates an expected call of PutBucketLoggingFromString.
func (mr *MockBosInterfaceMockRecorder) PutBucketLoggingFromString(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketLoggingFromString", reflect.TypeOf((*MockBosInterface)(nil).PutBucketLoggingFromString), arg0, arg1)
}

// PutBucketLoggingFromStruct mocks base method.
func (m *MockBosInterface) PutBucketLoggingFromStruct(arg0 string, arg1 *api.PutBucketLoggingArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketLoggingFromStruct", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketLoggingFromStruct indicates an expected call of PutBucketLoggingFromStruct.
func (mr *MockBosInterfaceMockRecorder) PutBucketLoggingFromStruct(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketLoggingFromStruct", reflect.TypeOf((*MockBosInterface)(nil).PutBucketLoggingFromStruct), arg0, arg1)
}

// PutBucketMirror mocks base method.
func (m *MockBosInterface) PutBucketMirror(arg0 string, arg1 *api.PutBucketMirrorArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketMirror", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketMirror indicates an expected call of PutBucketMirror.
func (mr *MockBosInterfaceMockRecorder) PutBucketMirror(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketMirror", reflect.TypeOf((*MockBosInterface)(nil).PutBucketMirror), arg0, arg1)
}

// PutBucketNotification mocks base method.
func (m *MockBosInterface) PutBucketNotification(arg0 string, arg1 api.PutBucketNotificationReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketNotification", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketNotification indicates an expected call of PutBucketNotification.
func (mr *MockBosInterfaceMockRecorder) PutBucketNotification(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketNotification", reflect.TypeOf((*MockBosInterface)(nil).PutBucketNotification), arg0, arg1)
}

// PutBucketReplication mocks base method.
func (m *MockBosInterface) PutBucketReplication(arg0 string, arg1 *bce.Body, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketReplication", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketReplication indicates an expected call of PutBucketReplication.
func (mr *MockBosInterfaceMockRecorder) PutBucketReplication(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketReplication", reflect.TypeOf((*MockBosInterface)(nil).PutBucketReplication), arg0, arg1, arg2)
}

// PutBucketReplicationFromFile mocks base method.
func (m *MockBosInterface) PutBucketReplicationFromFile(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketReplicationFromFile", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketReplicationFromFile indicates an expected call of PutBucketReplicationFromFile.
func (mr *MockBosInterfaceMockRecorder) PutBucketReplicationFromFile(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketReplicationFromFile", reflect.TypeOf((*MockBosInterface)(nil).PutBucketReplicationFromFile), arg0, arg1, arg2)
}

// PutBucketReplicationFromString mocks base method.
func (m *MockBosInterface) PutBucketReplicationFromString(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketReplicationFromString", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketReplicationFromString indicates an expected call of PutBucketReplicationFromString.
func (mr *MockBosInterfaceMockRecorder) PutBucketReplicationFromString(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketReplicationFromString", reflect.TypeOf((*MockBosInterface)(nil).PutBucketReplicationFromString), arg0, arg1, arg2)
}

// PutBucketReplicationFromStruct mocks base method.
func (m *MockBosInterface) PutBucketReplicationFromStruct(arg0 string, arg1 *api.PutBucketReplicationArgs, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketReplicationFromStruct", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketReplicationFromStruct indicates an expected call of PutBucketReplicationFromStruct.
func (mr *MockBosInterfaceMockRecorder) PutBucketReplicationFromStruct(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketReplicationFromStruct", reflect.TypeOf((*MockBosInterface)(nil).PutBucketReplicationFromStruct), arg0, arg1, arg2)
}

// PutBucketStaticWebsite mocks base method.
func (m *MockBosInterface) PutBucketStaticWebsite(arg0 string, arg1 *bce.Body) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketStaticWebsite", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketStaticWebsite indicates an expected call of PutBucketStaticWebsite.
func (mr *MockBosInterfaceMockRecorder) PutBucketStaticWebsite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketStaticWebsite", reflect.TypeOf((*MockBosInterface)(nil).PutBucketStaticWebsite), arg0, arg1)
}

// PutBucketStaticWebsiteFromString mocks base method.
func (m *MockBosInterface) PutBucketStaticWebsiteFromString(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketStaticWebsiteFromString", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketStaticWebsiteFromString indicates an expected call of PutBucketStaticWebsiteFromString.
func (mr *MockBosInterfaceMockRecorder) PutBucketStaticWebsiteFromString(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketStaticWebsiteFromString", reflect.TypeOf((*MockBosInterface)(nil).PutBucketStaticWebsiteFromString), arg0, arg1)
}

// PutBucketStaticWebsiteFromStruct mocks base method.
func (m *MockBosInterface) PutBucketStaticWebsiteFromStruct(arg0 string, arg1 *api.PutBucketStaticWebsiteArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketStaticWebsiteFromStruct", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketStaticWebsiteFromStruct indicates an expected call of PutBucketStaticWebsiteFromStruct.
func (mr *MockBosInterfaceMockRecorder) PutBucketStaticWebsiteFromStruct(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketStaticWebsiteFromStruct", reflect.TypeOf((*MockBosInterface)(nil).PutBucketStaticWebsiteFromStruct), arg0, arg1)
}

// PutBucketStorageclass mocks base method.
func (m *MockBosInterface) PutBucketStorageclass(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketStorageclass", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketStorageclass indicates an expected call of PutBucketStorageclass.
func (mr *MockBosInterfaceMockRecorder) PutBucketStorageclass(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketStorageclass", reflect.TypeOf((*MockBosInterface)(nil).PutBucketStorageclass), arg0, arg1)
}

// PutBucketTrash mocks base method.
func (m *MockBosInterface) PutBucketTrash(arg0 string, arg1 api.PutBucketTrashReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutBucketTrash", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutBucketTrash indicates an expected call of PutBucketTrash.
func (mr *MockBosInterfaceMockRecorder) PutBucketTrash(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutBucketTrash", reflect.TypeOf((*MockBosInterface)(nil).PutBucketTrash), arg0, arg1)
}

// PutObject mocks base method.
func (m *MockBosInterface) PutObject(arg0, arg1 string, arg2 *bce.Body, arg3 *api.PutObjectArgs) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutObject", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutObject indicates an expected call of PutObject.
func (mr *MockBosInterfaceMockRecorder) PutObject(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObject", reflect.TypeOf((*MockBosInterface)(nil).PutObject), arg0, arg1, arg2, arg3)
}

// PutObjectAcl mocks base method.
func (m *MockBosInterface) PutObjectAcl(arg0, arg1 string, arg2 *bce.Body) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutObjectAcl", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutObjectAcl indicates an expected call of PutObjectAcl.
func (mr *MockBosInterfaceMockRecorder) PutObjectAcl(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectAcl", reflect.TypeOf((*MockBosInterface)(nil).PutObjectAcl), arg0, arg1, arg2)
}

// PutObjectAclFromCanned mocks base method.
func (m *MockBosInterface) PutObjectAclFromCanned(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutObjectAclFromCanned", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutObjectAclFromCanned indicates an expected call of PutObjectAclFromCanned.
func (mr *MockBosInterfaceMockRecorder) PutObjectAclFromCanned(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectAclFromCanned", reflect.TypeOf((*MockBosInterface)(nil).PutObjectAclFromCanned), arg0, arg1, arg2)
}

// PutObjectAclFromFile mocks base method.
func (m *MockBosInterface) PutObjectAclFromFile(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutObjectAclFromFile", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutObjectAclFromFile indicates an expected call of PutObjectAclFromFile.
func (mr *MockBosInterfaceMockRecorder) PutObjectAclFromFile(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectAclFromFile", reflect.TypeOf((*MockBosInterface)(nil).PutObjectAclFromFile), arg0, arg1, arg2)
}

// PutObjectAclFromString mocks base method.
func (m *MockBosInterface) PutObjectAclFromString(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutObjectAclFromString", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutObjectAclFromString indicates an expected call of PutObjectAclFromString.
func (mr *MockBosInterfaceMockRecorder) PutObjectAclFromString(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectAclFromString", reflect.TypeOf((*MockBosInterface)(nil).PutObjectAclFromString), arg0, arg1, arg2)
}

// PutObjectAclFromStruct mocks base method.
func (m *MockBosInterface) PutObjectAclFromStruct(arg0, arg1 string, arg2 *api.PutObjectAclArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutObjectAclFromStruct", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutObjectAclFromStruct indicates an expected call of PutObjectAclFromStruct.
func (mr *MockBosInterfaceMockRecorder) PutObjectAclFromStruct(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectAclFromStruct", reflect.TypeOf((*MockBosInterface)(nil).PutObjectAclFromStruct), arg0, arg1, arg2)
}

// PutObjectAclGrantFullControl mocks base method.
func (m *MockBosInterface) PutObjectAclGrantFullControl(arg0, arg1 string, arg2 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutObjectAclGrantFullControl", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutObjectAclGrantFullControl indicates an expected call of PutObjectAclGrantFullControl.
func (mr *MockBosInterfaceMockRecorder) PutObjectAclGrantFullControl(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectAclGrantFullControl", reflect.TypeOf((*MockBosInterface)(nil).PutObjectAclGrantFullControl), varargs...)
}

// PutObjectAclGrantRead mocks base method.
func (m *MockBosInterface) PutObjectAclGrantRead(arg0, arg1 string, arg2 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutObjectAclGrantRead", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutObjectAclGrantRead indicates an expected call of PutObjectAclGrantRead.
func (mr *MockBosInterfaceMockRecorder) PutObjectAclGrantRead(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectAclGrantRead", reflect.TypeOf((*MockBosInterface)(nil).PutObjectAclGrantRead), varargs...)
}

// PutObjectFromBytes mocks base method.
func (m *MockBosInterface) PutObjectFromBytes(arg0, arg1 string, arg2 []byte, arg3 *api.PutObjectArgs) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutObjectFromBytes", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutObjectFromBytes indicates an expected call of PutObjectFromBytes.
func (mr *MockBosInterfaceMockRecorder) PutObjectFromBytes(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectFromBytes", reflect.TypeOf((*MockBosInterface)(nil).PutObjectFromBytes), arg0, arg1, arg2, arg3)
}

// PutObjectFromFile mocks base method.
func (m *MockBosInterface) PutObjectFromFile(arg0, arg1, arg2 string, arg3 *api.PutObjectArgs) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutObjectFromFile", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutObjectFromFile indicates an expected call of PutObjectFromFile.
func (mr *MockBosInterfaceMockRecorder) PutObjectFromFile(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectFromFile", reflect.TypeOf((*MockBosInterface)(nil).PutObjectFromFile), arg0, arg1, arg2, arg3)
}

// PutObjectFromStream mocks base method.
func (m *MockBosInterface) PutObjectFromStream(arg0, arg1 string, arg2 io.Reader, arg3 *api.PutObjectArgs) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutObjectFromStream", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutObjectFromStream indicates an expected call of PutObjectFromStream.
func (mr *MockBosInterfaceMockRecorder) PutObjectFromStream(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectFromStream", reflect.TypeOf((*MockBosInterface)(nil).PutObjectFromStream), arg0, arg1, arg2, arg3)
}

// PutObjectFromString mocks base method.
func (m *MockBosInterface) PutObjectFromString(arg0, arg1, arg2 string, arg3 *api.PutObjectArgs) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutObjectFromString", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutObjectFromString indicates an expected call of PutObjectFromString.
func (mr *MockBosInterfaceMockRecorder) PutObjectFromString(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObjectFromString", reflect.TypeOf((*MockBosInterface)(nil).PutObjectFromString), arg0, arg1, arg2, arg3)
}

// PutSymlink mocks base method.
func (m *MockBosInterface) PutSymlink(arg0, arg1, arg2 string, arg3 *api.PutSymlinkArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutSymlink", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutSymlink indicates an expected call of PutSymlink.
func (mr *MockBosInterfaceMockRecorder) PutSymlink(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutSymlink", reflect.TypeOf((*MockBosInterface)(nil).PutSymlink), arg0, arg1, arg2, arg3)
}

// RestoreObject mocks base method.
func (m *MockBosInterface) RestoreObject(arg0, arg1 string, arg2 int, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestoreObject", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestoreObject indicates an expected call of RestoreObject.
func (mr *MockBosInterfaceMockRecorder) RestoreObject(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestoreObject", reflect.TypeOf((*MockBosInterface)(nil).RestoreObject), arg0, arg1, arg2, arg3)
}

// SelectObject mocks base method.
func (m *MockBosInterface) SelectObject(arg0, arg1 string, arg2 *api.SelectObjectArgs) (*api.SelectObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectObject", arg0, arg1, arg2)
	ret0, _ := ret[0].(*api.SelectObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectObject indicates an expected call of SelectObject.
func (mr *MockBosInterfaceMockRecorder) SelectObject(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectObject", reflect.TypeOf((*MockBosInterface)(nil).SelectObject), arg0, arg1, arg2)
}

// SimpleAppendObject mocks base method.
func (m *MockBosInterface) SimpleAppendObject(arg0, arg1 string, arg2 *bce.Body, arg3 int64) (*api.AppendObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleAppendObject", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.AppendObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleAppendObject indicates an expected call of SimpleAppendObject.
func (mr *MockBosInterfaceMockRecorder) SimpleAppendObject(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleAppendObject", reflect.TypeOf((*MockBosInterface)(nil).SimpleAppendObject), arg0, arg1, arg2, arg3)
}

// SimpleAppendObjectFromFile mocks base method.
func (m *MockBosInterface) SimpleAppendObjectFromFile(arg0, arg1, arg2 string, arg3 int64) (*api.AppendObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleAppendObjectFromFile", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.AppendObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleAppendObjectFromFile indicates an expected call of SimpleAppendObjectFromFile.
func (mr *MockBosInterfaceMockRecorder) SimpleAppendObjectFromFile(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleAppendObjectFromFile", reflect.TypeOf((*MockBosInterface)(nil).SimpleAppendObjectFromFile), arg0, arg1, arg2, arg3)
}

// SimpleAppendObjectFromString mocks base method.
func (m *MockBosInterface) SimpleAppendObjectFromString(arg0, arg1, arg2 string, arg3 int64) (*api.AppendObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleAppendObjectFromString", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.AppendObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleAppendObjectFromString indicates an expected call of SimpleAppendObjectFromString.
func (mr *MockBosInterfaceMockRecorder) SimpleAppendObjectFromString(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleAppendObjectFromString", reflect.TypeOf((*MockBosInterface)(nil).SimpleAppendObjectFromString), arg0, arg1, arg2, arg3)
}

// SimpleFetchObject mocks base method.
func (m *MockBosInterface) SimpleFetchObject(arg0, arg1, arg2, arg3, arg4 string) (*api.FetchObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleFetchObject", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*api.FetchObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleFetchObject indicates an expected call of SimpleFetchObject.
func (mr *MockBosInterfaceMockRecorder) SimpleFetchObject(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleFetchObject", reflect.TypeOf((*MockBosInterface)(nil).SimpleFetchObject), arg0, arg1, arg2, arg3, arg4)
}

// SimpleListObjects mocks base method.
func (m *MockBosInterface) SimpleListObjects(arg0, arg1 string, arg2 int, arg3, arg4 string) (*api.ListObjectsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleListObjects", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*api.ListObjectsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleListObjects indicates an expected call of SimpleListObjects.
func (mr *MockBosInterfaceMockRecorder) SimpleListObjects(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleListObjects", reflect.TypeOf((*MockBosInterface)(nil).SimpleListObjects), arg0, arg1, arg2, arg3, arg4)
}

// SimplePutBucketStaticWebsite mocks base method.
func (m *MockBosInterface) SimplePutBucketStaticWebsite(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimplePutBucketStaticWebsite", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SimplePutBucketStaticWebsite indicates an expected call of SimplePutBucketStaticWebsite.
func (mr *MockBosInterfaceMockRecorder) SimplePutBucketStaticWebsite(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimplePutBucketStaticWebsite", reflect.TypeOf((*MockBosInterface)(nil).SimplePutBucketStaticWebsite), arg0, arg1, arg2)
}

// UploadPart mocks base method.
func (m *MockBosInterface) UploadPart(arg0, arg1, arg2 string, arg3 int, arg4 *bce.Body, arg5 *api.UploadPartArgs) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadPart", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadPart indicates an expected call of UploadPart.
func (mr *MockBosInterfaceMockRecorder) UploadPart(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadPart", reflect.TypeOf((*MockBosInterface)(nil).UploadPart), arg0, arg1, arg2, arg3, arg4, arg5)
}

// UploadPartCopy mocks base method.
func (m *MockBosInterface) UploadPartCopy(arg0, arg1, arg2, arg3, arg4 string, arg5 int, arg6 *api.UploadPartCopyArgs) (*api.CopyObjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadPartCopy", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(*api.CopyObjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadPartCopy indicates an expected call of UploadPartCopy.
func (mr *MockBosInterfaceMockRecorder) UploadPartCopy(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadPartCopy", reflect.TypeOf((*MockBosInterface)(nil).UploadPartCopy), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// UploadPartFromBytes mocks base method.
func (m *MockBosInterface) UploadPartFromBytes(arg0, arg1, arg2 string, arg3 int, arg4 []byte, arg5 *api.UploadPartArgs) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadPartFromBytes", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadPartFromBytes indicates an expected call of UploadPartFromBytes.
func (mr *MockBosInterfaceMockRecorder) UploadPartFromBytes(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadPartFromBytes", reflect.TypeOf((*MockBosInterface)(nil).UploadPartFromBytes), arg0, arg1, arg2, arg3, arg4, arg5)
}

// UploadSuperFile mocks base method.
func (m *MockBosInterface) UploadSuperFile(arg0, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadSuperFile", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UploadSuperFile indicates an expected call of UploadSuperFile.
func (mr *MockBosInterfaceMockRecorder) UploadSuperFile(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadSuperFile", reflect.TypeOf((*MockBosInterface)(nil).UploadSuperFile), arg0, arg1, arg2, arg3)
}
