// Code generated by mockery v2.28.1. DO NOT EDIT.

package sts

import (
	mock "github.com/stretchr/testify/mock"
	iam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	sts "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"
)

// Client is an autogenerated mock type for the ClientInterface type
type Client struct {
	mock.Mock
}

// AssumeRole provides a mock function with given fields: accountID, userID
func (_m *Client) AssumeRole(accountID string, userID string) (*sts.Credential, error) {
	ret := _m.Called(accountID, userID)

	var r0 *sts.Credential
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*sts.Credential, error)); ok {
		return rf(accountID, userID)
	}
	if rf, ok := ret.Get(0).(func(string, string) *sts.Credential); ok {
		r0 = rf(accountID, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*sts.Credential)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountID, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCredential provides a mock function with given fields: accountID, userID
func (_m *Client) GetCredential(accountID string, userID string) (*sts.Credential, error) {
	ret := _m.Called(accountID, userID)

	var r0 *sts.Credential
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*sts.Credential, error)); ok {
		return rf(accountID, userID)
	}
	if rf, ok := ret.Get(0).(func(string, string) *sts.Credential); ok {
		r0 = rf(accountID, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*sts.Credential)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountID, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IAM provides a mock function with given fields:
func (_m *Client) IAM() *iam.BceClient {
	ret := _m.Called()

	var r0 *iam.BceClient
	if rf, ok := ret.Get(0).(func() *iam.BceClient); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iam.BceClient)
		}
	}

	return r0
}

type mockConstructorTestingTNewClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewClient(t mockConstructorTestingTNewClient) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
