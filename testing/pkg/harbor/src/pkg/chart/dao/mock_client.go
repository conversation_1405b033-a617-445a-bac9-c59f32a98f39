// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/dao (interfaces: Dao)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	dao "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/dao"
	models "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/models"
)

// MockDao is a mock of Dao interface.
type MockDao struct {
	ctrl     *gomock.Controller
	recorder *MockDaoMockRecorder
}

// MockDaoMockRecorder is the mock recorder for MockDao.
type MockDaoMockRecorder struct {
	mock *MockDao
}

// NewMockDao creates a new mock instance.
func NewMockDao(ctrl *gomock.Controller) *MockDao {
	mock := &MockDao{ctrl: ctrl}
	mock.recorder = &MockDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDao) EXPECT() *MockDaoMockRecorder {
	return m.recorder
}

// ChartRepoCount mocks base method.
func (m *MockDao) ChartRepoCount(arg0 context.Context, arg1 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChartRepoCount", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChartRepoCount indicates an expected call of ChartRepoCount.
func (mr *MockDaoMockRecorder) ChartRepoCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChartRepoCount", reflect.TypeOf((*MockDao)(nil).ChartRepoCount), arg0, arg1)
}

// ChartSize mocks base method.
func (m *MockDao) ChartSize(arg0 context.Context, arg1 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChartSize", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChartSize indicates an expected call of ChartSize.
func (mr *MockDaoMockRecorder) ChartSize(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChartSize", reflect.TypeOf((*MockDao)(nil).ChartSize), arg0, arg1)
}

// ChartVersionCount mocks base method.
func (m *MockDao) ChartVersionCount(arg0 context.Context, arg1, arg2 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChartVersionCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChartVersionCount indicates an expected call of ChartVersionCount.
func (mr *MockDaoMockRecorder) ChartVersionCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChartVersionCount", reflect.TypeOf((*MockDao)(nil).ChartVersionCount), arg0, arg1, arg2)
}

// CountByProjects mocks base method.
func (m *MockDao) CountByProjects(arg0 context.Context, arg1 []string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByProjects", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByProjects indicates an expected call of CountByProjects.
func (mr *MockDaoMockRecorder) CountByProjects(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByProjects", reflect.TypeOf((*MockDao)(nil).CountByProjects), arg0, arg1)
}

// Create mocks base method.
func (m *MockDao) Create(arg0 context.Context, arg1 *models.Chart) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockDaoMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockDao)(nil).Create), arg0, arg1)
}

// CreateOrUpdate mocks base method.
func (m *MockDao) CreateOrUpdate(arg0 context.Context, arg1 *models.Chart) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdate", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrUpdate indicates an expected call of CreateOrUpdate.
func (mr *MockDaoMockRecorder) CreateOrUpdate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdate", reflect.TypeOf((*MockDao)(nil).CreateOrUpdate), arg0, arg1)
}

// DeleteChart mocks base method.
func (m *MockDao) DeleteChart(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteChart", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteChart indicates an expected call of DeleteChart.
func (mr *MockDaoMockRecorder) DeleteChart(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChart", reflect.TypeOf((*MockDao)(nil).DeleteChart), arg0, arg1, arg2)
}

// DeleteChartVersion mocks base method.
func (m *MockDao) DeleteChartVersion(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteChartVersion", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteChartVersion indicates an expected call of DeleteChartVersion.
func (mr *MockDaoMockRecorder) DeleteChartVersion(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChartVersion", reflect.TypeOf((*MockDao)(nil).DeleteChartVersion), arg0, arg1, arg2, arg3)
}

// GetChartSummaryByProject mocks base method.
func (m *MockDao) GetChartSummaryByProject(arg0 context.Context, arg1 string) ([]*dao.ChartSummary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChartSummaryByProject", arg0, arg1)
	ret0, _ := ret[0].([]*dao.ChartSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChartSummaryByProject indicates an expected call of GetChartSummaryByProject.
func (mr *MockDaoMockRecorder) GetChartSummaryByProject(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChartSummaryByProject", reflect.TypeOf((*MockDao)(nil).GetChartSummaryByProject), arg0, arg1)
}

// GetChartVersion mocks base method.
func (m *MockDao) GetChartVersion(arg0 context.Context, arg1, arg2, arg3 string) (*models.Chart, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChartVersion", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*models.Chart)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChartVersion indicates an expected call of GetChartVersion.
func (mr *MockDaoMockRecorder) GetChartVersion(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChartVersion", reflect.TypeOf((*MockDao)(nil).GetChartVersion), arg0, arg1, arg2, arg3)
}

// GetChartVersions mocks base method.
func (m *MockDao) GetChartVersions(arg0 context.Context, arg1, arg2 string, arg3, arg4 int) ([]*models.Chart, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChartVersions", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*models.Chart)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChartVersions indicates an expected call of GetChartVersions.
func (mr *MockDaoMockRecorder) GetChartVersions(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChartVersions", reflect.TypeOf((*MockDao)(nil).GetChartVersions), arg0, arg1, arg2, arg3, arg4)
}

// GetChartVersionsByProjects mocks base method.
func (m *MockDao) GetChartVersionsByProjects(arg0 context.Context, arg1 []string, arg2, arg3 int) ([]*models.Chart, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChartVersionsByProjects", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*models.Chart)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChartVersionsByProjects indicates an expected call of GetChartVersionsByProjects.
func (mr *MockDaoMockRecorder) GetChartVersionsByProjects(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChartVersionsByProjects", reflect.TypeOf((*MockDao)(nil).GetChartVersionsByProjects), arg0, arg1, arg2, arg3)
}

// GetUnreadyChartVersion mocks base method.
func (m *MockDao) GetUnreadyChartVersion(arg0 context.Context, arg1 bool) (int64, []*models.Chart, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnreadyChartVersion", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]*models.Chart)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUnreadyChartVersion indicates an expected call of GetUnreadyChartVersion.
func (mr *MockDaoMockRecorder) GetUnreadyChartVersion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnreadyChartVersion", reflect.TypeOf((*MockDao)(nil).GetUnreadyChartVersion), arg0, arg1)
}

// PreDeleteChart mocks base method.
func (m *MockDao) PreDeleteChart(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PreDeleteChart", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PreDeleteChart indicates an expected call of PreDeleteChart.
func (mr *MockDaoMockRecorder) PreDeleteChart(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PreDeleteChart", reflect.TypeOf((*MockDao)(nil).PreDeleteChart), arg0, arg1, arg2)
}

// PreDeleteChartVersion mocks base method.
func (m *MockDao) PreDeleteChartVersion(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PreDeleteChartVersion", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// PreDeleteChartVersion indicates an expected call of PreDeleteChartVersion.
func (mr *MockDaoMockRecorder) PreDeleteChartVersion(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PreDeleteChartVersion", reflect.TypeOf((*MockDao)(nil).PreDeleteChartVersion), arg0, arg1, arg2, arg3)
}

// ResetChartVersion mocks base method.
func (m *MockDao) ResetChartVersion(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetChartVersion", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetChartVersion indicates an expected call of ResetChartVersion.
func (mr *MockDaoMockRecorder) ResetChartVersion(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetChartVersion", reflect.TypeOf((*MockDao)(nil).ResetChartVersion), arg0, arg1, arg2, arg3)
}

// UpdateLastModified mocks base method.
func (m *MockDao) UpdateLastModified(arg0 context.Context, arg1 int64, arg2 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLastModified", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLastModified indicates an expected call of UpdateLastModified.
func (mr *MockDaoMockRecorder) UpdateLastModified(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLastModified", reflect.TypeOf((*MockDao)(nil).UpdateLastModified), arg0, arg1, arg2)
}
