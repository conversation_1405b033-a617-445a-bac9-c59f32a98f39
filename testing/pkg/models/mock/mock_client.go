// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	models "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// Delete mocks base method.
func (m *MockInterface) Delete(arg0 []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockInterfaceMockRecorder) Delete(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockInterface)(nil).Delete), arg0)
}

// DeleteExpiredUserToken mocks base method.
func (m *MockInterface) DeleteExpiredUserToken() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteExpiredUserToken")
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteExpiredUserToken indicates an expected call of DeleteExpiredUserToken.
func (mr *MockInterfaceMockRecorder) DeleteExpiredUserToken() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteExpiredUserToken", reflect.TypeOf((*MockInterface)(nil).DeleteExpiredUserToken))
}

// DeleteInstance mocks base method.
func (m *MockInterface) DeleteInstance(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstance", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstance indicates an expected call of DeleteInstance.
func (mr *MockInterfaceMockRecorder) DeleteInstance(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstance", reflect.TypeOf((*MockInterface)(nil).DeleteInstance), arg0)
}

// DeleteInstanceWithTimestamp mocks base method.
func (m *MockInterface) DeleteInstanceWithTimestamp(arg0 string, arg1 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstanceWithTimestamp", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstanceWithTimestamp indicates an expected call of DeleteInstanceWithTimestamp.
func (mr *MockInterfaceMockRecorder) DeleteInstanceWithTimestamp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstanceWithTimestamp", reflect.TypeOf((*MockInterface)(nil).DeleteInstanceWithTimestamp), arg0, arg1)
}

// FindExistedInstance mocks base method.
func (m *MockInterface) FindExistedInstance() ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindExistedInstance")
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindExistedInstance indicates an expected call of FindExistedInstance.
func (mr *MockInterfaceMockRecorder) FindExistedInstance() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindExistedInstance", reflect.TypeOf((*MockInterface)(nil).FindExistedInstance))
}

// FindInstanceFromLastUpdated mocks base method.
func (m *MockInterface) FindInstanceFromLastUpdated(arg0 time.Time) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindInstanceFromLastUpdated", arg0)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindInstanceFromLastUpdated indicates an expected call of FindInstanceFromLastUpdated.
func (mr *MockInterfaceMockRecorder) FindInstanceFromLastUpdated(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindInstanceFromLastUpdated", reflect.TypeOf((*MockInterface)(nil).FindInstanceFromLastUpdated), arg0)
}

// FindUserTokenByUser mocks base method.
func (m *MockInterface) FindUserTokenByUser(arg0, arg1, arg2 string) ([]*models.UserToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindUserTokenByUser", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.UserToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindUserTokenByUser indicates an expected call of FindUserTokenByUser.
func (mr *MockInterfaceMockRecorder) FindUserTokenByUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindUserTokenByUser", reflect.TypeOf((*MockInterface)(nil).FindUserTokenByUser), arg0, arg1, arg2)
}

// GetImageBuild mocks base method.
func (m *MockInterface) GetImageBuild(arg0 int64) (*models.ImageBuild, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImageBuild", arg0)
	ret0, _ := ret[0].(*models.ImageBuild)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImageBuild indicates an expected call of GetImageBuild.
func (mr *MockInterfaceMockRecorder) GetImageBuild(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImageBuild", reflect.TypeOf((*MockInterface)(nil).GetImageBuild), arg0)
}

// GetImageBuildLog mocks base method.
func (m *MockInterface) GetImageBuildLog(arg0 int64) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImageBuildLog", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImageBuildLog indicates an expected call of GetImageBuildLog.
func (mr *MockInterfaceMockRecorder) GetImageBuildLog(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImageBuildLog", reflect.TypeOf((*MockInterface)(nil).GetImageBuildLog), arg0)
}

// GetInstance mocks base method.
func (m *MockInterface) GetInstance(arg0 string) (*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstance", arg0)
	ret0, _ := ret[0].(*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstance indicates an expected call of GetInstance.
func (mr *MockInterfaceMockRecorder) GetInstance(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstance", reflect.TypeOf((*MockInterface)(nil).GetInstance), arg0)
}

// GetPermanentTokenByUser mocks base method.
func (m *MockInterface) GetPermanentTokenByUser(arg0, arg1, arg2 string) (*models.UserToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPermanentTokenByUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.UserToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPermanentTokenByUser indicates an expected call of GetPermanentTokenByUser.
func (mr *MockInterfaceMockRecorder) GetPermanentTokenByUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPermanentTokenByUser", reflect.TypeOf((*MockInterface)(nil).GetPermanentTokenByUser), arg0, arg1, arg2)
}

// InsertImageBuild mocks base method.
func (m *MockInterface) InsertImageBuild(arg0 *models.ImageBuild) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertImageBuild", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertImageBuild indicates an expected call of InsertImageBuild.
func (mr *MockInterfaceMockRecorder) InsertImageBuild(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertImageBuild", reflect.TypeOf((*MockInterface)(nil).InsertImageBuild), arg0)
}

// InsertInstance mocks base method.
func (m *MockInterface) InsertInstance(arg0 *models.Instance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertInstance", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertInstance indicates an expected call of InsertInstance.
func (mr *MockInterfaceMockRecorder) InsertInstance(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertInstance", reflect.TypeOf((*MockInterface)(nil).InsertInstance), arg0)
}

// InsertUserToken mocks base method.
func (m *MockInterface) InsertUserToken(arg0 *models.UserToken) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertUserToken", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertUserToken indicates an expected call of InsertUserToken.
func (mr *MockInterfaceMockRecorder) InsertUserToken(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUserToken", reflect.TypeOf((*MockInterface)(nil).InsertUserToken), arg0)
}

// ListImageBuildPaged mocks base method.
func (m *MockInterface) ListImageBuildPaged(arg0 string, arg1 map[string]interface{}, arg2, arg3 int) ([]*models.ImageBuild, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListImageBuildPaged", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*models.ImageBuild)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListImageBuildPaged indicates an expected call of ListImageBuildPaged.
func (mr *MockInterfaceMockRecorder) ListImageBuildPaged(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListImageBuildPaged", reflect.TypeOf((*MockInterface)(nil).ListImageBuildPaged), arg0, arg1, arg2, arg3)
}

// SetImageBuildStatusAndLog mocks base method.
func (m *MockInterface) SetImageBuildStatusAndLog(arg0 int64, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetImageBuildStatusAndLog", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetImageBuildStatusAndLog indicates an expected call of SetImageBuildStatusAndLog.
func (mr *MockInterfaceMockRecorder) SetImageBuildStatusAndLog(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetImageBuildStatusAndLog", reflect.TypeOf((*MockInterface)(nil).SetImageBuildStatusAndLog), arg0, arg1, arg2)
}

// UpdateImageBuildStatusByID mocks base method.
func (m *MockInterface) UpdateImageBuildStatusByID(arg0 int64, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateImageBuildStatusByID", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateImageBuildStatusByID indicates an expected call of UpdateImageBuildStatusByID.
func (mr *MockInterfaceMockRecorder) UpdateImageBuildStatusByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateImageBuildStatusByID", reflect.TypeOf((*MockInterface)(nil).UpdateImageBuildStatusByID), arg0, arg1)
}

// UpdateInstance mocks base method.
func (m *MockInterface) UpdateInstance(arg0 *models.Instance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstance", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstance indicates an expected call of UpdateInstance.
func (mr *MockInterfaceMockRecorder) UpdateInstance(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstance", reflect.TypeOf((*MockInterface)(nil).UpdateInstance), arg0)
}

// UpdateUserPassword mocks base method.
func (m *MockInterface) UpdateUserPassword(arg0 *models.UserToken) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserPassword", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserPassword indicates an expected call of UpdateUserPassword.
func (mr *MockInterfaceMockRecorder) UpdateUserPassword(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserPassword", reflect.TypeOf((*MockInterface)(nil).UpdateUserPassword), arg0)
}

// UpsertInstance mocks base method.
func (m *MockInterface) UpsertInstance(arg0 *models.Instance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertInstance", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertInstance indicates an expected call of UpsertInstance.
func (mr *MockInterfaceMockRecorder) UpsertInstance(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertInstance", reflect.TypeOf((*MockInterface)(nil).UpsertInstance), arg0)
}
