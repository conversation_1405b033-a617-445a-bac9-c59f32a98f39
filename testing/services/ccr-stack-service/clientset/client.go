// Code generated by mockery v2.43.0. DO NOT EDIT.

package clientset

import (
	client "sigs.k8s.io/controller-runtime/pkg/client"

	config "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/config"

	harbor "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"

	iam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"

	mock "github.com/stretchr/testify/mock"

	utils "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

// ClientSet is an autogenerated mock type for the ClientSetInterface type
type ClientSet struct {
	mock.Mock
}

// Conf provides a mock function with given fields:
func (_m *ClientSet) Conf() config.ServiceConfig {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Conf")
	}

	var r0 config.ServiceConfig
	if rf, ok := ret.Get(0).(func() config.ServiceConfig); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(config.ServiceConfig)
	}

	return r0
}

// HarborClient provides a mock function with given fields: harborHost, harborUser, harborPassword
func (_m *ClientSet) HarborClient(harborHost string, harborUser string, harborPassword string) (*harbor.HarborClient, error) {
	ret := _m.Called(harborHost, harborUser, harborPassword)

	if len(ret) == 0 {
		panic("no return value specified for HarborClient")
	}

	var r0 *harbor.HarborClient
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string) (*harbor.HarborClient, error)); ok {
		return rf(harborHost, harborUser, harborPassword)
	}
	if rf, ok := ret.Get(0).(func(string, string, string) *harbor.HarborClient); ok {
		r0 = rf(harborHost, harborUser, harborPassword)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*harbor.HarborClient)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(harborHost, harborUser, harborPassword)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HarborClientSimulate provides a mock function with given fields: harborHost, sid, harborCsrf
func (_m *ClientSet) HarborClientSimulate(harborHost string, sid string, harborCsrf *utils.HarborCsrf) (*harbor.HarborClient, error) {
	ret := _m.Called(harborHost, sid, harborCsrf)

	if len(ret) == 0 {
		panic("no return value specified for HarborClientSimulate")
	}

	var r0 *harbor.HarborClient
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, *utils.HarborCsrf) (*harbor.HarborClient, error)); ok {
		return rf(harborHost, sid, harborCsrf)
	}
	if rf, ok := ret.Get(0).(func(string, string, *utils.HarborCsrf) *harbor.HarborClient); ok {
		r0 = rf(harborHost, sid, harborCsrf)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*harbor.HarborClient)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, *utils.HarborCsrf) error); ok {
		r1 = rf(harborHost, sid, harborCsrf)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// InternalIam provides a mock function with given fields:
func (_m *ClientSet) InternalIam() iam.ClientInterface {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for InternalIam")
	}

	var r0 iam.ClientInterface
	if rf, ok := ret.Get(0).(func() iam.ClientInterface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(iam.ClientInterface)
		}
	}

	return r0
}

// K8sClient provides a mock function with given fields:
func (_m *ClientSet) K8sClient() client.Client {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for K8sClient")
	}

	var r0 client.Client
	if rf, ok := ret.Get(0).(func() client.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(client.Client)
		}
	}

	return r0
}

// NewClientSet creates a new instance of ClientSet. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClientSet(t interface {
	mock.TestingT
	Cleanup(func())
}) *ClientSet {
	mock := &ClientSet{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
