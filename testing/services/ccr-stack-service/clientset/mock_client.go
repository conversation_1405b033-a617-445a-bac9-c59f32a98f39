// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset (interfaces: ClientSetInterface)

// Package clientset is a generated GoMock package.
package clientset

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	iam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	harbor "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	utils "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	config "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/config"
	client "sigs.k8s.io/controller-runtime/pkg/client"
)

// MockClientSetInterface is a mock of ClientSetInterface interface.
type MockClientSetInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClientSetInterfaceMockRecorder
}

// MockClientSetInterfaceMockRecorder is the mock recorder for MockClientSetInterface.
type MockClientSetInterfaceMockRecorder struct {
	mock *MockClientSetInterface
}

// NewMockClientSetInterface creates a new mock instance.
func NewMockClientSetInterface(ctrl *gomock.Controller) *MockClientSetInterface {
	mock := &MockClientSetInterface{ctrl: ctrl}
	mock.recorder = &MockClientSetInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientSetInterface) EXPECT() *MockClientSetInterfaceMockRecorder {
	return m.recorder
}

// Conf mocks base method.
func (m *MockClientSetInterface) Conf() config.ServiceConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Conf")
	ret0, _ := ret[0].(config.ServiceConfig)
	return ret0
}

// Conf indicates an expected call of Conf.
func (mr *MockClientSetInterfaceMockRecorder) Conf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Conf", reflect.TypeOf((*MockClientSetInterface)(nil).Conf))
}

// HarborClient mocks base method.
func (m *MockClientSetInterface) HarborClient(arg0, arg1, arg2 string) (*harbor.HarborClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HarborClient", arg0, arg1, arg2)
	ret0, _ := ret[0].(*harbor.HarborClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HarborClient indicates an expected call of HarborClient.
func (mr *MockClientSetInterfaceMockRecorder) HarborClient(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HarborClient", reflect.TypeOf((*MockClientSetInterface)(nil).HarborClient), arg0, arg1, arg2)
}

// HarborClientSimulate mocks base method.
func (m *MockClientSetInterface) HarborClientSimulate(arg0, arg1 string, arg2 *utils.HarborCsrf) (*harbor.HarborClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HarborClientSimulate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*harbor.HarborClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HarborClientSimulate indicates an expected call of HarborClientSimulate.
func (mr *MockClientSetInterfaceMockRecorder) HarborClientSimulate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HarborClientSimulate", reflect.TypeOf((*MockClientSetInterface)(nil).HarborClientSimulate), arg0, arg1, arg2)
}

// InternalIam mocks base method.
func (m *MockClientSetInterface) InternalIam() iam.ClientInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InternalIam")
	ret0, _ := ret[0].(iam.ClientInterface)
	return ret0
}

// InternalIam indicates an expected call of InternalIam.
func (mr *MockClientSetInterfaceMockRecorder) InternalIam() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternalIam", reflect.TypeOf((*MockClientSetInterface)(nil).InternalIam))
}

// K8sClient mocks base method.
func (m *MockClientSetInterface) K8sClient() client.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "K8sClient")
	ret0, _ := ret[0].(client.Client)
	return ret0
}

// K8sClient indicates an expected call of K8sClient.
func (mr *MockClientSetInterfaceMockRecorder) K8sClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "K8sClient", reflect.TypeOf((*MockClientSetInterface)(nil).K8sClient))
}
