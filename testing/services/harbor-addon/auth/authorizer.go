// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/auth (interfaces: Interface)

// Package auth is a generated GoMock package.
package auth

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	token "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// GetClaims mocks base method.
func (m *MockInterface) GetClaims(arg0, arg1 string, arg2 []*token.ResourceActions, arg3 []byte) (*token.Claims, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClaims", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*token.Claims)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClaims indicates an expected call of GetClaims.
func (mr *MockInterfaceMockRecorder) GetClaims(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClaims", reflect.TypeOf((*MockInterface)(nil).GetClaims), arg0, arg1, arg2, arg3)
}

// Validate mocks base method.
func (m *MockInterface) Validate(arg0, arg1 string, arg2 []*token.ResourceActions, arg3 []byte, arg4 bool) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Validate", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Validate indicates an expected call of Validate.
func (mr *MockInterfaceMockRecorder) Validate(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Validate", reflect.TypeOf((*MockInterface)(nil).Validate), arg0, arg1, arg2, arg3, arg4)
}
