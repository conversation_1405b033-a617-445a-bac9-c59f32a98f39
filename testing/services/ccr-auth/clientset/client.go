// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/clientset (interfaces: ClientSetInterface)

// Package clientset is a generated GoMock package.
package clientset

import (
	http "net/http"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	iam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	logictag "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
	harbor "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	listers "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	models "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	client "sigs.k8s.io/controller-runtime/pkg/client"
)

// MockClientSetInterface is a mock of ClientSetInterface interface.
type MockClientSetInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClientSetInterfaceMockRecorder
}

// MockClientSetInterfaceMockRecorder is the mock recorder for MockClientSetInterface.
type MockClientSetInterfaceMockRecorder struct {
	mock *MockClientSetInterface
}

// NewMockClientSetInterface creates a new mock instance.
func NewMockClientSetInterface(ctrl *gomock.Controller) *MockClientSetInterface {
	mock := &MockClientSetInterface{ctrl: ctrl}
	mock.recorder = &MockClientSetInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientSetInterface) EXPECT() *MockClientSetInterfaceMockRecorder {
	return m.recorder
}

// BuildFakeHttpRequest mocks base method.
func (m *MockClientSetInterface) BuildFakeHttpRequest(arg0, arg1, arg2, arg3, arg4 string) (*http.Request, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuildFakeHttpRequest", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*http.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildFakeHttpRequest indicates an expected call of BuildFakeHttpRequest.
func (mr *MockClientSetInterfaceMockRecorder) BuildFakeHttpRequest(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildFakeHttpRequest", reflect.TypeOf((*MockClientSetInterface)(nil).BuildFakeHttpRequest), arg0, arg1, arg2, arg3, arg4)
}

// HarborClient mocks base method.
func (m *MockClientSetInterface) HarborClient(arg0 string) (*harbor.HarborClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HarborClient", arg0)
	ret0, _ := ret[0].(*harbor.HarborClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HarborClient indicates an expected call of HarborClient.
func (mr *MockClientSetInterfaceMockRecorder) HarborClient(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HarborClient", reflect.TypeOf((*MockClientSetInterface)(nil).HarborClient), arg0)
}

// InternalIam mocks base method.
func (m *MockClientSetInterface) InternalIam() iam.ClientInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InternalIam")
	ret0, _ := ret[0].(iam.ClientInterface)
	return ret0
}

// InternalIam indicates an expected call of InternalIam.
func (mr *MockClientSetInterfaceMockRecorder) InternalIam() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternalIam", reflect.TypeOf((*MockClientSetInterface)(nil).InternalIam))
}

// K8sClient mocks base method.
func (m *MockClientSetInterface) K8sClient() client.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "K8sClient")
	ret0, _ := ret[0].(client.Client)
	return ret0
}

// K8sClient indicates an expected call of K8sClient.
func (mr *MockClientSetInterfaceMockRecorder) K8sClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "K8sClient", reflect.TypeOf((*MockClientSetInterface)(nil).K8sClient))
}

// Lister mocks base method.
func (m *MockClientSetInterface) Lister() listers.ListerInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lister")
	ret0, _ := ret[0].(listers.ListerInterface)
	return ret0
}

// Lister indicates an expected call of Lister.
func (mr *MockClientSetInterfaceMockRecorder) Lister() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lister", reflect.TypeOf((*MockClientSetInterface)(nil).Lister))
}

// LogicTagClientForAccount mocks base method.
func (m *MockClientSetInterface) LogicTagClientForAccount(arg0, arg1 string) (logictag.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LogicTagClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(logictag.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LogicTagClientForAccount indicates an expected call of LogicTagClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) LogicTagClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LogicTagClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).LogicTagClientForAccount), arg0, arg1)
}

// SqlClient mocks base method.
func (m *MockClientSetInterface) SqlClient() *models.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SqlClient")
	ret0, _ := ret[0].(*models.Client)
	return ret0
}

// SqlClient indicates an expected call of SqlClient.
func (mr *MockClientSetInterfaceMockRecorder) SqlClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SqlClient", reflect.TypeOf((*MockClientSetInterface)(nil).SqlClient))
}
