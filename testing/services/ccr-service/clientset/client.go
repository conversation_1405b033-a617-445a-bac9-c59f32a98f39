// Code generated by mockery v2.43.0. DO NOT EDIT.

package clientset

import (
	auth "github.com/baidubce/bce-sdk-go/auth"
	bcd "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcd"
	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"

	bus "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bus"

	cce "github.com/baidubce/bce-sdk-go/services/cce"

	ccr "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr"

	certificate "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/certificate"

	client "sigs.k8s.io/controller-runtime/pkg/client"

	config "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/config"

	harbor "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"

	iam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"

	kubernetes "k8s.io/client-go/kubernetes"

	listers "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"

	logictag "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"

	mock "github.com/stretchr/testify/mock"

	models "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"

	personal "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr/personal"

	resourcegroup "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/resourcegroup"

	sts "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"

	usersetting "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"

	vpc "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
)

// ClientSet is an autogenerated mock type for the ClientSetInterface type
type ClientSet struct {
	mock.Mock
}

// AutoRuleClientForAccount provides a mock function with given fields: accountId, userId
func (_m *ClientSet) AutoRuleClientForAccount(accountId string, userId string) (billing.AutoRenewClientInterface, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for AutoRuleClientForAccount")
	}

	var r0 billing.AutoRenewClientInterface
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (billing.AutoRenewClientInterface, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) billing.AutoRenewClientInterface); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(billing.AutoRenewClientInterface)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BcdClientForAccount provides a mock function with given fields: accountId, userId
func (_m *ClientSet) BcdClientForAccount(accountId string, userId string) (bcd.Interface, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for BcdClientForAccount")
	}

	var r0 bcd.Interface
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (bcd.Interface, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) bcd.Interface); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(bcd.Interface)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BusClient provides a mock function with given fields:
func (_m *ClientSet) BusClient() bus.Client {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for BusClient")
	}

	var r0 bus.Client
	if rf, ok := ret.Get(0).(func() bus.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(bus.Client)
		}
	}

	return r0
}

// CCRClientForAccount provides a mock function with given fields: accountId, userId, endpoint
func (_m *ClientSet) CCRClientForAccount(accountId string, userId string, endpoint string) (ccr.ClientInterface, error) {
	ret := _m.Called(accountId, userId, endpoint)

	if len(ret) == 0 {
		panic("no return value specified for CCRClientForAccount")
	}

	var r0 ccr.ClientInterface
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string) (ccr.ClientInterface, error)); ok {
		return rf(accountId, userId, endpoint)
	}
	if rf, ok := ret.Get(0).(func(string, string, string) ccr.ClientInterface); ok {
		r0 = rf(accountId, userId, endpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ccr.ClientInterface)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(accountId, userId, endpoint)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CCRClientForAkSkSessionToken provides a mock function with given fields: ak, sk, sessionToken, endpoint
func (_m *ClientSet) CCRClientForAkSkSessionToken(ak string, sk string, sessionToken string, endpoint string) (ccr.ClientInterface, error) {
	ret := _m.Called(ak, sk, sessionToken, endpoint)

	if len(ret) == 0 {
		panic("no return value specified for CCRClientForAkSkSessionToken")
	}

	var r0 ccr.ClientInterface
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string, string) (ccr.ClientInterface, error)); ok {
		return rf(ak, sk, sessionToken, endpoint)
	}
	if rf, ok := ret.Get(0).(func(string, string, string, string) ccr.ClientInterface); ok {
		r0 = rf(ak, sk, sessionToken, endpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ccr.ClientInterface)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, string, string) error); ok {
		r1 = rf(ak, sk, sessionToken, endpoint)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CceClient provides a mock function with given fields:
func (_m *ClientSet) CceClient() *cce.Client {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for CceClient")
	}

	var r0 *cce.Client
	if rf, ok := ret.Get(0).(func() *cce.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*cce.Client)
		}
	}

	return r0
}

// CertClientForAccount provides a mock function with given fields: accountId, userId
func (_m *ClientSet) CertClientForAccount(accountId string, userId string) (certificate.Interface, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for CertClientForAccount")
	}

	var r0 certificate.Interface
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (certificate.Interface, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) certificate.Interface); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(certificate.Interface)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Conf provides a mock function with given fields:
func (_m *ClientSet) Conf() config.ServiceConfig {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Conf")
	}

	var r0 config.ServiceConfig
	if rf, ok := ret.Get(0).(func() config.ServiceConfig); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(config.ServiceConfig)
	}

	return r0
}

// FacadeClientForAccount provides a mock function with given fields: accountId, userId
func (_m *ClientSet) FacadeClientForAccount(accountId string, userId string) (billing.FacadeClientInterface, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for FacadeClientForAccount")
	}

	var r0 billing.FacadeClientInterface
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (billing.FacadeClientInterface, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) billing.FacadeClientInterface); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(billing.FacadeClientInterface)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBceCredential provides a mock function with given fields: accountId, userId
func (_m *ClientSet) GetBceCredential(accountId string, userId string) (*auth.BceCredentials, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for GetBceCredential")
	}

	var r0 *auth.BceCredentials
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*auth.BceCredentials, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) *auth.BceCredentials); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*auth.BceCredentials)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HarborClient provides a mock function with given fields: instanceID
func (_m *ClientSet) HarborClient(instanceID string) (*harbor.HarborClient, error) {
	ret := _m.Called(instanceID)

	if len(ret) == 0 {
		panic("no return value specified for HarborClient")
	}

	var r0 *harbor.HarborClient
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*harbor.HarborClient, error)); ok {
		return rf(instanceID)
	}
	if rf, ok := ret.Get(0).(func(string) *harbor.HarborClient); ok {
		r0 = rf(instanceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*harbor.HarborClient)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(instanceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HarborClientSimulate provides a mock function with given fields: instanceID, sid
func (_m *ClientSet) HarborClientSimulate(instanceID string, sid string) (*harbor.HarborClient, error) {
	ret := _m.Called(instanceID, sid)

	if len(ret) == 0 {
		panic("no return value specified for HarborClientSimulate")
	}

	var r0 *harbor.HarborClient
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*harbor.HarborClient, error)); ok {
		return rf(instanceID, sid)
	}
	if rf, ok := ret.Get(0).(func(string, string) *harbor.HarborClient); ok {
		r0 = rf(instanceID, sid)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*harbor.HarborClient)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(instanceID, sid)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// InternalIam provides a mock function with given fields:
func (_m *ClientSet) InternalIam() iam.ClientInterface {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for InternalIam")
	}

	var r0 iam.ClientInterface
	if rf, ok := ret.Get(0).(func() iam.ClientInterface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(iam.ClientInterface)
		}
	}

	return r0
}

// K8sClient provides a mock function with given fields:
func (_m *ClientSet) K8sClient() client.Client {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for K8sClient")
	}

	var r0 client.Client
	if rf, ok := ret.Get(0).(func() client.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(client.Client)
		}
	}

	return r0
}

// LegacyKubernetesClientset provides a mock function with given fields:
func (_m *ClientSet) LegacyKubernetesClientset() kubernetes.Interface {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for LegacyKubernetesClientset")
	}

	var r0 kubernetes.Interface
	if rf, ok := ret.Get(0).(func() kubernetes.Interface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(kubernetes.Interface)
		}
	}

	return r0
}

// Lister provides a mock function with given fields:
func (_m *ClientSet) Lister() listers.ListerInterface {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Lister")
	}

	var r0 listers.ListerInterface
	if rf, ok := ret.Get(0).(func() listers.ListerInterface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(listers.ListerInterface)
		}
	}

	return r0
}

// LogicTagClient provides a mock function with given fields:
func (_m *ClientSet) LogicTagClient() logictag.Client {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for LogicTagClient")
	}

	var r0 logictag.Client
	if rf, ok := ret.Get(0).(func() logictag.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(logictag.Client)
		}
	}

	return r0
}

// LogicTagClientForAccount provides a mock function with given fields: accountId, userId
func (_m *ClientSet) LogicTagClientForAccount(accountId string, userId string) (logictag.Client, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for LogicTagClientForAccount")
	}

	var r0 logictag.Client
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (logictag.Client, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) logictag.Client); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(logictag.Client)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OrderClient provides a mock function with given fields:
func (_m *ClientSet) OrderClient() billing.OrderClientInterface {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for OrderClient")
	}

	var r0 billing.OrderClientInterface
	if rf, ok := ret.Get(0).(func() billing.OrderClientInterface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(billing.OrderClientInterface)
		}
	}

	return r0
}

// OrderClientForAccount provides a mock function with given fields: accountId, userId
func (_m *ClientSet) OrderClientForAccount(accountId string, userId string) (billing.OrderClientInterface, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for OrderClientForAccount")
	}

	var r0 billing.OrderClientInterface
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (billing.OrderClientInterface, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) billing.OrderClientInterface); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(billing.OrderClientInterface)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PersonalClientForAccount provides a mock function with given fields: accountId, userId
func (_m *ClientSet) PersonalClientForAccount(accountId string, userId string) (personal.Interface, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for PersonalClientForAccount")
	}

	var r0 personal.Interface
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (personal.Interface, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) personal.Interface); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(personal.Interface)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ResourceClient provides a mock function with given fields:
func (_m *ClientSet) ResourceClient() billing.ResourceClientInterface {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ResourceClient")
	}

	var r0 billing.ResourceClientInterface
	if rf, ok := ret.Get(0).(func() billing.ResourceClientInterface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(billing.ResourceClientInterface)
		}
	}

	return r0
}

// ResourceClientForAccount provides a mock function with given fields: accountId, userId
func (_m *ClientSet) ResourceClientForAccount(accountId string, userId string) (billing.ResourceClientInterface, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for ResourceClientForAccount")
	}

	var r0 billing.ResourceClientInterface
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (billing.ResourceClientInterface, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) billing.ResourceClientInterface); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(billing.ResourceClientInterface)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ResourceGroupClient provides a mock function with given fields:
func (_m *ClientSet) ResourceGroupClient() resourcegroup.Client {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ResourceGroupClient")
	}

	var r0 resourcegroup.Client
	if rf, ok := ret.Get(0).(func() resourcegroup.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(resourcegroup.Client)
		}
	}

	return r0
}

// ResourceGroupClientForAccount provides a mock function with given fields: accountId, userId
func (_m *ClientSet) ResourceGroupClientForAccount(accountId string, userId string) (resourcegroup.Client, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for ResourceGroupClientForAccount")
	}

	var r0 resourcegroup.Client
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (resourcegroup.Client, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) resourcegroup.Client); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(resourcegroup.Client)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SqlClient provides a mock function with given fields:
func (_m *ClientSet) SqlClient() models.Interface {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for SqlClient")
	}

	var r0 models.Interface
	if rf, ok := ret.Get(0).(func() models.Interface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(models.Interface)
		}
	}

	return r0
}

// StsClient provides a mock function with given fields:
func (_m *ClientSet) StsClient() sts.ClientInterface {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for StsClient")
	}

	var r0 sts.ClientInterface
	if rf, ok := ret.Get(0).(func() sts.ClientInterface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(sts.ClientInterface)
		}
	}

	return r0
}

// Usersetting provides a mock function with given fields:
func (_m *ClientSet) Usersetting() usersetting.ClientInterface {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Usersetting")
	}

	var r0 usersetting.ClientInterface
	if rf, ok := ret.Get(0).(func() usersetting.ClientInterface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(usersetting.ClientInterface)
		}
	}

	return r0
}

// VpcClientForAccount provides a mock function with given fields: accountId, userId
func (_m *ClientSet) VpcClientForAccount(accountId string, userId string) (vpc.ClientInterface, error) {
	ret := _m.Called(accountId, userId)

	if len(ret) == 0 {
		panic("no return value specified for VpcClientForAccount")
	}

	var r0 vpc.ClientInterface
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (vpc.ClientInterface, error)); ok {
		return rf(accountId, userId)
	}
	if rf, ok := ret.Get(0).(func(string, string) vpc.ClientInterface); ok {
		r0 = rf(accountId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(vpc.ClientInterface)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewClientSet creates a new instance of ClientSet. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClientSet(t interface {
	mock.TestingT
	Cleanup(func())
}) *ClientSet {
	mock := &ClientSet{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
