// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset (interfaces: ClientSetInterface)

// Package clientset is a generated GoMock package.
package clientset

import (
	reflect "reflect"

	auth "github.com/baidubce/bce-sdk-go/auth"
	cce "github.com/baidubce/bce-sdk-go/services/cce"
	gomock "github.com/golang/mock/gomock"
	bcd "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcd"
	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	bus "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bus"
	ccr "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr"
	personal "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr/personal"
	certificate "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/certificate"
	iam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	logictag "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
	resourcegroup "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/resourcegroup"
	sts "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"
	usersetting "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	vpc "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
	harbor "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	listers "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	models "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	config "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/config"
	kubernetes "k8s.io/client-go/kubernetes"
	client "sigs.k8s.io/controller-runtime/pkg/client"
)

// MockClientSetInterface is a mock of ClientSetInterface interface.
type MockClientSetInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClientSetInterfaceMockRecorder
}

// MockClientSetInterfaceMockRecorder is the mock recorder for MockClientSetInterface.
type MockClientSetInterfaceMockRecorder struct {
	mock *MockClientSetInterface
}

// NewMockClientSetInterface creates a new mock instance.
func NewMockClientSetInterface(ctrl *gomock.Controller) *MockClientSetInterface {
	mock := &MockClientSetInterface{ctrl: ctrl}
	mock.recorder = &MockClientSetInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientSetInterface) EXPECT() *MockClientSetInterfaceMockRecorder {
	return m.recorder
}

// AutoRuleClientForAccount mocks base method.
func (m *MockClientSetInterface) AutoRuleClientForAccount(arg0, arg1 string) (billing.AutoRenewClientInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AutoRuleClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(billing.AutoRenewClientInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AutoRuleClientForAccount indicates an expected call of AutoRuleClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) AutoRuleClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoRuleClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).AutoRuleClientForAccount), arg0, arg1)
}

// BcdClientForAccount mocks base method.
func (m *MockClientSetInterface) BcdClientForAccount(arg0, arg1 string) (bcd.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BcdClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(bcd.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BcdClientForAccount indicates an expected call of BcdClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) BcdClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BcdClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).BcdClientForAccount), arg0, arg1)
}

// BusClient mocks base method.
func (m *MockClientSetInterface) BusClient() bus.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BusClient")
	ret0, _ := ret[0].(bus.Client)
	return ret0
}

// BusClient indicates an expected call of BusClient.
func (mr *MockClientSetInterfaceMockRecorder) BusClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BusClient", reflect.TypeOf((*MockClientSetInterface)(nil).BusClient))
}

// CCRClientForAccount mocks base method.
func (m *MockClientSetInterface) CCRClientForAccount(arg0, arg1, arg2 string) (ccr.ClientInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CCRClientForAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(ccr.ClientInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CCRClientForAccount indicates an expected call of CCRClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) CCRClientForAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CCRClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).CCRClientForAccount), arg0, arg1, arg2)
}

// CCRClientForAkSkSessionToken mocks base method.
func (m *MockClientSetInterface) CCRClientForAkSkSessionToken(arg0, arg1, arg2, arg3 string) (ccr.ClientInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CCRClientForAkSkSessionToken", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(ccr.ClientInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CCRClientForAkSkSessionToken indicates an expected call of CCRClientForAkSkSessionToken.
func (mr *MockClientSetInterfaceMockRecorder) CCRClientForAkSkSessionToken(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CCRClientForAkSkSessionToken", reflect.TypeOf((*MockClientSetInterface)(nil).CCRClientForAkSkSessionToken), arg0, arg1, arg2, arg3)
}

// CceClient mocks base method.
func (m *MockClientSetInterface) CceClient() *cce.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CceClient")
	ret0, _ := ret[0].(*cce.Client)
	return ret0
}

// CceClient indicates an expected call of CceClient.
func (mr *MockClientSetInterfaceMockRecorder) CceClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CceClient", reflect.TypeOf((*MockClientSetInterface)(nil).CceClient))
}

// CertClientForAccount mocks base method.
func (m *MockClientSetInterface) CertClientForAccount(arg0, arg1 string) (certificate.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CertClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(certificate.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CertClientForAccount indicates an expected call of CertClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) CertClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CertClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).CertClientForAccount), arg0, arg1)
}

// Conf mocks base method.
func (m *MockClientSetInterface) Conf() config.ServiceConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Conf")
	ret0, _ := ret[0].(config.ServiceConfig)
	return ret0
}

// Conf indicates an expected call of Conf.
func (mr *MockClientSetInterfaceMockRecorder) Conf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Conf", reflect.TypeOf((*MockClientSetInterface)(nil).Conf))
}

// FacadeClientForAccount mocks base method.
func (m *MockClientSetInterface) FacadeClientForAccount(arg0, arg1 string) (billing.FacadeClientInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FacadeClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(billing.FacadeClientInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FacadeClientForAccount indicates an expected call of FacadeClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) FacadeClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FacadeClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).FacadeClientForAccount), arg0, arg1)
}

// GetBceCredential mocks base method.
func (m *MockClientSetInterface) GetBceCredential(arg0, arg1 string) (*auth.BceCredentials, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBceCredential", arg0, arg1)
	ret0, _ := ret[0].(*auth.BceCredentials)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBceCredential indicates an expected call of GetBceCredential.
func (mr *MockClientSetInterfaceMockRecorder) GetBceCredential(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBceCredential", reflect.TypeOf((*MockClientSetInterface)(nil).GetBceCredential), arg0, arg1)
}

// HarborClient mocks base method.
func (m *MockClientSetInterface) HarborClient(arg0 string) (*harbor.HarborClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HarborClient", arg0)
	ret0, _ := ret[0].(*harbor.HarborClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HarborClient indicates an expected call of HarborClient.
func (mr *MockClientSetInterfaceMockRecorder) HarborClient(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HarborClient", reflect.TypeOf((*MockClientSetInterface)(nil).HarborClient), arg0)
}

// HarborClientSimulate mocks base method.
func (m *MockClientSetInterface) HarborClientSimulate(arg0, arg1 string) (*harbor.HarborClient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HarborClientSimulate", arg0, arg1)
	ret0, _ := ret[0].(*harbor.HarborClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HarborClientSimulate indicates an expected call of HarborClientSimulate.
func (mr *MockClientSetInterfaceMockRecorder) HarborClientSimulate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HarborClientSimulate", reflect.TypeOf((*MockClientSetInterface)(nil).HarborClientSimulate), arg0, arg1)
}

// InternalIam mocks base method.
func (m *MockClientSetInterface) InternalIam() iam.ClientInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InternalIam")
	ret0, _ := ret[0].(iam.ClientInterface)
	return ret0
}

// InternalIam indicates an expected call of InternalIam.
func (mr *MockClientSetInterfaceMockRecorder) InternalIam() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternalIam", reflect.TypeOf((*MockClientSetInterface)(nil).InternalIam))
}

// K8sClient mocks base method.
func (m *MockClientSetInterface) K8sClient() client.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "K8sClient")
	ret0, _ := ret[0].(client.Client)
	return ret0
}

// K8sClient indicates an expected call of K8sClient.
func (mr *MockClientSetInterfaceMockRecorder) K8sClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "K8sClient", reflect.TypeOf((*MockClientSetInterface)(nil).K8sClient))
}

// LegacyKubernetesClientset mocks base method.
func (m *MockClientSetInterface) LegacyKubernetesClientset() kubernetes.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LegacyKubernetesClientset")
	ret0, _ := ret[0].(kubernetes.Interface)
	return ret0
}

// LegacyKubernetesClientset indicates an expected call of LegacyKubernetesClientset.
func (mr *MockClientSetInterfaceMockRecorder) LegacyKubernetesClientset() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LegacyKubernetesClientset", reflect.TypeOf((*MockClientSetInterface)(nil).LegacyKubernetesClientset))
}

// Lister mocks base method.
func (m *MockClientSetInterface) Lister() listers.ListerInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lister")
	ret0, _ := ret[0].(listers.ListerInterface)
	return ret0
}

// Lister indicates an expected call of Lister.
func (mr *MockClientSetInterfaceMockRecorder) Lister() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lister", reflect.TypeOf((*MockClientSetInterface)(nil).Lister))
}

// LogicTagClient mocks base method.
func (m *MockClientSetInterface) LogicTagClient() logictag.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LogicTagClient")
	ret0, _ := ret[0].(logictag.Client)
	return ret0
}

// LogicTagClient indicates an expected call of LogicTagClient.
func (mr *MockClientSetInterfaceMockRecorder) LogicTagClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LogicTagClient", reflect.TypeOf((*MockClientSetInterface)(nil).LogicTagClient))
}

// LogicTagClientForAccount mocks base method.
func (m *MockClientSetInterface) LogicTagClientForAccount(arg0, arg1 string) (logictag.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LogicTagClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(logictag.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LogicTagClientForAccount indicates an expected call of LogicTagClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) LogicTagClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LogicTagClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).LogicTagClientForAccount), arg0, arg1)
}

// OrderClient mocks base method.
func (m *MockClientSetInterface) OrderClient() billing.OrderClientInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OrderClient")
	ret0, _ := ret[0].(billing.OrderClientInterface)
	return ret0
}

// OrderClient indicates an expected call of OrderClient.
func (mr *MockClientSetInterfaceMockRecorder) OrderClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderClient", reflect.TypeOf((*MockClientSetInterface)(nil).OrderClient))
}

// OrderClientForAccount mocks base method.
func (m *MockClientSetInterface) OrderClientForAccount(arg0, arg1 string) (billing.OrderClientInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OrderClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(billing.OrderClientInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OrderClientForAccount indicates an expected call of OrderClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) OrderClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).OrderClientForAccount), arg0, arg1)
}

// PersonalClientForAccount mocks base method.
func (m *MockClientSetInterface) PersonalClientForAccount(arg0, arg1 string) (personal.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PersonalClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(personal.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PersonalClientForAccount indicates an expected call of PersonalClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) PersonalClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PersonalClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).PersonalClientForAccount), arg0, arg1)
}

// ResourceClient mocks base method.
func (m *MockClientSetInterface) ResourceClient() billing.ResourceClientInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResourceClient")
	ret0, _ := ret[0].(billing.ResourceClientInterface)
	return ret0
}

// ResourceClient indicates an expected call of ResourceClient.
func (mr *MockClientSetInterfaceMockRecorder) ResourceClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceClient", reflect.TypeOf((*MockClientSetInterface)(nil).ResourceClient))
}

// ResourceClientForAccount mocks base method.
func (m *MockClientSetInterface) ResourceClientForAccount(arg0, arg1 string) (billing.ResourceClientInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResourceClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(billing.ResourceClientInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResourceClientForAccount indicates an expected call of ResourceClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) ResourceClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).ResourceClientForAccount), arg0, arg1)
}

// ResourceGroupClient mocks base method.
func (m *MockClientSetInterface) ResourceGroupClient() resourcegroup.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResourceGroupClient")
	ret0, _ := ret[0].(resourcegroup.Client)
	return ret0
}

// ResourceGroupClient indicates an expected call of ResourceGroupClient.
func (mr *MockClientSetInterfaceMockRecorder) ResourceGroupClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceGroupClient", reflect.TypeOf((*MockClientSetInterface)(nil).ResourceGroupClient))
}

// ResourceGroupClientForAccount mocks base method.
func (m *MockClientSetInterface) ResourceGroupClientForAccount(arg0, arg1 string) (resourcegroup.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResourceGroupClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(resourcegroup.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResourceGroupClientForAccount indicates an expected call of ResourceGroupClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) ResourceGroupClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceGroupClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).ResourceGroupClientForAccount), arg0, arg1)
}

// SqlClient mocks base method.
func (m *MockClientSetInterface) SqlClient() models.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SqlClient")
	ret0, _ := ret[0].(models.Interface)
	return ret0
}

// SqlClient indicates an expected call of SqlClient.
func (mr *MockClientSetInterfaceMockRecorder) SqlClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SqlClient", reflect.TypeOf((*MockClientSetInterface)(nil).SqlClient))
}

// StsClient mocks base method.
func (m *MockClientSetInterface) StsClient() sts.ClientInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StsClient")
	ret0, _ := ret[0].(sts.ClientInterface)
	return ret0
}

// StsClient indicates an expected call of StsClient.
func (mr *MockClientSetInterfaceMockRecorder) StsClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StsClient", reflect.TypeOf((*MockClientSetInterface)(nil).StsClient))
}

// Usersetting mocks base method.
func (m *MockClientSetInterface) Usersetting() usersetting.ClientInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Usersetting")
	ret0, _ := ret[0].(usersetting.ClientInterface)
	return ret0
}

// Usersetting indicates an expected call of Usersetting.
func (mr *MockClientSetInterfaceMockRecorder) Usersetting() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Usersetting", reflect.TypeOf((*MockClientSetInterface)(nil).Usersetting))
}

// VpcClientForAccount mocks base method.
func (m *MockClientSetInterface) VpcClientForAccount(arg0, arg1 string) (vpc.ClientInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VpcClientForAccount", arg0, arg1)
	ret0, _ := ret[0].(vpc.ClientInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VpcClientForAccount indicates an expected call of VpcClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) VpcClientForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VpcClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).VpcClientForAccount), arg0, arg1)
}
