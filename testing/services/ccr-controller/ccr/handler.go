// Code generated by mockery v2.28.1. DO NOT EDIT.

package ccr

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	v1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
)

// CCRHandler is an autogenerated mock type for the CCRHandlerInterface type
type CCRHandler struct {
	mock.Mock
}

// <PERSON>le provides a mock function with given fields: ctx, object
func (_m *CCRHandler) Handle(ctx context.Context, object *v1alpha1.CCR) error {
	ret := _m.Called(ctx, object)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *v1alpha1.CCR) error); ok {
		r0 = rf(ctx, object)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewCCRHandler interface {
	mock.TestingT
	Cleanup(func())
}

// NewCCRHandler creates a new instance of <PERSON>R<PERSON>andler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewCCRHandler(t mockConstructorTestingTNewCCRHandler) *CCRHandler {
	mock := &CCRHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
