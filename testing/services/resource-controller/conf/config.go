// Code generated by mockery v2.43.0. DO NOT EDIT.

package conf

import (
	mock "github.com/stretchr/testify/mock"
	sets "k8s.io/apimachinery/pkg/util/sets"
)

// Config is an autogenerated mock type for the ConfigInterface type
type Config struct {
	mock.Mock
}

// BindFeatures provides a mock function with given fields: features
func (_m *Config) BindFeatures(features sets.String) error {
	ret := _m.Called(features)

	if len(ret) == 0 {
		panic("no return value specified for BindFeatures")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(sets.String) error); ok {
		r0 = rf(features)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FeatureSupported provides a mock function with given fields: f
func (_m *Config) FeatureSupported(f string) bool {
	ret := _m.Called(f)

	if len(ret) == 0 {
		panic("no return value specified for FeatureSupported")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(f)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MustBLB provides a mock function with given fields:
func (_m *Config) MustBLB() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for MustBLB")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MustBilling provides a mock function with given fields:
func (_m *Config) MustBilling() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for MustBilling")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MustEIP provides a mock function with given fields:
func (_m *Config) MustEIP() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for MustEIP")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MustRds provides a mock function with given fields:
func (_m *Config) MustRds() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for MustRds")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MustResgroup provides a mock function with given fields:
func (_m *Config) MustResgroup() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for MustResgroup")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MustSQL provides a mock function with given fields:
func (_m *Config) MustSQL() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for MustSQL")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MustSTS provides a mock function with given fields:
func (_m *Config) MustSTS() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for MustSTS")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MustUserSetting provides a mock function with given fields:
func (_m *Config) MustUserSetting() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for MustUserSetting")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// NewConfig creates a new instance of Config. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewConfig(t interface {
	mock.TestingT
	Cleanup(func())
}) *Config {
	mock := &Config{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
