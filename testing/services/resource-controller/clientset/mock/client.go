// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset (interfaces: ClientSetInterface)

// Package clientset is a generated GoMock package.
package clientset

import (
	reflect "reflect"

	eip "github.com/baidubce/bce-sdk-go/services/eip"
	rds "github.com/baidubce/bce-sdk-go/services/rds"
	scs "github.com/baidubce/bce-sdk-go/services/scs"
	gomock "github.com/golang/mock/gomock"
	bcd "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcd"
	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	blb "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/blb"
	bosinterface "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bosinterface"
	certificate "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/certificate"
	dns "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"
	iam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	privatezone "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/privatezone"
	usersetting "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	vpc "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
	kafka "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/kafka"
	models "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	devops "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models/devops"
	clientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	rest "k8s.io/client-go/rest"
)

// MockClientSetInterface is a mock of ClientSetInterface interface.
type MockClientSetInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClientSetInterfaceMockRecorder
}

// MockClientSetInterfaceMockRecorder is the mock recorder for MockClientSetInterface.
type MockClientSetInterfaceMockRecorder struct {
	mock *MockClientSetInterface
}

// NewMockClientSetInterface creates a new mock instance.
func NewMockClientSetInterface(ctrl *gomock.Controller) *MockClientSetInterface {
	mock := &MockClientSetInterface{ctrl: ctrl}
	mock.recorder = &MockClientSetInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientSetInterface) EXPECT() *MockClientSetInterfaceMockRecorder {
	return m.recorder
}

// BcdClientForAccount mocks base method.
func (m *MockClientSetInterface) BcdClientForAccount(arg0, arg1, arg2 string) (bcd.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BcdClientForAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(bcd.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BcdClientForAccount indicates an expected call of BcdClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) BcdClientForAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BcdClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).BcdClientForAccount), arg0, arg1, arg2)
}

// BlbClient mocks base method.
func (m *MockClientSetInterface) BlbClient() *blb.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlbClient")
	ret0, _ := ret[0].(*blb.Client)
	return ret0
}

// BlbClient indicates an expected call of BlbClient.
func (mr *MockClientSetInterfaceMockRecorder) BlbClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlbClient", reflect.TypeOf((*MockClientSetInterface)(nil).BlbClient))
}

// BosClientForAccount mocks base method.
func (m *MockClientSetInterface) BosClientForAccount(arg0, arg1, arg2 string) (bosinterface.BosInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BosClientForAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(bosinterface.BosInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BosClientForAccount indicates an expected call of BosClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) BosClientForAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BosClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).BosClientForAccount), arg0, arg1, arg2)
}

// CertClientForAccount mocks base method.
func (m *MockClientSetInterface) CertClientForAccount(arg0, arg1, arg2 string) (certificate.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CertClientForAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(certificate.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CertClientForAccount indicates an expected call of CertClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) CertClientForAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CertClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).CertClientForAccount), arg0, arg1, arg2)
}

// DevopsClient mocks base method.
func (m *MockClientSetInterface) DevopsClient() *devops.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DevopsClient")
	ret0, _ := ret[0].(*devops.Client)
	return ret0
}

// DevopsClient indicates an expected call of DevopsClient.
func (mr *MockClientSetInterfaceMockRecorder) DevopsClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DevopsClient", reflect.TypeOf((*MockClientSetInterface)(nil).DevopsClient))
}

// DnsClient mocks base method.
func (m *MockClientSetInterface) DnsClient() *dns.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DnsClient")
	ret0, _ := ret[0].(*dns.Client)
	return ret0
}

// DnsClient indicates an expected call of DnsClient.
func (mr *MockClientSetInterfaceMockRecorder) DnsClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DnsClient", reflect.TypeOf((*MockClientSetInterface)(nil).DnsClient))
}

// EipClient mocks base method.
func (m *MockClientSetInterface) EipClient() *eip.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EipClient")
	ret0, _ := ret[0].(*eip.Client)
	return ret0
}

// EipClient indicates an expected call of EipClient.
func (mr *MockClientSetInterfaceMockRecorder) EipClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EipClient", reflect.TypeOf((*MockClientSetInterface)(nil).EipClient))
}

// IAMClient mocks base method.
func (m *MockClientSetInterface) IAMClient() *iam.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IAMClient")
	ret0, _ := ret[0].(*iam.Client)
	return ret0
}

// IAMClient indicates an expected call of IAMClient.
func (mr *MockClientSetInterfaceMockRecorder) IAMClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IAMClient", reflect.TypeOf((*MockClientSetInterface)(nil).IAMClient))
}

// PrivateZoneClientForAccount mocks base method.
func (m *MockClientSetInterface) PrivateZoneClientForAccount(arg0, arg1, arg2 string) (privatezone.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrivateZoneClientForAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(privatezone.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrivateZoneClientForAccount indicates an expected call of PrivateZoneClientForAccount.
func (mr *MockClientSetInterfaceMockRecorder) PrivateZoneClientForAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrivateZoneClientForAccount", reflect.TypeOf((*MockClientSetInterface)(nil).PrivateZoneClientForAccount), arg0, arg1, arg2)
}

// Producer mocks base method.
func (m *MockClientSetInterface) Producer() kafka.ProducerInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Producer")
	ret0, _ := ret[0].(kafka.ProducerInterface)
	return ret0
}

// Producer indicates an expected call of Producer.
func (mr *MockClientSetInterfaceMockRecorder) Producer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Producer", reflect.TypeOf((*MockClientSetInterface)(nil).Producer))
}

// RdsClient mocks base method.
func (m *MockClientSetInterface) RdsClient() *rds.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RdsClient")
	ret0, _ := ret[0].(*rds.Client)
	return ret0
}

// RdsClient indicates an expected call of RdsClient.
func (mr *MockClientSetInterfaceMockRecorder) RdsClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RdsClient", reflect.TypeOf((*MockClientSetInterface)(nil).RdsClient))
}

// ResourceClient mocks base method.
func (m *MockClientSetInterface) ResourceClient() billing.ResourceClientInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResourceClient")
	ret0, _ := ret[0].(billing.ResourceClientInterface)
	return ret0
}

// ResourceClient indicates an expected call of ResourceClient.
func (mr *MockClientSetInterfaceMockRecorder) ResourceClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceClient", reflect.TypeOf((*MockClientSetInterface)(nil).ResourceClient))
}

// RestConfig mocks base method.
func (m *MockClientSetInterface) RestConfig(arg0 string) (*rest.Config, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestConfig", arg0)
	ret0, _ := ret[0].(*rest.Config)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RestConfig indicates an expected call of RestConfig.
func (mr *MockClientSetInterfaceMockRecorder) RestConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestConfig", reflect.TypeOf((*MockClientSetInterface)(nil).RestConfig), arg0)
}

// ScsClient mocks base method.
func (m *MockClientSetInterface) ScsClient() *scs.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScsClient")
	ret0, _ := ret[0].(*scs.Client)
	return ret0
}

// ScsClient indicates an expected call of ScsClient.
func (mr *MockClientSetInterfaceMockRecorder) ScsClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScsClient", reflect.TypeOf((*MockClientSetInterface)(nil).ScsClient))
}

// SqlClient mocks base method.
func (m *MockClientSetInterface) SqlClient() *models.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SqlClient")
	ret0, _ := ret[0].(*models.Client)
	return ret0
}

// SqlClient indicates an expected call of SqlClient.
func (mr *MockClientSetInterfaceMockRecorder) SqlClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SqlClient", reflect.TypeOf((*MockClientSetInterface)(nil).SqlClient))
}

// StsCredentialWithExpiredAt mocks base method.
func (m *MockClientSetInterface) StsCredentialWithExpiredAt(arg0, arg1 string) (*clientset.Credentials, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StsCredentialWithExpiredAt", arg0, arg1)
	ret0, _ := ret[0].(*clientset.Credentials)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StsCredentialWithExpiredAt indicates an expected call of StsCredentialWithExpiredAt.
func (mr *MockClientSetInterfaceMockRecorder) StsCredentialWithExpiredAt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StsCredentialWithExpiredAt", reflect.TypeOf((*MockClientSetInterface)(nil).StsCredentialWithExpiredAt), arg0, arg1)
}

// Usersetting mocks base method.
func (m *MockClientSetInterface) Usersetting() usersetting.ClientInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Usersetting")
	ret0, _ := ret[0].(usersetting.ClientInterface)
	return ret0
}

// Usersetting indicates an expected call of Usersetting.
func (mr *MockClientSetInterfaceMockRecorder) Usersetting() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Usersetting", reflect.TypeOf((*MockClientSetInterface)(nil).Usersetting))
}

// VpcClientFromAccount mocks base method.
func (m *MockClientSetInterface) VpcClientFromAccount(arg0, arg1, arg2 string) (*vpc.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VpcClientFromAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*vpc.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VpcClientFromAccount indicates an expected call of VpcClientFromAccount.
func (mr *MockClientSetInterfaceMockRecorder) VpcClientFromAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VpcClientFromAccount", reflect.TypeOf((*MockClientSetInterface)(nil).VpcClientFromAccount), arg0, arg1, arg2)
}

// VpcDnsClient mocks base method.
func (m *MockClientSetInterface) VpcDnsClient() *dns.VpcDnsClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VpcDnsClient")
	ret0, _ := ret[0].(*dns.VpcDnsClient)
	return ret0
}

// VpcDnsClient indicates an expected call of VpcDnsClient.
func (mr *MockClientSetInterfaceMockRecorder) VpcDnsClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VpcDnsClient", reflect.TypeOf((*MockClientSetInterface)(nil).VpcDnsClient))
}

// VpcDnsClientFromAccount mocks base method.
func (m *MockClientSetInterface) VpcDnsClientFromAccount(arg0, arg1, arg2 string) (dns.VpcDnsClientInterface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VpcDnsClientFromAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(dns.VpcDnsClientInterface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VpcDnsClientFromAccount indicates an expected call of VpcDnsClientFromAccount.
func (mr *MockClientSetInterfaceMockRecorder) VpcDnsClientFromAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VpcDnsClientFromAccount", reflect.TypeOf((*MockClientSetInterface)(nil).VpcDnsClientFromAccount), arg0, arg1, arg2)
}

// VpcID mocks base method.
func (m *MockClientSetInterface) VpcID(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VpcID", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VpcID indicates an expected call of VpcID.
func (mr *MockClientSetInterfaceMockRecorder) VpcID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VpcID", reflect.TypeOf((*MockClientSetInterface)(nil).VpcID), arg0)
}
