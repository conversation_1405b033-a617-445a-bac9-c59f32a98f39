// Code generated by mockery v0.0.0-dev. DO NOT EDIT.

package clientset

import (
	bcd "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcd"
	billing "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"

	blb "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/blb"

	bosinterface "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bosinterface"

	certificate "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/certificate"

	clientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"

	devops "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models/devops"

	dns "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"

	eip "github.com/baidubce/bce-sdk-go/services/eip"

	gaiadb "github.com/baidubce/bce-sdk-go/services/gaiadb"

	iam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"

	kafka "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/kafka"

	mock "github.com/stretchr/testify/mock"

	models "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"

	privatezone "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/privatezone"

	rds "github.com/baidubce/bce-sdk-go/services/rds"

	rest "k8s.io/client-go/rest"

	scs "github.com/baidubce/bce-sdk-go/services/scs"

	usersetting "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"

	vpc "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
)

// ClientSet is an autogenerated mock type for the ClientSetInterface type
type ClientSet struct {
	mock.Mock
}

// BcdClientForAccount provides a mock function with given fields: accountId, userId, endpoint
func (_m *ClientSet) BcdClientForAccount(accountId string, userId string, endpoint string) (bcd.Interface, error) {
	ret := _m.Called(accountId, userId, endpoint)

	var r0 bcd.Interface
	if rf, ok := ret.Get(0).(func(string, string, string) bcd.Interface); ok {
		r0 = rf(accountId, userId, endpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(bcd.Interface)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(accountId, userId, endpoint)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BlbClient provides a mock function with given fields:
func (_m *ClientSet) BlbClient() *blb.Client {
	ret := _m.Called()

	var r0 *blb.Client
	if rf, ok := ret.Get(0).(func() *blb.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*blb.Client)
		}
	}

	return r0
}

// BosClientForAccount provides a mock function with given fields: accountId, userId, endpoint
func (_m *ClientSet) BosClientForAccount(accountId string, userId string, endpoint string) (bosinterface.BosInterface, error) {
	ret := _m.Called(accountId, userId, endpoint)

	var r0 bosinterface.BosInterface
	if rf, ok := ret.Get(0).(func(string, string, string) bosinterface.BosInterface); ok {
		r0 = rf(accountId, userId, endpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(bosinterface.BosInterface)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(accountId, userId, endpoint)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CertClientForAccount provides a mock function with given fields: accountId, userId, endpoint
func (_m *ClientSet) CertClientForAccount(accountId string, userId string, endpoint string) (certificate.Interface, error) {
	ret := _m.Called(accountId, userId, endpoint)

	var r0 certificate.Interface
	if rf, ok := ret.Get(0).(func(string, string, string) certificate.Interface); ok {
		r0 = rf(accountId, userId, endpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(certificate.Interface)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(accountId, userId, endpoint)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DevopsClient provides a mock function with given fields:
func (_m *ClientSet) DevopsClient() *devops.Client {
	ret := _m.Called()

	var r0 *devops.Client
	if rf, ok := ret.Get(0).(func() *devops.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*devops.Client)
		}
	}

	return r0
}

// DnsClient provides a mock function with given fields:
func (_m *ClientSet) DnsClient() *dns.Client {
	ret := _m.Called()

	var r0 *dns.Client
	if rf, ok := ret.Get(0).(func() *dns.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dns.Client)
		}
	}

	return r0
}

// EipClient provides a mock function with given fields:
func (_m *ClientSet) EipClient() *eip.Client {
	ret := _m.Called()

	var r0 *eip.Client
	if rf, ok := ret.Get(0).(func() *eip.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*eip.Client)
		}
	}

	return r0
}

// GaiaDBClient provides a mock function with given fields:
func (_m *ClientSet) GaiaDBClient() *gaiadb.Client {
	ret := _m.Called()

	var r0 *gaiadb.Client
	if rf, ok := ret.Get(0).(func() *gaiadb.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gaiadb.Client)
		}
	}

	return r0
}

// IAMClient provides a mock function with given fields:
func (_m *ClientSet) IAMClient() *iam.Client {
	ret := _m.Called()

	var r0 *iam.Client
	if rf, ok := ret.Get(0).(func() *iam.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iam.Client)
		}
	}

	return r0
}

// PrivateZoneClientForAccount provides a mock function with given fields: accountID, userID, endpoint
func (_m *ClientSet) PrivateZoneClientForAccount(accountID string, userID string, endpoint string) (privatezone.Interface, error) {
	ret := _m.Called(accountID, userID, endpoint)

	var r0 privatezone.Interface
	if rf, ok := ret.Get(0).(func(string, string, string) privatezone.Interface); ok {
		r0 = rf(accountID, userID, endpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(privatezone.Interface)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(accountID, userID, endpoint)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Producer provides a mock function with given fields:
func (_m *ClientSet) Producer() kafka.ProducerInterface {
	ret := _m.Called()

	var r0 kafka.ProducerInterface
	if rf, ok := ret.Get(0).(func() kafka.ProducerInterface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(kafka.ProducerInterface)
		}
	}

	return r0
}

// RdsClient provides a mock function with given fields:
func (_m *ClientSet) RdsClient() *rds.Client {
	ret := _m.Called()

	var r0 *rds.Client
	if rf, ok := ret.Get(0).(func() *rds.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*rds.Client)
		}
	}

	return r0
}

// ResourceClient provides a mock function with given fields:
func (_m *ClientSet) ResourceClient() billing.ResourceClientInterface {
	ret := _m.Called()

	var r0 billing.ResourceClientInterface
	if rf, ok := ret.Get(0).(func() billing.ResourceClientInterface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(billing.ResourceClientInterface)
		}
	}

	return r0
}

// RestConfig provides a mock function with given fields: clusterID
func (_m *ClientSet) RestConfig(clusterID string) (*rest.Config, error) {
	ret := _m.Called(clusterID)

	var r0 *rest.Config
	if rf, ok := ret.Get(0).(func(string) *rest.Config); ok {
		r0 = rf(clusterID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*rest.Config)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(clusterID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ScsClient provides a mock function with given fields:
func (_m *ClientSet) ScsClient() *scs.Client {
	ret := _m.Called()

	var r0 *scs.Client
	if rf, ok := ret.Get(0).(func() *scs.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*scs.Client)
		}
	}

	return r0
}

// SqlClient provides a mock function with given fields:
func (_m *ClientSet) SqlClient() *models.Client {
	ret := _m.Called()

	var r0 *models.Client
	if rf, ok := ret.Get(0).(func() *models.Client); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Client)
		}
	}

	return r0
}

// StsCredentialWithExpiredAt provides a mock function with given fields: accountId, username
func (_m *ClientSet) StsCredentialWithExpiredAt(accountId string, username string) (*clientset.Credentials, error) {
	ret := _m.Called(accountId, username)

	var r0 *clientset.Credentials
	if rf, ok := ret.Get(0).(func(string, string) *clientset.Credentials); ok {
		r0 = rf(accountId, username)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*clientset.Credentials)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(accountId, username)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Usersetting provides a mock function with given fields:
func (_m *ClientSet) Usersetting() usersetting.ClientInterface {
	ret := _m.Called()

	var r0 usersetting.ClientInterface
	if rf, ok := ret.Get(0).(func() usersetting.ClientInterface); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(usersetting.ClientInterface)
		}
	}

	return r0
}

// VpcClientFromAccount provides a mock function with given fields: accountId, userId, endpoint
func (_m *ClientSet) VpcClientFromAccount(accountId string, userId string, endpoint string) (*vpc.Client, error) {
	ret := _m.Called(accountId, userId, endpoint)

	var r0 *vpc.Client
	if rf, ok := ret.Get(0).(func(string, string, string) *vpc.Client); ok {
		r0 = rf(accountId, userId, endpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vpc.Client)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(accountId, userId, endpoint)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VpcDnsClient provides a mock function with given fields:
func (_m *ClientSet) VpcDnsClient() *dns.VpcDnsClient {
	ret := _m.Called()

	var r0 *dns.VpcDnsClient
	if rf, ok := ret.Get(0).(func() *dns.VpcDnsClient); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dns.VpcDnsClient)
		}
	}

	return r0
}

// VpcDnsClientFromAccount provides a mock function with given fields: accountId, userId, endpoint
func (_m *ClientSet) VpcDnsClientFromAccount(accountId string, userId string, endpoint string) (dns.VpcDnsClientInterface, error) {
	ret := _m.Called(accountId, userId, endpoint)

	var r0 dns.VpcDnsClientInterface
	if rf, ok := ret.Get(0).(func(string, string, string) dns.VpcDnsClientInterface); ok {
		r0 = rf(accountId, userId, endpoint)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(dns.VpcDnsClientInterface)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(accountId, userId, endpoint)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VpcID provides a mock function with given fields: clusterID
func (_m *ClientSet) VpcID(clusterID string) (string, error) {
	ret := _m.Called(clusterID)

	var r0 string
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(clusterID)
	} else {
		r0 = ret.Get(0).(string)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(clusterID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}
