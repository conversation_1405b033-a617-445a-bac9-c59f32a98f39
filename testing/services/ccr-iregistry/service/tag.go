// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/service (interfaces: TagInterface)

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	tag "github.com/goharbor/harbor/src/pkg/tag/model/tag"
	gomock "github.com/golang/mock/gomock"
	models "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/models"
)

// MockTagInterface is a mock of TagInterface interface.
type MockTagInterface struct {
	ctrl     *gomock.Controller
	recorder *MockTagInterfaceMockRecorder
}

// MockTagInterfaceMockRecorder is the mock recorder for MockTagInterface.
type MockTagInterfaceMockRecorder struct {
	mock *MockTagInterface
}

// NewMockTagInterface creates a new mock instance.
func NewMockTagInterface(ctrl *gomock.Controller) *MockTagInterface {
	mock := &MockTagInterface{ctrl: ctrl}
	mock.recorder = &MockTagInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTagInterface) EXPECT() *MockTagInterfaceMockRecorder {
	return m.recorder
}

// CountTags mocks base method.
func (m *MockTagInterface) CountTags(arg0 context.Context, arg1 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountTags", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountTags indicates an expected call of CountTags.
func (mr *MockTagInterfaceMockRecorder) CountTags(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountTags", reflect.TypeOf((*MockTagInterface)(nil).CountTags), arg0, arg1)
}

// GetTagByProjectNameAndRepoNameAndTagName mocks base method.
func (m *MockTagInterface) GetTagByProjectNameAndRepoNameAndTagName(arg0 context.Context, arg1 *models.GetTagByProjectNameAndRepoNameAndTagNameParam) (*tag.Tag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTagByProjectNameAndRepoNameAndTagName", arg0, arg1)
	ret0, _ := ret[0].(*tag.Tag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTagByProjectNameAndRepoNameAndTagName indicates an expected call of GetTagByProjectNameAndRepoNameAndTagName.
func (mr *MockTagInterfaceMockRecorder) GetTagByProjectNameAndRepoNameAndTagName(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTagByProjectNameAndRepoNameAndTagName", reflect.TypeOf((*MockTagInterface)(nil).GetTagByProjectNameAndRepoNameAndTagName), arg0, arg1)
}

// ListTags mocks base method.
func (m *MockTagInterface) ListTags(arg0 context.Context, arg1, arg2, arg3 string, arg4, arg5 int64) (int64, []*tag.Tag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTags", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]*tag.Tag)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListTags indicates an expected call of ListTags.
func (mr *MockTagInterfaceMockRecorder) ListTags(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTags", reflect.TypeOf((*MockTagInterface)(nil).ListTags), arg0, arg1, arg2, arg3, arg4, arg5)
}
