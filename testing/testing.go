package testing

//go:generate mockgen -destination=./pkg/bcesdk/uic/client.go -package=uic icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/uic Interface
//go:generate mockgen -destination=./pkg/bcesdk/ccr/personal/client.go -package=personal icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr/personal Interface
//go:generate mockgen -destination=./pkg/harbor/src/core/api/mock_storage.go -package=api icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/core/api Storage
//go:generate mockgen -destination=./pkg/harbor/src/pkg/chart/dao/mock_client.go -package=mock icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/dao Dao
//go:generate mockgen -destination=./pkg/models/mock/mock_client.go -package=mock icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models Interface
//go:generate mockgen -destination=./services/ccr-iregistry/service/tag.go -package=service icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/service TagInterface
//go:generate mockgen -destination=./services/ccr-service/clientset/mock_client.go -package=clientset icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset ClientSetInterface
//go:generate mockgen -destination=./pkg/service/harbor/registry/registry_type.go -package registry icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/registry RegistryTypeServiceInterface
//go:generate mockgen -destination=./pkg/service/harbor/registry/registry.go -package registry icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/registry RegistryServiceInterface
//go:generate mockgen -destination=./pkg/service/harbor/repository/repository.go -package repository icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/repository Interface
//go:generate mockgen -destination=./services/ccr-stack-service/clientset/mock_client.go -package=clientset icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset ClientSetInterface
//go:generate mockgen -destination=./services/harbor-addon/auth/authorizer.go -package auth icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/auth Interface
//go:generate mockgen -destination=./pkg/service/harbor/immutable/immutable.go -package immutable icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/immutable Interface
//go:generate mockgen -destination=./pkg/service/repository.go -package service icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service RepositoryServiceInterface
//go:generate mockgen -destination=./pkg/bcesdk/logictag/client.go -package=uic icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag Client
//go:generate mockgen -destination=./pkg/bcesdk/resourcegroup/client.go -package=uic icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/resourcegroup Client
//go:generate mockgen -destination=./pkg/bcesdk/ccr/mock/client.go -package=ccr icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr ClientInterface
//go:generate mockgen -destination=./pkg/service/mock_instance.go -package service icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service InstanceServiceInterface
//go:generate mockgen -destination=./pkg/bcesdk/iam/mock/client.go -package=iam icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam ClientInterface
//go:generate mockgen -destination=./services/ccr-auth/clientset/client.go -package=clientset icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/clientset ClientSetInterface
//go:generate mockgen --destination=./pkg/harbor/src/pkg/task/dao/mock_dao.go --package=dao icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/task/dao Dao
//go:generate mockgen -destination=./pkg/service/domain.go -package service icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service DomainServiceInterface

//go:generate mockgen -destination=./pkg/service/harbor/gc/gc.go -package gc icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/gc GCServiceInterface
//go:generate mockgen -destination=./pkg/bcesdk/bosinterface/mock/client.go -package=bosinterface icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bosinterface BosInterface
//go:generate mockgen -destination=./services/resource-controller/clientset/mock/client.go -package=clientset icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset ClientSetInterface

//go:generate mockery  --output ./pkg/bcesdk/billing --dir ../pkg/bcesdk/billing --outpkg billing --structname MockAutoRenewClient --name AutoRenewClientInterface --filename auto_renew_client_mock.go
//go:generate mockery  --output ./pkg/bcesdk/billing --dir ../pkg/bcesdk/billing --outpkg billing --structname FacadeClient --name FacadeClientInterface --filename facade_client.go
//go:generate mockery  --output ./pkg/bcesdk/billing --dir ../pkg/bcesdk/billing --outpkg billing --structname OrderClient --name OrderClientInterface --filename order_client.go
//go:generate mockery  --output ./services/ccr-service/clientset --dir ../services/ccr-service/clientset --outpkg clientset --structname ClientSet --name ClientSetInterface --filename client.go
//go:generate mockery  --output ./pkg/service --dir ../pkg/service --outpkg service --structname InstanceService --name InstanceServiceInterface --filename instance.go
//go:generate mockery  --output ./services/ccr-controller/ccr --dir ../services/ccr-controller/ccr --outpkg ccr --structname CCRHandler --name CCRHandlerInterface --filename handler.go
//go:generate mockery  --output ./pkg/bcesdk/billing --dir ../pkg/bcesdk/billing --outpkg billing --structname ResourceClient --name ResourceClientInterface --filename resource_client.go
//go:generate mockery  --output ./pkg/service --dir ../pkg/service --outpkg service --structname OrderService --name OrderServiceInterface --filename order.go
//go:generate mockery  --output ./pkg/service --dir ../pkg/service --outpkg service --structname ResourceService --name ResourceServiceInterface --filename resource.go
//go:generate mockery  --output ./pkg/bcesdk/iam --dir ../pkg/bcesdk/iam --outpkg iam --structname Client --name ClientInterface --filename client.go
//go:generate mockery  --output ./pkg/listers --dir ../pkg/listers --outpkg listers --structname MockLister --name ListerInterface --filename lister.go
//go:generate mockery  --output ./pkg/service/harbor/project --dir ../pkg/service/harbor/project --outpkg project --structname MockProjectService --name ProjectServiceInterface --filename project.go
//go:generate mockery  --output ./pkg/bcesdk/usersetting --dir ../pkg/bcesdk/usersetting --outpkg usersetting --structname MockClient --name ClientInterface --filename client.go
//go:generate mockery  --output ./pkg/bcesdk/billing --dir ../pkg/bcesdk/billing --outpkg billing --structname FacadeClient --name FacadeClientInterface --filename facade_client.go
//go:generate mockery  --output ./services/resource-controller/clientset --dir ../services/resource-controller/clientset --outpkg clientset --structname ClientSet --name ClientSetInterface --filename client.go
//go:generate mockery  --output ./pkg/bcesdk/sts --dir ../pkg/bcesdk/sts --outpkg sts --structname Client --name ClientInterface --filename client.go
//go:generate mockery  --output ./services/resource-controller/conf --dir ../services/resource-controller/conf --outpkg conf --structname Config --name ConfigInterface --filename config.go
//go:generate mockery  --output ./pkg/bcesdk/vpc --dir ../pkg/bcesdk/vpc --outpkg vpc --structname Client --name ClientInterface --filename client.go
//go:generate mockery  --output ./services/ccr-stack-service/clientset --dir ../services/ccr-stack-service/clientset --outpkg clientset --structname ClientSet --name ClientSetInterface --filename client.go
//go:generate mockery  --output ./pkg/bcesdk/ccr --dir ../pkg/bcesdk/ccr --outpkg ccr --structname Client --name ClientInterface --filename client.go
//go:generate mockery  --output ./pkg/bcesdk/certificate --dir ../pkg/bcesdk/certificate --outpkg certificate --structname CertClient --name Interface --filename client.go
//go:generate mockery  --output ./pkg/bcesdk/bcd --dir ../pkg/bcesdk/bcd --outpkg bcd --structname BcdClient --name Interface --filename client.go
//go:generate mockery  --output ./pkg/bcesdk/privatezone --dir ../pkg/bcesdk/privatezone --outpkg privatezone --structname PrivateZoneClient --name Interface --filename client.go
//go:generate mockery  --output ./pkg/kafka --dir ../pkg/kafka --outpkg kafka --structname KafkaClient --name ProducerInterface --filename kafka_client.go
