#!/bin/bash

SHELL_FOLDER=$(cd "$(dirname "$0")"; pwd)

# 镜像地址
image=${image:-"api-logic-mse:dev"}
# 镜像仓库地址
registry_url=${registry_url:-"registry.baidubce.com/cce-plugin-dev"}

CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o csm cmd/csm/main.go
#if [ "${image}" == "api-logic-mse:dev" ]; then
#  cp conf/mse_local.yaml csm_local.yaml
#else
#  cp conf/csm_local_eks.yaml csm_local.yaml
#fi

docker build -t "${image}" .
docker tag "${image}" "${registry_url}"/"${image}"
docker push "${registry_url}"/"${image}"

rm -rf csm
rm -rf csm_local.yaml
