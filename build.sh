#!/bin/bash
set -eu
set -o pipefail

function set_go_env() {
    # GO ENV
    go env -w GO111MODULE=on
    go env -w GONOPROXY=\*\*.baidu.com\*\*
    go env -w GONOSUMDB=\*

    unset GOPROXY
    go env -w GOPROXY=https://goproxy.baidu-int.com

    go mod tidy
}

function package_deploy() {
    for name in ${!serviceMap[*]}
    do
        path=${serviceMap[${name}]}
        bin_path="${path}/${name}"
        mkdir -p output/${name}
        cp ./infra/docker/${name}/Dockerfile output/${name}/

        if [ ${name} == "harbor-core" ]; then
          cp -rf ./infra/migrations/harbor/ output/${name}/migrations/
        fi

        build ${path} ${name}
    done

    echo "==========Package begin: chart files============"
    mkdir -p output/infra
    cp -r infra/charts output/infra/
    echo "==========Package success: chart files======="
}

function local_package_deploy() {
    for name in $@
    do
        if [ -z "${serviceMap[${name}]}" ]; then
            continue
        fi
        path=${serviceMap[${name}]}
        bin_path="${path}/${name}"

        mkdir -p output/${name}
        cp ./infra/docker/${name}/Dockerfile output/${name}/

        if [ ${name} == "harbor-core" ]; then
          cp -rf ./infra/migrations/harbor/ output/${name}/migrations/
        fi

        build ${path} ${name}
    done
}

function build() {
    echo "==========Package begin: name=${name} path=${path} bin_path=${bin_path}============"
    # 编译二进制
    if [ ${name} = "harbor-registry" ]; then
        cp ${path}/${name} output/${name}/
    else
        if [ ${name} = "ccr-controller" ]; then
            cp -r infra/charts/ccr output/${name}
        fi
        CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -ldflags "-X 'main.version=${git_commit_log}'" -o ${path}/${name} ${path}/
        if [[ $? -ne 0 ]]; then
            echo "[----------'GO build failed!----------]"
            echo "Command: CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags "-X 'main.version=${git_commit_log}'" -o ${path}/${name} ${path}/"
            exit -1
        fi
        mv $1/$2 output/$2/$2
    fi
    echo "==========Package success: name=${name}======="
}

    set_go_env
    git_commit_log=$(git log --pretty='format:%h' -n 1)

    declare -A serviceMap
    # 组件列表，key 是名称，用于镜像名，value 是包路径，用于 go build
    serviceMap["ccr-auth"]="./services/ccr-auth"
    serviceMap["ccr-controller"]="./services/ccr-controller"
    serviceMap["ccr-service"]="./services/ccr-service"
    serviceMap["ccr-iregistry"]="./services/ccr-iregistry"
    serviceMap["harbor-addon"]="./services/harbor-addon"
    serviceMap["ccr-registryctl"]="./services/ccr-registryctl"
    serviceMap["resource-controller"]="./services/resource-controller"
    serviceMap["harbor-core"]="./services/harbor-core"
    serviceMap["harbor-jobservice"]="./services/harbor-jobservice"
    serviceMap["harbor-registry"]="./infra/bin"
    serviceMap["acceleration-service"]="./services/acceleration-service"
    serviceMap["ccr-stack-service"]="./services/ccr-stack-service"
    serviceMap["ccr-event-keeper"]="./services/ccr-event-keeper"

    rm -rf output
    mkdir output

    if [ -z "$*" ]; then
        package_deploy
    else
        local_package_deploy $@
    fi
