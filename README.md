# 项目名称

## 设计相关文档

具体可参考如流文档记录：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/jNp_DTmrGi/egux6WbNt93lFD

## 构建测试环境

cce 沙盒环境：
```bash
bash build_local.sh
```

eks 测试环境：
```bash
image=api-logic-csm-eks:dev bash build_local.sh
```

## 监控配置

https://console.cloud.baidu-int.com/devops/icode/repos/baidu/cprom/grafana-provisioning/tree/master/dashboards/CSM

## 本地执行单测

按照文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/jNp_DTmrGi/MYxoMm6l4Ia5-H 配置好环境后，
本地执行以下命令即可本地执行单元测试。

```bash
GOARCH=amd64 make test | grep FAIl 
```

```bash
# 某个函数
GOARCH=amd64 go test --cover  -gcflags="-N -l" -v -run  TestCreateBlbService   ./

# 某个目录
GOARCH=amd64 go test --cover  -gcflags="-N -l" -v  ./

GOARCH=amd64 go test --cover  -gcflags="-N -l" .
```

## mock 接口

进入到某个 interface 目录下，如 `/pkg/service/diagnosis`。

mockgen --source client.go -package mock --destination ./mock/client_mocks.go


## 格式化代码

```bash
# e.g 配置golang的二进制版本路径
$ export GO_1_19_BIN=/usr/local/go/bin 
$ make format
```

## golangci-lint 检查

参考文档配置 https://cloud.baidu-int.com/icloud/iCode/%E7%99%BE%E5%BA%A6%E7%BC%96%E7%A0%81%E8%A7%84%E8%8C%83/Go%20%E7%BC%96%E7%A0%81%E8%A7%84%E8%8C%83%E6%A3%80%E6%9F%A5

## 项目结构(理想)
```
api-logic-csm  
├── cmd/  
│   └── csm/  
│       └── router/  
│           └── router.go  
│       └── handler/  
│       └── main.go  
├── internal/  
│   └── csm/  
│       └── web/    ->web 请求的结构化参数  
│           └── blb.go  
│       └── biz/    ->主要业务逻辑，提供事务，不允许同层调用  
│           └── blb/  
│               └── mocks/  
│               └── interface.go  
│               └── service.go  
│       └── dao/     ->数据库操作  
│           └── blb/  
│   └── pkg/  
│       └── request/  
│       └── db/  
│       └── log/  
└── pkg/  
│   ├── api/  
│   │   ├── v1/cnap/  
│   └── util/  
├── go.mod  
├── go.sum  
```

## 参考

[百度golang代码库组织和引用指南](http://wiki.baidu.com/pages/viewpage.action?pageId=515622823)
[百度内Go Module使用指南](http://wiki.baidu.com/pages/viewpage.action?pageId=917601678)
- [golang standards](https://github.com/golang-standards/project-layout/blob/master/README_zh.md)
- [Go 面向包的设计和架构分层](https://github.com/danceyoung/paper-code/blob/master/package-oriented-design/packageorienteddesign.md)
- [Go 项目目录结构](https://blog.csdn.net/wohu1104/article/details/123209272)

因此，csm 项目目录需要迭代中逐渐规范起来，一个典型的应用项目结构应该是这样的：
```
paper-code/examples/groupevent  
├── cmd/  
│   └── eventtimer/  
│       └── update/  
│       └── main.go  
│   └── eventserver/  
│       └── router/  
│           └── handler/  
│           └── router.go  
│       └── tests/  
│       └── main.go  
├── internal/  
│   └── eventserver/  
│       └── biz/  
│           └── event/  
│           └── member/  
│       └── data/  
│           └── service/  
│   └── eventpopdserver/  
│       └── event/  
│       └── member/  
│   └── pkg/  
│       └── cfg/  
│       └── db/  
│       └── log/  
└── vendor/  
│   ├── github.com/  
│   │   ├── ardanlabs/  
│   │   ├── golang/  
│   │   ├── prometheus/  
│   └── golang.org/  
├── go.mod  
├── go.sum  
```

一个完整的 web 请求应该包含如下几个步骤：
1. 认证鉴权（中间件）
2. 设置租户信息（中间件）
3. 通用预处理逻辑（中间件，如设置 Region) 
4. 转换数据（Bind）
5. 填充默认数据 
6. 校验参数 
7. 提取必要参数 
8. 业务相关数据校验
9. 逻辑模型转换
10. 具体业务逻辑
