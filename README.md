# 项目名称
简要说明

# 目录结构

CCE mono-repo

* DOC/ - 文档;
* CHANGELOG/ - ChangeLog 发布描述;
* pkg/ - Shared codes, or libraries common across the repo;
* services/ - Basically, long running services;
* infra/ - 包含 CCR 依赖, MetaCluster, 服务 Helm Chart, 自动化回归的完整描述.

## 快速开始
如何构建、安装、运行

### 生成harbor client
本地开发运行ccr-service ,需要先通过swagger生成harbor client代码

```shell
make swagger-generate
```

### 生成 harbor mock test file
```shell
make mock-generate
```
如果本机执行遇到 scripts/gen-mock.sh:9: bad substitution，需要更新mac bash

可以参看文章 [升级bash](https://hybriddbablog.com/2021/01/25/bash-bad-substitution-upgrade-your-bash-version/)

```shell
$ bash --version
GNU bash, version 3.2.57(1)-release (x86_64-apple-darwin20)
Copyright (C) 2007 Free Software Foundation, Inc.
```
**How to upgrade Bash on macOS.**

For macOS you should use homebrew to install things:

```shell
$ brew install bash
```

To verify the installation, you can check that you now have two versions of Bash on your system:

```bash
$ which -a bash
/opt/homebrew/bin/bash
/bin/bash
```

### ccr-service swagger json 文件生成
#### 安装 go-swagger，生成文档
```shell
go get github.com/go-swagger/go-swagger
```
_`如果遇到网络不通，可以手动下载后，编译放到path下`_

在项目根目录(ccr-service)执行以下命令，使用swag工具生成接口文档数据。
```shell
swag init --parseDependency main.go
```
执行完上述命令后，如果你写的注释格式没问题，此时你的项目根目录下会多出一个docs文件夹。
```shell
./docs
├── docs.go
├── swagger.json
└── swagger.yaml
```
#### 引入gin-swagger渲染文档数据
```go
import (
	
	_ "xxxxxx.baidu.com/docs" 

	gs "github.com/swaggo/gin-swagger"
	"github.com/swaggo/gin-swagger/swaggerFiles"

	"github.com/gin-gonic/gin"
)
```
注册swagger api相关路由
```go
r.GET("/swagger/*any", gs.WrapHandler(swaggerFiles.Handler))
```
项目程序运行起来，打开浏览器访问http://xxx.xxx.xxx.xxx:xxxx/swagger/index.html

### 构建镜像docker-builder 编译
```shell
docker build -t iregistry.baidu-int.com/docker/docker-golang:20.10.8
.1.16.8 -f infra/docker/docker-builder/Dockerfile
```

## 测试
如何执行自动化测试

## 如何贡献
贡献patch流程、质量要求

## 讨论
百度Golang交流群：1450752

## 链接
[百度golang代码库组织和引用指南](http://wiki.baidu.com/pages/viewpage.action?pageId=515622823)
[百度内Go Module使用指南](http://wiki.baidu.com/pages/viewpage.action?pageId=917601678)


[]: https://hybriddbablog.com/2021/01/25/bash-bad-substitution-upgrade-your-bash-version/