Global:
    version: 2.0

Default:
    profile : [build]

Profiles:
    - profile:
      name : build
      mode: AGENT
      environment:
        cluster: DECK_CENTOS6U3_K3
        tools:                   # <------ 配置软件版本信息
          - go: 1.18             # <------ 配置软件名称及其对应版本，支持指定具体版本（例如1.17、1.18等）or 1.19.latest（表示使用当前构建系统支持的对应版本软件的最新小版本）
      build:
        command: bash build.sh
      artifacts:
        release: true

    - profile:
      name: gcover                   # 插桩编译任务
      mode: AGENT
      environment:
        cluster: DECK_CENTOS6U3_K3
        tools:
          - go: 1.18
      build:
        gcover:                      # 开启打桩编译
          enable: true
        command: bash build.sh
      artifacts:
        release: true

    - profile:
      name: trivy_onfly_build
      mode: AGENT
      environment:
        image: DECK_CENTOS6U3_K3
        resourceType: SMALL
        tools:
          - go: 1.18
      build:
        command: bash infra/docker/trivy-onfly/build.sh

    - profile:                          # TODO 使用自定义镜像编译
      name: image_register
      mode: IMAGE_REGISTER              # 针对镜像注册，模式必须是IMAGE_REGISTER
      group_mail: <EMAIL>   # 邮件组
      images:                           # 镜像列表，每更新一个版本，均需要注册
        - image: iregistry.baidu-int.com/docker/deck_centos6u3_k3_docker20:v1.0.0.17