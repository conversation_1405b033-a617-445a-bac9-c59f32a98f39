apiVersion: v1
kind: ServiceAccount
metadata:
  name: istio-reader-service-account
  namespace: istio-system-csm-123456
  labels:
    app: istio-reader
    release: istio
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: istio-reader-clusterrole-istio-system-csm-123456
  labels:
    app: istio-reader
    release: istio
rules:
  - apiGroups:
      - "config.istio.io"
      - "security.istio.io"
      - "networking.istio.io"
      - "authentication.istio.io"
      - "rbac.istio.io"
    resources: ["*"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["endpoints", "pods", "services", "nodes", "replicationcontrollers", "namespaces", "secrets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["networking.istio.io"]
    verbs: [ "get", "watch", "list" ]
    resources: [ "workloadentries" ]
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["discovery.k8s.io"]
    resources: ["endpointslices"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceexports"]
    verbs: ["get", "list", "watch", "create", "delete"]
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceimports"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["authentication.k8s.io"]
    resources: ["tokenreviews"]
    verbs: ["create"]
  - apiGroups: ["authorization.k8s.io"]
    resources: ["subjectaccessreviews"]
    verbs: ["create"]
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["create", "get", "list", "watch", "update"]
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["mutatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update", "patch"]
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["validatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: istio-reader-clusterrole-istio-system-csm-123456
  labels:
    app: istio-reader
    release: istio
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: istio-reader-clusterrole-istio-system-csm-123456
subjects:
  - kind: ServiceAccount
    name: istio-reader-service-account
    namespace: istio-system-csm-123456
---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: istio-sidecar-injector-istio-system-csm-123456
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    app: sidecar-injector
    release: istio
webhooks:
  - name: rev.namespace.sidecar-injector.istio.io
    clientConfig:
      url: "https://1.1.1.1:443/inject/:ENV:cluster=test:ENV:net=bj"
      caBundle: ""
    sideEffects: None
    rules:
      - operations: [ "CREATE" ]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["pods"]
    failurePolicy: Fail
    admissionReviewVersions: ["v1beta1", "v1"]
    namespaceSelector:
      matchExpressions:
        - key: istio.io/rev
          operator: In
          values:
            - "default"
        - key: istio-injection
          operator: DoesNotExist
    objectSelector:
      matchExpressions:
        - key: sidecar.istio.io/inject
          operator: NotIn
          values:
            - "false"
  - name: rev.object.sidecar-injector.istio.io
    clientConfig:
      url: "https://1.1.1.1:443/inject/:ENV:cluster=test:ENV:net=bj"
      caBundle: ""
    sideEffects: None
    rules:
      - operations: [ "CREATE" ]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["pods"]
    failurePolicy: Fail
    admissionReviewVersions: ["v1beta1", "v1"]
    namespaceSelector:
      matchExpressions:
        - key: istio.io/rev
          operator: DoesNotExist
        - key: istio-injection
          operator: DoesNotExist
    objectSelector:
      matchExpressions:
        - key: sidecar.istio.io/inject
          operator: NotIn
          values:
            - "false"
        - key: istio.io/rev
          operator: In
          values:
            - "default"
  - name: namespace.sidecar-injector.istio.io
    clientConfig:
      url: "https://1.1.1.1:443/inject/:ENV:cluster=test:ENV:net=bj"
      caBundle: ""
    sideEffects: None
    rules:
      - operations: [ "CREATE" ]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["pods"]
    failurePolicy: Fail
    admissionReviewVersions: ["v1beta1", "v1"]
    namespaceSelector:
      matchExpressions:
        - key: istio-injection
          operator: In
          values:
            - enabled
    objectSelector:
      matchExpressions:
        - key: sidecar.istio.io/inject
          operator: NotIn
          values:
            - "false"
  - name: object.sidecar-injector.istio.io
    clientConfig:
      url: "https://1.1.1.1:443/inject/:ENV:cluster=test:ENV:net=bj"
      caBundle: ""
    sideEffects: None
    rules:
      - operations: [ "CREATE" ]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["pods"]
    failurePolicy: Fail
    admissionReviewVersions: ["v1beta1", "v1"]
    namespaceSelector:
      matchExpressions:
        - key: istio-injection
          operator: DoesNotExist
        - key: istio.io/rev
          operator: DoesNotExist
    objectSelector:
      matchExpressions:
        - key: sidecar.istio.io/inject
          operator: In
          values:
            - "true"
        - key: istio.io/rev
          operator: DoesNotExist
---