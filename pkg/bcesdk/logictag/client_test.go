package logictag

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// ClientTestSuite tests the tag client
type ClientTestSuite struct {
	suite.Suite

	testServer *httptest.Server
	client     Client
}

// TestClient is the entry of ClientTestSuite
func TestClient(t *testing.T) {
	suite.Run(t, new(ClientTestSuite))
}

// SetupSuite prepares the test suite env
func (suite *ClientTestSuite) SetupSuite() {
	suite.testServer = httptest.NewServer(&MockHandler{})

	c, err := NewClient("xxx", "xxx", suite.testServer.URL)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), c)

	suite.client = c
}

func (suite *ClientTestSuite) TestCreateTags() {
	err := suite.client.CreateTags("", &TagsArgs{
		Tags: []Tag{
			{
				TagKey:   "key",
				TagValue: "value",
			},
		},
	})
	require.NoError(suite.T(), err)
}

func (suite *ClientTestSuite) TestClientCheckTags() {
	err := suite.client.CheckTags("", &TagsArgs{
		Tags: []Tag{
			{
				TagKey:   "key",
				TagValue: "value",
			},
		},
	})
	require.NoError(suite.T(), err)
}

func (suite *ClientTestSuite) TestClientDeleteTags() {
	err := suite.client.DeleteTags("", &TagsArgs{
		Tags: []Tag{
			{
				TagKey:   "key",
				TagValue: "value",
			},
		},
	})
	require.NoError(suite.T(), err)
}

func (suite *ClientTestSuite) TestClientDeleteTagAssociation() {
	err := suite.client.DeleteTagAssociation("", &DeleteTagAssociationsArgs{
		Resource: Resource{
			ServiceType:  "resourceType",
			Region:       "bj",
			ResourceUUID: "",
		},
	})
	require.NoError(suite.T(), err)
}

func (suite *ClientTestSuite) TestClientCreateAndAssignTag() {
	err := suite.client.CreateAndAssignTag("", &CreateAndAssignArgs{
		Resources: []ResourceWithTag{
			{
				ServiceType:  "resourceType",
				Region:       "bj",
				ResourceUUID: "",
			},
		},
	})
	require.NoError(suite.T(), err)
}

func (suite *ClientTestSuite) TestClientDeltaAssignTags() {
	err := suite.client.DeltaAssignTags("", &DeltaAssignTagsArgs{
		InsertTags: []Tag{
			{
				TagKey:   "key",
				TagValue: "value",
			},
		},
		Resources: []Resource{
			{
				ServiceType:  "resourceType",
				Region:       "bj",
				ResourceUUID: "",
			},
		},
	})
	require.NoError(suite.T(), err)
}

//func (suite *ClientTestSuite) TestClientListTags() {
//	_, err := suite.client.ListTags("", true, &ListTagsArgs{
//		TagKey:        "key",
//		TagValue:      "value",
//		ServiceTypes:  []string{"resourceType"},
//		Regions:       []string{"bj"},
//		ResourceUuids: []string{""},
//		ResourceIds:   []string{""},
//	})
//	fmt.Printf("err: %v \n", err)
//
//	require.NoError(suite.T(), err)
//	//require.Equal(suite.T(), 200, resp.TagAssociationFulls[0])
//}
//
//func (suite *ClientTestSuite) TestClientListTagsV2() {
//	_, err := suite.client.ListTagsV2("", true, &ListTagsArgs{
//		TagKey:        "key",
//		TagValue:      "value",
//		ServiceTypes:  []string{"resourceType"},
//		Regions:       []string{"bj"},
//		ResourceUuids: []string{""},
//		ResourceIds:   []string{""},
//	})
//
//	require.NoError(suite.T(), err)
//	//require.Equal(suite.T(), 200, resp.TagAssociationFulls[0])
//}

// TearDownSuite clears the test suite env
func (suite *ClientTestSuite) TearDownSuite() {
	suite.testServer.Close()
}

type MockHandler struct{}

// ServeHTTP ...
func (mh *MockHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	fmt.Printf("r: %#v \n", r.URL.Path)
	switch r.Method {

	case http.MethodDelete:

		if r.URL.Path != "/api/logical/tag/v1" {
			w.WriteHeader(http.StatusNotFound)
			return
		}
		w.WriteHeader(http.StatusOK)
		break
	case http.MethodPut:
		if r.URL.Path != "/api/logical/tag/v1" {
			w.WriteHeader(http.StatusNotFound)
			return
		}
		w.WriteHeader(http.StatusOK)
		break
	case http.MethodPost:
		if r.URL.Path != "/api/logical/tag/v1" {
			w.WriteHeader(http.StatusNotFound)
			return
		}
		w.WriteHeader(http.StatusOK)
		break
	}
}
