package billing

import (
	"fmt"
	"net/http"

	"github.com/baidubce/bce-sdk-go/bce"
)

type OrderClientInterface interface {
	UpdateOrder(requestId string, orderId string, args *UpdateOrderRequest) error
	GetOrderDetail(requestId string, orderId string) (*GetOrderDetail, error)
}

type OrderClient struct {
	bce.Client
}

func NewOrderClient(ak, sk, endpoint string) (*OrderClient, error) {
	cli, err := bce.NewBceClientWithAkSk(ak, sk, endpoint)
	if err != nil {
		return nil, err
	}
	return &OrderClient{Client: cli}, nil
}

func (c *OrderClient) UpdateOrder(requestId string, orderId string, args *UpdateOrderRequest) error {

	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodPut).
		WithBody(args).
		WithURL(fmt.Sprintf("/orders/%s", orderId)).
		WithHeader("Content-Type", "application/json;charset=utf-8").
		WithHeader("X-Bce-Request-Id", requestId).
		Do()

	return err
}

func (c *OrderClient) GetOrderDetail(requestId string, orderId string) (*GetOrderDetail, error) {
	resp := &GetOrderDetail{}

	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodGet).
		WithURL(fmt.Sprintf("/orders/%s", orderId)).
		WithHeader("Content-Type", "application/json;charset=utf-8").
		WithHeader("X-Bce-Request-Id", requestId).
		WithResult(resp).
		Do()

	return resp, err
}
