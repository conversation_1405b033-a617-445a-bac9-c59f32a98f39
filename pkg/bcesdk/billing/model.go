package billing

import "time"

const (
	OrderTypeNEW          string = "NEW"
	OrderTypeRENEW        string = "RENEW"
	OrderTypeREDILATATION string = "DILATATION"
)

type OrderFacadeRequest struct {
	Region    string        `json:"region"`
	OrderType string        `json:"orderType"`
	Items     []interface{} `json:"items"`
}

type AbstractOrderItem struct {
	ServiceType   string              `json:"serviceType"`
	ProductType   string              `json:"productType"`
	PaymentMethod []PaymentMethodItem `json:"paymentMethod"`
}

type NewOrderItem struct {
	AbstractOrderItem `json:",inline"`
	Key               string       `json:"key"`
	Count             int          `json:"count"`
	Flavor            []FlavorItem `json:"flavor"`
	Extra             string       `json:"extra"`

	Time     int    `json:"time"`
	TimeUnit string `json:"timeUnit"` // 默认为MONTH. 可选值(MILLISECOND/SECOND/MINUTE/HOUR/DAY/WEEK/MONTH/YEAR)
}

type RenewOrderItem struct {
	AbstractOrderItem
	ResourceUuid string `json:"resourceUuid"`
	Time         int    `json:"time"`
	TimeUnit     string `json:"timeUnit"` // 默认为MONTH. 可选值(MILLISECOND/SECOND/MINUTE/HOUR/DAY/WEEK/MONTH/YEAR)}
}

type ResizeOrderItem struct {
	AbstractOrderItem
	Flavor       []FlavorItem `json:"flavor"`
	Extra        string       `json:"extra"`
	ResourceUuid string       `json:"resourceUuid"`
	ResizeType   int          `json:"resizeType"`
}

type FlavorItem struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Scale int    `json:"scale"`
}

type PaymentMethodItem struct {
	Type   string   `json:"type"`
	Values []string `json:"values"`
}

type OrderFacadeResponse struct {
	OrderID string `json:"orderId"`
}

type CreateAutoRenewRule struct {
	// 用户ID
	AccountId string `json:"accountId"`
	// 地域，当前支持 bj,gz,hk,hk02
	Region string `json:"region"`
	// 服务类型，当前支持 BCC,CDS,EIP
	ServiceType string `json:"serviceType"`
	// 资源id列表
	ServiceIds []string `json:"serviceIds"`
	// 每次的续费周期，相当于续费单位，支持month，year
	RenewTimeUnit string `json:"renewTimeUnit"`
	// 每次续费时长
	RenewTime int `json:"renewTime"`
}

type ListAutoRenewRule struct {
	// 数据起始位置,缺省值为0
	Begin int `json:"begin"`
	// 获取结果数量限制，缺省值为1000
	Limit int `json:"limit"`
	// 服务类型，当前支持 BCC,CDS,EIP
	ServiceType string `json:"serviceType"`
	// 资源ID，支持模糊查询
	Name string `json:"name"`
	// 用户ID
	AccountId string `json:"accountId"`
	// 地域，当前支持 bj,gz,hk,hk02
	Region string `json:"region"`
	// 自动续费规则的ID，支持模糊查询
	Uuid string `json:"uuid"`
}

type ListAutoRenewRuleResponse struct {
	// 自动续费规则的ID，支持模糊查询
	Uuid string `json:"uuid"`
	// 服务类型，当前支持 BCC,CDS,EIP
	ServiceType string `json:"serviceType"`
	// 用户ID
	AccountId string `json:"accountId"`
	// 资源id列表
	ServiceId string `json:"serviceId"`
	// 地域，当前支持 bj,gz,hk,hk02
	Region string `json:"region"`
	// 每次的续费周期，相当于续费单位，支持month，year
	RenewTimeUnit string `json:"renewTimeUnit"`
	// 每次续费时长
	RenewTime int `json:"renewTime"`

	CreateTime time.Time `json:"createTime"`
	UpdateTime time.Time `json:"updateTime"`
	DeleteTime time.Time `json:"deleteTime"`
}

//type AutoRenewRule struct {
//}

// Resource 每个资源的状态
type Resource struct {
	Key    string `json:"key"`
	ID     string `json:"id"`
	Status string `json:"status"`
}

// UpdateOrderRequest 订单的状态
type UpdateOrderRequest struct {
	Status            string     `json:"status"`
	Resources         []Resource `json:"resources"`
	DisplayFailReason string     `json:"displayFailReason"`
}

type GetResourceDetailRequest struct {
	ServiceType string `json:"serviceType"`
	Region      string `json:"region"`
	Name        string `json:"name"`
	AccountId   string `json:"accountId"`
}

type GetResourceDetailResponse struct {
	AccountId      string    `json:"accountId"`
	ServiceType    string    `json:"serviceType"`
	Region         string    `json:"region"`
	Name           string    `json:"name"`
	ShortId        string    `json:"shortId"`
	Status         string    `json:"status"`
	ProductType    string    `json:"productType"`
	SubProductType string    `json:"subProductType"`
	Uuid           string    `json:"uuid"`
	CreateTime     time.Time `json:"createTime"`
	StartTime      time.Time `json:"startTime"`
	ExpireTime     time.Time `json:"expireTime"`
	Flavor         string    `json:"flavor"`
	Extra          string    `json:"extra"`
	Reason         string    `json:"reason"`
	OrderId        string    `json:"orderId"`
	TaskStatus     string    `json:"taskStatus"`
	SpecificType   int       `json:"specificType"`
}

// ========= order detail begin =========

type GetOrderDetail struct {
	Uuid         string      `json:"uuid"` // 订单的唯一标识, 无连字符的32字符UUID
	Type         string      `json:"type"` // 订单类型,  目前包括新购(NEW)、续费(RENEW)、改变计费策略(SHIFT_CHARGE)、预付费升配(DILATATION)、预付费降配(SHRINKAGE)、后付费升降配(RESIZE)、退款(REFUND)、域名转入(TRANSFER_IN)
	AccountId    string      `json:"accountId"`
	UserId       string      `json:"userId"`
	ServiceType  string      `json:"serviceType"`  // 订单类型。涉及单一服务的订单为其服务缩写，设计多个服务的则为通过双下划线(__)连接多个服务名称，顺序是单词字母表顺序。
	ProductType  string      `json:"productType"`  // 预付费(prepay)、后付费(postpay)、或者订单同时存在预付费和后付费项(postpay__prepay, 注意分隔符是双下划线)
	Items        []OrderItem `json:"items"`        // 订单项数组, 一个元素代表一个单一产品的子订单
	Status       string      `json:"status"`       // 订单状态。包括 NEED_PURCHASE,CANCELLED, DEFERRED_CREATE, READY_FOR_CREATE,CREATING,CREATED,CREATE_FAILED, EXPIRED,REFUND_SUCC,REFUND_FAILED
	CreateTime   time.Time   `json:"createTime"`   // 订单创建时间
	PurchaseTime time.Time   `json:"purchaseTime"` // 订单付款时间
	UpdateTime   time.Time   `json:"updateTime"`   // 订单更新时间
	ResourceIds  []string    `json:"resourceIds"`  // 订单关联的资源
	BatchId      int         `json:"batchId"`      // 批量导入订单的标识, 一般为空
	Source       string      `json:"source"`       // 订单来源, 空串表示该订单为从console创建的订单
}

type OrderItem struct {
	Count         int           `json:"count"`  // 该订单购买实例的计数
	Extra         string        `json:"extra"`  // order不对该字段进行处理, console可以利用该字段存储自己关心的数据 "name:;adminPass:;securityGroupId:ff0d117d-b310-419a-b940-e1e822c01b90;securityGroupName:默认安全组;"
	Flavor        []*Flavor     `json:"flavor"` // 配置项
	Key           string        `json:"key"`    // 订单项的key, 用于区别订单的子订单项
	PaymentMethod PaymentMethod `json:"paymentMethod"`
	ProductType   string        `json:"productType"`
	Region        string        `json:"region"`
	ServiceType   string        `json:"serviceType"`
	Time          int           `json:"time"`     // 购买时长
	TimeUnit      string        `json:"timeUnit"` // 购买时长时间单位
}

type Flavor struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Scale int    `json:"scale"`
}

type Coupon struct {
	CouponId string  `json:"couponId"`
	Amount   float64 `json:"amount"`
}

type PaymentMethod struct {
	Coupons []Coupon `json:"coupons"`
}

// ========= order detail end =========

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type Extra struct {
	HistoryOrderImport bool     `yaml:"historyOrderImport"`
	Canary             bool     `yaml:"canary"`
	AutoRenew          bool     `yaml:"autoRenew"`
	AutoRenewTimeUnit  string   `yaml:"autoRenewTimeUnit"`
	AutoRenewTime      int      `yaml:"autoRenewTime"`
	InstanceId         string   `yaml:"instanceId"`
	InstanceName       string   `yaml:"instanceName"`
	InstanceType       string   `yaml:"instanceType"`
	Tags               []Tag    `yaml:"tags"`
	GroupIDs           []string `yaml:"groupIds"`
}

type ListResourceDetailRequest struct {
	AccountId                string   `json:"accountId,omitempty"`
	ServiceType              string   `json:"serviceType,omitempty"` // 资源的产品名称英文缩写, 如"CCR".
	ProductType              string   `json:"productType,omitempty"` // 资源的计费类型, postpay/prepey
	NameOrShortIds           []string `json:"nameOrShortIds,omitempty"`
	Status                   []string `json:"status,omitempty"` // RUNNING, STOPPED, CLEAR, CLEARING, DESTROYING, DESTROYED
	Region                   string   `json:"region,omitempty"`
	ExpireTimeBeforeOrEquals string   `json:"expireTimeBeforeOrEquals,omitempty"` // 资源过期时间在给定时间之前. utc 时区，tz 格式
	ExpireTimeAfterOrEquals  string   `json:"expireTimeAfterOrEquals,omitempty"`  // 资源过期时间在给定时间之前. utc 时区，tz 格式
	PageNo                   int      `json:"pageNo,omitempty"`                   // 分页查询的页码, 从1开始. 最小值为 1
	PageSize                 int      `json:"pageSize,omitempty"`                 // 分页查询的页大小. 最小值为 1 最大值为 200
}
type ListResourceDetailResponse struct {
	TotalCount int             `json:"totalCount"`
	PageNo     int             `json:"pageNo"`
	PageSize   int             `json:"pageSize"`
	Result     []*ResourceInfo `json:"result"`
	OrderBy    string          `json:"orderBy"`
	Order      string          `json:"order"`
}

type ResourceInfo struct {
	AccountId      string    `json:"accountId"`   // 账户 id
	ServiceType    string    `json:"serviceType"` // 产品英文名
	Region         string    `json:"region"`
	Name           string    `json:"name"` // 长 id
	ShortId        string    `json:"shortId"`
	Status         string    `json:"status"` // 资源状态
	ProductType    string    `json:"productType"`
	SubProductType string    `json:"subProductType"`
	Uuid           string    `json:"uuid"`
	CreateTime     time.Time `json:"createTime"`
	StartTime      time.Time `json:"startTime"`
	StopTime       time.Time `json:"stopTime"`
	ExpireTime     time.Time `json:"expireTime"` // 资源到期时间
	DestroyTime    time.Time `json:"destroyTime"`
	ReleaseTime    time.Time `json:"releaseTime"`
	Flavor         string    `json:"flavor"`
	Extra          string    `json:"extra"`
	Reason         string    `json:"reason"`
	OrderId        string    `json:"orderId"`
}
