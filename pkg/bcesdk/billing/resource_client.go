package billing

import (
	"net/http"

	"github.com/baidubce/bce-sdk-go/bce"
)

type ResourceClientInterface interface {
	GetResourceDetail(requestId string, args *GetResourceDetailRequest) (*GetResourceDetailResponse, error)
	ListResourceDetail(requestId string, args *ListResourceDetailRequest) (*ListResourceDetailResponse, error)
}

type ResourceClient struct {
	bce.Client
}

func NewResourceClient(ak, sk, endpoint string) (*ResourceClient, error) {
	cli, err := bce.NewBceClientWithAkSk(ak, sk, endpoint)
	if err != nil {
		return nil, err
	}
	return &ResourceClient{Client: cli}, nil
}

func (c *ResourceClient) GetResourceDetail(requestId string, args *GetResourceDetailRequest) (*GetResourceDetailResponse, error) {
	resp := &GetResourceDetailResponse{}

	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodPost).
		WithBody(args).
		WithURL("/v1/chargeResource/detail").
		WithHeader("Content-Type", "application/json;charset=utf-8").
		WithHeader("X-Bce-Request-Id", requestId).
		WithResult(resp).
		Do()

	return resp, err
}

func (c *ResourceClient) ListResourceDetail(requestId string, args *ListResourceDetailRequest) (*ListResourceDetailResponse, error) {
	resp := &ListResourceDetailResponse{}

	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodPost).
		WithBody(args).
		WithURL("/v1/query/resource/detail").
		WithHeader("Content-Type", "application/json;charset=utf-8").
		WithHeader("X-Bce-Request-Id", requestId).
		WithResult(resp).
		Do()

	return resp, err

}
