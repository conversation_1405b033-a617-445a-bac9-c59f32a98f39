package orderimport

import (
	http "net/http"

	"github.com/baidubce/bce-sdk-go/auth"

	"github.com/baidubce/bce-sdk-go/bce"
	bcehttp "github.com/baidubce/bce-sdk-go/http"

	"icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

type RealTimeOrderClient interface {
	ImportRealTimeOrder(requestId string, args *ImportOrderRequest) (*ImportOrderResponse, error)
}

type ImportRealTimeOrderClient struct {
	bce.Client
}

func NewImportRealTimeOrderClient(ak, sk, endpoint string) (RealTimeOrderClient, error) {
	cli, err := bce.NewBceClientWithAkSk(ak, sk, endpoint)
	if err != nil {
		return nil, err
	}

	return &ImportRealTimeOrderClient{cli}, nil
}

func (c *ImportRealTimeOrderClient) ImportRealTimeOrder(requestId string, args *ImportOrderRequest) (*ImportOrderResponse, error) {
	resp := &ImportOrderResponse{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodPost).
		WithURL("/orders/realTimeImport").
		WithHeader("Content-Type", iam.DEFAULT_CONTENT_TYPE).
		WithHeader("X-Bce-Request-Id", requestId).
		WithBody(args).
		WithResult(&resp).
		Do()

	return resp, err
}

type Client interface {
	ImportOrder(requestId string, authorization string, args *ImportOrderRequest) (*ImportOrderResponse, error)
}

type ImportOrderClient struct {
	bce.Client
}

func NewClientWithoutAKSK(endPoint string) (Client, error) {

	defaultSignOptions := &auth.SignOptions{
		HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
		ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS}
	defaultConf := &bce.BceClientConfiguration{
		Endpoint:                  endPoint,
		Region:                    bce.DEFAULT_REGION,
		UserAgent:                 bce.DEFAULT_USER_AGENT,
		SignOption:                defaultSignOptions,
		Retry:                     bce.DEFAULT_RETRY_POLICY,
		ConnectionTimeoutInMillis: bce.DEFAULT_CONNECTION_TIMEOUT_IN_MILLIS,
		RedirectDisabled:          false,
	}
	v1Signer := &auth.BceV1Signer{}

	client := &ImportOrderClient{bce.NewBceClient(defaultConf, v1Signer)}
	return client, nil
}

func (c *ImportOrderClient) ImportOrder(requestId string, authorization string, args *ImportOrderRequest) (*ImportOrderResponse, error) {
	resp := &ImportOrderResponse{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodPost).
		WithURL("/orders/import").
		WithHeader(bcehttp.CONTENT_TYPE, iam.DEFAULT_CONTENT_TYPE).
		WithHeader(bcehttp.BCE_REQUEST_ID, requestId).
		WithHeader(bcehttp.AUTHORIZATION, authorization).
		WithBody(args).
		WithResult(&resp).
		Do()

	return resp, err
}
