package ccr

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// ClientTestSuite tests the ccr client
type CCRClientTestSuite struct {
	suite.Suite

	testServer *httptest.Server
	client     ClientInterface
}

// TestClient is the entry of ClientTestSuite
func TestCCRClient(t *testing.T) {
	suite.Run(t, new(CCRClientTestSuite))
}

// SetupSuite prepares the test suite env
func (suite *CCRClientTestSuite) SetupSuite() {
	suite.testServer = httptest.NewServer(&CCRHandler{})

	c, err := NewClient("xxx", "xxx", suite.testServer.URL)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), c)

	suite.client = c
}

// TestCreateTemporaryPassword tests the create temporary password of the client
func (suite *CCRClientTestSuite) TestListInstances() {
	resp, err := suite.client.ListInstances(&ListInstanceRequest{
		PageNo:      1,
		PageSize:    10,
		KeywordType: "name",
		Keyword:     "foo",
	})
	require.NoError(suite.T(), err)
	require.Equal(suite.T(), 1, resp.Total)
	require.Equal(suite.T(), "foo", resp.Instances[0].Name)
}

// TestCreateTemporaryPassword tests the create temporary password of the client
func (suite *CCRClientTestSuite) TestCreateTemporaryPassword() {
	resp, err := suite.client.CreateTemporaryPassword("ccr-1xxxxxxx", &TemporaryPasswordArgs{
		Duration: 2,
	})
	require.NoError(suite.T(), err)
	require.Equal(suite.T(), "bar", resp.Password)
}

// TearDownSuite clears the test suite env
func (suite *CCRClientTestSuite) TearDownSuite() {
	suite.testServer.Close()
}

type CCRHandler struct{}

// ServeHTTP ...
func (mh *CCRHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {

	switch r.URL.Path {

	case "/v1/instances/ccr-1xxxxxxx/credential":
		switch r.Method {
		case http.MethodPost:

			data, err := json.Marshal(&TemporaryPasswordResponse{Password: "bar"})
			if err != nil {
				w.WriteHeader(http.StatusInternalServerError)
				return
			}

			_, err = w.Write(data)
			if err != nil {
				w.WriteHeader(http.StatusInternalServerError)
				break
			}
			w.WriteHeader(http.StatusOK)
			break
		default:
			w.WriteHeader(http.StatusNotFound)
		}

	case "/v1/instances":

		switch r.Method {
		case http.MethodGet:

			data, err := json.Marshal(&ListInstanceResponse{
				PageInfo: PageInfo{
					PageNo:   1,
					PageSize: 10,
					Total:    1,
				},
				Instances: []*InstanceInfo{
					{
						ID:   "ccr-1xxxxxxx",
						Name: "foo",
					},
				},
			})
			if err != nil {
				w.WriteHeader(http.StatusInternalServerError)
				return
			}

			_, err = w.Write(data)
			if err != nil {
				w.WriteHeader(http.StatusInternalServerError)
				break
			}
			w.WriteHeader(http.StatusOK)
			break
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	default:
		w.WriteHeader(http.StatusNotFound)
	}
}
