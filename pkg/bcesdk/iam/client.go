package iam

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/karlseguin/ccache/v2"

	"icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

type ClientInterface interface {
	GetUser(accountId, userId string) (*UserInfo, error)
	GetUsers(req *UserListRequest) (*UserListResponse, error)
	AuthAndVerify(req *http.Request, permissions *iam.PermissionRequest) (*iam.TokenAndVerifyResult, error)
	VerifyUserPermission(userID string, permissions *iam.PermissionRequest) (*VerifyResult, error)
	BatchVerifyUserPermission(userID string, batchPermissions *BatchPermissionRequest) (*BatchVerifyResponse, error)
	ValidatorRequest(req *http.Request) (token *iam.Token, err error)
}

type Client struct {
	*iam.BceClient
	cache *ccache.Cache
	token *iam.Token
}

func NewClient(cli *iam.BceClient) *Client {
	return &Client{
		BceClient: cli,
		cache:     ccache.New(ccache.Configure().MaxSize(5000).ItemsToPrune(500).GetsPerPromote(10)),
	}
}

func (c *Client) GetUser(accountId, userId string) (*UserInfo, error) {

	// get token from cache first
	item := c.cache.Get(genKey(accountId, userId))
	if item != nil && !item.Expired() {
		if user, ok := item.Value().(*UserInfo); ok {
			return user, nil
		}
	}

	listResp, err := c.GetUsers(&UserListRequest{
		DomainID: accountId,
	})
	if err != nil {
		return nil, err
	}
	var currentUser *UserInfo
	if listResp != nil {
		users := listResp.UserList
		for _, user := range users {
			if user.ID == userId {
				currentUser = &user
				break
			}
		}
	}
	if currentUser != nil {
		c.cache.Set(genKey(accountId, userId), currentUser, 7*24*time.Hour)
	}
	return currentUser, nil
}

func (c *Client) GetUsers(req *UserListRequest) (*UserListResponse, error) {
	bceRequest := &iam.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetUri("/v3/users")
	bceRequest.SetMethod(http.MethodGet)
	tokenId, err := c.GetConsoleTokenID()
	if err != nil {
		return nil, fmt.Errorf("getConsoleTokenID faild, error: %s", err)
	}
	bceRequest.SetHeader("X-Auth-Token", tokenId)
	bceRequest.SetHeader("X-Subuser-Support", "true")

	if req.DomainID != "" {
		bceRequest.SetParam("domain_id", req.DomainID)
	}

	if req.Name != "" {
		bceRequest.SetParam("name", req.Name)
	}

	if req.SubUser {
		bceRequest.SetParam("subuser", "true")
	}

	resp := &iam.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	listResp := UserListResponse{}
	if err := resp.ParseJsonBody(&listResp); err != nil {
		return nil, err
	}
	return &listResp, nil
}

func (c *Client) getToken() (*iam.Token, error) {
	if c.token == nil || c.token.ExpiresAt.Add(-1*time.Minute).Before(time.Now()) {
		tk, err := c.GetConsoleToken()
		if err != nil {
			return nil, fmt.Errorf("get console token failed: %w", err)
		}

		c.token = tk
	}

	return c.token, nil
}

func (c *Client) AuthAndVerify(req *http.Request, permissions *iam.PermissionRequest) (*iam.TokenAndVerifyResult, error) {
	tk, err := c.getToken()
	if err != nil {
		return nil, err
	}

	return c.BceClient.AuthAndVerifyWithToken(req, *permissions, tk.ID)
}

// VerifyUserPermission 用于验证用户权限
//
// userID: 用户ID
// permissions: 用户权限请求体
//
// 返回值：
// * VerifyResult: 验证结果
// error: 错误信息
func (c *Client) VerifyUserPermission(userID string, permissions *iam.PermissionRequest) (*VerifyResult, error) {
	tk, err := c.getToken()
	if err != nil {
		return nil, err
	}

	bceRequest := &iam.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetUri(c.Config.Version + fmt.Sprintf("/users/%s/permissions", userID))
	bceRequest.SetMethod(http.MethodPost)
	bceRequest.SetHeader("X-Auth-Token", tk.ID)

	dataByte, err := json.Marshal(&permissions)
	if err != nil {
		return nil, fmt.Errorf("marshal data[%v] err: %v", permissions, err)
	}

	fmt.Printf("verify user permission body: %s\n", dataByte)

	body, err := iam.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("newBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &iam.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, fmt.Errorf("send request: %v", err)
	}
	defer func() { resp.Body().Close() }()

	result := &VerifyResult{}
	if err := resp.ParseJsonBody(result); err != nil {
		return nil, fmt.Errorf("parse json body: %v", err)
	}
	fmt.Printf("verify user permission response: %v\n", result)
	return result, nil
}

func (c *Client) BatchVerifyUserPermission(userID string, batchPermissions *BatchPermissionRequest) (*BatchVerifyResponse, error) {
	tk, err := c.getToken()
	if err != nil {
		return nil, err
	}

	bceRequest := &iam.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetUri(c.Config.Version + fmt.Sprintf("/users/%s/batch_permissions", userID))
	bceRequest.SetMethod(http.MethodPost)
	bceRequest.SetHeader("X-Auth-Token", tk.ID)

	dataByte, err := json.Marshal(&batchPermissions)
	if err != nil {
		return nil, fmt.Errorf("marshal data[%v] err: %v", batchPermissions, err)
	}

	fmt.Printf("batch verify user permission body: %s\n", dataByte)

	body, err := iam.NewBodyFromBytes(dataByte)
	if err != nil {
		return nil, fmt.Errorf("newBodyFromBytes err: %v", err)
	}
	bceRequest.SetBody(body)
	resp := &iam.BceResponse{}
	if err := c.SendRequest(bceRequest, resp); err != nil {
		return nil, fmt.Errorf("send request: %v", err)
	}
	defer func() { resp.Body().Close() }()

	result := &BatchVerifyResponse{}
	if err := resp.ParseJsonBody(result); err != nil {
		return nil, fmt.Errorf("parse json body: %v", err)
	}
	fmt.Printf("batch verify user permission response: %v\n", result)
	return result, nil
}

func (c *Client) ValidatorRequest(req *http.Request) (token *iam.Token, err error) {
	return c.BceClient.ValidatorRequest(req)
}

func genKey(ids ...string) string {
	return strings.Join(ids, "@")
}
