package iam

import "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

type UserListRequest struct {
	DomainID     string `json:"domainId"`
	Name         string `json:"name"`
	SubUser      bool   `json:"subuser"`
	Email        string `json:"email"`
	Provider     string `json:"provider"`
	RoleType     string `json:"roleType"`
	Enabled      bool   `json:"enabled"`
	NameContians string `json:"name_contains"`
	Status       string `json:"status"`
}

type UserListResponse struct {
	UserList []UserInfo `json:"users"`
}

type UserInfo struct {
	ID                string   `json:"id"`
	Name              string   `json:"name"`
	Email             string   `json:"email"`
	Mobile            string   `json:"mobile"`
	Enabled           bool     `json:"enabled"`
	Provider          string   `json:"provider"`
	Roles             []string `json:"roles"`
	Account           string   `json:"account"`
	Subuser           bool     `json:"subuser"`
	Mobileverified    bool     `json:"mobileVerified"`
	Emailverified     bool     `json:"emailVerified"`
	Needresetpassword bool     `json:"needResetPassword"`
	Enabledlogin      bool     `json:"enabledLogin"`
	Enabledmfa        bool     `json:"enabledMfa"`
	Status            string   `json:"status"`
	DomainID          string   `json:"domain_id"`
	DefaultProjectID  string   `json:"default_project_id"`
	PublicID          string   `json:"public_id"`
}

type VerifyResult struct {
	VerifyResult iam.VerifyResult `json:"verify_result"`
}

type BatchPermissionRequest struct {
	VerifyList []VerifyEntity `json:"verify_list"`
}

type VerifyEntity struct {
	Service        string             `json:"service"`
	Region         string             `json:"region"`
	Resource       []string           `json:"resource"`
	Permission     []string           `json:"permission"`
	ResourceOwner  string             `json:"resource_owner"`
	RequestContext iam.RequestContext `json:"request_context"`
}

type EffectEntity struct {
	Effect string `json:"effect"`
	ID     string `json:"id"`
	Eid    string `json:"eid"`
}

type BatchVerifyResult struct {
	Result []EffectEntity `json:"result"`
}

type BatchVerifyResponse struct {
	VerifyResults []BatchVerifyResult `json:"verify_results"`
}
