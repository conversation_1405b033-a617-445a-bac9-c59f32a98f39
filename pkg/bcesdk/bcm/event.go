package bcm

import (
	"fmt"
)

var EventTypeDict = map[EventType]EventDetail{}

func init() {
	EventTypeDict = eventTypeDict()
}

func eventTypeDict() map[EventType]EventDetail {
	return map[EventType]EventDetail{
		EventTypeImagePushSucceeded:            {EventLevelNotice, EventAliasImagePushSucceeded, EventAliasEnImagePushSucceeded, nil},
		EventTypeImagePushFailed:               {EventLevelWarning, EventAliasImagePushFailed, EventAliasEnImagePushFailed, nil},
		EventTypeImageSynchronizationSucceeded: {EventLevelNotice, EventAliasImageSynchronizationSucceeded, EventAliasEnImageSynchronizationSucceeded, nil},
		EventTypeImageSynchronizationFailed:    {EventLevelWarning, EventAliasImageSynchronizationFailed, EventAliasEnImageSynchronizationFailed, nil},
		EventTypeImageScanSucceeded:            {EventLevelNotice, EventAliasImageScanSucceeded, EventAliasEnImageScanSucceeded, nil},
		EventTypeImageScanFailed:               {EventLevelWarning, EventAliasImageScanFailed, EventAliasEnImageScanFailed, nil},
		EventTypeImageTransitionSucceeded:      {EventLevelNotice, EventAliasImageTransitionSucceeded, EventAliasEnImageTransitionSucceeded, nil},
		EventTypeImageTransitionFailed:         {EventLevelWarning, EventAliasImageTransitionFailed, EventAliasEnImageTransitionFailed, nil},
	}
}

func GetEventTypeInfo(eventType EventType, instanceID, projectName, repositoryName, tagName, sha256, policyName, retCode string) (EventDetail, error) {
	info, ok := EventTypeDict[eventType]
	if !ok {
		return EventDetail{}, fmt.Errorf(`event type %s not found in event type dict`, eventType)
	}
	info.EventContent = &EventContent{
		InstanceID:     instanceID,
		ProjectName:    projectName,
		RepositoryName: repositoryName,
		TagName:        tagName,
		Sha256:         sha256,
		PolicyName:     policyName,
		Info:           string(info.EventAlias),
		Advice:         AdviceDefault,
	}
	switch eventType {
	case EventTypeImagePushSucceeded:
	case EventTypeImagePushFailed:
		info.EventContent.Info = fmt.Sprintf("%s，返回码%s", string(info.EventAlias), retCode)
		info.EventContent.Advice = AdviceReadDoc
		info.EventContent.TagName = fillNull(tagName)
		info.EventContent.Sha256 = fillNull(sha256)
		info.EventContent.ProjectName = fillNull(projectName)
	case EventTypeImageSynchronizationSucceeded:
	case EventTypeImageSynchronizationFailed:
		info.EventContent.Advice = AdviceViewConsole
	case EventTypeImageScanSucceeded:
	case EventTypeImageScanFailed:
		info.EventContent.Advice = AdviceViewConsole
		info.EventContent.TagName = fillNull(tagName)
	case EventTypeImageTransitionSucceeded:
	case EventTypeImageTransitionFailed:
		info.EventContent.Advice = AdviceViewConsole
		info.EventContent.TagName = fillNull(tagName)
	}
	return info, nil
}

func fillNull(s string) string {
	if s == "" {
		return "null"
	}
	return s
}
