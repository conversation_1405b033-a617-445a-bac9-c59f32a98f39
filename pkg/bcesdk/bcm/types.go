package bcm

type Event struct {
	Region       string       `json:"region"`
	ResourceID   string       `json:"resourceId"`
	ResourceType ResourceType `json:"resourceType"`
	ResourceName string       `json:"resourceName"`
	EventID      string       `json:"eventId"`
	EventType    EventType    `json:"eventType"`
	EventLevel   EventLevel   `json:"eventLevel"`
	EventAlias   EventAlias   `json:"eventAlias"`
	EventAliasEn EventAliasEn `json:"eventAliasEn"`
	Content      string       `json:"content"`
	Timestamp    string       `json:"timestamp"`
}

type ResourceType string
type EventType string
type EventLevel string
type EventAlias string
type EventAliasEn string

type EventContent struct {
	InstanceID     string `json:"instanceID,omitempty"`
	ProjectName    string `json:"projectName,omitempty"`
	RepositoryName string `json:"repositoryName,omitempty"`
	TagName        string `json:"tagName,omitempty"`
	Sha256         string `json:"sha256,omitempty"`
	Info           string `json:"info,omitempty"`
	Advice         string `json:"advice"`
	PolicyName     string `json:"policyName,omitempty"`
}

const (
	ResourceTypeInstance ResourceType = "INSTANCE" // 目前只有一个固定值

	EventLevelNotice   EventLevel = "NOTICE"
	EventLevelWarning  EventLevel = "WARNING"
	EventLevelMajor    EventLevel = "MAJOR"
	EventLevelCritical EventLevel = "CRITICAL"

	EventTypeImagePushSucceeded            EventType = "ImagePushSucceeded"
	EventTypeImagePushFailed               EventType = "ImagePushFailed"
	EventTypeImageSynchronizationSucceeded EventType = "ImageSynchronizationSucceeded"
	EventTypeImageSynchronizationFailed    EventType = "ImageSynchronizationFailed"
	EventTypeImageScanSucceeded            EventType = "ImageScanSucceeded"
	EventTypeImageScanFailed               EventType = "ImageScanFailed"
	EventTypeImageTransitionSucceeded      EventType = "ImageTransitionSucceeded"
	EventTypeImageTransitionFailed         EventType = "ImageTransitionFailed"

	EventAliasImagePushSucceeded            EventAlias = "镜像推送成功"
	EventAliasImagePushFailed               EventAlias = "镜像推送失败"
	EventAliasImageSynchronizationSucceeded EventAlias = "实例同步成功"
	EventAliasImageSynchronizationFailed    EventAlias = "实例同步失败"
	EventAliasImageScanSucceeded            EventAlias = "镜像安全扫描成功"
	EventAliasImageScanFailed               EventAlias = "镜像安全扫描失败"
	EventAliasImageTransitionSucceeded      EventAlias = "镜像加速版本转换成功"
	EventAliasImageTransitionFailed         EventAlias = "镜像加速版本转换失败"

	EventAliasEnImagePushSucceeded            EventAliasEn = "ImagePushSucceeded"
	EventAliasEnImagePushFailed               EventAliasEn = "ImagePushFailed"
	EventAliasEnImageSynchronizationSucceeded EventAliasEn = "ImageSynchronizationSucceeded"
	EventAliasEnImageSynchronizationFailed    EventAliasEn = "ImageSynchronizationFailed"
	EventAliasEnImageScanSucceeded            EventAliasEn = "ImageScanSucceeded"
	EventAliasEnImageScanFailed               EventAliasEn = "ImageScanFailed"
	EventAliasEnImageTransitionSucceeded      EventAliasEn = "ImageTransitionSucceeded"
	EventAliasEnImageTransitionFailed         EventAliasEn = "ImageTransitionFailed"

	AdviceDefault     string = ""
	AdviceReadDoc     string = "请参考CCR故障处理产品文档解决"
	AdviceViewConsole string = "请登录控制台查看"
)

type PushEventResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	EventID string `json:"eventId"`
}

type EventDetail struct {
	EventLevel   EventLevel
	EventAlias   EventAlias
	EventAliasEn EventAliasEn
	EventContent *EventContent
}
