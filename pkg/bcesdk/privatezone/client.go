package privatezone

import (
	"fmt"
	"net/http"

	"github.com/baidubce/bce-sdk-go/bce"
)

type Interface interface {
	AddRecord(requestID, zoneID string, args *AddRecordArgs) (*AddRecordResponse, error)
	ListRecord(requestID, zoneID, marker string, maxKeys int64) (*ListResolveResponse, error)
	DeleteRecord(requestID, zoneID, clientToken string) error
	CreateZone(requestID string, arg *CreateZoneArgs) (*CreateZoneResponse, error)
	DeleteZone(requestID, zoneID, clientToken string) error
	GetZone(requestID, zoneID string) (*ZoneInfo, error)
	BindVPC(requestID, zoneID string, arg *BindVPCArgs) error
	UnbindVPC(requestID, zoneID string, arg *BindVPCArgs) error
	ListZone(requestID, marker string, maxKeys int64) (*ListZoneResponse, error)
}

const (
	DefaultEndpoint = "http://privatezone.baidubce.com"
)

type Client struct {
	*bce.BceClient
}

func NewClient(ak, sk, token, endpoint string) (*Client, error) {
	if endpoint == "" {
		endpoint = DefaultEndpoint
	}
	cli, err := bce.NewBceClientWithAkSk(ak, sk, endpoint)
	if err != nil {
		return nil, err
	}
	cli.GetBceClientConfig().Credentials.AccessKeyId = ak
	cli.GetBceClientConfig().Credentials.SecretAccessKey = sk
	cli.GetBceClientConfig().Credentials.SessionToken = token

	return &Client{cli}, nil
}

func (c *Client) AddRecord(requestId, zoneId string, args *AddRecordArgs) (*AddRecordResponse, error) {
	result := AddRecordResponse{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodPost).
		WithQueryParamFilter("clientToken", args.ClientToken).
		WithHeader("X-Bce-Request-Id", requestId).
		WithHeader("Content-Type", "application/json").
		WithBody(args).
		WithResult(&result).
		WithURL(fmt.Sprintf("/v1/privatezone/%s/record", zoneId)).
		Do()

	return &result, err
}

func (c *Client) ListRecord(requestId, zoneId, marker string, maxKeys int64) (*ListResolveResponse, error) {
	result := ListResolveResponse{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodGet).
		WithHeader("X-Bce-Request-Id", requestId).
		WithQueryParamFilter("marker", marker).
		WithHeader("maxKeys", fmt.Sprintf("%d", maxKeys)).
		WithResult(&result).
		WithURL(fmt.Sprintf("/v1/privatezone/%s/record", zoneId)).
		Do()

	return &result, err
}

func (c *Client) DeleteRecord(requestId, recordId, clientToken string) error {
	return bce.NewRequestBuilder(c).
		WithMethod(http.MethodDelete).
		WithHeader("X-Bce-Request-Id", requestId).
		WithQueryParamFilter("clientToken", clientToken).
		WithURL(fmt.Sprintf("/v1/privatezone/record/%s", recordId)).
		Do()
}

func (c *Client) CreateZone(requestId string, arg *CreateZoneArgs) (*CreateZoneResponse, error) {
	var resp = CreateZoneResponse{}

	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodPost).
		WithHeader("X-Bce-Request-Id", requestId).
		WithHeader("Content-Type", "application/json").
		WithQueryParamFilter("clientToken", arg.ClientToken).
		WithBody(arg).
		WithURL("/v1/privatezone").
		WithResult(&resp).
		Do()

	return &resp, err
}

func (c *Client) DeleteZone(requestId, zoneId, clientToken string) error {
	return bce.NewRequestBuilder(c).
		WithMethod(http.MethodDelete).
		WithHeader("X-Bce-Request-Id", requestId).
		WithQueryParamFilter("clientToken", clientToken).
		WithURL("/v1/privatezone/" + zoneId).
		Do()
}

func (c *Client) GetZone(requestId, zoneId string) (*ZoneInfo, error) {
	var resp ZoneInfo

	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodGet).
		WithHeader("X-Bce-Request-Id", requestId).
		WithHeader("Content-Type", "application/json").
		WithURL("/v1/privatezone/" + zoneId).
		WithResult(&resp).
		Do()

	return &resp, err
}

func (c *Client) BindVPC(requestId, zoneId string, arg *BindVPCArgs) error {
	return bce.NewRequestBuilder(c).
		WithMethod(http.MethodPut).
		WithHeader("X-Bce-Request-Id", requestId).
		WithHeader("Content-Type", "application/json").
		WithQueryParamFilter("clientToken", arg.ClientToken).
		WithQueryParam("bind", "").
		WithBody(arg).
		WithURL("/v1/privatezone/" + zoneId).
		Do()
}

func (c *Client) UnbindVPC(requestId, zoneId string, arg *BindVPCArgs) error {
	return bce.NewRequestBuilder(c).
		WithMethod(http.MethodPut).
		WithHeader("X-Bce-Request-Id", requestId).
		WithHeader("Content-Type", "application/json").
		WithQueryParamFilter("clientToken", arg.ClientToken).
		WithQueryParam("unbind", "").
		WithBody(arg).
		WithURL("/v1/privatezone/" + zoneId).
		Do()
}

func (c *Client) ListZone(requestId, marker string, maxKeys int64) (*ListZoneResponse, error) {
	var resp ListZoneResponse

	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodGet).
		WithHeader("X-Bce-Request-Id", requestId).
		WithQueryParamFilter("marker", marker).
		WithHeader("maxKeys", fmt.Sprintf("%d", maxKeys)).
		WithResult(&resp).
		WithURL("/v1/privatezone").
		Do()

	return &resp, err
}
