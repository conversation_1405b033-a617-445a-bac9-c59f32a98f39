package gaiadb

import (
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
	"github.com/baidubce/bce-sdk-go/services/gaiadb"
)

// GetAvailableSubnetList - get available subnetList
//
// PARAMS:
//   - vpcID: the vpc id which you want to query
//
// RETURNS:
//   - []AvailableSubnet: the result of get available subnetList
//   - error: nil if success otherwise the specific error
func GetAvailableSubnetList(c *gaiadb.Client, vpcID string) ([]AvailableSubnet, error) {
	result := &[]AvailableSubnet{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.GET).
		WithURL(getGaiadbURI()+"/cluster/subnetId").
		WithQueryParamFilter("vpcId", vpcID).
		WithResult(result).
		Do()

	return *result, err
}

func getGaiadbURI() string {
	return "/v1/gaiadb"
}
