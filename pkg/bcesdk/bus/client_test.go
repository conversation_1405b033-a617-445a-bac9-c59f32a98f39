package bus

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// ClientTestSuite tests the v1 client
type ClientTestSuite struct {
	suite.Suite

	testServer *httptest.Server
	client     Client
}

// TestClient is the entry of ClientTestSuite
func TestClient(t *testing.T) {
	suite.Run(t, new(ClientTestSuite))
}

// SetupSuite prepares the test suite env
func (suite *ClientTestSuite) SetupSuite() {
	suite.testServer = httptest.NewServer(&MockHandler{})

	c, err := NewClient("xxx", "xxx", suite.testServer.URL)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), c)

	suite.client = c
}

// TestClientMetadata tests the metadata of the client
func (suite *ClientTestSuite) TestClientDeleteService() {
	err := suite.client.DeleteService("", &DeleteServiceRequest{
		Endpoint: "127.0.0.1:8080",
		Region:   "",
	})
	require.NoError(suite.T(), err)
}

// TestClientMetadata tests the metadata of the client
func (suite *ClientTestSuite) TestClientRegisterService() {
	err := suite.client.RegisterService("", &RegisterServiceRequest{
		Endpoint: "127.0.0.1:8080",
		Region:   "",
	})
	require.NoError(suite.T(), err)
}

// TearDownSuite clears the test suite env
func (suite *ClientTestSuite) TearDownSuite() {
	suite.testServer.Close()
}

type MockHandler struct{}

// ServeHTTP ...
func (mh *MockHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	fmt.Println(r.RequestURI)
	switch r.Method {

	case http.MethodDelete:

		if r.URL.Path != "/v1/bus" {
			w.WriteHeader(http.StatusNotFound)
			return
		}
		w.WriteHeader(http.StatusOK)
		break
	case http.MethodPut:
		if r.RequestURI != "/v1/bus" {
			w.WriteHeader(http.StatusNotFound)
			return
		}
		w.WriteHeader(http.StatusOK)
		break
	}
}
