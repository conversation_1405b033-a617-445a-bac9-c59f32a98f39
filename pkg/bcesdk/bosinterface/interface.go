package bosinterface

import (
	"io"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/bos/api"
)

type BosInterface interface {
	ListBuckets() (*api.ListBucketsResult, error)
	ListObjects(bucket string,
		args *api.ListObjectsArgs) (*api.ListObjectsResult, error)
	SimpleListObjects(bucket, prefix string, maxKeys int, marker,
		delimiter string) (*api.ListObjectsResult, error)
	HeadBucket(bucket string) error
	DoesBucketExist(bucket string) (bool, error)
	IsNsBucket(bucket string) bool
	PutBucket(bucket string) (string, error)
	DeleteBucket(bucket string) error
	GetBucketLocation(bucket string) (string, error)
	PutBucketAcl(bucket string, aclBody *bce.Body) error
	PutBucketAclFromCanned(bucket, cannedAcl string) error
	PutBucketAclFromFile(bucket, aclFile string) error
	PutBucketAclFromString(bucket, aclString string) error
	PutBucketAclFromStruct(bucket string, aclObj *api.PutBucketAclArgs) error
	GetBucketAcl(bucket string) (*api.GetBucketAclResult, error)
	PutBucketLogging(bucket string, body *bce.Body) error
	PutBucketLoggingFromString(bucket, logging string) error
	PutBucketLoggingFromStruct(bucket string, obj *api.PutBucketLoggingArgs) error
	GetBucketLogging(bucket string) (*api.GetBucketLoggingResult, error)
	DeleteBucketLogging(bucket string) error
	PutBucketLifecycle(bucket string, lifecycle *bce.Body) error
	PutBucketLifecycleFromString(bucket, lifecycle string) error
	GetBucketLifecycle(bucket string) (*api.GetBucketLifecycleResult, error)
	DeleteBucketLifecycle(bucket string) error
	PutBucketStorageclass(bucket, storageClass string) error
	GetBucketStorageclass(bucket string) (string, error)
	PutBucketReplication(bucket string, replicationConf *bce.Body, replicationRuleId string) error
	PutBucketReplicationFromFile(bucket, confFile string, replicationRuleId string) error
	PutBucketReplicationFromString(bucket, confString string, replicationRuleId string) error
	PutBucketReplicationFromStruct(bucket string,
		confObj *api.PutBucketReplicationArgs, replicationRuleId string) error
	GetBucketReplication(bucket string, replicationRuleId string) (*api.GetBucketReplicationResult, error)
	ListBucketReplication(bucket string) (*api.ListBucketReplicationResult, error)
	DeleteBucketReplication(bucket string, replicationRuleId string) error
	GetBucketReplicationProgress(bucket string, replicationRuleId string) (
		*api.GetBucketReplicationProgressResult, error)
	PutBucketEncryption(bucket, algorithm string) error
	GetBucketEncryption(bucket string) (string, error)
	DeleteBucketEncryption(bucket string) error
	PutBucketStaticWebsite(bucket string, config *bce.Body) error
	PutBucketStaticWebsiteFromString(bucket, jsonConfig string) error
	PutBucketStaticWebsiteFromStruct(bucket string,
		confObj *api.PutBucketStaticWebsiteArgs) error
	SimplePutBucketStaticWebsite(bucket, index, notFound string) error
	GetBucketStaticWebsite(bucket string) (
		*api.GetBucketStaticWebsiteResult, error)
	DeleteBucketStaticWebsite(bucket string) error
	PutBucketCors(bucket string, config *bce.Body) error
	PutBucketCorsFromFile(bucket, filename string) error
	PutBucketCorsFromString(bucket, jsonConfig string) error
	PutBucketCorsFromStruct(bucket string, confObj *api.PutBucketCorsArgs) error
	GetBucketCors(bucket string) (*api.GetBucketCorsResult, error)
	DeleteBucketCors(bucket string) error
	PutBucketCopyrightProtection(bucket string, resources ...string) error
	GetBucketCopyrightProtection(bucket string) ([]string, error)
	DeleteBucketCopyrightProtection(bucket string) error
	PutObject(bucket, object string, body *bce.Body, args *api.PutObjectArgs) (string, error)
	BasicPutObject(bucket, object string, body *bce.Body) (string, error)
	PutObjectFromBytes(bucket, object string, bytesArr []byte,
		args *api.PutObjectArgs) (string, error)
	PutObjectFromString(bucket, object, content string,
		args *api.PutObjectArgs) (string, error)
	PutObjectFromFile(bucket, object, fileName string,
		args *api.PutObjectArgs) (string, error)
	PutObjectFromStream(bucket, object string, reader io.Reader,
		args *api.PutObjectArgs) (string, error)
	CopyObject(bucket, object, srcBucket, srcObject string,
		args *api.CopyObjectArgs) (*api.CopyObjectResult, error)
	BasicCopyObject(bucket, object, srcBucket,
		srcObject string) (*api.CopyObjectResult, error)
	GetObject(bucket, object string, args map[string]string,
		ranges ...int64) (*api.GetObjectResult, error)
	BasicGetObject(bucket, object string) (*api.GetObjectResult, error)
	BasicGetObjectToFile(bucket, object, filePath string) error
	GetObjectMeta(bucket, object string) (*api.GetObjectMetaResult, error)
	SelectObject(bucket, object string, args *api.SelectObjectArgs) (*api.SelectObjectResult, error)
	FetchObject(bucket, object, source string,
		args *api.FetchObjectArgs) (*api.FetchObjectResult, error)
	BasicFetchObject(bucket, object, source string) (*api.FetchObjectResult, error)
	SimpleFetchObject(bucket, object, source, mode,
		storageClass string) (*api.FetchObjectResult, error)
	AppendObject(bucket, object string, content *bce.Body,
		args *api.AppendObjectArgs) (*api.AppendObjectResult, error)
	SimpleAppendObject(bucket, object string, content *bce.Body,
		offset int64) (*api.AppendObjectResult, error)
	SimpleAppendObjectFromString(bucket, object, content string, offset int64) (*api.AppendObjectResult, error)
	SimpleAppendObjectFromFile(bucket, object, filePath string, offset int64) (*api.AppendObjectResult, error)
	DeleteObject(bucket, object string) error
	DeleteMultipleObjects(bucket string, objectListStream *bce.Body) (*api.DeleteMultipleObjectsResult, error)
	DeleteMultipleObjectsFromString(bucket, objectListString string) (*api.DeleteMultipleObjectsResult, error)
	DeleteMultipleObjectsFromStruct(bucket string, objectListStruct *api.DeleteMultipleObjectsArgs) (*api.DeleteMultipleObjectsResult, error)
	DeleteMultipleObjectsFromKeyList(bucket string, keyList []string) (*api.DeleteMultipleObjectsResult, error)
	InitiateMultipartUpload(bucket, object, contentType string, args *api.InitiateMultipartUploadArgs) (*api.InitiateMultipartUploadResult, error)
	BasicInitiateMultipartUpload(bucket, object string) (*api.InitiateMultipartUploadResult, error)
	UploadPart(bucket, object, uploadId string, partNumber int, content *bce.Body, args *api.UploadPartArgs) (string, error)
	BasicUploadPart(bucket, object, uploadId string, partNumber int, content *bce.Body) (string, error)
	UploadPartFromBytes(bucket, object, uploadId string, partNumber int, content []byte, args *api.UploadPartArgs) (string, error)
	UploadPartCopy(bucket, object, srcBucket, srcObject, uploadId string, partNumber int, args *api.UploadPartCopyArgs) (*api.CopyObjectResult, error)
	BasicUploadPartCopy(bucket, object, srcBucket, srcObject, uploadId string, partNumber int) (*api.CopyObjectResult, error)
	CompleteMultipartUpload(bucket, object, uploadId string, body *bce.Body, args *api.CompleteMultipartUploadArgs) (*api.CompleteMultipartUploadResult, error)
	CompleteMultipartUploadFromStruct(bucket, object, uploadId string,
		args *api.CompleteMultipartUploadArgs) (*api.CompleteMultipartUploadResult, error)
	AbortMultipartUpload(bucket, object, uploadId string) error
	ListParts(bucket, object, uploadId string, args *api.ListPartsArgs) (*api.ListPartsResult, error)
	BasicListParts(bucket, object, uploadId string) (*api.ListPartsResult, error)
	ListMultipartUploads(bucket string, args *api.ListMultipartUploadsArgs) (*api.ListMultipartUploadsResult, error)
	BasicListMultipartUploads(bucket string) (*api.ListMultipartUploadsResult, error)
	UploadSuperFile(bucket, object, fileName, storageClass string) error
	DownloadSuperFile(bucket, object, fileName string) (err error)
	GeneratePresignedUrl(bucket, object string, expireInSeconds int, method string, headers, params map[string]string) string
	GeneratePresignedUrlPathStyle(bucket, object string, expireInSeconds int, method string,
		headers, params map[string]string) string
	BasicGeneratePresignedUrl(bucket, object string, expireInSeconds int) string
	PutObjectAcl(bucket, object string, aclBody *bce.Body) error
	PutObjectAclFromCanned(bucket, object, cannedAcl string) error
	PutObjectAclGrantRead(bucket, object string, ids ...string) error
	PutObjectAclGrantFullControl(bucket, object string, ids ...string) error
	PutObjectAclFromFile(bucket, object, aclFile string) error
	PutObjectAclFromString(bucket, object, aclString string) error
	PutObjectAclFromStruct(bucket, object string, aclObj *api.PutObjectAclArgs) error
	GetObjectAcl(bucket, object string) (*api.GetObjectAclResult, error)
	DeleteObjectAcl(bucket, object string) error
	RestoreObject(bucket string, object string, restoreDays int, restoreTier string) error
	PutBucketTrash(bucket string, trashReq api.PutBucketTrashReq) error
	GetBucketTrash(bucket string) (*api.GetBucketTrashResult, error)
	DeleteBucketTrash(bucket string) error
	PutBucketNotification(bucket string, putBucketNotificationReq api.PutBucketNotificationReq) error
	GetBucketNotification(bucket string) (*api.PutBucketNotificationReq, error)
	DeleteBucketNotification(bucket string) error
	ParallelUpload(bucket string, object string, filename string, contentType string,
		args *api.InitiateMultipartUploadArgs) (*api.CompleteMultipartUploadResult, error)
	ParallelCopy(srcBucketName string, srcObjectName string, destBucketName string, destObjectName string,
		args *api.MultiCopyObjectArgs, srcClient *bos.Client) (*api.CompleteMultipartUploadResult, error)
	PutSymlink(bucket string, object string, symlinkKey string, symlinkArgs *api.PutSymlinkArgs) error
	GetSymlink(bucket string, object string) (string, error)
	PutBucketMirror(bucket string, putBucketMirrorArgs *api.PutBucketMirrorArgs) error
	GetBucketMirror(bucket string) (*api.PutBucketMirrorArgs, error)
	DeleteBucketMirror(bucket string) error
}
