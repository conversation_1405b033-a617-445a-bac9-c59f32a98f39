package certificate

import "time"

// Cert 证书信息
type Cert struct {
	// certId:证书ID, 全局唯一
	CertID string `json:"certId"`

	// certName:证书名称
	CertName string `json:"certName"`

	// certType:证书类型，1:服务端证书(默认)  2:客户端证书
	CertType int `json:"certType"`

	// certCommonName:证书通用名称
	CertCommonName string `json:"certCommonName"`

	// certStartTime:证书生效时间
	CertStartTime time.Time `json:"certStartTime"`

	// certStopTime:证书到期时间
	CertStopTime time.Time `json:"certStopTime"`

	// certCreateTime:证书创建时间
	CertCreateTime time.Time `json:"certCreateTime"`

	// certUpdateTime:证书更新时间
	CertUpdateTime time.Time `json:"certUpdateTime"`
}

// ListCertsResult 证书列表
type ListCertsResult struct {
	Certs []Cert `json:"certs"`
}

// CertContent 证书内容
type CertContent struct {
	// certId:证书ID
	CertID string `json:"certId"`

	// certName:证书自定义名称
	CertName string `json:"certName"`

	// certServerData:服务器证书数据
	CertServerData string `json:"certServerData"`

	// certPrivateKey: 证书私钥
	CertPrivateKey string `json:"certPrivateKey"`

	// certLinkData:证书链数据
	CertLinkData string `json:"certLinkData"`

	// certType:证书类型，1:服务端证书(默认)  2:客户端证书
	CertType int `json:"certType"`
}

// ServiceUseCertResponse 证书使用信息
type ServiceUseCertResponse struct {
	// certId:证书ID
	CertID string `json:"certId"`

	// serviceName:服务名称
	ServiceName string `json:"serviceName"`

	// resourceId:资源ID
	ResourceID string `json:"resourceId"`

	// serviceCreateTime:使用证书开始时间
	ServiceCreateTime time.Time `json:"serviceCreateTime"`

	//serviceUpdateTime:更新证书使用时间
	ServiceUpdateTime time.Time `json:"serviceUpdateTime"`
}
