package dns

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/sirupsen/logrus"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

type Client struct {
	dnsHost     string
	publicToken string
	rdnsHost    string
}

func NewClient(dnsHost, dnsToken, rdnsHost string) *Client {
	return &Client{
		dnsHost:     dnsHost,
		publicToken: dnsToken,
		rdnsHost:    rdnsHost,
	}
}

// http://wiki.baidu.com/pages/viewpage.action?pageId=1512704996
func (c *Client) buildPublicRequest(method, path, token string, body interface{}) (*http.Request, error) {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("marshal request body failed: %w", err)
		}
	}

	req, err := http.NewRequest(method, path, bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	logrus.Infof("create public dns record request: %v, with body: %s", req.URL.String(), string(reqBody))

	if token != "" {
		req.Header.Set("Authorization", token)
	}

	return req, nil
}

func (c *Client) AddRecord(args *DNSRecord) error {
	path := strings.TrimSuffix(c.dnsHost, "/") + "/controller/v1/dns/bcedns/domain"

	req, err := c.buildPublicRequest(http.MethodPost, path, c.publicToken, args)
	if err != nil {
		return err
	}

	return c.buildResponse(req, nil)
}

func (c *Client) DeleteRecord(args *DNSRecord) error {
	path := strings.TrimSuffix(c.dnsHost, "/") + "/controller/v1/dns/bcedns/domain"

	req, err := c.buildPublicRequest(http.MethodDelete, path, c.publicToken, args)
	if err != nil {
		return err
	}

	return c.buildResponse(req, nil)
}

// in hkg: rdns will dispatched with bce dns
func (c *Client) AddRDNSRecord(args *DNSRecord) error {
	path := strings.TrimSuffix(c.rdnsHost, "/") + "/controller/v1/dns/bcedns/domain"
	if args.View == utils.RegionHKG {
		path = strings.TrimSuffix(c.dnsHost, "/") + "/controller/v1/dns/bcedns/domain"
	}

	req, err := c.buildPublicRequest(http.MethodPost, path, "", args)
	if err != nil {
		return err
	}

	return c.buildResponse(req, nil)
}

func (c *Client) DeleteRDNSRecord(args *DNSRecord) error {
	path := strings.TrimSuffix(c.rdnsHost, "/") + "/controller/v1/dns/bcedns/domain"
	if args.View == utils.RegionHKG {
		path = strings.TrimSuffix(c.dnsHost, "/") + "/controller/v1/dns/bcedns/domain"
	}

	req, err := c.buildPublicRequest(http.MethodDelete, path, "", args)
	if err != nil {
		return err
	}

	return c.buildResponse(req, nil)
}

func (c *Client) buildResponse(req *http.Request, v interface{}) error {
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	content, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("read from resp body failed: %w", err)
	}

	logrus.Infof("get response :%d %s", resp.StatusCode, string(content))

	if resp.StatusCode >= 400 {
		// try to unmarshal
		var dnsError DNSError
		err = json.Unmarshal(content, &dnsError)
		if err != nil {
			return &bce.BceServiceError{
				Code:       http.StatusText(resp.StatusCode),
				StatusCode: resp.StatusCode,
				Message:    string(content),
			}
		}

		dnsError.ErrCode = resp.StatusCode
		return &dnsError
	}

	if v == nil {
		return nil
	}

	err = json.Unmarshal(content, v)
	if err != nil {
		return fmt.Errorf("unmarshal body failed: %w", err)
	}

	return nil
}

type DNSError struct {
	ErrCode      int         `json:"errCode,omitempty"`
	ErrNum       int         `json:"ErrNum,omitempty"`
	ErrMsg       string      `json:"ErrMsg,omitempty"`
	RetData      interface{} `json:"RetData,omitempty"`
	URLManual    string      `json:"UrlManual,omitempty"`
	URLRedirect  string      `json:"UrlRedirect,omitempty"`
	RespBodyType int         `json:"RespBodyType,omitempty"`
}

func (d *DNSError) Error() string {
	return fmt.Sprintf("ErrCode: %d, ErrNum: %d, ErrMsg: %s, RetData: %v, URLManual: %v, URLRedirect: %s, RespBodyType: %d",
		d.ErrCode,
		d.ErrNum,
		d.ErrMsg,
		d.RetData,
		d.URLManual,
		d.URLRedirect,
		d.RespBodyType,
	)
}

func (d *DNSError) IsAlreadyExist() bool {
	if d.ErrCode == 401 && d.ErrNum == 401 && d.ErrMsg == "This type and rdata is already" {
		return true
	}

	return false
}

func (d *DNSError) IsNotFound() bool {
	if d.ErrCode == 401 && d.ErrNum == 401 && d.ErrMsg == "RR is not exit" {
		return true
	}

	return false
}
