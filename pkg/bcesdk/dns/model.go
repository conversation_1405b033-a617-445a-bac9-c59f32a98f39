package dns

// DNSRecord args for public
type DNSRecord struct {
	Domain string `json:"domain"`
	Zone   string `json:"zone"`
	Type   string `json:"type"`
	TTL    int    `json:"ttl"`
	Rdata  string `json:"rdata"`
	View   string `json:"view"`
}

type CreateVPCDNSRecordRequest struct {
	VpcId  string `json:"vpcId"`  // vpc长id
	Name   string `json:"name"`   // 解析记录名称，例如："abc.baidubce.com"
	Domain string `json:"domain"` // 域名，例如："baidubce.com"
	Type   string `json:"type"`   // 解析记录类型，包含："A", "AAAA" rds目前使用这两种类型
	Value  string `json:"value"`  // 解析记录值
	Ttl    int    `json:"ttl"`    // 解析记录在本地DNS服务器的缓存时间（单位：秒），默认60s
}
type CreateVPCDNSRecordResponse struct {
	Id string `json:"id"`
}

type UpdateVPCDNSRecordResquest struct {
	Name  string `json:"name"`  // 解析记录名称，例如："abc.baidubce.com"
	Type  string `json:"type"`  // 解析记录类型，包含："A", "AAAA" rds目前使用这两种类型
	Value string `json:"value"` // 解析记录值
	Ttl   int    `json:"ttl"`   // 解析记录在本地DNS服务器的缓存时间（单位：秒），默认60s
}

type ListVpcDnsRecordResponse struct {
	NextMarker  string              `json:"nextMarker"`
	Marker      string              `json:"marker"`
	MaxKeys     int                 `json:"maxKeys"`
	IsTruncated bool                `json:"isTruncated"`
	Records     []*VpcDNSRecordData `json:"records"`
}

type VpcDNSRecordData struct {
	Id    string `json:"id"`
	Name  string `json:"name"`  // 解析记录名称，例如："abc.baidubce.com"
	Type  string `json:"type"`  // 解析记录类型，包含："A", "AAAA" rds目前使用这两种类型
	Value string `json:"value"` // 解析记录值
	Ttl   int    `json:"ttl"`   // 解析记录在本地DNS服务器的缓存时间（单位：秒），默认60s
	VpcId string `json:"vpcId"` // vpc长id
}
type GetVpcDnsRecordResponse struct {
	Record *VpcDNSRecordData `json:"record"`
}
