package lane

import (
	"fmt"
	"os"
	"path"

	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Option struct {
	DB           *dbutil.DB
	LaneFilePath string
}

func NewOption(d *gorm.DB) *Option {
	pwd, err := os.Getwd()
	if err != nil {
		fmt.Printf("get pwd error %v", err)
		panic(err)
	}
	return &Option{
		DB:           dbutil.NewDB(d),
		LaneFilePath: path.Join(pwd, constants.BaseLanePath),
	}
}
