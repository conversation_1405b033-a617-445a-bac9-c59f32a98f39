package lane

import (
	kubeContext "context"
	"fmt"
	"path"
	"reflect"
	"sort"
	"strings"
	"sync"
	"time"

	"istio.io/client-go/pkg/apis/networking/v1alpha3"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/lane"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	laneDeploy "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/deploy/lane"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil/rollback"
)

type Service struct {
	opt            *Option
	laneModel      lane.ServiceInterface
	instancesModel instances.ServiceInterface
	clusterModel   cluster.ServiceInterface
	cceService     cce.ClientInterface
}

func NewService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:            option,
		laneModel:      lane.NewLaneService(lane.NewOption(gormDB)),
		instancesModel: instances.NewInstancesService(instances.NewOption(gormDB)),
		clusterModel:   cluster.NewClusterService(cluster.NewOption(gormDB)),
		cceService:     cce.NewClientService(),
	}
}

// NewLaneGroup 新建泳道组
func (s *Service) NewLaneGroup(ctx context.CsmContext, laneGroupParams *meta.LaneGroupParams, accountID string) (err error) {

	if laneGroupParams.InstanceUUID == "" || accountID == "" {
		ctx.CsmLogger().Errorf("instanceID or accountID is empty")
		return csmErr.NewInvalidParameterInputValueException("instanceID or accountID is empty")
	}

	laneGroupModel, toErr := laneGroupParams.ToLaneGroupsModel()
	if toErr != nil {
		return toErr
	}
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()

	laneModel := s.laneModel.WithTx(dbutil.NewDB(tx))
	groupID, genErr := laneModel.GenerateLaneGroupID()
	if genErr != nil {
		ctx.CsmLogger().Errorf("failed to generateLaneGroupID err is %s", genErr.Error())
		return genErr
	}
	laneGroupModel.GroupID = groupID
	laneGroupModel.AccountId = accountID

	// TODO 暂时不用加泳道组数量校验

	err = laneModel.NewLaneGroup(ctx, laneGroupModel)
	if err != nil {
		return err
	}

	// 创建envoyFilter
	laneGroupParams.GroupID = groupID
	err = s.newEnvoyFiltersForLaneGroup(ctx, laneGroupParams)
	if err != nil {
		return err
	}

	// 创建基准泳道
	laneGroupParams.BaseLane.GroupID = groupID
	laneGroupParams.BaseLane.InstanceUUID = laneGroupParams.InstanceUUID
	laneGroupParams.BaseLane.IsBase = true

	err = s.newBaseLane(ctx, laneModel, &laneGroupParams.BaseLane, accountID, laneGroupParams.RouteHeader)
	if err != nil {
		return err
	}
	tx.Commit()
	return nil
}

// newBaseLane 新建基准泳道
func (s *Service) newBaseLane(ctx context.CsmContext, laneServer lane.ServiceInterface,
	laneParams *meta.LaneParams, accountID, routeHeader string) (err error) {
	if laneParams.InstanceUUID == "" || accountID == "" {
		ctx.CsmLogger().Errorf("instanceID or accountID is empty")
		return csmErr.NewInvalidParameterInputValueException("instanceID or accountID is empty")
	}

	laneModel, toErr := laneParams.ToLanesModel()
	if toErr != nil {
		return toErr
	}

	laneID, genErr := laneServer.GenerateLaneID()
	if genErr != nil {
		ctx.CsmLogger().Errorf("failed to generateLaneGroupID err is %s", genErr.Error())
		return genErr
	}
	laneModel.LaneID = laneID
	laneModel.AccountId = accountID

	// 后创建VS + DR
	laneParams.LaneID = laneID
	route := &meta.RouteRule{
		MatchRequest: meta.MatchRequest{
			Headers: []meta.Header{
				{
					Name:            routeHeader,
					MatchingMode:    strings.ToLower(constants.ExactMatchMode),
					MatchingContent: laneParams.LaneName,
				},
			},
		},
	}

	err = s.CreateOrUpdateDestinationRule(ctx, laneParams)
	if err != nil {
		return err
	}

	// 等待一段时间，保证控制面先同步DestinationRule
	time.Sleep(1 * time.Second)

	err = s.newVirtualServiceForLane(ctx, laneParams, route)
	if err != nil {
		return err
	}

	// TODO 暂时不用加泳道数量校验
	err = laneServer.NewLane(ctx, laneModel)
	if err != nil {
		return err
	}

	// 新建delegate类型
	err = s.createOrUpdateVSDelegate(ctx, laneServer, laneModel.InstanceUUID, accountID)
	if err != nil {
		return err
	}

	return nil
}

// NewLane 新建泳道
func (s *Service) NewLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) (err error) {
	if laneParams.InstanceUUID == "" || accountID == "" {
		ctx.CsmLogger().Errorf("instanceID or accountID is empty")
		return csmErr.NewInvalidParameterInputValueException("instanceID or accountID is empty")
	}

	laneModel, toErr := laneParams.ToLanesModel()
	if toErr != nil {
		return toErr
	}
	// 获取泳道组信息
	laneGroup, err := s.laneModel.GetLaneGroupByID(ctx, laneModel.InstanceUUID, laneModel.GroupID, accountID)
	if err != nil {
		return err
	}
	routeHeader := laneGroup.RouteHeader

	laneID, genErr := s.laneModel.GenerateLaneID()
	if genErr != nil {
		ctx.CsmLogger().Errorf("failed to generateLaneGroupID err is %s", genErr.Error())
		return genErr
	}
	laneModel.LaneID = laneID
	laneModel.AccountId = accountID

	// 后创建VS + DR
	laneParams.LaneID = laneID
	route := &meta.RouteRule{
		MatchRequest: meta.MatchRequest{
			Headers: []meta.Header{
				{
					Name:            routeHeader,
					MatchingMode:    strings.ToLower(constants.ExactMatchMode),
					MatchingContent: laneParams.LaneName,
				},
			},
		},
	}

	// 串行创建
	err = s.CreateOrUpdateDestinationRule(ctx, laneParams)
	if err != nil {
		return err
	}

	// 等待一段时间，保证控制面先同步DestinationRule
	time.Sleep(1 * time.Second)

	err = s.newVirtualServiceForLane(ctx, laneParams, route)
	if err != nil {
		return err
	}

	// TODO 暂时不用加泳道数量校验
	err = s.laneModel.NewLane(ctx, laneModel)
	if err != nil {
		return err
	}

	// 新建delegate类型
	err = s.createOrUpdateVSDelegate(ctx, s.laneModel, laneParams.InstanceUUID, accountID)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) handleDeleteForVSDelegate(ctx context.CsmContext, laneModel lane.ServiceInterface,
	deletedServices []meta.ServiceListParams, instanceUUID, accountID string) error {
	if instanceUUID == "" {
		return csmErr.NewInvalidParameterInputValueException("InstanceUUID is invalid")
	}

	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return err
	}

	vsFilePath := path.Join(s.opt.LaneFilePath, constants.LaneVirtualServiceDelegateTmpl)
	externalCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, instanceUUID, instance.InstanceType)
	if cErr != nil {
		return cErr
	}
	region := externalCluster.Region
	clusterID := externalCluster.ClusterUUID
	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterID, meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	// 获取当前实例下所有泳道组
	laneGroups, err := laneModel.GetLaneGroupsByInstanceUUID(ctx, instanceUUID, accountID)
	if err != nil {
		return err
	}
	laneList := make([]meta.Lanes, 0)
	// 获取现在依然存在的服务列表
	serviceList := make([]meta.ServiceListParams, 0)
	if laneGroups == nil || len(laneGroups) <= 0 {
		ctx.CsmLogger().Infof("instanceUUID %s's laneGroup is empty", instanceUUID)
	} else {
		//nolint
		for _, lg := range laneGroups {
			// 获取当前泳道组下所有泳道
			where := &meta.Lanes{
				GroupID:      lg.GroupID,
				InstanceUUID: lg.InstanceUUID,
				Deleted:      csm.Int(0),
			}

			tempLaneList, listErr := laneModel.GetLanes(ctx, &meta.Lanes{}, where)
			if listErr != nil {
				return listErr
			}

			if tempLaneList == nil || len(tempLaneList) <= 0 {
				// 没有泳道则跳过，日志打印记录一下
				ctx.CsmLogger().Warnf(fmt.Sprintf("not found lane, groupID is %s", lg.GroupID))
				continue
			}
			// 添加泳道
			laneList = append(laneList, tempLaneList...)
			// 添加服务列表
			lgServiceList := meta.ModelStringToServiceList(lg.ServiceList)
			needServices := findNeedServices(lgServiceList, serviceList)
			serviceList = append(serviceList, needServices...)
		}

		namespace := ""
		if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
			namespace = instance.IstioInstallNamespace
		}

		vsDelegateParseParams := laneDeploy.NewVSDelegateParseParams(constants.APIVersion16, namespace,
			serviceList, laneList, laneGroups)

		deployer, deployErr := laneDeploy.NewDeploy(k8sClient, nil, nil, nil, vsDelegateParseParams)
		if deployErr != nil {
			return deployErr
		}

		err = deployer.CreateOrUpdateVirtualServiceForDelegate(ctx, vsFilePath)
		if err != nil {
			return err
		}
	}

	// 获取需要删除的服务列表
	needDeleted := findNeedServices(deletedServices, serviceList)
	for index := range needDeleted {
		deleteService := needDeleted[index]
		deletedNamespace := deleteService.Namespace
		if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
			deletedNamespace = instance.IstioInstallNamespace
		}
		// virtualService Delegate name is "vs-delegate-{serviceName}"

		deletedName := fmt.Sprintf("vs-delegate-%s", strings.ToLower(deleteService.ServiceName))
		err = k8sClient.Istio().NetworkingV1alpha3().VirtualServices(deletedNamespace).Delete(kubeContext.TODO(), deletedName, metav1.DeleteOptions{})
		if err != nil && !kubeErrors.IsNotFound(err) {
			return err
		}

	}

	return nil
}

func (s *Service) createOrUpdateVSDelegate(ctx context.CsmContext, laneModel lane.ServiceInterface, instanceUUID, accountID string) error {
	if instanceUUID == "" {
		return csmErr.NewInvalidParameterInputValueException("InstanceUUID is invalid")
	}

	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return err
	}

	vsFilePath := path.Join(s.opt.LaneFilePath, constants.LaneVirtualServiceDelegateTmpl)
	externalCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, instanceUUID, instance.InstanceType)
	if cErr != nil {
		return cErr
	}
	region := externalCluster.Region
	clusterID := externalCluster.ClusterUUID
	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterID, meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	// 获取当前实例下所有泳道组
	laneGroups, err := laneModel.GetLaneGroupsByInstanceUUID(ctx, instanceUUID, accountID)
	if err != nil {
		return err
	}

	if laneGroups == nil || len(laneGroups) == 0 {
		ctx.CsmLogger().Infof("instanceUUID %s's laneGroup is empty", instanceUUID)
		return nil
	}
	laneList := make([]meta.Lanes, 0)

	serviceList := make([]meta.ServiceListParams, 0)
	for _, lg := range laneGroups {
		// 获取当前泳道组下所有泳道
		where := &meta.Lanes{
			GroupID:      lg.GroupID,
			InstanceUUID: lg.InstanceUUID,
			Deleted:      csm.Int(0),
		}

		tempLaneList, listErr := laneModel.GetLanes(ctx, &meta.Lanes{}, where)
		if listErr != nil {
			return listErr
		}

		if tempLaneList == nil || len(tempLaneList) <= 0 {
			// 没有泳道则跳过，日志打印记录一下
			ctx.CsmLogger().Warnf(fmt.Sprintf("not found lane, groupID is %s", lg.GroupID))
			continue
		}
		// 添加泳道
		laneList = append(laneList, tempLaneList...)
		// 添加服务列表
		lgServiceList := meta.ModelStringToServiceList(lg.ServiceList)
		needServices := findNeedServices(lgServiceList, serviceList)
		serviceList = append(serviceList, needServices...)
	}

	namespace := ""
	if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
		namespace = instance.IstioInstallNamespace
	}

	vsDelegateParseParams := laneDeploy.NewVSDelegateParseParams(constants.APIVersion16, namespace,
		serviceList, laneList, laneGroups)

	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, nil, nil, nil, vsDelegateParseParams)
	if deployErr != nil {
		return deployErr
	}

	err = deployer.CreateOrUpdateVirtualServiceForDelegate(ctx, vsFilePath)
	if err != nil {
		return err
	}

	return nil
}

// newEnvoyFiltersForLaneGroup 添加envoyFilter
func (s *Service) newEnvoyFiltersForLaneGroup(ctx context.CsmContext, laneGroupParams *meta.LaneGroupParams) error {
	if laneGroupParams.GroupID == "" || laneGroupParams.InstanceUUID == "" {
		return csmErr.NewInvalidParameterInputValueException("groupID and instanceUUID is required")
	}

	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, laneGroupParams.InstanceUUID)
	if err != nil {
		return err
	}

	efFilePath := path.Join(s.opt.LaneFilePath, constants.LaneEnvoyFilterTmpl)

	// 独立网格 crd需要提交到primary集群，托管网格crd需要提交到external集群
	externalCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, laneGroupParams.InstanceUUID, instance.InstanceType)
	if cErr != nil {
		return cErr
	}
	region := externalCluster.Region
	clusterID := externalCluster.ClusterUUID

	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterID, meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	// 为所有服务创建模板
	efParams := make([]laneDeploy.EFParseParams, 0)
	for _, serviceTmp := range laneGroupParams.ServiceList {
		// se的host不创建envoyFilter
		if serviceTmp.IsHost {
			continue
		}

		namespace := serviceTmp.Namespace

		// 托管网格，ef添加在istiod所在命名空间
		if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
			namespace = instance.IstioInstallNamespace
		}
		efTmp := laneDeploy.NewEFParseParams(laneGroupParams, constants.APIVersion16, namespace, serviceTmp.ServiceName)
		efParams = append(efParams, efTmp...)
	}

	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, nil, nil, efParams, nil)
	if deployErr != nil {
		return deployErr
	}
	err = deployer.CreateEnvoyFilter(ctx, efFilePath)
	if err != nil {
		return err
	}

	return nil
}

// newVirtualServiceForLane 添加virtualService
// 因为virtualService作用范围全局生效，所以独立网格只需要在服务所在的ns下添加一个vs即可
func (s *Service) newVirtualServiceForLane(ctx context.CsmContext, laneParams *meta.LaneParams, route *meta.RouteRule) error {
	if laneParams.GroupID == "" || laneParams.InstanceUUID == "" {
		return csmErr.NewInvalidParameterInputValueException("groupID and instanceUUID is required")
	}

	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, laneParams.InstanceUUID)
	if err != nil {
		return err
	}

	vsFilePath := path.Join(s.opt.LaneFilePath, constants.LaneVirtualServiceTmpl)
	externalCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, laneParams.InstanceUUID, instance.InstanceType)
	if cErr != nil {
		return cErr
	}
	region := externalCluster.Region
	clusterID := externalCluster.ClusterUUID
	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterID, meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	// 为所有服务创建模板
	vsParams := make([]laneDeploy.VSParseParams, 0)
	for _, serviceTmp := range laneParams.ServiceList {
		namespace := serviceTmp.Namespace
		// 托管网格，virtualService添加在istiod所在命名空间
		if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
			namespace = instance.IstioInstallNamespace
		}
		host := fmt.Sprintf("%s.%s.svc.cluster.local",
			strings.ToLower(serviceTmp.ServiceName), serviceTmp.Namespace)
		if serviceTmp.IsHost {
			host = strings.ToLower(serviceTmp.ServiceName)
		}

		vsParam := laneDeploy.VSParseParams{
			ApiVersion: constants.APIVersion16,
			Host:       host,
			Namespace:  namespace,
			SubsetName: laneParams.LaneName,
		}
		vsTmp := laneDeploy.NewVSParseParams([]meta.RouteRule{*route}, laneParams.LaneID, serviceTmp.ServiceName, vsParam)
		vsParams = append(vsParams, vsTmp)
	}

	// 非基准泳道补全virtualService
	if !laneParams.IsBase {
		where := &meta.Lanes{
			GroupID: laneParams.GroupID,
			IsBase:  csm.Bool(true),
		}
		baseLane, baseErr := s.laneModel.GetLanes(ctx, &meta.Lanes{}, where)
		if baseErr != nil {
			return csmErr.NewDBOperationException(fmt.Errorf("failed to get baseLane, groupID is %s", laneParams.GroupID))
		}
		if baseLane == nil || len(baseLane) != 1 {
			// 没有基准则跳过，日志打印记录一下
			ctx.CsmLogger().Warnf(fmt.Sprintf("not found baseLane, groupID is %s", laneParams.GroupID))
			goto createVS
		}
		// 获取需要补充的服务列表
		baseLaneParams := meta.ToLaneParams(baseLane)
		addServiceList := findNeedServices(baseLaneParams[0].ServiceList, laneParams.ServiceList)
		for _, serviceTmp := range addServiceList {
			namespace := serviceTmp.Namespace
			// 托管网格，virtualService添加在istiod所在命名空间
			if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
				namespace = instance.IstioInstallNamespace
			}
			host := fmt.Sprintf("%s.%s.svc.cluster.local",
				strings.ToLower(serviceTmp.ServiceName), serviceTmp.Namespace)
			if serviceTmp.IsHost {
				host = strings.ToLower(serviceTmp.ServiceName)
			}
			vsParam := laneDeploy.VSParseParams{
				ApiVersion: constants.APIVersion16,
				Host:       host,
				Namespace:  namespace,
				// 导流到基准泳道
				SubsetName: baseLane[0].LaneName,
			}
			vsTmp := laneDeploy.NewVSParseParams([]meta.RouteRule{*route}, laneParams.LaneID, serviceTmp.ServiceName, vsParam)
			vsParams = append(vsParams, vsTmp)
		}
	}

createVS:
	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, vsParams, nil, nil, nil)
	if deployErr != nil {
		return deployErr
	}

	err = deployer.CreateOrUpdateVirtualService(ctx, vsFilePath)
	if err != nil {
		return err
	}

	return nil
}

// updateVirtualService 修改virtualService
func (s *Service) updateVirtualService(ctx context.CsmContext, laneParams, oldLaneParam *meta.LaneParams, accountID string) error {
	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, laneParams.InstanceUUID)
	if err != nil {
		return err
	}

	istioCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, laneParams.InstanceUUID, instance.InstanceType)
	if cErr != nil {
		return cErr
	}
	region := istioCluster.Region
	clusterID := istioCluster.ClusterUUID

	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterID, meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	// 获取泳道组
	laneGroup, err := s.laneModel.GetLaneGroupByID(ctx, laneParams.InstanceUUID, laneParams.GroupID, accountID)
	if err != nil {
		return err
	}

	route := &meta.RouteRule{
		MatchRequest: meta.MatchRequest{
			Headers: []meta.Header{
				{
					Name:            laneGroup.RouteHeader,
					MatchingMode:    strings.ToLower(constants.ExactMatchMode),
					MatchingContent: laneParams.LaneName,
				},
			},
		},
	}

	vsFilePath := path.Join(s.opt.LaneFilePath, constants.LaneVirtualServiceTmpl)
	vsParams := make([]laneDeploy.VSParseParams, 0)

	// 新增ServiceList
	addServiceList := findNeedServices(laneParams.ServiceList, oldLaneParam.ServiceList)
	if len(addServiceList) > 0 {
		// 新增的service，需要将原有流向基准泳道的流量打到当前泳道
		for _, addService := range addServiceList {
			namespace := addService.Namespace
			// 托管网格，virtualService添加在istiod所在命名空间
			if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
				namespace = instance.IstioInstallNamespace
			}

			host := fmt.Sprintf("%s.%s.svc.cluster.local", strings.ToLower(addService.ServiceName), addService.Namespace)
			if addService.IsHost {
				host = strings.ToLower(addService.ServiceName)
			}
			vsParam := laneDeploy.VSParseParams{
				ApiVersion: constants.APIVersion16,
				Host:       host,
				Namespace:  namespace,
				SubsetName: laneParams.LaneName,
			}
			vsTmp := laneDeploy.NewVSParseParams([]meta.RouteRule{*route}, laneParams.LaneID, addService.ServiceName, vsParam)
			vsParams = append(vsParams, vsTmp)
		}
	}

	// 删除的ServiceList
	deleteServiceList := findNeedServices(oldLaneParam.ServiceList, laneParams.ServiceList)
	if len(deleteServiceList) > 0 {
		where := &meta.Lanes{
			GroupID: laneParams.GroupID,
			IsBase:  csm.Bool(true),
		}
		baseLane, baseErr := s.laneModel.GetLanes(ctx, &meta.Lanes{}, where)
		if baseErr != nil {
			return csmErr.NewDBOperationException(fmt.Errorf("failed to get baseLane, groupID is %s", laneParams.GroupID))
		}
		if baseLane == nil || len(baseLane) != 1 {
			// 没有基准则跳过，日志打印记录一下
			ctx.CsmLogger().Warnf(fmt.Sprintf("not found baseLane, groupID is %s", laneParams.GroupID))
			goto updateVS
		}
		for _, serviceTmp := range deleteServiceList {
			namespace := serviceTmp.Namespace
			// 托管网格，virtualService添加在istiod所在命名空间
			if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
				namespace = instance.IstioInstallNamespace
			}

			host := fmt.Sprintf("%s.%s.svc.cluster.local", strings.ToLower(serviceTmp.ServiceName), serviceTmp.Namespace)
			if serviceTmp.IsHost {
				host = strings.ToLower(serviceTmp.ServiceName)
			}
			vsParam := laneDeploy.VSParseParams{
				ApiVersion: constants.APIVersion16,
				Host:       host,
				Namespace:  namespace,
				// 导流到基准泳道
				SubsetName: baseLane[0].LaneName,
			}

			vsTmp := laneDeploy.NewVSParseParams([]meta.RouteRule{*route}, laneParams.LaneID, serviceTmp.ServiceName, vsParam)
			vsParams = append(vsParams, vsTmp)
		}
	}

updateVS:
	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, vsParams, nil, nil, nil)
	if deployErr != nil {
		return deployErr
	}

	err = deployer.CreateOrUpdateVirtualService(ctx, vsFilePath)
	if err != nil {
		return err
	}

	return nil
}

// findNeedServices 返回serviceList中对比baseServiceList缺少的服务列表
func findNeedServices(baseServiceList, serviceList []meta.ServiceListParams) []meta.ServiceListParams {
	result := make([]meta.ServiceListParams, 0)
	for _, serv := range baseServiceList {
		need := true
		//nolint
		for _, laneService := range serviceList {
			if reflect.DeepEqual(serv, laneService) {
				need = false
				break
			}
		}
		if need {
			result = append(result, serv)
		}
	}
	return result
}

func (s *Service) CreateOrUpdateDestinationRule(ctx context.CsmContext, laneParams *meta.LaneParams) error {
	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, laneParams.InstanceUUID)
	if err != nil {
		return err
	}

	drFilePath := path.Join(s.opt.LaneFilePath, constants.LaneDestinationRuleTmpl)
	// 全量DR
	drParams := make([]laneDeploy.DRParseParams, 0)
	for _, serviceTmp := range laneParams.ServiceList {
		host := fmt.Sprintf("%s.%s.svc.cluster.local", strings.ToLower(serviceTmp.ServiceName), serviceTmp.Namespace)
		if serviceTmp.IsHost {
			host = strings.ToLower(serviceTmp.ServiceName)
		}
		drTmp := laneDeploy.NewDRParseParams(constants.APIVersion16, host, laneParams.LaneID, laneParams.LaneName,
			serviceTmp.ServiceName, laneParams.LabelSelectorKey, laneParams.LabelSelectorValue)
		drParams = append(drParams, drTmp)
	}

	istioCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, laneParams.InstanceUUID, instance.InstanceType)
	if cErr != nil {
		return cErr
	}
	region := istioCluster.Region
	clusterID := istioCluster.ClusterUUID
	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterID, meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	allServiceList := laneParams.ServiceList

	// TODO 可能不需要补全，待联调确实
	// 非基准泳道补全DestinationRule
	if !laneParams.IsBase {
		where := &meta.Lanes{
			GroupID: laneParams.GroupID,
			IsBase:  csm.Bool(true),
		}
		baseLane, baseErr := s.laneModel.GetLanes(ctx, &meta.Lanes{}, where)
		if baseErr != nil {
			return csmErr.NewDBOperationException(fmt.Errorf("failed to get baseLane, groupID is %s", laneParams.GroupID))
		}
		if baseLane == nil || len(baseLane) != 1 {
			// 没有基准则跳过，日志打印记录一下
			ctx.CsmLogger().Warnf(fmt.Sprintf("not found baseLane, groupID is %s", laneParams.GroupID))
			goto UpdateDR
		}

		// 获取需要补充的服务列表
		baseLaneParams := meta.ToLaneParams(baseLane)
		addServiceList := findNeedServices(baseLaneParams[0].ServiceList, laneParams.ServiceList)
		for _, serviceTmp := range addServiceList {
			host := fmt.Sprintf("%s.%s.svc.cluster.local", strings.ToLower(serviceTmp.ServiceName), serviceTmp.Namespace)
			if serviceTmp.IsHost {
				host = strings.ToLower(serviceTmp.ServiceName)
			}
			// 没有对应版本的服务，流量导向基准泳道
			drTmp := laneDeploy.NewDRParseParams(constants.APIVersion16, host, laneParams.LaneID, laneParams.LaneName,
				serviceTmp.ServiceName, baseLaneParams[0].LabelSelectorKey, baseLaneParams[0].LabelSelectorValue)
			drParams = append(drParams, drTmp)
		}
		allServiceList = baseLaneParams[0].ServiceList
	}

UpdateDR:
	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, nil, drParams, nil, nil)
	if deployErr != nil {
		return deployErr
	}

	if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
		// 托管网格，virtualService添加在istiod所在命名空间
		namespace := instance.IstioInstallNamespace
		err = deployer.CreateOrUpdateDestinationRule(ctx, namespace, drFilePath)
		if err != nil {
			return err
		}
	} else if meta.MeshType(instance.InstanceType) == meta.StandaloneMeshType {
		// 独立集群
		for _, serviceTmp := range distinctCluster(allServiceList) {
			err = deployer.CreateOrUpdateDestinationRule(ctx, serviceTmp.Namespace, drFilePath)
			if err != nil {
				return err
			}
		}
	} else {
		return csmErr.NewServiceException(fmt.Sprintf("%s instance type is unsupported", instance.InstanceType))
	}

	return nil
}

func distinctCluster(serviceList []meta.ServiceListParams) []meta.ServiceListParams {
	result := make([]meta.ServiceListParams, 0)
	// key为 {cce集群地域}-{cce集群ID}
	clusterMap := make(map[string]bool)
	for _, clu := range serviceList {
		key := fmt.Sprintf("%s-%s", clu.ClusterRegion, clu.ClusterID)
		if clusterMap[key] {
			continue
		}
		clusterMap[key] = true
		result = append(result, clu)
	}
	return result
}

func (s *Service) GetLaneGroups(ctx context.CsmContext, accountID string) (*meta.LaneGroupParamsResponse, error) {
	instanceUUID := ctx.Param(constants.InstanceIDPathParam)
	if instanceUUID == "" {
		return nil, csmErr.NewMissingParametersException(constants.InstanceIDPathParam)
	}
	laneGroups, err := s.laneModel.GetLaneGroupsByInstanceUUID(ctx, instanceUUID, accountID)
	if err != nil {
		return nil, err
	}
	// serviceList转换
	result := make([]meta.LaneGroupParams, 0)
	for _, tmpLaneGroup := range laneGroups {
		serviceList := meta.ModelStringToServiceList(tmpLaneGroup.ServiceList)
		tmp := meta.LaneGroupParams{
			GroupID:     tmpLaneGroup.GroupID,
			GroupName:   tmpLaneGroup.GroupName,
			TraceHeader: tmpLaneGroup.TraceHeader,
			RouteHeader: tmpLaneGroup.RouteHeader,
			ServiceList: serviceList,
		}
		result = append(result, tmp)
	}

	response := &meta.LaneGroupParamsResponse{
		PageSize:   len(result),
		PageNo:     1,
		TotalCount: len(result),
		Result:     result,
	}

	return response, nil
}

func (s *Service) DeleteAllLaneGroupByInstanceUUID(ctx context.CsmContext, instanceUUID string) (err error) {
	// 删除所有泳道组
	accountID, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	laneGroups, err := s.laneModel.GetLaneGroupsByInstanceUUID(ctx, instanceUUID, accountID)
	if err != nil {
		return err
	}

	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return err
	}

	clusterModel, err := s.clusterModel.GetIstiodCluster(ctx, instanceUUID, instance.InstanceType)
	if err != nil {
		// 兼容独立网格异常脏数据场景：主集群已经被移出，但网格实例依旧存在
		// 这种场景下，主集群不存在，不需要删除其中的crd，直接返回即可
		if meta.MeshType(instance.InstanceType) == meta.StandaloneMeshType &&
			strings.Contains(err.Error(), "record not found") {
			return nil
		}
		return err
	}
	namespace := ""
	if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
		namespace = instance.IstioInstallNamespace
	}

	k8sClient, err := s.cceService.NewClient(ctx, clusterModel.Region, clusterModel.ClusterUUID, meta.MeshType(instance.InstanceType))
	if err != nil {
		// 兼容独立网格异常脏数据场景：主集群已经被移出，但网格实例依旧存在
		// 这种场景下，主集群不存在，不需要删除其中的crd，直接返回即可
		if strings.Contains(err.Error(), "ClusterNotFound") {
			return nil
		}
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, nil, nil, nil, nil)
	if deployErr != nil {
		return deployErr
	}

	serviceList := make([]meta.ServiceListParams, 0)

	for index, _ := range laneGroups {
		laneGroup := laneGroups[index]
		// 添加服务列表
		lgServiceList := meta.ModelStringToServiceList(laneGroup.ServiceList)
		needServices := findNeedServices(lgServiceList, serviceList)
		serviceList = append(serviceList, needServices...)

		// 获取所有泳道
		laneModelList, err := s.laneModel.GetLanesByGroupID(ctx, laneGroup.InstanceUUID, laneGroup.GroupID, accountID)
		if err != nil {
			return err
		}

		// 构建参数删除CRD
		laneGroupParams := laneGroup.ToLaneGroupParams()
		laneParams := meta.ToLaneParams(laneModelList)
		err = deployer.DeleteLaneGroupCRDs(ctx, laneGroupParams, laneParams, namespace)
		if err != nil {
			return err
		}

	}

	//删除所有delegate
	for _, svc := range serviceList {
		delegateNamespace := svc.Namespace
		if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
			delegateNamespace = instance.IstioInstallNamespace
		}
		name := fmt.Sprintf("vs-delegate-%s", strings.ToLower(svc.ServiceName))
		err = k8sClient.Istio().NetworkingV1alpha3().VirtualServices(delegateNamespace).Delete(kubeContext.TODO(), name, metav1.DeleteOptions{})
		if err != nil && !kubeErrors.IsNotFound(err) {
			return err
		}
	}

	return s.laneModel.DeleteAllLaneGroupByInstanceUUID(ctx, instanceUUID, accountID)
}

// DeleteLaneGroupByID 删除泳道组，并且删除其下所有的泳道和对应的CRD
func (s *Service) DeleteLaneGroupByID(ctx context.CsmContext, laneGroup *meta.LaneGroupParams, accountID string) (err error) {
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()

	laneService := s.laneModel.WithTx(dbutil.NewDB(tx))
	laneGroupModel, err := laneService.GetLaneGroupByID(ctx, laneGroup.InstanceUUID, laneGroup.GroupID, accountID)
	if err != nil {
		return err
	}

	// 获取所有泳道
	laneModelList, err := laneService.GetLanesByGroupID(ctx, laneGroup.InstanceUUID, laneGroup.GroupID, accountID)
	if err != nil {
		return err
	}

	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, laneGroup.InstanceUUID)
	if err != nil {
		return err
	}

	clusterModel, err := s.clusterModel.GetIstiodCluster(ctx, laneGroup.InstanceUUID, instance.InstanceType)
	if err != nil {
		return err
	}
	namespace := ""
	if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
		namespace = instance.IstioInstallNamespace
	}

	k8sClient, err := s.cceService.NewClient(ctx, clusterModel.Region, clusterModel.ClusterUUID, meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, nil, nil, nil, nil)
	if deployErr != nil {
		return deployErr
	}

	// 构建参数删除CRD
	serviceList := meta.ModelStringToServiceList(laneGroupModel.ServiceList)
	laneGroup.ServiceList = serviceList

	laneParams := meta.ToLaneParams(laneModelList)

	err = deployer.DeleteLaneGroupCRDs(ctx, laneGroup, laneParams, namespace)
	if err != nil {
		return err
	}

	// 删除数据库数据
	deleteErr := laneService.DeleteLaneGroup(ctx, laneGroupModel)
	if deleteErr != nil {
		return deleteErr
	}

	// 处理virtualService Delegate
	err = s.handleDeleteForVSDelegate(ctx, laneService, meta.ModelStringToServiceList(laneGroupModel.ServiceList),
		laneGroup.InstanceUUID, accountID)
	if err != nil {
		return err
	}

	tx.Commit()

	return nil
}

// GetLabelSelectorSet 获取服务标签，筛选所有pod都有的key 和 SE上的所有label（集度SE对应的pod上没有与SE对应的label，所以这里直接添加）
func (s *Service) GetLabelSelectorSet(ctx context.CsmContext, laneGroupParams *meta.LaneGroupParams) (map[string][]string, error) {
	if len(laneGroupParams.ServiceList) == 0 {
		ctx.CsmLogger().Warnf("ServiceList is empty")
		return nil, csmErr.NewInvalidParameterInputValueException("ServiceList is empty")
	}

	result := make(map[string][]string, 0)
	isFirst := true
	// 获取服务所有的pod的标签
	for _, temp := range laneGroupParams.ServiceList {
		if temp.IsHost {
			continue
		}
		k8sClient, err := s.cceService.NewClient(ctx, temp.ClusterRegion, temp.ClusterID, meta.StandaloneMeshType)
		if err != nil {
			ctx.CsmLogger().Errorf("cce NewClient error %v", err)
			return nil, err
		}
		// 用app: {服务名}
		podList, err := k8sClient.Kube().CoreV1().Pods(temp.Namespace).List(kubeContext.TODO(), metav1.ListOptions{
			LabelSelector: fmt.Sprintf("app=%s", strings.ToLower(temp.ServiceName)),
		})
		if err != nil {
			return nil, err
		}

		for _, pod := range podList.Items {
			// 第一个服务的label作为标准
			if isFirst {
				for key, value := range pod.Labels {
					if invalidPodLabelKey(key) {
						continue
					}
					if _, found := result[key]; !found {
						values := []string{value}
						result[key] = values
					} else {
						result[key] = append(result[key], value)
					}
				}
			} else {
				foundMap := make(map[string]struct{}, 0)
				for key, value := range pod.Labels {
					if _, found := result[key]; found {
						foundMap[key] = struct{}{}
						result[key] = append(result[key], value)
					}
				}
				for key, _ := range result {
					// nolint
					if _, found := foundMap[key]; !found {
						delete(result, key)
					}
				}
			}
		}
		isFirst = false
	}
	labelMap := make(map[string][]string, 0)
	// 这里获取到所有共有的key，但是value值未去重
	for key, values := range result {
		//nolint
		newValue := make([]string, 0)
		foundMap := make(map[string]struct{}, 0)
		for _, valueStr := range values {
			if _, found := foundMap[valueStr]; found {
				continue
			}
			foundMap[valueStr] = struct{}{}
			newValue = append(newValue, valueStr)
		}
		labelMap[key] = newValue
	}

	seMap, seErr := s.GetServiceEntryLabels(ctx, laneGroupParams)
	if seErr != nil {
		ctx.CsmLogger().Warnf("GetServiceEntryLabels failed, err is %s", seErr.Error())
	}

	// 添加SE部分
	for key, values := range seMap {
		//nolint
		newValue := make([]string, 0)
		if _, found := labelMap[key]; found {
			newValue = labelMap[key]
		}

		foundMap := make(map[string]struct{}, 0)
		for _, valueStr := range values {
			if _, found := foundMap[valueStr]; found {
				continue
			}
			foundMap[valueStr] = struct{}{}
			newValue = append(newValue, valueStr)
		}
		labelMap[key] = newValue
	}

	return labelMap, nil
}

func (s *Service) GetServiceEntryLabels(ctx context.CsmContext, laneGroupParams *meta.LaneGroupParams) (map[string][]string, error) {
	if len(laneGroupParams.ServiceList) == 0 {
		ctx.CsmLogger().Warnf("ServiceList is empty")
		return nil, csmErr.NewInvalidParameterInputValueException("ServiceList is empty")
	}

	result := make(map[string][]string, 0)
	isFirst := true
	// 获取所有ServiceEntry label
	for _, temp := range laneGroupParams.ServiceList {
		if !temp.IsHost {
			continue
		}
		k8sClient, err := s.cceService.NewClient(ctx, temp.ClusterRegion, temp.ClusterID, meta.StandaloneMeshType)
		if err != nil {
			ctx.CsmLogger().Errorf("cce NewClient error %v", err)
			return nil, err
		}

		// 获取所有的ServiceEntry
		seList, err := k8sClient.Istio().NetworkingV1alpha3().ServiceEntries(temp.Namespace).List(kubeContext.TODO(), metav1.ListOptions{})
		if err != nil {
			return nil, err
		}

		// label使用endpoints中的label
		for i := range seList.Items {
			// 找到根据host找到对应的se
			if !checkHost(strings.ToLower(temp.ServiceName), seList.Items[i].Spec.Hosts) {
				continue
			}
			// 第一个服务的label作为标准
			if isFirst {
				//nolint
				for _, endpoint := range seList.Items[i].Spec.Endpoints {
					// nolint
					for key, value := range endpoint.Labels {
						if invalidPodLabelKey(key) {
							continue
						}
						if _, found := result[key]; !found {
							values := []string{value}
							result[key] = values
						} else {
							result[key] = append(result[key], value)
						}
					}
				}
			} else {
				foundMap := make(map[string]struct{}, 0)
				for _, endpoint := range seList.Items[i].Spec.Endpoints {
					for key, value := range endpoint.Labels {
						if _, found := result[key]; found {
							foundMap[key] = struct{}{}
							result[key] = append(result[key], value)
						}
					}
				}
				for key, _ := range result {
					// nolint
					if _, found := foundMap[key]; !found {
						delete(result, key)
					}
				}
			}
		}
		isFirst = false
	}

	return result, nil
}

func invalidPodLabelKey(key string) bool {
	if strings.EqualFold(key, meta.InvalidKeyApp) {
		return true
	}
	return false
}

func checkHost(host string, hosts []string) bool {
	for _, temp := range hosts {
		if strings.EqualFold(host, temp) {
			return true
		}
	}
	return false
}

// ModifyLane 修改泳道。 只涉及labelSelector 和 serviceList字段，泳道名不能修改。
func (s *Service) ModifyLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) (err error) {
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	laneService := s.laneModel.WithTx(dbutil.NewDB(tx))
	laneModel, err := laneService.GetLaneByID(ctx, laneParams.LaneID, laneParams.InstanceUUID, accountID)
	if err != nil {
		return err
	}
	listLaneParam := meta.ToLaneParams([]meta.Lanes{*laneModel})
	oldLaneParam := listLaneParam[0]
	// 如果未修改，则直接返回
	if reflect.DeepEqual(oldLaneParam, laneParams) {
		tx.Commit()
		return nil
	}

	// labelSelector 和 ServiceList发生修改需要修改DestinationRule
	err = s.CreateOrUpdateDestinationRule(ctx, laneParams)
	if err != nil {
		return err
	}

	// 等待一段时间，保证控制面先同步DestinationRule
	// 修改泳道时，DestinationRule不会删除。当泳道服务删除时，也会默认创建到基线泳道的DestinationRule。
	// 所以这里保证DR变更在前，可以确保流量无损
	time.Sleep(1 * time.Second)

	// 修改CRD
	// 只有ServiceList发生变化才需要重新修改vs。
	if !reflect.DeepEqual(oldLaneParam.ServiceList, laneParams.ServiceList) {
		err = s.updateVirtualService(ctx, laneParams, &oldLaneParam, accountID)
		if err != nil {
			return err
		}
	}

	// 修改数据库记录
	update := laneModel
	update.LaneName = laneParams.LaneName
	update.LabelSelectorKey = laneParams.LabelSelectorKey
	update.LabelSelectorValue = laneParams.LabelSelectorValue
	servList := meta.ServiceListToModel(laneParams.ServiceList)
	update.ServiceList = servList

	err = laneService.ModifyLane(ctx, laneModel, update)
	if err != nil {
		return nil
	}

	// 新建delegate类型
	err = s.createOrUpdateVSDelegate(ctx, laneService, laneParams.InstanceUUID, accountID)
	if err != nil {
		return err
	}

	tx.Commit()

	return nil
}

// ModifyBaseLane 修改基准泳道
// 1、原有基准泳道设置为非基准泳道，新泳道设置为基准泳道
// 2、其他非基准泳道，修改CRD，将流量导向新基准泳道
func (s *Service) ModifyBaseLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) (err error) {
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	laneService := s.laneModel.WithTx(dbutil.NewDB(tx))
	oldLane, err := laneService.GetLaneByID(ctx, laneParams.LaneID, laneParams.InstanceUUID, accountID)
	if err != nil {
		return err
	}

	if *oldLane.IsBase {
		tx.Commit()
		return nil
	}

	// 获取泳道组
	laneGroup, err := laneService.GetLaneGroupByID(ctx, laneParams.InstanceUUID, laneParams.GroupID, accountID)
	if err != nil {
		return err
	}

	// 当前泳道包含服务与泳道组服务不一致，则不能设置为基准泳道
	laneServiceList := meta.ModelStringToServiceList(oldLane.ServiceList)
	laneGroupServiceList := meta.ModelStringToServiceList(laneGroup.ServiceList)
	sortServiceListParams(laneServiceList)
	sortServiceListParams(laneGroupServiceList)

	if !reflect.DeepEqual(laneServiceList, laneGroupServiceList) {
		ctx.CsmLogger().Info(fmt.Sprintf("lane %s can't set to base lane", laneParams.LaneID))
		return fmt.Errorf("lane %s can't set to base lane", laneParams.LaneID)
	}

	// 修改基准泳道
	listLaneParam := meta.ToLaneParams([]meta.Lanes{*oldLane})
	newBaseLaneParam := listLaneParam[0]
	// 如果未修改，则直接返回
	if reflect.DeepEqual(newBaseLaneParam, laneParams) {
		tx.Commit()
		return nil
	}

	// 找原基准泳道
	where := &meta.Lanes{
		GroupID:      laneParams.GroupID,
		IsBase:       csm.Bool(true),
		InstanceUUID: laneParams.InstanceUUID,
		Deleted:      csm.Int(0),
	}

	oldBaseLaneList, bErr := laneService.GetLanes(ctx, &meta.Lanes{}, where)
	if bErr != nil {
		return bErr
	}

	if len(oldBaseLaneList) > 1 {
		return csmErr.NewResourceConflictException(fmt.Sprintf("laneGroup %s base lane conflict", laneParams.GroupID))
	}

	// 只需要修改非基准泳道的vs，变更导向基准泳道
	search := &meta.Lanes{
		GroupID:      laneParams.GroupID,
		Deleted:      csm.Int(0),
		InstanceUUID: laneParams.InstanceUUID,
	}
	laneList, lErr := laneService.GetLanes(ctx, search, &meta.Lanes{})
	if lErr != nil {
		return bErr
	}
	for _, laneTemp := range laneList {
		if laneTemp.LaneID == laneParams.LaneID || (len(oldBaseLaneList) == 1 && laneTemp.LaneID == oldBaseLaneList[0].LaneID) {
			continue
		}
		laneTempParams := meta.ToLaneParams([]meta.Lanes{laneTemp})
		err = s.handleBaseLaneVirtualService(ctx, &laneTempParams[0], &newBaseLaneParam, accountID)
		if err != nil {
			return err
		}
	}

	// 修改数据库记录
	updateLane := oldLane
	updateLane.IsBase = csm.Bool(true)

	err = laneService.ModifyLane(ctx, oldLane, updateLane)
	if err != nil {
		return nil
	}

	if len(oldBaseLaneList) == 1 {
		updateOldBaseLane := oldBaseLaneList[0]
		updateOldBaseLane.IsBase = csm.Bool(false)
		err = laneService.ModifyLane(ctx, &oldBaseLaneList[0], &updateOldBaseLane)
		if err != nil {
			return nil
		}
	}

	tx.Commit()

	return nil
}

func sortServiceListParams(serviceList []meta.ServiceListParams) {
	sort.SliceStable(serviceList, func(i, j int) bool {
		if serviceList[i].ClusterName != serviceList[j].ClusterName {
			return serviceList[i].ClusterName < serviceList[j].ClusterName
		}
		if serviceList[i].ClusterRegion != serviceList[j].ClusterRegion {
			return serviceList[i].ClusterRegion < serviceList[j].ClusterRegion
		}
		if serviceList[i].ClusterID != serviceList[j].ClusterID {
			return serviceList[i].ClusterID < serviceList[j].ClusterID
		}
		if serviceList[i].Namespace != serviceList[j].Namespace {
			return serviceList[i].Namespace < serviceList[j].Namespace
		}
		return serviceList[i].ServiceName < serviceList[j].ServiceName
	})
	return
}

func (s *Service) handleBaseLaneVirtualService(ctx context.CsmContext, laneParams, baseLaneParams *meta.LaneParams, accountID string) error {
	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, laneParams.InstanceUUID)
	if err != nil {
		return err
	}

	istioCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, laneParams.InstanceUUID, instance.InstanceType)
	if cErr != nil {
		return cErr
	}
	region := istioCluster.Region
	clusterID := istioCluster.ClusterUUID

	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, region, clusterID, meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	// 获取泳道组
	laneGroup, err := s.laneModel.GetLaneGroupByID(ctx, laneParams.InstanceUUID, laneParams.GroupID, accountID)
	if err != nil {
		return err
	}

	route := &meta.RouteRule{
		MatchRequest: meta.MatchRequest{
			Headers: []meta.Header{
				{
					Name:            laneGroup.RouteHeader,
					MatchingMode:    strings.ToLower(constants.ExactMatchMode),
					MatchingContent: laneParams.LaneName,
				},
			},
		},
	}

	vsFilePath := path.Join(s.opt.LaneFilePath, constants.LaneVirtualServiceTmpl)
	vsParams := make([]laneDeploy.VSParseParams, 0)

	// 获取非基准泳道缺失的ServiceList
	needServiceList := findNeedServices(laneParams.ServiceList, baseLaneParams.ServiceList)
	if len(needServiceList) > 0 {
		// 需要将流量导向基准泳道
		for _, addService := range needServiceList {
			namespace := addService.Namespace
			// 托管网格，virtualService添加在istiod所在命名空间
			if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
				namespace = instance.IstioInstallNamespace
			}

			host := fmt.Sprintf("%s.%s.svc.cluster.local", strings.ToLower(addService.ServiceName), addService.Namespace)
			if addService.IsHost {
				host = strings.ToLower(addService.ServiceName)
			}
			vsParam := laneDeploy.VSParseParams{
				ApiVersion: constants.APIVersion16,
				Host:       host,
				Namespace:  namespace,
				SubsetName: baseLaneParams.LaneName,
			}
			vsTmp := laneDeploy.NewVSParseParams([]meta.RouteRule{*route}, laneParams.LaneID, addService.ServiceName, vsParam)
			vsParams = append(vsParams, vsTmp)
		}
	}

	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, vsParams, nil, nil, nil)
	if deployErr != nil {
		return deployErr
	}

	err = deployer.CreateOrUpdateVirtualService(ctx, vsFilePath)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) DeleteLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) (err error) {
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	laneService := s.laneModel.WithTx(dbutil.NewDB(tx))
	// 获取泳道组
	laneGroupModel, err := laneService.GetLaneGroupByID(ctx, laneParams.InstanceUUID, laneParams.GroupID, accountID)
	if err != nil {
		return err
	}

	// 获取泳道
	laneModel, err := laneService.GetLaneByID(ctx, laneParams.LaneID, laneParams.InstanceUUID, accountID)
	if err != nil {
		return err
	}

	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, laneParams.InstanceUUID)
	if err != nil {
		return err
	}

	clusterModel, err := s.clusterModel.GetIstiodCluster(ctx, laneParams.InstanceUUID, instance.InstanceType)
	if err != nil {
		return err
	}
	namespace := ""
	if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
		namespace = instance.IstioInstallNamespace
	}

	k8sClient, err := s.cceService.NewClient(ctx, clusterModel.Region, clusterModel.ClusterUUID, meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, nil, nil, nil, nil)
	if deployErr != nil {
		return deployErr
	}

	// 构建参数删除CRD
	deleteLaneParam := meta.ToLaneParams([]meta.Lanes{*laneModel})
	err = deployer.DeleteLaneCRDs(ctx, laneGroupModel.ToLaneGroupParams(), deleteLaneParam, namespace)
	if err != nil {
		return err
	}

	// 删除数据库数据
	deleteErr := laneService.DeleteLane(ctx, laneModel)
	if deleteErr != nil {
		return deleteErr
	}

	// 更新delegate
	deletedServices := meta.ModelStringToServiceList(laneModel.ServiceList)
	err = s.handleDeleteForVSDelegate(ctx, laneService, deletedServices, laneParams.InstanceUUID, accountID)
	if err != nil {
		return err
	}

	tx.Commit()

	return nil
}

func (s *Service) GetLanes(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) (*meta.LaneParamsResponse, error) {
	// 获取所有泳道
	where := &meta.Lanes{
		InstanceUUID: laneParams.InstanceUUID,
		GroupID:      laneParams.GroupID,
		AccountId:    accountID,
	}
	laneList, err := s.laneModel.GetLanes(ctx, &meta.Lanes{}, where)
	if err != nil {
		return nil, err
	}
	// 排序，基线泳道在第一个，其余泳道按照泳道名字排序
	sort.SliceStable(laneList, func(i, j int) bool {
		// 先按 IsBase 排序，true 在前
		if laneList[i].IsBase != nil && laneList[j].IsBase != nil {
			if *laneList[i].IsBase != *laneList[j].IsBase {
				return *laneList[i].IsBase
			}
		}

		// IsBase 相同的情况下，按 LaneName 排序
		return laneList[i].LaneName < laneList[j].LaneName
	})

	listLaneParams := meta.ToLaneParams(laneList)

	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, laneParams.InstanceUUID)
	if err != nil {
		return nil, err
	}

	istioCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, laneParams.InstanceUUID, instance.InstanceType)
	if cErr != nil {
		return nil, cErr
	}

	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, istioCluster.Region, istioCluster.ClusterUUID,
		meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return nil, err
	}

	for index, tempLane := range listLaneParams {
		routeList := make([]meta.RouteParams, 0)
		laneID := tempLane.LaneID
		// nolint
		for _, tempService := range tempLane.ServiceList {
			namespace := tempService.Namespace
			// 托管网格，virtualService添加在istiod所在命名空间
			if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
				namespace = instance.IstioInstallNamespace
			}
			// 从api server中获取vs，name格式：vs-{laneID}-{serviceName}
			vsName := fmt.Sprintf("vs-%s-%s", laneID, tempService.ServiceName)
			vs, err := k8sClient.Istio().NetworkingV1alpha3().VirtualServices(namespace).Get(kubeContext.TODO(),
				vsName, metav1.GetOptions{})
			if err != nil && !kubeErrors.IsNotFound(err) {
				ctx.CsmLogger().Warnf("get vs %s failed, err is %s", vsName, err.Error())
				continue
			}
			tempRoute := meta.RouteParams{
				ClusterRegion: tempService.ClusterRegion,
				ClusterID:     tempService.ClusterID,
				Namespace:     tempService.Namespace,
				ServiceName:   tempService.ServiceName,
			}
			rules := handleVSToRouteRules(vs)
			if rules == nil || len(rules) == 0 {
				continue
			}
			tempRoute.Rules = rules
			routeList = append(routeList, tempRoute)
		}
		listLaneParams[index].RouteList = routeList
	}

	result := &meta.LaneParamsResponse{
		Result:     listLaneParams,
		PageNo:     1,
		PageSize:   len(listLaneParams),
		TotalCount: len(listLaneParams),
	}

	return result, nil
}

func handleVSToRouteRules(vs *v1alpha3.VirtualService) []meta.RouteRule {
	if vs == nil || vs.Spec.Http == nil || len(vs.Spec.Http) == 0 {
		return nil
	}
	rules := make([]meta.RouteRule, 0)
	// 只处理http
	for _, httpRoute := range vs.Spec.Http {
		// 没有Match，为默认规则
		if httpRoute == nil || len(httpRoute.Match) == 0 {
			continue
		}

		// nolint
		for _, match := range httpRoute.Match {
			// match.name为空，则说明当前这条引流规则是默认添加的。
			if match == nil || match.Name == "" {
				continue
			}
			if match.GetHeaders() == nil || len(match.GetHeaders()) == 0 {
				continue
			}
			temp := meta.MatchRequest{}
			headers := make([]meta.Header, 0)
			// header加载
			for key, value := range match.GetHeaders() {
				if value == nil {
					continue
				}
				header := meta.Header{}
				if value.GetRegex() != "" {
					header.MatchingContent = value.GetRegex()
					header.MatchingMode = constants.RegexMatchMode
				} else if value.GetExact() != "" {
					header.MatchingContent = value.GetExact()
					header.MatchingMode = constants.ExactMatchMode
				} else if value.GetPrefix() != "" {
					header.MatchingContent = value.GetPrefix()
					header.MatchingMode = constants.PrefixMatchMode
				}
				header.Name = key
				headers = append(headers, header)
			}
			temp.Headers = headers
			temp.RouteName = match.Name
			// uri存在
			if match.GetUri() != nil {
				if match.Uri.GetRegex() != "" {
					regex := match.Uri.GetRegex()
					temp.Uri.MatchingContent = regex
					temp.Uri.MatchingMode = constants.RegexMatchMode
					temp.Uri.Enabled = true
				} else if match.Uri.GetExact() != "" {
					temp.Uri.MatchingContent = match.Uri.GetExact()
					temp.Uri.MatchingMode = constants.ExactMatchMode
					temp.Uri.Enabled = true
				} else if match.Uri.GetPrefix() != "" {
					temp.Uri.MatchingContent = match.Uri.GetPrefix()
					temp.Uri.MatchingMode = constants.PrefixMatchMode
					temp.Uri.Enabled = true
				}
			}

			// TODO 处理Destination
			rule := meta.RouteRule{
				MatchRequest: temp,
			}

			rules = append(rules, rule)
		}
	}

	return rules
}

func (s *Service) NewRoute(ctx context.CsmContext, routeParams *meta.RouteParams, accountID string) error {
	// 先获取VS，查看是否存在存量的，存在则添加到末尾，不存在则新建
	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, routeParams.InstanceUUID)
	if err != nil {
		return err
	}

	istioCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, routeParams.InstanceUUID, instance.InstanceType)
	if cErr != nil {
		return cErr
	}

	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, istioCluster.Region, istioCluster.ClusterUUID,
		meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	namespace := routeParams.Namespace
	// 托管网格，virtualService添加在istiod所在命名空间
	if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
		namespace = instance.IstioInstallNamespace
	}
	// 从api server中获取vs，name格式：vs-{laneID}-{serviceName}
	vsName := fmt.Sprintf("vs-%s-%s", strings.ToLower(routeParams.LaneID), strings.ToLower(routeParams.ServiceName))
	vs, err := k8sClient.Istio().NetworkingV1alpha3().VirtualServices(namespace).Get(kubeContext.TODO(),
		vsName, metav1.GetOptions{})
	if err != nil && !kubeErrors.IsNotFound(err) {
		ctx.CsmLogger().Warnf("get vs %s failed, err is %s", vsName, err.Error())
		return err
	}
	routeRules := make([]meta.RouteRule, 0)
	if vs != nil {
		rules := handleVSToRouteRules(vs)
		routeRules = append(routeRules, rules...)
	}

	routeRules = append(routeRules, routeParams.Rules...)

	// 获取泳道信息
	laneModel, err := s.laneModel.GetLaneByID(ctx, routeParams.LaneID, routeParams.InstanceUUID, accountID)
	if err != nil {
		return err
	}
	vsParams := make([]laneDeploy.VSParseParams, 0)
	host := fmt.Sprintf("%s.%s.svc.cluster.local", strings.ToLower(routeParams.ServiceName), routeParams.Namespace)
	if routeParams.IsHost {
		host = strings.ToLower(routeParams.ServiceName)
	}
	vsParam := laneDeploy.VSParseParams{
		ApiVersion: constants.APIVersion16,
		Host:       host,
		Namespace:  namespace,
		SubsetName: laneModel.LaneName,
	}

	// 添加默认的引流匹配规则
	laneGroup, err := s.laneModel.GetLaneGroupByID(ctx, routeParams.InstanceUUID, routeParams.GroupID, accountID)
	if err != nil {
		return err
	}

	defaultRoute := meta.RouteRule{
		MatchRequest: meta.MatchRequest{
			Headers: []meta.Header{
				{
					Name:            laneGroup.RouteHeader,
					MatchingMode:    constants.ExactMatchMode,
					MatchingContent: laneModel.LaneName,
				},
			},
		},
	}

	routeRules = append(routeRules, defaultRoute)

	vsTmp := laneDeploy.NewVSParseParams(routeRules, routeParams.LaneID, routeParams.ServiceName, vsParam)
	vsParams = append(vsParams, vsTmp)

	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, vsParams, nil, nil, nil)
	if deployErr != nil {
		return deployErr
	}

	vsFilePath := path.Join(s.opt.LaneFilePath, constants.LaneVirtualServiceTmpl)
	err = deployer.CreateOrUpdateVirtualService(ctx, vsFilePath)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) ModifyRoute(ctx context.CsmContext, routeParams *meta.RouteParams, accountID string) error {
	// 先获取VS，查看是否存在存量的，存在则添加到末尾，不存在则新建
	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, routeParams.InstanceUUID)
	if err != nil {
		return err
	}

	istioCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, routeParams.InstanceUUID, instance.InstanceType)
	if cErr != nil {
		return cErr
	}

	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, istioCluster.Region, istioCluster.ClusterUUID,
		meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	namespace := routeParams.Namespace
	// 托管网格，virtualService添加在istiod所在命名空间
	if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
		namespace = instance.IstioInstallNamespace
	}
	// 获取泳道组
	laneGroup, err := s.laneModel.GetLaneGroupByID(ctx, routeParams.InstanceUUID, routeParams.GroupID, accountID)
	if err != nil {
		return err
	}

	// 获取泳道信息
	laneModel, err := s.laneModel.GetLaneByID(ctx, routeParams.LaneID, routeParams.InstanceUUID, accountID)
	if err != nil {
		return err
	}
	vsParams := make([]laneDeploy.VSParseParams, 0)
	host := fmt.Sprintf("%s.%s.svc.cluster.local", strings.ToLower(routeParams.ServiceName), routeParams.Namespace)
	if routeParams.IsHost {
		host = strings.ToLower(routeParams.ServiceName)
	}
	vsParam := laneDeploy.VSParseParams{
		ApiVersion: constants.APIVersion16,
		Host:       host,
		Namespace:  namespace,
		SubsetName: laneModel.LaneName,
	}
	// 目前只有处理第一个rule
	// 添加上默认的引流规则
	defaultRoute := meta.RouteRule{
		MatchRequest: meta.MatchRequest{
			Headers: []meta.Header{
				{
					Name:            laneGroup.RouteHeader,
					MatchingMode:    constants.ExactMatchMode,
					MatchingContent: laneModel.LaneName,
				},
			},
		}}

	routeParams.Rules = append(routeParams.Rules, defaultRoute)
	vsTmp := laneDeploy.NewVSParseParams(routeParams.Rules, routeParams.LaneID, routeParams.ServiceName, vsParam)
	vsParams = append(vsParams, vsTmp)

	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, vsParams, nil, nil, nil)
	if deployErr != nil {
		return deployErr
	}

	vsFilePath := path.Join(s.opt.LaneFilePath, constants.LaneVirtualServiceTmpl)
	err = deployer.CreateOrUpdateVirtualService(ctx, vsFilePath)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) GetRouteRules(ctx context.CsmContext, routeParams *meta.RouteParams) ([]meta.RouteParams, error) {
	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, routeParams.InstanceUUID)
	if err != nil {
		return nil, err
	}

	istioCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, routeParams.InstanceUUID, instance.InstanceType)
	if cErr != nil {
		return nil, cErr
	}

	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, istioCluster.Region, istioCluster.ClusterUUID,
		meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return nil, err
	}

	namespace := routeParams.Namespace
	// 托管网格，virtualService添加在istiod所在命名空间
	if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
		namespace = instance.IstioInstallNamespace
	}
	// 从api server中获取vs，name格式：vs-{laneID}-{serviceName}
	vsName := fmt.Sprintf("vs-%s-%s", strings.ToLower(routeParams.LaneID), strings.ToLower(routeParams.ServiceName))
	vs, err := k8sClient.Istio().NetworkingV1alpha3().VirtualServices(namespace).Get(kubeContext.TODO(),
		vsName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Warnf("get vs %s failed, err is %s", vsName, err.Error())
		return nil, err
	}
	routeRules := make([]meta.RouteRule, 0)
	if vs != nil {
		rules := handleVSToRouteRules(vs)
		routeRules = append(routeRules, rules...)
	}

	routeParam := meta.RouteParams{
		ClusterRegion: routeParams.ClusterRegion,
		ClusterID:     routeParams.ClusterID,
		Namespace:     routeParams.Namespace,
		ServiceName:   routeParams.ServiceName,
		Rules:         routeRules,
	}

	// TODO 待支持多service
	result := []meta.RouteParams{routeParam}
	return result, nil
}

// DeleteRoute 删除引流规则：用默认的VS覆盖之前的VS
func (s *Service) DeleteRoute(ctx context.CsmContext, routeParams *meta.RouteParams, accountID string) error {
	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, routeParams.InstanceUUID)
	if err != nil {
		return err
	}

	istioCluster, cErr := s.clusterModel.GetIstiodCluster(ctx, routeParams.InstanceUUID, instance.InstanceType)
	if cErr != nil {
		return cErr
	}

	// 获取对应集群 kubeClient
	k8sClient, err := s.cceService.NewClient(ctx, istioCluster.Region, istioCluster.ClusterUUID,
		meta.MeshType(instance.InstanceType))
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}

	// 获取泳道信息
	laneModel, err := s.laneModel.GetLaneByID(ctx, routeParams.LaneID, routeParams.InstanceUUID, accountID)
	if err != nil {
		return err
	}
	// 添加默认的引流匹配规则
	laneGroup, err := s.laneModel.GetLaneGroupByID(ctx, routeParams.InstanceUUID, routeParams.GroupID, accountID)
	if err != nil {
		return err
	}

	defaultRoute := meta.RouteRule{
		MatchRequest: meta.MatchRequest{
			Headers: []meta.Header{
				{
					Name:            laneGroup.RouteHeader,
					MatchingMode:    constants.ExactMatchMode,
					MatchingContent: laneModel.LaneName,
				},
			},
		},
	}

	vsParams := make([]laneDeploy.VSParseParams, 0)

	host := fmt.Sprintf("%s.%s.svc.cluster.local", routeParams.ServiceName, routeParams.Namespace)
	if routeParams.IsHost {
		host = strings.ToLower(routeParams.ServiceName)
	}

	namespace := routeParams.Namespace
	// 托管网格，virtualService添加在istiod所在命名空间
	if meta.MeshType(instance.InstanceType) == meta.HostingMeshType {
		namespace = instance.IstioInstallNamespace
	}

	vsParam := laneDeploy.VSParseParams{
		ApiVersion: constants.APIVersion16,
		Host:       host,
		Namespace:  namespace,
		SubsetName: laneModel.LaneName,
	}

	vsTmp := laneDeploy.NewVSParseParams([]meta.RouteRule{defaultRoute}, routeParams.LaneID, routeParams.ServiceName, vsParam)
	vsParams = append(vsParams, vsTmp)

	deployer, deployErr := laneDeploy.NewDeploy(k8sClient, vsParams, nil, nil, nil)
	if deployErr != nil {
		return deployErr
	}

	vsFilePath := path.Join(s.opt.LaneFilePath, constants.LaneVirtualServiceTmpl)
	err = deployer.CreateOrUpdateVirtualService(ctx, vsFilePath)
	if err != nil {
		return err
	}

	return nil
}

// GetServiceList 获取当前csm实例所有纳管集群的服务和serviceEntry，支持集群和namespace筛选。
/** 获取逻辑：获取当前实例纳管的所有集群信息，然后获取所有的k8s service 和 ServiceEntry信息。
 *  具体获取逻辑按照网格模式阐述如下：
 *  独立网格：k8s service 获取所有纳管集群；ServiceEntry 只获取primary集群。
 *  托管网格：k8s service 获取external集群的网关 + remote集群的k8s service；
 *           ServiceEntry 只获取external集群的istio-system-{Instance-UUID} namespace下的。
 */
func (s *Service) GetServiceList(ctx context.CsmContext, serviceListParams *meta.LaneGroupServiceListParams) (*meta.LaneServiceListResponse, error) {
	if serviceListParams == nil {
		return nil, csmErr.NewInvalidParameterInputValueException("invalid request params")
	}
	clusterList, err := s.clusterModel.GetAllClusterByInstanceUUID(ctx, serviceListParams.InstanceUUID)
	if err != nil {
		return nil, err
	}

	// 获取网格类型
	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, serviceListParams.InstanceUUID)
	if err != nil {
		return nil, err
	}
	meshType := meta.MeshType(instance.InstanceType)

	result := make([]meta.ServiceListParams, 0)

	var wg sync.WaitGroup
	var mutex sync.Mutex
	for _, mc := range *clusterList {
		// 支持特定集群筛选
		if serviceListParams.ClusterRegion != "" && serviceListParams.ClusterID != "" {
			if !strings.EqualFold(mc.Region, serviceListParams.ClusterRegion) || !strings.EqualFold(mc.ClusterUUID, serviceListParams.ClusterID) {
				continue
			}
		}
		wg.Add(1)
		go func(mc meta.Cluster, result *[]meta.ServiceListParams, mutex *sync.Mutex) {
			cxt, cancel := kubeContext.WithTimeout(kubeContext.Background(), constants.KubeTimeout)
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Errorf("goroutine exited abnormally because: ", e)
				}
				cancel()
				wg.Done()
			}()

			// 默认全量获取
			namespace := ""
			// 支持namespace筛选
			if serviceListParams.Namespace != "" {
				namespace = serviceListParams.Namespace
			} else if meshType == meta.HostingMeshType && mc.ClusterType == string(meta.ClusterTypeExternal) {
				// 托管网格的external集群只查询指定ns下的服务
				namespace = fmt.Sprintf("istio-system-%s", instance.InstanceUUID)
			}

			// 获取adminKubeConfig，只有集群为external类型的集群，需要用aksk获取
			k8sClientType := meta.StandaloneMeshType
			if mc.ClusterType == string(meta.ClusterTypeExternal) {
				k8sClientType = meta.HostingMeshType
			}
			// TODO NewClient应该与cluster类型相关而不是与网格类型相关。
			client, err := s.cceService.NewClient(ctx, mc.Region, mc.ClusterUUID, k8sClientType)
			if err != nil {
				return
			}

			// get k8s services
			k8sServices, err := client.Kube().CoreV1().Services(namespace).List(cxt, metav1.ListOptions{})
			if err != nil {
				return
			}

			clusterResult := make([]meta.ServiceListParams, 0)
			for _, ks := range k8sServices.Items {
				// 过滤掉istiod
				if ks.Name == "istiod" {
					continue
				}
				temp := meta.ServiceListParams{
					Namespace:     ks.Namespace,
					ClusterID:     mc.ClusterUUID,
					ClusterName:   mc.ClusterName,
					ClusterRegion: mc.Region,
					ServiceName:   ks.Name,
					IsHost:        false,
				}
				clusterResult = append(clusterResult, temp)
			}

			// 获取serviceEntry，集度目前使用
			// ServiceEntry 只获取primary集群 和 external集群
			if mc.ClusterType == string(meta.ClusterTypeExternal) || mc.ClusterType == string(meta.ClusterTypePrimary) {
				seServices, err := client.Istio().NetworkingV1alpha3().ServiceEntries(namespace).List(cxt, metav1.ListOptions{})
				if err != nil {
					return
				}
				for i := range seServices.Items {
					if seServices.Items[i].Spec.Hosts == nil {
						continue
					}
					//nolint
					for index := range seServices.Items[i].Spec.Hosts {
						host := seServices.Items[i].Spec.Hosts[index]
						temp := meta.ServiceListParams{
							Namespace:     seServices.Items[i].Namespace,
							ClusterID:     mc.ClusterUUID,
							ClusterName:   mc.ClusterName,
							ClusterRegion: mc.Region,
							ServiceName:   host,
							IsHost:        true,
						}
						clusterResult = append(clusterResult, temp)
					}
				}
			}

			mutex.Lock()
			*result = append(*result, clusterResult...)
			mutex.Unlock()

		}(mc, &result, &mutex)
	}
	wg.Wait()

	// 按照ClusterName、Namespace和ServiceName依次排序
	sort.SliceStable(result, func(i, j int) bool {
		if result[i].ClusterName != result[j].ClusterName {
			return result[i].ClusterName < result[j].ClusterName
		}
		if result[i].Namespace != result[j].Namespace {
			return result[i].Namespace < result[j].Namespace
		}
		return result[i].ServiceName < result[j].ServiceName
	})

	response := &meta.LaneServiceListResponse{
		Result:     result,
		TotalCount: len(result),
	}
	if serviceListParams.PageNo <= 0 || serviceListParams.PageSize <= 0 {
		response.PageNo = 1
		response.PageSize = len(result)
	} else {
		response.PageNo = serviceListParams.PageNo
		response.PageSize = serviceListParams.PageSize
		startNo := (serviceListParams.PageNo - 1) * serviceListParams.PageSize
		endNo := serviceListParams.PageNo * serviceListParams.PageSize
		endNo = util.MinInt(len(result), endNo)
		response.Result = result[startNo:endNo]
	}

	return response, nil
}
