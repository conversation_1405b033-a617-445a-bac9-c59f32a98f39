package lane

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	NewLaneGroup(ctx context.CsmContext, laneGroupParams *meta.LaneGroupParams, accountID string) error
	GetLaneGroups(ctx context.CsmContext, accountID string) (*meta.LaneGroupParamsResponse, error)
	DeleteLaneGroupByID(ctx context.CsmContext, laneGroup *meta.LaneGroupParams, accountID string) error
	DeleteAllLaneGroupByInstanceUUID(ctx context.CsmContext, instanceUUID string) error
	GetServiceList(ctx context.CsmContext, serviceListParams *meta.LaneGroupServiceListParams) (*meta.LaneServiceListResponse, error)

	NewLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) error
	GetLabelSelectorSet(ctx context.CsmContext, laneGroupParams *meta.LaneGroupParams) (map[string][]string, error)
	ModifyLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) (err error)
	ModifyBaseLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) (err error)
	DeleteLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) (err error)
	GetLanes(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) (*meta.LaneParamsResponse, error)

	NewRoute(ctx context.CsmContext, routeParams *meta.RouteParams, accountID string) error
	ModifyRoute(ctx context.CsmContext, routeParams *meta.RouteParams, accountID string) error
	GetRouteRules(ctx context.CsmContext, routeParams *meta.RouteParams) ([]meta.RouteParams, error)
	DeleteRoute(ctx context.CsmContext, routeParams *meta.RouteParams, accountID string) error
}
