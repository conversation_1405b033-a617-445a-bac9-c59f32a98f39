package lane

import (
	kubeContext "context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"path"
	"path/filepath"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	networkingv1alpha3 "istio.io/api/networking/v1alpha3"
	"istio.io/client-go/pkg/apis/networking/v1alpha3"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	mockCluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	mockInstance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	mockLane "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/lane/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

var (
	pwd, _   = os.Getwd()
	testPath = path.Join(pwd, "../../../", constants.BaseLanePath)

	mockDB, _  = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx, _ = ctxCsm.NewCsmContextMock()

	instanceUUID               = "test"
	instanceName               = "test"
	instanceType               = "standalone"
	instanceHostingType        = "hosting"
	accountId                  = "1"
	discoverySelectorEnabled   = true
	discoverySelectorLabels    = "{\"user\": \"test\"}"
	discoverySelectorLabelsMap = map[string]string{"user": "test"}
	istioInstallNamespace      = "test-ns"
	instanceManageScope        = "cluster"
	clusterUUID                = "test-1"
	meshInstanceId             = "test01"

	namespace   = "istio-system"
	name        = "test"
	clusterID   = "test-123456"
	clusterName = "istio-test01"
	clusterType = "standalone"
	region      = "bj"

	istioVersion = "1.16.5"
	status       = "status"
	testRegion   = "bj"
	user         = &sdkIAM.User{
		ID:   "1",
		Name: "test-user",
		Domain: sdkIAM.UserDomain{
			ID:   accountId,
			Name: "aaaa",
		},
	}
)

func TestNewLaneGroup(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	pwd, _ := os.Getwd()
	testPath := path.Join(pwd, "../../../", constants.BaseLanePath)

	tests := []struct {
		name            string
		laneGroupParams *meta.LaneGroupParams
		lanes           []meta.Lanes
		instanceType    string
		success         bool
	}{
		{
			name:            "success",
			laneGroupParams: buildLaneGroupParams(),
			lanes:           []meta.Lanes{*buildLanes(true)},
			instanceType:    instanceType,
			success:         true,
		},
		{
			name:            "hosting-success",
			laneGroupParams: buildLaneGroupParams(),
			lanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(false),
			}},
			instanceType: instanceHostingType,
			success:      true,
		},
	}
	for _, tt := range tests {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCService,
				laneModel:      mockLaneModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockLaneModel.EXPECT().WithTx(gomock.Any()).Return(mockLaneModel).AnyTimes()
			mockLaneModel.EXPECT().NewLaneGroup(mockCtx, gomock.Any()).Return(nil).AnyTimes()
			mockLaneModel.EXPECT().NewLane(mockCtx, gomock.Any()).Return(nil).AnyTimes()
			mockLaneModel.EXPECT().GenerateLaneGroupID().Return("1111", nil).AnyTimes()
			mockLaneModel.EXPECT().GenerateLaneID().Return("2222", nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLaneGroups(), nil).AnyTimes()
			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupsByInstanceUUID(mockCtx, gomock.Any(), gomock.Any()).AnyTimes().Return(
				[]meta.LaneGroups{*buildLaneGroups()}, nil)

			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()

			err := s.NewLaneGroup(mockCtx, tt.laneGroupParams, "acc")
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestNewLane(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	pwd, _ := os.Getwd()
	testPath := path.Join(pwd, "../../../", constants.BaseLanePath)

	tests := []struct {
		name         string
		laneParams   *meta.LaneParams
		lanes        []meta.Lanes
		instanceType string
		success      bool
	}{
		{
			name:         "success",
			laneParams:   buildLaneParams(),
			lanes:        []meta.Lanes{*buildLanes(true)},
			instanceType: instanceType,
			success:      true,
		},
		{
			name: "hosting-success",
			laneParams: &meta.LaneParams{
				InstanceUUID:       "instance-uuid",
				GroupID:            "group-id",
				LaneID:             "lane-id",
				LaneName:           "lane-name",
				LabelSelectorKey:   "selector-key",
				LabelSelectorValue: "selector-value",
				ServiceList:        buildServiceListParams(),
				IsBase:             false,
			},
			lanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(false),
			}},
			instanceType: instanceHostingType,
			success:      true,
		},
	}
	for _, tt := range tests {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCService,
				laneModel:      mockLaneModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockLaneModel.EXPECT().NewLane(mockCtx, gomock.Any()).Return(nil).AnyTimes()
			mockLaneModel.EXPECT().GenerateLaneID().Return("2222", nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLaneGroups(), nil).AnyTimes()
			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupsByInstanceUUID(mockCtx, gomock.Any(), gomock.Any()).AnyTimes().Return(
				[]meta.LaneGroups{*buildLaneGroups()}, nil)

			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()

			err := s.NewLane(mockCtx, tt.laneParams, "acc")
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestModifyLane(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	pwd, _ := os.Getwd()
	testPath := path.Join(pwd, "../../../", constants.BaseLanePath)

	tests := []struct {
		name         string
		laneParams   *meta.LaneParams
		lanes        []meta.Lanes
		instanceType string
		success      bool
	}{
		{
			name:         "success",
			laneParams:   buildLaneParams(),
			lanes:        []meta.Lanes{*buildLanes(true)},
			instanceType: instanceType,
			success:      true,
		},
		{
			name: "hosting-success",
			laneParams: &meta.LaneParams{
				InstanceUUID:       "instance-uuid",
				GroupID:            "group-id",
				LaneID:             "lane-id",
				LaneName:           "lane-name",
				LabelSelectorKey:   "selector-key",
				LabelSelectorValue: "selector-value",
				ServiceList:        buildServiceListParams(),
				IsBase:             false,
			},
			lanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(false),
			}},
			instanceType: instanceHostingType,
			success:      true,
		},
	}
	for _, tt := range tests {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCService,
				laneModel:      mockLaneModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockLaneModel.EXPECT().WithTx(gomock.Any()).Return(mockLaneModel).AnyTimes()
			mockLaneModel.EXPECT().GetLaneByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(buildLanes(true), nil)
			mockLaneModel.EXPECT().NewLane(mockCtx, gomock.Any()).Return(nil).AnyTimes()
			mockLaneModel.EXPECT().GenerateLaneID().Return("2222", nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLaneGroups(), nil).AnyTimes()
			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().ModifyLane(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupsByInstanceUUID(mockCtx, gomock.Any(), gomock.Any()).AnyTimes().Return(
				[]meta.LaneGroups{*buildLaneGroups()}, nil)

			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()

			err := s.ModifyLane(mockCtx, tt.laneParams, "acc")
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestGetLaneGroups(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	pwd, _ := os.Getwd()
	testPath := path.Join(pwd, "../../../", constants.BaseLanePath)

	tests := []struct {
		name       string
		laneGroups []meta.LaneGroups
		success    bool
	}{
		{
			name:       "success",
			laneGroups: []meta.LaneGroups{*buildLaneGroups()},
			success:    true,
		},
	}
	for _, tt := range tests {
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:       NewOption(mockDB),
				laneModel: mockLaneModel,
			}
			s.opt.LaneFilePath = testPath
			mockCtx.SetParamNames(constants.InstanceIDPathParam)
			mockCtx.SetParamValues("aa")

			mockLaneModel.EXPECT().GetLaneGroupsByInstanceUUID(mockCtx, gomock.Any(), gomock.Any()).Return(tt.laneGroups, nil).AnyTimes()

			_, err := s.GetLaneGroups(mockCtx, accountId)
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestDeleteLaneGroupByID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name         string
		laneGroups   *meta.LaneGroups
		lanes        []meta.Lanes
		instanceType string
		success      bool
	}{
		{
			name:         "success",
			laneGroups:   buildLaneGroups(),
			lanes:        []meta.Lanes{*buildLanes(true)},
			instanceType: string(meta.HostingMeshType),
			success:      true,
		},
	}
	for _, tt := range tests {
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				laneModel:      mockLaneModel,
				instancesModel: mockInstanceModel,
				cceService:     mockCService,
				clusterModel:   mockClusterModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockCtx.SetParamNames(constants.InstanceIDPathParam)
			mockCtx.SetParamValues("aa")

			mockLaneModel.EXPECT().WithTx(gomock.Any()).Return(mockLaneModel).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.laneGroups, nil).AnyTimes()
			mockLaneModel.EXPECT().GetLanesByGroupID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().DeleteLaneGroup(mockCtx, gomock.Any()).Return(nil).AnyTimes()
			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()
			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupsByInstanceUUID(mockCtx, gomock.Any(), gomock.Any()).AnyTimes().Return(
				[]meta.LaneGroups{*buildLaneGroups()}, nil)

			err := s.DeleteLaneGroupByID(mockCtx, buildLaneGroupParams(), accountId)
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestDeleteAllLaneGroupByInstanceUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name          string
		laneGroups    *meta.LaneGroups
		laneGroupList []meta.LaneGroups
		lanes         []meta.Lanes
		instanceType  string
		success       bool
	}{
		{
			name:          "success",
			laneGroups:    buildLaneGroups(),
			laneGroupList: []meta.LaneGroups{*buildLaneGroups()},
			lanes:         []meta.Lanes{*buildLanes(true)},
			instanceType:  string(meta.HostingMeshType),
			success:       true,
		},
	}
	for _, tt := range tests {
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				laneModel:      mockLaneModel,
				instancesModel: mockInstanceModel,
				cceService:     mockCService,
				clusterModel:   mockClusterModel,
			}
			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.Set(reg.ContextRegion, "bj")

			mockCtx := ctxCsm.NewCsmContext(c)
			mockCtx.Set("User", user)
			mockCtx.Set(reg.ContextRegion, testRegion)
			mockCtx.SetParamNames(constants.InstanceIDPathParam, constants.LaneGroupIDParam, constants.LaneIDParam)
			mockCtx.SetParamValues("inst-1", "group-1", "lane-1")
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockCtx.SetParamNames(constants.InstanceIDPathParam)
			mockCtx.SetParamValues("aa")

			mockLaneModel.EXPECT().GetLaneGroupsByInstanceUUID(mockCtx, gomock.Any(), gomock.Any()).Return(tt.laneGroupList, nil).AnyTimes()

			mockLaneModel.EXPECT().WithTx(gomock.Any()).Return(mockLaneModel).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.laneGroups, nil).AnyTimes()
			mockLaneModel.EXPECT().GetLanesByGroupID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().DeleteAllLaneGroupByInstanceUUID(mockCtx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()
			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()

			err := s.DeleteAllLaneGroupByInstanceUUID(mockCtx, instanceUUID)
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestDeleteLane(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name         string
		laneGroups   *meta.LaneGroups
		lanes        []meta.Lanes
		instanceType string
		success      bool
	}{
		{
			name:       "success",
			laneGroups: buildLaneGroups(),
			lanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(true),
			}},
			instanceType: string(meta.HostingMeshType),
			success:      true,
		},
	}
	for _, tt := range tests {
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				laneModel:      mockLaneModel,
				instancesModel: mockInstanceModel,
				cceService:     mockCService,
				clusterModel:   mockClusterModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockCtx.SetParamNames(constants.InstanceIDPathParam)
			mockCtx.SetParamValues("aa")

			mockLaneModel.EXPECT().WithTx(gomock.Any()).Return(mockLaneModel).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.laneGroups, nil).AnyTimes()
			mockLaneModel.EXPECT().GetLanesByGroupID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().DeleteLane(mockCtx, gomock.Any()).Return(nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLanes(true), nil).AnyTimes()
			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupsByInstanceUUID(mockCtx, gomock.Any(), gomock.Any()).AnyTimes().Return(
				[]meta.LaneGroups{*buildLaneGroups()}, nil)

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()
			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()

			err := s.DeleteLane(mockCtx, buildLaneParams(), accountId)
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestGetLabelSelectorSet(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name       string
		laneGroups *meta.LaneGroups
		success    bool
	}{
		{
			name:       "success",
			laneGroups: buildLaneGroups(),
			success:    true,
		},
		{
			name: "success get se",
			laneGroups: &meta.LaneGroups{
				GroupID:     "group-id",
				GroupName:   "group-name",
				TraceHeader: "x-trace-header",
				RouteHeader: "x-route-header",
				ServiceList: "bj/name/cce-x/default/hello.con/true",
			},
			success: true,
		},
	}
	for _, tt := range tests {
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				laneModel:      mockLaneModel,
				instancesModel: mockInstanceModel,
				cceService:     mockCService,
				clusterModel:   mockClusterModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()

			pod := &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "service1",
					},
				},
			}

			podList := &v1.PodList{
				Items: []v1.Pod{*pod},
			}
			_, _ = fakeClient.Kube().CoreV1().Pods("namespace1").Create(kubeContext.TODO(), pod, metav1.CreateOptions{})
			if podList != nil {
				podList, _ = fakeClient.Kube().CoreV1().Pods("test").List(kubeContext.TODO(), metav1.ListOptions{
					LabelSelector: fmt.Sprintf("app=32ss"),
				})
			}
			endpoint := &networkingv1alpha3.WorkloadEntry{
				Labels: map[string]string{"color": "default"},
			}
			invEndpoint := &networkingv1alpha3.WorkloadEntry{
				Labels: map[string]string{"version": "v4", "color": "ary"},
			}
			endpoints := make([]*networkingv1alpha3.WorkloadEntry, 0)
			endpoints = append(endpoints, endpoint)
			endpoints = append(endpoints, invEndpoint)
			seCRD := &v1alpha3.ServiceEntry{
				ObjectMeta: metav1.ObjectMeta{
					Name: "vs-lane-id1111-3211s",
				},
				Spec: networkingv1alpha3.ServiceEntry{
					Hosts:     []string{"hello.con"},
					Endpoints: endpoints,
				},
			}
			_, _ = fakeClient.Istio().NetworkingV1alpha3().ServiceEntries("namespace1").Create(kubeContext.TODO(), seCRD, metav1.CreateOptions{})

			se2CRD := &v1alpha3.ServiceEntry{
				ObjectMeta: metav1.ObjectMeta{
					Name: "vs-lane-id1111-3s",
				},
				Spec: networkingv1alpha3.ServiceEntry{
					Hosts:     []string{"hello1.con"},
					Endpoints: endpoints,
				},
			}
			_, _ = fakeClient.Istio().NetworkingV1alpha3().ServiceEntries("namespace1").Create(kubeContext.TODO(), se2CRD, metav1.CreateOptions{})

			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockCtx.SetParamNames(constants.InstanceIDPathParam)
			mockCtx.SetParamValues("aa")

			_, err := s.GetLabelSelectorSet(mockCtx, buildLaneGroupParams())
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestModifyBaseLane(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	pwd, _ := os.Getwd()
	testPath := path.Join(pwd, "../../../", constants.BaseLanePath)

	tests := []struct {
		name         string
		laneParams   *meta.LaneParams
		baseLanes    []meta.Lanes
		lanes        []meta.Lanes
		instanceType string
		success      bool
	}{
		{
			name:         "success",
			laneParams:   buildLaneParams(),
			baseLanes:    []meta.Lanes{*buildLanes(true)},
			instanceType: instanceType,
			success:      true,
		},
		{
			name: "hosting-success",
			laneParams: &meta.LaneParams{
				InstanceUUID:       "instance-uuid",
				GroupID:            "group-id",
				LaneID:             "lane-id",
				LaneName:           "lane-name",
				LabelSelectorKey:   "selector-key",
				LabelSelectorValue: "selector-value",
				ServiceList:        buildServiceListParams(),
				IsBase:             false,
			},
			baseLanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/true,bj/name/cce-x/default/11111/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(false),
			}},
			lanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id1111",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(false),
			}},
			instanceType: instanceHostingType,
			success:      true,
		},
	}
	for _, tt := range tests {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCService,
				laneModel:      mockLaneModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockLaneModel.EXPECT().WithTx(gomock.Any()).Return(mockLaneModel).AnyTimes()
			mockLaneModel.EXPECT().GetLaneByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(buildLanes(false), nil)
			mockLaneModel.EXPECT().NewLane(mockCtx, gomock.Any()).Return(nil).AnyTimes()
			mockLaneModel.EXPECT().GenerateLaneID().Return("2222", nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLaneGroups(), nil).AnyTimes()
			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.baseLanes, nil).Times(1)

			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).Times(1)
			mockLaneModel.EXPECT().ModifyLane(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()

			err := s.ModifyBaseLane(mockCtx, tt.laneParams, "acc")
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestGetLanes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	pwd, _ := os.Getwd()
	testPath := path.Join(pwd, "../../../", constants.BaseLanePath)

	tests := []struct {
		name         string
		laneParams   *meta.LaneParams
		lanes        []meta.Lanes
		vs           *v1alpha3.VirtualService
		instanceType string
		success      bool
	}{
		{
			name:         "success",
			laneParams:   buildLaneParams(),
			instanceType: instanceType,
			success:      true,
		},
		{
			name: "hosting-success",
			laneParams: &meta.LaneParams{
				InstanceUUID:       "instance-uuid",
				GroupID:            "group-id",
				LaneID:             "lane-id",
				LaneName:           "lane-name",
				LabelSelectorKey:   "selector-key",
				LabelSelectorValue: "selector-value",
				ServiceList:        buildServiceListParams(),
				IsBase:             false,
			},
			lanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id1111",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(false),
			}},
			vs: &v1alpha3.VirtualService{
				ObjectMeta: metav1.ObjectMeta{
					Name: "vs-lane-id1111-3211s",
				},
				Spec: networkingv1alpha3.VirtualService{
					Http: []*networkingv1alpha3.HTTPRoute{{
						Match: []*networkingv1alpha3.HTTPMatchRequest{
							{
								Name: "test-name",
								Headers: map[string]*networkingv1alpha3.StringMatch{
									"example-header": {
										MatchType: &networkingv1alpha3.StringMatch_Exact{Exact: "/example-path"}},
								},
								Uri: &networkingv1alpha3.StringMatch{
									MatchType: &networkingv1alpha3.StringMatch_Exact{Exact: "/example-path"},
								},
							},
							{
								Name: "test-name1",
								Headers: map[string]*networkingv1alpha3.StringMatch{
									"example-header1": {
										MatchType: &networkingv1alpha3.StringMatch_Regex{Regex: "/example-path1"}},
								},
								Uri: &networkingv1alpha3.StringMatch{
									MatchType: &networkingv1alpha3.StringMatch_Regex{Regex: "/example-path1"},
								},
							},
						},
					}},
				},
			},
			instanceType: instanceHostingType,
			success:      true,
		},
	}
	for _, tt := range tests {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCService,
				laneModel:      mockLaneModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			_, _ = fakeClient.Istio().NetworkingV1alpha3().VirtualServices(istioInstallNamespace).Create(kubeContext.TODO(),
				tt.vs, metav1.CreateOptions{})
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()

			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()

			_, err := s.GetLanes(mockCtx, tt.laneParams, "acc")
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestNewRoute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	pwd, _ := os.Getwd()
	testPath := path.Join(pwd, "../../../", constants.BaseLanePath)

	tests := []struct {
		name         string
		routeParam   *meta.RouteParams
		lanes        []meta.Lanes
		vs           *v1alpha3.VirtualService
		instanceType string
		success      bool
	}{
		{
			name:         "success",
			routeParam:   buildRouteParams(),
			instanceType: instanceType,
			success:      true,
		},
		{
			name:       "hosting-success",
			routeParam: buildRouteParams(),
			lanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id1111",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(false),
			}},
			vs: &v1alpha3.VirtualService{
				ObjectMeta: metav1.ObjectMeta{
					Name: "vs-lane-id-service-name",
				},
				Spec: networkingv1alpha3.VirtualService{
					Http: []*networkingv1alpha3.HTTPRoute{{
						Match: []*networkingv1alpha3.HTTPMatchRequest{
							{
								Name: "test-name",
								Headers: map[string]*networkingv1alpha3.StringMatch{
									"example-header": {
										MatchType: &networkingv1alpha3.StringMatch_Exact{Exact: "/example-path"}},
								},
								Uri: &networkingv1alpha3.StringMatch{
									MatchType: &networkingv1alpha3.StringMatch_Exact{Exact: "/example-path"},
								},
							},
						},
					}},
				},
			},
			instanceType: instanceHostingType,
			success:      true,
		},
	}
	for _, tt := range tests {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCService,
				laneModel:      mockLaneModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			_, _ = fakeClient.Istio().NetworkingV1alpha3().VirtualServices(istioInstallNamespace).Create(kubeContext.TODO(),
				tt.vs, metav1.CreateOptions{})
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLanes(false), nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLaneGroups(), nil).AnyTimes()

			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()

			err := s.NewRoute(mockCtx, tt.routeParam, "acc")
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestModifyRoute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	pwd, _ := os.Getwd()
	testPath := path.Join(pwd, "../../../", constants.BaseLanePath)

	tests := []struct {
		name         string
		routeParam   *meta.RouteParams
		lanes        []meta.Lanes
		instanceType string
		success      bool
	}{
		{
			name:         "success",
			routeParam:   buildRouteParams(),
			instanceType: instanceType,
			success:      true,
		},
		{
			name:       "hosting-success",
			routeParam: buildRouteParams(),
			lanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id1111",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(false),
			}},
			instanceType: instanceHostingType,
			success:      true,
		},
	}
	for _, tt := range tests {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCService,
				laneModel:      mockLaneModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLanes(false), nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLaneGroups(), nil).AnyTimes()

			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()

			err := s.ModifyRoute(mockCtx, tt.routeParam, "acc")
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestGetRouteRules(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	pwd, _ := os.Getwd()
	testPath := path.Join(pwd, "../../../", constants.BaseLanePath)

	tests := []struct {
		name         string
		routeParam   *meta.RouteParams
		lanes        []meta.Lanes
		vs           *v1alpha3.VirtualService
		instanceType string
		success      bool
	}{
		{
			name:       "hosting-success",
			routeParam: buildRouteParams(),
			lanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id1111",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(false),
			}},
			vs: &v1alpha3.VirtualService{
				ObjectMeta: metav1.ObjectMeta{
					Name: "vs-lane-id-service-name",
				},
				Spec: networkingv1alpha3.VirtualService{
					Http: []*networkingv1alpha3.HTTPRoute{{
						Match: []*networkingv1alpha3.HTTPMatchRequest{
							{
								Name: "test-name",
								Headers: map[string]*networkingv1alpha3.StringMatch{
									"example-header": {
										MatchType: &networkingv1alpha3.StringMatch_Exact{Exact: "/example-path"}},
								},
								Uri: &networkingv1alpha3.StringMatch{
									MatchType: &networkingv1alpha3.StringMatch_Exact{Exact: "/example-path"},
								},
							},
						},
					}},
				},
			},
			instanceType: instanceHostingType,
			success:      true,
		},
	}
	for _, tt := range tests {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCService,
				laneModel:      mockLaneModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			_, _ = fakeClient.Istio().NetworkingV1alpha3().VirtualServices(istioInstallNamespace).Create(kubeContext.TODO(),
				tt.vs, metav1.CreateOptions{})
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLanes(false), nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLaneGroups(), nil).AnyTimes()

			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()

			_, err := s.GetRouteRules(mockCtx, tt.routeParam)
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestDeleteRoute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	pwd, _ := os.Getwd()
	testPath := path.Join(pwd, "../../../", constants.BaseLanePath)

	tests := []struct {
		name         string
		routeParam   *meta.RouteParams
		lanes        []meta.Lanes
		instanceType string
		success      bool
	}{
		{
			name:         "success",
			routeParam:   buildRouteParams(),
			instanceType: instanceType,
			success:      true,
		},
		{
			name:       "hosting-success",
			routeParam: buildRouteParams(),
			lanes: []meta.Lanes{{
				GroupID:            "group-id",
				LaneID:             "lane-id1111",
				InstanceUUID:       instanceUUID,
				AccountId:          accountId,
				ServiceList:        "bj/name/cce-x/default/3211s/false",
				LaneName:           name,
				LabelSelectorValue: "value",
				LabelSelectorKey:   "key",
				IsBase:             csm.Bool(false),
			}},
			instanceType: instanceHostingType,
			success:      true,
		},
	}
	for _, tt := range tests {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)
		mockCService := mockCceService.NewMockClientInterface(ctrl)
		mockLaneModel := mockLane.NewMockServiceInterface(ctrl)
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCService,
				laneModel:      mockLaneModel,
			}
			s.opt.LaneFilePath = testPath
			fakeClient := kube.NewFakeClient()
			mockCService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			mockLaneModel.EXPECT().GetLanes(mockCtx, gomock.Any(), gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLanes(false), nil).AnyTimes()
			mockLaneModel.EXPECT().GetLaneGroupByID(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(buildLaneGroups(), nil).AnyTimes()

			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildCluster(), nil).AnyTimes()
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(buildInstances(tt.instanceType), nil).AnyTimes()

			err := s.DeleteRoute(mockCtx, tt.routeParam, "acc")
			if tt.success {
				assert.NoError(t, err)
			} else {

			}
		})
	}
}

func TestGetServiceList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name         string
		clusterList  *[]meta.Cluster
		instanceType string
		testMrp      *meta.LaneGroupServiceListParams
		expectErr    error
	}{
		{
			name:         "correct-GetMeshInstanceServices",
			instanceType: string(meta.StandaloneMeshType),
			clusterList: &[]meta.Cluster{
				{
					ClusterType: string(meta.ClusterTypeExternal),
				},
				{
					ClusterType: string(meta.ClusterTypeRemote),
					Region:      "bj",
					ClusterName: clusterName,
					ClusterUUID: "abc",
				},
			},
			testMrp: &meta.LaneGroupServiceListParams{
				InstanceUUID: instanceUUID,
			},
			expectErr: nil,
		},
		{
			name:         "with clusterParams",
			instanceType: string(meta.StandaloneMeshType),
			clusterList: &[]meta.Cluster{
				{
					ClusterType: string(meta.ClusterTypeExternal),
				},
				{
					ClusterType: string(meta.ClusterTypeRemote),
					Region:      "bj",
					ClusterName: clusterName,
					ClusterUUID: "abc",
				},
			},
			testMrp: &meta.LaneGroupServiceListParams{
				InstanceUUID: instanceUUID,
			},
			expectErr: nil,
		},
		{
			name:         "with namespaceParams",
			instanceType: string(meta.HostingMeshType),
			clusterList: &[]meta.Cluster{
				{
					ClusterType: string(meta.ClusterTypeExternal),
				},
				{
					ClusterType: string(meta.ClusterTypeRemote),
					Region:      "bj",
					ClusterName: clusterName,
					ClusterUUID: "abc",
				},
				{
					ClusterType: string(meta.ClusterTypeRemote),
					Region:      "bj1",
					ClusterName: "ns-11",
					ClusterUUID: "abc12",
				},
			},
			testMrp: &meta.LaneGroupServiceListParams{
				InstanceUUID: instanceUUID,
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockCceService := mockCceService.NewMockClientInterface(ctrl)
		mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
		service := &Service{
			clusterModel:   mockClusterModel,
			cceService:     mockCceService,
			instancesModel: mockInstanceModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()
			mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(mockCtx, gomock.Any()).Return(testInfo.clusterList, nil)
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(
				buildInstances(testInfo.instanceType), nil)
			_, err := service.GetServiceList(mockCtx, testInfo.testMrp)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func buildServiceListParams() []meta.ServiceListParams {
	return []meta.ServiceListParams{
		{
			ClusterRegion: "region1",
			ClusterName:   "cluster1",
			ClusterID:     "id1",
			Namespace:     "namespace1",
			ServiceName:   "service1",
			IsHost:        false,
		},
		{
			ClusterRegion: "region1",
			ClusterName:   "cluster1",
			ClusterID:     "id1",
			Namespace:     "namespace1",
			ServiceName:   "hello.con",
			IsHost:        true,
		},
		{
			ClusterRegion: "region1",
			ClusterName:   "cluster1",
			ClusterID:     "id1",
			Namespace:     "namespace1",
			ServiceName:   "hello1.con",
			IsHost:        true,
		},
		// 添加更多服务，如果需要
	}
}

func buildLaneGroupParams() *meta.LaneGroupParams {
	return &meta.LaneGroupParams{
		InstanceUUID: "instance-uuid",
		GroupID:      "group-id",
		GroupName:    "group-name",
		TraceHeader:  "x-trace-header",
		RouteHeader:  "x-route-header",
		ServiceList:  buildServiceListParams(),
		BaseLane:     *buildLaneParams(),
	}
}

func buildLaneParams() *meta.LaneParams {
	return &meta.LaneParams{
		InstanceUUID:       "instance-uuid",
		GroupID:            "group-id",
		LaneID:             "lane-id",
		LaneName:           "lane-name",
		LabelSelectorKey:   "selector-key",
		LabelSelectorValue: "selector-value",
		ServiceList:        buildServiceListParams(),
		IsBase:             true,
	}

}

func buildLaneGroups() *meta.LaneGroups {
	return &meta.LaneGroups{
		GroupID:     "group-id",
		GroupName:   "group-name",
		TraceHeader: "x-trace-header",
		RouteHeader: "x-route-header",
		ServiceList: "bj/name/cce-x/default/32ss/false",
	}
}

func buildLanes(isBase bool) *meta.Lanes {
	return &meta.Lanes{
		GroupID:            "group-id",
		LaneID:             "lane-id",
		InstanceUUID:       instanceUUID,
		AccountId:          accountId,
		ServiceList:        "bj/name/cce-x/default/32ss/false",
		LaneName:           name,
		LabelSelectorValue: "value",
		LabelSelectorKey:   "key",
		IsBase:             csm.Bool(isBase),
	}
}

func buildInstances(instanceType string) *meta.Instances {
	return &meta.Instances{
		InstanceUUID:             instanceUUID,
		InstanceName:             instanceName,
		InstanceType:             instanceType,
		IstioVersion:             testdata.Version1146,
		Region:                   region,
		AccountId:                accountId,
		DiscoverySelectorEnabled: csm.Bool(discoverySelectorEnabled),
		DiscoverySelectorLabels:  discoverySelectorLabels,
		IstioInstallNamespace:    istioInstallNamespace,
		InstanceManageScope:      instanceManageScope,
		MultiProtocolEnabled:     csm.Bool(true),
	}
}

func buildCluster() *meta.Cluster {
	return &meta.Cluster{
		InstanceUUID:          instanceUUID,
		ClusterUUID:           clusterUUID,
		ClusterName:           clusterName,
		ClusterType:           clusterType,
		Region:                region,
		AccountId:             accountId,
		IstioInstallNamespace: istioInstallNamespace,
	}
}

func buildRouteParams() *meta.RouteParams {
	return &meta.RouteParams{
		InstanceUUID:  "instance-uuid",
		GroupID:       "group-id",
		LaneID:        "lane-id",
		ClusterRegion: "cluster-region",
		ClusterID:     "cluster-id",
		Namespace:     "namespace",
		ServiceName:   "service-name",
		Rules: []meta.RouteRule{
			{
				MatchRequest: meta.MatchRequest{
					RouteName: "route-name",
					Headers: []meta.Header{
						{
							Name:            "header-name",
							MatchingMode:    "exact",
							MatchingContent: "header-content",
						},
					},
					Uri: meta.Uri{
						MatchingMode:    "prefix",
						MatchingContent: "/example-path",
						Enabled:         true,
					},
				},

				RouteDestinations: []meta.RouteDestinationRule{
					{
						Destination: meta.Destination{
							Name: "test-1",
						},
					},
				},
			},
		},
	}
}
