// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// DeleteAllLaneGroupByInstanceUUID mocks base method.
func (m *MockServiceInterface) DeleteAllLaneGroupByInstanceUUID(ctx context.CsmContext, instanceUUID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAllLaneGroupByInstanceUUID", ctx, instanceUUID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAllLaneGroupByInstanceUUID indicates an expected call of DeleteAllLaneGroupByInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) DeleteAllLaneGroupByInstanceUUID(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllLaneGroupByInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).DeleteAllLaneGroupByInstanceUUID), ctx, instanceUUID)
}

// DeleteLane mocks base method.
func (m *MockServiceInterface) DeleteLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLane", ctx, laneParams, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLane indicates an expected call of DeleteLane.
func (mr *MockServiceInterfaceMockRecorder) DeleteLane(ctx, laneParams, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLane", reflect.TypeOf((*MockServiceInterface)(nil).DeleteLane), ctx, laneParams, accountID)
}

// DeleteLaneGroupByID mocks base method.
func (m *MockServiceInterface) DeleteLaneGroupByID(ctx context.CsmContext, laneGroup *meta.LaneGroupParams, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLaneGroupByID", ctx, laneGroup, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLaneGroupByID indicates an expected call of DeleteLaneGroupByID.
func (mr *MockServiceInterfaceMockRecorder) DeleteLaneGroupByID(ctx, laneGroup, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLaneGroupByID", reflect.TypeOf((*MockServiceInterface)(nil).DeleteLaneGroupByID), ctx, laneGroup, accountID)
}

// DeleteRoute mocks base method.
func (m *MockServiceInterface) DeleteRoute(ctx context.CsmContext, routeParams *meta.RouteParams, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRoute", ctx, routeParams, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRoute indicates an expected call of DeleteRoute.
func (mr *MockServiceInterfaceMockRecorder) DeleteRoute(ctx, routeParams, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRoute", reflect.TypeOf((*MockServiceInterface)(nil).DeleteRoute), ctx, routeParams, accountID)
}

// GetLabelSelectorSet mocks base method.
func (m *MockServiceInterface) GetLabelSelectorSet(ctx context.CsmContext, laneGroupParams *meta.LaneGroupParams) (map[string][]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLabelSelectorSet", ctx, laneGroupParams)
	ret0, _ := ret[0].(map[string][]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLabelSelectorSet indicates an expected call of GetLabelSelectorSet.
func (mr *MockServiceInterfaceMockRecorder) GetLabelSelectorSet(ctx, laneGroupParams interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLabelSelectorSet", reflect.TypeOf((*MockServiceInterface)(nil).GetLabelSelectorSet), ctx, laneGroupParams)
}

// GetLaneGroups mocks base method.
func (m *MockServiceInterface) GetLaneGroups(ctx context.CsmContext, accountID string) (*meta.LaneGroupParamsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLaneGroups", ctx, accountID)
	ret0, _ := ret[0].(*meta.LaneGroupParamsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLaneGroups indicates an expected call of GetLaneGroups.
func (mr *MockServiceInterfaceMockRecorder) GetLaneGroups(ctx, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLaneGroups", reflect.TypeOf((*MockServiceInterface)(nil).GetLaneGroups), ctx, accountID)
}

// GetLanes mocks base method.
func (m *MockServiceInterface) GetLanes(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) (*meta.LaneParamsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLanes", ctx, laneParams, accountID)
	ret0, _ := ret[0].(*meta.LaneParamsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLanes indicates an expected call of GetLanes.
func (mr *MockServiceInterfaceMockRecorder) GetLanes(ctx, laneParams, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLanes", reflect.TypeOf((*MockServiceInterface)(nil).GetLanes), ctx, laneParams, accountID)
}

// GetRouteRules mocks base method.
func (m *MockServiceInterface) GetRouteRules(ctx context.CsmContext, routeParams *meta.RouteParams) ([]meta.RouteParams, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRouteRules", ctx, routeParams)
	ret0, _ := ret[0].([]meta.RouteParams)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRouteRules indicates an expected call of GetRouteRules.
func (mr *MockServiceInterfaceMockRecorder) GetRouteRules(ctx, routeParams interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRouteRules", reflect.TypeOf((*MockServiceInterface)(nil).GetRouteRules), ctx, routeParams)
}

// GetServiceList mocks base method.
func (m *MockServiceInterface) GetServiceList(ctx context.CsmContext, serviceListParams *meta.LaneGroupServiceListParams) (*meta.LaneServiceListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceList", ctx, serviceListParams)
	ret0, _ := ret[0].(*meta.LaneServiceListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceList indicates an expected call of GetServiceList.
func (mr *MockServiceInterfaceMockRecorder) GetServiceList(ctx, serviceListParams interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceList", reflect.TypeOf((*MockServiceInterface)(nil).GetServiceList), ctx, serviceListParams)
}

// ModifyBaseLane mocks base method.
func (m *MockServiceInterface) ModifyBaseLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyBaseLane", ctx, laneParams, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyBaseLane indicates an expected call of ModifyBaseLane.
func (mr *MockServiceInterfaceMockRecorder) ModifyBaseLane(ctx, laneParams, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyBaseLane", reflect.TypeOf((*MockServiceInterface)(nil).ModifyBaseLane), ctx, laneParams, accountID)
}

// ModifyLane mocks base method.
func (m *MockServiceInterface) ModifyLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyLane", ctx, laneParams, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyLane indicates an expected call of ModifyLane.
func (mr *MockServiceInterfaceMockRecorder) ModifyLane(ctx, laneParams, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyLane", reflect.TypeOf((*MockServiceInterface)(nil).ModifyLane), ctx, laneParams, accountID)
}

// ModifyRoute mocks base method.
func (m *MockServiceInterface) ModifyRoute(ctx context.CsmContext, routeParams *meta.RouteParams, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyRoute", ctx, routeParams, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyRoute indicates an expected call of ModifyRoute.
func (mr *MockServiceInterfaceMockRecorder) ModifyRoute(ctx, routeParams, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyRoute", reflect.TypeOf((*MockServiceInterface)(nil).ModifyRoute), ctx, routeParams, accountID)
}

// NewLane mocks base method.
func (m *MockServiceInterface) NewLane(ctx context.CsmContext, laneParams *meta.LaneParams, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewLane", ctx, laneParams, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewLane indicates an expected call of NewLane.
func (mr *MockServiceInterfaceMockRecorder) NewLane(ctx, laneParams, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewLane", reflect.TypeOf((*MockServiceInterface)(nil).NewLane), ctx, laneParams, accountID)
}

// NewLaneGroup mocks base method.
func (m *MockServiceInterface) NewLaneGroup(ctx context.CsmContext, laneGroupParams *meta.LaneGroupParams, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewLaneGroup", ctx, laneGroupParams, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewLaneGroup indicates an expected call of NewLaneGroup.
func (mr *MockServiceInterfaceMockRecorder) NewLaneGroup(ctx, laneGroupParams, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewLaneGroup", reflect.TypeOf((*MockServiceInterface)(nil).NewLaneGroup), ctx, laneGroupParams, accountID)
}

// NewRoute mocks base method.
func (m *MockServiceInterface) NewRoute(ctx context.CsmContext, routeParams *meta.RouteParams, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewRoute", ctx, routeParams, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewRoute indicates an expected call of NewRoute.
func (mr *MockServiceInterfaceMockRecorder) NewRoute(ctx, routeParams, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewRoute", reflect.TypeOf((*MockServiceInterface)(nil).NewRoute), ctx, routeParams, accountID)
}
