package service

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
)

type ResourceServiceInterface interface {
	Start(ctx context.Context, resourceId, accountId string) error
	Stop(ctx context.Context, resourceId string) error
	Deleted(ctx context.Context, resourceId string) error
}

type ResourceService struct {
	clients clientset.ClientSetInterface
	region  string
}

func NewResourceService(clis clientset.ClientSetInterface, region string) *ResourceService {
	return &ResourceService{
		clients: clis,
		region:  region,
	}
}

func (r *ResourceService) Start(ctx context.Context, resourceId, accountId string) error {
	logger := gin_context.LoggerFromContext(ctx)
	requestId := gin_context.RequestIdFromContext(ctx)

	ccr := &v1alpha1.CCR{}
	if err := r.clients.K8sClient().Get(ctx, client.ObjectKey{Name: resourceId}, ccr); err != nil {
		logger.Errorf("[StartResource] get ccr resource: %s failed: %s", resourceId, err)
		return err
	}

	if ccr.Status.Phase == v1alpha1.CCRRunning {

		resourceClient, err := r.clients.ResourceClientForAccount(accountId, accountId)
		if err != nil {
			logger.Errorf("[StartResource] create resource client failed: %s", err)
			return err
		}

		resource, err := resourceClient.GetResourceDetail(requestId, &billing.GetResourceDetailRequest{
			ServiceType: billing.ServiceType,
			Region:      common.GetRealRegion(r.region),
			AccountId:   accountId,
			Name:        resourceId,
		})
		if err != nil {
			logger.Errorf("[StartResource] get resource detail failed: %s", err)
			return err
		}

		// 更新到期时间
		expireTime := metav1.NewTime(resource.ExpireTime)

		updateObj := ccr.DeepCopy()
		updateObj.Spec.ExpireTime = &expireTime
		updateObj.Labels[v1alpha1.LastOrderIdKey] = resource.OrderId

		if err := r.clients.K8sClient().Patch(ctx, updateObj, client.MergeFrom(ccr)); err != nil {
			logger.Errorf("[StartResource] patch to billing action failed: %s", err)
			return err
		}
		logger.Infof("[StartResource] patch billing expire time complate")
		return nil
	}

	if ccr.Status.Phase == v1alpha1.CCRStopped {
		updateObj := ccr.DeepCopy()
		updateObj.Spec.Action = v1alpha1.StartAction

		if err := r.clients.K8sClient().Patch(ctx, updateObj, client.MergeFrom(ccr)); err != nil {
			logger.Errorf("[StartResource] patch to billing action failed: %s", err)
			return err
		}
		logger.Infof("[StartResource] patch billing action to %s complate", v1alpha1.StartAction)
	}

	return fmt.Errorf("ccr instance status is: %s not running", ccr.Status.Phase)
}

func (r *ResourceService) Stop(ctx context.Context, resourceId string) error {
	logger := gin_context.LoggerFromContext(ctx)

	ccr := &v1alpha1.CCR{}
	if err := r.clients.K8sClient().Get(ctx, client.ObjectKey{Name: resourceId}, ccr); err != nil {
		logger.Errorf("[StopResource] get ccr resource: %s failed: %s", resourceId, err)
		return err
	}

	if ccr.Status.Phase == v1alpha1.CCRStopped {
		logger.Errorf("[StopResource] ccr resource stattus: %s,return nil", ccr.Status.Phase)
		return nil
	}

	if ccr.Status.Phase == v1alpha1.CCRRunning {
		updateObj := ccr.DeepCopy()
		updateObj.Spec.Action = v1alpha1.StopAction

		if err := r.clients.K8sClient().Patch(ctx, updateObj, client.MergeFrom(ccr)); err != nil {
			logger.Errorf("[StopResource] patch to billing action failed: %s", err)
			return err
		}
		logger.Infof("[StopResource] patch billing action to %s complate", v1alpha1.StartAction)
	}

	return fmt.Errorf("ccr instance status is: %s not stopped", ccr.Status.Phase)
}

func (r *ResourceService) Deleted(ctx context.Context, resourceId string) error {
	logger := gin_context.LoggerFromContext(ctx)

	ccr := &v1alpha1.CCR{}
	if err := r.clients.K8sClient().Get(ctx, client.ObjectKey{Name: resourceId}, ccr); err != nil {
		logger.Errorf("[DeleteResource] get ccr resource: %s failed: %s", resourceId, err)
		if apierrors.IsNotFound(err) {
			logger.Infof("[DeleteResource] ccr: %s is destroyed", resourceId)
			return nil
		}
		return err
	}

	if err := r.clients.K8sClient().Delete(ctx, ccr); err != nil {
		logger.Errorf("[DeleteResource] to delete resource failed: %s", err)
		return err
	}

	return fmt.Errorf("ccr instance already exist")
}
