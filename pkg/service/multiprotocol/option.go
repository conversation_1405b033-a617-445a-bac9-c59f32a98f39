package multiprotocol

import (
	"github.com/spf13/viper"
)

const (
	aerakiHub = "istio.standalone.aeraki.hub"

	aerakiImagePullPolicy = "istio.standalone.aeraki.imagePullPolicy"

	aerakiSupportVersion = "istio.standalone.aeraki.supportVersion"

	// AerakiProxyImage is the image name used by Aeraki Proxy component
	// registry.baidubce.com/csm-offline/meta-protocol-proxy:1.14.6 stands for ghcr.io/aeraki-mesh/meta-protocol-proxy:1.2.3
	// registry.baidubce.com/csm-offline/meta-protocol-proxy:1.16.5 stands for ghcr.io/aeraki-mesh/meta-protocol-proxy:1.3.1
	AerakiProxyImage = "meta-protocol-proxy"

	DefaultProxyImage = "proxyv2"
)

var (
	DefaultIstiodAddr       = "istiod.%s:15010"
	DefaultIstiodRemoteAddr = "istiod-remote.%s:15010"

	DefaultXdsAddr       = "aeraki.%s"
	DefaultXdsRemoteAddr = "istiod-remote.%s"

	DefaultXdsPort       = ":15010"
	DefaultXdsRemotePort = ":15011"
)

type Option struct {
	Namespace       string
	Image           string
	ImagePullPolicy string
	IstiodAddr      string
	XdsAddr         string
	XdsPort         string
	IsMaster        bool
	IsPrimary       bool
}

func NewOptionWith(namespace, image, imagePullPolicy, istiodAddr,
	xdsAddr, xdsPort string, isMaster, isPrimary bool) *Option {
	return &Option{
		Namespace:       namespace,
		Image:           image,
		ImagePullPolicy: imagePullPolicy,
		IstiodAddr:      istiodAddr,
		XdsAddr:         xdsAddr,
		XdsPort:         xdsPort,
		IsMaster:        isMaster,
		IsPrimary:       isPrimary,
	}
}

func getAerakiHub() string {
	return viper.GetString(aerakiHub)
}

func getAerakiImage(version string) string {
	images := viper.GetStringMapString(aerakiSupportVersion)
	aerakiImage := images[version]
	return aerakiImage
}

func GetAerakiImageURL(version string) string {
	return getAerakiHub() + ":" + getAerakiImage(version)
}

func GetAerakiImagePullPolicy() string {
	return viper.GetString(aerakiImagePullPolicy)
}

func getAerakiSupportVersionWithMap() map[string]string {
	return viper.GetStringMapString(aerakiSupportVersion)
}

func getAerakiSupportVersion() []string {
	supportVersion := getAerakiSupportVersionWithMap()
	// get all key for versions
	versions := make([]string, 0, len(supportVersion))
	for key := range supportVersion {
		versions = append(versions, key)
	}
	return versions
}
