// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	multiprotocol "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/multiprotocol"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// CheckVersionSupport mocks base method.
func (m *MockServiceInterface) CheckVersionSupport(ctx context.CsmContext, version string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckVersionSupport", ctx, version)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckVersionSupport indicates an expected call of CheckVersionSupport.
func (mr *MockServiceInterfaceMockRecorder) CheckVersionSupport(ctx, version interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckVersionSupport", reflect.TypeOf((*MockServiceInterface)(nil).CheckVersionSupport), ctx, version)
}

// ParseAerakiTmpl mocks base method.
func (m *MockServiceInterface) ParseAerakiTmpl(ctx context.CsmContext, option *multiprotocol.Option) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseAerakiTmpl", ctx, option)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseAerakiTmpl indicates an expected call of ParseAerakiTmpl.
func (mr *MockServiceInterfaceMockRecorder) ParseAerakiTmpl(ctx, option interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseAerakiTmpl", reflect.TypeOf((*MockServiceInterface)(nil).ParseAerakiTmpl), ctx, option)
}
