package replication

import (
	"context"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	v2client "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/replication"
	mockreplic "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/replication/mocks"
	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	ccrmodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
)

func TestPolicyService_NewPolicy(t *testing.T) {
	type args struct {
		ctx    context.Context
		kind   string
		policy *harbormodel.ReplicationPolicy
	}
	tests := []struct {
		name    string
		r       *PolicyService
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "success",
			r:    &PolicyService{},
			args: func() args {
				repCli := &mockreplic.MockReplicationClientService{}
				repCli.On("CreateReplicationPolicy", mock.Anything, mock.Anything).Return(&replication.CreateReplicationPolicyCreated{
					Location: "/test/10",
				}, nil)
				v2Cli := &v2client.Harbor{
					Replication: repCli,
				}
				harborCli := &harbor.HarborClient{
					V2Client: v2Cli,
				}

				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Set("HARBOR_CLIENT_IDENTITY", harborCli)

				return args{
					ctx:    ctx,
					kind:   "sync",
					policy: &harbormodel.ReplicationPolicy{},
				}
			}(),
			want:    "/test/10",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &PolicyService{}
			got, err := r.NewPolicy(tt.args.ctx, tt.args.kind, tt.args.policy)
			if (err != nil) != tt.wantErr {
				t.Errorf("PolicyService.NewPolicy() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("PolicyService.NewPolicy() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPolicyService_DeletePolicy(t *testing.T) {
	type args struct {
		ctx      context.Context
		policyID int64
	}
	tests := []struct {
		name    string
		r       *PolicyService
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "success",
			r:    &PolicyService{},
			args: func() args {
				repCli := &mockreplic.MockReplicationClientService{}
				repCli.On("DeleteReplicationPolicy", mock.Anything, mock.Anything).Return(nil, nil)
				v2Cli := &v2client.Harbor{
					Replication: repCli,
				}
				harborCli := &harbor.HarborClient{
					V2Client: v2Cli,
				}

				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Set("HARBOR_CLIENT_IDENTITY", harborCli)

				return args{
					ctx:      ctx,
					policyID: 10,
				}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &PolicyService{}
			if err := r.DeletePolicy(tt.args.ctx, tt.args.policyID); (err != nil) != tt.wantErr {
				t.Errorf("PolicyService.DeletePolicy() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPolicyService_ListSyncPolicies(t *testing.T) {
	type args struct {
		ctx        context.Context
		publicURL  string
		policyName string
		pageNo     int64
		pageSize   int64
	}
	tests := []struct {
		name    string
		r       *PolicyService
		args    args
		want    []*ccrmodel.SyncPolicyResult
		want1   int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "success",
			r:    &PolicyService{},
			args: func() args {
				repCli := &mockreplic.MockReplicationClientService{}
				repCli.On("ListReplicationPolicies", mock.Anything, mock.Anything).Return(&replication.ListReplicationPoliciesOK{
					XTotalCount: 1,
					Payload: []*harbormodel.ReplicationPolicy{
						{
							Name: "CCR-SYNC-test",
							DestRegistry: &harbormodel.Registry{
								Credential: &harbormodel.RegistryCredential{},
								Name:       "ccr-1234567890",
							},
							SrcRegistry: &harbormodel.Registry{
								Credential: &harbormodel.RegistryCredential{},
							},
						},
					},
				}, nil)
				repCli.On("ListReplicationExecutions", mock.Anything, mock.Anything).Return(&replication.ListReplicationExecutionsOK{
					XTotalCount: 1,
					Payload: []*harbormodel.ReplicationExecution{
						{
							Status: "RUNNING",
						},
					},
				}, nil)
				harborCli := &harbor.HarborClient{
					V2Client: &v2client.Harbor{
						Replication: repCli,
					},
				}

				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Set("HARBOR_CLIENT_IDENTITY", harborCli)

				return args{
					ctx:        ctx,
					publicURL:  "test.com",
					policyName: "test",
					pageNo:     1,
					pageSize:   10,
				}
			}(),
			wantErr: false,
			want1:   1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &PolicyService{}
			_, got1, err := r.ListSyncPolicies(tt.args.ctx, tt.args.publicURL, tt.args.policyName, tt.args.pageNo, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("PolicyService.ListSyncPolicies() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got1 != tt.want1 {
				t.Errorf("PolicyService.ListSyncPolicies() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestPolicyService_GetSyncPolicy(t *testing.T) {
	type args struct {
		ctx       context.Context
		publicURL string
		policyID  int64
	}
	tests := []struct {
		name    string
		r       *PolicyService
		args    args
		want    *ccrmodel.SyncPolicyResult
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "success",
			r:    &PolicyService{},
			args: func() args {
				repCli := &mockreplic.MockReplicationClientService{}
				repCli.On("GetReplicationPolicy", mock.Anything, mock.Anything).Return(&replication.GetReplicationPolicyOK{
					Payload: &harbormodel.ReplicationPolicy{
						Name: "CCR-SYNC-test",
						DestRegistry: &harbormodel.Registry{
							Credential: &harbormodel.RegistryCredential{},
							Name:       "ccr-1234567890",
						},
						SrcRegistry: &harbormodel.Registry{
							Credential: &harbormodel.RegistryCredential{},
						},
					},
				}, nil)
				repCli.On("ListReplicationExecutions", mock.Anything, mock.Anything).Return(&replication.ListReplicationExecutionsOK{
					XTotalCount: 1,
					Payload: []*harbormodel.ReplicationExecution{
						{
							Status: "RUNNING",
						},
					},
				}, nil)
				v2Cli := &v2client.Harbor{
					Replication: repCli,
				}
				harborCli := &harbor.HarborClient{
					V2Client: v2Cli,
				}

				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Set("HARBOR_CLIENT_IDENTITY", harborCli)

				return args{
					ctx:       ctx,
					policyID:  10,
					publicURL: "test.com",
				}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &PolicyService{}
			_, err := r.GetSyncPolicy(tt.args.ctx, tt.args.publicURL, tt.args.policyID)
			if (err != nil) != tt.wantErr {
				t.Errorf("PolicyService.GetSyncPolicy() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestPolicyService_UpdatePolicy(t *testing.T) {
	type args struct {
		ctx      context.Context
		policyID int64
		kind     string
		policy   *harbormodel.ReplicationPolicy
	}
	tests := []struct {
		name    string
		r       *PolicyService
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "success",
			r:    &PolicyService{},
			args: func() args {
				repCli := &mockreplic.MockReplicationClientService{}
				repCli.On("UpdateReplicationPolicy", mock.Anything, mock.Anything).Return(nil, nil)
				v2Cli := &v2client.Harbor{
					Replication: repCli,
				}
				harborCli := &harbor.HarborClient{
					V2Client: v2Cli,
				}

				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Set("HARBOR_CLIENT_IDENTITY", harborCli)

				return args{
					ctx:      ctx,
					policyID: 10,
					kind:     "test",
					policy: &harbormodel.ReplicationPolicy{
						Name: "CCR-SYNC-test",
					},
				}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &PolicyService{}
			if err := r.UpdatePolicy(tt.args.ctx, tt.args.policyID, tt.args.kind, tt.args.policy); (err != nil) != tt.wantErr {
				t.Errorf("PolicyService.UpdatePolicy() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
