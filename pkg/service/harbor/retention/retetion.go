package retention

import (
	"context"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/retention"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type RetentionService struct {
}

func NewRetentionService() *RetentionService {
	return &RetentionService{}
}

// NewRetention Create Retention Policy
func (r *RetentionService) NewRetention(ctx context.Context, policy *model.RetentionPolicy) (string, error) {
	logger := gin_context.LoggerFromContext(ctx)
	harborClient := middleware.HarborClientFromContext(ctx)
	resp, err := harborClient.V2Client.Retention.CreateRetention(
		retention.NewCreateRetentionParamsWithContext(ctx).WithPolicy(policy), harborClient.AuthInfo)

	if err != nil {
		logger.Errorf("use swagger client call harbor failed: %s", err)
		return "", err
	}

	return resp.Location, nil
}
