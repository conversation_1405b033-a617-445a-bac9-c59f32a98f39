package crd

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetCrds(ctx context.CsmContext, params *meta.CrdParams) ([]meta.Crd, *meta.PageResult, error)
	GetCrd(ctx context.CsmContext, param *meta.CrdParam) (*meta.Crd, error)
	BatchCrd(ctx context.CsmContext, param *meta.CrdParam) (*meta.BatchCreateCrdInfo, error)
	CreateCrd(ctx context.CsmContext, param *meta.CrdParam) (*meta.Crd, error)
	UpdateCrd(ctx context.CsmContext, param *meta.CrdParam) (*meta.Crd, error)
	DeleteCrds(ctx context.CsmContext, params *meta.CrdParams) error
}
