package crd

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	networkingv1beta1 "istio.io/api/networking/v1beta1"
	"istio.io/client-go/pkg/apis/networking/v1alpha3"
	"istio.io/client-go/pkg/apis/networking/v1beta1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	k8sYaml "k8s.io/apimachinery/pkg/runtime/serializer/yaml"

	mockModelCluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	mockModelInstance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

var (
	crdAllowed                = "crd.allowedKind"
	testInstanceUUID          = "csm-123456"
	testInstanceType          = "hosting"
	standaloneType            = "standalone"
	testInstanceName          = "xxxxx"
	testIstioVersion          = "1.13.2"
	testInstanceStatus        = "running"
	testClusterName           = "test-cluster"
	testClusterUUID           = "cce-123456"
	testRegion                = "bj"
	testIstioInstallNamespace = "istio-system-csm-123456"
	testInstanceManageScope   = "namespace"

	mockDB       *gorm.DB
	mockCtx, _   = ctxCsm.NewCsmContextMock()
	instanceUUID = "csm-nxzqosxx"
	content      = "apiVersion: networking.istio.io/v1alpha3\nkind: VirtualService\nmetadata:\n  namespace: xxx\n " +
		" name: istio-system-csm-knvvl6z3-helloworld-vs\nspec:\n  hosts:\n   " +
		" - helloworld.default.svc.cluster.local\n  exportTo:\n    - default\n  http:\n  - route:\n  " +
		"  - destination:\n        host: helloworld.default.svc.cluster.local\n        subset: v1\n   " +
		"   weight: 100\n    - destination:\n        host: helloworld.default.svc.cluster.local\n   " +
		"     subset: v2\n      weight: 0\n---\napiVersion: networking.istio.io/v1alpha3\n" +
		"kind: DestinationRule\nmetadata:\n  namespace: istio-system-csm-knvvl6z3\n  " +
		"name: istio-system-csm-knvvl6z3-helloworld-dr\nspec:\n  exportTo:\n    " +
		"- default\n  host: helloworld.default.svc.cluster.local\n  subsets:\n  " +
		"- name: v1\n    labels:\n      version: v1\n  - name: v2\n    labels:\n      version: v2"
	sidecarContent = "apiVersion: networking.istio.io/v1alpha3\nkind: Sidecar\nmetadata:\n  " +
		"name: test-sidecar\n  namespace: test-ns\nspec:\n  egress:\n  - hosts:\n    - \"*/*\"\n    " +
		"- \"istio-system/*\"\n  outboundTrafficPolicy:\n    mode: REGISTRY_ONLY\n"

	metaRoute = "apiVersion: metaprotocol.aeraki.io/v1alpha1\nkind: MetaRouter\nmetadata:\n  " +
		"name: test-metaprotocol-dubbo-route\n  namespace: test-ns\nspec:\n  hosts:\n    " +
		"- dubbo-sample-provider.meta-dubbo.svc.cluster.local\n  routes:\n    - name: v1\n      " +
		"match:\n        attributes:\n          interface:\n            " +
		"exact: org.apache.dubbo.samples.basic.api.DemoService\n          method:\n            " +
		"exact: sayHello\n          foo:\n            exact: bar\n      route:\n        - destination:\n            " +
		"host: dubbo-sample-provider.meta-dubbo.svc.cluster.local\n            subset: v1"
)

func buildMockCrdParam() *meta.CrdParam {
	return &meta.CrdParam{
		InstanceUUID: instanceUUID,
		Content:      content,
	}
}

func buildMockCrdParams() *meta.CrdParams {
	return &meta.CrdParams{
		CsmMeshRequestParams: &meta.CsmMeshRequestParams{
			InstanceUUID: testInstanceUUID,
			PageSize:     10,
			PageNo:       1,
		},
		Keys:      []meta.CrdKey{},
		Namespace: "test-ns",
		Kind:      "VirtualService",
		Name:      "",
		Labels:    map[string]string{"foo": "bar"},
	}
}

func buildMockClusterList() *[]meta.Cluster {
	mockClusterList := []meta.Cluster{{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
		ClusterType:           "REMOTE",
	}}
	return &mockClusterList
}

func buildMockCluster() meta.Cluster {
	mockCluster := meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}
	return mockCluster
}

func buildMockInstances() *meta.Instances {
	instance := &meta.Instances{
		InstanceUUID:          testInstanceUUID,
		InstanceName:          testInstanceName,
		InstanceType:          testInstanceType,
		IstioInstallNamespace: testIstioInstallNamespace,
		InstanceManageScope:   testInstanceManageScope,
		ConfigCluster:         string(meta.ClusterTypeConfig),
	}
	return instance
}

func buildMockStandaloneInstances() *meta.Instances {
	instance := &meta.Instances{
		InstanceUUID:          testInstanceUUID,
		InstanceName:          testInstanceName,
		InstanceType:          standaloneType,
		IstioInstallNamespace: testIstioInstallNamespace,
		InstanceManageScope:   testInstanceManageScope,
	}
	return instance
}

func buildIstioVS() *v1beta1.VirtualService {
	return &v1beta1.VirtualService{
		TypeMeta: metav1.TypeMeta{
			Kind:       "VirtualService",
			APIVersion: "networking.istio.io/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-vs",
			Namespace: "test-ns",
			Labels:    map[string]string{"foo": "bar"},
		},
		Spec: networkingv1beta1.VirtualService{
			Hosts: []string{"test-host"},
			Http: []*networkingv1beta1.HTTPRoute{{
				Name: "http-test",
				Route: []*networkingv1beta1.HTTPRouteDestination{{
					Destination: &networkingv1beta1.Destination{
						Host: "target-host",
						Port: &networkingv1beta1.PortSelector{
							Number: 8080,
						},
					},
					Weight: 100,
				}},
			}},
		},
	}
}

func buildSidecar() *v1alpha3.Sidecar {
	return &v1alpha3.Sidecar{
		TypeMeta: metav1.TypeMeta{
			Kind:       "sidecar",
			APIVersion: "networking.istio.io/v1alpha3",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "default",
			Namespace: "test-ns",
		},
	}
}

func buildNs() *v1.Namespace {
	return &v1.Namespace{
		TypeMeta: metav1.TypeMeta{Kind: "Namespace"},
		ObjectMeta: metav1.ObjectMeta{
			Name:   "test-ns",
			Labels: map[string]string{constants.MeshInstanceId: testInstanceUUID},
		},
		Spec: v1.NamespaceSpec{},
	}
}

func TestCreateCrd(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockModelInstance.NewMockServiceInterface(ctrl)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockClusterModel := mockModelCluster.NewMockServiceInterface(ctrl)

	tests := []struct {
		name     string
		crdParam *meta.CrdParam
		wantErr  error
	}{
		{
			name:     "CreateCrd-ok",
			crdParam: buildMockCrdParam(),
			wantErr:  nil,
		},
		{
			name: "CreateCrd-sidecra",
			crdParam: &meta.CrdParam{
				InstanceUUID: instanceUUID,
				Content:      sidecarContent,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt:           NewOption(mockDB),
				instanceModel: mockInstanceModel,
				cceService:    mockCceService,
				clusterModel:  mockClusterModel,
			}
			mockInstanceCluster := buildMockCluster()
			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&mockInstanceCluster, meta.StandaloneMeshType, nil)

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockInstances(), nil)
			mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockClusterList(), nil)

			fakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)

			_, err := service.CreateCrd(mockCtx, tt.crdParam)
			assert.Nil(t, err)
		})
	}
}

func TestBatchCrd(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockModelInstance.NewMockServiceInterface(ctrl)
	cceServiceMock := mockCceService.NewMockClientInterface(ctrl)
	mockClusterModel := mockModelCluster.NewMockServiceInterface(ctrl)
	tests := []struct {
		name       string
		allowed    []string
		crdParam   *meta.CrdParam
		success    bool
		wantErrMsg string
	}{
		{
			name:       "BatchCrd-ok",
			allowed:    []string{"MetaRouter"},
			crdParam:   buildMockCrdParam(),
			success:    true,
			wantErrMsg: "",
		},
		{
			name:    "create-metaRoute",
			allowed: []string{"MetaRouter"},
			crdParam: &meta.CrdParam{
				InstanceUUID: instanceUUID,
				Content:      metaRoute,
			},
			success:    true,
			wantErrMsg: "",
		},
		{
			name: "create metaRoute fail",
			crdParam: &meta.CrdParam{
				InstanceUUID: instanceUUID,
				Content:      metaRoute,
			},
			success:    false,
			wantErrMsg: "Not support kind MetaRouter",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			viper.Set(crdAllowed, tt.allowed)

			service := &Service{
				opt:           NewOption(mockDB),
				instanceModel: mockInstanceModel,
				cceService:    cceServiceMock,
				clusterModel:  mockClusterModel,
			}
			mockInstanceCluster := buildMockCluster()
			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&mockInstanceCluster,
				meta.StandaloneMeshType, nil).AnyTimes()

			fakeClient := kube.NewFakeClient()
			cceServiceMock.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockInstances(), nil)
			mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockClusterList(), nil)

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(buildMockInstances(), nil).AnyTimes()
			_, err := service.BatchCrd(mockCtx, tt.crdParam)
			if tt.success {
				assert.Nil(t, err)
			} else {
				assert.ErrorContains(t, err, tt.wantErrMsg)
			}

			// 清理环境
			viper.Set(crdAllowed, nil)
		})
	}
}

func TestGetCrds(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockModelInstance.NewMockServiceInterface(ctrl)
	mockClusterModel := mockModelCluster.NewMockServiceInterface(ctrl)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	fakeClient := kube.NewFakeClient()

	gvr := schema.GroupVersionResource{
		Group:    "metaprotocol.aeraki.io",
		Version:  "v1alpha1",
		Resource: "MetaRouters",
	}
	dec := k8sYaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme)
	obj := &unstructured.Unstructured{}
	_, _, _ = dec.Decode([]byte(metaRoute), nil, obj)

	_, _ = fakeClient.Istio().NetworkingV1beta1().VirtualServices("test-ns").Create(context.TODO(), buildIstioVS(), metav1.CreateOptions{})
	_, _ = fakeClient.Kube().CoreV1().Namespaces().Create(context.TODO(), buildNs(), metav1.CreateOptions{})
	_, _ = fakeClient.Dynamic().Resource(gvr).Namespace("test-ns").Create(context.TODO(), obj, metav1.CreateOptions{})

	tests := []struct {
		name      string
		crdParams *meta.CrdParams
		instance  *meta.Instances
		wantErr   error
	}{
		{
			name:      "GetCrd-ok",
			crdParams: buildMockCrdParams(),
			instance:  buildMockInstances(),
			wantErr:   nil,
		},
		{
			name: "GetCrds-scope-ok",
			crdParams: &meta.CrdParams{
				CsmMeshRequestParams: &meta.CsmMeshRequestParams{
					InstanceUUID: testInstanceUUID,
					PageSize:     10,
					PageNo:       1,
				},
				Keys:      []meta.CrdKey{},
				Namespace: "test-ns",
				Kind:      "MetaRouter",
				Name:      "",
			},
			instance: &meta.Instances{
				InstanceUUID:          testInstanceUUID,
				InstanceName:          testInstanceName,
				InstanceType:          testInstanceType,
				IstioInstallNamespace: testIstioInstallNamespace,
				InstanceManageScope:   "cluster",
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			viper.Set(crdAllowed, []string{"MetaRouter"})
			service := &Service{
				opt:           NewOption(mockDB),
				instanceModel: mockInstanceModel,
				cceService:    mockCceService,
				clusterModel:  mockClusterModel,
			}
			mockInstanceCluster := buildMockCluster()
			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&mockInstanceCluster, meta.StandaloneMeshType, nil).AnyTimes()

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockInstances(), nil)
			mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockClusterList(), nil)

			mockCceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			cs, _, err := service.GetCrds(mockCtx, tt.crdParams)
			assert.NotNil(t, cs)
			assert.Nil(t, err)
		})
	}
}

func TestGetCrd(t *testing.T) {
	tests := []struct {
		name     string
		crdParam *meta.CrdParam
		cluster  *meta.Cluster
		wantErr  error
	}{
		{
			name: "get metaRoute not found",
			crdParam: &meta.CrdParam{
				InstanceUUID: instanceUUID,
				Content:      metaRoute,
				CrdKey: meta.CrdKey{
					Name:      "test-metaprotocol-dubbo-route",
					Namespace: "test-ns",
					Kind:      "MetaRouter",
				},
			},
			cluster: &meta.Cluster{
				InstanceUUID:          testInstanceUUID,
				ClusterUUID:           testClusterUUID,
				ClusterName:           testClusterName,
				Region:                testRegion,
				IstioInstallNamespace: testInstanceManageScope,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockInstanceModel := mockModelInstance.NewMockServiceInterface(ctrl)
			cceService := mockCceService.NewMockClientInterface(ctrl)
			mockClusterModel := mockModelCluster.NewMockServiceInterface(ctrl)
			fakeClient := kube.NewFakeClient()

			_, _ = fakeClient.Kube().CoreV1().Namespaces().Create(context.TODO(), buildNs(), metav1.CreateOptions{})

			viper.Set(crdAllowed, []string{"MetaRouter"})
			service := &Service{
				opt:           NewOption(mockDB),
				instanceModel: mockInstanceModel,
				cceService:    cceService,
				clusterModel:  mockClusterModel,
			}
			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(tt.cluster,
				meta.StandaloneMeshType, nil).AnyTimes()
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockInstances(), nil)
			mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockClusterList(), nil)

			cceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
				fakeClient, nil).AnyTimes()

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(
				buildMockInstances(), nil).AnyTimes()
			_, err := service.GetCrd(mockCtx, tt.crdParam)
			assert.EqualError(t, err, "metarouters.metaprotocol.aeraki.io"+
				" \"test-metaprotocol-dubbo-route\" not found")
		})
	}
}

func TestUpdateCrd(t *testing.T) {

	tests := []struct {
		name       string
		crdParam   *meta.CrdParam
		cluster    *meta.Cluster
		wantErrStr string
	}{
		{
			name: "update metaRoute not found",
			crdParam: &meta.CrdParam{
				InstanceUUID: instanceUUID,
				Content:      metaRoute,
				CrdKey: meta.CrdKey{
					Name:      "test-metaprotocol-dubbo-route",
					Namespace: "test-ns",
					Kind:      "MetaRouter",
				},
			},
			cluster: &meta.Cluster{
				InstanceUUID:          testInstanceUUID,
				ClusterUUID:           testClusterUUID,
				ClusterName:           testClusterName,
				Region:                testRegion,
				IstioInstallNamespace: testInstanceManageScope,
			},
			wantErrStr: "metarouters.metaprotocol.aeraki.io" +
				" \"test-metaprotocol-dubbo-route\" not found",
		},
		{
			name: "update sidecar not found",
			crdParam: &meta.CrdParam{
				InstanceUUID: instanceUUID,
				Content:      sidecarContent,
				CrdKey: meta.CrdKey{
					Name:      "test-sidecar",
					Namespace: "test-ns",
					Kind:      "Sidecar",
				},
			},
			cluster: &meta.Cluster{
				InstanceUUID:          testInstanceUUID,
				ClusterUUID:           testClusterUUID,
				ClusterName:           testClusterName,
				Region:                testRegion,
				IstioInstallNamespace: testInstanceManageScope,
			},
			wantErrStr: "not found",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockInstanceModel := mockModelInstance.NewMockServiceInterface(ctrl)
			cceService := mockCceService.NewMockClientInterface(ctrl)
			mockClusterModel := mockModelCluster.NewMockServiceInterface(ctrl)
			fakeClient := kube.NewFakeClient()

			_, _ = fakeClient.Kube().CoreV1().Namespaces().Create(context.TODO(), buildNs(), metav1.CreateOptions{})
			_, err1 := fakeClient.Istio().NetworkingV1alpha3().Sidecars("test-ns").Create(context.TODO(), buildSidecar(), metav1.CreateOptions{})
			if err1 != nil {

			}
			viper.Set(crdAllowed, []string{"MetaRouter"})
			service := &Service{
				opt:           NewOption(mockDB),
				instanceModel: mockInstanceModel,
				cceService:    cceService,
				clusterModel:  mockClusterModel,
			}
			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(tt.cluster,
				meta.StandaloneMeshType, nil).AnyTimes()
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockInstances(), nil)
			mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockClusterList(), nil)

			cceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
				fakeClient, nil).AnyTimes()

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(
				buildMockStandaloneInstances(), nil).AnyTimes()
			_, err := service.UpdateCrd(mockCtx, tt.crdParam)
			assert.Contains(t, err.Error(), tt.wantErrStr)
		})
	}
}

func TestDeleteCrd(t *testing.T) {
	tests := []struct {
		name      string
		crdParams *meta.CrdParams
		cluster   *meta.Cluster
		wantErr   error
	}{
		{
			name: "delete metaRoute not found",
			crdParams: &meta.CrdParams{
				CsmMeshRequestParams: &meta.CsmMeshRequestParams{
					InstanceUUID: testInstanceUUID,
					PageSize:     10,
					PageNo:       1,
				},
				Keys: []meta.CrdKey{{
					Namespace: "test-ns",
					Kind:      "MetaRouter",
					Name:      "test-route",
				}},
			},
			cluster: &meta.Cluster{
				InstanceUUID:          testInstanceUUID,
				ClusterUUID:           testClusterUUID,
				ClusterName:           testClusterName,
				Region:                testRegion,
				IstioInstallNamespace: testInstanceManageScope,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockInstanceModel := mockModelInstance.NewMockServiceInterface(ctrl)
			cceService := mockCceService.NewMockClientInterface(ctrl)
			mockClusterModel := mockModelCluster.NewMockServiceInterface(ctrl)
			fakeClient := kube.NewFakeClient()

			_, _ = fakeClient.Kube().CoreV1().Namespaces().Create(context.TODO(), buildNs(), metav1.CreateOptions{})

			viper.Set(crdAllowed, []string{"MetaRouter"})
			service := &Service{
				opt:           NewOption(mockDB),
				instanceModel: mockInstanceModel,
				cceService:    cceService,
				clusterModel:  mockClusterModel,
			}
			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(tt.cluster,
				meta.StandaloneMeshType, nil).AnyTimes()

			cceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
				fakeClient, nil).AnyTimes()
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockInstances(), nil)
			mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(mockCtx, gomock.Any()).AnyTimes().Return(buildMockClusterList(), nil)

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(
				buildMockInstances(), nil).AnyTimes()
			err := service.DeleteCrds(mockCtx, tt.crdParams)
			assert.EqualError(t, err, "metarouters.metaprotocol.aeraki.io"+
				" \"test-route\" not found")
		})
	}
}
