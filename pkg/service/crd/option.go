package crd

import (
	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

const (
	crdAllowedKind = "crd.allowedKind"
)

type Option struct {
	DB             *dbutil.DB
	crdAllowedKind []string
}

func NewOption(d *gorm.DB) *Option {
	return &Option{
		DB:             dbutil.NewDB(d),
		crdAllowedKind: viper.GetStringSlice(crdAllowedKind),
	}
}
