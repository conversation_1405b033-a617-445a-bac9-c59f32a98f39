package crd

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/jucardi/go-streams/streams"
	"github.com/pkg/errors"
	"gopkg.in/yaml.v2"
	extensionsV1alpha1 "istio.io/client-go/pkg/apis/extensions/v1alpha1"
	"istio.io/client-go/pkg/apis/networking/v1alpha3"
	"istio.io/client-go/pkg/apis/security/v1beta1"
	"istio.io/client-go/pkg/apis/telemetry/v1alpha1"
	istioClient "istio.io/client-go/pkg/clientset/versioned"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	k8sMeta "k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime/schema"
	k8sYaml "k8s.io/apimachinery/pkg/runtime/serializer/yaml"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/ptrutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/sliceutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

const (
	crdAPIVersionV1beta1  = "networking.istio.io/v1beta1"
	crdAPIVersionV1alpha3 = "networking.istio.io/v1alpha3"

	listTimeout = 2
	maxChunk    = 100

	crdListFieldItems              = "Items"
	crdListFieldRemainingItemCount = "RemainingItemCount"
	crdListFieldContinue           = "Continue"

	crdFieldAPIVersion        = "APIVersion"
	crdFieldNamespace         = "Namespace"
	crdFieldKind              = "Kind"
	crdFieldName              = "Name"
	crdFieldCreationTimestamp = "CreationTimestamp"

	crdManagedFields    = "ManagedFields"
	crdManagedFieldTime = "Time"
)

type Service struct {
	opt *Option

	clusterModel  cluster.ServiceInterface
	instanceModel instances.ServiceInterface
	cceService    cce.ClientInterface
}

func NewCrdService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt: option,

		clusterModel:  cluster.NewClusterService(cluster.NewOption(gormDB)),
		instanceModel: instances.NewInstancesService(instances.NewOption(gormDB)),
		cceService:    cce.NewClientService(),
	}
}

func (s *Service) GetCrds(ctx csmContext.CsmContext, params *meta.CrdParams) (crds []meta.Crd, pr *meta.PageResult, err error) {
	// 支持分页获取 crd 列表
	// 支持按更新时间排序
	// 支持 kind 模糊查询
	// 支持 crd name 模糊查询
	k8sClient, err := s.getK8sClient(ctx, params.InstanceUUID)
	if err != nil {
		return nil, nil, err
	}
	// 兼容config集群在用户集群的托管网格场景 getK8sClient方法 err为nil时，k8sClient也可能为空。
	// 这种情况当前方法直接返回空
	if k8sClient == nil {
		return nil, &meta.PageResult{}, nil
	}

	cs, _, err := s.getAllCrds(ctx, k8sClient, params)
	if err != nil {
		return nil, nil, err
	}

	// 支持模糊查询
	crds = streams.
		FromArray(cs).
		Filter(func(v interface{}) bool {
			c := v.(meta.Crd)
			if len(strings.TrimSpace(params.Namespace)) == 0 {
				return true
			}
			return strings.EqualFold(params.Namespace, c.Namespace)
		}).
		Filter(func(v interface{}) bool {
			c := v.(meta.Crd)
			if len(strings.TrimSpace(params.Kind)) == 0 {
				return true
			}
			kinds := strings.Split(params.Kind, ",")
			return sliceutil.StringContains(kinds, c.Kind)
		}).
		Filter(func(v interface{}) bool {
			c := v.(meta.Crd)
			if len(strings.TrimSpace(params.Name)) == 0 {
				return true
			}
			return strings.EqualFold(params.Name, c.Name)
		}).
		Filter(func(v interface{}) bool {
			c := v.(meta.Crd)
			if len(params.Labels) == 0 {
				return true
			}
			return isLabelsMatch(params.Labels, c.Content)
		}).
		Filter(func(v interface{}) bool {
			c := v.(meta.Crd)
			if strings.EqualFold(params.KeywordType, vo.QueryCrdKind) {
				return strings.Contains(c.Kind, params.Keyword)
			}
			return true
		}).
		Filter(func(v interface{}) bool {
			c := v.(meta.Crd)
			if strings.EqualFold(params.KeywordType, vo.QueryCrdName) {
				return strings.Contains(c.Name, params.Keyword)
			}
			return true
		}).
		ToArray().([]meta.Crd)

	pageResult := &meta.PageResult{
		PageSize:   params.PageSize,
		PageNo:     params.PageNo,
		TotalCount: int64(len(crds)),
	}

	start := (params.PageNo - 1) * params.PageSize
	end := util.MinInt64(params.PageNo*params.PageSize, int64(len(crds)))
	if start >= end {
		return []meta.Crd{}, pageResult, nil
	}
	sliceOfCrds := crds[start:end]

	return sliceOfCrds, pageResult, nil
}

func (s *Service) GetCrd(ctx csmContext.CsmContext, param *meta.CrdParam) (crd *meta.Crd, err error) {
	k8sClient, err := s.getK8sClient(ctx, param.InstanceUUID)
	if err != nil {
		return nil, err
	}

	// 兼容config集群在用户集群的托管网格场景 getK8sClient方法 err为nil时，k8sClient也可能为空。
	// 这种情况当前方法直接返回空
	if k8sClient == nil {
		return nil, nil
	}

	// 处理非istio的crd
	if s.isAllowedCrdKind(param.Kind) {
		return s.getAllowedKindCrd(ctx, k8sClient, param.Namespace, param.Kind, param.Name)
	}
	crd, err = s.getCrd(ctx, k8sClient.Istio(), param.Namespace, meta.Kind(param.Kind), param.Name)
	if err != nil {
		return nil, err
	}

	// TODO: 更多边界处理
	return crd, nil

}

func (s *Service) adjustParam(ctx csmContext.CsmContext, param *meta.CrdParam) (*meta.CrdParam, error) {
	instanceInfo, err := s.instanceModel.GetInstanceByInstanceUUID(ctx, param.InstanceUUID)
	if err != nil {
		return nil, err
	}
	// istio资源提交在数据面集群的不需要强制变更ns
	if instanceInfo.InstanceType == string(meta.HostingMeshType) &&
		strings.EqualFold(instanceInfo.ConfigCluster, constants.ConfigClusterEXTERNAL) {
		namespace := instanceInfo.IstioInstallNamespace
		param.Namespace = namespace
		content, contentErr := s.convertYamlNamespace(param.Content, namespace)
		if contentErr != nil {
			return nil, contentErr
		}
		param.Content = content
	}
	return param, nil
}

func (s *Service) convertYamlNamespace(content string, namespace string) (string, error) {
	m := make(map[interface{}]interface{})
	err := yaml.Unmarshal([]byte(content), &m)
	if err != nil {
		return "", errors.Errorf("ConvertYamlNamespace the format of crd is invalid error %v", err)
	}
	if metadata, metaOk := m["metadata"]; metaOk {
		md := metadata.(map[interface{}]interface{})
		md["namespace"] = namespace
	}
	value, err := yaml.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(value), nil
}

func (s *Service) CreateCrd(ctx csmContext.CsmContext, param *meta.CrdParam) (crd *meta.Crd, err error) {
	client, err := s.getK8sClient(ctx, param.InstanceUUID)
	if err != nil {
		return nil, err
	}
	// 兼容config集群在用户集群的托管网格场景 getK8sClient方法 err为nil时，k8sClient也可能为空。
	// 这种情况当前方法直接返回空
	if client == nil {
		return nil, nil
	}
	return s.createOneCrd(ctx, client, param)
}

func (s *Service) BatchCrd(ctx csmContext.CsmContext, param *meta.CrdParam) (*meta.BatchCreateCrdInfo, error) {
	client, err := s.getK8sClient(ctx, param.InstanceUUID)
	if err != nil {
		return nil, err
	}

	// 兼容config集群在用户集群的托管网格场景 getK8sClient方法 err为nil时，k8sClient也可能为空。
	// 这种情况当前方法直接返回空
	if client == nil {
		return &meta.BatchCreateCrdInfo{}, nil
	}

	var resultCrd = make([]meta.Crd, 0)
	var failedDetails strings.Builder

	// 支持批量创建crd
	// --- 分割crd
	crdContentList := meta.SplitYaml(param.Content)
	for index := range crdContentList {
		content := strings.TrimSpace(crdContentList[index])
		if len(content) == 0 {
			continue
		}
		newParam := &meta.CrdParam{
			InstanceUUID: param.InstanceUUID,
			Content:      content,
		}

		oneCrd, oneCrdErr := s.createOneCrd(ctx, client, newParam)
		if oneCrdErr != nil {
			failedDetails.WriteString(fmt.Sprintf("error is: %s", oneCrdErr.Error()))
			err = oneCrdErr
			continue
		}
		resultCrd = append(resultCrd, *oneCrd)
	}

	resultInfo := &meta.BatchCreateCrdInfo{
		SuccessCrdList:   resultCrd,
		FailedCrdMessage: failedDetails.String(),
	}
	// 全失败时，返回最后一个报错内容
	if len(resultCrd) == 0 {
		return resultInfo, err
	}

	return resultInfo, nil
}

func (s *Service) createOneCrd(ctx csmContext.CsmContext, client kube.Client, param *meta.CrdParam) (crd *meta.Crd, err error) {
	param, err = s.adjustParam(ctx, param)
	if err != nil {
		return nil, err
	}

	crd, err = s.createCrd(ctx, client, param.Content)
	if err != nil {
		return nil, err
	}
	// TODO: 更多边界处理
	return crd, nil
}

func (s *Service) UpdateCrd(ctx csmContext.CsmContext, param *meta.CrdParam) (crd *meta.Crd, err error) {
	k8sClient, err := s.getK8sClient(ctx, param.InstanceUUID)
	if err != nil {
		return nil, err
	}

	// 兼容config集群在用户集群的托管网格场景 getK8sClient方法 err为nil时，k8sClient也可能为空。
	// 这种情况当前方法直接返回空
	if k8sClient == nil {
		return nil, nil
	}

	param, err = s.adjustParam(ctx, param)
	if err != nil {
		return nil, err
	}

	crd, err = s.updateCrd(ctx, k8sClient, param.Namespace, param.Kind, param.Name, param.Content)
	if err != nil {
		return nil, err
	}

	// TODO: 更多边界处理
	return crd, nil
}

func (s *Service) DeleteCrds(ctx csmContext.CsmContext, params *meta.CrdParams) (err error) {
	k8sClient, err := s.getK8sClient(ctx, params.InstanceUUID)
	if err != nil {
		return err
	}

	// 兼容config集群在用户集群的托管网格场景 getK8sClient方法 err为nil时，k8sClient也可能为空。
	// 这种情况当前方法直接返回空
	if k8sClient == nil {
		return nil
	}

	for _, ck := range params.Keys {
		err = s.deleteCrd(ctx, k8sClient, ck.Namespace, ck.Kind, ck.Name)
		if err != nil {
			// TODO: 需产品确认
			return err
		}
	}

	// TODO: 更多边界处理
	return nil

}

func assertNilOrError(ctx csmContext.CsmContext, ptr interface{}, err error) bool {
	t := reflect.TypeOf(ptr)
	if t.Kind() != reflect.Ptr {
		ctx.CsmLogger().Errorf("All types must be pointers to structs.")
		return false
	}
	t = t.Elem()
	if t.Kind() != reflect.Struct {
		ctx.CsmLogger().Errorf("All types must be pointers to structs.")
		return false
	}

	if err != nil || ptr == nil {
		return false
	}

	return true
}

func convertToCrd(istioCrd interface{}, namespace, kind, name string, updatedAt time.Time) (*meta.Crd, error) {
	// Convert istio crd to crd model
	crdYaml, err := util.StructToYamlString(istioCrd)
	if err != nil {
		return nil, err
	}
	return &meta.Crd{
		Namespace: namespace,
		Kind:      kind,
		Name:      name,
		UpdatedAt: updatedAt,
		Content:   crdYaml,
	}, nil
}

func (s *Service) getK8sClient(ctx csmContext.CsmContext, meshInstanceId string) (kube.Client, error) {
	c, meshType, err := s.instanceModel.GetInstanceIstiodCluster(ctx, meshInstanceId)
	if err != nil {
		// TODO: 添加日志，并错误处理
		return nil, err
	}
	// 托管网格需要判断istio crd是否提交到config集群
	if meshType == meta.HostingMeshType {
		csmInstance, insErr := s.instanceModel.GetInstanceByInstanceUUID(ctx, meshInstanceId)
		if insErr != nil {
			return nil, err
		}
		if strings.EqualFold(csmInstance.ConfigCluster, constants.ConfigClusterREMOTE) {
			// TODO 优化必须有config集群
			// 没有config集群时，直接返回nil
			allCluster, getErr := s.clusterModel.GetAllRemoteClusterByInstanceUUID(ctx, meshInstanceId)
			if getErr != nil {
				return nil, err
			}
			// 这种情况表示没有config集群
			if allCluster == nil || len(*allCluster) <= 0 {
				ctx.CsmLogger().Infof("meshInstanceId is %s don't have config cluster", meshInstanceId)
				return nil, nil
			}
			var targetCluster meta.Cluster
			existConfig := false
			// 下面兼容异常情况：存在remote集群，但是不存在config集群。这种情况控制台不可能出现，一般为用户手动操作导致。
			for _, remoteCluster := range *allCluster {
				if strings.EqualFold(remoteCluster.ClusterType, string(meta.ClusterTypeConfig)) {
					targetCluster = remoteCluster
					existConfig = true
					break
				}
			}
			// config集群是用户集群，需要通过sts方式获取，所以传入Standalone MeshType类型
			if existConfig {
				return s.cceService.NewClient(ctx, targetCluster.Region, targetCluster.ClusterUUID, meta.StandaloneMeshType)
			}
			// 这种情况表示没有config集群
			ctx.CsmLogger().Infof("meshInstanceId is %s don't have config cluster", meshInstanceId)
			return nil, nil
		}
	}

	return s.cceService.NewClient(ctx, c.Region, c.ClusterUUID, meshType)
}

func (s *Service) getIstioClient(ctx csmContext.CsmContext, meshInstanceId string) (istioClient.Interface, error) {
	client, err := s.getK8sClient(ctx, meshInstanceId)
	if err != nil {
		return nil, err
	}

	return client.Istio(), nil
}

func (s *Service) getClient(ctx csmContext.CsmContext, meshInstanceId string) (istioClient.Interface, kubernetes.Interface, error) {
	client, err := s.getK8sClient(ctx, meshInstanceId)
	if err != nil {
		return nil, nil, err
	}

	return client.Istio(), client.Kube(), nil
}

func (s *Service) getAllowedKindCrd(ctx csmContext.CsmContext, client kube.Client, namespace, kind, name string) (crd *meta.Crd, err error) {
	// TODO 待优化
	gvr := schema.GroupVersionResource{
		Group:    getDynamicGroup(kind),
		Version:  "v1alpha1",
		Resource: getDynamicResource(kind),
	}
	var dynamicClient dynamic.ResourceInterface
	if namespace == "" {
		dynamicClient = client.Dynamic().Resource(gvr)
	} else {
		dynamicClient = client.Dynamic().Resource(gvr).Namespace(namespace)
	}
	obj, allErr := dynamicClient.Get(context.TODO(), name, metav1.GetOptions{})
	if allErr != nil {
		ctx.CsmLogger().Errorf("Get kind %s name %s err", kind, name, allErr.Error())
		return nil, allErr
	}
	if obj == nil {
		ctx.CsmLogger().Errorf("not found kind %s name %s crd in namespace %s", kind, name, namespace)
		return nil, errors.New(fmt.Sprintf("not found kind %s name %s crd in namespace %s",
			kind, name, namespace))
	}

	return convertToCrd(obj, obj.GetNamespace(), obj.GetKind(), obj.GetName(), obj.GetCreationTimestamp().Time)
}

func (s *Service) getCrd(ctx csmContext.CsmContext, client istioClient.Interface, namespace string, kind meta.Kind, name string) (
	crd *meta.Crd, err error) {
	istioCrd, err := getIstioCrd(ctx, client, namespace, kind, name)
	if err != nil {
		return nil, err
	}

	v := reflect.ValueOf(istioCrd)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	} else if v.Kind() != reflect.Struct {
		return nil, errors.Errorf("Cannot reflect to struct type. %v", istioCrd)
	}

	// 补充 gvk
	v.FieldByName(crdFieldAPIVersion).Set(func() reflect.Value {
		if kind == meta.EnvoyFilter {
			return reflect.ValueOf(crdAPIVersionV1alpha3)
		}
		return reflect.ValueOf(crdAPIVersionV1beta1)
	}())
	v.FieldByName(crdFieldKind).Set(reflect.ValueOf(string(kind)))

	updatedAt := calUpdatedTime(istioCrd)

	// 添加 CRD
	crd, err = convertToCrd(istioCrd, namespace, string(kind), name, *updatedAt)
	if err != nil {
		return nil, err
	}
	return crd, nil
}

func calUpdatedTime(istioCrd interface{}) *time.Time {
	v := reflect.ValueOf(istioCrd)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	} else if v.Kind() != reflect.Struct {
		return nil
	}

	creationTime := v.FieldByName(crdFieldCreationTimestamp).Interface().(metav1.Time)
	updatedAt := creationTime.Time
	itemsValue := v.FieldByName(crdManagedFields)
	for i := 0; i < itemsValue.Len(); i++ {
		item := itemsValue.Index(i)
		updatedTime := item.FieldByName(crdManagedFieldTime).Interface().(*metav1.Time)
		if updatedTime.Time.After(updatedAt) {
			updatedAt = updatedTime.Time
		}
	}

	return &updatedAt
}

func getIstioCrd(ctx csmContext.CsmContext, client istioClient.Interface, namespace string, kind meta.Kind, name string) (
	istioCrd interface{}, err error) {
	var ic interface{}

	switch kind {
	case meta.DestinationRule:
		ic, err = client.NetworkingV1beta1().DestinationRules(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.EnvoyFilter:
		ic, err = client.NetworkingV1alpha3().EnvoyFilters(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.Gateway:
		ic, err = client.NetworkingV1beta1().Gateways(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.ServiceEntry:
		ic, err = client.NetworkingV1beta1().ServiceEntries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.Sidecar:
		ic, err = client.NetworkingV1beta1().Sidecars(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.VirtualService:
		ic, err = client.NetworkingV1beta1().VirtualServices(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.WorkloadEntry:
		ic, err = client.NetworkingV1beta1().WorkloadEntries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.WorkloadGroup:
		ic, err = client.NetworkingV1beta1().WorkloadGroups(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.AuthorizationPolicy:
		ic, err = client.SecurityV1beta1().AuthorizationPolicies(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.PeerAuthentication:
		ic, err = client.SecurityV1beta1().PeerAuthentications(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.RequestAuthentication:
		ic, err = client.SecurityV1beta1().RequestAuthentications(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.Telemetry:
		ic, err = client.TelemetryV1alpha1().Telemetries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	case meta.WasmPlugin:
		ic, err = client.ExtensionsV1alpha1().WasmPlugins(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	default:
		return nil, fmt.Errorf("not support crd kind: %s", kind)
	}
	if !assertNilOrError(ctx, ic, err) {
		return nil, err
	}
	return ic, nil
}

func listIstioCrds(client istioClient.Interface, namespace string,
	kind meta.Kind, continueToken string, ls map[string]string) (
	istioCrds interface{}, err error) {
	// TODO: List Watch，缓存到内存中，实现条件查询，模糊查询
	var crdList interface{}

	labelSelector := metav1.LabelSelector{MatchLabels: ls}
	listOptions := metav1.ListOptions{
		TimeoutSeconds: ptrutil.Int64(int64(listTimeout)),
		Limit:          maxChunk,
		Continue:       continueToken,
		LabelSelector:  labels.Set(labelSelector.MatchLabels).String(),
	}

	switch kind {
	case meta.DestinationRule:
		crdList, err = client.NetworkingV1beta1().DestinationRules(namespace).List(context.TODO(), listOptions)
	case meta.EnvoyFilter:
		crdList, err = client.NetworkingV1alpha3().EnvoyFilters(namespace).List(context.TODO(), listOptions)
	case meta.Gateway:
		crdList, err = client.NetworkingV1beta1().Gateways(namespace).List(context.TODO(), listOptions)
	case meta.ServiceEntry:
		crdList, err = client.NetworkingV1beta1().ServiceEntries(namespace).List(context.TODO(), listOptions)
	case meta.Sidecar:
		crdList, err = client.NetworkingV1beta1().Sidecars(namespace).List(context.TODO(), listOptions)
	case meta.VirtualService:
		crdList, err = client.NetworkingV1beta1().VirtualServices(namespace).List(context.TODO(), listOptions)
	case meta.WorkloadEntry:
		crdList, err = client.NetworkingV1beta1().WorkloadEntries(namespace).List(context.TODO(), listOptions)
	case meta.WorkloadGroup:
		crdList, err = client.NetworkingV1beta1().WorkloadGroups(namespace).List(context.TODO(), listOptions)
	case meta.AuthorizationPolicy:
		crdList, err = client.SecurityV1beta1().AuthorizationPolicies(namespace).List(context.TODO(), listOptions)
	case meta.PeerAuthentication:
		crdList, err = client.SecurityV1beta1().PeerAuthentications(namespace).List(context.TODO(), listOptions)
	case meta.RequestAuthentication:
		crdList, err = client.SecurityV1beta1().RequestAuthentications(namespace).List(context.TODO(), listOptions)
	case meta.Telemetry:
		crdList, err = client.TelemetryV1alpha1().Telemetries(namespace).List(context.TODO(), listOptions)
	case meta.WasmPlugin:
		crdList, err = client.ExtensionsV1alpha1().WasmPlugins(namespace).List(context.TODO(), listOptions)
	default:
		return nil, fmt.Errorf("not support crd kind: %s", kind)
	}
	if err != nil {
		return nil, err
	}

	return crdList, err
}

func getCrdsByNamespaceAndKind(crds *[]meta.Crd, namespace string, kind meta.Kind, client istioClient.Interface, labels map[string]string) (
	totalCount int64, err error) {
	// TODO: 缓存到内存中，后续优化
	var count int64

	var continueToken string
	for {
		crdList, err := listIstioCrds(client, namespace, kind, continueToken, labels)
		if err != nil {
			return 0, err
		}

		v := reflect.ValueOf(crdList)
		if v.Kind() == reflect.Ptr {
			v = v.Elem()
		} else if v.Kind() != reflect.Struct {
			return 0, errors.New("Cannot reflect to struct type.")
		}

		itemsValue := v.FieldByName(crdListFieldItems)
		if crdList == nil || itemsValue.Len() == 0 {
			break
		}

		// 添加 CRD
		for i := 0; i < itemsValue.Len(); i++ {
			crdItem := itemsValue.Index(i)
			crdns := crdItem.FieldByName(crdFieldNamespace).String()
			crdkind := crdItem.FieldByName(crdFieldKind).String()
			name := crdItem.FieldByName(crdFieldName).String()
			updatedAt := calUpdatedTime(crdItem.Interface())

			// TODO: 实现更新时间
			c, err := convertToCrd(crdItem.Interface(), crdns, crdkind, name, *updatedAt)
			if err != nil {
				return 0, err
			}
			*crds = append(*crds, *c)
		}
		// 计数
		count += int64(itemsValue.Len())

		continueToken = v.FieldByName(crdListFieldContinue).String()
		if len(continueToken) == 0 {
			// 没有更多 CRD
			break
		}
	}

	return count, nil
}

// 同步获取所有 crd
func (s *Service) getAllCrds(ctx csmContext.CsmContext, k8sClient kube.Client, params *meta.CrdParams) (crds []meta.Crd, totalCount int64, err error) {
	// TODO: 缓存到内存中，后续优化
	crds = make([]meta.Crd, 0)
	var count int64

	instance, err := s.instanceModel.GetInstanceByInstanceUUID(ctx, params.InstanceUUID)
	if err != nil {
		return nil, 0, err
	}
	client := k8sClient.Istio()
	kubeClient := k8sClient.Kube()

	// 兼容istio资源保存在数据面集群的托管网格场景
	if strings.EqualFold(instance.InstanceManageScope, string(meta.InstanceManageClusterScope)) ||
		(meta.MeshType(instance.InstanceType) == meta.HostingMeshType &&
			strings.EqualFold(instance.ConfigCluster, constants.ConfigClusterREMOTE)) {
		for _, kind := range meta.GetRegisteredCrdKind() {
			var c int64
			c, err = getCrdsByNamespaceAndKind(&crds, "", kind, client, params.Labels)
			if err != nil {
				// TODO:
				continue
			}
			count += c
		}
		listCrd, num, allowedErr := s.listAllowedKindCrd(ctx, k8sClient, "")
		if allowedErr != nil {
			return nil, 0, allowedErr
		}
		crds = append(crds, listCrd...)
		count += num
	} else {
		// 获取 mesh 实例相关的 namespace
		labelSelector := fmt.Sprintf(constants.MeshInstanceId+"=%s", params.InstanceUUID)
		listOptions := metav1.ListOptions{
			LabelSelector: labelSelector,
		}
		namespaceList, err := kubeClient.CoreV1().Namespaces().List(context.TODO(), listOptions)
		if err != nil {
			ctx.CsmLogger().Errorf("get namespace failed. list options: %v", listOptions)
			return nil, 0, err
		}
		if namespaceList == nil || namespaceList.Items == nil || len(namespaceList.Items) == 0 {
			ctx.CsmLogger().Errorf("can not get namespaces. list options: %v", listOptions)
			return nil, 0, nil
		}

		var wg sync.WaitGroup
		// crd列表更新加锁
		var crdsMu sync.Mutex

		wg.Add(1)
		// 串行查询较慢，修改为并行查询
		// 并行查询按种类和命名空间分类的CRD
		for _, kind := range meta.GetRegisteredCrdKind() {
			wg.Add(1)
			go func(ctx csmContext.CsmContext, kind meta.Kind) {
				defer wg.Done()
				for _, ns := range namespaceList.Items {
					c, err := getCrdsByNamespaceAndKind(&crds, ns.Name, kind, client, params.Labels)
					if err != nil {
						// TODO 处理异常，目前暂时日志打印
						ctx.CsmLogger().Errorf("get ns %s crds kind %s failed. err: %s", ns.Name, kind, err.Error())
						return
					}
					atomic.AddInt64(&count, c)
				}
			}(ctx, kind)
		}

		// 并行查询非istio类型的CRD
		for _, ns := range namespaceList.Items {
			wg.Add(1)
			go func(ctx csmContext.CsmContext, crdsMu *sync.Mutex, ns string) {
				defer wg.Done()
				listCrd, num, allowedErr := s.listAllowedKindCrd(ctx, k8sClient, ns)
				if allowedErr != nil {
					// TODO 处理异常，目前暂时日志打印
					ctx.CsmLogger().Errorf("get ns %s crds failed. err: %s", ns, allowedErr.Error())
					return
				}
				// 注意：因为append不是线程安全的，所以需要在此加锁
				crdsMu.Lock()
				crds = append(crds, listCrd...)
				crdsMu.Unlock()
				atomic.AddInt64(&count, num)
			}(ctx, &crdsMu, ns.Name)
		}

		wg.Done()
		wg.Wait() // 等待所有并行查询完成
	}

	return crds, count, nil
}

func getDynamicGroup(crdKind string) string {
	if strings.EqualFold(crdKind, meta.MetaRouter) || strings.EqualFold(crdKind, meta.ApplicationProtocol) {
		return meta.GroupMetaprotocol
	} else if strings.EqualFold(crdKind, meta.DubboAuthorizationPolicy) {
		return meta.GroupDubbo
	} else if strings.EqualFold(crdKind, meta.RedisService) || strings.EqualFold(crdKind, meta.RedisDestination) {
		return meta.GroupRedis
	}

	return ""
}

func getDynamicResource(crdKind string) string {
	if strings.EqualFold(crdKind, meta.DubboAuthorizationPolicy) {
		return meta.ResourceDubboAuthorizationPolicy
	}

	return strings.ToLower(crdKind) + "s"
}

func (s *Service) listAllowedKindCrd(ctx csmContext.CsmContext, k8sClient kube.Client, namespace string) ([]meta.Crd, int64, error) {
	crds := make([]meta.Crd, 0)
	var count int64
	// 处理非istio的crd
	for _, crdKind := range s.opt.crdAllowedKind {
		// TODO 待优化
		gvr := schema.GroupVersionResource{
			Group:    getDynamicGroup(crdKind),
			Version:  "v1alpha1",
			Resource: getDynamicResource(crdKind),
		}
		var dynamicClient dynamic.ResourceInterface
		if namespace == "" {
			dynamicClient = k8sClient.Dynamic().Resource(gvr)
		} else {
			dynamicClient = k8sClient.Dynamic().Resource(gvr).Namespace(namespace)
		}
		listcrd, allErr := dynamicClient.List(context.TODO(), metav1.ListOptions{})
		if allErr != nil && !kubeErrors.IsNotFound(allErr) {
			return nil, 0, allErr
		}
		if listcrd == nil {
			ctx.CsmLogger().Infof("crd kind %s is nil", strings.ToLower(crdKind))
			continue
		}
		v := reflect.ValueOf(listcrd)
		if v.Kind() == reflect.Ptr {
			v = v.Elem()
		} else if v.Kind() != reflect.Struct {
			ctx.CsmLogger().Error("Cannot reflect to struct type.")
			return nil, 0, errors.New("Cannot reflect to struct type.")
		}

		itemsValue := v.FieldByName(crdListFieldItems)
		if itemsValue.Len() == 0 {
			continue
		}

		// 添加 CRD
		for i := 0; i < itemsValue.Len(); i++ {
			crdItem := itemsValue.Index(i)
			obj := crdItem.Interface().(unstructured.Unstructured)
			c, err := convertToCrd(crdItem.Interface(), obj.GetNamespace(), obj.GetKind(), obj.GetName(), obj.GetCreationTimestamp().Time)
			if err != nil {
				return nil, 0, err
			}
			crds = append(crds, *c)
			count++
		}
	}
	return crds, 0, nil
}

// nolint:gocyclo
func (s *Service) createCrd(ctx csmContext.CsmContext, k8sClient kube.Client,
	content string) (crd *meta.Crd, err error) {
	if len(content) == 0 {
		return nil, errors.Errorf("No crd to create: %s", content)
	}

	m := make(map[interface{}]interface{})
	err = yaml.Unmarshal([]byte(content), &m)
	if err != nil {
		return nil, errors.New("The format of crd is invalid.")
	}

	var kind meta.Kind
	if k, ok := m["kind"]; ok {
		// TODO: validate crd
		kind = meta.Kind(k.(string))
	}

	var namespace string
	if metadata, ok := m["metadata"]; ok {
		md := metadata.(map[interface{}]interface{})
		if n, ok := md["namespace"]; ok {
			namespace = n.(string)
		}
		// istio类型的CRD，namespace默认为default。aeraki类型的可能为cluster级别的CRD，这里暂不修改
		if meta.IsRegisteredCrdKindForIstio(kind) && len(strings.TrimSpace(namespace)) == 0 {
			namespace = "default"
		}
	}

	client := k8sClient.Istio()

	// TODO: 用反射优化
	// TODO: 重复创建时返回特定错误
	switch kind {
	case meta.Sidecar:
		target := &v1alpha3.Sidecar{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.NetworkingV1alpha3().Sidecars(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.VirtualService:
		target := &v1alpha3.VirtualService{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.NetworkingV1alpha3().VirtualServices(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.DestinationRule:
		target := &v1alpha3.DestinationRule{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.NetworkingV1alpha3().DestinationRules(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.EnvoyFilter:
		target := &v1alpha3.EnvoyFilter{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.NetworkingV1alpha3().EnvoyFilters(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.Gateway:
		target := &v1alpha3.Gateway{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.NetworkingV1alpha3().Gateways(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.ServiceEntry:
		target := &v1alpha3.ServiceEntry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.NetworkingV1alpha3().ServiceEntries(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.WorkloadEntry:
		target := &v1alpha3.WorkloadEntry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.NetworkingV1alpha3().WorkloadEntries(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.WorkloadGroup:
		target := &v1alpha3.WorkloadGroup{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.NetworkingV1alpha3().WorkloadGroups(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.AuthorizationPolicy:
		target := &v1beta1.AuthorizationPolicy{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.SecurityV1beta1().AuthorizationPolicies(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.PeerAuthentication:
		target := &v1beta1.PeerAuthentication{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.SecurityV1beta1().PeerAuthentications(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.RequestAuthentication:
		target := &v1beta1.RequestAuthentication{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.SecurityV1beta1().RequestAuthentications(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.Telemetry:
		target := &v1alpha1.Telemetry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.TelemetryV1alpha1().Telemetries(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case meta.WasmPlugin:
		target := &extensionsV1alpha1.WasmPlugin{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		ic, err := client.ExtensionsV1alpha1().WasmPlugins(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	default:
		if s.isAllowedCrdKind(string(kind)) {
			dec := k8sYaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme)
			obj := &unstructured.Unstructured{}
			_, _, err = dec.Decode([]byte(content), nil, obj)
			if err != nil {
				return nil, fmt.Errorf("error decoding YAML file %s: %v", content, err)
			}
			gvk := obj.GroupVersionKind()
			gvr, _ := k8sMeta.UnsafeGuessKindToResource(gvk)
			ic, dyErr := k8sClient.Dynamic().Resource(gvr).Namespace(namespace).Create(context.TODO(), obj, metav1.CreateOptions{})
			if !assertNilOrError(ctx, ic, dyErr) {
				ctx.CsmLogger().Errorf("dynamic create dyErr is %s", dyErr.Error())
				return nil, dyErr
			}

			crd, err = convertToCrd(ic, namespace, ic.GetKind(), ic.GetName(), time.Now())
			return crd, err
		}
		return nil, errors.Errorf("Not support kind %s", kind)
	}

	if err != nil {
		return nil, err
	}

	// TODO: 更多边界处理
	return crd, nil
}

func (s *Service) isAllowedCrdKind(crdKind string) bool {
	for _, allowedKind := range s.opt.crdAllowedKind {
		if strings.EqualFold(allowedKind, crdKind) {
			return true
		}
	}
	return false
}

// nolint:gocyclo
func (s *Service) updateCrd(ctx csmContext.CsmContext, k8sClient kube.Client, namespace,
	kind, name, content string) (crd *meta.Crd, err error) {
	// TODO: 用反射优化
	// TODO: 验证
	client := k8sClient.Istio()

	switch kind {
	case string(meta.Sidecar):
		target := &v1alpha3.Sidecar{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.NetworkingV1alpha3().Sidecars(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.NetworkingV1alpha3().Sidecars(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.VirtualService):
		target := &v1alpha3.VirtualService{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.NetworkingV1alpha3().VirtualServices(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.NetworkingV1alpha3().VirtualServices(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.DestinationRule):
		target := &v1alpha3.DestinationRule{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.NetworkingV1alpha3().DestinationRules(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.NetworkingV1alpha3().DestinationRules(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.EnvoyFilter):
		target := &v1alpha3.EnvoyFilter{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.NetworkingV1alpha3().EnvoyFilters(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.NetworkingV1alpha3().EnvoyFilters(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.Gateway):
		target := &v1alpha3.Gateway{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.NetworkingV1alpha3().Gateways(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.NetworkingV1alpha3().Gateways(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.ServiceEntry):
		target := &v1alpha3.ServiceEntry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.NetworkingV1alpha3().ServiceEntries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.NetworkingV1alpha3().ServiceEntries(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.WorkloadEntry):
		target := &v1alpha3.WorkloadEntry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.NetworkingV1alpha3().WorkloadEntries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.NetworkingV1alpha3().WorkloadEntries(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.WorkloadGroup):
		target := &v1alpha3.WorkloadGroup{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.NetworkingV1alpha3().WorkloadGroups(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.NetworkingV1alpha3().WorkloadGroups(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.AuthorizationPolicy):
		target := &v1beta1.AuthorizationPolicy{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.SecurityV1beta1().AuthorizationPolicies(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.SecurityV1beta1().AuthorizationPolicies(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.PeerAuthentication):
		target := &v1beta1.PeerAuthentication{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.SecurityV1beta1().PeerAuthentications(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.SecurityV1beta1().PeerAuthentications(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.RequestAuthentication):
		target := &v1beta1.RequestAuthentication{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.SecurityV1beta1().RequestAuthentications(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.SecurityV1beta1().RequestAuthentications(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.Telemetry):
		target := &v1alpha1.Telemetry{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.TelemetryV1alpha1().Telemetries(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.TelemetryV1alpha1().Telemetries(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	case string(meta.WasmPlugin):
		target := &extensionsV1alpha1.WasmPlugin{}
		err = util.YamlStringToStruct(content, target)
		if err != nil {
			return nil, err
		}

		oc, err := client.ExtensionsV1alpha1().WasmPlugins(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if !assertNilOrError(ctx, oc, err) {
			return nil, err
		}

		target.ResourceVersion = oc.ResourceVersion

		ic, err := client.ExtensionsV1alpha1().WasmPlugins(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
		if !assertNilOrError(ctx, ic, err) {
			return nil, err
		}
		// TODO: 判断更新时间
		crd, err = convertToCrd(ic, ic.Namespace, ic.Kind, ic.Name, ic.CreationTimestamp.Time)
	default:
		if s.isAllowedCrdKind(kind) {
			dec := k8sYaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme)
			obj := &unstructured.Unstructured{}
			_, _, err = dec.Decode([]byte(content), nil, obj)
			if err != nil {
				return nil, fmt.Errorf("error decoding YAML file %s: %v", content, err)
			}
			gvk := obj.GroupVersionKind()
			gvr, _ := k8sMeta.UnsafeGuessKindToResource(gvk)
			var dynamicClient dynamic.ResourceInterface
			if namespace == "" {
				dynamicClient = k8sClient.Dynamic().Resource(gvr)
			} else {
				dynamicClient = k8sClient.Dynamic().Resource(gvr).Namespace(namespace)
			}

			ic, dyErr := dynamicClient.Update(context.TODO(), obj, metav1.UpdateOptions{})
			if !assertNilOrError(ctx, ic, dyErr) {
				return nil, dyErr
			}

			crd, err = convertToCrd(ic, namespace, ic.GetKind(), ic.GetName(), time.Now())
			return crd, err
		}
		return nil, errors.Errorf("Not support kind %s", kind)
	}

	if err != nil {
		return nil, err
	}

	// TODO: 更多边界处理
	return crd, nil
}

func (s *Service) deleteCrd(ctx csmContext.CsmContext, k8sClient kube.Client, namespace, kind, name string) (err error) {
	// TODO: 用反射优化
	client := k8sClient.Istio()
	switch kind {
	case string(meta.Sidecar):
		err = client.NetworkingV1alpha3().Sidecars(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.VirtualService):
		err = client.NetworkingV1alpha3().VirtualServices(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.DestinationRule):
		err = client.NetworkingV1alpha3().DestinationRules(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.EnvoyFilter):
		err = client.NetworkingV1alpha3().EnvoyFilters(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.Gateway):
		err = client.NetworkingV1alpha3().Gateways(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.ServiceEntry):
		err = client.NetworkingV1alpha3().ServiceEntries(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.WorkloadEntry):
		err = client.NetworkingV1alpha3().WorkloadEntries(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.WorkloadGroup):
		err = client.NetworkingV1alpha3().WorkloadGroups(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.AuthorizationPolicy):
		err = client.SecurityV1beta1().AuthorizationPolicies(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.PeerAuthentication):
		err = client.SecurityV1beta1().PeerAuthentications(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.RequestAuthentication):
		err = client.SecurityV1beta1().RequestAuthentications(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.Telemetry):
		err = client.TelemetryV1alpha1().Telemetries(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	case string(meta.WasmPlugin):
		err = client.ExtensionsV1alpha1().WasmPlugins(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	default:
		if s.isAllowedCrdKind(kind) {
			gvr := schema.GroupVersionResource{
				Group:    getDynamicGroup(kind),
				Version:  "v1alpha1",
				Resource: getDynamicResource(kind),
			}
			var dynamicClient dynamic.ResourceInterface
			if namespace == "" {
				dynamicClient = k8sClient.Dynamic().Resource(gvr)
			} else {
				dynamicClient = k8sClient.Dynamic().Resource(gvr).Namespace(namespace)
			}
			return dynamicClient.Delete(context.TODO(), name, metav1.DeleteOptions{})
		}
		return errors.Errorf("Not support kind %s", kind)
	}

	if err != nil {
		return err
	}

	// TODO: 更多边界处理
	return nil
}

func isLabelsMatch(matchLabels map[string]string, content string) bool {
	m := make(map[interface{}]interface{})
	err := yaml.Unmarshal([]byte(content), &m)
	if err != nil {
		return false
	}

	labels := make(map[interface{}]interface{})
	if metadata, ok := m["metadata"]; ok {
		md := metadata.(map[interface{}]interface{})
		if n, ok := md["labels"]; ok {
			labels = n.(map[interface{}]interface{})
		}
		for k, v := range matchLabels {
			if vv, ok := labels[k]; ok && v == vv {
				continue
			}
			return false
		}
		return true
	}
	return false
}
