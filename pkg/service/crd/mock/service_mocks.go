// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// BatchCrd mocks base method.
func (m *MockServiceInterface) BatchCrd(ctx context.CsmContext, param *meta.CrdParam) (*meta.BatchCreateCrdInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCrd", ctx, param)
	ret0, _ := ret[0].(*meta.BatchCreateCrdInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCrd indicates an expected call of BatchCrd.
func (mr *MockServiceInterfaceMockRecorder) BatchCrd(ctx, param interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCrd", reflect.TypeOf((*MockServiceInterface)(nil).BatchCrd), ctx, param)
}

// CreateCrd mocks base method.
func (m *MockServiceInterface) CreateCrd(ctx context.CsmContext, param *meta.CrdParam) (*meta.Crd, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCrd", ctx, param)
	ret0, _ := ret[0].(*meta.Crd)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCrd indicates an expected call of CreateCrd.
func (mr *MockServiceInterfaceMockRecorder) CreateCrd(ctx, param interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCrd", reflect.TypeOf((*MockServiceInterface)(nil).CreateCrd), ctx, param)
}

// DeleteCrds mocks base method.
func (m *MockServiceInterface) DeleteCrds(ctx context.CsmContext, params *meta.CrdParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCrds", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteCrds indicates an expected call of DeleteCrds.
func (mr *MockServiceInterfaceMockRecorder) DeleteCrds(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCrds", reflect.TypeOf((*MockServiceInterface)(nil).DeleteCrds), ctx, params)
}

// GetCrd mocks base method.
func (m *MockServiceInterface) GetCrd(ctx context.CsmContext, param *meta.CrdParam) (*meta.Crd, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCrd", ctx, param)
	ret0, _ := ret[0].(*meta.Crd)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCrd indicates an expected call of GetCrd.
func (mr *MockServiceInterfaceMockRecorder) GetCrd(ctx, param interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCrd", reflect.TypeOf((*MockServiceInterface)(nil).GetCrd), ctx, param)
}

// GetCrds mocks base method.
func (m *MockServiceInterface) GetCrds(ctx context.CsmContext, params *meta.CrdParams) ([]meta.Crd, *meta.PageResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCrds", ctx, params)
	ret0, _ := ret[0].([]meta.Crd)
	ret1, _ := ret[1].(*meta.PageResult)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCrds indicates an expected call of GetCrds.
func (mr *MockServiceInterfaceMockRecorder) GetCrds(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCrds", reflect.TypeOf((*MockServiceInterface)(nil).GetCrds), ctx, params)
}

// UpdateCrd mocks base method.
func (m *MockServiceInterface) UpdateCrd(ctx context.CsmContext, param *meta.CrdParam) (*meta.Crd, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCrd", ctx, param)
	ret0, _ := ret[0].(*meta.Crd)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCrd indicates an expected call of UpdateCrd.
func (mr *MockServiceInterfaceMockRecorder) UpdateCrd(ctx, param interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCrd", reflect.TypeOf((*MockServiceInterface)(nil).UpdateCrd), ctx, param)
}
