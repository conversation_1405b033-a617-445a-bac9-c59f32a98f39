package helm

import (
	"fmt"
	"path"
	"runtime"
	"strings"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/command"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"
)

// Service for helm
type Service struct {
	*meta.HelmOption
	HelmCommand func(ctx context.CsmContext) command.ExecInterface
}

func NewService(option *meta.HelmOption) ServiceInterface {
	return &Service{
		HelmOption:  option,
		HelmCommand: command.NewExecClient,
	}
}

func (s *Service) YAML(ctx context.CsmContext, helmOptions *meta.HelmOption, addonName string, args []string) ([]string, error) {
	// 命令模板 helm template {name} -n {namespace} filepath --set {自定义参数}
	chartsPath := path.Join(helmOptions.HelmChartsPath, addonName)
	subCommand := fmt.Sprintf(" template %s %s", addonName, chartsPath)
	helmCmd := s.genHelmCommad(subCommand, args)

	ctx.CsmLogger().Infof("Helm YAML helmCmd is %s", helmCmd)

	stdout, _, err := s.HelmCommand(ctx).Exec(ctx, helmCmd)
	if err != nil {
		ctx.CsmLogger().Errorf("HelmCommand failed, execCmd: %v, err: %v ", helmCmd, err)
		return nil, err
	}
	return object.ManifestK8sObject(ctx, string(stdout))
}

// genHelmCommad generates helm command
func (s *Service) genHelmCommad(subcommand string, commandArgs []string) string {
	return getHelmBin(s.HelmBinPath) + " " + subcommand + " " + strings.Join(commandArgs, " ")
}

func getHelmBin(helmBinPath string) string {
	sysType := runtime.GOOS
	if sysType == constants.Darwin {
		helmBinPath = helmBinPath + "_arm"
	}
	return helmBinPath
}
