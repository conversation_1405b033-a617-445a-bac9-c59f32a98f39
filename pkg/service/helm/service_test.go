package helm

import (
	"fmt"
	"path"
	"runtime"
	"strconv"
	"testing"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	helm_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

var (
	ctx, _ = csmContext.NewCsmContextMock()
)

func TestYAML(t *testing.T) {
	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../")
	helmOption := helm_meta.NewOption(rootPath, "", "")
	cases := []struct {
		name      string
		option    *helm_meta.HelmOption
		expectErr bool
	}{
		{
			name:   "normal",
			option: helmOption,
		},
	}
	for _, c := range cases {
		service := NewService(c.option)
		_, err := service.YAML(ctx, c.option, "registry2istio", nil)
		if (err != nil) != c.expectErr {
			if err != nil {
				fmt.<PERSON><PERSON><PERSON>("%s", err)
			}
			t.<PERSON><PERSON><PERSON>("Case %s YAML failed expect isError=%s but actual not",
				c.name, strconv.FormatBool(c.expectErr))
		}
	}
}
