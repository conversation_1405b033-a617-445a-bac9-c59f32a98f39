// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// YAML mocks base method.
func (m *MockServiceInterface) YAML(ctx context.CsmContext, helmOptions *meta.HelmOption, addonName string, args []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "YAML", ctx, helmOptions, addonName, args)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// YAML indicates an expected call of YAML.
func (mr *MockServiceInterfaceMockRecorder) YAML(ctx, helmOptions, addonName, args interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "YAML", reflect.TypeOf((*MockServiceInterface)(nil).YAML), ctx, helmOptions, addonName, args)
}
