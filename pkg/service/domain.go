package service

import (
	"context"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type DomainServiceInterface interface {
	ListUserCert(ctx context.Context, accountId, userId string) (*model.ListCertResponse, error)
	CheckDomainIcp(c context.Context, domainName string) (bool, error)
}

type DomainService struct {
	clients clientset.ClientSetInterface
}

// NewDomainService new domain service
func NewDomainService(clis clientset.ClientSetInterface) DomainServiceInterface {
	return &DomainService{
		clients: clis,
	}
}

// ListUserCert list user cert
func (d DomainService) ListUserCert(c context.Context, accountId, userId string) (*model.ListCertResponse, error) {

	logger := gin_context.LoggerFromContext(c)

	certCli, err := d.clients.CertClientForAccount(accountId, userId)

	if err != nil {
		logger.Errorf("get cert client for account %s failed: %s", accountId, err)
		return nil, err
	}

	certs, err := certCli.ListCert()
	if err != nil {
		logger.Errorf("list account %s certs failed: %s", accountId, err)
		return nil, err
	}

	certResults := make([]*model.Cert, 0)
	for _, t := range certs.Certs {
		// 只返回服务端类型&未过期的证书
		if t.CertType == 1 {
			if t.CertStopTime.After(time.Now()) {
				cert := &model.Cert{
					CertID:         t.CertID,
					CertName:       t.CertName,
					CertExpireTime: t.CertStopTime,
				}
				certResults = append(certResults, cert)
			}
		}
	}

	certResponse := &model.ListCertResponse{
		Items: certResults,
	}

	return certResponse, nil
}

// CheckDomainIcp 检查域名备案状态
func (d DomainService) CheckDomainIcp(c context.Context, domainName string) (bool, error) {

	logger := gin_context.LoggerFromContext(c)

	accountID := middleware.AccountIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	bcdClient, err := d.clients.BcdClientForAccount(accountID, userID)
	if err != nil {
		logger.Errorf("get bcd client failed: %s", err)
	}

	domainICPed, err := bcdClient.CheckDomainICP(domainName)
	if err != nil {
		logger.Errorf("check domain %s icp failed: %s", domainName, err)
		return false, err
	}
	if !domainICPed {
		logger.Errorf("the domain %s not beian.", domainName)
	}
	return domainICPed, nil

}
