package billing

import (
	"strings"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/billing"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

type Service struct {
	opt *Option

	billingModel billing.ServiceInterface
}

func NewQuoteService(option *Option) *Service {
	return &Service{
		opt:          option,
		billingModel: billing.NewService(billing.NewOption()),
	}
}

func (s *Service) GetQuote(ctx context.CsmContext, instanceType string) (quoteInfo *meta.ResponsePayload,
	err error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, err
	}
	flavorItemName1 := constants.TGWGSL
	flavorItemName2 := constants.TGWGsidecarGLF
	if strings.EqualFold(string(version.StandaloneVersionType), instanceType) {
		flavorItemName1 = constants.DLWGSL
		flavorItemName2 = constants.DLWGsidecarGLF
	}
	requestPayload := meta.RequestPayload{
		Requests: []meta.Request{
			{
				UUID:        uuid.New().String(),
				AccountID:   accountId,
				ServiceType: constants.CSM,
				Region:      ctx.Request().Header.Get(constants.RegionHeaderKey),
				Duration:    1,
				TimeUnit:    constants.TimeUnit,
				OrderType:   constants.OrderType,
				Count:       1,
				Flavor: meta.Flavor{
					FlavorItems: []meta.FlavorItem{
						{Name: flavorItemName1, Value: "1", Scale: 1},
						{Name: constants.SubServiceType, Value: constants.WGSLF, Scale: 1},
					},
				},
				ProductType:    constants.Postpay,
				ChargeItemName: constants.RunningTimeMinutes,
				QueryTime:      time.Now(),
			},
			{
				UUID:        uuid.New().String(),
				AccountID:   accountId,
				ServiceType: constants.CSM,
				Region:      ctx.Request().Header.Get(constants.RegionHeaderKey),
				Duration:    1,
				TimeUnit:    constants.TimeUnit,
				OrderType:   constants.OrderType,
				Count:       1,
				Flavor: meta.Flavor{
					FlavorItems: []meta.FlavorItem{
						{Name: flavorItemName2, Value: "101", Scale: 1},
						{Name: constants.SubServiceType, Value: constants.SidecarGLF, Scale: 1},
					},
				},
				ProductType:    constants.Postpay,
				ChargeItemName: constants.RunningTimeMinutes,
				QueryTime:      time.Now(),
			},
		},
	}
	// billing 接口查询产品价格
	quoteInfo, err = s.billingModel.NewClient(ctx, requestPayload)
	if err != nil {
		ctx.CsmLogger().Errorf("Error NewClient for billing: %v", err)
		return
	}

	if strings.EqualFold(string(version.StandaloneVersionType), instanceType) {
		// 查询 blb 价格
		requestPayload = meta.RequestPayload{
			Requests: []meta.Request{
				{
					UUID:        uuid.New().String(),
					AccountID:   accountId,
					ServiceType: constants.BLB,
					Region:      ctx.Request().Header.Get(constants.RegionHeaderKey),
					Duration:    1,
					TimeUnit:    constants.TimeUnit,
					OrderType:   constants.OrderType,
					Count:       1,
					Flavor: meta.Flavor{
						FlavorItems: []meta.FlavorItem{
							{Name: "payload", Value: "common", Scale: 1},
							{Name: constants.SubServiceType, Value: constants.Default, Scale: 1},
						},
					},
					ProductType:    constants.Postpay,
					ChargeItemName: constants.RunningTimeMinutes,
					QueryTime:      time.Now(),
				},
			},
		}
		blbQuoteInfo := &meta.ResponsePayload{}
		blbQuoteInfo, err = s.billingModel.NewClient(ctx, requestPayload)
		if err != nil {
			ctx.CsmLogger().Errorf("Error NewClient for billing: %v", err)
			return
		}
		quoteInfo.Response = append(quoteInfo.Response, blbQuoteInfo.Response[0])
	} else {
		// 查询 eip 价格
		requestPayload = meta.RequestPayload{
			Requests: []meta.Request{
				{
					UUID:        uuid.New().String(),
					AccountID:   accountId,
					ServiceType: constants.EIP,
					Region:      ctx.Request().Header.Get(constants.RegionHeaderKey),
					Duration:    1,
					TimeUnit:    constants.TimeUnit,
					OrderType:   constants.OrderType,
					Count:       1,
					Flavor: meta.Flavor{
						FlavorItems: []meta.FlavorItem{
							{Name: "bandwidth", Value: "1M", Scale: 1},
							{Name: constants.SubServiceType, Value: constants.Default, Scale: 1},
						},
					},
					ProductType:    constants.Postpay,
					ChargeItemName: constants.RunningTimeMinutes,
					QueryTime:      time.Now(),
				},
			},
		}
		eipQuoteInfo := &meta.ResponsePayload{}
		eipQuoteInfo, err = s.billingModel.NewClient(ctx, requestPayload)
		if err != nil {
			ctx.CsmLogger().Errorf("Error NewClient for billing: %v", err)
			return
		}
		quoteInfo.Response = append(quoteInfo.Response, eipQuoteInfo.Response[0])
	}
	return quoteInfo, nil
}
