package billing

import (
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	billingMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/billing/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

var (
	User         = "User"
	iamAccountId = "123"
	iamUser      = &sdkIAM.User{
		ID:   "1",
		Name: "test-user",
		Domain: sdkIAM.UserDomain{
			ID:   iamAccountId,
			Name: "aaaa",
		},
	}
)

func TestService_GetQuote(t *testing.T) {
	tests := []struct {
		name          string
		instanceType  string
		csmResponse   *meta.ResponsePayload
		eipResponse   *meta.ResponsePayload
		wantQuoteInfo *meta.ResponsePayload
		wantErr       bool
	}{
		{
			name:         "test_getQuote",
			instanceType: "hosting",
			csmResponse: &meta.ResponsePayload{
				Response: []meta.QuoteInfo{
					{
						Uuid:               "c484d6ba-4de8-4224-b7e7-9efc50221e5c",
						Price:              1.5,
						PriceId:            -********,
						CatalogPrice:       1.5,
						PriceName:          "CATALOG",
						PriceType:          "CATALOG",
						ErrorCode:          0,
						DiscountRate:       100,
						SkuId:              "S0056000087CSM000011",
						PostPayPriceDetail: meta.PostPayPriceDetail{},
					},
				},
			},
			eipResponse: &meta.ResponsePayload{
				Response: []meta.QuoteInfo{
					{
						Uuid:               "c484d6ba-4de8-4224-b7e7-9efc50221e51",
						Price:              0.29,
						PriceId:            -17698312,
						CatalogPrice:       0.29,
						PriceName:          "CATALOG",
						PriceType:          "CATALOG",
						ErrorCode:          0,
						DiscountRate:       100,
						SkuId:              "S0056000087CSM000009",
						PostPayPriceDetail: meta.PostPayPriceDetail{},
					},
				},
			},
			wantQuoteInfo: &meta.ResponsePayload{
				Response: []meta.QuoteInfo{
					{
						Uuid:               "c484d6ba-4de8-4224-b7e7-9efc50221e5c",
						Price:              1.5,
						PriceId:            -********,
						CatalogPrice:       1.5,
						PriceName:          "CATALOG",
						PriceType:          "CATALOG",
						ErrorCode:          0,
						DiscountRate:       100,
						SkuId:              "S0056000087CSM000011",
						PostPayPriceDetail: meta.PostPayPriceDetail{},
					},
					{
						Uuid:               "c484d6ba-4de8-4224-b7e7-9efc50221e51",
						Price:              0.29,
						PriceId:            -17698312,
						CatalogPrice:       0.29,
						PriceName:          "CATALOG",
						PriceType:          "CATALOG",
						ErrorCode:          0,
						DiscountRate:       100,
						SkuId:              "S0056000087CSM000009",
						PostPayPriceDetail: meta.PostPayPriceDetail{},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, _ := csmContext.NewCsmContextMock()
			ctx.Set(reg.ContextRegion, "bj")
			ctx.Set(User, iamUser)
			ctrl := gomock.NewController(t)
			billingModel := billingMock.NewMockServiceInterface(ctrl)

			billingModel.EXPECT().NewClient(ctx, gomock.Any()).Return(tt.csmResponse, nil)
			billingModel.EXPECT().NewClient(ctx, gomock.Any()).Return(tt.eipResponse, nil)
			s := &Service{
				billingModel: billingModel,
			}
			gotQuoteInfo, err := s.GetQuote(ctx, tt.instanceType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetQuote() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotQuoteInfo, tt.wantQuoteInfo) {
				t.Errorf("GetQuote() gotQuoteInfo = %v, want %v", gotQuoteInfo, tt.wantQuoteInfo)
			}
		})
	}
}
