package service

import (
	"context"
	"testing"

	"github.com/goharbor/harbor/src/testing/mock"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	testingbilling "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/billing"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

func TestOrderService_autoRenew(t *testing.T) {
	type fields struct {
		clients clientset.ClientSetInterface
		region  string
	}
	type args struct {
		ctx           context.Context
		accountId     string
		userId        string
		region        string
		renewTimeUnit string
		renewTime     int
		serviceIds    []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				clients: &testingclientset.ClientSet{},
				region:  "gz",
			},
			args: args{
				ctx:        context.Background(),
				accountId:  "xxxx",
				serviceIds: []string{"aaa", "bbb"},
			},
			wantErr: false,
		},
		{
			name: "case2",
			fields: fields{
				clients: &testingclientset.ClientSet{},
				region:  "gz",
			},
			args: args{
				ctx:        context.Background(),
				accountId:  "xxxx",
				serviceIds: []string{"aaa", "bbb"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {

		if tt.name == "case1" {
			autoRenewClient := &testingbilling.AutoRenewClient{}
			mock.OnAnything(tt.fields.clients, "AutoRuleClientForAccount").Return(autoRenewClient, nil)
			mock.OnAnything(autoRenewClient, "ListAutoRenewRule").Return([]*billing.ListAutoRenewRuleResponse{{Uuid: "xxxx"}}, nil)
			mock.OnAnything(autoRenewClient, "CreateAutoRenewRule").Return(nil)
		}

		if tt.name == "case2" {
			autoRenewClient := &testingbilling.AutoRenewClient{}
			mock.OnAnything(tt.fields.clients, "AutoRuleClientForAccount").Return(autoRenewClient, nil)
			mock.OnAnything(autoRenewClient, "ListAutoRenewRule").Return([]*billing.ListAutoRenewRuleResponse{}, nil)
			mock.OnAnything(autoRenewClient, "CreateAutoRenewRule").Return(nil)
		}
		t.Run(tt.name, func(t *testing.T) {
			o := &OrderService{
				clients: tt.fields.clients,
				region:  tt.fields.region,
			}
			if err := o.autoRenew(tt.args.ctx, tt.args.accountId, tt.args.userId, tt.args.region, tt.args.renewTimeUnit, tt.args.renewTime, tt.args.serviceIds); (err != nil) != tt.wantErr {
				t.Errorf("autoRenew() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
