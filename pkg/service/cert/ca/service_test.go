package ca

import (
	"context"
	"os"
	"testing"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ctx "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
)

var (
	Region          = "bj"
	ClusterId       = "test-xxx"
	SystemNamespace = "istio-system"
	mockCtx, _      = ctx.NewCsmContextMock()
)

func buildNameSpace() *v1.Namespace {
	return &v1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: SystemNamespace,
		},
	}
}

func TestGenerateIstioCaData(t *testing.T) {
	fakeClient := kube.NewFakeClient()
	certParams := CertParams{
		Region:          Region,
		Version:         testdata.Version1146,
		ClusterId:       ClusterId,
		SystemNamespace: SystemNamespace,
	}
	service := NewCertService(fakeClient, certParams)

	tempDir, err := os.MkdirTemp("/tmp", "test")
	if err != nil {
		t.Errorf("os.MkdirTemp error %v", err)
	}
	defer os.RemoveAll(tempDir)

	_, rootCAConfErr := NewRootConfig(tempDir)
	if rootCAConfErr != nil {
		t.Errorf("NewRootConfig error %v", rootCAConfErr)
	}
	data, err := service.GenerateIstioCaData(mockCtx, tempDir)
	fakeClient.Kube().CoreV1().Namespaces().Create(context.TODO(), buildNameSpace(), metav1.CreateOptions{})
	err = service.CreateCaCerts(mockCtx, data, SystemNamespace)
	if err != nil {
		t.Errorf("CreateCaCerts error %v", err)
	}
}
