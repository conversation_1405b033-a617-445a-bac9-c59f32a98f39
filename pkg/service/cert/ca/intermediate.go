package ca

import (
	"fmt"
	"os"
	"path/filepath"

	ctxContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/tmpl"
)

const (
	istioConfTemplate = `
[ req ]
encrypt_key = no
prompt = no
utf8 = yes
default_md = sha256
default_bits = 4096
req_extensions = req_ext
x509_extensions = req_ext
distinguished_name = req_dn
[ req_ext ]
subjectKeyIdentifier = hash
basicConstraints = critical, CA:true, pathlen:0
keyUsage = critical, digitalSignature, nonRepudiation, keyEncipherment, keyCertSign
subjectAltName=@san
[ san ]
DNS.1 = istiod.{{ .SystemNamespace }}
DNS.2 = istiod.{{ .SystemNamespace }}.svc
DNS.3 = istio-pilot.{{ .SystemNamespace }}
DNS.4 = istio-pilot.{{ .SystemNamespace }}.svc
[ req_dn ]
O = Istio
CN = Intermediate CA
`
)

// NewIstioConfig creates an extensions configuration for Istio, using the given system namespace in
// the DNS SANs.
func NewIstioConfig(ctx ctxContext.CsmContext, systemNamespace string) (string, error) {
	return tmpl.Evaluate(ctx, istioConfTemplate, map[string]string{
		"SystemNamespace": systemNamespace,
	})
}

// Intermediate is an intermediate CA for a single cluster.
type Intermediate struct {
	KeyFile           string
	ConfFile          string
	CSRFile           string
	CertFile          string
	RootFile          string
	CertChainPermFile string
	Root              Root
}

// NewIntermediate creates a new intermediate CA for the given cluster.
func NewIntermediate(workDir, config string, root Root) (Intermediate, error) {
	if _, err := os.Stat(workDir); os.IsNotExist(err) {
		dErr := os.MkdirAll(workDir, os.ModePerm)
		if dErr != nil {
			return Intermediate{}, fmt.Errorf("error creating interminate cert folder %s: %v", workDir, dErr)
		}
	}

	ca := Intermediate{
		KeyFile:           filepath.Join(workDir, "ca-key.pem"),
		ConfFile:          filepath.Join(workDir, "ca.conf"),
		CSRFile:           filepath.Join(workDir, "ca.csr"),
		CertFile:          filepath.Join(workDir, "ca-cert.pem"),
		RootFile:          filepath.Join(workDir, "root-cert.pem"),
		CertChainPermFile: filepath.Join(workDir, "cert-chain.pem"),
		Root:              root,
	}

	// Write out the CA config file.
	if err := os.WriteFile(ca.ConfFile, []byte(config), os.ModePerm); err != nil {
		return Intermediate{}, err
	}

	// Create the key for the intermediate CA.
	if err := GenerateKey(ca.KeyFile); err != nil {
		return Intermediate{}, err
	}

	// Create the CSR for the intermediate CA.
	if err := GenerateCSR(ca.ConfFile, ca.KeyFile, ca.CSRFile); err != nil {
		return Intermediate{}, err
	}

	// Create the intermediate cert, signed by the root.
	if err := GenerateIntermediateCert(ca.ConfFile, ca.CSRFile, root.CertFile,
		root.KeyFile, ca.CertFile); err != nil {
		return Intermediate{}, err
	}

	caCert, err := file.AsString(ca.CertFile)
	if err != nil {
		return Intermediate{}, err
	}
	rootCert, err := file.AsString(ca.Root.CertFile)
	if err != nil {
		return Intermediate{}, err
	}
	// Create the cert chain by concatenating the intermediate and root certs.
	certChain := caCert + rootCert
	if err := os.WriteFile(ca.CertChainPermFile, []byte(certChain), os.ModePerm); err != nil {
		return Intermediate{}, err
	}

	if err := os.WriteFile(ca.RootFile, []byte(rootCert), os.ModePerm); err != nil {
		return Intermediate{}, err
	}

	return ca, nil
}

// NewIstioCaData generates istio ca
func (ca Intermediate) NewIstioCaData() (map[string][]byte, error) {
	caCert, err := file.AsString(ca.CertFile)
	if err != nil {
		return nil, err
	}

	caKey, err := file.AsString(ca.KeyFile)
	if err != nil {
		return nil, err
	}

	rootCert, err := file.AsString(ca.Root.CertFile)
	if err != nil {
		return nil, err
	}

	certChain, err := file.AsString(ca.CertChainPermFile)
	if err != nil {
		return nil, err
	}
	// Create the cert chain by concatenating the intermediate and root certs.
	// certChain := caCert + rootCert

	data := map[string][]byte{
		"ca-cert.pem":    []byte(caCert),
		"ca-key.pem":     []byte(caKey),
		"cert-chain.pem": []byte(certChain),
		"root-cert.pem":  []byte(rootCert),
	}
	return data, nil
}
