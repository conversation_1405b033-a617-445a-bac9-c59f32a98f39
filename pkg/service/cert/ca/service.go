package ca

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"

	kubeApiCore "k8s.io/api/core/v1"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ctx "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

var rootCAConf = `
[ req ]
encrypt_key = no
prompt = no
utf8 = yes
default_md = sha256
default_bits = 4096
req_extensions = req_ext
x509_extensions = req_ext
distinguished_name = req_dn
[ req_ext ]
subjectKeyIdentifier = hash
basicConstraints = critical, CA:true
keyUsage = critical, digitalSignature, nonRepudiation, keyEncipherment, keyCertSign
[ req_dn ]
O = Istio
CN = Root CA`

type CertService struct {
	client     kube.Client
	certParams CertParams
}

type CertParams struct {
	Region          string
	Version         string
	ClusterId       string
	SystemNamespace string
}

func NewCertService(k8sClient kube.Client, certParams CertParams) ServiceInterface {
	return &CertService{
		client:     k8sClient,
		certParams: certParams,
	}
}

func (c *CertService) GenerateIstioCaData(ctx ctx.CsmContext, rootDir string) (map[string][]byte, error) {
	if c.certParams.Region == "" {
		return nil, errors.New("istio region is nil")
	}
	if c.certParams.Version == "" {
		return nil, errors.New("istio version is nil")
	}
	if c.certParams.ClusterId == "" {
		return nil, errors.New("cluster name is nil")
	}
	if c.certParams.SystemNamespace == "" {
		return nil, errors.New("cert SystemNamespace is nil")
	}
	ctx.CsmLogger().Infof("the rootDir is %s", rootDir)
	caLoader, err := NewRoot(rootDir)
	if err != nil {
		return nil, fmt.Errorf("error reading CA cert file %s: %v", rootDir, err)
	}
	// Create the new config for the CA
	caConfig, err := NewIstioConfig(ctx, c.certParams.SystemNamespace)
	if err != nil {
		return nil, err
	}
	clusterDir := filepath.Join(rootDir, c.certParams.Region+"-"+c.certParams.Version+"-"+c.certParams.ClusterId+constants.SuffixCaCertName)
	ctx.CsmLogger().Infof("the clusterDir is %s", clusterDir)
	intermediateCA, err := NewIntermediate(clusterDir, caConfig, caLoader)
	if err != nil {
		return nil, err
	}
	return intermediateCA.NewIstioCaData()
}

func (c *CertService) CreateCaCerts(ctx ctx.CsmContext, data map[string][]byte, namespace string) error {
	ctx.CsmLogger().Infof("CreateCaCerts with data=%q, namespace=%s", data, namespace)
	caSecret := c.IstioCASecret(data)
	err := kube.WaitNamespaceReady(ctx, c.client, namespace)
	if err != nil {
		return err
	}
	_, err = c.client.Kube().CoreV1().Secrets(namespace).Create(context.TODO(), caSecret, metav1.CreateOptions{})
	if kubeErrors.IsAlreadyExists(err) {
		ctx.CsmLogger().Errorf("%s has existed, so update it", constants.CaName)
		_, err = c.client.Kube().CoreV1().Secrets(namespace).Update(context.TODO(), caSecret, metav1.UpdateOptions{})
	}
	if err != nil {
		ctx.CsmLogger().Errorf("create secret generic %v error %v", constants.CaName, err)
		return err
	}
	ctx.CsmLogger().Infof("create secret generic %v successful", constants.CaName)
	return nil
}

// IstioCASecret creates a secret (named "cacerts") containing the intermediate certificate and cert chain.
func (c *CertService) IstioCASecret(data map[string][]byte) *kubeApiCore.Secret {
	return &kubeApiCore.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name: constants.CaName,
		},
		Data: data,
	}
}

// Root stands for root CA.
type Root struct {
	// KeyFile is the path to the file containing the private key for the CA.
	KeyFile string

	// ConfFile is the path to the file containing the extensions configuration file.
	ConfFile string

	// CSRFile used to generate the cert.
	CSRFile string

	// CertFile the cert for the root CA.
	CertFile string
}

// NewRoot generates the files for a new self-signed Root CA files under the given directory.
func NewRoot(workDir string) (Root, error) {
	root := Root{
		KeyFile:  filepath.Join(workDir, "root-key.pem"),
		ConfFile: filepath.Join(workDir, "root-ca.conf"),
		CSRFile:  filepath.Join(workDir, "root-cert.csr"),
		CertFile: filepath.Join(workDir, "root-cert.pem"),
	}
	return root, nil
}

// NewRootConfig generates root ca for test
func NewRootConfig(workDir string) (Root, error) {
	root := Root{
		KeyFile:  filepath.Join(workDir, "root-key.pem"),
		ConfFile: filepath.Join(workDir, "root-ca.conf"),
		CSRFile:  filepath.Join(workDir, "root-ca.csr"),
		CertFile: filepath.Join(workDir, "root-cert.pem"),
	}
	// Write out the conf file.
	if err := os.WriteFile(root.ConfFile, []byte(rootCAConf), os.ModePerm); err != nil {
		return Root{}, err
	}

	// Create the root key.
	if err := GenerateKey(root.KeyFile); err != nil {
		return Root{}, err
	}

	// Create the root CSR
	if err := GenerateCSR(root.ConfFile, root.KeyFile, root.CSRFile); err != nil {
		return Root{}, err
	}

	// Create the root cert
	if err := GenerateCert(root.ConfFile, root.CSRFile, root.KeyFile, root.CertFile); err != nil {
		return Root{}, err
	}
	return root, nil
}
