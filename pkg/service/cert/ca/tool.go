package ca

import (
	"fmt"
	"os/exec"
)

// GenerateKey and writes output to keyFile.
func GenerateKey(keyFile string) error {
	return openssl("genrsa", "-out", keyFile, "4096")
}

// GenerateCSR and writes output to csrFile.
func GenerateCSR(confFile, keyFile, csrFile string) error {
	return openssl("req", "-new",
		"-config", confFile,
		"-key", keyFile,
		"-out", csrFile)
}

// GenerateIntermediateCert from the rootCA and writes to certFile.
func GenerateIntermediateCert(confFile, csrFile, rootCertFile, rootKeyFile, certFile string) error {
	return openssl("x509", "-req",
		"-days", "7300",
		"-CA", rootCertFile,
		"-CAkey", rootKeyFile,
		"-CAcreateserial",
		"-extensions", "req_ext",
		"-extfile", confFile,
		"-in", csrFile,
		"-out", certFile)
}

// GenerateCert and writes output to certFile.
func GenerateCert(confFile, csrFile, keyFile, certFile string) error {
	return openssl("x509", "-req",
		"-days", "36500",
		"-signkey", keyFile,
		"-extensions", "req_ext",
		"-extfile", confFile,
		"-in", csrFile,
		"-out", certFile)
}

func openssl(args ...string) error {
	cmd := exec.Command("openssl", args...)
	if out, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("command %s failed: %q %v", cmd.String(), string(out), err)
	}
	return nil
}
