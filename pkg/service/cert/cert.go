package cert

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path"
	"sync"

	v1 "k8s.io/api/core/v1"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/command"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

type ManagerCert interface {
	GenerateCert(ctx csmContext.CsmContext) error
	GenerateCa(ctx csmContext.CsmContext) (map[string][]byte, error)
	CreateCaCerts(ctx csmContext.CsmContext, namespace string, data map[string][]byte) error
	GetCertData(ctx csmContext.CsmContext, destinationCert string) (map[string][]byte, error)
}

type Cert struct {
	*sync.RWMutex
	client kube.Client
	Params
}

type Params struct {
	Region    string
	Version   string
	ClusterId string
}

func NewManagerCert(region, version, clusterId string, k8sClient kube.Client) ManagerCert {
	return &Cert{
		Params: Params{
			Region:    region,
			Version:   version,
			ClusterId: clusterId,
		},
		client:  k8sClient,
		RWMutex: new(sync.RWMutex),
	}
}

// GenerateCert 获取根证书并且生成中间证书
func (cert *Cert) GenerateCert(ctx csmContext.CsmContext) error {
	// todo use cert api or use openssl to generate cert for istio
	if cert.ClusterId == "" {
		return errors.New("cluster name is nil")
	}
	if cert.Version == "" {
		return errors.New("istio version is nil")
	}
	// todo 本地涉及变更 pwd 路径，加个锁，后续考虑对接第三方 api 或者使用 openssl
	cert.Lock()
	defer cert.Unlock()
	pwd, err := os.Getwd()
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("current pwd is %s", pwd)
	// generate or get root ca
	configPath := path.Join(pwd, constants.Templates)
	certPath := path.Join(configPath, constants.CertBasePath)
	err = os.Mkdir(certPath, constants.FilePerm)
	if os.IsNotExist(err) {
		return err
	}
	err = os.Chdir(certPath)
	if err != nil {
		ctx.CsmLogger().Infof("chdir directory err %v", pwd)
		return err
	}
	// TODO: 后续需要拼接 accountId
	destinationCert := path.Join(certPath, cert.Region+"-"+cert.ClusterId)
	err = file.RemoveFile(destinationCert)
	if err != nil {
		return err
	}
	// generate intermediate certificates
	ctx.CsmLogger().Infof("start generate intermediate certificates")
	certGenerateCmd := path.Join(configPath, constants.BaseIstioTemplate, constants.StandaloneToolCertsCertMakePath)
	clusterPrimary := cert.Region + "-" + cert.ClusterId + constants.SuffixCaCertName
	cmd := fmt.Sprintf("make -f %s %s", certGenerateCmd, clusterPrimary)
	_, _, err = command.ExecCmdOut(ctx, cmd)
	if err != nil {
		return err
	}
	err = os.Chdir(pwd)
	if err != nil {
		ctx.CsmLogger().Infof("chdir directory err %v", pwd)
		return err
	}
	ctx.CsmLogger().Infof("generate intermediate certificates successful")
	return nil
}

// GenerateCa 生成 ca 证书
func (cert *Cert) GenerateCa(ctx csmContext.CsmContext) (map[string][]byte, error) {
	pwd, err := os.Getwd()
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("the current pwd is %s", pwd)
	configPath := path.Join(pwd, constants.Templates)
	// generate root ca or get root ca
	certPath := path.Join(configPath, constants.CertBasePath)
	ctx.CsmLogger().Infof("the current cluster %s in %s", cert.ClusterId, cert.Region)
	destinationCert := path.Join(certPath, cert.Region+"-"+cert.ClusterId)
	data, err := cert.GetCertData(ctx, destinationCert)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (cert *Cert) CreateCaCerts(ctx csmContext.CsmContext, namespace string, data map[string][]byte) error {
	err := kube.WaitNamespaceReady(ctx, cert.client, namespace)
	if err != nil {
		return err
	}
	// todo 对于任何 k8s 资源创建，最好使用重试机制
	caSecret := cert.getCaSecret(constants.CaName, namespace, data)
	// create cacerts cert
	_, err = cert.client.Kube().CoreV1().Secrets(namespace).Create(context.TODO(), caSecret, metav1.CreateOptions{})
	if kubeErrors.IsAlreadyExists(err) {
		ctx.CsmLogger().Infof("%s has existed, so update it", constants.CaName)
		_, err = cert.client.Kube().CoreV1().Secrets(namespace).Update(context.TODO(), caSecret, metav1.UpdateOptions{})
	}
	if err != nil {
		ctx.CsmLogger().Infof("create secret generic %v error %v", constants.CaName, err)
		return err
	}
	ctx.CsmLogger().Infof("create secret generic %v successful", constants.CaName)
	return nil
}

// GetCertData 生成 ca 证书内容
func (cert *Cert) GetCertData(ctx csmContext.CsmContext, destinationCert string) (map[string][]byte, error) {
	data := make(map[string][]byte)

	caCertPemPath := path.Join(destinationCert, constants.CaCertPemName)
	caKeyPemPath := path.Join(destinationCert, constants.CaKeyPemName)
	certChainPemPath := path.Join(destinationCert, constants.CertChainPemName)
	rootCertPemPath := path.Join(destinationCert, constants.RootCertPemName)

	caCertPem, err := cert.getCert(caCertPemPath)
	if err != nil {
		return data, err
	}
	caKeyPem, err := cert.getCert(caKeyPemPath)
	if err != nil {
		return data, err
	}
	certChainPem, err := cert.getCert(certChainPemPath)
	if err != nil {
		return data, err
	}
	rootCertPem, err := cert.getCert(rootCertPemPath)
	if err != nil {
		return data, err
	}

	data[constants.CaCertPemName] = caCertPem
	data[constants.CaKeyPemName] = caKeyPem
	data[constants.CertChainPemName] = certChainPem
	data[constants.RootCertPemName] = rootCertPem
	return data, nil
}

// getCert 读取证书文件内容
func (cert *Cert) getCert(path string) ([]byte, error) {
	return os.ReadFile(path)
}

// getCaSecret 组装 ca secret
func (cert *Cert) getCaSecret(name, namespace string, data map[string][]byte) *v1.Secret {
	secret := &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:         name,
			GenerateName: "generic",
			Namespace:    namespace,
		},
		Type: v1.SecretTypeOpaque,
		Data: data,
	}
	return secret
}
