package cert

import (
	"testing"

	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
)

var (
	region     = "bj"
	clusterId  = "test-xxx"
	mockCtx, _ = ctxCsm.NewCsmContextMock()
)

func TestGenerateCert(t *testing.T) {
	fakeClient := kube.NewFakeClient()
	managerCert := NewManagerCert(region, testdata.Version1146, clusterId, fakeClient)
	_ = managerCert.GenerateCert(mockCtx)
}
