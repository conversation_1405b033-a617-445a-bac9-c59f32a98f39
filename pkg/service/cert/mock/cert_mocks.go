// Code generated by MockGen. DO NOT EDIT.
// Source: cert.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockManagerCert is a mock of ManagerCert interface.
type MockManagerCert struct {
	ctrl     *gomock.Controller
	recorder *MockManagerCertMockRecorder
}

// MockManagerCertMockRecorder is the mock recorder for MockManagerCert.
type MockManagerCertMockRecorder struct {
	mock *MockManagerCert
}

// NewMockManagerCert creates a new mock instance.
func NewMockManagerCert(ctrl *gomock.Controller) *MockManagerCert {
	mock := &MockManagerCert{ctrl: ctrl}
	mock.recorder = &MockManagerCertMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockManagerCert) EXPECT() *MockManagerCertMockRecorder {
	return m.recorder
}

// CreateCaCerts mocks base method.
func (m *MockManagerCert) CreateCaCerts(ctx context.CsmContext, namespace string, data map[string][]byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCaCerts", ctx, namespace, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateCaCerts indicates an expected call of CreateCaCerts.
func (mr *MockManagerCertMockRecorder) CreateCaCerts(ctx, namespace, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCaCerts", reflect.TypeOf((*MockManagerCert)(nil).CreateCaCerts), ctx, namespace, data)
}

// GenerateCa mocks base method.
func (m *MockManagerCert) GenerateCa(ctx context.CsmContext) (map[string][]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateCa", ctx)
	ret0, _ := ret[0].(map[string][]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateCa indicates an expected call of GenerateCa.
func (mr *MockManagerCertMockRecorder) GenerateCa(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateCa", reflect.TypeOf((*MockManagerCert)(nil).GenerateCa), ctx)
}

// GenerateCert mocks base method.
func (m *MockManagerCert) GenerateCert(ctx context.CsmContext) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateCert", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// GenerateCert indicates an expected call of GenerateCert.
func (mr *MockManagerCertMockRecorder) GenerateCert(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateCert", reflect.TypeOf((*MockManagerCert)(nil).GenerateCert), ctx)
}

// GetCertData mocks base method.
func (m *MockManagerCert) GetCertData(ctx context.CsmContext, destinationCert string) (map[string][]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCertData", ctx, destinationCert)
	ret0, _ := ret[0].(map[string][]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCertData indicates an expected call of GetCertData.
func (mr *MockManagerCertMockRecorder) GetCertData(ctx, destinationCert interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCertData", reflect.TypeOf((*MockManagerCert)(nil).GetCertData), ctx, destinationCert)
}
