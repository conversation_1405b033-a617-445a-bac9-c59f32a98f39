package sugar

import (
	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

const (
	cloudHostingRegion = "cloud.hostingRegion"
)

type Option struct {
	DB            *dbutil.DB
	EksAccountIds []string
	HostingRegion interface{}
	EksProfile    string
}

func NewOption(d *gorm.DB) *Option {
	hostingRegion := viper.Get(cloudHostingRegion)
	return &Option{
		DB:            dbutil.NewDB(d),
		EksAccountIds: viper.GetStringSlice(meta.EksAccountIds),
		EksProfile:    viper.GetString(meta.EksProfile),
		HostingRegion: hostingRegion,
	}
}
