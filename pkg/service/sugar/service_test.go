package sugar

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"

	mockCluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	mockInstanceModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockInstance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

var (
	mockDB, _  = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx, _ = ctxCsm.NewCsmContextMock()

	testInstanceIDs           = []string{"test1", "test2"}
	testInstanceNames         = []string{"inst-0", "inst-1"}
	testClusterUUID           = []string{"test-cluster1", "test-cluster2"}
	testAccountID             = []string{"12345", "2345678"}
	testInstanceTypes         = []string{"hosting", "standalone"}
	testRegion                = "bj"
	testIstioInstallNamespace = "istio-test"
)

func buildMockClusters() []meta.Cluster {
	mockCluster := []meta.Cluster{
		{
			InstanceUUID:          testInstanceIDs[0],
			ClusterUUID:           testClusterUUID[0],
			Region:                testRegion,
			IstioInstallNamespace: testIstioInstallNamespace,
		},
		{
			InstanceUUID:          testInstanceIDs[1],
			ClusterUUID:           testClusterUUID[1],
			Region:                testRegion,
			IstioInstallNamespace: testIstioInstallNamespace,
		},
	}
	return mockCluster
}

func buildMockInstance() meta.Instances {
	instance := meta.Instances{
		InstanceUUID: "123456",
		InstanceName: "instance1",
		InstanceType: "hosting",
	}
	return instance
}

func buildMockCluster() meta.Cluster {
	cluster := meta.Cluster{
		InstanceUUID:          "123456",
		ClusterUUID:           "456788",
		ClusterName:           "cluster1",
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}
	return cluster
}

func TestService_GetScale(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockInstanceModel.NewMockServiceInterface(ctrl)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
	mockInstanceService := mockInstance.NewMockServiceInterface(ctrl)
	mockCluster := buildMockClusters()

	var mockInstancesList []meta.Instances
	var row []meta.Rows
	for i := 0; i < len(testInstanceIDs); i++ {
		mockInstancesList = append(mockInstancesList, meta.Instances{
			InstanceUUID: testInstanceIDs[i],
			InstanceName: testInstanceNames[i],
			AccountId:    testAccountID[i],
			InstanceType: testInstanceTypes[i],
			Region:       testRegion,
		})
	}
	columns := []meta.Columns{
		{
			Name: constants.ID,
			Id:   constants.AccountID,
		},
		{
			Name: constants.UserName,
			Id:   constants.Name,
		},
		{
			Name: constants.Standalone,
			Id:   constants.StandaloneInstance,
		},
		{
			Name: constants.Hosting,
			Id:   constants.HostingInstance,
		},
		{
			Name: constants.Sum,
			Id:   constants.InstanceSum,
		},
		{
			Name: constants.Standalone,
			Id:   constants.StandaloneSidecar,
		},
		{
			Name: constants.Hosting,
			Id:   constants.HostingSidecar,
		},
		{
			Name: constants.Sum,
			Id:   constants.SidecarSum,
		},
	}
	item := []meta.Item{
		{
			Name:    constants.User,
			Colspan: 2,
		},
		{
			Name:    constants.InstanceScale,
			Colspan: 3,
		},
		{
			Name:    constants.SidecarScale,
			Colspan: 3,
		},
	}
	var superHeaders []interface{}
	superHeaders = append(superHeaders, item)
	row = []meta.Rows{
		{
			AccountId:          testAccountID[1],
			Name:               "",
			StandaloneInstance: 1,
			HostingInstance:    0,
			InstanceSum:        1,
			StandaloneSidecar:  3,
			HostingSidecar:     0,
			SidecarSum:         3,
		},
		{
			AccountId:          testAccountID[0],
			Name:               "",
			StandaloneInstance: 0,
			HostingInstance:    1,
			InstanceSum:        1,
			StandaloneSidecar:  0,
			HostingSidecar:     2,
			SidecarSum:         2,
		},
	}
	tests := []struct {
		name    string
		want    *meta.Response
		cluster []meta.Cluster
		wantErr bool
	}{
		{
			name:    "test-getScale",
			cluster: mockCluster,
			want: &meta.Response{
				Data: map[string]interface{}{
					constants.Columns:      columns,
					constants.SuperHeaders: superHeaders,
					constants.Rows:         row,
				},
				Status: 0,
				Msg:    "",
			},

			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt:              NewOption(mockDB),
				instancesModel:   mockInstanceModel,
				clusterModel:     mockClusterModel,
				instancesService: mockInstanceService,
			}
			userNames := make(map[string]string)
			userNames["123456"] = "xxxxxx"
			mockInstanceModel.EXPECT().GetAccountID(mockCtx).Return(testAccountID, nil)
			mockInstanceModel.EXPECT().GetAllInstances(mockCtx, gomock.Any()).Return(mockInstancesList, nil).AnyTimes()
			mockClusterModel.EXPECT().GetAllIstiodCluster(gomock.Any(), gomock.Any()).Return(&tt.cluster, nil).AnyTimes()
			mockInstanceService.EXPECT().GetActiveSidecarNum(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(2, nil).AnyTimes()
			mockInstanceService.EXPECT().GetSugarSidecarNum(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(3, nil).AnyTimes()
			got, err := service.GetScale(mockCtx, "bj", userNames)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetScale() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetScale() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetUserList(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockInstanceModel.NewMockServiceInterface(ctrl)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
	mockInstanceService := mockInstance.NewMockServiceInterface(ctrl)

	mockInstance := buildMockInstance()
	mockCluster := buildMockCluster()

	columns := []meta.Columns{
		{
			Name: constants.InstanceIDPathParam,
			Id:   constants.ComponentValue,
		},
		{
			Name: constants.CnapName,
			Id:   constants.Name,
		},
		{
			Name: constants.CnapCode,
			Id:   constants.Code,
		},
		{
			Name: constants.UserAccountUUID,
			Id:   constants.AccountUUID,
		},
		{
			Name: constants.Standalone,
			Id:   constants.StandaloneSidecar,
		},
		{
			Name: constants.Hosting,
			Id:   constants.HostingSidecar,
		},
	}
	user := &[]meta.User{
		{
			ComponentValue:    "csm-xxxx",
			Name:              "xxxxxx",
			Code:              "xxxxxxx",
			AccountUuid:       "345678909568763456",
			HostingSidecar:    0,
			StandaloneSidecar: 0,
		},
	}

	users := []meta.User{
		{
			ComponentValue:    "csm-6tegf0u8",
			Name:              "crm-csm-6tegf0u8",
			Code:              "crm-test-demo",
			AccountUuid:       "",
			HostingSidecar:    2,
			StandaloneSidecar: 0,
		},
		{
			ComponentValue:    "csm-xxxx",
			Name:              "xxxxxx",
			Code:              "xxxxxxx",
			AccountUuid:       "345678909568763456",
			HostingSidecar:    2,
			StandaloneSidecar: 0,
		},
		{
			ComponentValue:    "csm-iif0ggfl",
			Name:              "crm-csm-iif0ggfl",
			Code:              "test-crm",
			AccountUuid:       "",
			HostingSidecar:    2,
			StandaloneSidecar: 0,
		},
		{
			ComponentValue:    "csm-hydlh994",
			Name:              "crm-csm-hydlh994",
			Code:              "crm-account-38a13",
			AccountUuid:       "",
			HostingSidecar:    2,
			StandaloneSidecar: 0,
		},
	}

	tests := []struct {
		name     string
		instance *meta.Instances
		cluster  *meta.Cluster
		want     *meta.Response
		wantErr  bool
	}{
		{
			name:     "test-getUserList",
			instance: &mockInstance,
			cluster:  &mockCluster,
			want: &meta.Response{
				Data: map[string]interface{}{
					constants.Columns: columns,
					constants.Rows:    users,
				},
				Status: 0,
				Msg:    "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:              NewOption(mockDB),
				instancesModel:   mockInstanceModel,
				clusterModel:     mockClusterModel,
				instancesService: mockInstanceService,
			}
			ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				if r.Method != http.MethodGet {
					w.WriteHeader(http.StatusNotFound)
				}
				w.WriteHeader(http.StatusOK)
				userInfo, _ := json.Marshal(user)
				w.Write(userInfo)
			}))
			defer ts.Close()

			getUsersURL = ts.URL

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(tt.instance, nil).AnyTimes()
			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(tt.cluster, nil).AnyTimes()
			mockInstanceService.EXPECT().GetActiveSidecarNum(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(2, nil).AnyTimes()

			got, err := s.GetUserList(mockCtx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !assert.ElementsMatch(t, got.Data[constants.Rows], tt.want.Data[constants.Rows]) {
				t.Errorf("GetUserList() got = %v, want %v", got, tt.want)
			}
		})
	}
}
