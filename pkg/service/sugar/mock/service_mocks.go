// Code generated by MockGen. DO NOT EDIT.
// Source: ./interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// GetScale mocks base method.
func (m *MockServiceInterface) GetScale(ctx context.CsmContext, region string, names map[string]string) (*meta.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScale", ctx, region, names)
	ret0, _ := ret[0].(*meta.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScale indicates an expected call of GetScale.
func (mr *MockServiceInterfaceMockRecorder) GetScale(ctx, region, names interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScale", reflect.TypeOf((*MockServiceInterface)(nil).GetScale), ctx, region, names)
}

// GetUserList mocks base method.
func (m *MockServiceInterface) GetUserList(ctx context.CsmContext) (*meta.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserList", ctx)
	ret0, _ := ret[0].(*meta.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserList indicates an expected call of GetUserList.
func (mr *MockServiceInterfaceMockRecorder) GetUserList(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserList", reflect.TypeOf((*MockServiceInterface)(nil).GetUserList), ctx)
}
