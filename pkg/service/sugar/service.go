package sugar

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"sort"
	"sync"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	instancesService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

type Service struct {
	opt *Option

	instancesModel   instances.ServiceInterface
	clusterModel     cluster.ServiceInterface
	instancesService instancesService.ServiceInterface
}

func NewSugarService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:              option,
		instancesModel:   instances.NewInstancesService(instances.NewOption(gormDB)),
		clusterModel:     cluster.NewClusterService(cluster.NewOption(gormDB)),
		instancesService: instancesService.NewInstanceService(instancesService.NewOption(gormDB)),
	}
}

var getUsersURL = "http://appspace-core.prod.appspace.appspace.baidu.com/api/v1/admin/csm/account"

func (s *Service) GetScale(ctx csmContext.CsmContext, region string, names map[string]string) (*meta.Response, error) {
	accountIds, err := s.instancesModel.GetAccountID(ctx)
	if err != nil {
		return nil, err
	}
	instanceList, err := s.instancesModel.GetAllInstances(ctx, region)
	if err != nil {
		return nil, err
	}
	primaryClusters, err := s.clusterModel.GetAllIstiodCluster(ctx, string(meta.ClusterTypePrimary))
	if err != nil {
		return nil, err
	}
	externalClusters, err := s.clusterModel.GetAllIstiodCluster(ctx, string(meta.ClusterTypeExternal))
	if err != nil {
		return nil, err
	}

	item := []meta.Item{
		{
			Name:    constants.User,
			Colspan: 2,
		},
		{
			Name:    constants.InstanceScale,
			Colspan: 3,
		},
		{
			Name:    constants.SidecarScale,
			Colspan: 3,
		},
	}
	var superHeaders []interface{}
	superHeaders = append(superHeaders, item)

	columns := []meta.Columns{
		{
			Name: constants.ID,
			Id:   constants.AccountID,
		},
		{
			Name: constants.UserName,
			Id:   constants.Name,
		},
		{
			Name: constants.Standalone,
			Id:   constants.StandaloneInstance,
		},
		{
			Name: constants.Hosting,
			Id:   constants.HostingInstance,
		},
		{
			Name: constants.Sum,
			Id:   constants.InstanceSum,
		},
		{
			Name: constants.Standalone,
			Id:   constants.StandaloneSidecar,
		},
		{
			Name: constants.Hosting,
			Id:   constants.HostingSidecar,
		},
		{
			Name: constants.Sum,
			Id:   constants.SidecarSum,
		},
	}

	var rows []meta.Rows
	for _, accountId := range accountIds {
		totalStandaloneSidecarNum := 0
		totalHostingSidecarNum := 0
		var standaloneInstances, hostingInstances int
		var wg sync.WaitGroup
		var nsMtx sync.Mutex
		var userName string
		for _, instance := range instanceList {
			if instance.AccountId == accountId {
				instanceUUID := instance.InstanceUUID
				instanceType := instance.InstanceType
				userName = names[accountId]
				hostingSidecarNum := 0
				standaloneSidecarNum := 0

				if instanceType == string(version.HostingVersionType) {
					hostingInstances++
					for _, clusterInfo := range *externalClusters {
						if clusterInfo.InstanceUUID == instanceUUID {
							wg.Add(1)
							go func(clusterInfo meta.Cluster) {
								defer func() {
									if e := recover(); e != nil {
										ctx.CsmLogger().Errorf("goroutine exited abnormally because: %v", e)
									}
									wg.Done()
								}()
								hostingSidecarNum, err = s.instancesService.GetActiveSidecarNum(ctx, instanceUUID,
									&clusterInfo, meta.MeshType(instanceType))
								if err != nil {
									ctx.CsmLogger().Errorf("instance %s failed to get sidecar nums, err %v", instanceUUID, err)
								}
								nsMtx.Lock()
								totalHostingSidecarNum += hostingSidecarNum
								nsMtx.Unlock()
							}(clusterInfo)
						}
					}

				} else {
					standaloneInstances++
					for _, clusterInfo := range *primaryClusters {
						if clusterInfo.InstanceUUID == instanceUUID {
							wg.Add(1)
							go func(clusterInfo meta.Cluster) {
								defer func() {
									if e := recover(); e != nil {
										ctx.CsmLogger().Errorf("goroutine exited abnormally because: %v", e)
									}
									wg.Done()
								}()
								standaloneSidecarNum, err = s.instancesService.GetSugarSidecarNum(ctx, instanceUUID,
									&clusterInfo, meta.MeshType(instanceType))
								if err != nil {
									ctx.CsmLogger().Errorf("instance %s failed to get sidecar nums, err %v", instanceUUID, err)
								}
								nsMtx.Lock()
								totalStandaloneSidecarNum += standaloneSidecarNum
								nsMtx.Unlock()
							}(clusterInfo)
						}
					}
				}
			}
		}
		wg.Wait()
		row := meta.Rows{
			AccountId:          accountId,
			Name:               userName,
			StandaloneInstance: standaloneInstances,
			HostingInstance:    hostingInstances,
			InstanceSum:        standaloneInstances + hostingInstances,
			StandaloneSidecar:  totalStandaloneSidecarNum,
			HostingSidecar:     totalHostingSidecarNum,
			SidecarSum:         totalStandaloneSidecarNum + totalHostingSidecarNum,
		}
		rows = append(rows, row)
	}
	sort.SliceStable(rows, func(i, j int) bool {
		return rows[i].SidecarSum > rows[j].SidecarSum
	})

	data := map[string]interface{}{constants.Columns: columns, constants.SuperHeaders: superHeaders, constants.Rows: rows}
	response := &meta.Response{
		Data:   data,
		Status: 0,
		Msg:    "",
	}
	return response, nil
}

func (s *Service) GetUserList(ctx csmContext.CsmContext) (*meta.Response, error) {
	columns := []meta.Columns{
		{
			Name: constants.InstanceID,
			Id:   constants.ComponentValue,
		},
		{
			Name: constants.CnapName,
			Id:   constants.Name,
		},
		{
			Name: constants.CnapCode,
			Id:   constants.Code,
		},
		{
			Name: constants.UserAccountUUID,
			Id:   constants.AccountUUID,
		},
		{
			Name: constants.Standalone,
			Id:   constants.StandaloneSidecar,
		},
		{
			Name: constants.Hosting,
			Id:   constants.HostingSidecar,
		},
	}

	rsp, err := http.Get(getUsersURL)
	defer rsp.Body.Close()
	if err != nil {
		ctx.CsmLogger().Errorf("get users has error : %v", err)
		return nil, err
	}
	body, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		return nil, err
	}
	var users []meta.User
	err = json.Unmarshal(body, &users)
	if err != nil {
		return nil, err
	}

	var newUser meta.User
	instancesId := []string{constants.InstanceID1, constants.InstanceID2, constants.InstanceID3}
	codes := []string{constants.Code1, constants.Code2, constants.Code3}
	for i, instanceId := range instancesId {
		newUser.ComponentValue = instanceId
		newUser.Name = "crm-" + instanceId
		newUser.Code = codes[i]
		users = append(users, newUser)
	}

	var wg sync.WaitGroup
	var nsMtx sync.Mutex
	hostingSidecarNum := 0
	standaloneSidecarNum := 0
	var userInfo []meta.User
	for _, user := range users {
		instanceInfo, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, user.ComponentValue)
		if err != nil {
			ctx.CsmLogger().Errorf("instance %s getInstanceByInstanceUUID has error : %v", user.ComponentValue, err)
			userInfo = append(userInfo, user)
		} else {
			istiodCluster, err := s.clusterModel.GetIstiodCluster(ctx, user.ComponentValue, instanceInfo.InstanceType)
			if err != nil {
				ctx.CsmLogger().Errorf("instance %s getIstiodCluster has error : %v", user.ComponentValue, err)
			}
			if instanceInfo.InstanceType == string(version.HostingVersionType) {
				wg.Add(1)
				go func(user meta.User) {
					defer func() {
						if e := recover(); e != nil {
							ctx.CsmLogger().Errorf("goroutine exited abnormally because: %v", e)
						}
						wg.Done()
					}()
					hostingSidecarNum, err = s.instancesService.GetActiveSidecarNum(ctx, instanceInfo.InstanceUUID,
						istiodCluster, meta.MeshType(instanceInfo.InstanceType))
					if err != nil {
						ctx.CsmLogger().Errorf("instance %s failed to get sidecar nums, err %v", instanceInfo.InstanceUUID, err)
					}
					nsMtx.Lock()
					user.HostingSidecar = hostingSidecarNum
					userInfo = append(userInfo, user)
					nsMtx.Unlock()
				}(user)
			} else {
				wg.Add(1)
				go func(user meta.User) {
					defer func() {
						if e := recover(); e != nil {
							ctx.CsmLogger().Errorf("goroutine exited abnormally because: %v", e)
						}
						wg.Done()
					}()
					standaloneSidecarNum, err = s.instancesService.GetSugarSidecarNum(ctx, instanceInfo.InstanceUUID,
						istiodCluster, meta.MeshType(instanceInfo.InstanceType))
					if err != nil {
						ctx.CsmLogger().Errorf("instance %s failed to get sidecar nums, err %v", instanceInfo.InstanceUUID, err)
					}
					nsMtx.Lock()
					user.StandaloneSidecar = standaloneSidecarNum
					userInfo = append(userInfo, user)
					nsMtx.Unlock()
				}(user)
			}
		}
	}
	wg.Wait()
	sort.SliceStable(userInfo, func(i, j int) bool {
		if userInfo[i].StandaloneSidecar > userInfo[j].StandaloneSidecar {
			return true
		}
		return false
	})

	data := map[string]interface{}{constants.Columns: columns, constants.Rows: userInfo}
	response := &meta.Response{
		Data:   data,
		Status: 0,
		Msg:    "",
	}
	return response, nil
}
