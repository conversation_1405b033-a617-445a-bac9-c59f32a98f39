// Code generated by MockGen. DO NOT EDIT.
// Source: ./interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	meta0 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	version "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/version"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// AddHostingServiceMeshInstance mocks base method.
func (m *MockServiceInterface) AddHostingServiceMeshInstance(ctx context.CsmContext, createMeshRequest *meta0.CreateMeshRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddHostingServiceMeshInstance", ctx, createMeshRequest)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddHostingServiceMeshInstance indicates an expected call of AddHostingServiceMeshInstance.
func (mr *MockServiceInterfaceMockRecorder) AddHostingServiceMeshInstance(ctx, createMeshRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddHostingServiceMeshInstance", reflect.TypeOf((*MockServiceInterface)(nil).AddHostingServiceMeshInstance), ctx, createMeshRequest)
}

// AddMeshInstance mocks base method.
func (m *MockServiceInterface) AddMeshInstance(ctx context.CsmContext, createMeshRequest *meta0.CreateMeshRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMeshInstance", ctx, createMeshRequest)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddMeshInstance indicates an expected call of AddMeshInstance.
func (mr *MockServiceInterfaceMockRecorder) AddMeshInstance(ctx, createMeshRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMeshInstance", reflect.TypeOf((*MockServiceInterface)(nil).AddMeshInstance), ctx, createMeshRequest)
}

// AddRemoteConfigMeshCluster mocks base method.
func (m *MockServiceInterface) AddRemoteConfigMeshCluster(ctx context.CsmContext, createRemoteUserMeshCluster *meta0.CreateRemoteUserMeshCluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRemoteConfigMeshCluster", ctx, createRemoteUserMeshCluster)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRemoteConfigMeshCluster indicates an expected call of AddRemoteConfigMeshCluster.
func (mr *MockServiceInterfaceMockRecorder) AddRemoteConfigMeshCluster(ctx, createRemoteUserMeshCluster interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRemoteConfigMeshCluster", reflect.TypeOf((*MockServiceInterface)(nil).AddRemoteConfigMeshCluster), ctx, createRemoteUserMeshCluster)
}

// AddRemoteUserMeshCluster mocks base method.
func (m *MockServiceInterface) AddRemoteUserMeshCluster(ctx context.CsmContext, createRemoteUserMeshCluster *meta0.CreateRemoteUserMeshCluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRemoteUserMeshCluster", ctx, createRemoteUserMeshCluster)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRemoteUserMeshCluster indicates an expected call of AddRemoteUserMeshCluster.
func (mr *MockServiceInterfaceMockRecorder) AddRemoteUserMeshCluster(ctx, createRemoteUserMeshCluster interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRemoteUserMeshCluster", reflect.TypeOf((*MockServiceInterface)(nil).AddRemoteUserMeshCluster), ctx, createRemoteUserMeshCluster)
}

// CreateNamespace mocks base method.
func (m *MockServiceInterface) CreateNamespace(ctx context.CsmContext, instanceId, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNamespace", ctx, instanceId, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateNamespace indicates an expected call of CreateNamespace.
func (mr *MockServiceInterfaceMockRecorder) CreateNamespace(ctx, instanceId, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNamespace", reflect.TypeOf((*MockServiceInterface)(nil).CreateNamespace), ctx, instanceId, namespace)
}

// DeleteNamespace mocks base method.
func (m *MockServiceInterface) DeleteNamespace(ctx context.CsmContext, instanceId, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNamespace", ctx, instanceId, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNamespace indicates an expected call of DeleteNamespace.
func (mr *MockServiceInterfaceMockRecorder) DeleteNamespace(ctx, instanceId, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNamespace", reflect.TypeOf((*MockServiceInterface)(nil).DeleteNamespace), ctx, instanceId, namespace)
}

// DeleteServiceMeshInstance mocks base method.
func (m *MockServiceInterface) DeleteServiceMeshInstance(ctx context.CsmContext, deleteMeshRequest *meta0.DeleteMeshRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteServiceMeshInstance", ctx, deleteMeshRequest)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServiceMeshInstance indicates an expected call of DeleteServiceMeshInstance.
func (mr *MockServiceInterfaceMockRecorder) DeleteServiceMeshInstance(ctx, deleteMeshRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceMeshInstance", reflect.TypeOf((*MockServiceInterface)(nil).DeleteServiceMeshInstance), ctx, deleteMeshRequest)
}

// GenerateInstancesID mocks base method.
func (m *MockServiceInterface) GenerateInstancesID(ctx context.CsmContext) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateInstancesID", ctx)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateInstancesID indicates an expected call of GenerateInstancesID.
func (mr *MockServiceInterfaceMockRecorder) GenerateInstancesID(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateInstancesID", reflect.TypeOf((*MockServiceInterface)(nil).GenerateInstancesID), ctx)
}

// GetActiveSidecarNum mocks base method.
func (m *MockServiceInterface) GetActiveSidecarNum(ctx context.CsmContext, instanceUUID string, istiodCluster *meta.Cluster, meshType meta.MeshType) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveSidecarNum", ctx, instanceUUID, istiodCluster, meshType)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveSidecarNum indicates an expected call of GetActiveSidecarNum.
func (mr *MockServiceInterfaceMockRecorder) GetActiveSidecarNum(ctx, instanceUUID, istiodCluster, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveSidecarNum", reflect.TypeOf((*MockServiceInterface)(nil).GetActiveSidecarNum), ctx, instanceUUID, istiodCluster, meshType)
}

// GetAllCceClusterByRegion mocks base method.
func (m *MockServiceInterface) GetAllCceClusterByRegion(ctx context.CsmContext, region string) ([]meta0.MeshCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllCceClusterByRegion", ctx, region)
	ret0, _ := ret[0].([]meta0.MeshCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllCceClusterByRegion indicates an expected call of GetAllCceClusterByRegion.
func (mr *MockServiceInterfaceMockRecorder) GetAllCceClusterByRegion(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCceClusterByRegion", reflect.TypeOf((*MockServiceInterface)(nil).GetAllCceClusterByRegion), ctx, region)
}

// GetAllInstances mocks base method.
func (m *MockServiceInterface) GetAllInstances(ctx context.CsmContext) ([]meta.Instances, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllInstances", ctx)
	ret0, _ := ret[0].([]meta.Instances)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllInstances indicates an expected call of GetAllInstances.
func (mr *MockServiceInterfaceMockRecorder) GetAllInstances(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllInstances", reflect.TypeOf((*MockServiceInterface)(nil).GetAllInstances), ctx)
}

// GetDiscoverySelector mocks base method.
func (m *MockServiceInterface) GetDiscoverySelector(ctx context.CsmContext, instanceUUID string) (*meta0.DiscoverySelector, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiscoverySelector", ctx, instanceUUID)
	ret0, _ := ret[0].(*meta0.DiscoverySelector)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiscoverySelector indicates an expected call of GetDiscoverySelector.
func (mr *MockServiceInterfaceMockRecorder) GetDiscoverySelector(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiscoverySelector", reflect.TypeOf((*MockServiceInterface)(nil).GetDiscoverySelector), ctx, instanceUUID)
}

// GetHostingCluster mocks base method.
func (m *MockServiceInterface) GetHostingCluster(ctx context.CsmContext, region string) (string, string) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHostingCluster", ctx, region)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	return ret0, ret1
}

// GetHostingCluster indicates an expected call of GetHostingCluster.
func (mr *MockServiceInterfaceMockRecorder) GetHostingCluster(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHostingCluster", reflect.TypeOf((*MockServiceInterface)(nil).GetHostingCluster), ctx, region)
}

// GetInstanceDetail mocks base method.
func (m *MockServiceInterface) GetInstanceDetail(ctx context.CsmContext, instanceUUID string) (*meta.InstanceDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceDetail", ctx, instanceUUID)
	ret0, _ := ret[0].(*meta.InstanceDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceDetail indicates an expected call of GetInstanceDetail.
func (mr *MockServiceInterfaceMockRecorder) GetInstanceDetail(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceDetail", reflect.TypeOf((*MockServiceInterface)(nil).GetInstanceDetail), ctx, instanceUUID)
}

// GetInstanceStatus mocks base method.
func (m *MockServiceInterface) GetInstanceStatus(ctx context.CsmContext, instanceId, remoteClusterID, remoteClusterRegion string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceStatus", ctx, instanceId, remoteClusterID, remoteClusterRegion)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceStatus indicates an expected call of GetInstanceStatus.
func (mr *MockServiceInterfaceMockRecorder) GetInstanceStatus(ctx, instanceId, remoteClusterID, remoteClusterRegion interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceStatus", reflect.TypeOf((*MockServiceInterface)(nil).GetInstanceStatus), ctx, instanceId, remoteClusterID, remoteClusterRegion)
}

// GetIstioSupportK8sVersion mocks base method.
func (m *MockServiceInterface) GetIstioSupportK8sVersion(ctx context.CsmContext) []version.IstioSupportK8sVersion {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIstioSupportK8sVersion", ctx)
	ret0, _ := ret[0].([]version.IstioSupportK8sVersion)
	return ret0
}

// GetIstioSupportK8sVersion indicates an expected call of GetIstioSupportK8sVersion.
func (mr *MockServiceInterfaceMockRecorder) GetIstioSupportK8sVersion(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIstioSupportK8sVersion", reflect.TypeOf((*MockServiceInterface)(nil).GetIstioSupportK8sVersion), ctx)
}

// GetServiceMeshInstances mocks base method.
func (m *MockServiceInterface) GetServiceMeshInstances(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.MeshInstanceListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceMeshInstances", ctx, mrp)
	ret0, _ := ret[0].(*meta.MeshInstanceListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceMeshInstances indicates an expected call of GetServiceMeshInstances.
func (mr *MockServiceInterfaceMockRecorder) GetServiceMeshInstances(ctx, mrp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceMeshInstances", reflect.TypeOf((*MockServiceInterface)(nil).GetServiceMeshInstances), ctx, mrp)
}

// GetSugarSidecarNum mocks base method.
func (m *MockServiceInterface) GetSugarSidecarNum(ctx context.CsmContext, instanceUUID string, istiodCluster *meta.Cluster, meshType meta.MeshType) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSugarSidecarNum", ctx, instanceUUID, istiodCluster, meshType)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSugarSidecarNum indicates an expected call of GetSugarSidecarNum.
func (mr *MockServiceInterfaceMockRecorder) GetSugarSidecarNum(ctx, instanceUUID, istiodCluster, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSugarSidecarNum", reflect.TypeOf((*MockServiceInterface)(nil).GetSugarSidecarNum), ctx, instanceUUID, istiodCluster, meshType)
}

// GetVpcAndSubnetInfo mocks base method.
func (m *MockServiceInterface) GetVpcAndSubnetInfo(ctx context.CsmContext, vpcId, subnetId, region string) (*meta.InstanceNetworkType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVpcAndSubnetInfo", ctx, vpcId, subnetId, region)
	ret0, _ := ret[0].(*meta.InstanceNetworkType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVpcAndSubnetInfo indicates an expected call of GetVpcAndSubnetInfo.
func (mr *MockServiceInterfaceMockRecorder) GetVpcAndSubnetInfo(ctx, vpcId, subnetId, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVpcAndSubnetInfo", reflect.TypeOf((*MockServiceInterface)(nil).GetVpcAndSubnetInfo), ctx, vpcId, subnetId, region)
}

// InstallMeshCluster mocks base method.
func (m *MockServiceInterface) InstallMeshCluster(ctx context.CsmContext, instances *meta.Instances, clusters *meta.Cluster, pt meta.PaaSType, traceInfo *meta.TraceInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallMeshCluster", ctx, instances, clusters, pt, traceInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// InstallMeshCluster indicates an expected call of InstallMeshCluster.
func (mr *MockServiceInterfaceMockRecorder) InstallMeshCluster(ctx, instances, clusters, pt, traceInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallMeshCluster", reflect.TypeOf((*MockServiceInterface)(nil).InstallMeshCluster), ctx, instances, clusters, pt, traceInfo)
}

// InstallRemoteMeshCluster mocks base method.
func (m *MockServiceInterface) InstallRemoteMeshCluster(ctx context.CsmContext, instances *meta.Instances, primaryCluster, clusters *meta.Cluster, pt meta.PaaSType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallRemoteMeshCluster", ctx, instances, primaryCluster, clusters, pt)
	ret0, _ := ret[0].(error)
	return ret0
}

// InstallRemoteMeshCluster indicates an expected call of InstallRemoteMeshCluster.
func (mr *MockServiceInterfaceMockRecorder) InstallRemoteMeshCluster(ctx, instances, primaryCluster, clusters, pt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallRemoteMeshCluster", reflect.TypeOf((*MockServiceInterface)(nil).InstallRemoteMeshCluster), ctx, instances, primaryCluster, clusters, pt)
}

// NewServiceMeshInstance mocks base method.
func (m *MockServiceInterface) NewServiceMeshInstance(ctx context.CsmContext, instances *meta.Instances, clusters *meta.Cluster, traceInfo *meta.TraceInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewServiceMeshInstance", ctx, instances, clusters, traceInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewServiceMeshInstance indicates an expected call of NewServiceMeshInstance.
func (mr *MockServiceInterfaceMockRecorder) NewServiceMeshInstance(ctx, instances, clusters, traceInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewServiceMeshInstance", reflect.TypeOf((*MockServiceInterface)(nil).NewServiceMeshInstance), ctx, instances, clusters, traceInfo)
}

// RemoveMeshInstance mocks base method.
func (m *MockServiceInterface) RemoveMeshInstance(ctx context.CsmContext, deleteMeshCluster *meta0.DeleteMeshInstance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveMeshInstance", ctx, deleteMeshCluster)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveMeshInstance indicates an expected call of RemoveMeshInstance.
func (mr *MockServiceInterfaceMockRecorder) RemoveMeshInstance(ctx, deleteMeshCluster interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveMeshInstance", reflect.TypeOf((*MockServiceInterface)(nil).RemoveMeshInstance), ctx, deleteMeshCluster)
}

// RemoveRemoteConfigMeshCluster mocks base method.
func (m *MockServiceInterface) RemoveRemoteConfigMeshCluster(ctx context.CsmContext, deleteRemoteUserMeshCluster *meta0.DeleteRemoteUserMeshCluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveRemoteConfigMeshCluster", ctx, deleteRemoteUserMeshCluster)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveRemoteConfigMeshCluster indicates an expected call of RemoveRemoteConfigMeshCluster.
func (mr *MockServiceInterfaceMockRecorder) RemoveRemoteConfigMeshCluster(ctx, deleteRemoteUserMeshCluster interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveRemoteConfigMeshCluster", reflect.TypeOf((*MockServiceInterface)(nil).RemoveRemoteConfigMeshCluster), ctx, deleteRemoteUserMeshCluster)
}

// RemoveRemoteUserMeshCluster mocks base method.
func (m *MockServiceInterface) RemoveRemoteUserMeshCluster(ctx context.CsmContext, deleteRemoteUserMeshCluster *meta0.DeleteRemoteUserMeshCluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveRemoteUserMeshCluster", ctx, deleteRemoteUserMeshCluster)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveRemoteUserMeshCluster indicates an expected call of RemoveRemoteUserMeshCluster.
func (mr *MockServiceInterfaceMockRecorder) RemoveRemoteUserMeshCluster(ctx, deleteRemoteUserMeshCluster interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveRemoteUserMeshCluster", reflect.TypeOf((*MockServiceInterface)(nil).RemoveRemoteUserMeshCluster), ctx, deleteRemoteUserMeshCluster)
}

// UnInstallMeshCluster mocks base method.
func (m *MockServiceInterface) UnInstallMeshCluster(ctx context.CsmContext, instances *meta.Instances, cluster *meta.Cluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnInstallMeshCluster", ctx, instances, cluster)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnInstallMeshCluster indicates an expected call of UnInstallMeshCluster.
func (mr *MockServiceInterfaceMockRecorder) UnInstallMeshCluster(ctx, instances, cluster interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnInstallMeshCluster", reflect.TypeOf((*MockServiceInterface)(nil).UnInstallMeshCluster), ctx, instances, cluster)
}

// UpdateDiscoverySelector mocks base method.
func (m *MockServiceInterface) UpdateDiscoverySelector(ctx context.CsmContext, instanceUUID string, selector *meta0.DiscoverySelector) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDiscoverySelector", ctx, instanceUUID, selector)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDiscoverySelector indicates an expected call of UpdateDiscoverySelector.
func (mr *MockServiceInterfaceMockRecorder) UpdateDiscoverySelector(ctx, instanceUUID, selector interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDiscoverySelector", reflect.TypeOf((*MockServiceInterface)(nil).UpdateDiscoverySelector), ctx, instanceUUID, selector)
}
