package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr/personal"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
)

const imageBuilderWorkflowTemplateName = "image-builder-template"

var (
	vpcDomainRegexp    = regexp.MustCompile(`^(ccr-[0-9a-zA-z]{8})-vpc[\-\.]cnc\.([^.]*).baidubce.com$`)
	publicDomainRegexp = regexp.MustCompile(`^(ccr-[0-9a-zA-z]{8})-pub[\-\.]cnc\.([^.]*).baidubce.com$`)
)

type RepositoryServiceInterface interface {
	CreateKanikoRegistrySecret(ctx context.Context, instanceInfo *listers.InstanceInfo) error
	CreateBuildRepositoryTask(ctx context.Context, projectName, repositoryName, tagName string,
		dockerfile string, fromType model.FromType, accountID, userID, username, buildID string,
		instanceInfo *listers.InstanceInfo) (string, error)
	ListBuildRepositoryTask(ctx context.Context, instanceID, projectName,
		repositoryName, keywordType, keyword string) ([]*model.BuildRepositoryTaskResult, error)
	GetBuildRepositoryTask(ctx context.Context, instanceID, projectName,
		repositoryName, buildID string) (*model.BuildRepositoryTaskResult, error)
	DeleteBuildRepositoryTask(ctx context.Context, instanceID string, buildID string) error
	BatchDeleteBuildRepositoryTask(ctx context.Context, instanceID string, buildIDs []string) error
}

type RepositoryService struct {
	clients clientset.ClientSetInterface
}

func NewRepositoryService(clis clientset.ClientSetInterface) RepositoryServiceInterface {
	return &RepositoryService{
		clients: clis,
	}
}

func (h *RepositoryService) getRepoCredential(ctx context.Context, accountID, userID, userName string, registries []string) (*DockerConfigJSON, error) {
	logger := gin_context.LoggerFromContext(ctx)

	config := &DockerConfigJSON{
		Auths: make(DockerConfig),
	}
	for _, v := range registries {
		if _, ok := config.Auths[v]; ok {
			// already exist
			continue
		}

		if v == "registry.baidubce.com" {
			personCli, err := h.clients.PersonalClientForAccount(accountID, userID)
			if err != nil {
				logger.Errorf("failed to get PersonalClientForAccount, accountID: %v, userID: %v, err: %v", accountID, userID, err)
				return nil, err
			}

			personResp, err := personCli.CreatePersonalTemporaryPassword(&personal.TemporaryPasswordArgs{
				Duration: 1,
			})
			if err != nil {
				logger.Errorf("createPersonalTemporaryPassword failed, accountID: %v, userID: %v, err: %v", accountID, userID, err)
				return nil, err
			}

			config.Auths[v] = DockerConfigEntry{
				Username: userID,
				Password: personResp.Token,
			}

			continue
		}

		// ccr-xxxxxxxx-vpc.cnc.bj.baidubce.com
		instanceID, region := extractIDAndRegionFromEndpoint(v)
		if instanceID == "" || !utils.IsSameRegion(region, h.clients.Conf().Region) {
			logger.Infof("cann't get vpc domain info, skipping for : %v", v)
			continue
		}

		cli, err := h.clients.CCRClientForAccount(accountID, userID, "127.0.0.1:8500")
		if err != nil {
			logger.Errorf("create ccr client failed! accountID: %v, userID: %v, err: %v", accountID, userID, err)
			return nil, err
		}

		passwordResp, err := cli.CreateTemporaryPassword(instanceID, &ccr.TemporaryPasswordArgs{
			Duration: 1,
		})
		if err != nil {
			logger.Errorf("createTemporaryPassword failed, accountID: %v, userID: %v, err: %v", accountID, userID, err)
			return nil, err
		}

		config.Auths[v] = DockerConfigEntry{
			Username: userName,
			Password: passwordResp.Password,
		}
	}

	return config, nil
}

// CreateKanikoRegistrySecret 函数用于创建Kubernetes中用于Harbor实例的 Kaniko 拉取秘钥
func (h *RepositoryService) CreateKanikoRegistrySecret(ctx context.Context, instanceInfo *listers.InstanceInfo) error {
	logger := gin_context.LoggerFromContext(ctx)

	var KanikoRegistrySecret corev1.Secret
	err := h.clients.K8sClient().Get(ctx, types.NamespacedName{Namespace: instanceInfo.ID, Name: "kaniko-registry-secret"}, &KanikoRegistrySecret)
	if err != nil && !errors.IsNotFound(err) {
		logger.Errorf("get kaniko pull secret failed: %s", err)
		return err
	}

	if errors.IsNotFound(err) {
		dockerConfigJSON := DockerConfigJSON{
			Auths: DockerConfig{
				fmt.Sprintf("%s-harbor-core:80", instanceInfo.ID): DockerConfigEntry{
					Auth:     base64.StdEncoding.EncodeToString([]byte(instanceInfo.UserName + ":" + instanceInfo.Password)),
					Username: instanceInfo.UserName,
					Password: instanceInfo.Password,
				},
			},
		}
		dockerConfigJSONData, err := json.Marshal(dockerConfigJSON)
		if err != nil {
			logger.Errorf("marshal docker config json failed: %s", err)
			return err
		}

		KanikoRegistrySecret = corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "kaniko-registry-secret",
				Namespace: instanceInfo.ID,
			},
			Data: map[string][]byte{"config.json": dockerConfigJSONData},
		}
		err = h.clients.K8sClient().Create(ctx, &KanikoRegistrySecret)
		if err != nil {
			logger.Errorf("create kaniko pull secret failed: %s", err)
			return err
		}
	}
	return nil
}

func (h *RepositoryService) CreateBuildRepositoryTask(ctx context.Context, projectName, repositoryName, tagName string,
	dockerfile string, fromType model.FromType, accountID, userID, username, buildID string,
	instanceInfo *listers.InstanceInfo) (string, error) {
	logger := gin_context.LoggerFromContext(ctx)

	// create secret
	srcImageEndpoint := extractImageEndpointFromDockerfile(dockerfile)
	localImageEndpoint := instanceInfo.PrivateURL

	dockerConfig, err := h.getRepoCredential(ctx, accountID, userID, username, []string{srcImageEndpoint, localImageEndpoint})
	if err != nil {
		logger.Errorf("get repo credential failed: %s", err)
		return "", err
	}

	dockerConfigJson, err := json.Marshal(dockerConfig)
	if err != nil {
		logger.Errorf("marshal docker config json failed: %s", err)
		return "", err
	}

	err = h.clients.K8sClient().Create(ctx, &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "image-build-" + buildID,
			Namespace: instanceInfo.ID,
			Labels: map[string]string{
				"kind": "image-build",
			},
		},
		Data: map[string][]byte{"config.json": dockerConfigJson},
	})

	if err != nil {
		if errors.IsAlreadyExists(err) {
			logger.Warnf("build image secret already exists, skip create: %v", buildID)
		}
		logger.Errorf("craete build image secret failed: %v", err)
		return "", err
	}

	buildWorkflow := &wfv1.Workflow{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "image-build-" + buildID,
			Namespace: instanceInfo.ID,
			Labels: map[string]string{
				"kind": "image-build",
			},
		},
		Spec: wfv1.WorkflowSpec{
			Arguments: wfv1.Arguments{
				Parameters: []wfv1.Parameter{
					{
						Name:  "dockerfile",
						Value: wfv1.AnyStringPtr(base64.StdEncoding.EncodeToString([]byte(dockerfile))),
					}, {
						Name:  "destination",
						Value: wfv1.AnyStringPtr(fmt.Sprintf("%s/%s/%s:%s", instanceInfo.PrivateURL, projectName, repositoryName, tagName)),
					}, {
						Name:  "insecureRegistry",
						Value: wfv1.AnyStringPtr(fmt.Sprintf("%s-harbor-core:80", instanceInfo.ID)),
					}, {
						Name:  "fromType",
						Value: wfv1.AnyStringPtr(fromType),
					}, {
						Name:  "createBy",
						Value: wfv1.AnyStringPtr(userID),
					}, {
						Name:  "tagName",
						Value: wfv1.AnyStringPtr(tagName),
					},
				},
			},
			WorkflowTemplateRef: &wfv1.WorkflowTemplateRef{
				Name:         imageBuilderWorkflowTemplateName,
				ClusterScope: true,
			},
			Volumes: []corev1.Volume{
				{
					Name: "config",
					VolumeSource: corev1.VolumeSource{
						Secret: &corev1.SecretVolumeSource{
							SecretName: "image-build-" + buildID,
						},
					},
				},
			},
			HostAliases: []corev1.HostAlias{
				{
					IP:        instanceInfo.IngressEndpoint,
					Hostnames: []string{instanceInfo.PrivateURL},
				},
			},
		},
	}

	srcInstanceID, region := extractIDAndRegionFromEndpoint(srcImageEndpoint)
	if srcInstanceID != "" || region == h.clients.Conf().Region {
		srcImageInfo, err := h.clients.Lister().GetInstanceInfo(srcInstanceID)
		if err != nil {
			logger.Errorf("get instance info of %s failed: %s", srcInstanceID, err)
			return "", err
		}

		buildWorkflow.Spec.HostAliases = append(buildWorkflow.Spec.HostAliases, corev1.HostAlias{
			IP:        srcImageInfo.IngressEndpoint,
			Hostnames: []string{srcImageInfo.PrivateURL},
		})
	}

	if err := h.clients.K8sClient().Create(ctx, buildWorkflow); err != nil {
		logger.Errorf("create argo workflow build image failed: %s", err)
		return "", err
	}
	return buildWorkflow.Name, nil
}

func (cls *RepositoryService) ListBuildRepositoryTask(ctx context.Context, instanceID, projectName, repositoryName, keywordType, keyword string) ([]*model.BuildRepositoryTaskResult, error) {
	logger := gin_context.LoggerFromContext(ctx)
	var wfList wfv1.WorkflowList
	err := cls.clients.K8sClient().List(context.Background(), &wfList, &client.ListOptions{
		LabelSelector: labels.SelectorFromSet(labels.Set{"projectName": projectName, "repositoryName": repositoryName}),

		Namespace: instanceID,
		Limit:     1000,
	})

	if err != nil {
		logger.Errorf("list kaniko workflow failed: %s", err)
		return nil, fmt.Errorf("list kaniko workflow failed: %w", err)
	}

	items := make([]*model.BuildRepositoryTaskResult, 0)

	if len(wfList.Items) == 0 {
		return items, nil
	}

	matched := 0
	for _, v := range wfList.Items {
		switch {
		case keyword == "":
			matched++
		case keywordType == "id":
			if strings.Index(v.GetName(), keyword) != -1 {
				matched++
			}
		}

		info := repositoryTaskFromObject(&v)
		if matched > len(items) {
			items = append(items, info)
		}
	}
	return items, nil
}

func (cls *RepositoryService) GetBuildRepositoryTask(ctx context.Context, instanceID, projectName, repositoryName, buildID string) (*model.BuildRepositoryTaskResult, error) {
	logger := gin_context.LoggerFromContext(ctx)

	var wf wfv1.Workflow
	if err := cls.clients.K8sClient().Get(ctx, client.ObjectKey{Name: buildID, Namespace: instanceID}, &wf); err != nil {
		logger.Errorf("get kaniko workflow failed: %s", err)
		return nil, err
	}

	info := repositoryTaskFromObject(&wf)

	return info, nil

}

func (h *RepositoryService) DeleteBuildRepositoryTask(ctx context.Context, instanceID string, buildID string) error {
	logger := gin_context.LoggerFromContext(ctx)
	wf := &wfv1.Workflow{
		ObjectMeta: metav1.ObjectMeta{Name: buildID, Namespace: instanceID},
	}
	if err := h.clients.K8sClient().Delete(ctx, wf, &client.DeleteOptions{}); err != nil {
		logger.Errorf("delete kaniko workflow failed: %s", err)
		return err
	}
	return nil
}

func (h *RepositoryService) BatchDeleteBuildRepositoryTask(ctx context.Context, instanceID string, buildIDs []string) error {
	logger := gin_context.LoggerFromContext(ctx)

	for _, buildID := range buildIDs {
		wf := &wfv1.Workflow{
			ObjectMeta: metav1.ObjectMeta{Name: "image-build-" + buildID, Namespace: instanceID},
		}
		if err := h.clients.K8sClient().Delete(ctx, wf, &client.DeleteOptions{}); err != nil {
			if errors.IsNotFound(err) {
				logger.Warnf("image build %v has been delete, skip now", buildID)
				continue
			}

			logger.Errorf("delete kaniko workflow failed: %s", err)
			return err
		}
	}

	return nil
}

func repositoryTaskFromObject(obj *wfv1.Workflow) *model.BuildRepositoryTaskResult {

	createdAt := obj.GetCreationTimestamp().Time

	var tagName string
	var isLatest bool
	var dockerfile string
	var createBy string
	var fromType string

	for _, parameter := range obj.Spec.Arguments.Parameters {
		if parameter.Name == "tagName" {
			tagName = parameter.Value.String()
			continue
		}
		if parameter.Name == "dockerfile" {
			dockerfile = parameter.Value.String()
			continue
		}
		if parameter.Name == "createBy" {
			createBy = parameter.Value.String()
			continue
		}
		if parameter.Name == "fromType" {
			fromType = parameter.Value.String()
			continue
		}
	}

	if tagName == "latest" {
		isLatest = true
	}

	return &model.BuildRepositoryTaskResult{
		ID:         obj.GetName(),
		TagName:    tagName,
		IsLatest:   isLatest,
		FromType:   fromType,
		Dockerfile: dockerfile,
		CreateAt:   createdAt,
		CreateBy:   createBy,
		Status:     repositoryTaskStatusFromWorkflowPhase(obj.Status.Phase),
	}
}

func repositoryTaskStatusFromWorkflowPhase(phase wfv1.WorkflowPhase) string {
	switch phase {
	case wfv1.WorkflowUnknown, wfv1.WorkflowPending:
		phase = "Pending"
	case wfv1.WorkflowRunning:
		phase = "Running"
	case wfv1.WorkflowSucceeded:
		phase = "Succeeded"
	case wfv1.WorkflowFailed:
		phase = "Failed"
	case wfv1.WorkflowError:
		phase = "Error"
	}

	return strings.ToLower(string(phase))
}

func extractImageEndpointFromDockerfile(dockerfile string) string {
	// from xxx:test
	imageLine := strings.SplitN(dockerfile, "\n", 2)[0]

	dockerfile = strings.TrimSpace(imageLine)

	prefix := "from"
	if !strings.EqualFold(prefix, dockerfile[0:len(prefix)]) {
		return ""
	}

	dockerfile = dockerfile[len(prefix):]
	dockerfile = strings.TrimSpace(dockerfile)

	imageName := strings.SplitN(dockerfile, " ", 2)[0]
	if !strings.Contains(imageName, "/") {
		return "docker.io"
	}

	return strings.SplitN(imageName, "/", 2)[0]
}

func extractIDAndRegionFromEndpoint(endpoint string) (string, string) {
	// ccr-xxxxxxxx-vpc.cnc.bj.baidubce.com
	parts := vpcDomainRegexp.FindStringSubmatch(endpoint)
	if len(parts) != 3 {
		return "", ""
	}

	return parts[1], parts[2]
}

func normalizeDockerfile(dockerfile, dstHost string, instanceInfo *listers.InstanceInfo) string {
	dockerfile = strings.TrimSpace(dockerfile)

	prefix := "from"
	if !strings.EqualFold(prefix, dockerfile[0:len(prefix)]) {
		return dockerfile
	}

	dockerfile = dockerfile[len(prefix):]
	dockerfile = strings.TrimSpace(dockerfile)

	if strings.HasPrefix(dockerfile, instanceInfo.PrivateURL) {
		return "FROM " + dstHost + dockerfile[len(instanceInfo.PrivateURL):]
	}

	if strings.HasPrefix(dockerfile, instanceInfo.PublicURL) {
		return "FROM " + dstHost + dockerfile[len(instanceInfo.PublicURL):]
	}

	return "FROM " + dockerfile
}

// DockerConfigJSON represents a local docker auth config file
// for pulling images.
type DockerConfigJSON struct {
	Auths DockerConfig `json:"auths" datapolicy:"token"`
	// +optional
	HTTPHeaders map[string]string `json:"HttpHeaders,omitempty" datapolicy:"token"`
}

// DockerConfig represents the config file used by the docker CLI.
// This config that represents the credentials that should be used
// when pulling images from specific image repositories.
type DockerConfig map[string]DockerConfigEntry

// DockerConfigEntry holds the user information that grant the access to docker registry
type DockerConfigEntry struct {
	Username string `json:"username,omitempty"`
	Password string `json:"password,omitempty" datapolicy:"password"`
	Email    string `json:"email,omitempty"`
	Auth     string `json:"auth,omitempty" datapolicy:"token"`
}
