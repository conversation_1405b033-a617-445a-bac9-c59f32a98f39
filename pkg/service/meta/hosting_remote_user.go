package meta

import "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"

// CreateRemoteUserMeshCluster struct
type CreateRemoteUserMeshCluster struct {
	*Instances
	PaaSType       meta.PaaSType
	InstancesModel *meta.Instances
	ClusterModel   *meta.Cluster
}

// ToCreateRemoteUserMeshCluster 初始化某个实例下要添加的远程用户集群
func ToCreateRemoteUserMeshCluster(paaSType meta.PaaSType, instancesModel *meta.Instances, clusterModel *meta.Cluster) *CreateRemoteUserMeshCluster {
	createMeshRequest := &CreateRemoteUserMeshCluster{
		PaaSType:       paaSType,
		InstancesModel: instancesModel,
		ClusterModel:   clusterModel,
		Instances:      &Instances{},
	}
	createMeshRequest.Type = instancesModel.InstanceType
	return createMeshRequest
}

// DeleteRemoteUserMeshCluster struct
type DeleteRemoteUserMeshCluster struct {
	*Instances
	PaaSType       meta.PaaSType
	InstancesModel *meta.Instances
	ClusterModel   *meta.Cluster
}

// ToDeleteRemoteUserMeshCluster 初始化某个实例下要删除的远程用户集群
func ToDeleteRemoteUserMeshCluster(paaSType meta.PaaSType, instancesModel *meta.Instances, clusterModel *meta.Cluster) *DeleteRemoteUserMeshCluster {
	deleteMeshRequest := &DeleteRemoteUserMeshCluster{
		PaaSType:       paaSType,
		InstancesModel: instancesModel,
		ClusterModel:   clusterModel,
		Instances:      &Instances{},
	}
	deleteMeshRequest.Type = instancesModel.InstanceType
	return deleteMeshRequest
}
