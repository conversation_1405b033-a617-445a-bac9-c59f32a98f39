package meta

import (
	"os"
	"path"
)

const (
	AddonsHelm       = "templates/addons/helm"
	AddonsHelmBin    = AddonsHelm + "/bin/helm"
	AddonsHelmCharts = AddonsHelm + "/charts"
)

type HelmOption struct {
	// 表示 Helm 二进制根目录
	HelmBinPath string
	// 表示 Helm charts 根目录
	HelmChartsPath string
}

func NewOption(rootDir, helmBin, helmCharts string) *HelmOption {
	if rootDir == "" {
		rootDir, _ = os.Getwd()
	}
	if helmBin == "" {
		helmBin = path.Join(rootDir, AddonsHelmBin)
	}
	if helmCharts == "" {
		helmCharts = path.Join(rootDir, AddonsHelmCharts)
	}
	return &HelmOption{
		HelmBinPath:    helmBin,
		HelmChartsPath: helmCharts,
	}
}
