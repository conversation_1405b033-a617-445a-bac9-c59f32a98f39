package meta

type AgentCheckResult struct {
	IsExist     bool   `json:"isExist"`
	ClusterUUID string `json:"clusterId"`
}

type LogStoreListResult struct {
	Result     []LogStoreDetail `json:"result,omitempty"`     // result可以是单个对象也可以是对象数组，不是必须的
	PageNo     int              `json:"pageNo,omitempty"`     // pageNo不是必须的
	PageSize   int              `json:"pageSize,omitempty"`   // pageSize不是必须的
	TotalCount int              `json:"totalCount,omitempty"` // totalCount不是必须的
}
type LogStoreDetail struct {
	Name      string `json:"name"`      // name是必须的
	Retention int    `json:"retention"` // retention是必须的
	Region    string `json:"region"`    // region是必须的
}

type BlsListResult struct {
	Result     []BlsDetail `json:"result,omitempty"` // result可以是单个对象也可以是对象数组，不是必须的
	PageNo     int         `json:"pageNo,omitempty"`
	PageSize   int         `json:"pageSize,omitempty"`
	TotalCount int         `json:"totalCount,omitempty"`
	Status     string      `json:"status"`
}

type BlsDetail struct {
	Name       string `json:"name"`
	Retention  int    `json:"retention"`
	CreateTime string `json:"createTime"`
	UpdateTime string `json:"updateTime"`
}

type BlsCloseResult struct {
	BlsError BlsCloseError `json:"error"`
}
type BlsCloseError struct {
	ErrorMsg string `json:"errorMsg"`
}
