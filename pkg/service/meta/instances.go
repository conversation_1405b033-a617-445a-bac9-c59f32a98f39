package meta

import "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"

// DeleteMeshRequest struct
type DeleteMeshRequest struct {
	InstanceUUID             string `json:"instanceUUID"`
	InstanceType             string `json:"instanceType"`
	PaaSType                 meta.PaaSType
	IsReleaseControlPlaneBlb bool `json:"isReleaseControlPlaneBlb"`
	IsReleaseEip             bool `json:"isReleaseEip"`
}

// DeleteMeshInstance struct
type DeleteMeshInstance struct {
	PaaSType                 meta.PaaSType
	IsReleaseControlPlaneBlb bool `json:"isReleaseControlPlaneBlb"`
	IsReleaseEip             bool `json:"isReleaseEip"`
	InstanceModel            *meta.Instances
	ClusterModel             *meta.Cluster
}

// ToDeleteMeshInstance 初始化删除集群信息
func ToDeleteMeshInstance(paaSType meta.PaaSType, instancesModel *meta.Instances, clusterModel *meta.Cluster) *DeleteMeshInstance {
	return &DeleteMeshInstance{
		PaaSType:      paaSType,
		InstanceModel: instancesModel,
		ClusterModel:  clusterModel,
	}
}

// CreateMeshRequest struct
type CreateMeshRequest struct {
	*Instances
	PaaSType       meta.PaaSType
	InstancesModel *meta.Instances
	ClusterModel   *meta.Cluster
}

type Instances struct {
	Type                    string               `json:"type"`
	Region                  string               `json:"region"`
	ServiceMeshInstanceName string               `json:"serviceMeshInstanceName"`
	IstioVersion            string               `json:"istioVersion"`
	InstallationClusterId   string               `json:"installationClusterId"`
	InstallationClusterName string               `json:"installationClusterName"`
	AccountId               string               `json:"accountId"`
	InstancesUUID           string               `json:"serviceMeshInstanceId"`
	SecurityGroupId         string               `json:"securityGroupId"`
	Scope                   string               `json:"scope"`
	NetworkType             NetworkType          `json:"networkType"`
	DiscoverySelector       *DiscoverySelector   `json:"discoverySelector"`
	Monitor                 *Monitor             `json:"monitor"`
	ElasticPublicNetwork    ElasticPublicNetwork `json:"elasticPublicNetwork"`
	TraceInfo               *TraceInfo           `json:"traceInfo"`
}

// NetworkType contains the attribute of vpc
type NetworkType struct {
	VpcNetworkId string `json:"vpcNetworkId"`
	SubnetId     string `json:"subnetId"`
}

// ElasticPublicNetwork contains
type ElasticPublicNetwork struct {
	Enabled           bool                     `json:"enabled"`
	PublicNetworkType ElasticPublicNetworkType `json:"type"`
	id                string                   `json:"id"`
}

type ElasticPublicNetworkType string

const (
	// BIND 公网访问绑定现有的 EIP
	BIND ElasticPublicNetworkType = "BIND"
	// BUY 公网访问购买新的 EIP
	BUY ElasticPublicNetworkType = "Buy"
)

// DiscoverySelector contains parameters for implementing selective service discovery
type DiscoverySelector struct {
	Enabled     bool              `json:"enabled"`
	MatchLabels map[string]string `json:"matchLabels"`
}

type Monitor struct {
	Enabled   bool              `json:"enabled"`
	Instances []MonitorInstance `json:"instances"`
}

type MonitorInstance struct {
	Region string `json:"region"`
	Id     string `json:"id"`
}

// TraceInfo 链路追踪信息
type TraceInfo struct {
	TraceEnabled bool    `json:"traceEnabled"`
	SamplingRate float32 `json:"samplingRate"`
	Service      string  `json:"service"`
	Address      string  `json:"address"`
}
