package meta

type BindBlbArgs struct {
	GatewayId             string               `json:"gatewayId"`
	ServiceMeshInstanceId string               `json:"serviceMeshInstanceId"`
	NetWorkConfig         NetWorkConfig        `json:"networkConfig"`
	ElasticPublicNetwork  ElasticPublicNetwork `json:"elasticPublicNetwork"`
}

type UnBindBlbArgs struct {
	IsReleaseBlb          bool   `json:"isReleaseBlb"`
	IsReleseEip           bool   `json:"isReleaseEip"`
	BlbId                 string `json:"blbId"`
	EipId                 string `json:"eipId"`
	GatewayId             string `json:"gatewayId"`
	ServiceMeshInstanceId string `json:"instanceId"`
}

type NetWorkConfig struct {
	BlbId       string      `json:"blbId"`
	NetworkType NetworkType `json:"networkType"`
}
