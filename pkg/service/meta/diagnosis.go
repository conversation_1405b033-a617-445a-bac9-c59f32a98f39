package meta

// NamespacesResult 前后端交互返回的信息
type NamespacesResult struct {
	Namespace []Namespace `json:"result"`
}

type Namespace struct {
	// 名称
	Name string `json:"name,omitempty"`
	// 状态
	Status string `json:"status,omitempty"`
}

// 判断两个 Namespace 是否相等
func (ns Namespace) equal(ns1 Namespace) bool {
	return ns.Name == ns1.Name
}

// PodsResult 前后端交互返回的信息
type PodsResult struct {
	Pods []Pod `json:"result"`
}

// Pod 命名空间下注入 Sidecar 的 Pod
type Pod struct {
	// 名称
	Name string `json:"name,omitempty"`
	// 状态
	Status string `json:"status,omitempty"`
}

type ProxyStatus struct {
	Proxy       string
	ClusterName string
	PilotName   string
	Version     string
	CDS         string
	LDS         string
	EDS         string
	RDS         string
	ECDS        string
}

type Status struct {
	PilotName string
	SyncStatus
}

// SyncStatus is the synchronization status between <PERSON> and a given Envoy
type SyncStatus struct {
	ClusterID            string `json:"cluster_id,omitempty"`
	ProxyID              string `json:"proxy,omitempty"`
	ProxyVersion         string `json:"proxy_version,omitempty"`
	IstioVersion         string `json:"istio_version,omitempty"`
	ClusterSent          string `json:"cluster_sent,omitempty"`
	ClusterAcked         string `json:"cluster_acked,omitempty"`
	ListenerSent         string `json:"listener_sent,omitempty"`
	ListenerAcked        string `json:"listener_acked,omitempty"`
	RouteSent            string `json:"route_sent,omitempty"`
	RouteAcked           string `json:"route_acked,omitempty"`
	EndpointSent         string `json:"endpoint_sent,omitempty"`
	EndpointAcked        string `json:"endpoint_acked,omitempty"`
	ExtensionConfigSent  string `json:"extensionconfig_sent,omitempty"`
	ExtensionConfigAcked string `json:"extensionconfig_acked,omitempty"`
}

const (
	QueryDiagnosisProxyName = "proxyName"
)

type ProxyConfigRequest struct {
	Region    string `query:"region"`
	ClusterID string `query:"clusterID"`
	Namespace string `query:"namespace"`
	PodName   string `query:"podName"`
	TypeName  string `query:"typeName"`
}

type PCCluster struct {
	ServiceFqdn     string `json:"domain,omitempty"`
	Port            string `json:"port,omitempty"`
	Subset          string `json:"subset,omitempty"`
	Direction       string `json:"trafficDirection,omitempty"`
	Type            string `json:"type,omitempty"`
	DestinationRule string `json:"destinationRule,omitempty"`
}

type PCListener struct {
	Address     string `json:"address,omitempty"`
	Destination string `json:"destination,omitempty"`
	MatchRule   string `json:"matchRule,omitempty"`
	Port        string `json:"port,omitempty"`
}

type PCRoute struct {
	MatchRule   string `json:"matchRule,omitempty"`
	Name        string `json:"name,omitempty"`
	RouteDomain string `json:"routeDomain,omitempty"`
	VhostName   string `json:"vhostName,omitempty"`
}

type PCEndpoint struct {
	Endpoint     string `json:"endpoint,omitempty"`
	Status       string `json:"status,omitempty"`
	OutLierCheck string `json:"telemetryResult,omitempty"`
	Cluster      string `json:"config,omitempty"`
}

type PCSecret struct {
	EffectiveDate string `json:"effectiveDate,omitempty"`
	ExpiryDate    string `json:"expiryDate,omitempty"`
	IsValid       bool   `json:"isValid,omitempty"`
	ResourceName  string `json:"resourceName,omitempty"`
	SerialNumber  string `json:"serialNumber,omitempty"`
	Status        string `json:"status,omitempty"`
	Type          string `json:"type,omitempty"`
}

type PCBootstrap struct {
	Content string `json:"bootstrap,omitempty"`
}

type Exception struct {
	Name        string `json:"name,omitempty"`
	Level       string `json:"level,omitempty"`
	Code        string `json:"code,omitempty"`
	Description string `json:"description,omitempty"`
	Version     string `json:"version,omitempty"`
}
