package meta

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

func TestNewLabelSelectorRequirementWithMap(t *testing.T) {
	tests := []struct {
		name   string
		labels map[string]string
		op     LabelSelectorOperator
		expect []*LabelSelectorRequirement
	}{
		{
			name:   "test01-NewLabelSelectorRequirementWithMap",
			op:     LabelSelectorOpIn,
			labels: map[string]string{"user": "a"},
			expect: []*LabelSelectorRequirement{
				{
					Key:      "user",
					Operator: LabelSelectorOpIn,
					Values:   []string{"a"},
				},
			},
		},
		{
			name:   "test02-NewLabelSelectorRequirementWithMap",
			op:     LabelSelectorOpNotIn,
			labels: map[string]string{"user": "a"},
			expect: []*LabelSelectorRequirement{
				{
					Key:      "user",
					Operator: LabelSelectorOpNotIn,
					Values:   []string{"a"},
				},
			},
		},
		{
			name:   "test03-NewLabelSelectorRequirementWithMap",
			op:     LabelSelectorOpExists,
			labels: map[string]string{"user": "a"},
			expect: []*LabelSelectorRequirement{
				{
					Key:      "user",
					Operator: LabelSelectorOpExists,
					Values:   []string{"a"},
				},
			},
		},
		{
			name:   "test04-NewLabelSelectorRequirementWithMap",
			op:     LabelSelectorOpDoesNotExist,
			labels: map[string]string{"user": "a"},
			expect: []*LabelSelectorRequirement{
				{
					Key:      "user",
					Operator: LabelSelectorOpDoesNotExist,
					Values:   []string{"a"},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewLabelSelectorRequirementWithMap(tt.op, tt.labels)
			if !reflect.DeepEqual(got, tt.expect) {
				t.Errorf("got = %v, expect %v", got, tt.expect)
			}
		})
	}
}

func TestNewDiscoverySelector(t *testing.T) {
	expectDs := &DiscoverySelector{
		Enabled:     true,
		MatchLabels: map[string]string{"user": "foo"},
	}
	ds := NewDiscoverySelector(true, map[string]string{"user": "foo"})
	assert.Equal(t, ds, expectDs)
}

func TestGetDiscoverySelectorsWithMap(t *testing.T) {
	tests := []struct {
		name             string
		op               LabelSelectorOperator
		matchExpressions map[string]string
		matchLabels      map[string]string
		expect           *metav1.LabelSelector
	}{
		{
			name:             "GetDiscoverySelectorsWithMap-test01",
			op:               LabelSelectorOpIn,
			matchExpressions: map[string]string{"user": "foo"},
			matchLabels:      map[string]string{constants.MeshInstanceId: "csm-123456"},
			expect: &metav1.LabelSelector{
				MatchLabels: map[string]string{constants.MeshInstanceId: "csm-123456"},
				MatchExpressions: []metav1.LabelSelectorRequirement{
					{
						Key:      "user",
						Operator: metav1.LabelSelectorOperator(LabelSelectorOpIn),
						Values:   []string{"foo"},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetDiscoverySelectorsWithMap(tt.op, tt.matchExpressions, tt.matchLabels)
			if !reflect.DeepEqual(got, tt.expect) {
				t.Errorf("got = %v, expect %v", got, tt.expect)
			}
		})
	}
}

func TestGetDiscoverySelectors(t *testing.T) {
	matchExpressions := make(map[string][]string, 0)
	matchExpressions["user"] = []string{"test01", "test02"}

	matchLabels := make(map[string]string, 0)
	matchLabels[constants.MeshInstanceId] = "csm-123456"

	ls := &metav1.LabelSelector{
		MatchLabels: matchLabels,
		MatchExpressions: []metav1.LabelSelectorRequirement{
			{
				Key:      "user",
				Operator: metav1.LabelSelectorOperator(LabelSelectorOpIn),
				Values:   []string{"test01", "test02"},
			},
		},
	}

	tests := []struct {
		name             string
		op               LabelSelectorOperator
		matchExpressions map[string][]string
		matchLabels      map[string]string
		expect           []*metav1.LabelSelector
	}{
		{
			name:             "GetDiscoverySelectors-test01",
			op:               LabelSelectorOpIn,
			matchExpressions: matchExpressions,
			matchLabels:      matchLabels,
			expect:           []*metav1.LabelSelector{ls},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetDiscoverySelectors(tt.op, tt.matchExpressions, tt.matchLabels)
			if !reflect.DeepEqual(got, tt.expect) {
				t.Errorf("got = %v, expect %v", got, tt.expect)
			}
		})
	}
}
