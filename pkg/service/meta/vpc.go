package meta

type VPCArgs struct{}

type SubnetArgs struct {
	VpcID string `json:"vpcId"`
}

// VPCResult defines the structure of the output parameters for the ListVPC api
type VPCResult struct {
	VPCs []VPC `json:"vpcs"`
}

type VPC struct {
	VpcID string `json:"vpcId"`
	Name  string `json:"name"`
	Cidr  string `json:"cidr"`
}

// SubnetResult defines the structure of the output parameters for the ListSubnet api
type SubnetResult struct {
	Subnets []Subnet `json:"subnets"`
}

type Subnet struct {
	SubnetID string `json:"subnetId"`
	Name     string `json:"name"`
	Cidr     string `json:"cidr"`
}

type SecurityGroupArgs struct {
	VpcID string `json:"vpcId"`
}

type SecurityGroupResult struct {
	SecurityGroups []SecurityGroup `json:"securityGroups"`
}

type SecurityGroup struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Desc string `json:"desc"`
}
