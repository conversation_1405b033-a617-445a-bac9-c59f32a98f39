package meta

import v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

// MeshCluster 服务网格实例集群列表
type MeshCluster struct {
	// ClusterName 集群名称
	ClusterName string `json:"clusterName"`

	// ClusterType 集群类型
	ClusterType string `json:"clusterType"`

	// ClusterUuid 集群 uuid
	ClusterUuid string `json:"clusterUuid"`

	// Region 地域
	Region string `json:"region"`

	// Version k8s version
	Version string `json:"version"`

	// Admin 账户是否具有 admin 权限
	Admin bool `json:"Admin"`

	// istioInstalledStatus istio 集群是否安装
	IstioInstalledStatus bool `json:"istioInstalledStatus"`
}

// LabelSelectorOperator A label selector operator is the set of operators
// that can be used in a selector requirement.
type LabelSelectorOperator string

const (
	LabelSelectorOpIn           LabelSelectorOperator = "In"
	LabelSelectorOpNotIn        LabelSelectorOperator = "NotIn"
	LabelSelectorOpExists       LabelSelectorOperator = "Exists"
	LabelSelectorOpDoesNotExist LabelSelectorOperator = "DoesNotExist"
)

type LabelSelectorRequirement struct {
	Key string `json:"key"`
	// Valid operators are In, NotIn, Exists and DoesNotExist.
	Operator LabelSelectorOperator `json:"operator"`
	Values   []string              `json:"values"`
}

func NewLabelSelectorRequirementWithValue(key string, op LabelSelectorOperator, values []string) *LabelSelectorRequirement {
	return &LabelSelectorRequirement{
		Key:      key,
		Operator: op,
		Values:   values,
	}
}

func NewLabelSelectorRequirementWithMapString(op LabelSelectorOperator, data map[string][]string) []*LabelSelectorRequirement {
	labelSelectorRequirement := make([]*LabelSelectorRequirement, 0)
	for k, v := range data {
		tmp := NewLabelSelectorRequirementWithValue(k, op, v)
		labelSelectorRequirement = append(labelSelectorRequirement, tmp)
	}
	return labelSelectorRequirement
}

func NewLabelSelectorRequirementWithMap(op LabelSelectorOperator, data map[string]string) []*LabelSelectorRequirement {
	value := make(map[string][]string, 0)
	for k, v := range data {
		value[k] = []string{v}
	}

	return NewLabelSelectorRequirementWithMapString(op, value)
}

func NewDiscoverySelector(enabled bool, matchLabels map[string]string) *DiscoverySelector {
	ds := &DiscoverySelector{
		Enabled:     enabled,
		MatchLabels: matchLabels,
	}
	return ds
}

func GetDiscoverySelectors(op LabelSelectorOperator, matchExpressions map[string][]string,
	matchLabels map[string]string) []*v1.LabelSelector {
	lss := make([]*v1.LabelSelector, 0)
	ls := &v1.LabelSelector{}
	ls.MatchLabels = matchLabels

	lsrs := make([]v1.LabelSelectorRequirement, 0)
	for k, v := range matchExpressions {
		tmp := v1.LabelSelectorRequirement{
			Key:      k,
			Operator: v1.LabelSelectorOperator(op),
			Values:   v,
		}
		lsrs = append(lsrs, tmp)
	}
	ls.MatchExpressions = lsrs

	lss = append(lss, ls)
	return lss
}

func GetDiscoverySelectorsWithMap(op LabelSelectorOperator, matchExpressions map[string]string,
	matchLabels map[string]string) *v1.LabelSelector {
	ls := &v1.LabelSelector{}
	ls.MatchLabels = matchLabels

	lsr := make([]v1.LabelSelectorRequirement, 0)
	for k, v := range matchExpressions {
		tmp := v1.LabelSelectorRequirement{
			Key:      k,
			Operator: v1.LabelSelectorOperator(op),
			Values:   []string{v},
		}
		lsr = append(lsr, tmp)
	}
	ls.MatchExpressions = lsr

	return ls
}
