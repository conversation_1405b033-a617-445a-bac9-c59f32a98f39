package gateway

import (
	"strconv"
	"strings"

	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	ctxContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// TODO: 放到conf文件里
const (
	LimitMemoryQuota string = "4096Mi"
	LimitCPUQuota    string = "2000m"
	LimitEniNum      string = "1"

	DefaultMemoryQuota string = "256Mi"
	DefaultCPUQuota    string = "200m"
	DefaultEniNum      string = "1"
)

type ResourceType string

const (
	// HPAResourceType Horizontal Pod Autoscaling Crd Type
	HPAResourceType ResourceType = "HPA"
	// GwVsResourceType Gateway and VirtualService Crd Type
	GwVsResourceType ResourceType = "GwVs"
	// TelemetryResourceType Telemetry Crd Type
	TelemetryResourceType ResourceType = "Telemetry"
)

// Quota 资源配额
type Quota struct {
	Memory string
	Cpu    string
	EniNum string
}

// ResourcesConf 网关代理的资源配额
type ResourcesConf struct {
	Requests *Quota
	Limits   *Quota
}

// IopGatewayParseParams 解析 iop-ingressgateway 模板参数
type IopGatewayParseParams struct {
	// 镜像标签
	Tag string
	// 镜像仓库地址
	Hub string
	// 网关名称
	GatewayName string
	// 网关安装命名空间
	Namespace string
	// 网关代理副本数
	Replicas int16
	// 网关代理资源配额
	Resources *ResourcesConf
	// 控制面版本
	IstioVersion string
	// VPC所属网段
	VpcCidr string
	// VPC子网ID
	SubnetID string
	// 资源账户ID
	AccountID string
	// 安全组ID，多个用逗号分隔
	SecurityGroupIDs string
	// BLB ID
	BlbID string
	// BLB绑定的EIP
	BindedEip string
	// 是否仅供内网使用
	OnlyInternalAccess string
	// 网关CPromInstance ID
	CPromInstanceID string
	// 网关CPromAgent ID
	CPromAgentID string
	// 网关CPromScrapeJob ID
	CPromScrapeJobID string
	// TLS加速注解
	TLSAccelerationAnnotation string
}

func newResourceQuota(memory, cpu, eni string) *Quota {
	return &Quota{Memory: memory, Cpu: cpu, EniNum: eni}
}

func limitedResourceQuota() *Quota {
	if viper.GetBool("local.dev") {
		return newResourceQuota(LimitMemoryQuota, LimitCPUQuota, LimitEniNum)
	}
	return newResourceQuota(LimitMemoryQuota, LimitCPUQuota, LimitEniNum)
}

func defaultResourceQuota() *Quota {
	return newResourceQuota(DefaultMemoryQuota, DefaultCPUQuota, DefaultEniNum)
}

func parseResourceQuota(ctx ctxContext.CsmContext, resourceQuota string) (req *Quota, lmt *Quota) {
	resourceQuota = strings.ToLower(resourceQuota)
	splitIdx := strings.Index(resourceQuota, "c")
	if splitIdx == -1 {
		ctx.CsmLogger().Warn("invalid resource quota, return default value")
		return defaultResourceQuota(), limitedResourceQuota()
	}
	cpuQuota, err := strconv.Atoi(resourceQuota[:splitIdx])
	if err != nil {
		ctx.CsmLogger().Warn("fail to convert cpu quota, return default value")
		return defaultResourceQuota(), limitedResourceQuota()
	}
	memoryQuota, err := strconv.Atoi(resourceQuota[splitIdx+1 : len(resourceQuota)-1])
	if err != nil {
		ctx.CsmLogger().Warn("fail to convert memory quota, return default value")
		return defaultResourceQuota(), limitedResourceQuota()
	}
	// 资源有限用于本地测试
	return newResourceQuota(strconv.Itoa(memoryQuota*128)+"Mi", strconv.Itoa(cpuQuota*100)+"m", DefaultEniNum),
		newResourceQuota(strconv.Itoa(memoryQuota*1024)+"Mi", strconv.Itoa(cpuQuota*1000)+"m", DefaultEniNum)
}

// NewIopGatewayParseParams init
func NewIopGatewayParseParams(ctx ctxContext.CsmContext, gw *meta.GatewayModel) *IopGatewayParseParams {
	reqQ, limitQ := parseResourceQuota(ctx, gw.ResourceQuota)
	params := &IopGatewayParseParams{
		Tag:                gw.IstioVersion,
		Hub:                viper.GetString("istio.hosting.iop.hub"),
		GatewayName:        gw.GatewayName,
		Namespace:          gw.Namespace,
		Replicas:           gw.Replicas,
		IstioVersion:       gw.IstioVersion,
		SubnetID:           gw.SubnetId,
		AccountID:          gw.AccountId,
		VpcCidr:            "",
		SecurityGroupIDs:   "",
		BlbID:              "",
		BindedEip:          "",
		CPromInstanceID:    gw.MonitorInstanceID,
		CPromAgentID:       gw.MonitorAgentID,
		CPromScrapeJobID:   gw.MonitorJobID,
		OnlyInternalAccess: "true",
		Resources: &ResourcesConf{
			Requests: reqQ,
			Limits:   limitQ,
		},
		TLSAccelerationAnnotation: "",
	}

	return params
}
