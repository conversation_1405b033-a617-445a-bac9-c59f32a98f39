// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// DeployGwVs mocks base method.
func (m *MockServiceInterface) DeployGwVs(ctx context.CsmContext, gatewayUUID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeployGwVs", ctx, gatewayUUID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeployGwVs indicates an expected call of DeployGwVs.
func (mr *MockServiceInterfaceMockRecorder) DeployGwVs(ctx, gatewayUUID, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeployGwVs", reflect.TypeOf((*MockServiceInterface)(nil).DeployGwVs), ctx, gatewayUUID, namespace)
}

// DeployHPA mocks base method.
func (m *MockServiceInterface) DeployHPA(ctx context.CsmContext, hpaConf *meta.HpaConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeployHPA", ctx, hpaConf)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeployHPA indicates an expected call of DeployHPA.
func (mr *MockServiceInterfaceMockRecorder) DeployHPA(ctx, hpaConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeployHPA", reflect.TypeOf((*MockServiceInterface)(nil).DeployHPA), ctx, hpaConf)
}

// DeployIngressController mocks base method.
func (m *MockServiceInterface) DeployIngressController(ctx context.CsmContext, gateway *meta.GatewayModel, ingressParam *meta.IngressParam) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeployIngressController", ctx, gateway, ingressParam)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeployIngressController indicates an expected call of DeployIngressController.
func (mr *MockServiceInterfaceMockRecorder) DeployIngressController(ctx, gateway, ingressParam interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeployIngressController", reflect.TypeOf((*MockServiceInterface)(nil).DeployIngressController), ctx, gateway, ingressParam)
}

// DeployTelemetry mocks base method.
func (m *MockServiceInterface) DeployTelemetry(ctx context.CsmContext, blsTaskID, gatewayUUID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeployTelemetry", ctx, blsTaskID, gatewayUUID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeployTelemetry indicates an expected call of DeployTelemetry.
func (mr *MockServiceInterfaceMockRecorder) DeployTelemetry(ctx, blsTaskID, gatewayUUID, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeployTelemetry", reflect.TypeOf((*MockServiceInterface)(nil).DeployTelemetry), ctx, blsTaskID, gatewayUUID, namespace)
}

// DisableTLSAcceleration mocks base method.
func (m *MockServiceInterface) DisableTLSAcceleration(ctx context.CsmContext, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableTLSAcceleration", ctx, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DisableTLSAcceleration indicates an expected call of DisableTLSAcceleration.
func (mr *MockServiceInterfaceMockRecorder) DisableTLSAcceleration(ctx, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableTLSAcceleration", reflect.TypeOf((*MockServiceInterface)(nil).DisableTLSAcceleration), ctx, namespace)
}

// EnableTLSAcceleration mocks base method.
func (m *MockServiceInterface) EnableTLSAcceleration(ctx context.CsmContext, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableTLSAcceleration", ctx, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// EnableTLSAcceleration indicates an expected call of EnableTLSAcceleration.
func (mr *MockServiceInterfaceMockRecorder) EnableTLSAcceleration(ctx, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableTLSAcceleration", reflect.TypeOf((*MockServiceInterface)(nil).EnableTLSAcceleration), ctx, namespace)
}

// Init mocks base method.
func (m *MockServiceInterface) Init(ctx context.CsmContext, region, clusterId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Init", ctx, region, clusterId)
	ret0, _ := ret[0].(error)
	return ret0
}

// Init indicates an expected call of Init.
func (mr *MockServiceInterfaceMockRecorder) Init(ctx, region, clusterId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockServiceInterface)(nil).Init), ctx, region, clusterId)
}

// InstallCCEIngressGateway mocks base method.
func (m *MockServiceInterface) InstallCCEIngressGateway(ctx context.CsmContext, gateway *meta.GatewayModel, blbConf *meta.BlbConf, tlsAccConf *meta.TLSAccConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallCCEIngressGateway", ctx, gateway, blbConf, tlsAccConf)
	ret0, _ := ret[0].(error)
	return ret0
}

// InstallCCEIngressGateway indicates an expected call of InstallCCEIngressGateway.
func (mr *MockServiceInterfaceMockRecorder) InstallCCEIngressGateway(ctx, gateway, blbConf, tlsAccConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallCCEIngressGateway", reflect.TypeOf((*MockServiceInterface)(nil).InstallCCEIngressGateway), ctx, gateway, blbConf, tlsAccConf)
}

// UnDeployIngressController mocks base method.
func (m *MockServiceInterface) UnDeployIngressController(ctx context.CsmContext, gateway *meta.GatewayModel, ingressParam *meta.IngressParam) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnDeployIngressController", ctx, gateway, ingressParam)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnDeployIngressController indicates an expected call of UnDeployIngressController.
func (mr *MockServiceInterfaceMockRecorder) UnDeployIngressController(ctx, gateway, ingressParam interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnDeployIngressController", reflect.TypeOf((*MockServiceInterface)(nil).UnDeployIngressController), ctx, gateway, ingressParam)
}

// UndeployGwVs mocks base method.
func (m *MockServiceInterface) UndeployGwVs(ctx context.CsmContext, gatewayUUID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UndeployGwVs", ctx, gatewayUUID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// UndeployGwVs indicates an expected call of UndeployGwVs.
func (mr *MockServiceInterfaceMockRecorder) UndeployGwVs(ctx, gatewayUUID, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UndeployGwVs", reflect.TypeOf((*MockServiceInterface)(nil).UndeployGwVs), ctx, gatewayUUID, namespace)
}

// UndeployHPA mocks base method.
func (m *MockServiceInterface) UndeployHPA(ctx context.CsmContext, hpaConf *meta.HpaConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UndeployHPA", ctx, hpaConf)
	ret0, _ := ret[0].(error)
	return ret0
}

// UndeployHPA indicates an expected call of UndeployHPA.
func (mr *MockServiceInterfaceMockRecorder) UndeployHPA(ctx, hpaConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UndeployHPA", reflect.TypeOf((*MockServiceInterface)(nil).UndeployHPA), ctx, hpaConf)
}

// UndeployTelemetry mocks base method.
func (m *MockServiceInterface) UndeployTelemetry(ctx context.CsmContext, gatewayUUID, namespace string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UndeployTelemetry", ctx, gatewayUUID, namespace)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UndeployTelemetry indicates an expected call of UndeployTelemetry.
func (mr *MockServiceInterfaceMockRecorder) UndeployTelemetry(ctx, gatewayUUID, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UndeployTelemetry", reflect.TypeOf((*MockServiceInterface)(nil).UndeployTelemetry), ctx, gatewayUUID, namespace)
}

// UninstallCCEIngressGateway mocks base method.
func (m *MockServiceInterface) UninstallCCEIngressGateway(ctx context.CsmContext, gateway *meta.GatewayModel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UninstallCCEIngressGateway", ctx, gateway)
	ret0, _ := ret[0].(error)
	return ret0
}

// UninstallCCEIngressGateway indicates an expected call of UninstallCCEIngressGateway.
func (mr *MockServiceInterfaceMockRecorder) UninstallCCEIngressGateway(ctx, gateway interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UninstallCCEIngressGateway", reflect.TypeOf((*MockServiceInterface)(nil).UninstallCCEIngressGateway), ctx, gateway)
}

// UpdateIngressAccess mocks base method.
func (m *MockServiceInterface) UpdateIngressAccess(ctx context.CsmContext, clusterRoleName string, enable bool, remoteClusters []meta.Cluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateIngressAccess", ctx, clusterRoleName, enable, remoteClusters)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateIngressAccess indicates an expected call of UpdateIngressAccess.
func (mr *MockServiceInterfaceMockRecorder) UpdateIngressAccess(ctx, clusterRoleName, enable, remoteClusters interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateIngressAccess", reflect.TypeOf((*MockServiceInterface)(nil).UpdateIngressAccess), ctx, clusterRoleName, enable, remoteClusters)
}

// UpdateIngressInfoInAnnotation mocks base method.
func (m *MockServiceInterface) UpdateIngressInfoInAnnotation(ctx context.CsmContext, namespace string, ingressParam *meta.IngressParam) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateIngressInfoInAnnotation", ctx, namespace, ingressParam)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateIngressInfoInAnnotation indicates an expected call of UpdateIngressInfoInAnnotation.
func (mr *MockServiceInterfaceMockRecorder) UpdateIngressInfoInAnnotation(ctx, namespace, ingressParam interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateIngressInfoInAnnotation", reflect.TypeOf((*MockServiceInterface)(nil).UpdateIngressInfoInAnnotation), ctx, namespace, ingressParam)
}

// UpdateMonitorInfoInAnnotation mocks base method.
func (m *MockServiceInterface) UpdateMonitorInfoInAnnotation(ctx context.CsmContext, gatewayModel *meta.GatewayModel, cpromInfo *meta.CPromGatewayInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMonitorInfoInAnnotation", ctx, gatewayModel, cpromInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMonitorInfoInAnnotation indicates an expected call of UpdateMonitorInfoInAnnotation.
func (mr *MockServiceInterfaceMockRecorder) UpdateMonitorInfoInAnnotation(ctx, gatewayModel, cpromInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMonitorInfoInAnnotation", reflect.TypeOf((*MockServiceInterface)(nil).UpdateMonitorInfoInAnnotation), ctx, gatewayModel, cpromInfo)
}

// UpdateResourceQuota mocks base method.
func (m *MockServiceInterface) UpdateResourceQuota(ctx context.CsmContext, resourceQuota, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResourceQuota", ctx, resourceQuota, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateResourceQuota indicates an expected call of UpdateResourceQuota.
func (mr *MockServiceInterfaceMockRecorder) UpdateResourceQuota(ctx, resourceQuota, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResourceQuota", reflect.TypeOf((*MockServiceInterface)(nil).UpdateResourceQuota), ctx, resourceQuota, namespace)
}
