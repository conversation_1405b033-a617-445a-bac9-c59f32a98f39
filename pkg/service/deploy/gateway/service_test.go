package gateway

import (
	"context"
	"path"
	"runtime"
	"testing"

	"github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/baidubce/bce-sdk-go/services/vpc"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"istio.io/api/networking/v1beta1"
	netv1beta1 "istio.io/client-go/pkg/apis/networking/v1beta1"
	"istio.io/client-go/pkg/apis/telemetry/v1alpha1"
	v1 "k8s.io/api/apps/v1"
	v12 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	vpcModelMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/vpc/mock"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	cceServiceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

func TestInit(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-InitK8sClient",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		service := &Deployer{
			cceService: mockCceService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			mockCtx := csmContext.MockNewCsmContext()
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			err := service.Init(mockCtx, "bj", "abc")
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func buildGatewayModel() *meta.GatewayModel {
	return &meta.GatewayModel{
		ClusterUUID:   "clu-1",
		GatewayName:   "gw-1",
		GatewayUUID:   "unit-test",
		Namespace:     "ns-1",
		Replicas:      2,
		ResourceQuota: "2c4g",
		Region:        "bj",
		AccountId:     "123",
		VpcNetworkId:  "vpc-1",
		SubnetId:      "sb-1",
	}
}

func TestInstallCCEIngressGateway(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// TODO: 这里可以做下优化
	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name           string
		vpcDetail      vpc.GetVPCDetailResult
		securityGroups api.ListSecurityGroupResult
		blbConf        *meta.BlbConf
		tlsAccConf     *meta.TLSAccConf
		expectErr      error
	}{
		{
			name: "correct-InstallGateway",
			vpcDetail: vpc.GetVPCDetailResult{
				VPC: vpc.ShowVPCModel{
					Cidr: "abc",
				},
			},
			securityGroups: api.ListSecurityGroupResult{
				SecurityGroups: []api.SecurityGroupModel{
					{
						Id:   "123",
						Name: "default",
					},
				},
			},
			blbConf: &meta.BlbConf{
				BlbID: "lb-test",
				EipConf: &meta.EipConf{
					Enabled: true,
					IP:      "*******",
				},
			},
			tlsAccConf: &meta.TLSAccConf{
				Enabled: true,
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockVpcModel := vpcModelMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		service := &Deployer{
			client:     kube.NewFakeClient(),
			vpcModel:   mockVpcModel,
			cceService: mockCceService,
		}
		service.configPath = testConfigPath
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			mockVpcModel.EXPECT().GetVPCDetail(mockCtx, gomock.Any(), gomock.Any()).Return(&testInfo.vpcDetail, nil)
			mockVpcModel.EXPECT().ListSecurityGroup(mockCtx, gomock.Any(), gomock.Any()).Return(&testInfo.securityGroups, nil)

			testGateway := buildGatewayModel()
			err := service.InstallCCEIngressGateway(mockCtx, testGateway, testInfo.blbConf, testInfo.tlsAccConf)
			/*if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}*/
			if testInfo.expectErr != nil {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestUninstallCCEIngressGateway(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name           string
		vpcDetail      vpc.GetVPCDetailResult
		securityGroups api.ListSecurityGroupResult
		expectErr      error
	}{
		{
			name: "correct-UninstallGateway",
			vpcDetail: vpc.GetVPCDetailResult{
				VPC: vpc.ShowVPCModel{
					Cidr: "abc",
				},
			},
			securityGroups: api.ListSecurityGroupResult{
				SecurityGroups: []api.SecurityGroupModel{
					{
						Id:   "123",
						Name: "default",
					},
				},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockVpcModel := vpcModelMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		service := &Deployer{
			client:     kube.NewFakeClient(),
			vpcModel:   mockVpcModel,
			cceService: mockCceService,
		}
		service.configPath = testConfigPath
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			mockVpcModel.EXPECT().GetVPCDetail(mockCtx, gomock.Any(), gomock.Any()).Return(&testInfo.vpcDetail, nil)
			mockVpcModel.EXPECT().ListSecurityGroup(mockCtx, gomock.Any(), gomock.Any()).Return(&testInfo.securityGroups, nil)

			testGateway := buildGatewayModel()
			err := service.UninstallCCEIngressGateway(mockCtx, testGateway)
			/*if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}*/
			if testInfo.expectErr != nil {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestDeployHPA(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name      string
		hpaConf   meta.HpaConf
		gateway   *meta.GatewayModel
		expectErr error
	}{
		{
			name: "correct-DeployHPA",
			hpaConf: meta.HpaConf{
				Enabled:     true,
				MinReplicas: 1,
				MaxReplicas: 3,
				Namespace:   "ns-1",
				GatewayUUID: "unit-test",
			},
			gateway:   buildGatewayModel(),
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockCtx := csmContext.MockNewCsmContext()
		service := &Deployer{
			client: kube.NewFakeClient(),
			params: NewIopGatewayParseParams(mockCtx, testInfo.gateway),
		}
		t.Run(testInfo.name, func(t *testing.T) {
			service.configPath = testConfigPath
			mockCtx := csmContext.MockNewCsmContext()
			err := service.DeployHPA(mockCtx, &testInfo.hpaConf)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
			_ = file.RemoveFile(service.hpaYamlPath)
		})
	}
}

func TestUndeployHPA(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name      string
		hpaConf   meta.HpaConf
		gateway   *meta.GatewayModel
		expectErr error
	}{
		{
			name: "correct-DeployHPA",
			hpaConf: meta.HpaConf{
				Enabled:     true,
				MinReplicas: 1,
				MaxReplicas: 3,
				Namespace:   "ns-1",
				GatewayUUID: "unit-test",
			},
			gateway:   buildGatewayModel(),
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockCtx := csmContext.MockNewCsmContext()
		service := &Deployer{
			client: kube.NewFakeClient(),
			params: NewIopGatewayParseParams(mockCtx, testInfo.gateway),
		}
		t.Run(testInfo.name, func(t *testing.T) {
			service.configPath = testConfigPath
			mockCtx := csmContext.MockNewCsmContext()
			err := service.UndeployHPA(mockCtx, &testInfo.hpaConf)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestDeployGwVs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-DeployGwVs",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		service := &Deployer{
			client: kube.NewFakeClient(),
		}
		t.Run(testInfo.name, func(t *testing.T) {
			service.configPath = testConfigPath
			mockCtx := csmContext.MockNewCsmContext()
			err := service.DeployGwVs(mockCtx, "gw-123", "ns-1")
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func buildGatewaySecret() *v12.Secret {
	return &v12.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "cert-0n3pjjjue21g-helloworld",
			Namespace: "ns-1",
		},
	}
}

func buildGatewayCrd80443() *netv1beta1.Gateway {
	return &netv1beta1.Gateway{
		ObjectMeta: metav1.ObjectMeta{
			Labels:    map[string]string{"app": "istio-ingressgateway"},
			Name:      constants.GwCrdName,
			Namespace: "ns-1",
		},
		Spec: v1beta1.Gateway{
			Servers: []*v1beta1.Server{
				{
					Hosts: []string{"*"},
					Port: &v1beta1.Port{
						Name:     "http_1672317078",
						Number:   80,
						Protocol: "HTTP",
					},
					Tls: &v1beta1.ServerTLSSettings{
						HttpsRedirect: true,
					},
				},
				{
					Hosts: []string{"*"},
					Port: &v1beta1.Port{
						Name:     "https_1672317078",
						Number:   443,
						Protocol: "HTTPS",
					},
					Tls: &v1beta1.ServerTLSSettings{
						Mode:           v1beta1.ServerTLSSettings_SIMPLE,
						CredentialName: "cert-0n3pjjjue21g-helloworld",
					},
				},
			},
		},
	}
}

func TestUndeployGwVs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name       string
		testSecret *v12.Secret
		expectErr  error
	}{
		{
			name:       "correct-UndeployGwVs",
			testSecret: buildGatewaySecret(),
			expectErr:  nil,
		},
	}
	for _, testInfo := range testInfos {
		service := &Deployer{
			client: kube.NewFakeClient(),
		}
		if testInfo.testSecret != nil {
			_, _ = service.client.Kube().CoreV1().Secrets("ns-1").Create(
				context.TODO(), testInfo.testSecret, metav1.CreateOptions{})
		}

		t.Run(testInfo.name, func(t *testing.T) {
			service.configPath = testConfigPath
			mockCtx := csmContext.MockNewCsmContext()
			err := service.UndeployGwVs(mockCtx, "gw-123", "ns-1")
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestDeployTelemetry(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name      string
		testDs    *v1.DaemonSet
		expectErr error
	}{
		{
			name: "correct-DeployTelemetry",
			testDs: &v1.DaemonSet{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.LogBeatName,
					Namespace: constants.LogBeatNamespace,
				},
				Spec: v1.DaemonSetSpec{
					Template: v12.PodTemplateSpec{
						Spec: v12.PodSpec{
							Containers: []v12.Container{
								{
									Env: []v12.EnvVar{
										{
											Name: constants.LogBeatTaskEnv,
										},
									},
								},
							},
						},
					},
				},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		service := &Deployer{
			client: kube.NewFakeClient(),
		}
		t.Run(testInfo.name, func(t *testing.T) {
			service.configPath = testConfigPath
			mockCtx := csmContext.MockNewCsmContext()
			if testInfo.testDs != nil {
				_, _ = service.client.Kube().AppsV1().DaemonSets(constants.LogBeatNamespace).Create(context.TODO(),
					testInfo.testDs, metav1.CreateOptions{})
			}
			err := service.DeployTelemetry(mockCtx, "task-123", "gw-123", "ns-1")
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
				logBeatDs, _ := service.client.Kube().AppsV1().DaemonSets(constants.LogBeatNamespace).Get(
					context.TODO(), constants.LogBeatName, metav1.GetOptions{})
				assert.Equal(t, "task-123", logBeatDs.Spec.Template.Spec.Containers[0].Env[0].Value)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestUndeployTelemetry(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name      string
		testDs    *v1.DaemonSet
		testTm    *v1alpha1.Telemetry
		expectErr error
	}{
		{
			name: "correct-DeployTelemetry",
			testDs: &v1.DaemonSet{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.LogBeatName,
					Namespace: constants.LogBeatNamespace,
				},
				Spec: v1.DaemonSetSpec{
					Template: v12.PodTemplateSpec{
						Spec: v12.PodSpec{
							Containers: []v12.Container{
								{
									Env: []v12.EnvVar{
										{
											Name:  constants.LogBeatTaskEnv,
											Value: "task-123,task-456",
										},
									},
								},
							},
						},
					},
				},
			},
			testTm: &v1alpha1.Telemetry{
				ObjectMeta: metav1.ObjectMeta{
					Labels:    map[string]string{constants.GwTelemetryLabelKey: "task-123"},
					Name:      constants.GwTelemetryCrdName,
					Namespace: "ns-1",
				},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		service := &Deployer{
			client: kube.NewFakeClient(),
		}
		t.Run(testInfo.name, func(t *testing.T) {
			service.configPath = testConfigPath
			mockCtx := csmContext.MockNewCsmContext()
			if testInfo.testDs != nil {
				_, _ = service.client.Kube().AppsV1().DaemonSets(constants.LogBeatNamespace).Create(context.TODO(),
					testInfo.testDs, metav1.CreateOptions{})
			}
			if testInfo.testTm != nil {
				_, _ = service.client.Istio().TelemetryV1alpha1().Telemetries("ns-1").Create(context.TODO(),
					testInfo.testTm, metav1.CreateOptions{})
			}

			taskID, err := service.UndeployTelemetry(mockCtx, "gw-123", "ns-1")
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
				logBeatDs, _ := service.client.Kube().AppsV1().DaemonSets(constants.LogBeatNamespace).Get(
					context.TODO(), constants.LogBeatName, metav1.GetOptions{})
				assert.Equal(t, "task-456", logBeatDs.Spec.Template.Spec.Containers[0].Env[0].Value)
				assert.Equal(t, "task-123", taskID)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestUpdateMonitorInfoInAnnotation(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name        string
		cpromInfo   *meta.CPromGatewayInfo
		testService *v12.Service
		expectErr   error
	}{
		{
			name: "correct-DeployTelemetry",
			cpromInfo: &meta.CPromGatewayInfo{
				ID:          "id",
				AgentID:     "agent-id",
				ScrapeJobID: "scrape-id",
			},
			testService: &v12.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IngressGatewayResourceName,
					Namespace: constants.LogBeatNamespace,
					Annotations: map[string]string{
						"constants.CsmCPromInstanceID": "is1",
					},
				},
				Spec: v12.ServiceSpec{},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {

		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		service := &Deployer{
			params: &IopGatewayParseParams{
				Namespace: constants.LogBeatNamespace,
			},
			cceService: mockCceService,
			client:     kube.NewFakeClient(),
		}
		t.Run(testInfo.name, func(t *testing.T) {
			service.configPath = testConfigPath
			mockCtx := csmContext.MockNewCsmContext()
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(service.client, nil)

			_, _ = service.client.Kube().CoreV1().Services(constants.LogBeatNamespace).Create(context.TODO(),
				testInfo.testService, metav1.CreateOptions{})

			gatewayModel := buildGatewayModel()
			gatewayModel.Namespace = constants.LogBeatNamespace
			err := service.UpdateMonitorInfoInAnnotation(mockCtx, gatewayModel, testInfo.cpromInfo)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestEnableTLSAcceleration(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name       string
		deployment *v1.Deployment
		expectErr  error
	}{
		{
			name: "correct-EnableTLSAcceleration",
			deployment: &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IngressGatewayResourceName,
					Namespace: "ns-1",
				},
				Spec: v1.DeploymentSpec{
					Template: v12.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Annotations: map[string]string{},
						},
					},
				},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		service := &Deployer{
			client: kube.NewFakeClient(),
		}
		if testInfo.deployment != nil {
			_, _ = service.client.Kube().AppsV1().Deployments("ns-1").Create(
				context.TODO(), testInfo.deployment, metav1.CreateOptions{})
		}

		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			err := service.EnableTLSAcceleration(mockCtx, "ns-1")
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestDisableTLSAcceleration(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name       string
		deployment *v1.Deployment
		expectErr  error
	}{
		{
			name: "correct-DisableTLSAcceleration",
			deployment: &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IngressGatewayResourceName,
					Namespace: "ns-1",
				},
				Spec: v1.DeploymentSpec{
					Template: v12.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Annotations: map[string]string{constants.TLSAccelerationAnnotationKey: constants.TLSAccelerationAnnotationVal},
						},
					},
				},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		service := &Deployer{
			client: kube.NewFakeClient(),
		}
		if testInfo.deployment != nil {
			_, _ = service.client.Kube().AppsV1().Deployments("ns-1").Create(
				context.TODO(), testInfo.deployment, metav1.CreateOptions{})
		}

		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			err := service.DisableTLSAcceleration(mockCtx, "ns-1")
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestDeployer_UpdateResourceQuota(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name       string
		deployment *v1.Deployment
		wantErr    bool
	}{
		{
			name: "test-updateResourceQuota",
			deployment: &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IngressGatewayResourceName,
					Namespace: "ns-1",
				},
				Spec: v1.DeploymentSpec{
					Template: v12.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{},
						Spec: v12.PodSpec{
							Containers: []v12.Container{
								{
									Resources: v12.ResourceRequirements{
										Limits: v12.ResourceList{},
									},
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "test-updateResourceQuota-error",
			deployment: &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IngressGatewayResourceName,
					Namespace: "ns-2",
				},
				Spec: v1.DeploymentSpec{
					Template: v12.PodTemplateSpec{
						Spec: v12.PodSpec{
							Containers: []v12.Container{
								{
									Resources: v12.ResourceRequirements{
										Limits: v12.ResourceList{},
									},
								},
							},
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			service := &Deployer{
				client: kube.NewFakeClient(),
			}
			_, _ = service.client.Kube().AppsV1().Deployments("ns-1").Create(
				context.TODO(), tt.deployment, metav1.CreateOptions{})

			if err := service.UpdateResourceQuota(mockCtx, "1C2G", "ns-1"); (err != nil) != tt.wantErr {
				t.Errorf("UpdateResourceQuota() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDeployer_UpdateIngressInfoInAnnotation(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name         string
		ingressParam *meta.IngressParam
		testService  *v12.Service
		expectErr    error
	}{
		{
			name: "correct-DeployHigressController",
			ingressParam: &meta.IngressParam{
				Enabled: true,
				CLusterList: []meta.RemoteCluster{
					{
						ClusterID: "clu-1",
						Region:    "gz",
						Enabled:   true,
					},
					{
						ClusterID: "clu-2",
						Region:    "bj",
						Enabled:   false,
					},
				},
			},
			testService: &v12.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.ServiceNameHigress,
					Namespace: constants.LogBeatNamespace,
				},
				Spec: v12.ServiceSpec{},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		service := &Deployer{
			params: &IopGatewayParseParams{
				Namespace: constants.LogBeatNamespace,
			},
			cceService: mockCceService,
			client:     kube.NewFakeClient(),
		}
		t.Run(testInfo.name, func(t *testing.T) {
			service.configPath = testConfigPath
			mockCtx := csmContext.MockNewCsmContext()
			//mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(service.client, nil)

			_, _ = service.client.Kube().CoreV1().Services(constants.LogBeatNamespace).Create(context.TODO(),
				testInfo.testService, metav1.CreateOptions{})

			err := service.UpdateIngressInfoInAnnotation(mockCtx, constants.LogBeatNamespace, testInfo.ingressParam)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestDeployIngressController(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// TODO: 这里可以做下优化
	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name           string
		ingressParam   *meta.IngressParam
		vpcDetail      vpc.GetVPCDetailResult
		testService    *v12.Service
		testDeploy     *v1.Deployment
		istioConfigMap *v12.ConfigMap
		istioDeploy    *v1.Deployment
		securityGroups api.ListSecurityGroupResult
		expectErr      error
	}{
		{
			name: "correct-DeployIngressController",
			ingressParam: &meta.IngressParam{
				Enabled: true,
				CLusterList: []meta.RemoteCluster{
					{
						ClusterID: "clu-1",
						Region:    "gz",
						Enabled:   true,
					},
					{
						ClusterID: "clu-2",
						Region:    "bj",
						Enabled:   false,
					},
				},
			},
			testService: &v12.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.ServiceNameHigress,
					Namespace: constants.LogBeatNamespace,
				},
				Spec: v12.ServiceSpec{},
			},
			testDeploy: &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.ServiceNameHigress,
					Namespace: constants.LogBeatNamespace,
				},
				Spec: v1.DeploymentSpec{
					Template: v12.PodTemplateSpec{
						Spec: v12.PodSpec{
							Containers: []v12.Container{{
								Name:  "higress",
								Image: "image",
							}},
						},
					},
					Replicas: csm.Int32(1),
				},
				Status: v1.DeploymentStatus{
					AvailableReplicas: 1,
					Replicas:          1,
				},
			},
			istioConfigMap: &v12.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IstioConfimapName,
					Namespace: constants.LogBeatNamespace,
				},
				Data: map[string]string{
					constants.IstioConfigMapMeshName: "default",
				},
			},
			istioDeploy: &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IstiodDeploymentName,
					Namespace: constants.LogBeatNamespace,
				},
				Spec: v1.DeploymentSpec{
					Template: v12.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Annotations: map[string]string{
								"test": "1",
							},
						},
					},
					Replicas: csm.Int32(1),
				},
			},
			vpcDetail: vpc.GetVPCDetailResult{
				VPC: vpc.ShowVPCModel{
					Cidr: "abc",
				},
			},
			securityGroups: api.ListSecurityGroupResult{
				SecurityGroups: []api.SecurityGroupModel{
					{
						Id:   "123",
						Name: "default",
						Desc: "default",
					},
				},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			mockVpcModel := vpcModelMock.NewMockServiceInterface(ctrl)
			mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
			service := &Deployer{
				client:     kube.NewFakeClient(),
				vpcModel:   mockVpcModel,
				cceService: mockCceService,
			}
			service.configPath = testConfigPath

			mockCtx := csmContext.MockNewCsmContext()
			_, _ = service.client.Kube().CoreV1().Services(constants.LogBeatNamespace).Create(context.TODO(),
				testInfo.testService, metav1.CreateOptions{})
			_, _ = service.client.Kube().AppsV1().Deployments(constants.LogBeatNamespace).Create(context.TODO(),
				testInfo.testDeploy, metav1.CreateOptions{})
			_, _ = service.client.Kube().AppsV1().Deployments(constants.LogBeatNamespace).Create(context.TODO(),
				testInfo.istioDeploy, metav1.CreateOptions{})
			_, _ = service.client.Kube().CoreV1().ConfigMaps(constants.LogBeatNamespace).Create(context.TODO(),
				testInfo.istioConfigMap, metav1.CreateOptions{})

			mockVpcModel.EXPECT().GetVPCDetail(mockCtx, gomock.Any(), gomock.Any()).Return(&testInfo.vpcDetail, nil)
			mockVpcModel.EXPECT().ListSecurityGroup(mockCtx, gomock.Any(), gomock.Any()).Return(&testInfo.securityGroups, nil)

			testGateway := buildGatewayModel()
			testGateway.Namespace = constants.LogBeatNamespace
			err := service.DeployIngressController(mockCtx, testGateway, testInfo.ingressParam)
			if testInfo.expectErr != nil {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestUnDeployIngressController(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// TODO: 这里可以做下优化
	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name           string
		ingressParam   *meta.IngressParam
		testService    *v12.Service
		testDeploy     *v1.Deployment
		istioConfigMap *v12.ConfigMap
		istioDeploy    *v1.Deployment
		expectErr      error
	}{
		{
			name: "correct-UnDeployIngressController",
			ingressParam: &meta.IngressParam{
				Enabled: false,
				CLusterList: []meta.RemoteCluster{
					{
						ClusterID: "clu-1",
						Region:    "gz",
						Enabled:   false,
					},
					{
						ClusterID: "clu-2",
						Region:    "bj",
						Enabled:   false,
					},
				},
			},
			testService: &v12.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.ServiceNameHigress,
					Namespace: constants.LogBeatNamespace,
				},
				Spec: v12.ServiceSpec{},
			},
			testDeploy: &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.ServiceNameHigress,
					Namespace: constants.LogBeatNamespace,
				},
				Spec: v1.DeploymentSpec{
					Template: v12.PodTemplateSpec{
						Spec: v12.PodSpec{
							Containers: []v12.Container{{
								Name:  "higress",
								Image: "image",
							}},
						},
					},
					Replicas: csm.Int32(1),
				},
				Status: v1.DeploymentStatus{
					AvailableReplicas: 1,
					Replicas:          1,
				},
			},
			istioConfigMap: &v12.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IstioConfimapName,
					Namespace: constants.LogBeatNamespace,
				},
				Data: map[string]string{
					constants.IstioConfigMapMeshName: "default",
				},
			},
			istioDeploy: &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IstiodDeploymentName,
					Namespace: constants.LogBeatNamespace,
				},
				Spec: v1.DeploymentSpec{
					Template: v12.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Annotations: map[string]string{
								"test": "1",
							},
						},
					},
					Replicas: csm.Int32(1),
				},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			mockVpcModel := vpcModelMock.NewMockServiceInterface(ctrl)
			mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
			service := &Deployer{
				client:     kube.NewFakeClient(),
				vpcModel:   mockVpcModel,
				cceService: mockCceService,
			}
			service.configPath = testConfigPath

			mockCtx := csmContext.MockNewCsmContext()
			_, _ = service.client.Kube().CoreV1().Services(constants.LogBeatNamespace).Create(context.TODO(),
				testInfo.testService, metav1.CreateOptions{})
			_, _ = service.client.Kube().AppsV1().Deployments(constants.LogBeatNamespace).Create(context.TODO(),
				testInfo.testDeploy, metav1.CreateOptions{})
			_, _ = service.client.Kube().AppsV1().Deployments(constants.LogBeatNamespace).Create(context.TODO(),
				testInfo.istioDeploy, metav1.CreateOptions{})
			_, _ = service.client.Kube().CoreV1().ConfigMaps(constants.LogBeatNamespace).Create(context.TODO(),
				testInfo.istioConfigMap, metav1.CreateOptions{})

			testGateway := buildGatewayModel()
			testGateway.Namespace = constants.LogBeatNamespace
			err := service.UnDeployIngressController(mockCtx, testGateway, testInfo.ingressParam)
			if testInfo.expectErr != nil {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestUpdateIngressAccess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// TODO: 这里可以做下优化
	_, filename, _, _ := runtime.Caller(0)
	testConfigPath := path.Join(path.Dir(filename), "../../../../", constants.Templates)

	testInfos := []struct {
		name           string
		enabled        bool
		remoteClusters []meta.Cluster
		vpcDetail      vpc.GetVPCDetailResult
		clusterRole    *rbacv1.ClusterRole
		securityGroups api.ListSecurityGroupResult
		expectErr      error
	}{
		{
			name:    "add-UpdateIngressAccess",
			enabled: true,
			remoteClusters: []meta.Cluster{
				{
					ClusterUUID: "clu-1",
					Region:      "gz",
				},
			},
			clusterRole: &rbacv1.ClusterRole{
				ObjectMeta: metav1.ObjectMeta{
					Name: "clusterRoleName",
				},
			},
			expectErr: nil,
		},
		{
			name:    "remove-UpdateIngressAccess",
			enabled: false,
			remoteClusters: []meta.Cluster{
				{
					ClusterUUID: "clu-1",
					Region:      "gz",
				},
			},
			clusterRole: &rbacv1.ClusterRole{
				ObjectMeta: metav1.ObjectMeta{
					Name: "clusterRoleName",
				},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			mockVpcModel := vpcModelMock.NewMockServiceInterface(ctrl)
			mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
			service := &Deployer{
				client:     kube.NewFakeClient(),
				vpcModel:   mockVpcModel,
				cceService: mockCceService,
			}
			service.configPath = testConfigPath

			mockCtx := csmContext.MockNewCsmContext()
			_, _ = service.client.Kube().RbacV1().ClusterRoles().Create(context.TODO(),
				testInfo.clusterRole, metav1.CreateOptions{})
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(service.client, nil)

			err := service.UpdateIngressAccess(mockCtx, "clusterRoleName", testInfo.enabled, testInfo.remoteClusters)
			if testInfo.expectErr != nil {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}
