package gateway

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	Init(ctx csmContext.CsmContext, region, clusterId string) error
	InstallCCEIngressGateway(ctx csmContext.CsmContext, gateway *meta.GatewayModel, blbConf *meta.BlbConf, tlsAccConf *meta.TLSAccConf) error
	UninstallCCEIngressGateway(ctx csmContext.CsmContext, gateway *meta.GatewayModel) error
	DeployHPA(ctx csmContext.CsmContext, hpaConf *meta.HpaConf) error
	UndeployHPA(ctx csmContext.CsmContext, hpaConf *meta.HpaConf) error
	DeployGwVs(ctx csmContext.CsmContext, gatewayUUID, namespace string) error
	UndeployGwVs(ctx csmContext.CsmContext, gatewayUUID, namespace string) error
	DeployTelemetry(ctx csmContext.CsmContext, blsTaskID, gatewayUUID, namespace string) error
	UndeployTelemetry(ctx csmContext.CsmContext, gatewayUUID, namespace string) (string, error)
	UpdateMonitorInfoInAnnotation(ctx csmContext.CsmContext, gatewayModel *meta.GatewayModel, cpromInfo *meta.CPromGatewayInfo) error
	EnableTLSAcceleration(ctx csmContext.CsmContext, namespace string) error
	DisableTLSAcceleration(ctx csmContext.CsmContext, namespace string) error
	UpdateResourceQuota(ctx csmContext.CsmContext, resourceQuota, namespace string) error
	UpdateIngressInfoInAnnotation(ctx csmContext.CsmContext, namespace string, ingressParam *meta.IngressParam) error
	DeployIngressController(ctx csmContext.CsmContext, gateway *meta.GatewayModel, ingressParam *meta.IngressParam) error
	UnDeployIngressController(ctx csmContext.CsmContext, gateway *meta.GatewayModel, ingressParam *meta.IngressParam) error
	UpdateIngressAccess(ctx csmContext.CsmContext, clusterRoleName string, enable bool, remoteClusters []meta.Cluster) error
}
