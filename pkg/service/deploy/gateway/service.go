package gateway

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	bccApi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/spf13/viper"
	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/vpc"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/command"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/tmpl"
)

type Deployer struct {
	client                 kube.Client
	params                 *IopGatewayParseParams
	vpcModel               vpc.ServiceInterface
	cceService             cce.ClientInterface
	configPath             string
	ingressGatewayYamlPath string
	hpaYamlPath            string
	gwVsYamlPath           string
	telemetryYamlPath      string
	higressHub             string
	higressImage           string
	higressTag             string
}

func NewDeployerService() *Deployer {
	return &Deployer{
		vpcModel:   vpc.NewService(vpc.NewOption()),
		cceService: cce.NewClientService(),
	}
}

// Init 初始化gateway deployer的一些通用成员
func (deploy *Deployer) Init(ctx csmContext.CsmContext, region, clusterId string) error {
	// TODO：此处的Client需要修改为根据sts或者aksk方式创建
	// TODO: 这里的实例类型需要注意下
	k8sClient, err := deploy.cceService.NewClient(ctx, region, clusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	deploy.client = k8sClient

	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("get pwd error %v", err)
		return err
	}
	deploy.configPath = path.Join(pwd, constants.Templates)
	// for higress controller
	deploy.higressHub = viper.GetString("istio.hosting.higress.hub")
	deploy.higressImage = viper.GetString("istio.hosting.higress.image")
	deploy.higressTag = viper.GetString("istio.hosting.higress.tag")

	return nil
}

func (deploy *Deployer) initParams(ctx csmContext.CsmContext, gateway *meta.GatewayModel,
	blbConf *meta.BlbConf, tlsAccConf *meta.TLSAccConf) error {
	deployParams := NewIopGatewayParseParams(ctx, gateway)

	vpcDetail, vpcErr := deploy.vpcModel.GetVPCDetail(ctx, gateway.VpcNetworkId, gateway.Region)
	if vpcErr != nil {
		ctx.CsmLogger().Errorf("fail to get vpcDetail %v", vpcErr)
		return vpcErr
	}
	deployParams.VpcCidr = vpcDetail.VPC.Cidr

	args := &bccApi.ListSecurityGroupArgs{
		VpcId: gateway.VpcNetworkId,
	}
	vpcSecurityGroups, vpcErr := deploy.vpcModel.ListSecurityGroup(ctx, args, gateway.Region)
	if vpcErr != nil {
		ctx.CsmLogger().Errorf("fail to get vpcSecurityGroups %v", vpcErr)
		return vpcErr
	}
	for _, sg := range vpcSecurityGroups.SecurityGroups {
		if sg.Desc == constants.VpcSecurityGroupDefaultName {
			deployParams.SecurityGroupIDs = sg.Id
			break
		}
	}

	if blbConf != nil {
		deployParams.BlbID = blbConf.BlbID
		if blbConf.EipConf.Enabled {
			deployParams.OnlyInternalAccess = "false"
			deployParams.BindedEip = blbConf.EipConf.IP
		}
	}

	if tlsAccConf != nil {
		if tlsAccConf.Enabled {
			deployParams.TLSAccelerationAnnotation = "|\n" + constants.TLSAccelerationAnnotationVal
		}
	}

	deploy.params = deployParams
	return nil
}

func (deploy *Deployer) generateK8sObjects(ctx csmContext.CsmContext, prefix string) ([]string, error) {
	ingressGatewaySrcPath := path.Join(deploy.configPath, constants.BaseGatewayTemplate, constants.IngressGatewayTmpl)
	ingressGatewayDstPath := path.Join(deploy.configPath, constants.BaseGatewayTemplate, prefix+"-"+constants.IngressGatewayYaml)

	err := tmpl.EvaluatePathTmpl(ctx, ingressGatewaySrcPath, ingressGatewayDstPath, deploy.params)
	if err != nil {
		return nil, err
	}
	deploy.ingressGatewayYamlPath = ingressGatewayDstPath

	versionBase := path.Join(deploy.configPath, constants.HostingBaseIstioTemplate, deploy.params.IstioVersion)
	istioCtlBin := path.Join(versionBase, constants.BaseIstioBin, util.GetIstioCtl(ctx))
	installIngressGatewayCmd := istioCtlBin + fmt.Sprintf(" manifest generate -f %s", deploy.ingressGatewayYamlPath)
	outStr, errStr, err := command.ExecCmdOut(ctx, installIngressGatewayCmd)
	if len(errStr) > 0 || err != nil {
		return nil, fmt.Errorf("istioctl manifest generate err %v", err)
	}
	return object.ManifestK8sObject(ctx, string(outStr))
}

// InstallCCEIngressGateway 生成并部署托管网关CRD
func (deploy *Deployer) InstallCCEIngressGateway(ctx csmContext.CsmContext, gateway *meta.GatewayModel,
	blbConf *meta.BlbConf, tlsAccConf *meta.TLSAccConf) error {
	ctx.CsmLogger().Infof("install cce ingress-gateway with K8sObject")
	err := deploy.initParams(ctx, gateway, blbConf, tlsAccConf)
	if err != nil {
		return err
	}
	objectsList, err := deploy.generateK8sObjects(ctx, gateway.GatewayUUID)
	if err != nil {
		return err
	}
	err = kube.CreateOrUpdateK8sResource(ctx, deploy.client, objectsList)
	return err
}

// UninstallCCEIngressGateway 移除网关的工作负载
func (deploy *Deployer) UninstallCCEIngressGateway(ctx csmContext.CsmContext, gateway *meta.GatewayModel) error {
	ctx.CsmLogger().Infof("uninstall cce ingress-gateway with K8sObject")
	// TODO: 增加获取BLB信息的相关逻辑
	err := deploy.initParams(ctx, gateway, nil, nil)
	if err != nil {
		return err
	}
	objectsList, err := deploy.generateK8sObjects(ctx, gateway.GatewayUUID)
	if err != nil {
		return err
	}
	err = kube.DeleteK8sResource(ctx, deploy.client, objectsList)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to delete gateway k8s resource: %v", err)
		return err
	}
	// 删除生成的yaml本地文件
	err = file.RemoveFile(deploy.ingressGatewayYamlPath)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to remove gateway yaml file: %v", err)
	}
	return nil
}

func (deploy *Deployer) HelmDeployTemplate(ctx csmContext.CsmContext, namespace, hub, image, tag string,
	annotationMap map[string]string) ([]string, error) {
	helmBin := path.Join(deploy.configPath, constants.BaseHigressPath, constants.BaseHelmBin,
		util.GetHelmCtl(ctx))
	helmChartFilePath := path.Join(deploy.configPath, constants.BaseHigressPath, constants.BaseHigressChart)

	// 命令模板 helm template {name} -n {namespace} filepath --set {自定义参数}
	helmCmd := helmBin + fmt.Sprintf(" template %s -n %s %s --set hub=%s --set controller.image=%s --set controller.tag=%s",
		constants.BaseHigressName, namespace, helmChartFilePath, hub, image, tag)
	// 添加podAnnotation标签
	for key, value := range annotationMap {
		helmCmd = helmCmd + fmt.Sprintf(" --set controller.podAnnotations.%s=%s", key, value)
	}
	ctx.CsmLogger().Infof("HelmDeployTemplate helmCmd is %s", helmCmd)
	outStr, errStr, err := command.ExecCmdOut(ctx, helmCmd)
	if len(errStr) > 0 || err != nil {
		return nil, csmErr.NewResourceNotFoundException(fmt.Sprintf("helm template err %s", err.Error()))
	}

	return object.ManifestK8sObject(ctx, string(outStr))
}

func (deploy *Deployer) HelmUndeployTemplate(ctx csmContext.CsmContext, namespace string) ([]string, error) {
	helmBin := path.Join(deploy.configPath, constants.BaseHigressPath, constants.BaseHelmBin,
		util.GetHelmCtl(ctx))
	helmChartFilePath := path.Join(deploy.configPath, constants.BaseHigressPath, constants.BaseHigressChart)

	// 命令模板 helm template {name} -n {namespace} filepath --set {自定义参数}
	helmCmd := helmBin + fmt.Sprintf(" template %s -n %s %s", constants.BaseHigressName, namespace, helmChartFilePath)
	outStr, errStr, err := command.ExecCmdOut(ctx, helmCmd)
	if len(errStr) > 0 || err != nil {
		return nil, csmErr.NewResourceNotFoundException(fmt.Sprintf("helm template err %s", err.Error()))
	}

	return object.ManifestK8sObject(ctx, string(outStr))
}

func (deploy *Deployer) UpdateIngressAccess(ctx csmContext.CsmContext, clusterRoleName string, enable bool,
	remoteClusters []meta.Cluster) error {
	if enable {
		return deploy.addIngressAPIRule(ctx, clusterRoleName, remoteClusters)
	}
	return deploy.removeIngressAPIRule(ctx, clusterRoleName, remoteClusters)
}

// addIngressAPIRule 添加访问remote集群的ingress权限
func (deploy *Deployer) addIngressAPIRule(ctx csmContext.CsmContext, clusterRoleName string, remoteClusters []meta.Cluster) error {
	for index := range remoteClusters {
		clu := remoteClusters[index]
		// 初始化remote用户集群
		remoteClient, err := deploy.cceService.NewClient(ctx, clu.Region, clu.ClusterUUID, meta.StandaloneMeshType)
		if err != nil {
			ctx.CsmLogger().Errorf("cce NewClient error %v", err)
			return err
		}
		clusterRole, err := remoteClient.Kube().RbacV1().ClusterRoles().Get(context.TODO(), clusterRoleName, metav1.GetOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("Error getting ClusterRole: %s", err.Error())
			return err
		}
		// 如果已经有权限，则不需要添加
		if hasIngressAccess(clusterRole.Rules) > -1 {
			return nil
		}

		// 增加新的权限规则
		newRule := rbacv1.PolicyRule{
			APIGroups: []string{constants.RbacAPIGroups},
			Resources: []string{constants.RbacResourcesIngress, constants.RbacResourcesIngressclasses},
			Verbs:     []string{"get", "list", "watch"},
		}
		clusterRole.Rules = append(clusterRole.Rules, newRule)
		_, err = remoteClient.Kube().RbacV1().ClusterRoles().Update(context.TODO(), clusterRole, metav1.UpdateOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("Error updating ClusterRole: %s\n", err.Error())
			return err
		}
	}

	return nil
}

// removeIngressAPIRule 移出所有remote集群ingress权限
func (deploy *Deployer) removeIngressAPIRule(ctx csmContext.CsmContext, clusterRoleName string, remoteClusters []meta.Cluster) error {
	for index := range remoteClusters {
		clu := remoteClusters[index]
		// 初始化remote用户集群
		remoteClient, err := deploy.cceService.NewClient(ctx, clu.Region, clu.ClusterUUID, meta.StandaloneMeshType)
		if err != nil {
			ctx.CsmLogger().Errorf("cce NewClient error %v", err)
			return err
		}
		clusterRole, err := remoteClient.Kube().RbacV1().ClusterRoles().Get(context.TODO(), clusterRoleName, metav1.GetOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("Error getting ClusterRole: %s", err.Error())
			return err
		}

		// 如果没有权限，则不需要删除
		index := hasIngressAccess(clusterRole.Rules)
		if index == -1 {
			return nil
		}
		// 删除权限规则
		if index == len(clusterRole.Rules) {
			clusterRole.Rules = append(clusterRole.Rules[:index])
		} else {
			clusterRole.Rules = append(clusterRole.Rules[:index], clusterRole.Rules[index+1:]...)
		}

		_, err = remoteClient.Kube().RbacV1().ClusterRoles().Update(context.TODO(), clusterRole, metav1.UpdateOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("Error updating ClusterRole: %s\n", err.Error())
			return err
		}
	}

	return nil
}

func hasIngressAccess(rules []rbacv1.PolicyRule) int {
	for index, rule := range rules {
		if len(rule.APIGroups) == 1 && rule.APIGroups[0] == constants.RbacAPIGroups && len(rule.Resources) == 2 {
			if (rule.Resources[0] == constants.RbacResourcesIngress || rule.Resources[0] == constants.RbacResourcesIngressclasses) &&
				(rule.Resources[1] == constants.RbacResourcesIngress || rule.Resources[1] == constants.RbacResourcesIngressclasses) {
				return index
			}
		}
	}
	return -1
}

// DeployIngressController 部署higress controller
func (deploy *Deployer) DeployIngressController(ctx csmContext.CsmContext, gateway *meta.GatewayModel, ingressParam *meta.IngressParam) error {
	ctx.CsmLogger().Infof("deploy ingress controller with helm")
	if !ingressParam.Enabled {
		return csmErr.NewInvalidParameterValueException("ingressParam.Enabled is false, can not deploy")
	}

	// 获取vpc信息
	vpcDetail, vpcErr := deploy.vpcModel.GetVPCDetail(ctx, gateway.VpcNetworkId, gateway.Region)
	if vpcErr != nil {
		ctx.CsmLogger().Errorf("fail to get vpcDetail %v", vpcErr)
		return vpcErr
	}
	// yaml文件中补充cross vpc信息
	sgID, securityErr := deploy.getSecurityGroupID(ctx, gateway, constants.VpcSecurityGroupDefaultName)
	if securityErr != nil {
		return securityErr
	}

	annotationMap := make(map[string]string, 0)
	annotationMap[constants.CrossVpcUserID] = gateway.AccountId
	annotationMap[constants.CrossVpcVpcCidr] = vpcDetail.VPC.Cidr
	annotationMap[constants.CrossVpcSubnetID] = gateway.SubnetId
	annotationMap[constants.CrossVpcSecurityGroupIDs] = sgID
	annotationMap[constants.CrossVpcDefaultRouteExcludedCidrs] = constants.ValueDefaultRouteExcludedCidrs
	annotationMap[constants.CrossVpcPrivateIPAddress] = ""

	// 使用helm生成k8s资源描述文件
	objectsList, err := deploy.HelmDeployTemplate(ctx, gateway.Namespace, deploy.higressHub, deploy.higressImage,
		deploy.higressTag, annotationMap)
	// apply 到集群中
	err = kube.CreateOrUpdateK8sResource(ctx, deploy.client, objectsList)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to deploy higress controller, err: %s", err.Error())
		return err
	}

	// 等待higress启动以后，修改istio cm信息
	go func(ctx csmContext.CsmContext, namespace string, ingressParam *meta.IngressParam) {
		err := kube.WaitDeploymentReady(ctx, deploy.client, constants.IstiodDeploymentName, namespace)
		if err != nil {
			fmt.Printf("Failed to waitForHigressToBeRunning, err is %s", err.Error())
			return
		}
		updateErr := deploy.UpdateConfigMapForHigress(ctx, constants.IstioConfimapName, namespace,
			constants.IstioConfigMapMeshName, constants.PortHigressXds, ingressParam.Enabled)
		if updateErr != nil {
			fmt.Printf("Failed to UpdateConfigMapForHigress, err is %s", updateErr.Error())
			return
		}
		err = deploy.UpdateIngressInfoInAnnotation(ctx, namespace, ingressParam)
		if err != nil {
			fmt.Printf("Failed to UpdateIngressInfoInAnnotation, err is %s", err.Error())
			return
		}
	}(ctx, gateway.Namespace, ingressParam)
	return nil
}

// UnDeployIngressController 卸载higress controller
func (deploy *Deployer) UnDeployIngressController(ctx csmContext.CsmContext, gateway *meta.GatewayModel, ingressParam *meta.IngressParam) error {
	ctx.CsmLogger().Infof("undeploy ingress controller with helm")
	if ingressParam.Enabled {
		return csmErr.NewInvalidParameterValueException("ingressParam.Enabled is true, can not undeploy")
	}
	// 使用helm生成k8s资源描述文件
	objectsList, err := deploy.HelmUndeployTemplate(ctx, gateway.Namespace)
	// apply 到集群中
	err = kube.DeleteK8sResource(ctx, deploy.client, objectsList)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to undeploy higress controller, err: %s", err.Error())
		return err
	}
	updateErr := deploy.UpdateConfigMapForHigress(ctx, constants.IstioConfimapName, gateway.Namespace,
		constants.IstioConfigMapMeshName, constants.PortHigressXds, ingressParam.Enabled)
	if updateErr != nil {
		ctx.CsmLogger().Errorf("Failed to UpdateConfigMapForHigress, err is %s", updateErr.Error())
		return updateErr
	}

	return nil
}

func (deploy *Deployer) UpdateConfigMapForHigress(ctx csmContext.CsmContext, cmName, namespace, key string, port int, enable bool) error {
	configMap, err := deploy.client.Kube().CoreV1().ConfigMaps(namespace).Get(context.TODO(), cmName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get configmap %v, error is %s", cmName, err.Error())
		return err
	}
	// Modify the ConfigMap data.
	data, ok := configMap.Data[key]
	if !ok {
		return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("fail to get configmap data %s", key))
	}
	updatedData := ""
	// 适配higress-controller的内容
	var buf bytes.Buffer
	buf.WriteString("configSources:\n")
	buf.WriteString("- address: \"k8s://\"\n")
	buf.WriteString(fmt.Sprintf("- address: \"xds://%s.%s:%d\"\n", constants.ServiceNameHigress, namespace, port))

	if enable {
		if strings.Contains(data, buf.String()) {
			updatedData = data
		} else {
			updatedData = buf.String() + data
		}
	} else {
		updatedData = strings.ReplaceAll(data, buf.String(), "")
	}

	configMap.Data[key] = updatedData
	// Update the ConfigMap.
	_, err = deploy.client.Kube().CoreV1().ConfigMaps(namespace).Update(context.TODO(), configMap, metav1.UpdateOptions{})
	if err != nil {
		return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("Failed to update ConfigMap %s: %v", cmName, err))
	}

	// 更新cm需要触发istio pod重启，cm才能生效。这里通过给istio的deploy新增一组无用的annotation来触发pod重建。
	istioDeploy, err := deploy.client.Kube().AppsV1().Deployments(namespace).Get(context.TODO(),
		constants.IstiodDeploymentName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get svc %s, error is %s", constants.ServiceNameHigress, err.Error())
		return err
	}
	istioDeploy.Spec.Template.ObjectMeta.Annotations[constants.RestartIstioAnnotation] = time.Now().Format(time.RFC3339)

	// 更新部署信息
	_, err = deploy.client.Kube().AppsV1().Deployments(namespace).Update(context.TODO(), istioDeploy, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	return nil
}

func (deploy *Deployer) getSecurityGroupID(ctx csmContext.CsmContext, gateway *meta.GatewayModel, SecurityGroupName string) (string, error) {
	args := &bccApi.ListSecurityGroupArgs{
		VpcId: gateway.VpcNetworkId,
	}
	vpcSecurityGroups, vpcErr := deploy.vpcModel.ListSecurityGroup(ctx, args, gateway.Region)
	if vpcErr != nil {
		ctx.CsmLogger().Errorf("fail to get vpcSecurityGroups %v", vpcErr)
		return "", vpcErr
	}
	for _, sg := range vpcSecurityGroups.SecurityGroups {
		if sg.Desc == SecurityGroupName {
			return sg.Id, nil
		}
	}
	return "", csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("not found %s SecurityGroup", SecurityGroupName))
}

func (deploy *Deployer) generateOtherK8sObjects(ctx csmContext.CsmContext, tmplFile, yamlFile, prefix string,
	resourceType ResourceType, data interface{}) ([]string, error) {
	srcPath := path.Join(deploy.configPath, constants.BaseGatewayTemplate, tmplFile)
	dstPath := path.Join(deploy.configPath, constants.BaseGatewayTemplate, prefix+"-"+yamlFile)

	err := tmpl.EvaluatePathTmpl(ctx, srcPath, dstPath, data)
	if err != nil {
		return nil, err
	}
	switch resourceType {
	case HPAResourceType:
		deploy.hpaYamlPath = dstPath
	case GwVsResourceType:
		deploy.gwVsYamlPath = dstPath
	case TelemetryResourceType:
		deploy.telemetryYamlPath = dstPath
	}
	outStr, err := os.ReadFile(dstPath)
	if err != nil {
		return nil, err
	}
	return object.ManifestK8sObject(ctx, string(outStr))
}

// DeployHPA 部署HPA CRD
func (deploy *Deployer) DeployHPA(ctx csmContext.CsmContext, hpaConf *meta.HpaConf) error {
	objectsList, err := deploy.generateOtherK8sObjects(ctx, constants.HpaTmpl, constants.HpaYaml,
		hpaConf.GatewayUUID, HPAResourceType, hpaConf)
	if err != nil {
		return err
	}
	err = kube.CreateOrUpdateK8sResource(ctx, deploy.client, objectsList)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to deploy hpa: %v", err)
		return err
	}
	return nil
}

// UndeployHPA 删除HPA资源
func (deploy *Deployer) UndeployHPA(ctx csmContext.CsmContext, hpaConf *meta.HpaConf) error {
	objectsList, err := deploy.generateOtherK8sObjects(ctx, constants.HpaTmpl, constants.HpaYaml,
		hpaConf.GatewayUUID, HPAResourceType, hpaConf)
	if err != nil {
		return err
	}
	err = kube.DeleteK8sResource(ctx, deploy.client, objectsList)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to delete hpa k8s resource: %v", err)
		return err
	}
	// 删除生成的yaml本地文件
	err = file.RemoveFile(deploy.hpaYamlPath)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to remove hpa yaml file: %v", err)
	}
	return nil
}

type GwVs struct {
	Namespace string
}

// DeployGwVs 部署Gateway和VirtualService
func (deploy *Deployer) DeployGwVs(ctx csmContext.CsmContext, gatewayUUID, namespace string) error {
	objectsList, err := deploy.generateOtherK8sObjects(ctx, constants.GwVsTmpl, constants.GwVsYaml,
		gatewayUUID, GwVsResourceType, GwVs{namespace})
	if err != nil {
		return err
	}
	err = kube.CreateOrUpdateK8sResource(ctx, deploy.client, objectsList)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to deploy gw-vs crd: %v", err)
		return err
	}
	return nil
}

// UndeployGwVs 删除Gateway和VirtualService
func (deploy *Deployer) UndeployGwVs(ctx csmContext.CsmContext, gatewayUUID, namespace string) error {
	// 删除网关域名下Secret证书
	secretList, err := deploy.client.Kube().CoreV1().Secrets(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get gateway secret list: %v", err)
	} else {
		for _, sc := range secretList.Items {
			if strings.HasPrefix(sc.Name, constants.GwSecretNamePrefix) {
				err = deploy.client.Kube().CoreV1().Secrets(namespace).Delete(context.TODO(), sc.Name, metav1.DeleteOptions{})
				if err != nil {
					ctx.CsmLogger().Errorf("fail to delete gateway secret: %s: %v", sc.Name, err)
				}
			}
		}
	}

	objectsList, err := deploy.generateOtherK8sObjects(ctx, constants.GwVsTmpl, constants.GwVsYaml,
		gatewayUUID, GwVsResourceType, GwVs{namespace})
	if err != nil {
		return err
	}
	err = kube.DeleteK8sResource(ctx, deploy.client, objectsList)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to delete gw-vs crd: %v", err)
		return err
	}
	// 删除生成的yaml本地文件
	err = file.RemoveFile(deploy.gwVsYamlPath)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to remove gw-vs yaml file: %v", err)
	}
	return nil
}

type Telemetry struct {
	TaskID    string
	Namespace string
}

// DeployTelemetry 部署Gateway日志开关Crd
func (deploy *Deployer) DeployTelemetry(ctx csmContext.CsmContext, blsTaskID, gatewayUUID, namespace string) error {
	// 添加BlsTaskID到log-beat
	logBeatDs, err := deploy.client.Kube().AppsV1().DaemonSets(constants.LogBeatNamespace).Get(
		context.TODO(), constants.LogBeatName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get logbeat-ds: %v", err)
		return err
	}
	for idx, env := range logBeatDs.Spec.Template.Spec.Containers[0].Env {
		if env.Name == constants.LogBeatTaskEnv {
			if env.Value == "" {
				logBeatDs.Spec.Template.Spec.Containers[0].Env[idx].Value = blsTaskID
			} else {
				logBeatDs.Spec.Template.Spec.Containers[0].Env[idx].Value = env.Value + "," + blsTaskID
			}
			break
		}
	}
	_, err = deploy.client.Kube().AppsV1().DaemonSets(constants.LogBeatNamespace).Update(context.TODO(), logBeatDs, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to update logbeat-ds: %v", err)
		return err
	}
	// 部署Telemetry Crd
	objectsList, err := deploy.generateOtherK8sObjects(ctx, constants.TelemetryTmpl, constants.TelemetryYaml, gatewayUUID,
		TelemetryResourceType, Telemetry{blsTaskID, namespace})
	if err != nil {
		return err
	}
	err = kube.CreateOrUpdateK8sResource(ctx, deploy.client, objectsList)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to deploy telemetry crd: %v", err)
		return err
	}
	return nil
}

// UndeployTelemetry 卸载Gateway日志开关Crd
func (deploy *Deployer) UndeployTelemetry(ctx csmContext.CsmContext, gatewayUUID, namespace string) (string, error) {
	// 获取当前的TaskID
	telemetry, err := deploy.client.Istio().TelemetryV1alpha1().Telemetries(namespace).Get(context.TODO(),
		constants.GwTelemetryCrdName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Warnf("fail to get telemetry: %v", err)
		return "", nil
	}
	blsTaskID := telemetry.ObjectMeta.Labels[constants.GwTelemetryLabelKey]

	// 在logbeat-ds中移除该taskID
	logBeatDs, err := deploy.client.Kube().AppsV1().DaemonSets(constants.LogBeatNamespace).Get(
		context.TODO(), constants.LogBeatName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get logbeat-ds: %v", err)
		return blsTaskID, err
	}
	for idx, env := range logBeatDs.Spec.Template.Spec.Containers[0].Env {
		if env.Name == constants.LogBeatTaskEnv {
			taskIDList := strings.Split(env.Value, ",")
			taskIDs := ""
			for _, id := range taskIDList {
				if id != blsTaskID {
					taskIDs += id
					taskIDs += ","
				}
			}
			if len(taskIDs) != 0 {
				taskIDs = taskIDs[:len(taskIDs)-1]
			}
			logBeatDs.Spec.Template.Spec.Containers[0].Env[idx].Value = taskIDs
			break
		}
	}
	_, err = deploy.client.Kube().AppsV1().DaemonSets(constants.LogBeatNamespace).Update(context.TODO(), logBeatDs, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to update logbeat-ds: %v", err)
		return blsTaskID, err
	}
	// 删除Telemetry资源
	objectsList, err := deploy.generateOtherK8sObjects(ctx, constants.TelemetryTmpl, constants.TelemetryYaml,
		gatewayUUID, TelemetryResourceType, Telemetry{blsTaskID, namespace})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to delete telemetry: %v", err)
		return blsTaskID, err
	}

	err = kube.DeleteK8sResource(ctx, deploy.client, objectsList)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to delete telemetry crd: %v", err)
		return blsTaskID, err
	}
	// 删除生成的yaml本地文件
	err = file.RemoveFile(deploy.telemetryYamlPath)
	if err != nil {
		ctx.CsmLogger().Error("fail to remove telemetry yaml file: %v")
	}
	return blsTaskID, nil
}

// UpdateMonitorInfoInAnnotation 更新托管网关service.annotation的CProm信息，让lb-controller可以更新采集任务
func (deploy *Deployer) UpdateMonitorInfoInAnnotation(ctx csmContext.CsmContext, gatewayModel *meta.GatewayModel,
	cpromInfo *meta.CPromGatewayInfo) error {
	if gatewayModel == nil {
		return csmErr.NewInvalidParameterValueException("gatewayModel is nil")
	}

	namespace := gatewayModel.Namespace
	err := deploy.Init(ctx, gatewayModel.Region, gatewayModel.ClusterUUID)
	if err != nil {
		ctx.CsmLogger().Error("fail to initialize gateway")
		return err
	}
	gatewaySvc, err := deploy.client.Kube().CoreV1().Services(namespace).Get(context.TODO(),
		constants.IngressGatewayResourceName, metav1.GetOptions{})
	if err != nil {
		return err
	}
	// update monitor info
	gatewaySvc.Annotations[constants.CsmCPromInstanceID] = cpromInfo.ID
	gatewaySvc.Annotations[constants.CsmCPromAgentID] = cpromInfo.AgentID
	gatewaySvc.Annotations[constants.CsmCPromScrapeJobID] = cpromInfo.ScrapeJobID

	_, err = deploy.client.Kube().CoreV1().Services(namespace).Update(context.TODO(), gatewaySvc, metav1.UpdateOptions{})
	if err != nil {
		return err
	}
	return nil
}

func (deploy *Deployer) EnableTLSAcceleration(ctx csmContext.CsmContext, namespace string) error {
	gatewayDeploy, err := deploy.client.Kube().AppsV1().Deployments(namespace).Get(context.TODO(),
		constants.IngressGatewayResourceName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get gateway deployment: %v", err)
		return err
	}
	if _, ok := gatewayDeploy.Spec.Template.ObjectMeta.Annotations[constants.TLSAccelerationAnnotationKey]; ok {
		return nil
	}
	gatewayDeploy.Spec.Template.ObjectMeta.Annotations[constants.TLSAccelerationAnnotationKey] = constants.TLSAccelerationAnnotationVal
	_, err = deploy.client.Kube().AppsV1().Deployments(namespace).Update(context.TODO(), gatewayDeploy, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to update gateway deployment: %v", err)
		return err
	}

	return nil
}

func (deploy *Deployer) DisableTLSAcceleration(ctx csmContext.CsmContext, namespace string) error {
	gatewayDeploy, err := deploy.client.Kube().AppsV1().Deployments(namespace).Get(context.TODO(),
		constants.IngressGatewayResourceName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get gateway deployment: %v", err)
		return err
	}
	if _, ok := gatewayDeploy.Spec.Template.ObjectMeta.Annotations[constants.TLSAccelerationAnnotationKey]; !ok {
		return nil
	}
	delete(gatewayDeploy.Spec.Template.ObjectMeta.Annotations, constants.TLSAccelerationAnnotationKey)
	_, err = deploy.client.Kube().AppsV1().Deployments(namespace).Update(context.TODO(), gatewayDeploy, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to update gateway deployment: %v", err)
		return err
	}

	return nil
}

func (deploy *Deployer) UpdateResourceQuota(ctx csmContext.CsmContext, resourceQuota, namespace string) error {
	gatewayDeploy, err := deploy.client.Kube().AppsV1().Deployments(namespace).Get(context.TODO(),
		constants.IngressGatewayResourceName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get gateway deployment: %v", err)
		return err
	}
	_, lmtQ := parseResourceQuota(ctx, resourceQuota)
	cpuQ, err := resource.ParseQuantity(lmtQ.Cpu)
	if err != nil {
		return err
	}
	memQ, err := resource.ParseQuantity(lmtQ.Memory)
	if err != nil {
		return err
	}

	gatewayDeploy.Spec.Template.Spec.Containers[0].Resources.Limits[constants.Cpu] = cpuQ
	gatewayDeploy.Spec.Template.Spec.Containers[0].Resources.Limits[constants.Memory] = memQ

	_, err = deploy.client.Kube().AppsV1().Deployments(namespace).Update(context.TODO(),
		gatewayDeploy, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to update gateway deployment: %v", err)
		return err
	}
	return nil
}

// UpdateIngressInfoInAnnotation 更新 Ingress 信息到注解中
func (deploy *Deployer) UpdateIngressInfoInAnnotation(ctx csmContext.CsmContext, namespace string,
	ingressParam *meta.IngressParam) error {
	higressSvc, err := deploy.client.Kube().CoreV1().Services(namespace).Get(context.TODO(),
		constants.ServiceNameHigress, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get svc %s, error is %s", constants.ServiceNameHigress, err.Error())
		return err
	}
	// 构建remote集群ID列表
	var buf bytes.Buffer
	sep := ""
	for _, cluster := range ingressParam.CLusterList {
		if cluster.Enabled {
			buf.WriteString(sep + cluster.Region + "-" + cluster.ClusterID)
			sep = ","
		}
	}
	if higressSvc.Annotations == nil {
		higressSvc.Annotations = make(map[string]string)
	}

	flag := "False"
	if ingressParam.Enabled {
		flag = "True"
		higressSvc.Annotations[constants.AnnotationSyncClusterIDs] = buf.String()
	}
	higressSvc.Annotations[constants.AnnotationEnableSyncIngress] = flag
	_, err = deploy.client.Kube().CoreV1().Services(namespace).Update(context.TODO(), higressSvc, metav1.UpdateOptions{})
	if err != nil {
		return err
	}
	return nil
}
