package deploy

import (
	"context"
	"fmt"
	"os"
	"path"
	"runtime"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/otiai10/copy"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmcontext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	admissionregistrationv1 "k8s.io/api/admissionregistration/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

var (
	ConfigPath     = "/tmp/test"
	Version        = "1.13.2"
	Namespace      = "istio-system"
	Region         = "bj"
	MeshInstanceId = "test01"
	ClusterName    = "istio-test01"
	CceClusterUuid = "xxxxxxx"
	mockContext, _ = csmcontext.NewCsmContextMock()

	TestWebhook      = "test-webhook"
	IopStandaloneHub = "istio.standalone.iop.hub"

	ContextIAMUser = "User"
)

func TestDeployFunc(t *testing.T) {
	params := &Params{
		ConfigPath:              ConfigPath,
		Version:                 Version,
		Namespace:               Namespace,
		Region:                  Region,
		MeshInstanceId:          MeshInstanceId,
		ClusterName:             ClusterName,
		CceClusterUuid:          CceClusterUuid,
		DiscoverySelectorLabels: nil,
	}

	labels := map[string]string{"user": "test01"}

	fakeClient := kube.NewFakeClient()
	deploy := NewDeploy(params, fakeClient)

	eastwestGatewayValue := deploy.getEastWestGatewayIstioLabel()
	assert.Equal(t, eastwestGatewayValue, constants.EastWestGatewayName+"-"+params.MeshInstanceId)

	istiodGatewayValue := deploy.getIstiodGatewayName()
	assert.Equal(t, istiodGatewayValue, "istiod-gateway")

	istiodValue := deploy.getIstiodVsName()
	assert.Equal(t, istiodValue, "istiod-vs")

	err1 := deploy.AddNamespace(mockContext, labels)
	assert.Nil(t, err1)

	err2 := deploy.AddNamespace(mockContext, labels)
	assert.Nil(t, err2)

	uniqueName, err3 := deploy.getUniqueName()
	assert.Nil(t, err3)
	assert.Equal(t, uniqueName, strings.Join([]string{Region, ClusterName, CceClusterUuid, Version, Namespace}, "_"))

	cmdStr := "test-cmd"
	cmdValue := deploy.getIstioCtlWithNamespace(cmdStr)
	assert.Equal(t, cmdValue, cmdStr+fmt.Sprintf(" --istioNamespace %s", Namespace))
	deploy.DiscoverySelectorLabels = labels

	cmdValue = deploy.getIstioCtlWithNamespace(cmdStr)
	assert.Equal(t, cmdValue, cmdStr+fmt.Sprintf(" --istioNamespace %s", Namespace))

}

func buildMutatingWebhookConfiguration() *admissionregistrationv1.MutatingWebhookConfiguration {
	return &admissionregistrationv1.MutatingWebhookConfiguration{
		ObjectMeta: metav1.ObjectMeta{
			Name:            TestWebhook,
			ResourceVersion: "1",
		},
		Webhooks: []admissionregistrationv1.MutatingWebhook{
			{
				Name:         "aaa",
				ClientConfig: admissionregistrationv1.WebhookClientConfig{},
			},
		},
	}
}

func TestAddMutatingWebhookConfigurationWithMeshInstanceId(t *testing.T) {
	params := &Params{
		ConfigPath:              ConfigPath,
		Version:                 Version,
		Namespace:               Namespace,
		Region:                  Region,
		MeshInstanceId:          MeshInstanceId,
		ClusterName:             ClusterName,
		CceClusterUuid:          CceClusterUuid,
		DiscoverySelectorLabels: nil,
	}

	fakeClient := kube.NewFakeClient()
	deploy := NewDeploy(params, fakeClient)

	mockMutatingWebhookConfiguration := buildMutatingWebhookConfiguration()
	fakeClient.Kube().AdmissionregistrationV1().MutatingWebhookConfigurations().Create(context.TODO(), mockMutatingWebhookConfiguration, metav1.CreateOptions{})

	err := deploy.addMutatingWebhookConfigurationWithMeshInstanceId(mockContext, fakeClient, TestWebhook, params.MeshInstanceId)
	assert.Nil(t, err)
}

func buildThirdPartyJwtApiResourceList() []*metav1.APIResourceList {
	apiResourceList := []*metav1.APIResourceList{
		{
			APIResources: []metav1.APIResource{{Name: "serviceaccounts/token"}},
		},
	}
	return apiResourceList
}

func TestDeploy_InstallIstio(t *testing.T) {
	type args struct {
		ctx csmcontext.CsmContext
		eks bool
	}
	ctrl := gomock.NewController(t)
	cceService := mockCceService.NewMockClientInterface(ctrl)

	fakeClient := kube.NewFakeClient()

	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../")
	sourceTemplatesDir := path.Join(rootPath, constants.Templates)
	tempDir, err := os.MkdirTemp("/tmp", "test")
	if err != nil {
		t.Errorf("os.MkdirTemp error %v", err)
	}
	testRoot := path.Join(tempDir, constants.Templates)
	defer os.RemoveAll(tempDir)

	err = copy.Copy(sourceTemplatesDir, testRoot)
	if err != nil {
		t.Errorf("copy.Copy error %v", err)
	}
	viper.Set(meta.EksAccountIds, "0c0b3c9dbb6e41308d3bfd587d908922")

	params := Params{
		MeshInstanceId: MeshInstanceId,
		Version:        Version,
		Region:         Region,
		IsRemote:       false,
		ClusterName:    ClusterName,
		CceClusterUuid: CceClusterUuid,
		Namespace:      Namespace,
		ConfigPath:     sourceTemplatesDir,
	}
	viper.Set(IopStandaloneHub, "registry.baidubce.com/csm-offline")

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test-installIstio",
			args: args{
				ctx: mockContext,
				eks: true,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			deploy := &Deploy{
				cceService:      cceService,
				client:          fakeClient,
				Params:          &params,
				opt:             version.NewOption(),
				IopTemplatePath: path.Join(testRoot, constants.BaseIstioTemplate),
			}
			tt.args.ctx.Set(ContextIAMUser, &iam.User{
				ID:   "",
				Name: "",
				Domain: &iam.Domain{
					ID:   "0c0b3c9dbb6e41308d3bfd587d908922",
					Name: "",
				},
				Password: "",
			})

			if err := deploy.InstallIstio(tt.args.ctx, tt.args.eks); (err != nil) != tt.wantErr {
				t.Errorf("InstallIstio() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
