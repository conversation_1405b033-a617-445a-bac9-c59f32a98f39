package deploy

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewParams(t *testing.T) {
	version := "1.13.2"
	namespace := "istio-system"
	region := "bj"
	meshInstanceId := "csm-123456"
	clusterName := "istio-test01"
	clusterUuid := "cce-123456"
	want := &Params{
		Version:        version,
		Namespace:      namespace,
		Region:         region,
		MeshInstanceId: meshInstanceId,
		ClusterName:    clusterName,
		CceClusterUuid: clusterUuid,
	}

	got := NewParams(version, namespace, region, meshInstanceId, clusterName, clusterUuid)

	assert.Equal(t, got, want)
}

func TestNewIopParseParams(t *testing.T) {
	meshInstanceId := "csm-123456"
	clusterName := "istio-test01"
	network := "bj"
	tag := "1.13.2"
	hub := "xxx"

	want := &IopParseParams{
		Hub:            hub,
		Tag:            tag,
		MeshInstanceId: meshInstanceId,
		ClusterName:    clusterName,
		Network:        network,
	}
	got := NewIopParseParams(meshInstanceId, clusterName, network, tag, hub)
	assert.Equal(t, got, want)
}

func TestNewEastWestGatewayParseParams(t *testing.T) {
	eastWestGatewayIstioLabel := "test"
	network := "bj"
	tag := "1.13.2"
	hub := "xxx"

	want := &IopParseParams{
		Network:                   network,
		Tag:                       tag,
		Hub:                       hub,
		EastWestGatewayIstioLabel: eastWestGatewayIstioLabel,
	}
	got := NewEastWestGatewayParseParams(network, tag, hub, eastWestGatewayIstioLabel)
	assert.Equal(t, got, want)
}

func TestNewIstiodExposeGatewayVsParams(t *testing.T) {
	eastWestGatewayIstioLabel := "test"
	namespace := "istio-system"
	istiodGatewayName := "istiod-gateway"
	istiodVsName := "istiod-vs"
	want := &IstiodExposeGatewayVsParams{
		Namespace:                 namespace,
		IstiodGatewayName:         istiodGatewayName,
		IstiodVsName:              istiodVsName,
		EastWestGatewayIstioLabel: eastWestGatewayIstioLabel,
	}
	got := NewIstiodExposeGatewayVsParams(namespace, istiodGatewayName, istiodVsName, eastWestGatewayIstioLabel)
	assert.Equal(t, got, want)
}

func TestProxyResource(t *testing.T) {
	tests := []struct {
		name     string
		funcName func() *Resources
		expect   *Resources
	}{
		{
			name:     "test-NewCceDefaultProxyResources",
			funcName: NewCceDefaultProxyResources,
			expect: &Resources{
				Requests: cceProxyRequests,
				Limits:   cceProxyLimits,
			},
		},
		{
			name:     "test-NewCceDefaultInitProxyResources",
			funcName: NewCceDefaultInitProxyResources,
			expect: &Resources{
				Requests: cceInitProxyRequests,
				Limits:   cceInitProxyLimits,
			},
		},
		{
			name:     "test-NewEksDefaultProxyResources",
			funcName: NewEksDefaultProxyResources,
			expect: &Resources{
				Requests: eksProxyRequests,
				Limits:   eksProxyLimits,
			},
		},
		{
			name:     "test-NewEksDefaultInitProxyResources",
			funcName: NewEksDefaultInitProxyResources,
			expect: &Resources{
				Requests: eksInitProxyRequests,
				Limits:   eksInitProxyLimits,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.funcName()
			if !reflect.DeepEqual(tt.expect, got) {
				t.Errorf("NewCceResources got=%v, want=%v", got, tt.expect)
			}
		})
	}
}
