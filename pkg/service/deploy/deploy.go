package deploy

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path"
	"strings"
	"text/template"
	"time"

	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/spf13/viper"
	v1 "k8s.io/api/core/v1"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	service_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/multiprotocol"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/command"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/jwt"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"
)

type Deploy struct {
	cceService    cce.ClientInterface
	client        kube.Client
	PrimaryClient kube.Client
	opt           *version.Option
	*Params
	IopTemplatePath         string
	KubeConfigPath          string
	VpcKubeConfigPath       string // 添加 remote 集群时，用于生成 secret
	EastWestGatewayYamlPath string
}

// NewDeploy 使用 params 与 client 初始化 Deploy
func NewDeploy(params *Params, client kube.Client) *Deploy {
	return &Deploy{
		client:     client,
		Params:     params,
		opt:        version.NewOption(),
		cceService: cce.NewClientService(),
	}
}

// AddNamespace 创建 namespace
func (deploy *Deploy) AddNamespace(ctx csmContext.CsmContext, labels map[string]string) error {
	ns := &v1.Namespace{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Name:   deploy.Namespace,
			Labels: labels,
		},
	}
	var err error
	var namespace *v1.Namespace
	_, err = deploy.client.Kube().CoreV1().Namespaces().Create(context.TODO(), ns, metav1.CreateOptions{})
	if kubeErrors.IsAlreadyExists(err) {
		namespace, err = deploy.client.Kube().CoreV1().Namespaces().Get(context.TODO(), deploy.Namespace, metav1.GetOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("get %s namespace err %v", deploy.Namespace, err)
			return err
		}
		sourceLabel := namespace.Labels
		if sourceLabel == nil {
			sourceLabel = make(map[string]string)
		}
		for k, v := range labels {
			sourceLabel[k] = v
		}
		namespace.Labels = sourceLabel
		_, err = deploy.client.Kube().CoreV1().Namespaces().Update(context.TODO(), namespace, metav1.UpdateOptions{})
	}
	if err != nil {
		ctx.CsmLogger().Errorf("create or update %s namespace err %v", deploy.Namespace, err)
		return err
	}
	ctx.CsmLogger().Infof("create or update %s namespace successful", deploy.Namespace)
	return nil
}

// parseEastWestGateway 解析东西向网关模板
func (deploy *Deploy) parseEastWestGateway(ctx csmContext.CsmContext, eastWestGatewaySrcPath,
	eastWestGatewayDestinationPath string, iopParams *IopParseParams) error {
	tempValue, err := os.ReadFile(eastWestGatewaySrcPath)
	if err != nil {
		ctx.CsmLogger().Errorf("read east-west gateway templates %s error %v", eastWestGatewaySrcPath, err)
		return err
	}
	tmpl, err := template.New("istio-eastwestgateway").Parse(string(tempValue))
	if err != nil {
		ctx.CsmLogger().Errorf("istio east-west gateway Parse failed: %v", err)
		return err
	}

	var buf bytes.Buffer
	if err = tmpl.Execute(&buf, iopParams); err != nil {
		ctx.CsmLogger().Errorf("tmpl execute failed: %v", err)
		return err
	}
	err = file.RewriteFile(ctx, eastWestGatewayDestinationPath, buf.Bytes())
	if err != nil {
		return err
	}
	deploy.EastWestGatewayYamlPath = eastWestGatewayDestinationPath
	return nil
}

// parseIopTemplate 解析 iop 安装模板
func (deploy *Deploy) parseIopTemplate(ctx csmContext.CsmContext, iopTemplateSrcPath,
	iopParseDestinationPath string, iopParams *IopParseParams) error {
	tempValue, err := os.ReadFile(iopTemplateSrcPath)
	if err != nil {
		ctx.CsmLogger().Errorf("read istio templates %s error %v", iopTemplateSrcPath, err)
		return err
	}
	tmpl, err := template.New("istio").Parse(string(tempValue))
	if err != nil {
		ctx.CsmLogger().Errorf("istio templates Parse failed: %v", err)
		return err
	}

	var buf bytes.Buffer
	if err = tmpl.Execute(&buf, iopParams); err != nil {
		ctx.CsmLogger().Errorf("tmpl execute failed: %v", err)
		return err
	}
	err = file.RewriteFile(ctx, iopParseDestinationPath, buf.Bytes())
	if err != nil {
		return err
	}
	deploy.IopTemplatePath = iopParseDestinationPath
	return nil
}

func (deploy *Deploy) writeKubeConfig(ctx csmContext.CsmContext) (err error) {
	kubeConfig, err := deploy.cceService.GetCCEClusterKubeConfigByClusterUUID(ctx, deploy.Region, deploy.CceClusterUuid,
		cce_v2.KubeConfigTypeInternal, meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("get kubeconfig yaml error %v", err)
		return err
	}
	uniqueName, err := deploy.getUniqueName()
	if err != nil {
		return err
	}
	kubeConfigName := uniqueName + ".yaml"
	kubeConfigPath := path.Join(deploy.ConfigPath, constants.BaseIstioTemplate, deploy.Version, kubeConfigName)
	err = file.RewriteFile(ctx, kubeConfigPath, kubeConfig)
	if err != nil {
		return err
	}
	deploy.KubeConfigPath = kubeConfigPath

	// 添加 remote 集群时，需要用到 VPC 地址
	if deploy.IsRemote {
		// TODO: 这里的实例类型需要注意下
		vpcKubeConfig, err := deploy.cceService.GetCCEClusterKubeConfigByClusterUUID(ctx, deploy.Region,
			deploy.CceClusterUuid, cce_v2.KubeConfigTypeVPC, meta.StandaloneMeshType)
		if err != nil {
			ctx.CsmLogger().Errorf("get vpc kubeconfig yaml error %v", err)
			return err
		}
		vpcKubeConfigName := uniqueName + "_vpc.yaml"
		vpcKubeConfigPath := path.Join(deploy.ConfigPath, constants.BaseIstioTemplate, deploy.Version, vpcKubeConfigName)
		err = file.RewriteFile(ctx, vpcKubeConfigPath, vpcKubeConfig)
		if err != nil {
			return err
		}
		deploy.VpcKubeConfigPath = vpcKubeConfigPath
	}
	return nil
}

// getUniqueName 根据 istio 地区、版本、名称拼接唯一标识
func (deploy *Deploy) getUniqueName() (string, error) {
	if deploy.Region == "" {
		return "", errors.New("istio region is null")
	}
	if deploy.ClusterName == "" {
		return "", errors.New("istio clusterName is null")
	}
	if deploy.CceClusterUuid == "" {
		return "", errors.New("istio CceClusterUuid is null")
	}
	if deploy.Version == "" {
		return "", errors.New("istio Version is null")
	}
	if deploy.Namespace == "" {
		return "", errors.New("istio namespace is null")
	}
	uniqueName := deploy.Region + "_" + deploy.ClusterName + "_" + deploy.CceClusterUuid + "_" + deploy.Version + "_" + deploy.Namespace
	return uniqueName, nil
}

func (deploy *Deploy) getIstioCtlWithNamespace(cmdStr string) string {
	namespace := constants.IstioNamespace
	if len(deploy.DiscoverySelectorLabels) > 0 {
		namespace = deploy.Namespace
	}
	return object.GetIstioCtlWithNamespace(namespace, cmdStr)
}

// UnInstallIstio 卸载 istio 集群
func (deploy *Deploy) UnInstallIstio(ctx csmContext.CsmContext, eks bool) error {
	if eks {
		err := deploy.parseEKSIopTemplate(ctx)
		if err != nil {
			return err
		}
	} else {
		err := deploy.parseCCEIopTemplate(ctx)
		if err != nil {
			return err
		}
	}
	istioCtlBin := path.Join(deploy.ConfigPath, constants.BaseIstioTemplate, deploy.Version,
		constants.BaseIstioBin, util.GetIstioCtl(ctx))
	installCmd := istioCtlBin + fmt.Sprintf(" manifest generate -f  %s ", deploy.IopTemplatePath)
	outStr, errStr, err := command.ExecCmdOut(ctx, installCmd)
	if len(errStr) > 0 || err != nil {
		return fmt.Errorf("istioctl manifest generate err %v", err)
	}
	objectsList, err := object.ManifestK8sObject(ctx, string(outStr))
	// todo check that the k8s cluster exists for istio crd
	objectsList = object.GetsK8sObjectWithExcludeGroupKind(ctx, objectsList, []string{object.CustomResourceDefinitionStr})
	if err != nil {
		ctx.CsmLogger().Errorf("uninstall istio error %v", err)
		return err
	}
	err = kube.DeleteResources(ctx, deploy.client, objectsList)
	return err
}

// InstallIstio 安装 istio 集群
func (deploy *Deploy) InstallIstio(ctx csmContext.CsmContext, eks bool) error {
	eksProfile := viper.GetString(meta.EksProfile)
	if eks && (deploy.IsRemote || strings.EqualFold(eksProfile, meta.EksProfileGrey)) {
		// 临时逻辑，一站式环境主集群用 CCE 模式安装，从集群用 EKS 模式安装
		// 即，一站式环境的主集群当前默认为 CCE 集群，从集群默认为 EKS 集群
		err := deploy.parseEKSIopTemplate(ctx)
		if err != nil {
			return err
		}
	} else {
		err := deploy.parseCCEIopTemplate(ctx)
		if err != nil {
			return err
		}
	}
	jwtPolicy := jwt.SupportedJWTPolicy(deploy.client.Kube())
	jwtPolicyValue := jwt.FirstPartyJWT
	if jwtPolicy {
		jwtPolicyValue = jwt.ThirdPartyJWT
	}
	istioCtlBin := path.Join(deploy.ConfigPath, constants.BaseIstioTemplate, deploy.Version,
		constants.BaseIstioBin, util.GetIstioCtl(ctx))
	installCmd := istioCtlBin + fmt.Sprintf(" manifest generate --set values.global.jwtPolicy=%s -f  %s ",
		jwtPolicyValue, deploy.IopTemplatePath)
	outStr, errStr, err := command.ExecCmdOut(ctx, installCmd)
	if len(errStr) > 0 || err != nil {
		return fmt.Errorf("istioctl manifest generate err %v", err)
	}
	objectsList, err := object.ManifestK8sObject(ctx, string(outStr))
	if err != nil {
		return err
	}

	excludeObjectsList := object.GetsK8sObjectWithExcludeGroupKind(ctx, objectsList, []string{object.EnvoyFilterNetworkingAPIGroupName,
		object.MutatingWebhookConfigurationGroupKindStr, object.ValidatingWebhookConfigurationGroupKindStr})

	err = kube.CreateResources(ctx, deploy.client, excludeObjectsList)
	if err != nil {
		return err
	}

	// 等待 istiod deployment 与 svc 就绪后，创建 EnvoyFilter
	err = kube.WaitServiceReady(ctx, deploy.client, constants.IstiodServiceName, deploy.Namespace)
	if err != nil {
		ctx.CsmLogger().Errorf(fmt.Sprintf("waiting %s ready err %v", constants.IstiodServiceName, err))
		return err
	}
	err = kube.WaitDeploymentReady(ctx, deploy.client, constants.IstiodDeploymentName, deploy.Namespace)
	if err != nil {
		return err
	}

	envoyFilters := object.GetsK8sObjectWithGroupKind(ctx, objectsList, object.EnvoyFilterNetworkingAPIGroupName)
	err = kube.CreateResources(ctx, deploy.client, envoyFilters)
	if err != nil {
		return err
	}

	validatingWebhookConfiguration := object.GetsK8sObjectWithGroupKind(ctx, objectsList, object.ValidatingWebhookConfigurationGroupKindStr)
	mutatingWebhookConfiguration := object.GetsK8sObjectWithGroupKind(ctx, objectsList, object.MutatingWebhookConfigurationGroupKindStr)
	// 对于内部 eks，更新 validating 与 mutating webhook
	if eks {
		// todo 考虑是否有更好的方案用于内部 eks 用户
		// 目前采取的方案是 mutatingwebhookconfiguration 中增加一组 webhook mesh.instance-id.eks 用于网关的 auto 镜像替换
		labels := deploy.DiscoverySelectorLabels
		for vwcIndex, o := range validatingWebhookConfiguration {
			vwc, vwcErr := object.UpdateValidatingWebhookConfigurationWithLabels(ctx, o, labels)
			if vwcErr != nil {
				return vwcErr
			}
			validatingWebhookConfiguration[vwcIndex] = vwc
		}
		meshInstanceId := deploy.MeshInstanceId
		for mwcIndex, o := range mutatingWebhookConfiguration {
			mwc, mwcErr := object.UpdateMutatingWebhookConfigurationWithLabelsAndMeshInstanceId(ctx, o, labels, meshInstanceId)
			if mwcErr != nil {
				return mwcErr
			}
			mutatingWebhookConfiguration[mwcIndex] = mwc
		}
	}

	ctx.CsmLogger().Infof("creating %s", object.ValidatingWebhookConfigurationGroupKindStr)
	err = kube.CreateResources(ctx, deploy.client, validatingWebhookConfiguration)
	if err != nil {
		ctx.CsmLogger().Errorf("creating %s err %v", object.ValidatingWebhookConfigurationGroupKindStr, err)
		return err
	}
	ctx.CsmLogger().Infof("creating %s success", object.ValidatingWebhookConfigurationGroupKindStr)

	ctx.CsmLogger().Infof("creating %s", object.MutatingWebhookConfigurationGroupKindStr)
	err = kube.CreateResources(ctx, deploy.client, mutatingWebhookConfiguration)
	if err != nil {
		ctx.CsmLogger().Infof("creating %s error %v", object.MutatingWebhookConfigurationGroupKindStr, err)
		return err
	}
	ctx.CsmLogger().Infof("creating %s success", object.MutatingWebhookConfigurationGroupKindStr)

	if eks {
		// 增加一组 mesh.instance-id.istio.io webhook
		meshInstanceId := deploy.MeshInstanceId
		name := util.GetStrWithSplit([]string{constants.IstioSidecarInjectorMutatingWebhookConfiguration, deploy.Namespace},
			constants.SplitSlash)
		err = deploy.addMutatingWebhookConfigurationWithMeshInstanceId(ctx, deploy.client, name, meshInstanceId)
		if err != nil {
			return err
		}
		ctx.CsmLogger().Infof("updating %s success", object.MutatingWebhookConfigurationGroupKindStr)
	}

	// 对于内部 eks，修改镜像 并 新增env参数
	if eks {
		oldDeploy, err := deploy.client.Kube().AppsV1().Deployments(deploy.Namespace).Get(context.TODO(),
			constants.IstiodDeploymentName, metav1.GetOptions{})
		if err != nil {
			return err
		}
		container := &oldDeploy.Spec.Template.Spec.Containers[0]
		container.Image = deploy.opt.Standalone.IopHub + "/pilot:baidu-cnap-" + deploy.Version
		value := v1.EnvVar{
			Name:  constants.EnvName,
			Value: constants.EnvValue,
		}
		container.Env = append(container.Env, value)
		_, err = json.MarshalIndent(container.Env, "", "\t")
		if err != nil {
			return err
		}
		ctx.CsmLogger().Infof("eks container is %s", container)
		_, err = deploy.client.Kube().AppsV1().Deployments(deploy.Namespace).
			Update(context.TODO(), oldDeploy, metav1.UpdateOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("Update deploy env error %v", err)
		}
	}
	return err
}

func (deploy *Deploy) addMutatingWebhookConfigurationWithMeshInstanceId(ctx csmContext.CsmContext, c kube.Client,
	name, meshInstanceId string) error {
	err := kube.WaitMutatingWebhookConfigurationReady(ctx, c, name)
	if err != nil {
		return err
	}
	for i := 0; i < constants.MaxRetryNums; i++ {
		ctx.CsmLogger().Infof("get MutatingWebhookConfigurations for %v times", i+1)
		mwc, getErr := c.Kube().AdmissionregistrationV1().MutatingWebhookConfigurations().Get(context.TODO(),
			name, metav1.GetOptions{})
		if getErr != nil {
			ctx.CsmLogger().Warnf("get MutatingWebhookConfigurations %s error %v", name, getErr)
			continue
		}
		webhooks := mwc.Webhooks
		if len(webhooks) == 0 {
			ctx.CsmLogger().Warnf("MutatingWebhookConfiguration length must be greater than zero")
			continue
		}
		mutatingWebhook := webhooks[0]
		mutatingWebhook.Name = constants.MeshMutatingWebhookConfigurationInstanceId
		matchExpressions := map[string]string{constants.MeshInstanceId: meshInstanceId}
		ls := service_meta.GetDiscoverySelectorsWithMap(service_meta.LabelSelectorOpIn, matchExpressions, nil)
		mutatingWebhook.NamespaceSelector = ls
		// 忽略错误
		// ignore := registrationv1.Ignore
		// mutatingWebhook.FailurePolicy = &ignore
		webhooks = append(webhooks, mutatingWebhook)
		mwc.Webhooks = webhooks
		_, updateErr := c.Kube().AdmissionregistrationV1().MutatingWebhookConfigurations().Update(context.TODO(),
			mwc, metav1.UpdateOptions{})
		if updateErr != nil {
			ctx.CsmLogger().Warnf("update MutatingWebhookConfigurations %s error %v", name, updateErr)
		} else {
			ctx.CsmLogger().Infof("update MutatingWebhookConfigurations %s success", name)
			break
		}
		time.Sleep(constants.RetryInternal)
	}
	return err
}

func (deploy *Deploy) getEastWestGatewayIstioLabel() string {
	return util.GetStrWithSplit([]string{constants.EastWestGatewayName, deploy.MeshInstanceId}, constants.SplitSlash)
}

func (deploy *Deploy) getIstiodGatewayName() string {
	return constants.IstiodGatewayName
}

func (deploy *Deploy) getIstiodVsName() string {
	return constants.IstiodVsName
}

// InstallCCEEastWestGateway 部署东西向网关
func (deploy *Deploy) InstallCCEEastWestGateway(ctx csmContext.CsmContext) error {
	ctx.CsmLogger().Infof("install cce east-west gateway with K8sObject")
	versionBase := path.Join(deploy.ConfigPath, constants.BaseIstioTemplate, deploy.Version)
	eastWestGatewaySrcPath := path.Join(versionBase, constants.StandaloneTemplate, constants.EastWestGatewayTmplName)
	uniqueName, err := deploy.getUniqueName()
	if err != nil {
		return err
	}
	EastWestGatewayPath := uniqueName + "_cce_" + constants.EastWestGatewayYaml

	eastWestGatewayDestinationPath := path.Join(versionBase, EastWestGatewayPath)
	eastWestGatewayIstioLabel := deploy.getEastWestGatewayIstioLabel()
	cceGateWayParams := NewEastWestGatewayParseParams(deploy.Region, deploy.Version, deploy.opt.Standalone.IopHub,
		eastWestGatewayIstioLabel)
	cceGateWayParams.Namespace = deploy.Namespace
	cceGateWayParams.MeshInstanceId = deploy.MeshInstanceId
	err = deploy.parseEastWestGateway(ctx, eastWestGatewaySrcPath, eastWestGatewayDestinationPath, cceGateWayParams)
	if err != nil {
		return err
	}

	istioCtlBin := path.Join(versionBase, constants.BaseIstioBin, util.GetIstioCtl(ctx))
	installEastWestGatewayCmd := istioCtlBin + fmt.Sprintf(" manifest generate -f %s", deploy.EastWestGatewayYamlPath)
	installEastWestGatewayCmd = deploy.getIstioCtlWithNamespace(installEastWestGatewayCmd)
	outStr, errStr, err := command.ExecCmdOut(ctx, installEastWestGatewayCmd)
	if len(errStr) > 0 || err != nil {
		return fmt.Errorf("istioctl manifest generate err %v", err)
	}
	objectsList, err := object.ManifestK8sObject(ctx, string(outStr))
	if err != nil {
		return err
	}
	err = kube.CreateResources(ctx, deploy.client, objectsList)
	if err != nil {
		return err
	}

	// 通过网关暴露 istiod 服务
	exposeIstiodeGatewayVsPath := path.Join(versionBase, constants.StandaloneTemplate, constants.ExposeIstiodTmplName)
	istiodExposeGatewayVsParams := NewIstiodExposeGatewayVsParams(deploy.Namespace, deploy.getIstiodGatewayName(),
		deploy.getIstiodVsName(), eastWestGatewayIstioLabel)
	yamlValues, err := deploy.parseIstiodeGatewayVs(ctx, exposeIstiodeGatewayVsPath, istiodExposeGatewayVsParams)
	if err != nil {
		return err
	}
	err = deploy.deployGatewayVs(ctx, string(yamlValues))
	return err
}

func (deploy *Deploy) deployGatewayVs(ctx csmContext.CsmContext, contents string) error {
	objectsList, err := object.ManifestK8sObject(ctx, contents)
	if err != nil {
		return err
	}
	err = kube.CreateResources(ctx, deploy.client, objectsList)
	if err != nil {
		return err
	}
	return nil
}

func (deploy *Deploy) parseIstiodeGatewayVs(ctx csmContext.CsmContext, istiodeGatewayVsPath string,
	params *IstiodExposeGatewayVsParams) ([]byte, error) {
	tempValue, err := os.ReadFile(istiodeGatewayVsPath)
	if err != nil {
		ctx.CsmLogger().Errorf("read expose istiod templates %s error %v", istiodeGatewayVsPath, err)
		return nil, err
	}
	tmpl, err := template.New("expose-istiod").Parse(string(tempValue))
	if err != nil {
		ctx.CsmLogger().Errorf("istio east-west gateway Parse failed: %v", err)
		return nil, err
	}

	var buf bytes.Buffer
	if err = tmpl.Execute(&buf, params); err != nil {
		ctx.CsmLogger().Errorf("tmpl execute failed: %v", err)
		return nil, err
	}
	return buf.Bytes(), nil
}

// RemoveAllTempFile remove temp iop install yaml and k8s kube config
func (deploy *Deploy) RemoveAllTempFile(ctx csmContext.CsmContext) error {
	iopInstallPath := deploy.IopTemplatePath
	if len(iopInstallPath) > 0 {
		err := file.RemoveFile(iopInstallPath)
		if err != nil {
			return err
		}
	} else {
		ctx.CsmLogger().Warnf("iop install yaml path is nil")
	}
	eastWestGatewayYamlPath := deploy.EastWestGatewayYamlPath
	if len(eastWestGatewayYamlPath) > 0 {
		err := file.RemoveFile(eastWestGatewayYamlPath)
		if err != nil {
			return err
		}
	} else {
		ctx.CsmLogger().Warnf("iop east west gateway yaml path is nil")
	}
	kubeConfigPath := deploy.KubeConfigPath
	if len(kubeConfigPath) > 0 {
		err := file.RemoveFile(kubeConfigPath)
		if err != nil {
			return err
		}
	} else {
		ctx.CsmLogger().Warnf("iop kube config yaml path is nil")
	}
	return nil
}

func (deploy *Deploy) UnDeployIstio(ctx csmContext.CsmContext, eks bool) error {
	// uninstall istio
	err := deploy.UnInstallIstio(ctx, eks)
	if err != nil {
		return err
	}

	if deploy.IsRemote {
		// 不再允许 Primary 集群访问 Remote 集群 API Server
		err = deploy.DenyPrimaryAccess(ctx)
		if err != nil {
			ctx.CsmLogger().Errorf("Remove primary cluster access error %v", err)
			return err
		}
	}

	// remove namespace
	err = deploy.RemoveNameSpace(ctx)
	if err != nil {
		return err
	}

	// remove temp files
	err = deploy.RemoveAllTempFile(ctx)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("uninstall region=%s clusterName=%s clusterUuid=%s namespace=%s successful",
		deploy.Region, deploy.ClusterName, deploy.CceClusterUuid, deploy.Namespace)
	return nil
}

// RemoveNameSpace remove all "istio-ca-root-cert" in namespace
func (deploy *Deploy) RemoveNameSpace(ctx csmContext.CsmContext) error {
	// TODO we need to consider eks
	cmErr := kube.DeleteAllIstioCARootCert(ctx, deploy.client)
	deploy.client.Kube().CoreV1().Namespaces().Delete(context.TODO(), deploy.Namespace, metav1.DeleteOptions{})
	nsErr := wait.PollImmediate(constants.PollInternal, constants.Internal, func() (bool, error) {
		ctx.CsmLogger().Infof("delete %s namespace", deploy.Namespace)
		_, err := deploy.client.Kube().CoreV1().Namespaces().Get(context.TODO(), deploy.Namespace, metav1.GetOptions{})
		if kubeErrors.IsNotFound(err) {
			return true, nil // done
		}
		if err != nil {
			return false, err // stop wait with error
		}
		return false, nil
	})
	if nsErr != nil {
		ns, err := deploy.client.Kube().CoreV1().Namespaces().Get(context.TODO(), deploy.Namespace, metav1.GetOptions{})
		if ns.Status.Phase == v1.NamespaceTerminating {
			ctx.CsmLogger().Infof("delete %s namespace with patch force...", deploy.Namespace)
			err = kube.DeleteNamespaceWithFinalizers(ctx, deploy.client, ns)
			if err != nil {
				return err
			}
			ctx.CsmLogger().Infof("delete %s namespace with patch force ok...", deploy.Namespace)
			return nil
		}
		return err
	}
	ctx.CsmLogger().Infof("delete %s namespace successful", deploy.Namespace)
	return cmErr
}

func (deploy *Deploy) parseCCEIopTemplate(ctx csmContext.CsmContext) error {
	// parse iop templates yaml
	versionBase := path.Join(deploy.ConfigPath, constants.BaseIstioTemplate, deploy.Params.Version)
	iopSrcPath := path.Join(versionBase, constants.StandaloneTemplate, constants.IopTemplateTmplName)
	uniqueName, err := deploy.getUniqueName()
	if err != nil {
		return err
	}
	iopTemplateName := fmt.Sprintf("istio_iop_cce_%s.yaml", uniqueName)
	tempValuePath := path.Join(versionBase, iopTemplateName)
	// TODO: 构造待优化
	cceIopTemplate := NewIopParseParams(deploy.MeshInstanceId, deploy.Region+"-"+deploy.CceClusterUuid,
		deploy.Region, deploy.Version, deploy.opt.Standalone.IopHub)
	// 表示开启选择性服务发现
	if len(deploy.DiscoverySelectorLabels) > 0 {
		labels := make(map[string]string, 0)
		labels[constants.MeshInstanceId] = deploy.MeshInstanceId
		cceIopTemplate.DiscoverySelectorLabels = labels
		cceIopTemplate.MatchExpressions = service_meta.NewLabelSelectorRequirementWithMap(
			service_meta.LabelSelectorOpIn, deploy.DiscoverySelectorLabels)
		cceIopTemplate.AccessLogFile = false
	}
	cceIopTemplate.Proxy = NewCceDefaultProxyResources()
	cceIopTemplate.InitProxy = NewCceDefaultInitProxyResources()
	// 支持多协议
	if deploy.MultiProtocolEnabled {
		cceIopTemplate.ProxyImage = multiprotocol.AerakiProxyImage
	} else {
		cceIopTemplate.ProxyImage = multiprotocol.DefaultProxyImage
	}
	// 开启链路追踪
	if deploy.TraceEnabled {
		cceIopTemplate.SamplingRate = deploy.SamplingRate
		cceIopTemplate.Address = deploy.Address
	} else {
		cceIopTemplate.SamplingRate = 1
		cceIopTemplate.Address = constants.Zipkin + "." + deploy.Namespace + constants.Port
	}
	cceIopTemplate.Namespace = deploy.Namespace
	if deploy.IsRemote {
		// TODO: 做判断，如果主集群没有完成安装东西网关，则等待或返回
		svc, err := deploy.PrimaryClient.Kube().CoreV1().Services(deploy.Namespace).
			Get(context.TODO(), constants.EastWestGatewaySvc, metav1.GetOptions{})
		if err != nil {
			return err
		}
		cceIopTemplate.DiscoveryAddress = svc.Status.LoadBalancer.Ingress[0].IP
		// 获取主集群配额
		cm, err := deploy.PrimaryClient.Kube().CoreV1().ConfigMaps(deploy.Namespace).
			Get(context.TODO(), constants.ConfigMapName, metav1.GetOptions{})
		if err != nil {
			return err
		}
		values := cm.Data["values"]
		cmv := &ConfigMapValues{}
		err = json.Unmarshal([]byte(values), cmv)
		if err != nil {
			return err
		}
		if cmv.Global != nil && cmv.Global.Proxy != nil && cmv.Global.Proxy.Resources != nil {
			if cmv.Global.Proxy.Resources.Limits != nil {
				cceIopTemplate.Proxy.Limits = cmv.Global.Proxy.Resources.Limits
			}
			if cmv.Global.Proxy.Resources.Requests != nil {
				cceIopTemplate.Proxy.Requests = cmv.Global.Proxy.Resources.Requests
			}
		}
	}
	err = deploy.parseIopTemplate(ctx, iopSrcPath, tempValuePath, cceIopTemplate)
	return err
}

func (deploy *Deploy) AllowPrimaryAccess(ctx csmContext.CsmContext) error {
	if deploy.KubeConfigPath == "" {
		return errors.New("kubeconfig path is nil")
	}
	if deploy.PrimaryClient == nil {
		return errors.New("primary k8s client is nil")
	}

	remoteSecretName := deploy.Region + "-" + deploy.CceClusterUuid // TODO: 待优化
	restConfig, err := clientcmd.BuildConfigFromFlags("", deploy.VpcKubeConfigPath)
	if err != nil {
		return fmt.Errorf("failed to create k8s rest client: %s", err)
	}
	vpcAPIServer := restConfig.Host

	// TODO: 考虑并发情况
	istioCtlBin := path.Join(deploy.ConfigPath, constants.BaseIstioTemplate, deploy.Version,
		constants.BaseIstioBin, util.GetIstioCtl(ctx))
	installCmd := istioCtlBin + fmt.Sprintf(" x create-remote-secret --kubeconfig=%s --name=%s --server=%s -n %s",
		deploy.KubeConfigPath, remoteSecretName, vpcAPIServer, deploy.Namespace)
	out, _, err := command.ExecCmdOut(ctx, installCmd)
	if err != nil {
		ctx.CsmLogger().Errorf("install istio error err %v", err)
		return err
	}

	ctx.CsmLogger().Infof("AllowPrimaryAccess create-remote-secret generate kube config successful")

	err = kube.CreateObject(ctx, deploy.PrimaryClient, out)
	if err != nil {
		return err
	}

	return nil
}

func (deploy *Deploy) DenyPrimaryAccess(ctx csmContext.CsmContext) error {
	if deploy.PrimaryClient == nil {
		return errors.New("primary k8s client is nil")
	}

	remoteSecretName := deploy.Region + "-" + deploy.CceClusterUuid // TODO: 待优化
	err := deploy.PrimaryClient.Kube().CoreV1().Secrets(deploy.Namespace).Delete(
		context.TODO(), constants.IstioRemoteSecretPrefix+remoteSecretName, metav1.DeleteOptions{})
	if kubeErrors.IsNotFound(err) {
		return nil // done
	}
	if err != nil {
		return err
	}
	return nil
}

func (deploy *Deploy) DeployIstio(ctx csmContext.CsmContext) (err error) {
	// todo kubeconfig use k8s-resource instead of kubeconfig
	err = deploy.writeKubeConfig(ctx)
	if err != nil {
		return err
	}

	if deploy.IsRemote {
		// 允许 Primary 集群访问 Remote 集群 API Server
		err = deploy.AllowPrimaryAccess(ctx)
		if err != nil {
			ctx.CsmLogger().Errorf("Allow primary cluster access error %v", err)
			return err
		}
	}

	// install istio
	err = deploy.InstallIstio(ctx, false)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("DeployIstio install istio success with meshInstanceId=%s,namespace=%s",
		deploy.MeshInstanceId, deploy.Namespace)

	// 只有在安装主集群或跨网络从集群时，才需要安装东西网关
	if !deploy.IsRemote || deploy.Region != deploy.InstanceRegion {
		// install east west gateway
		err = deploy.InstallCCEEastWestGateway(ctx)
		if err != nil {
			return err
		}
		ctx.CsmLogger().Infof("DeployIstio install istio-eastwestgateway success with meshInstanceId=%s,namespace=%s",
			deploy.MeshInstanceId, deploy.Namespace)
	}

	// remove temp files
	err = deploy.RemoveAllTempFile(ctx)
	if err != nil {
		return err
	}

	return nil
}

func (deploy *Deploy) DeployEksIstio(ctx csmContext.CsmContext) error {
	// todo kubeconfig use k8s-resource instead of kubeconfig
	err := deploy.writeKubeConfig(ctx)
	if err != nil {
		return err
	}

	if deploy.IsRemote {
		// 允许 Primary 集群访问 Remote 集群 API Server
		err = deploy.AllowPrimaryAccess(ctx)
		if err != nil {
			ctx.CsmLogger().Errorf("Allow primary cluster access error %v", err)
			return err
		}
	}
	// install istio
	err = deploy.InstallIstio(ctx, true)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("DeployEksIstio install istio success with meshInstanceId=%s,namespace=%s",
		deploy.MeshInstanceId, deploy.Namespace)
	// 只有在安装主集群时，才需要安装东西网关
	// 注意：此处与 CCE 不一样
	if !deploy.IsRemote {
		// install east west gateway
		// 短期方案：一站式环境主集群为 CCE 集群，因此网关也需要按照 CCE 方式安装
		// err = deploy.InstallEKSEastWestGateway()
		eksProfile := viper.GetString(meta.EksProfile)
		if strings.EqualFold(eksProfile, meta.EksProfileGrey) {
			err = deploy.InstallEKSEastWestGateway(ctx)
		} else {
			err = deploy.InstallCCEEastWestGateway(ctx)
		}
		if err != nil {
			return err
		}
		ctx.CsmLogger().Infof("DeployEksIstio install istio-eastwestgateway success with meshInstanceId=%s,namespace=%s",
			deploy.MeshInstanceId, deploy.Namespace)
	}

	// remove temp files
	err = deploy.RemoveAllTempFile(ctx)
	if err != nil {
		return err
	}

	return nil
}

func (deploy *Deploy) parseEKSIopTemplate(ctx csmContext.CsmContext) error {
	// parse iop templates yaml
	versionBase := path.Join(deploy.ConfigPath, constants.BaseIstioTemplate, deploy.Params.Version)
	iopSrcPath := path.Join(versionBase, constants.EksIopTemplate, constants.EksIopTemplateTmplName)
	uniqueName, err := deploy.getUniqueName()
	if err != nil {
		return err
	}
	iopTemplateName := fmt.Sprintf("istio_iop_eks_%s.yaml", uniqueName)
	tempValuePath := path.Join(versionBase, iopTemplateName)
	eksIopTemplate := NewIopParseParams(deploy.MeshInstanceId, deploy.Region+"-"+deploy.CceClusterUuid,
		constants.EksNetWork, deploy.Version, deploy.opt.Standalone.IopHub)
	// 表示开启选择性服务发现
	if len(deploy.DiscoverySelectorLabels) > 0 {
		labels := make(map[string]string, 0)
		labels[constants.MeshInstanceId] = deploy.MeshInstanceId
		eksIopTemplate.DiscoverySelectorLabels = labels
		eksIopTemplate.MatchExpressions = service_meta.NewLabelSelectorRequirementWithMap(service_meta.LabelSelectorOpIn,
			deploy.DiscoverySelectorLabels)
		eksIopTemplate.AccessLogFile = false
	}
	eksIopTemplate.Proxy = NewEksDefaultProxyResources()
	eksIopTemplate.InitProxy = NewEksDefaultInitProxyResources()
	eksIopTemplate.Namespace = deploy.Namespace
	// 开启链路追踪
	if deploy.TraceEnabled {
		eksIopTemplate.SamplingRate = deploy.SamplingRate
		eksIopTemplate.Address = deploy.Address
	} else {
		eksIopTemplate.SamplingRate = 1
		eksIopTemplate.Address = constants.Zipkin + "." + deploy.Namespace + constants.Port
	}
	if deploy.IsRemote {
		svc, err := deploy.PrimaryClient.Kube().CoreV1().Services(deploy.Namespace).
			Get(context.TODO(), constants.EastWestGatewaySvc, metav1.GetOptions{})
		if err != nil {
			return err
		}
		eksIopTemplate.DiscoveryAddress = svc.Status.LoadBalancer.Ingress[0].IP
		// EKS 不再获取主集群配额，配额格式 CCE 和 EKS 不同
		// 获取主集群配额
		// cm, err := deploy.PrimaryClient.Kube().CoreV1().ConfigMaps(deploy.Namespace).
		// 	Get(context.TODO(), constants.ConfigMapName, metav1.GetOptions{})
		// if err != nil {
		// 	return err
		// }
		// values := cm.Data["values"]
		// cmv := &ConfigMapValues{}
		// err = json.Unmarshal([]byte(values), cmv)
		// if err != nil {
		// 	return err
		// }
		// if cmv.Global != nil && cmv.Global.Proxy != nil && cmv.Global.Proxy.Resources != nil {
		// 	if cmv.Global.Proxy.Resources.Limits != nil {
		// 		eksIopTemplate.Proxy.Limits = cmv.Global.Proxy.Resources.Limits
		// 	}
		// 	if cmv.Global.Proxy.Resources.Requests != nil {
		// 		eksIopTemplate.Proxy.Requests = cmv.Global.Proxy.Resources.Requests
		// 	}
		// }
	}
	err = deploy.parseIopTemplate(ctx, iopSrcPath, tempValuePath, eksIopTemplate)
	if err != nil {
		return err
	}
	return nil
}

// InstallEKSEastWestGateway 部署 EKS 东西向网关
func (deploy *Deploy) InstallEKSEastWestGateway(ctx csmContext.CsmContext) error {
	ctx.CsmLogger().Infof("install eks east-west gateway")
	versionBase := path.Join(deploy.ConfigPath, constants.BaseIstioTemplate, deploy.Params.Version)
	eastWestGatewaySrcPath := path.Join(versionBase, constants.EksIopTemplate, constants.EksEastWestGatewayTmplName)
	uniqueName, err := deploy.getUniqueName()
	if err != nil {
		return err
	}
	EastWestGatewayName := uniqueName + "_eks_" + constants.EastWestGatewayYaml
	EastWestGatewayDestinationPath := path.Join(versionBase, EastWestGatewayName)

	eastWestGatewayIstioLabel := deploy.getEastWestGatewayIstioLabel()
	eksGateway := NewEastWestGatewayParseParams(constants.EksNetWork, deploy.Version, deploy.opt.Standalone.IopHub,
		eastWestGatewayIstioLabel)
	eksGateway.Namespace = deploy.Namespace
	err = deploy.parseEastWestGateway(ctx, eastWestGatewaySrcPath, EastWestGatewayDestinationPath, eksGateway)
	if err != nil {
		return err
	}
	istioCtlBin := path.Join(versionBase, constants.BaseIstioBin, util.GetIstioCtl(ctx))
	installEastWestGatewayCmd := istioCtlBin + fmt.Sprintf(" manifest generate -f %s", deploy.EastWestGatewayYamlPath)
	installEastWestGatewayCmd = deploy.getIstioCtlWithNamespace(installEastWestGatewayCmd)
	outStr, errStr, err := command.ExecCmdOut(ctx, installEastWestGatewayCmd)
	if len(errStr) > 0 || err != nil {
		return fmt.Errorf("istioctl manifest generate err %v", err)
	}
	objectsList, err := object.ManifestK8sObject(ctx, string(outStr))
	if err != nil {
		return err
	}
	err = kube.CreateResources(ctx, deploy.client, objectsList)
	if err != nil {
		return err
	}

	// 通过网关暴露 istiod 服务
	exposeIstiodeGatewayVsPath := path.Join(versionBase, constants.StandaloneTemplate, constants.ExposeIstiodTmplName)
	istiodExposeGatewayVsParams := NewIstiodExposeGatewayVsParams(deploy.Namespace, deploy.getIstiodGatewayName(),
		deploy.getIstiodVsName(), eastWestGatewayIstioLabel)
	yamlValues, err := deploy.parseIstiodeGatewayVs(ctx, exposeIstiodeGatewayVsPath, istiodExposeGatewayVsParams)
	if err != nil {
		return err
	}
	err = deploy.deployGatewayVs(ctx, string(yamlValues))
	return err
}
