package lane

import (
	"os"
	"path"
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

var (
	mockCtx, _ = csmContext.NewCsmContextMock()
)

func TestNewEnvoyFilter(t *testing.T) {
	pwd, _ := os.Getwd()
	rootPath := path.Join(pwd, "../../../../")
	testPath := path.Join(rootPath, constants.BaseLanePath, constants.LaneEnvoyFilterTmpl)
	tests := []struct {
		name     string
		efParams []EFParseParams
		wantErr  error
	}{
		{
			name: "NewEnvoyFilter-success",
			efParams: []EFParseParams{
				buildEFParseParams(),
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()

			deployerService, _ := NewDeploy(fakeClient, nil, nil, tt.efParams, nil)

			err := deployerService.CreateEnvoyFilter(mockCtx, testPath)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}

func TestNewOrUpdateVirtualService(t *testing.T) {
	pwd, _ := os.Getwd()
	rootPath := path.Join(pwd, "../../../../")
	testPath := path.Join(rootPath, constants.BaseLanePath, constants.LaneVirtualServiceTmpl)
	tests := []struct {
		name     string
		vsParams []VSParseParams
		wantErr  error
	}{
		{
			name: "success",
			vsParams: []VSParseParams{
				buildVSParseParams(),
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()

			deployerService, _ := NewDeploy(fakeClient, tt.vsParams, nil, nil, nil)

			err := deployerService.CreateOrUpdateVirtualService(mockCtx, testPath)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}

func TestCreateOrUpdateVirtualServiceForDelegate(t *testing.T) {
	pwd, _ := os.Getwd()
	rootPath := path.Join(pwd, "../../../../")
	testPath := path.Join(rootPath, constants.BaseLanePath, constants.LaneVirtualServiceDelegateTmpl)
	tests := []struct {
		name     string
		vsParams []VSDelegateParseParams
		wantErr  error
	}{
		{
			name: "success",
			vsParams: []VSDelegateParseParams{
				buildVSDelegateParseParams(),
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()

			deployerService, _ := NewDeploy(fakeClient, nil, nil, nil, tt.vsParams)

			err := deployerService.CreateOrUpdateVirtualServiceForDelegate(mockCtx, testPath)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}

func TestNewOrUpdateDestinationRule(t *testing.T) {
	pwd, _ := os.Getwd()
	rootPath := path.Join(pwd, "../../../../")
	testPath := path.Join(rootPath, constants.BaseLanePath, constants.LaneDestinationRuleTmpl)
	tests := []struct {
		name     string
		drParams []DRParseParams
		wantErr  error
	}{
		{
			name: "success",
			drParams: []DRParseParams{
				buildDRParseParams(),
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()

			deployerService, _ := NewDeploy(fakeClient, nil, tt.drParams, nil, nil)

			err := deployerService.CreateOrUpdateDestinationRule(mockCtx, "", testPath)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}

func TestDeleteLaneGroupCRDs(t *testing.T) {
	tests := []struct {
		name            string
		laneGroupParams *meta.LaneGroupParams
		laneParams      []meta.LaneParams
		namespace       string
		wantErr         error
	}{
		{
			name:            "delete crd standalone success",
			laneGroupParams: buildLaneGroupParams(),
			laneParams:      []meta.LaneParams{buildLaneParams()},
			namespace:       "",
			wantErr:         nil,
		},
		{
			name:            "delete crd hosting success",
			laneGroupParams: buildLaneGroupParams(),
			laneParams:      []meta.LaneParams{buildLaneParams()},
			namespace:       "istio-system-test",
			wantErr:         nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()

			deployerService, _ := NewDeploy(fakeClient, nil, nil, nil, nil)

			err := deployerService.DeleteLaneGroupCRDs(mockCtx, tt.laneGroupParams, tt.laneParams, "")
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}

func buildServiceList() []meta.ServiceListParams {
	return []meta.ServiceListParams{
		{
			ClusterRegion: "region1",
			ClusterName:   "cluster1",
			ClusterID:     "id1",
			Namespace:     "namespace1",
			ServiceName:   "service1",
		},
	}
}

func buildLaneGroupParams() *meta.LaneGroupParams {
	return &meta.LaneGroupParams{
		InstanceUUID: "instance-uuid",
		GroupID:      "group-id",
		GroupName:    "group-name",
		TraceHeader:  "x-trace-header",
		RouteHeader:  "x-route-header",
		ServiceList:  buildServiceList(),
		BaseLane:     buildLaneParams(),
	}
}

func buildLaneParams() meta.LaneParams {
	laneParams := meta.LaneParams{
		InstanceUUID:       "instance-uuid",
		GroupID:            "group-id",
		LaneID:             "lane-id",
		LaneName:           "lane-name",
		LabelSelectorKey:   "selector-key",
		LabelSelectorValue: "selector-value",
		ServiceList:        buildServiceList(),
		IsBase:             true,
	}
	return laneParams
}

func buildDRParseParams() DRParseParams {
	return DRParseParams{
		ApiVersion:         constants.APIVersion16,
		Name:               "dr-laneID-serviceName",
		Host:               "service.host",
		LabelSelectorKey:   "lane",
		LabelSelectorValue: "lane-name",
		SubsetName:         "lane-name",
	}
}

func buildVSDelegateParseParams() VSDelegateParseParams {
	return VSDelegateParseParams{
		ApiVersion: constants.APIVersion16,
		Name:       "vs-laneID-serviceName",
		Namespace:  "default",
		Host:       "service.host",
		HTTPDelegateRoute: []HTTPDelegateRoute{
			{
				Header: Header{
					HeaderName:      "Test-Header",
					MatchingMode:    "Exact",
					MatchingContent: "Test-Content",
				},
				DelegateName:      "name",
				DelegateNamespace: "ns",
			},
		},
	}
}

func buildVSParseParams() VSParseParams {
	headers := []Header{
		{
			HeaderName:      "Test-Header",
			MatchingMode:    "Exact",
			MatchingContent: "Test-Content",
		},
	}

	uri := Uri{
		MatchingMode:    "Prefix",
		MatchingContent: "/test",
	}
	httpMatch := []HTTPMatch{{
		HttpMatchName: "route-name",
		Headers:       headers,
		Uri:           uri,
	}}

	return VSParseParams{
		ApiVersion: constants.APIVersion16,
		Name:       "vs-laneID-serviceName",
		Namespace:  "default",
		Host:       "service.host",
		SubsetName: "lane-name",
		HTTPMatch:  httpMatch,
	}
}

func buildEFParseParams() EFParseParams {
	return EFParseParams{
		ApiVersion:   constants.APIVersion16,
		Name:         "ef-groupID-serviceName-in",
		Namespace:    "default",
		PatchContext: constants.EFPatchContextIn,
		Operation:    constants.EFOperationIn,
		TraceHeader:  "x-trace-id",
		RouteHeader:  "x-route-id",
		Labels: map[string]string{
			"app": "serviceName",
		},
	}
}

func buildRoute() []meta.RouteRule {
	return []meta.RouteRule{{
		MatchRequest: meta.MatchRequest{
			Headers: []meta.Header{
				{
					Name:            "Test-Header",
					MatchingMode:    "exact",
					MatchingContent: "Test-Content",
				},
			},
			Uri: meta.Uri{
				MatchingMode:    "exact",
				MatchingContent: "/test",
				Enabled:         true,
			},
			RouteName: "test-name",
		},
	}}
}

func TestNewVSParseParams(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		name         string
		route        []meta.RouteRule
		apiVersion   string
		host         string
		namespace    string
		laneID       string
		serviceName  string
		subsetName   string
		expectedName string
	}{
		{
			name:         "SuccessCase",
			route:        buildRoute(),
			apiVersion:   "v1",
			host:         "test.host",
			namespace:    "default",
			laneID:       "123",
			serviceName:  "test-service",
			subsetName:   "test-subset",
			expectedName: "vs-123-test-service",
		},
		// 可以添加更多的测试用例
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用 NewVSParseParams
			vsParam := VSParseParams{
				ApiVersion: tt.apiVersion,
				Host:       tt.host,
				Namespace:  tt.namespace,
				SubsetName: tt.subsetName,
			}
			result := NewVSParseParams(tt.route, tt.laneID, tt.serviceName, vsParam)
			assert.Equal(t, "exact", result.HTTPMatch[0].Uri.MatchingMode)
		})
	}
}

func TestNewEFParseParams(t *testing.T) {
	tests := []struct {
		name            string
		laneGroup       *meta.LaneGroupParams
		apiVersion      string
		namespace       string
		serviceName     string
		expectedNameIn  string
		expectedNameOut string
	}{
		{
			name: "SuccessCase",
			laneGroup: &meta.LaneGroupParams{
				TraceHeader: "trace",
				RouteHeader: "route",
				GroupID:     "groupId",
			},
			apiVersion:      "v1",
			namespace:       "default",
			serviceName:     "test-service",
			expectedNameIn:  "ef-groupId-test-service-in",
			expectedNameOut: "ef-groupId-test-service-out",
		},
		// 添加更多测试用例...
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results := NewEFParseParams(tt.laneGroup, tt.apiVersion, tt.namespace, tt.serviceName)

			// 可根据需要进行更多检查
			assert.Equal(t, 2, len(results))
			assert.Equal(t, tt.expectedNameIn, results[0].Name)
			assert.Equal(t, tt.expectedNameOut, results[1].Name)
		})
	}
}

func TestNewDRParseParams(t *testing.T) {
	tests := []struct {
		name               string
		apiVersion         string
		host               string
		laneID             string
		laneName           string
		serviceName        string
		labelSelectorKey   string
		labelSelectorValue string
		expectedName       string
	}{
		{
			name:               "SuccessCase",
			apiVersion:         "v1",
			host:               "test.host",
			laneID:             "123",
			laneName:           "lane-name",
			serviceName:        "test-service",
			labelSelectorKey:   "selector-key",
			labelSelectorValue: "selector-value",
			expectedName:       "dr-123-test-service",
		},
		// 添加更多测试用例...
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewDRParseParams(tt.apiVersion, tt.host, tt.laneID, tt.laneName, tt.serviceName, tt.labelSelectorKey, tt.labelSelectorValue)

			// 可根据需要进行更多检查
			assert.Equal(t, tt.expectedName, result.Name)
		})
	}
}
