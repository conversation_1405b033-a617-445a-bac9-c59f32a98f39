package lane

import (
	"fmt"
	"strings"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

// EFParseParams 解析 ef-lane 模板参数，一个泳道组对应两个ef，分别作用在inbound和outbound
type EFParseParams struct {
	// ApiVersion版本，支持不同istio版本
	ApiVersion string

	// 名字，格式为 "ef-{groupID}-{serviceName}-in/out"
	Name string

	// 命名空间
	Namespace string

	// PatchContext selects a class of configurations based on the traffic flow direction and workload type.
	// 详情见https://istio.io/latest/docs/reference/config/networking/envoy-filter/#EnvoyFilter-PatchContext
	PatchContext string

	// Operation denotes how the patch should be applied to the selected configuration.
	// 详情见https://istio.io/latest/docs/reference/config/networking/envoy-filter/#EnvoyFilter-Patch-Operation
	Operation string

	// 链路透传请求头
	TraceHeader string

	// 链路引流请求头
	RouteHeader string

	// EnvoyFilter作用范围的labels一般指定用户名
	Labels map[string]string
}

// VSParseParams 解析 vs-lane 模板参数，每个泳道服务对应一个vs
type VSParseParams struct {
	// ApiVersion版本，支持不同istio版本
	ApiVersion string

	// 名字，格式为 "vs-{laneID}-{serviceName}"
	Name string

	// 命名空间
	Namespace string

	// 服务名FQDN
	Host string

	// route.subset名字，与泳道名保持一致
	SubsetName string

	HTTPMatch []HTTPMatch
}

type HTTPMatch struct {
	// 引流规则名称
	HttpMatchName string

	Headers []Header
	Uri     Uri
}

type Header struct {
	HeaderName      string
	MatchingMode    string
	MatchingContent string
}

type Uri struct {
	MatchingMode    string
	MatchingContent string
}

// DRParseParams 解析 dr-lane 模板参数
type DRParseParams struct {
	// ApiVersion版本，支持不同istio版本
	ApiVersion string

	// 名字，格式为 "dr-{laneID}-{serviceName}"
	Name string

	// 服务名FQDN
	Host string

	// 匹配泳道内服务标签的标签名称
	LabelSelectorKey string

	// 匹配泳道内服务标签的标签值
	LabelSelectorValue string

	// 泳道目的地名称
	SubsetName string
}

type VSDelegateParseParams struct {
	// ApiVersion版本，支持不同istio版本
	ApiVersion string

	// 名字，格式为 "vs-{groupID}-{serviceName}"
	Name string

	// 命名空间
	Namespace string

	// 服务名FQDN
	Host string

	HTTPDelegateRoute []HTTPDelegateRoute
}

type HTTPDelegateRoute struct {
	Header
	DelegateName      string
	DelegateNamespace string
}

// NewEFParseParams 生成inbound和outbound两个envoyFilter转换模板
func NewEFParseParams(laneGroup *meta.LaneGroupParams, apiVersion, namespace, serviceName string) []EFParseParams {
	// workSelect默认使用服务名
	labels := make(map[string]string)
	labels[constants.EFWorkSelectorLabelKey] = serviceName

	result := make([]EFParseParams, 0)
	paramsIn := EFParseParams{
		TraceHeader: laneGroup.TraceHeader,
		RouteHeader: laneGroup.RouteHeader,
		Name: fmt.Sprintf("ef-%s-%s-%s", laneGroup.GroupID,
			strings.ToLower(serviceName), constants.EFNameInboundSuffix),
		PatchContext: constants.EFPatchContextIn,
		Operation:    constants.EFOperationIn,
		ApiVersion:   apiVersion,
		Namespace:    namespace,
		Labels:       labels,
	}
	paramsOut := EFParseParams{
		TraceHeader: laneGroup.TraceHeader,
		RouteHeader: laneGroup.RouteHeader,
		Name: fmt.Sprintf("ef-%s-%s-%s", laneGroup.GroupID,
			strings.ToLower(serviceName), constants.EFNameOutboundSuffix),
		PatchContext: constants.EFPatchContextOut,
		Operation:    constants.EFOperationOut,
		ApiVersion:   apiVersion,
		Namespace:    namespace,
		Labels:       labels,
	}

	result = append(result, paramsIn, paramsOut)
	return result
}

// NewDRParseParams 构建DR转换模板
func NewDRParseParams(apiVersion, host, laneID, laneName, serviceName, labelSelectorKey, labelSelectorValue string) DRParseParams {
	result := DRParseParams{
		ApiVersion:         apiVersion,
		Name:               fmt.Sprintf("dr-%s-%s", laneID, serviceName),
		Host:               host,
		LabelSelectorValue: labelSelectorValue,
		LabelSelectorKey:   labelSelectorKey,
		SubsetName:         laneName,
	}
	return result
}

// NewVSParseParams 构建VS转换模板
func NewVSParseParams(route []meta.RouteRule, laneID, serviceName string, vsParam VSParseParams) VSParseParams {
	httpMatches := make([]HTTPMatch, 0)
	// 泳道一定有一个routeHeader
	for _, tempRoute := range route {
		match := tempRoute.MatchRequest
		httpMatch := HTTPMatch{}
		headers := make([]Header, 0)
		for _, header := range match.Headers {
			tmp := Header{
				HeaderName:      header.Name,
				MatchingMode:    strings.ToLower(header.MatchingMode),
				MatchingContent: strings.ToLower(header.MatchingContent),
			}
			headers = append(headers, tmp)
		}
		httpMatch.Headers = headers
		if match.Uri.Enabled {
			httpMatch.Uri = Uri{
				MatchingMode:    strings.ToLower(match.Uri.MatchingMode),
				MatchingContent: match.Uri.MatchingContent,
			}
		}
		httpMatch.HttpMatchName = match.RouteName
		httpMatches = append(httpMatches, httpMatch)
	}

	result := VSParseParams{
		ApiVersion: vsParam.ApiVersion,
		Namespace:  vsParam.Namespace,
		Name:       fmt.Sprintf("vs-%s-%s", laneID, serviceName),
		Host:       vsParam.Host,
		SubsetName: vsParam.SubsetName,
		HTTPMatch:  httpMatches,
	}
	return result
}

// NewVSDelegateParseParams 构建VS-delegate转换模板
func NewVSDelegateParseParams(apiVersion, namespace string, serviceList []meta.ServiceListParams, lanes []meta.Lanes,
	laneGroups []meta.LaneGroups) []VSDelegateParseParams {
	result := make([]VSDelegateParseParams, 0)
	groupMap := make(map[string]meta.LaneGroups)

	for index := range laneGroups {
		tempGroup := laneGroups[index]
		groupMap[tempGroup.GroupID] = tempGroup
	}

	for index := range serviceList {
		httpDelegateRoute := make([]HTTPDelegateRoute, 0)
		serviceParam := serviceList[index]

		// 兼容托管网格架构
		installNamespace := serviceParam.Namespace
		if namespace != "" {
			installNamespace = namespace
		}

		host := fmt.Sprintf("%s.%s.svc.cluster.local", strings.ToLower(serviceParam.ServiceName),
			strings.ToLower(serviceParam.Namespace))
		if serviceParam.IsHost {
			host = strings.ToLower(serviceParam.ServiceName)
		}
		// 泳道一定有一个routeHeader
		for _, tempLane := range lanes {
			// 当前泳道关联的泳道组存在该服务才构建delegate，否则跳过
			servStr := meta.ServiceListToModel([]meta.ServiceListParams{serviceParam})
			lg := groupMap[tempLane.GroupID]
			if !strings.Contains(lg.ServiceList, servStr) {
				continue
			}

			header := Header{
				HeaderName:      lg.RouteHeader,
				MatchingMode:    strings.ToLower(constants.ExactMatchMode),
				MatchingContent: strings.ToLower(tempLane.LaneName),
			}
			delegateName := fmt.Sprintf("vs-%s-%s", strings.ToLower(tempLane.LaneID), strings.ToLower(serviceParam.ServiceName))

			httpDelegate := HTTPDelegateRoute{
				Header:            header,
				DelegateName:      delegateName,
				DelegateNamespace: installNamespace,
			}
			httpDelegateRoute = append(httpDelegateRoute, httpDelegate)
		}

		vs := VSDelegateParseParams{
			ApiVersion:        apiVersion,
			Namespace:         installNamespace,
			Name:              fmt.Sprintf("vs-delegate-%s", strings.ToLower(serviceParam.ServiceName)),
			Host:              host,
			HTTPDelegateRoute: httpDelegateRoute,
		}

		result = append(result, vs)
	}
	return result
}
