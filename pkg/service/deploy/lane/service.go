package lane

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/tmpl"
	"istio.io/client-go/pkg/apis/networking/v1alpha3"
)

type Deploy struct {
	client           kube.Client
	vsParams         []VSParseParams
	drParams         []DRParseParams
	efParams         []EFParseParams
	vsDelegateParams []VSDelegateParseParams
	cceService       cce.ClientInterface
}

func NewDeploy(client kube.Client, vsParams []VSParseParams, drParams []DRParseParams, efParams []EFParseParams,
	vsDelegateParams []VSDelegateParseParams) (*Deploy, error) {

	deploy := &Deploy{
		client:           client,
		vsParams:         vsParams,
		drParams:         drParams,
		efParams:         efParams,
		vsDelegateParams: vsDelegateParams,
		cceService:       cce.NewClientService(),
	}
	return deploy, nil
}

func (deploy *Deploy) CreateEnvoyFilter(ctx csmContext.CsmContext, laneFilePath string) error {
	// 生成ef文件
	laneEFContent, err := os.ReadFile(laneFilePath)
	if err != nil {
		return err
	}
	for _, efParam := range deploy.efParams {
		efYaml, evaErr := tmpl.Evaluate(ctx, string(laneEFContent), efParam)
		if evaErr != nil {
			return evaErr
		}
		target := &v1alpha3.EnvoyFilter{}
		err = util.YamlStringToStruct(efYaml, target)
		if err != nil {
			return err
		}
		// 存在则更新，不存在则创建
		_, getErr := deploy.client.Istio().NetworkingV1alpha3().EnvoyFilters(efParam.Namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if getErr != nil && kubeErrors.IsAlreadyExists(getErr) {
			_, updateErr := deploy.client.Istio().NetworkingV1alpha3().EnvoyFilters(efParam.Namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
			if updateErr != nil {
				return updateErr
			}
		}
	}

	return nil
}

func (deploy *Deploy) CreateOrUpdateVirtualService(ctx csmContext.CsmContext, laneFilePath string) error {
	// 生成vs文件
	laneVSContent, err := os.ReadFile(laneFilePath)
	if err != nil {
		return err
	}
	for _, vsParam := range deploy.vsParams {
		efYaml, evaErr := tmpl.Evaluate(ctx, string(laneVSContent), vsParam)
		if evaErr != nil {
			return evaErr
		}
		target := &v1alpha3.VirtualService{}
		err = util.YamlStringToStruct(efYaml, target)
		if err != nil {
			return err
		}
		// 存在则更新，不存在则创建
		_, getErr := deploy.client.Istio().NetworkingV1alpha3().VirtualServices(vsParam.Namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if getErr != nil && kubeErrors.IsAlreadyExists(getErr) {
			oldVs, gErr := deploy.client.Istio().NetworkingV1alpha3().VirtualServices(vsParam.Namespace).Get(context.TODO(), target.Name, metav1.GetOptions{})
			if gErr != nil {
				return gErr
			}
			target.ResourceVersion = oldVs.ResourceVersion
			_, updateErr := deploy.client.Istio().NetworkingV1alpha3().VirtualServices(vsParam.Namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
			if updateErr != nil {
				return updateErr
			}
		}
	}

	return nil
}

func (deploy *Deploy) CreateOrUpdateVirtualServiceForDelegate(ctx csmContext.CsmContext, laneFilePath string) error {
	// 生成vs文件
	laneVSContent, err := os.ReadFile(laneFilePath)
	if err != nil {
		return err
	}
	for _, vsParam := range deploy.vsDelegateParams {
		efYaml, evaErr := tmpl.Evaluate(ctx, string(laneVSContent), vsParam)
		if evaErr != nil {
			return evaErr
		}
		target := &v1alpha3.VirtualService{}
		err = util.YamlStringToStruct(efYaml, target)
		if err != nil {
			return err
		}
		// 存在则更新，不存在则创建
		_, getErr := deploy.client.Istio().NetworkingV1alpha3().VirtualServices(vsParam.Namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if getErr != nil && kubeErrors.IsAlreadyExists(getErr) {
			oldVs, gErr := deploy.client.Istio().NetworkingV1alpha3().VirtualServices(vsParam.Namespace).Get(context.TODO(), target.Name, metav1.GetOptions{})
			if gErr != nil {
				return gErr
			}
			target.ResourceVersion = oldVs.ResourceVersion
			_, updateErr := deploy.client.Istio().NetworkingV1alpha3().VirtualServices(vsParam.Namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
			if updateErr != nil {
				return updateErr
			}
		}
	}

	return nil
}

func (deploy *Deploy) CreateOrUpdateDestinationRule(ctx csmContext.CsmContext, namespace, laneFilePath string) error {
	// 生成dr文件
	laneDRContent, err := os.ReadFile(laneFilePath)
	if err != nil {
		return err
	}
	for _, drParam := range deploy.drParams {
		efYaml, evaErr := tmpl.Evaluate(ctx, string(laneDRContent), drParam)
		if evaErr != nil {
			return evaErr
		}
		target := &v1alpha3.DestinationRule{}
		err = util.YamlStringToStruct(efYaml, target)
		if err != nil {
			return err
		}
		// 存在则更新，不存在则创建
		_, getErr := deploy.client.Istio().NetworkingV1alpha3().DestinationRules(namespace).Create(context.TODO(), target, metav1.CreateOptions{})
		if getErr != nil && kubeErrors.IsAlreadyExists(getErr) {
			oldDr, gErr := deploy.client.Istio().NetworkingV1alpha3().DestinationRules(namespace).Get(context.TODO(), target.Name, metav1.GetOptions{})
			if gErr != nil {
				return gErr
			}
			target.ResourceVersion = oldDr.ResourceVersion
			_, updateErr := deploy.client.Istio().NetworkingV1alpha3().DestinationRules(namespace).Update(context.TODO(), target, metav1.UpdateOptions{})
			if updateErr != nil {
				return updateErr
			}
		}
	}

	return nil
}

// DeleteLaneGroupCRDs 删除泳道组中所有的CRD。删除方式：根据groupID或laneID在所有涉及ns下查找ef、dr和vs，然后删除
// name命令规范
// envoyFilter：    "ef-{groupID}-{serviceName}-in/out"
// virtualService： "vs-{laneID}-{serviceName}"
// DestinationRule："dr-{laneID}-{serviceName}"
func (deploy *Deploy) DeleteLaneGroupCRDs(ctx csmContext.CsmContext, laneGroup *meta.LaneGroupParams,
	lanes []meta.LaneParams, namespace string) error {
	if namespace == "" {
		return deploy.deleteLaneGroupCRDsForStandalone(ctx, laneGroup, lanes)
	}
	return deploy.deleteLaneGroupCRDsForHosting(ctx, laneGroup, lanes, namespace)
}

func (deploy *Deploy) DeleteLaneCRDs(ctx csmContext.CsmContext, laneGroup *meta.LaneGroupParams,
	lanes []meta.LaneParams, namespace string) error {
	if namespace == "" {
		return deploy.deleteLaneCRDsForStandalone(ctx, laneGroup, lanes)
	}
	return deploy.deleteLaneCRDsForHosting(ctx, laneGroup, lanes, namespace)
}

func (deploy *Deploy) deleteLaneGroupCRDsForStandalone(ctx csmContext.CsmContext, laneGroup *meta.LaneGroupParams, lanes []meta.LaneParams) error {
	// 以ns粒度删除crd
	for _, serviceTmp := range laneGroup.ServiceList {
		if serviceTmp.IsHost {
			continue
		}
		// 删除ef
		efErr := deploy.deleteEnvoyFilter(serviceTmp.Namespace, laneGroup.GroupID, serviceTmp.ServiceName)
		if efErr != nil {
			return efErr
		}
	}
	return deploy.deleteLaneCRDsForStandalone(ctx, laneGroup, lanes)
}

func (deploy *Deploy) deleteLaneCRDsForStandalone(ctx csmContext.CsmContext, laneGroup *meta.LaneGroupParams, lanes []meta.LaneParams) error {
	// 删除vs和dr
	for _, laneParam := range lanes {
		// nolint
		for _, serviceTmp := range laneGroup.ServiceList {
			// 删除vs "vs-{laneID}-{serviceName}"
			vsName := fmt.Sprintf("vs-%s-%s", strings.ToLower(laneParam.LaneID), strings.ToLower(serviceTmp.ServiceName))
			err := deploy.client.Istio().NetworkingV1alpha3().VirtualServices(serviceTmp.Namespace).Delete(context.TODO(), vsName, metav1.DeleteOptions{})
			if err != nil && !kubeErrors.IsNotFound(err) {
				return err
			}
		}
	}

	// 删除CRD时，等待一段时间，保证控制面同步完VirtualService，再删除DestinationRule
	time.Sleep(1 * time.Second)

	for _, laneParam := range lanes {
		namespaceMap := make(map[string]struct{})
		// nolint
		for _, serviceTmp := range laneGroup.ServiceList {
			namespaceMap[serviceTmp.Namespace] = struct{}{}
		}
		// 删除dr
		err := deploy.deleteDestinationRule(laneParam.LaneID, namespaceMap, laneGroup.ServiceList)
		if err != nil {
			return err
		}
	}

	return nil
}

func (deploy *Deploy) deleteLaneGroupCRDsForHosting(ctx csmContext.CsmContext, laneGroup *meta.LaneGroupParams,
	lanes []meta.LaneParams, namespace string) error {
	// 以ns粒度删除crd
	for _, serviceTmp := range laneGroup.ServiceList {
		if serviceTmp.IsHost {
			continue
		}
		// 删除ef
		efErr := deploy.deleteEnvoyFilter(namespace, laneGroup.GroupID, serviceTmp.ServiceName)
		if efErr != nil {
			return efErr
		}
	}
	return deploy.deleteLaneCRDsForHosting(ctx, laneGroup, lanes, namespace)
}

func (deploy *Deploy) deleteLaneCRDsForHosting(ctx csmContext.CsmContext, laneGroup *meta.LaneGroupParams,
	lanes []meta.LaneParams, namespace string) error {
	namespaceMap := make(map[string]struct{})
	namespaceMap[namespace] = struct{}{}
	// 先删除vs
	for _, laneParam := range lanes {
		// nolint
		for _, serviceTmp := range laneGroup.ServiceList {
			// 删除vs "vs-{laneID}-{serviceName}"
			vsName := fmt.Sprintf("vs-%s-%s", strings.ToLower(laneParam.LaneID), strings.ToLower(serviceTmp.ServiceName))
			err := deploy.client.Istio().NetworkingV1alpha3().VirtualServices(namespace).Delete(context.TODO(), vsName, metav1.DeleteOptions{})
			if err != nil && !kubeErrors.IsNotFound(err) {
				return err
			}
		}
	}

	// 删除CRD时，等待一段时间，保证控制面同步完VirtualService，再删除DestinationRule
	time.Sleep(1 * time.Second)

	// 删除dr
	for _, laneParam := range lanes {
		err := deploy.deleteDestinationRule(laneParam.LaneID, namespaceMap, laneGroup.ServiceList)
		if err != nil && !kubeErrors.IsNotFound(err) {
			return err
		}
	}

	return nil
}

func (deploy *Deploy) deleteEnvoyFilter(namespace, groupID, serviceName string) error {
	nameIn := fmt.Sprintf("ef-%s-%s-in", strings.ToLower(groupID), strings.ToLower(serviceName))
	nameOut := fmt.Sprintf("ef-%s-%s-out", strings.ToLower(groupID), strings.ToLower(serviceName))
	err := deploy.client.Istio().NetworkingV1alpha3().EnvoyFilters(namespace).Delete(context.TODO(), nameIn, metav1.DeleteOptions{})
	if err != nil && !kubeErrors.IsNotFound(err) {
		return csmErr.NewServiceException(fmt.Sprintf("delete envoyFilter %s failed", nameIn))
	}
	err = deploy.client.Istio().NetworkingV1alpha3().EnvoyFilters(namespace).Delete(context.TODO(), nameOut, metav1.DeleteOptions{})
	if err != nil && !kubeErrors.IsNotFound(err) {
		return csmErr.NewServiceException(fmt.Sprintf("delete envoyFilter %s failed", nameOut))
	}
	return nil
}

func (deploy *Deploy) deleteDestinationRule(laneID string, namespaceMap map[string]struct{}, serviceList []meta.ServiceListParams) error {
	// "dr-{laneID}-{serviceName}"
	//nolint
	for namespace, _ := range namespaceMap {
		//nolint
		for _, serviceTmp := range serviceList {
			drName := fmt.Sprintf("dr-%s-%s", strings.ToLower(laneID), strings.ToLower(serviceTmp.ServiceName))
			err := deploy.client.Istio().NetworkingV1alpha3().DestinationRules(namespace).Delete(context.TODO(), drName, metav1.DeleteOptions{})
			if err != nil && !kubeErrors.IsNotFound(err) {
				return csmErr.NewServiceException(fmt.Sprintf("delete envoyFilter %s failed", drName))
			}
		}
	}
	return nil
}
