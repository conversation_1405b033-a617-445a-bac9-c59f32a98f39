// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateEnvoyFilter mocks base method.
func (m *MockServiceInterface) CreateEnvoyFilter(ctx context.CsmContext) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEnvoyFilter", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateEnvoyFilter indicates an expected call of CreateEnvoyFilter.
func (mr *MockServiceInterfaceMockRecorder) CreateEnvoyFilter(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEnvoyFilter", reflect.TypeOf((*MockServiceInterface)(nil).CreateEnvoyFilter), ctx)
}

// CreateOrUpdateDestinationRule mocks base method.
func (m *MockServiceInterface) CreateOrUpdateDestinationRule(ctx context.CsmContext, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateDestinationRule", ctx, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrUpdateDestinationRule indicates an expected call of CreateOrUpdateDestinationRule.
func (mr *MockServiceInterfaceMockRecorder) CreateOrUpdateDestinationRule(ctx, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateDestinationRule", reflect.TypeOf((*MockServiceInterface)(nil).CreateOrUpdateDestinationRule), ctx, namespace)
}

// CreateOrUpdateVirtualService mocks base method.
func (m *MockServiceInterface) CreateOrUpdateVirtualService(ctx context.CsmContext) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateVirtualService", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrUpdateVirtualService indicates an expected call of CreateOrUpdateVirtualService.
func (mr *MockServiceInterfaceMockRecorder) CreateOrUpdateVirtualService(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateVirtualService", reflect.TypeOf((*MockServiceInterface)(nil).CreateOrUpdateVirtualService), ctx)
}

// CreateOrUpdateVirtualServiceForDelegate mocks base method.
func (m *MockServiceInterface) CreateOrUpdateVirtualServiceForDelegate(ctx context.CsmContext, laneFilePath string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateVirtualServiceForDelegate", ctx, laneFilePath)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrUpdateVirtualServiceForDelegate indicates an expected call of CreateOrUpdateVirtualServiceForDelegate.
func (mr *MockServiceInterfaceMockRecorder) CreateOrUpdateVirtualServiceForDelegate(ctx, laneFilePath interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateVirtualServiceForDelegate", reflect.TypeOf((*MockServiceInterface)(nil).CreateOrUpdateVirtualServiceForDelegate), ctx, laneFilePath)
}

// DeleteLaneCRDs mocks base method.
func (m *MockServiceInterface) DeleteLaneCRDs(ctx context.CsmContext, laneGroup *meta.LaneGroupParams, lanes []meta.LaneParams, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLaneCRDs", ctx, laneGroup, lanes, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLaneCRDs indicates an expected call of DeleteLaneCRDs.
func (mr *MockServiceInterfaceMockRecorder) DeleteLaneCRDs(ctx, laneGroup, lanes, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLaneCRDs", reflect.TypeOf((*MockServiceInterface)(nil).DeleteLaneCRDs), ctx, laneGroup, lanes, namespace)
}

// DeleteLaneGroupCRDs mocks base method.
func (m *MockServiceInterface) DeleteLaneGroupCRDs(ctx context.CsmContext, laneGroup *meta.LaneGroupParams, lanes []meta.LaneParams, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLaneGroupCRDs", ctx, laneGroup, lanes, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLaneGroupCRDs indicates an expected call of DeleteLaneGroupCRDs.
func (mr *MockServiceInterfaceMockRecorder) DeleteLaneGroupCRDs(ctx, laneGroup, lanes, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLaneGroupCRDs", reflect.TypeOf((*MockServiceInterface)(nil).DeleteLaneGroupCRDs), ctx, laneGroup, lanes, namespace)
}
