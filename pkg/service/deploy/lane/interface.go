package lane

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	CreateEnvoyFilter(ctx csmContext.CsmContext) error
	CreateOrUpdateVirtualService(ctx csmContext.CsmContext) error
	CreateOrUpdateDestinationRule(ctx csmContext.CsmContext, namespace string) error
	CreateOrUpdateVirtualServiceForDelegate(ctx csmContext.CsmContext, laneFilePath string) error
	DeleteLaneGroupCRDs(ctx csmContext.CsmContext, laneGroup *meta.LaneGroupParams, lanes []meta.LaneParams, namespace string) error
	DeleteLaneCRDs(ctx csmContext.CsmContext, laneGroup *meta.LaneGroupParams, lanes []meta.LaneParams, namespace string) error
}
