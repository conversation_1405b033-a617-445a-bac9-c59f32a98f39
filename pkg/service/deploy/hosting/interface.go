package hosting

import (
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	InstallInstanceMesh(ctx csmContext.CsmContext, iopTemplates *IopTemplates) error
	UnInstallInstanceMesh(ctx csmContext.CsmContext, iopTemplates *IopTemplates) error
	InstallRemoteUserMesh(ctx csmContext.CsmContext, userIopTemplate *IopTemplates) error
	InstallRemoteConfigMesh(ctx csmContext.CsmContext, userIopTemplate *IopTemplates) error
	UnInstallRemoteUserMesh(ctx csmContext.CsmContext, remoteUserIopTemplates *IopTemplates) error
	UnInstallRemoteConfigMesh(ctx csmContext.CsmContext, remoteUserIopTemplates *IopTemplates) error
}
