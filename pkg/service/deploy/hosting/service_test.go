package hosting

import (
	"context"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"testing"

	"github.com/baidubce/bce-sdk-go/services/endpoint"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/otiai10/copy"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/discovery/fake"

	blbService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	mockCertModelService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cert/mock"
	modelmeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockBlbService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/blb/mock"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	mockVpcService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpc/mock"
	mockExecService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/command/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
)

var (
	mockCtx, _ = csmContext.NewCsmContextMock()
	mockDB, _  = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
)

var (
	namespace      = "istio-system-csm-123456"
	hub            = "registry.baidubce.com/csm-offline"
	instanceId     = "test01"
	region         = "bj"
	clusterName    = "test"
	cceClusterUuid = "test"
	mockAccountID  = "1"
	mockVpvID      = "xxx"
)

var (
	istioConfigMapValue = `defaultConfig:
  discoveryAddress: ***********:15012
  holdApplicationUntilProxyStarts: true
  tracing:
    sampling: 100
    zipkin:
      address: zipkin.istio-system-csm-123456:9411`
)

func buildIopTemplates() *IopTemplates {
	return &IopTemplates{
		ExternalIstiodAddress: "*******",
		VpcEniTemplates: &VpcEniTemplates{
			AccountID:        mockAccountID,
			VpcID:            mockVpvID,
			VpcCidr:          "xxx",
			SubnetID:         "xxx",
			SecurityGroupIds: "xxx",
			PrivateIPAddress: "",
			EniAnnotation: &EniAnnotation{
				EniRequests: &EniRequests{
					Eni: &Eni{
						Num: "1",
					},
				},
				EniLimits: &EniLimits{
					Eni: &Eni{
						Num: "1",
					}},
			},
		},
		PaasType: modelmeta.PaaSTypeCCE,
		FrontTemplates: &FrontTemplates{
			Namespace:               namespace,
			Hub:                     hub,
			Tag:                     testdata.Version1146,
			InstanceRegion:          "",
			InstanceId:              instanceId,
			ClusterRegion:           region,
			ClusterName:             clusterName,
			CceClusterUuid:          cceClusterUuid,
			DiscoverySelectorLabels: map[string]string{"test": "test"},
			AccessLogFile:           false,
		},
		TraceEnabled: true,
	}
}

const (
	testKubeConfigName         = "kubeconfig.yaml"
	testRemoteUserIstioRawName = "remote_user_istio_raw.yaml"
	testData                   = "testdata"
)

func buildMockKubeConfig(workdir string) []byte {
	fileName := path.Join(workdir, testData, testKubeConfigName)
	res, err := os.ReadFile(fileName)
	if err != nil {
		panic(err)
	}
	return res
}

func buildMockRemoteUserIstioRaw(workdir string) []byte {
	fileName := path.Join(workdir, testData, testRemoteUserIstioRawName)
	res, err := os.ReadFile(fileName)
	if err != nil {
		panic(err)
	}
	return res
}

func buildMockConfigmap(name, namespace, data string) *v1.ConfigMap {
	return &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Data: map[string]string{
			constants.IstioConfigMapMeshName: data,
		},
	}
}

func buildIstioService() *v1.Service {
	return &v1.Service{
		Status: v1.ServiceStatus{
			LoadBalancer: v1.LoadBalancerStatus{
				Ingress: []v1.LoadBalancerIngress{
					{
						IP:       "*******",
						Hostname: "test",
					},
				},
			},
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      constants.IstiodServiceName,
			Namespace: namespace,
		},
	}
}

func buildThirdPartyJwtApiResourceList() []*metav1.APIResourceList {
	apiResourceList := []*metav1.APIResourceList{
		{
			APIResources: []metav1.APIResource{{Name: "serviceaccounts/token"}},
		},
	}
	return apiResourceList
}

func buildMockGetBlbServiceResult() *blbService.GetBlbServiceResult {
	return &blbService.GetBlbServiceResult{
		ServiceId:   "xxx-1",
		ServiceName: "xxx-2",
		InstanceId:  "xxx-3",
		Service:     "xxx-4",
	}
}

func buildMockCreateBlbServiceResult() *blbService.CreateBlbServiceResult {
	return &blbService.CreateBlbServiceResult{
		Service: "xxxx",
	}
}

func buildMockCreateEndpointResult() *endpoint.CreateEndpointResult {
	return &endpoint.CreateEndpointResult{
		Id:        "xxx",
		IpAddress: "*******",
	}
}

func TestInstallInstanceMesh(t *testing.T) {
	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../../")
	sourceTemplatesDir := path.Join(rootPath, constants.Templates)

	tempDir, err := os.MkdirTemp("/tmp", "test")
	if err != nil {
		t.Errorf("os.MkdirTemp error %v", err)
	}
	testRoot := path.Join(tempDir, constants.Templates)
	defer os.RemoveAll(tempDir)

	err = copy.Copy(sourceTemplatesDir, testRoot)
	if err != nil {
		t.Errorf("copy.Copy error %v", err)
	}
	rootCertPath := path.Join(testRoot, constants.CertBasePath)
	templatePath := path.Join(testRoot, constants.HostingBaseIstioTemplate)

	iopTemplates := buildIopTemplates()
	iopTemplates.RootCertPath = rootCertPath
	iopTemplates.TemplatePath = templatePath

	tests := []struct {
		name          string
		iopTemplates  *IopTemplates
		wantErr       error
		thirdPartyJwt bool
	}{
		{
			name:          "InstallInstanceMesh-success",
			iopTemplates:  iopTemplates,
			thirdPartyJwt: true,
			wantErr:       nil,
		},
		{
			name:          "InstallInstanceMesh-firstPartyJwt",
			iopTemplates:  iopTemplates,
			thirdPartyJwt: false,
			wantErr:       fmt.Errorf("values.global.jwtPolicy is deprecated"),
		},
	}
	ctrl := gomock.NewController(t)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockCertModelService := mockCertModelService.NewMockServiceInterface(ctrl)
	mockBlbService := mockBlbService.NewMockServiceInterface(ctrl)
	mockVpcService := mockVpcService.NewMockServiceInterface(ctrl)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			fakeClient.Kube().CoreV1().Services(namespace).Create(context.TODO(), buildIstioService(), metav1.CreateOptions{})
			if tt.thirdPartyJwt {
				fakeClient.Kube().Discovery().(*fake.FakeDiscovery).Resources = buildThirdPartyJwtApiResourceList()
			}

			deployerService := NewDeployerService(mockCtx, NewOption(mockDB))
			deployerService.cceService = mockCceService
			deployerService.certModelService = mockCertModelService
			deployerService.blbService = mockBlbService
			deployerService.vpcService = mockVpcService

			mockCceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			mockBlbService.EXPECT().CreateBlbService(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildMockCreateBlbServiceResult(), nil)
			mockBlbService.EXPECT().GetBlbService(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildMockGetBlbServiceResult(), nil).AnyTimes()
			mockVpcService.EXPECT().CreateEndpoint(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildMockCreateEndpointResult(), nil)
			mockCertModelService.EXPECT().GetCertByClusterUUIDAndRegionAndAccountId(
				gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mockCertModelService.EXPECT().NewCert(gomock.Any(), gomock.Any()).Return(nil)

			err = deployerService.InstallInstanceMesh(mockCtx, iopTemplates)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			}
		})
	}
}

func TestUnInstallInstanceMesh(t *testing.T) {
	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../../")
	sourceTemplatesDir := path.Join(rootPath, constants.Templates)

	tempDir, err := os.MkdirTemp("/tmp", "test")
	defer os.RemoveAll(tempDir)
	if err != nil {
		t.Errorf("os.MkdirTemp error %v", err)
	}
	testRoot := path.Join(tempDir, constants.Templates)

	err = copy.Copy(sourceTemplatesDir, testRoot)
	if err != nil {
		t.Errorf("copy.Copy error %v", err)
	}
	templatePath := path.Join(testRoot, constants.HostingBaseIstioTemplate)

	iopTemplates := buildIopTemplates()
	iopTemplates.TemplatePath = templatePath
	tests := []struct {
		name          string
		iopTemplates  *IopTemplates
		wantErr       error
		thirdPartyJwt bool
	}{
		{
			name:          "UnInstallInstanceMesh-success",
			iopTemplates:  iopTemplates,
			thirdPartyJwt: true,
			wantErr:       nil,
		},
	}
	ctrl := gomock.NewController(t)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockCertModelService := mockCertModelService.NewMockServiceInterface(ctrl)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			fakeClient.Kube().CoreV1().Services(namespace).Create(context.TODO(), buildIstioService(), metav1.CreateOptions{})
			if tt.thirdPartyJwt {
				fakeClient.Kube().Discovery().(*fake.FakeDiscovery).Resources = buildThirdPartyJwtApiResourceList()
			}

			deployerService := NewDeployerService(mockCtx, NewOption(mockDB))
			deployerService.cceService = mockCceService
			deployerService.certModelService = mockCertModelService

			mockCceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			//nolint:errcheck // 单测
			fakeClient.Kube().CoreV1().ConfigMaps(namespace).Create(context.TODO(),
				buildMockConfigmap(constants.IstioConfimapName, namespace, istioConfigMapValue), metav1.CreateOptions{})

			err = deployerService.UnInstallInstanceMesh(mockCtx, iopTemplates)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err, tt.wantErr)
			}
		})
	}
}

func TestInstallRemoteUserMesh(t *testing.T) {
	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../../")
	sourceTemplatesDir := path.Join(rootPath, constants.Templates)

	tempDir, err := os.MkdirTemp("/tmp", "test")
	if err != nil {
		t.Errorf("os.MkdirTemp error %v", err)
	}
	testRoot := path.Join(tempDir, constants.Templates)
	defer os.RemoveAll(tempDir)

	err = copy.Copy(sourceTemplatesDir, testRoot)
	if err != nil {
		t.Errorf("copy.Copy error %v", err)
	}
	templatePath := path.Join(testRoot, constants.HostingBaseIstioTemplate)

	iopTemplates := buildIopTemplates()
	iopTemplates.TemplatePath = templatePath

	tests := []struct {
		name          string
		iopTemplates  *IopTemplates
		wantErr       error
		thirdPartyJwt bool
	}{
		{
			name:          "InstallRemoteUserMesh-success",
			iopTemplates:  iopTemplates,
			thirdPartyJwt: true,
			wantErr:       nil,
		},
	}
	ctrl := gomock.NewController(t)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockKubeConfigData := buildMockKubeConfig(rootPath)
	mockRemoteUserIstioRaw := buildMockRemoteUserIstioRaw(rootPath)
	mockExecService := mockExecService.NewMockExecInterface(ctrl)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clusterFakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(clusterFakeClient, nil)

			instanceFakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(instanceFakeClient, nil)

			mockCceService.EXPECT().GetCCEClusterKubeConfigByClusterUUID(
				gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockKubeConfigData, nil)
			mockCceService.EXPECT().GetCCEClusterKubeConfigByClusterUUID(
				gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockKubeConfigData, nil)
			mockExecService.EXPECT().Exec(mockCtx, gomock.Any()).Return(mockRemoteUserIstioRaw, nil, nil)
			mockExecService.EXPECT().Exec(mockCtx, gomock.Any()).Return(nil, nil, nil)

			//nolint:errcheck // 单测
			instanceFakeClient.Kube().CoreV1().Services(namespace).Create(context.TODO(), buildIstioService(), metav1.CreateOptions{})
			//nolint:errcheck // 单测
			instanceFakeClient.Kube().CoreV1().ConfigMaps(namespace).Create(context.TODO(),
				buildMockConfigmap(constants.IstioConfimapName, namespace, istioConfigMapValue), metav1.CreateOptions{})

			deployerService := NewDeployerService(mockCtx, NewOption(mockDB))
			deployerService.cceService = mockCceService
			deployerService.execService = mockExecService

			err = deployerService.InstallRemoteUserMesh(mockCtx, iopTemplates)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err, tt.wantErr)
			}
		})
	}
}

func buildMockListEndpointResult() *endpoint.ListEndpointResult {
	return &endpoint.ListEndpointResult{
		Endpoints: []endpoint.Endpoint{
			{
				Name:      instanceId,
				IpAddress: "*******",
			},
		},
	}
}

func TestUnInstallRemoteUserMesh(t *testing.T) {
	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../../")
	sourceTemplatesDir := path.Join(rootPath, constants.Templates)

	tempDir, err := os.MkdirTemp("/tmp", "test")
	defer os.RemoveAll(tempDir)
	if err != nil {
		t.Errorf("os.MkdirTemp error %v", err)
	}
	testRoot := path.Join(tempDir, constants.Templates)

	err = copy.Copy(sourceTemplatesDir, testRoot)
	if err != nil {
		t.Errorf("copy.Copy error %v", err)
	}
	templatePath := path.Join(testRoot, constants.HostingBaseIstioTemplate)

	iopTemplates := buildIopTemplates()
	iopTemplates.TemplatePath = templatePath

	tests := []struct {
		name          string
		iopTemplates  *IopTemplates
		wantErr       error
		thirdPartyJwt bool
	}{
		{
			name:          "UnInstallRemoteUserMesh-success",
			iopTemplates:  iopTemplates,
			thirdPartyJwt: true,
			wantErr:       nil,
		},
	}
	ctrl := gomock.NewController(t)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockRemoteUserIstioRaw := buildMockRemoteUserIstioRaw(rootPath)
	mockExecService := mockExecService.NewMockExecInterface(ctrl)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clusterFakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(clusterFakeClient, nil)

			instanceFakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(instanceFakeClient, nil)

			instanceFakeClient.Kube().CoreV1().Services(namespace).Create(context.TODO(), buildIstioService(), metav1.CreateOptions{})
			//nolint:errcheck // 单测
			instanceFakeClient.Kube().CoreV1().ConfigMaps(namespace).Create(context.TODO(),
				buildMockConfigmap(constants.IstioConfimapName, namespace, istioConfigMapValue), metav1.CreateOptions{})
			mockExecService.EXPECT().Exec(mockCtx, gomock.Any()).Return(mockRemoteUserIstioRaw, nil, nil)

			deployerService := NewDeployerService(mockCtx, NewOption(mockDB))
			deployerService.cceService = mockCceService
			deployerService.execService = mockExecService

			err = deployerService.UnInstallRemoteUserMesh(mockCtx, iopTemplates)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err, tt.wantErr)
			}
		})
	}
}
