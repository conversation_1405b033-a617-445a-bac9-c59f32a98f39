package hosting

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path"
	"strings"
	"sync"

	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/baidubce/bce-sdk-go/services/endpoint"
	"gopkg.in/yaml.v3"
	v1 "k8s.io/api/core/v1"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/tools/clientcmd"

	blbService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	bceUtil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/util"
	certModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/blb"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cert/ca"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/deploy"
	service_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/vpc"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/command"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/jwt"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/tmpl"
)

type Deployer struct {
	vpcService       vpc.ServiceInterface
	blbService       blb.ServiceInterface
	caService        ca.ServiceInterface
	certModelService certModel.ServiceInterface
	cceService       cce.ClientInterface
	instanceClient   kube.Client
	client           kube.Client
	execService      command.ExecInterface
	iopTemplates     *IopTemplates
	*sync.RWMutex
}

func NewDeployerService(ctx csmContext.CsmContext, option *Option) *Deployer {
	return &Deployer{
		RWMutex:          new(sync.RWMutex),
		vpcService:       vpc.NewVPCService(),
		blbService:       blb.NewBlbService(blb.NewOption(option.DB.DB)),
		cceService:       cce.NewClientService(),
		certModelService: certModel.NewCertService(certModel.NewOption(option.DB.DB)),
		execService:      command.NewExecClient(ctx),
	}
}

func (deployer *Deployer) removeNamespace(ctx csmContext.CsmContext, client kube.Client, namespace string) error {
	nsError := client.Kube().CoreV1().Namespaces().Delete(context.TODO(), namespace, metav1.DeleteOptions{})
	ctx.CsmLogger().Infof("delete namespace error=%v", nsError)
	nsErr := wait.PollImmediate(constants.PollInternal, constants.Internal, func() (bool, error) {
		ctx.CsmLogger().Infof("delete %s namespace", namespace)
		_, err := client.Kube().CoreV1().Namespaces().Get(context.TODO(), namespace, metav1.GetOptions{})
		if kubeErrors.IsNotFound(err) {
			return true, nil // done
		}
		if err != nil {
			return false, err // stop wait with error
		}
		return false, nil
	})
	if nsErr != nil {
		ns, err := client.Kube().CoreV1().Namespaces().Get(context.TODO(), namespace, metav1.GetOptions{})
		if ns.Status.Phase == v1.NamespaceTerminating {
			ctx.CsmLogger().Infof("delete %s namespace with patch force...", namespace)
			err = kube.DeleteNamespaceWithFinalizers(ctx, client, ns)
			if err != nil {
				return err
			}
			ctx.CsmLogger().Infof("delete %s namespace with patch force ok...", namespace)
			return nil
		}
		return err
	}
	ctx.CsmLogger().Infof("delete %s namespace successful", namespace)
	return nil
}

func (deployer *Deployer) createNamespace(ctx csmContext.CsmContext, client kube.Client,
	namespace string, labels map[string]string) error {
	ns := &v1.Namespace{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Name:   namespace,
			Labels: labels,
		},
	}
	var err error
	_, err = client.Kube().CoreV1().Namespaces().Create(context.TODO(), ns, metav1.CreateOptions{})
	if kubeErrors.IsAlreadyExists(err) {
		ns, err = client.Kube().CoreV1().Namespaces().Get(context.TODO(), namespace, metav1.GetOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("get %s namespace err %v", namespace, err)
			return err
		}
		sourceLabel := ns.Labels
		if sourceLabel == nil {
			sourceLabel = make(map[string]string)
		}
		for k, v := range labels {
			sourceLabel[k] = v
		}
		ns.Labels = sourceLabel
		_, err = client.Kube().CoreV1().Namespaces().Update(context.TODO(), ns, metav1.UpdateOptions{})
	}
	if err != nil {
		ctx.CsmLogger().Errorf("create or update %s namespace err %v", namespace, err)
		return err
	}
	ctx.CsmLogger().Infof("create or update %s namespace successful", namespace)
	return nil
}

func (deployer *Deployer) generateCertData(ctx csmContext.CsmContext, namespace string) error {
	accountId := deployer.iopTemplates.AccountId
	clusterId := deployer.iopTemplates.CceClusterUuid
	region := deployer.iopTemplates.ClusterRegion
	caCert := make(map[string][]byte)
	// 先判断数据库中是否存在集群证书
	certData, _ := deployer.certModelService.GetCertByClusterUUIDAndRegionAndAccountId(ctx, region, clusterId, accountId)
	if certData != nil {
		// 从数据库中获取中间证书
		ctx.CsmLogger().Infof("get cert from database with region=%s,clusterId=%s,accountId=%s", region, clusterId, accountId)
		caCert[constants.CaCertPemName] = []byte(certData.CaCertPem)
		caCert[constants.CaKeyPemName] = []byte(certData.CaKeyPem)
		caCert[constants.CertChainPemName] = []byte(certData.CertChainPem)
		caCert[constants.RootCertPemName] = []byte(certData.RootCertPem)
	} else {
		ctx.CsmLogger().Infof("getCertByClusterUUIDAndRegionAndAccountId get nil, "+
			" we will generate cert with region=%s,clusterId=%s,accountId=%s", region, clusterId, accountId)
		// 使用根证书生成集群中间证书
		cert, err := deployer.caService.GenerateIstioCaData(ctx, deployer.iopTemplates.RootCertPath)
		if err != nil {
			ctx.CsmLogger().Errorf("GenerateIstioCaData error %v", err)
			return err
		}
		caCertPem := string(cert[constants.CaCertPemName])
		caKeyPem := string(cert[constants.CaKeyPemName])
		certChainPem := string(cert[constants.CertChainPemName])
		rootCertPem := string(cert[constants.RootCertPemName])
		certInfo := meta.NewCert(region, clusterId, accountId, caCertPem, caKeyPem, certChainPem, rootCertPem)
		// 入库
		err = deployer.certModelService.NewCert(ctx, certInfo)
		if err != nil {
			ctx.CsmLogger().Errorf("insert cert to db error %v", err)
			return err
		}
		caCert = cert
	}
	err := deployer.caService.CreateCaCerts(ctx, caCert, namespace)
	if err != nil {
		ctx.CsmLogger().Errorf("CreateCaCerts error %v", err)
		return err
	}
	return nil
}

func (deployer *Deployer) createIstioCa(ctx csmContext.CsmContext) error {
	instanceId := deployer.iopTemplates.InstanceId
	namespace := deployer.iopTemplates.Namespace
	labels := make(map[string]string)
	var network string
	paasType := deployer.iopTemplates.PaasType
	if paasType == meta.PaaSTypeCCE {
		network = deployer.iopTemplates.ClusterRegion
	} else if paasType == meta.PaaSTypeEKS {
		network = constants.EksNetWork
	} else {
		ctx.CsmLogger().Errorf("not support PaaS %s", paasType)
		return fmt.Errorf("not support PaaS %s", paasType)
	}
	labels[constants.TopologyIstioIoNetWork] = network
	labels[constants.MeshInstanceId] = instanceId
	labels[constants.MeshAccountId] = deployer.iopTemplates.AccountId
	err := deployer.createNamespace(ctx, deployer.instanceClient, namespace, labels)
	if err != nil {
		ctx.CsmLogger().Errorf("CreateNamespace error %v", err)
		return err
	}
	err = deployer.generateCertData(ctx, namespace)
	if err != nil {
		ctx.CsmLogger().Errorf("generateCertData error %v", err)
		return err
	}
	return nil
}

func (deployer *Deployer) generateSuffixName(ctx csmContext.CsmContext) (string, error) {
	region := deployer.iopTemplates.ClusterRegion
	if len(region) == 0 {
		return "", errors.New("region of istio with cluster is null")
	}
	version := deployer.iopTemplates.Tag
	if len(version) == 0 {
		return "", errors.New("version(tag) of istio is null")
	}
	clusterName := deployer.iopTemplates.ClusterName
	if len(clusterName) == 0 {
		return "", errors.New("clusterName of istio is null")
	}
	cceClusterUuid := deployer.iopTemplates.CceClusterUuid
	if len(cceClusterUuid) == 0 {
		return "", errors.New("cceClusterUUID of istio is null")
	}
	namespace := deployer.iopTemplates.Namespace
	if len(namespace) == 0 {
		return "", errors.New("namespace of istio is null")
	}
	paasType := deployer.iopTemplates.PaasType
	fileName := util.GetStrWithSplit([]string{region, version, clusterName,
		cceClusterUuid, namespace, string(paasType)}, "_")
	return fileName, nil
}

// removeAllTempFile remove all install or uninstall files
func (deployer *Deployer) removeAllTempFile(ctx csmContext.CsmContext) error {
	kubeConfigPath := deployer.iopTemplates.KubeConfigPath
	if len(kubeConfigPath) > 0 {
		err := file.RemoveFile(kubeConfigPath)
		if err != nil {
			return err
		}
	} else {
		ctx.CsmLogger().Warn("kubeconfig yaml file is nil")
	}

	iopTmplPath := deployer.iopTemplates.IopTemplatePath
	if len(iopTmplPath) > 0 {
		err := file.RemoveFile(iopTmplPath)
		if err != nil {
			return err
		}
	} else {
		ctx.CsmLogger().Warn("iop yaml file is nil")
	}

	vpcKubeConfigPath := deployer.iopTemplates.VpcKubeConfigPath
	if len(vpcKubeConfigPath) > 0 {
		err := file.RemoveFile(vpcKubeConfigPath)
		if err != nil {
			return err
		}
	} else {
		ctx.CsmLogger().Warn("kubeconfig yaml file is nil")
	}

	return nil
}

func (deployer *Deployer) parseTemplate(ctx csmContext.CsmContext) error {
	tagPath := path.Join(deployer.iopTemplates.TemplatePath, deployer.iopTemplates.Tag)
	iopTmplPath := path.Join(tagPath, constants.HostingTmpl)

	var srcIopTmplPath string
	namespace := deployer.iopTemplates.Namespace
	tag := deployer.iopTemplates.Tag
	hub := deployer.iopTemplates.Hub
	instanceId := deployer.iopTemplates.InstanceId
	region := deployer.iopTemplates.ClusterRegion
	clusterCceId := deployer.iopTemplates.CceClusterUuid
	var proxyResource *deploy.Resources
	var initProxyResource *deploy.Resources

	paasType := deployer.iopTemplates.PaasType
	if paasType == meta.PaaSTypeCCE {
		srcIopTmplPath = path.Join(iopTmplPath, constants.HostingCceIopTmplName)
		proxyResource = deploy.NewCceDefaultProxyResources()
		initProxyResource = deploy.NewCceDefaultInitProxyResources()
	} else if paasType == meta.PaaSTypeEKS {
		srcIopTmplPath = path.Join(iopTmplPath, constants.HostingEksIopTmplName)
		region = constants.EksNetWork
		proxyResource = deploy.NewEksDefaultProxyResources()
		initProxyResource = deploy.NewEksDefaultInitProxyResources()
	}

	iopClusterId := util.GetStrWithSplit([]string{region, clusterCceId}, constants.SplitSlash)
	parseIopTmplParams := NewParseIopTmplParams(namespace, tag, hub, instanceId, iopClusterId)
	parseIopTmplParams.AccessLogFile = false
	parseIopTmplParams.Network = region
	parseIopTmplParams.Proxy = proxyResource
	parseIopTmplParams.InitProxy = initProxyResource
	parseIopTmplParams.IsRemote = deployer.iopTemplates.IsRemote
	// istiod 支持选择性服务发现
	if len(deployer.iopTemplates.DiscoverySelectorLabels) > 0 {
		labels := make(map[string]string, 0)
		labels[constants.MeshInstanceId] = deployer.iopTemplates.InstanceId
		parseIopTmplParams.DiscoverySelectorLabels = labels
		parseIopTmplParams.MatchExpressions = service_meta.NewLabelSelectorRequirementWithMap(
			service_meta.LabelSelectorOpIn, deployer.iopTemplates.DiscoverySelectorLabels)
	}
	parseIopTmplParams.ExternalIstiodAddress = deployer.iopTemplates.ExternalIstiodAddress
	parseIopTmplParams.VpcEniTemplates = deployer.iopTemplates.VpcEniTemplates

	// 开启链路追踪
	if deployer.iopTemplates.TraceEnabled {
		parseIopTmplParams.SamplingRate = deployer.iopTemplates.SamplingRate
		parseIopTmplParams.Address = deployer.iopTemplates.Address
	} else {
		parseIopTmplParams.SamplingRate = 1
		parseIopTmplParams.Address = constants.Zipkin + "." + deployer.iopTemplates.Namespace + constants.Port
	}
	iopPrefixName, err := deployer.generateSuffixName(ctx)
	if err != nil {
		return err
	}
	desIopTmplValue := fmt.Sprintf("%s_istio_iop.yaml", iopPrefixName)
	desIopTmplPath := path.Join(tagPath, desIopTmplValue)
	err = tmpl.EvaluatePathTmpl(ctx, srcIopTmplPath, desIopTmplPath, parseIopTmplParams)
	if err != nil {
		return err
	}
	deployer.iopTemplates.IopTemplatePath = desIopTmplPath
	return nil
}

func (deployer *Deployer) manifestK8sObject(ctx csmContext.CsmContext, cmd string) ([]string, error) {
	outStr, errStr, err := deployer.execService.Exec(ctx, cmd)
	if len(errStr) > 0 || err != nil {
		return nil, fmt.Errorf("istioctl manifest generate errStr %s err %v", string(errStr), err)
	}
	objectsList, err := object.ManifestK8sObject(ctx, string(outStr))
	return objectsList, err
}

func (deployer *Deployer) manifestInstallK8sObject(ctx csmContext.CsmContext) ([]string, error) {
	jwtPolicy := jwt.SupportedJWTPolicy(deployer.instanceClient.Kube())
	jwtPolicyValue := jwt.FirstPartyJWT
	if jwtPolicy {
		jwtPolicyValue = jwt.ThirdPartyJWT
	}
	// https://istio.io/latest/docs/ops/best-practices/security/#configure-third-party-service-account-tokens
	// todo pre check for k8s env for istio
	istioctlBin := path.Join(deployer.iopTemplates.TemplatePath, deployer.iopTemplates.Tag,
		constants.BaseIstioBin, util.GetIstioCtl(ctx))
	installCmd := fmt.Sprintf("%s manifest generate --set values.global.jwtPolicy=%s -f  %s ",
		istioctlBin, jwtPolicyValue, deployer.iopTemplates.IopTemplatePath)
	return deployer.manifestK8sObject(ctx, installCmd)
}

func (deployer *Deployer) deployIstiodService(ctx csmContext.CsmContext) error {
	tagPath := path.Join(deployer.iopTemplates.TemplatePath, deployer.iopTemplates.Tag)
	iopTmplPath := path.Join(tagPath, constants.HostingTmpl)
	namespace := deployer.iopTemplates.Namespace
	parseIstiodTmplParams := NewIstiodServiceParams(namespace)
	istiodServiceTmplPath := path.Join(iopTmplPath, constants.HostingCceIstiodServiceTmplName)
	istiodServiceValue, err := tmpl.EvaluateIstiodService(ctx, istiodServiceTmplPath, parseIstiodTmplParams)
	if err != nil {
		return err
	}
	err = kube.CreateOrUpdateK8sResource(ctx, deployer.instanceClient, []string{istiodServiceValue})
	if err != nil {
		return err
	}
	err = kube.WaitServiceReady(ctx, deployer.instanceClient, constants.IstiodServiceName, namespace)
	if err != nil {
		ctx.CsmLogger().Errorf(fmt.Sprintf("waiting %s ready err %v", constants.IstiodServiceName, err))
		return err
	}
	return nil
}

// filterIstioK8sObjects 目前做了修改，该方法只能用来处理托管网格
func filterIstioK8sObjects(ctx csmContext.CsmContext, objects []string) []string {
	groupKindObjects := make([]string, 0)
	for _, o := range objects {
		obj, parseErr := object.ParseYAMLToK8sObject([]byte(o))
		if parseErr != nil {
			ctx.CsmLogger().Errorf("ParseYAMLToK8sObject err %v", parseErr)
			return []string{}
		}
		gk := obj.Group + "/" + obj.Kind
		if gk == "/Service" && obj.Name == constants.IstiodServiceName {
			ctx.CsmLogger().Infof("exclude resource %s=%s", gk, obj.Name)
			continue
		}
		ctx.CsmLogger().Debugf("add resource %s=%s", gk, obj.Name)

		// TODO 适配VK，仅仅验证过1.14.6版本
		// 清理掉configMap::istio-sidecar-inject中的HOST_IP
		if obj.Kind == "ConfigMap" && obj.Name == constants.IstioConfigMapInjectName {
			lines := strings.Split(o, "\n")
			newLines := make([]string, 0, len(lines))

			for i := 0; i < len(lines); i++ {
				if strings.Contains(lines[i], "name: HOST_IP") {
					// 跳过 HOST_IP 相关的行
					// TODO cm中格式如下
					// - name: HOST_IP
					//   valueFrom:
					//     fieldRef:
					//       fieldPath: status.hostIP
					i += 3
					continue
				}
				newLines = append(newLines, lines[i])
			}
			groupKindObjects = append(groupKindObjects, strings.Join(newLines, "\n"))

		} else {
			groupKindObjects = append(groupKindObjects, o)
		}
	}
	return groupKindObjects
}

// preUnInstall 在安装前预处理，获取相关信息并设置到iopTemplates中
// ctx csmContext.CsmContext 上下文对象
// iopTemplates *IopTemplates 包含部署参数的结构体指针
// 返回值 error 如果出错则返回非nil错误
func (deployer *Deployer) preUnInstall(ctx csmContext.CsmContext, iopTemplates *IopTemplates) error {
	region := iopTemplates.ClusterRegion
	clusterId := iopTemplates.CceClusterUuid

	// TODO 根据托管与独立区别有待优化
	instanceClient, err := deployer.cceService.NewClient(ctx, region, clusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	deployer.iopTemplates = iopTemplates
	deployer.instanceClient = instanceClient
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("get pwd error %v", err)
		return err
	}

	vpcEndpointName := deployer.iopTemplates.InstanceId
	discoveryIP, err := deployer.getHostingIstiodAddress(ctx)
	if err != nil {
		ctx.CsmLogger().Warnf("getHostingIstiodAddress get error %v", err)
	}
	if len(discoveryIP) == 0 {
		discoveryIP = constants.DefaultDiscoveryIP
	}
	ctx.CsmLogger().Infof("the discoveryIP is %s", discoveryIP)

	var endpointID string
	var blbServiceName string
	args := &endpoint.ListEndpointArgs{
		VpcId: deployer.iopTemplates.VpcEniTemplates.VpcID,
	}
	res, err := deployer.vpcService.ListEndpoints(ctx, args, region)
	if err != nil {
		ctx.CsmLogger().Warnf("listEndpoints get error %v", err)
	} else {
		for _, value := range res.Endpoints {
			if value.Name == vpcEndpointName && value.IpAddress == discoveryIP {
				ctx.CsmLogger().Infof("%v", value)
				endpointID = value.EndpointId
				blbServiceName = value.Service
				break
			}
		}
	}
	ctx.CsmLogger().Infof("the endpointID is 【%s】, discoveryIP is 【%s】,blbServiceName is 【%s】",
		endpointID, discoveryIP, blbServiceName)
	deployer.iopTemplates.ExternalIstiodAddress = discoveryIP
	deployer.iopTemplates.EndpointID = endpointID
	deployer.iopTemplates.BlbServiceName = blbServiceName

	if len(deployer.iopTemplates.TemplatePath) == 0 {
		deployer.iopTemplates.TemplatePath = path.Join(pwd, constants.Templates, constants.HostingBaseIstioTemplate)
	}
	return nil
}

// UnInstallInstanceMesh 删除托管服务网格实例
func (deployer *Deployer) UnInstallInstanceMesh(ctx csmContext.CsmContext, iopTemplates *IopTemplates) error {
	// 删除服务网格实例前置操作
	err := deployer.preUnInstall(ctx, iopTemplates)
	if err != nil {
		return err
	}

	// 解析 istio 删除模版
	err = deployer.parseTemplate(ctx)
	if err != nil {
		return err
	}

	// 删除 istio 集群
	objectsList, err := deployer.manifestInstallK8sObject(ctx)
	if err != nil {
		return err
	}
	objectsList = object.GetsK8sObjectWithExcludeGroupKind(ctx, objectsList, []string{object.CustomResourceDefinitionStr})
	ctx.CsmLogger().Infof("the uninstall resource length %v", len(objectsList))
	err = kube.DeleteK8sResource(ctx, deployer.instanceClient, objectsList)
	if err != nil {
		return err
	}

	installNamespace := deployer.iopTemplates.Namespace
	// 删除命名空间
	err = deployer.removeNamespace(ctx, deployer.instanceClient, installNamespace)
	if err != nil {
		return err
	}

	// 删除实例后置操作
	err = deployer.postUnInstallI(ctx, iopTemplates)
	if err != nil {
		return err
	}

	ctx.CsmLogger().Infof("uninstall namespace=%s successful", installNamespace)
	return nil
}

func (deployer *Deployer) postUnInstallI(ctx csmContext.CsmContext, iopTemplates *IopTemplates) error {
	// 托管集群需要删除服务发布点
	serviceName := deployer.iopTemplates.BlbServiceName
	args := &blbService.DeleteBlbServiceArgs{
		Service: serviceName,
	}
	ctx.CsmLogger().Infof("deleteBlbService Service=【%s】 in blb service", serviceName)
	err := deployer.blbService.DeleteBlbService(ctx, args, deployer.iopTemplates.ClusterRegion)
	if err != nil {
		ctx.CsmLogger().Warnf("deleteBlbService in blb service error %v", err)
	} else {
		ctx.CsmLogger().Infof("deleteBlbService Service=【%s】 in blb service success", serviceName)
	}

	// 用户集群需要删除服务网卡
	region := deployer.iopTemplates.InstanceRegion
	endpointId := deployer.iopTemplates.EndpointID

	ctx.CsmLogger().Infof("deleteEndpoint endpointId=【%s】 in vpc endpoint", endpointId)
	err = deployer.vpcService.DeleteEndpoint(ctx, endpointId, region)
	if err != nil {
		ctx.CsmLogger().Warnf("deleteEndpoint in vpc endpoint error %v", err)
	} else {
		ctx.CsmLogger().Infof("deleteEndpoint endpointId=【%s】in vpc endpoint success", endpointId)
	}

	// remove all temp files
	err = deployer.removeAllTempFile(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (deployer *Deployer) preInstall(ctx csmContext.CsmContext, iopTemplates *IopTemplates) error {
	region := iopTemplates.ClusterRegion
	version := iopTemplates.Tag
	clusterId := iopTemplates.CceClusterUuid
	systemNamespace := iopTemplates.Namespace
	ctx.CsmLogger().Infof("the value region=%s,version=%s,clusterId=%s,systemNamespace=%s",
		region, version, clusterId, systemNamespace)
	// TODO 根据托管与独立区别有待优化
	instanceClient, err := deployer.cceService.NewClient(ctx, region, clusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("cce GetHostingClient error %v", err)
		return err
	}
	certParams := ca.CertParams{
		Region:          region,
		Version:         version,
		ClusterId:       clusterId,
		SystemNamespace: systemNamespace,
	}
	caService := ca.NewCertService(instanceClient, certParams)

	deployer.caService = caService
	deployer.instanceClient = instanceClient
	deployer.iopTemplates = iopTemplates
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("get pwd error %v", err)
		return err
	}
	if len(deployer.iopTemplates.RootCertPath) == 0 {
		deployer.iopTemplates.RootCertPath = path.Join(pwd, constants.Templates, constants.CertBasePath)
	}
	if len(deployer.iopTemplates.TemplatePath) == 0 {
		deployer.iopTemplates.TemplatePath = path.Join(pwd, constants.Templates, constants.HostingBaseIstioTemplate)
	}
	return nil
}

// createBlbServiceAndUserVpcEndpoint 在托管集群创建服务发布点、用户集群创建服务网卡
func (deployer *Deployer) createBlbServiceAndUserVpcEndpoint(ctx csmContext.CsmContext) error {
	// 获取服务 istiod LoadBalancer 的 ID
	loadBalancerID, err := deployer.getIstiodBlbId(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("getIstiodBlbId error %v", err)
		return err
	}
	ctx.CsmLogger().Infof("the value of [%s] is [%s]", constants.CceLoadBalancerID, loadBalancerID)

	// 托管集群创建服务发布点
	region := deployer.iopTemplates.ClusterRegion
	name := deployer.iopTemplates.InstanceId
	serviceName := strings.ReplaceAll(name, constants.SplitSlash, "")

	ctx.CsmLogger().Infof("the value of CreateBlbService region=%s,name=%s,description=%s,"+
		"serviceName=%s", region, name, constants.CsmBlbServiceDefaultDescribe, serviceName)

	args := &blbService.CreateBlbServiceArgs{
		ClientToken: bceUtil.GetClientToken(),
		Name:        name,
		Description: constants.CsmBlbServiceDefaultDescribe,
		ServiceName: serviceName,
		InstanceID:  loadBalancerID,
		AuthList:    blbService.NewDefaultAuth(),
	}
	createBlbServiceResult, err := deployer.blbService.CreateBlbService(ctx, args, deployer.iopTemplates.ClusterRegion)
	if err != nil {
		ctx.CsmLogger().Errorf("createBlbService in blb service error %v", err)
		return err
	}
	service := createBlbServiceResult.Service
	// TODO 安装或者与第三方 API 交互最好是由 controller 实现（重试、校验等逻辑）
	getBlbServiceArgs := &blbService.GetBlbServiceArgs{
		Service: service,
	}
	getErr := wait.PollImmediate(constants.PollInternal, constants.Internal, func() (done bool, err error) {
		getBlbServiceResult, err := deployer.blbService.GetBlbService(ctx, getBlbServiceArgs, deployer.iopTemplates.ClusterRegion)
		if err != nil {
			ctx.CsmLogger().Infof("GetBlbService in blb service error %v", err)
			return false, err
		}
		instanceID := getBlbServiceResult.InstanceId
		ctx.CsmLogger().Infof("GetBlbService instanceID is %s", instanceID)
		return len(instanceID) > 0, nil
	})
	if getErr != nil {
		ctx.CsmLogger().Errorf("GetBlbService in blb service error %v", getErr)
		return getErr
	}

	// 用户集群创建服务网卡
	vpcEndpointName := deployer.iopTemplates.InstanceId
	vpcEndpointService := service
	vpcEni := deployer.iopTemplates.VpcEniTemplates

	createEndpointArgs := &endpoint.CreateEndpointArgs{
		ClientToken: bceUtil.GetClientToken(),
		VpcId:       vpcEni.VpcID,
		Name:        vpcEndpointName,
		SubnetId:    vpcEni.SubnetID,
		Service:     vpcEndpointService,
		Description: constants.CsmVpcEndpointDefaultDescribe,
		Billing: &endpoint.Billing{
			PaymentTiming: "Postpaid",
		},
	}

	createEndpointResult, err := deployer.vpcService.CreateEndpoint(ctx, createEndpointArgs, region)
	if err != nil {
		ctx.CsmLogger().Errorf("createEndpoint in vpc endpoint error %v", err)
		return err
	}
	ctx.CsmLogger().Infof("the result of vpc endpoint %v", createEndpointResult)
	deployer.iopTemplates.ExternalIstiodAddress = createEndpointResult.IpAddress
	return nil
}

func (deployer *Deployer) getIstiodBlbId(ctx csmContext.CsmContext) (string, error) {
	namespace := deployer.iopTemplates.Namespace
	svc, err := deployer.instanceClient.Kube().CoreV1().Services(namespace).Get(context.TODO(),
		constants.IstiodServiceName, metav1.GetOptions{})
	if err != nil {
		return "", err
	}
	var bldID string
	if svc.Spec.Type == v1.ServiceTypeLoadBalancer {
		annotations := svc.ObjectMeta.Annotations
		if annotations != nil {
			bldID = annotations[constants.CceLoadBalancerID]
		}
	}
	return bldID, nil
}

// InstallInstanceMesh 部署托管服务网格实例或者对应的用户集群
func (deployer *Deployer) InstallInstanceMesh(ctx csmContext.CsmContext, iopTemplates *IopTemplates) error {
	// 安装实例前置操作
	err := deployer.preInstall(ctx, iopTemplates)
	if err != nil {
		return err
	}

	// 创建证书、入库
	err = deployer.createIstioCa(ctx)
	if err != nil {
		return err
	}

	// 部署 istiod service
	err = deployer.deployIstiodService(ctx)
	if err != nil {
		return err
	}

	// 创建服务发布点与服务网卡
	err = deployer.createBlbServiceAndUserVpcEndpoint(ctx)
	if err != nil {
		return err
	}

	// 解析模版 istio 模板
	err = deployer.parseTemplate(ctx)
	if err != nil {
		return err
	}

	// 创建 istio 集群
	objectsList, err := deployer.manifestInstallK8sObject(ctx)
	if err != nil {
		return err
	}
	filterObjectsList := filterIstioK8sObjects(ctx, objectsList)
	ctx.CsmLogger().Infof("the install resource length %v", len(filterObjectsList))
	err = kube.CreateOrUpdateK8sResource(ctx, deployer.instanceClient, filterObjectsList)
	if err != nil {
		return err
	}

	paasType := deployer.iopTemplates.PaasType
	if paasType == meta.PaaSTypeEKS {
		// todo eks
	}

	// 安装实例后置操作
	err = deployer.postInstallI(ctx, iopTemplates)
	if err != nil {
		return err
	}

	namespace := deployer.iopTemplates.Namespace
	ctx.CsmLogger().Infof("install istio namespace=%s successful", namespace)
	return nil
}

func (deployer *Deployer) postInstallI(ctx csmContext.CsmContext, iopTemplates *IopTemplates) error {
	// remove all temp files
	err := deployer.removeAllTempFile(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (deployer *Deployer) preRemoteUserMesh(ctx csmContext.CsmContext, remoteUserIopTemplate *IopTemplates) error {
	clusterRegion := remoteUserIopTemplate.ClusterRegion
	clusterId := remoteUserIopTemplate.CceClusterUuid
	version := remoteUserIopTemplate.Tag
	systemNamespace := remoteUserIopTemplate.Namespace

	// TODO 根据托管与独立区别有待优化，初始化用户集群
	client, err := deployer.cceService.NewClient(ctx, clusterRegion, clusterId, meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("cce NewClient error %v", err)
		return err
	}
	deployer.client = client

	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("get pwd error %v", err)
		return err
	}

	certParams := ca.CertParams{
		Region:          clusterRegion,
		Version:         version,
		ClusterId:       clusterId,
		SystemNamespace: systemNamespace,
	}
	caService := ca.NewCertService(client, certParams)

	deployer.caService = caService
	deployer.iopTemplates = remoteUserIopTemplate
	if len(deployer.iopTemplates.TemplatePath) == 0 {
		deployer.iopTemplates.TemplatePath = path.Join(pwd, constants.Templates, constants.HostingBaseIstioTemplate)
	}

	if len(deployer.iopTemplates.RootCertPath) == 0 {
		deployer.iopTemplates.RootCertPath = path.Join(pwd, constants.Templates, constants.CertBasePath)
	}

	// 初始化托管集群
	instanceRegion := remoteUserIopTemplate.InstanceRegion
	instanceClusterId := remoteUserIopTemplate.InstanceCceClusterUuid
	instanceClient, instanceClientErr := deployer.cceService.NewClient(ctx, instanceRegion, instanceClusterId, meta.HostingMeshType)
	if instanceClientErr != nil {
		ctx.CsmLogger().Errorf("cce external NewClient error %v", instanceClientErr)
		return instanceClientErr
	}
	deployer.instanceClient = instanceClient
	return nil
}

func (deployer *Deployer) allowHostingAccess(ctx csmContext.CsmContext) error {
	return deployer.createRemoteSecret(ctx, false)
}

// 创建remote config
func (deployer *Deployer) allowHostingConfigAccess(ctx csmContext.CsmContext) error {
	return deployer.createRemoteSecret(ctx, true)
}

func (deployer *Deployer) createRemoteSecret(ctx csmContext.CsmContext, isConfigCluster bool) error {
	if deployer.iopTemplates.KubeConfigPath == "" {
		return errors.New("KubeConfig path is nil")
	}
	if deployer.instanceClient == nil {
		return errors.New("instance client is nil")
	}

	remoteSecretName := deployer.iopTemplates.ClusterRegion + "-" + deployer.iopTemplates.CceClusterUuid
	restConfig, err := clientcmd.BuildConfigFromFlags("", deployer.iopTemplates.VpcKubeConfigPath)
	if err != nil {
		return fmt.Errorf("failed to create k8s rest client: %s", err)
	}
	vpcAPIServer := restConfig.Host

	istioctlBin := path.Join(deployer.iopTemplates.TemplatePath, deployer.iopTemplates.Tag,
		constants.BaseIstioBin, util.GetIstioCtl(ctx))

	// configCluster需要使用sa为istiod，示例创建语句如下：
	// istioctl x create-remote-secret --kubeconfig= --type=config --namespace=  --name= --service-account=istiod --create-service-account=false
	createRemoteSecretCmd := ""
	if isConfigCluster {
		createRemoteSecretCmd = fmt.Sprintf("%s x create-remote-secret --create-service-account=true "+
			"--service-account=istiod --kubeconfig=%s --name=%s --type=config --server=%s --namespace %s",
			istioctlBin, deployer.iopTemplates.KubeConfigPath, remoteSecretName, vpcAPIServer, deployer.iopTemplates.Namespace)
	} else {
		createRemoteSecretCmd = fmt.Sprintf("%s x create-remote-secret --create-service-account=false --kubeconfig=%s "+
			" --name=%s --type=remote --server=%s --namespace %s", istioctlBin, deployer.iopTemplates.KubeConfigPath, remoteSecretName,
			vpcAPIServer, deployer.iopTemplates.Namespace)
	}

	objectsList, err := deployer.manifestK8sObject(ctx, createRemoteSecretCmd)
	if err != nil {
		return err
	}

	err = kube.CreateOrUpdateK8sResource(ctx, deployer.instanceClient, objectsList)
	return err
}

func (deployer *Deployer) removeHostingAccess(ctx csmContext.CsmContext) error {
	if deployer.instanceClient == nil {
		return errors.New("instance client is nil")
	}
	remoteSecretName := deployer.iopTemplates.ClusterRegion + "-" + deployer.iopTemplates.CceClusterUuid
	err := deployer.instanceClient.Kube().CoreV1().Secrets(deployer.iopTemplates.Namespace).Delete(
		context.TODO(), constants.IstioRemoteSecretPrefix+remoteSecretName, metav1.DeleteOptions{})
	if err != nil && !kubeErrors.IsNotFound(err) {
		return err
	}
	ctx.CsmLogger().Infof("RemoveHostingAccess remove secret=%s in service mesh success", remoteSecretName)
	return nil
}

func (deployer *Deployer) removeHostingConfigAccess(ctx csmContext.CsmContext) error {
	if deployer.instanceClient == nil {
		return errors.New("instance client is nil")
	}
	err := deployer.instanceClient.Kube().CoreV1().Secrets(deployer.iopTemplates.Namespace).Delete(
		context.TODO(), constants.IstioRemoteConfigSecretName, metav1.DeleteOptions{})
	if err != nil && !kubeErrors.IsNotFound(err) {
		return err
	}
	ctx.CsmLogger().Infof("RemoveHostingAccess remove secret=%s in service mesh success", constants.IstioRemoteConfigSecretName)
	return nil
}

func (deployer *Deployer) WriteKubeConfig(ctx csmContext.CsmContext) (err error) {
	// TODO 根据托管与独立区别有待优化
	kubeConfig, err := deployer.cceService.GetCCEClusterKubeConfigByClusterUUID(ctx, deployer.iopTemplates.ClusterRegion,
		deployer.iopTemplates.CceClusterUuid, cce_v2.KubeConfigTypeInternal, meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("get kubeconfig from GetCCEClusterKubeConfigByClusterUUID error %v", err)
		return err
	}

	kubeConfigFileName, err := deployer.generateSuffixName(ctx)
	if err != nil {
		return err
	}
	kubeConfigName := fmt.Sprintf("%s_kubeconfig.yaml", kubeConfigFileName)
	kubeConfigPath := path.Join(deployer.iopTemplates.TemplatePath, deployer.iopTemplates.Tag, kubeConfigName)
	err = file.RewriteFile(ctx, kubeConfigPath, kubeConfig)
	if err != nil {
		return err
	}
	deployer.iopTemplates.KubeConfigPath = kubeConfigPath

	vpcKubeConfig, err := deployer.cceService.GetCCEClusterKubeConfigByClusterUUID(ctx, deployer.iopTemplates.ClusterRegion,
		deployer.iopTemplates.CceClusterUuid, cce_v2.KubeConfigTypeVPC, meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("get kubeconfig from GetCCEClusterKubeConfigByClusterUUID error %v", err)
		return err
	}
	vpcKubeConfigName := kubeConfigFileName + "_vpc.yaml"
	vpcKubeConfigPath := path.Join(deployer.iopTemplates.TemplatePath, deployer.iopTemplates.Tag, vpcKubeConfigName)
	err = file.RewriteFile(ctx, vpcKubeConfigPath, vpcKubeConfig)
	if err != nil {
		return err
	}
	deployer.iopTemplates.VpcKubeConfigPath = vpcKubeConfigPath
	return nil
}

// getHostingIstiodAddress 获取托管服务网格的控制平面的地址
// 对于与服务网格实例不同地域的用户集群，通过服务网卡获取控制平面的地址不可取，所以从 istio configmap 获取控制平面地址
func (deployer *Deployer) getHostingIstiodAddress(ctx csmContext.CsmContext) (string, error) {
	namespace := deployer.iopTemplates.Namespace
	ctx.CsmLogger().Infof("get discoveryAddress from %s configmap in %s namespace",
		constants.IstioConfigMapMeshName, namespace)
	var endpointVpcIP string
	istioConfigMap, err := deployer.instanceClient.Kube().CoreV1().ConfigMaps(namespace).Get(context.TODO(),
		constants.IstioConfimapName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("%v", err)
		return endpointVpcIP, err
	}
	meshValue := istioConfigMap.Data[constants.IstioConfigMapMeshName]
	mapData := make(map[string]interface{})
	err = yaml.Unmarshal([]byte(meshValue), &mapData)
	if err != nil {
		ctx.CsmLogger().Errorf("%v", err)
		return endpointVpcIP, err
	}
	defaultConfig := mapData[constants.IstiodefaultConfigName]
	defaultConfigValue := defaultConfig.(map[string]interface{})
	endpointVpcIP = defaultConfigValue[constants.IstiodiscoveryAddressName].(string)
	endpointVpcIP = strings.ReplaceAll(endpointVpcIP, constants.IstioDiscoveryAddressPort, "")
	ctx.CsmLogger().Infof("the IpAddress is 【%s】", endpointVpcIP)
	return endpointVpcIP, nil
}

func (deployer *Deployer) parseRemoteUserTemplate(ctx csmContext.CsmContext) error {
	tagPath := path.Join(deployer.iopTemplates.TemplatePath, deployer.iopTemplates.Tag)
	iopTmplPath := path.Join(tagPath, constants.HostingTmpl)
	var srcIopTmplPath string
	namespace := deployer.iopTemplates.Namespace
	region := deployer.iopTemplates.ClusterRegion
	paasType := deployer.iopTemplates.PaasType
	if paasType == meta.PaaSTypeCCE {
		srcIopTmplPath = path.Join(iopTmplPath, constants.HostingCceRemoteIopName)
	} else if paasType == meta.PaaSTypeEKS {
		srcIopTmplPath = path.Join(iopTmplPath, constants.HostingEksRemoteIopTmplName)
	}

	hostingIstiodAddress, err := deployer.getHostingIstiodAddress(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("getHostingIstiodAddress error %v", err)
		return err
	}

	cceClusterUuid := deployer.iopTemplates.CceClusterUuid
	iopRemoteCluster := util.GetStrWithSplit([]string{region, cceClusterUuid}, constants.SplitSlash)
	parseRemoteTemplateParams := NewParseRemoteTmplParams(namespace, hostingIstiodAddress, iopRemoteCluster, region)
	remoteUserIopPrefixName, err := deployer.generateSuffixName(ctx)
	if err != nil {
		return err
	}
	desIopTmplValue := fmt.Sprintf("%s_remote_user_istio_iop.yaml", remoteUserIopPrefixName)
	desIopTmplPath := path.Join(tagPath, desIopTmplValue)
	err = tmpl.EvaluatePathTmpl(ctx, srcIopTmplPath, desIopTmplPath, parseRemoteTemplateParams)
	if err != nil {
		return err
	}
	deployer.iopTemplates.IopTemplatePath = desIopTmplPath
	return nil
}

// parseRemoteConfigTemplate 仅仅支持cce remote config集群配置
func (deployer *Deployer) parseRemoteConfigTemplate(ctx csmContext.CsmContext) error {
	tagPath := path.Join(deployer.iopTemplates.TemplatePath, deployer.iopTemplates.Tag)
	iopTmplPath := path.Join(tagPath, constants.HostingTmpl)
	var srcIopTmplPath string
	namespace := deployer.iopTemplates.Namespace
	region := deployer.iopTemplates.ClusterRegion
	srcIopTmplPath = path.Join(iopTmplPath, constants.HostingCceRemoteConfigIopName)

	hostingIstiodAddress, err := deployer.getHostingIstiodAddress(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("getHostingIstiodAddress error %v", err)
		return err
	}

	cceClusterUuid := deployer.iopTemplates.CceClusterUuid
	iopRemoteCluster := util.GetStrWithSplit([]string{region, cceClusterUuid}, constants.SplitSlash)
	parseRemoteTemplateParams := NewParseRemoteTmplParams(namespace, hostingIstiodAddress, iopRemoteCluster, region)
	remoteUserIopPrefixName, err := deployer.generateSuffixName(ctx)
	if err != nil {
		return err
	}
	desIopTmplValue := fmt.Sprintf("%s_remote_config_user_istio_iop.yaml", remoteUserIopPrefixName)
	desIopTmplPath := path.Join(tagPath, desIopTmplValue)
	err = tmpl.EvaluatePathTmpl(ctx, srcIopTmplPath, desIopTmplPath, parseRemoteTemplateParams)
	if err != nil {
		return err
	}
	deployer.iopTemplates.IopTemplatePath = desIopTmplPath
	return nil
}

func (deployer *Deployer) manifestRemoteMeshK8sObject(ctx csmContext.CsmContext) ([]string, error) {
	istioctlBin := path.Join(deployer.iopTemplates.TemplatePath, deployer.iopTemplates.Tag,
		constants.BaseIstioBin, util.GetIstioCtl(ctx))
	cmd := fmt.Sprintf("%s manifest generate -f  %s", istioctlBin, deployer.iopTemplates.IopTemplatePath)
	return deployer.manifestK8sObject(ctx, cmd)
}

func (deployer *Deployer) InstallRemoteUserMesh(ctx csmContext.CsmContext, remoteUserIopTemplates *IopTemplates) error {
	// 安装托管实例对应的用户集群操作前
	err := deployer.preRemoteUserMesh(ctx, remoteUserIopTemplates)
	if err != nil {
		return err
	}

	// 创建 kubeconfig, 用于在托管实例给远程集群创建 secret
	err = deployer.WriteKubeConfig(ctx)
	if err != nil {
		return err
	}

	// 在用户集群创建 istio 资源
	err = deployer.parseRemoteUserTemplate(ctx)
	if err != nil {
		return err
	}
	// 在用户集群创建命名空间
	namespace := remoteUserIopTemplates.Namespace
	err = deployer.createNamespace(ctx, deployer.client, namespace, nil)
	if err != nil {
		return err
	}
	err = kube.WaitNamespaceReady(ctx, deployer.client, namespace)
	if err != nil {
		return err
	}
	// 用户集群部署 istio 资源
	objectsList, err := deployer.manifestRemoteMeshK8sObject(ctx)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("the install resource length %v", len(objectsList))
	err = kube.CreateOrUpdateK8sResource(ctx, deployer.client, objectsList)
	if err != nil {
		return err
	}

	// 在托管服务网格实例处，添加访问用户集群 secret
	err = deployer.allowHostingAccess(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("Allow hosting mesh instance access error %v", err)
		return err
	}

	// 安装远程集群后置操作
	err = deployer.postRemoteUserMesh(ctx, remoteUserIopTemplates)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("install remote user istio namespace=%s successful", namespace)
	return nil
}

// InstallRemoteConfigMesh 安装remote config集群配置
func (deployer *Deployer) InstallRemoteConfigMesh(ctx csmContext.CsmContext, remoteUserIopTemplates *IopTemplates) error {
	// 安装托管实例对应的用户集群操作前
	err := deployer.preRemoteUserMesh(ctx, remoteUserIopTemplates)
	if err != nil {
		return err
	}

	// 创建 kubeconfig, 用于在托管实例给远程集群创建 secret
	err = deployer.WriteKubeConfig(ctx)
	if err != nil {
		return err
	}

	// 在用户集群创建 istio 资源
	err = deployer.parseRemoteConfigTemplate(ctx)
	if err != nil {
		return err
	}
	// 在用户集群创建命名空间
	namespace := remoteUserIopTemplates.Namespace
	err = deployer.createNamespace(ctx, deployer.client, namespace, nil)
	if err != nil {
		return err
	}
	err = kube.WaitNamespaceReady(ctx, deployer.client, namespace)
	if err != nil {
		return err
	}

	// 创建证书、入库
	err = deployer.createIstioCa(ctx)
	if err != nil {
		return err
	}

	// 用户集群部署 istio 资源
	objectsList, err := deployer.manifestRemoteMeshK8sObject(ctx)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("the install resource length %v", len(objectsList))
	err = kube.CreateOrUpdateK8sResource(ctx, deployer.client, objectsList)
	if err != nil {
		return err
	}

	// 在托管服务网格实例处，添加访问用户config集群 secret
	err = deployer.allowHostingConfigAccess(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("Allow hosting mesh instance access error %v", err)
		return err
	}

	// 安装远程集群后置操作
	err = deployer.postRemoteUserMesh(ctx, remoteUserIopTemplates)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("install remote config user istio namespace=%s successful", namespace)
	return nil
}

func (deployer *Deployer) postRemoteUserMesh(ctx csmContext.CsmContext, remoteUserIopTemplates *IopTemplates) error {
	// remove all temp files
	err := deployer.removeAllTempFile(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (deployer *Deployer) UnInstallRemoteUserMesh(ctx csmContext.CsmContext, remoteUserIopTemplates *IopTemplates) error {
	// 删除托管实例对应的用户集群操作前
	err := deployer.preRemoteUserMesh(ctx, remoteUserIopTemplates)
	if err != nil {
		return err
	}

	// 在用户集群创建 istio 资源
	err = deployer.parseRemoteUserTemplate(ctx)
	if err != nil {
		return err
	}
	objectsList, err := deployer.manifestRemoteMeshK8sObject(ctx)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("the remote user resource length %v", len(objectsList))
	err = kube.DeleteK8sResource(ctx, deployer.client, objectsList)
	if err != nil {
		return err
	}

	// 在用户集群删除命名空间
	namespace := remoteUserIopTemplates.Namespace
	err = deployer.removeNamespace(ctx, deployer.client, namespace)
	if err != nil {
		return err
	}

	// 在托管服务网格实例处，删除访问用户集群 secret
	err = deployer.removeHostingAccess(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("Remove hosting mesh instance access error %v", err)
		return err
	}

	// 安装用户集群后置操作
	err = deployer.postRemoteUserMesh(ctx, remoteUserIopTemplates)
	if err != nil {
		return err
	}

	ctx.CsmLogger().Infof("uninstall remote user istio namespace=%s successful", namespace)
	return nil
}

func (deployer *Deployer) UnInstallRemoteConfigMesh(ctx csmContext.CsmContext, remoteUserIopTemplates *IopTemplates) error {
	// 删除托管实例对应的用户集群操作前
	err := deployer.preRemoteUserMesh(ctx, remoteUserIopTemplates)
	if err != nil {
		return err
	}

	// 在用户集群创建 istio 资源
	err = deployer.parseRemoteConfigTemplate(ctx)
	if err != nil {
		return err
	}
	objectsList, err := deployer.manifestRemoteMeshK8sObject(ctx)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("the remote config resource length %v", len(objectsList))
	err = kube.DeleteK8sResource(ctx, deployer.client, objectsList)
	if err != nil {
		return err
	}

	// 在用户集群删除命名空间
	namespace := remoteUserIopTemplates.Namespace
	err = deployer.removeNamespace(ctx, deployer.client, namespace)
	if err != nil {
		return err
	}

	// 在托管服务网格实例处，删除访问用户集群 secret
	err = deployer.removeHostingConfigAccess(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("Remove hosting mesh instance access error %v", err)
		return err
	}

	// 安装用户集群后置操作
	err = deployer.postRemoteUserMesh(ctx, remoteUserIopTemplates)
	if err != nil {
		return err
	}

	ctx.CsmLogger().Infof("uninstall remote config istio namespace=%s successful", namespace)
	return nil
}
