// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	hosting "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/deploy/hosting"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// InstallInstanceMesh mocks base method.
func (m *MockServiceInterface) InstallInstanceMesh(ctx context.CsmContext, iopTemplates *hosting.IopTemplates) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallInstanceMesh", ctx, iopTemplates)
	ret0, _ := ret[0].(error)
	return ret0
}

// InstallInstanceMesh indicates an expected call of InstallInstanceMesh.
func (mr *MockServiceInterfaceMockRecorder) InstallInstanceMesh(ctx, iopTemplates interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallInstanceMesh", reflect.TypeOf((*MockServiceInterface)(nil).InstallInstanceMesh), ctx, iopTemplates)
}

// InstallRemoteConfigMesh mocks base method.
func (m *MockServiceInterface) InstallRemoteConfigMesh(ctx context.CsmContext, userIopTemplate *hosting.IopTemplates) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallRemoteConfigMesh", ctx, userIopTemplate)
	ret0, _ := ret[0].(error)
	return ret0
}

// InstallRemoteConfigMesh indicates an expected call of InstallRemoteConfigMesh.
func (mr *MockServiceInterfaceMockRecorder) InstallRemoteConfigMesh(ctx, userIopTemplate interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallRemoteConfigMesh", reflect.TypeOf((*MockServiceInterface)(nil).InstallRemoteConfigMesh), ctx, userIopTemplate)
}

// InstallRemoteUserMesh mocks base method.
func (m *MockServiceInterface) InstallRemoteUserMesh(ctx context.CsmContext, userIopTemplate *hosting.IopTemplates) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallRemoteUserMesh", ctx, userIopTemplate)
	ret0, _ := ret[0].(error)
	return ret0
}

// InstallRemoteUserMesh indicates an expected call of InstallRemoteUserMesh.
func (mr *MockServiceInterfaceMockRecorder) InstallRemoteUserMesh(ctx, userIopTemplate interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallRemoteUserMesh", reflect.TypeOf((*MockServiceInterface)(nil).InstallRemoteUserMesh), ctx, userIopTemplate)
}

// UnInstallInstanceMesh mocks base method.
func (m *MockServiceInterface) UnInstallInstanceMesh(ctx context.CsmContext, iopTemplates *hosting.IopTemplates) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnInstallInstanceMesh", ctx, iopTemplates)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnInstallInstanceMesh indicates an expected call of UnInstallInstanceMesh.
func (mr *MockServiceInterfaceMockRecorder) UnInstallInstanceMesh(ctx, iopTemplates interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnInstallInstanceMesh", reflect.TypeOf((*MockServiceInterface)(nil).UnInstallInstanceMesh), ctx, iopTemplates)
}

// UnInstallRemoteConfigMesh mocks base method.
func (m *MockServiceInterface) UnInstallRemoteConfigMesh(ctx context.CsmContext, remoteUserIopTemplates *hosting.IopTemplates) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnInstallRemoteConfigMesh", ctx, remoteUserIopTemplates)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnInstallRemoteConfigMesh indicates an expected call of UnInstallRemoteConfigMesh.
func (mr *MockServiceInterfaceMockRecorder) UnInstallRemoteConfigMesh(ctx, remoteUserIopTemplates interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnInstallRemoteConfigMesh", reflect.TypeOf((*MockServiceInterface)(nil).UnInstallRemoteConfigMesh), ctx, remoteUserIopTemplates)
}

// UnInstallRemoteUserMesh mocks base method.
func (m *MockServiceInterface) UnInstallRemoteUserMesh(ctx context.CsmContext, remoteUserIopTemplates *hosting.IopTemplates) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnInstallRemoteUserMesh", ctx, remoteUserIopTemplates)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnInstallRemoteUserMesh indicates an expected call of UnInstallRemoteUserMesh.
func (mr *MockServiceInterfaceMockRecorder) UnInstallRemoteUserMesh(ctx, remoteUserIopTemplates interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnInstallRemoteUserMesh", reflect.TypeOf((*MockServiceInterface)(nil).UnInstallRemoteUserMesh), ctx, remoteUserIopTemplates)
}
