package hosting

import (
	"github.com/jinzhu/gorm"

	modelmeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/deploy"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Option struct {
	DB *dbutil.DB
}

func NewOption(d *gorm.DB) *Option {
	return &Option{
		DB: dbutil.NewDB(d),
	}
}

type FrontTemplates struct {
	Namespace               string
	Hub                     string
	Tag                     string
	InstanceRegion          string
	InstanceCceClusterUuid  string
	InstanceId              string
	ClusterRegion           string
	ClusterName             string
	CceClusterUuid          string
	AccessLogFile           bool
	DiscoverySelectorLabels map[string]string
}

type IopTemplates struct {
	PaasType              modelmeta.PaaSType
	RootCertPath          string
	TemplatePath          string
	IopTemplatePath       string
	KubeConfigPath        string
	VpcKubeConfigPath     string
	Network               string
	AccountId             string
	EndpointID            string
	BlbServiceName        string
	ExternalIstiodAddress string
	IsRemote              bool
	*FrontTemplates
	VpcEniTemplates  *VpcEniTemplates
	MatchExpressions []*meta.LabelSelectorRequirement
	Proxy            *deploy.Resources
	InitProxy        *deploy.Resources
	// 开启链路追踪
	TraceEnabled bool
	// trace 采样率
	SamplingRate float32
	// trace 服务地址
	Address string
}

type Eni struct {
	Num string
}

type EniRequests struct {
	Eni *Eni
}

type EniLimits struct {
	Eni *Eni
}

type EniAnnotation struct {
	EniRequests *EniRequests
	EniLimits   *EniLimits
}

type VpcEniTemplates struct {
	// 必选项。资源账户 ID，对端 VPC 的用户 ID
	AccountID string

	// VpcId vpc ID
	VpcID string

	// 必选项。VPC 所属网段，跨 VPC 弹性网卡所属 VPC 的网段
	VpcCidr string

	// 必选项。VPC 子网 ID，跨 VPC 弹性网卡所属的子网
	SubnetID string

	// 必选项。安全组ID，多个用逗号分隔
	SecurityGroupIds string

	// 可选项。跨 VPC 弹性网卡的主 IP 地址。若不指定，由 VPC 自动分配。
	PrivateIPAddress string

	EniAnnotation *EniAnnotation
}

type ParseIopTmplParams struct {
	Namespace  string
	Tag        string
	Hub        string
	InstanceId string
	// IopClusterId iop CLUSTER_ID region-cceId
	IopClusterId            string
	Network                 string
	AccessLogFile           bool
	IsRemote                bool
	ExternalIstiodAddress   string
	DiscoverySelectorLabels map[string]string
	MatchExpressions        []*meta.LabelSelectorRequirement
	Proxy                   *deploy.Resources
	InitProxy               *deploy.Resources
	VpcEniTemplates         *VpcEniTemplates
	// trace 采样率
	SamplingRate float32
	// trace 服务地址
	Address string
}

func NewParseIopTmplParams(namespace, tag, hub, instanceId, iopClusterId string) *ParseIopTmplParams {
	return &ParseIopTmplParams{
		Namespace:    namespace,
		Tag:          tag,
		Hub:          hub,
		InstanceId:   instanceId,
		IopClusterId: iopClusterId,
	}
}

type ParseIstiodTmplParams struct {
	Namespace string
}

func NewIstiodServiceParams(namespace string) *ParseIstiodTmplParams {
	return &ParseIstiodTmplParams{
		Namespace: namespace,
	}
}

type ParseRemoteTmplParams struct {
	Namespace            string
	HostingIstiodAddress string
	IopRemoteCluster     string
	NetWork              string
}

func NewParseRemoteTmplParams(namespace, hostingIstiodAddress, iopRemoteCluster, netWork string) *ParseRemoteTmplParams {
	return &ParseRemoteTmplParams{
		Namespace:            namespace,
		HostingIstiodAddress: hostingIstiodAddress,
		IopRemoteCluster:     iopRemoteCluster,
		NetWork:              netWork,
	}
}
