// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// AddGatewayDomain mocks base method.
func (m *MockServiceInterface) AddGatewayDomain(ctx context.CsmContext, domainConf *meta.DomainConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddGatewayDomain", ctx, domainConf)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddGatewayDomain indicates an expected call of AddGatewayDomain.
func (mr *MockServiceInterfaceMockRecorder) AddGatewayDomain(ctx, domainConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGatewayDomain", reflect.TypeOf((*MockServiceInterface)(nil).AddGatewayDomain), ctx, domainConf)
}

// DeleteGateway mocks base method.
func (m *MockServiceInterface) DeleteGateway(ctx context.CsmContext, instanceUUID, gatewayUUID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteGateway", ctx, instanceUUID, gatewayUUID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteGateway indicates an expected call of DeleteGateway.
func (mr *MockServiceInterfaceMockRecorder) DeleteGateway(ctx, instanceUUID, gatewayUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGateway", reflect.TypeOf((*MockServiceInterface)(nil).DeleteGateway), ctx, instanceUUID, gatewayUUID)
}

// DeleteGatewayDomain mocks base method.
func (m *MockServiceInterface) DeleteGatewayDomain(ctx context.CsmContext, domainConf *meta.DomainConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteGatewayDomain", ctx, domainConf)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteGatewayDomain indicates an expected call of DeleteGatewayDomain.
func (mr *MockServiceInterfaceMockRecorder) DeleteGatewayDomain(ctx, domainConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGatewayDomain", reflect.TypeOf((*MockServiceInterface)(nil).DeleteGatewayDomain), ctx, domainConf)
}

// ExistGatewayWithInstanceUUID mocks base method.
func (m *MockServiceInterface) ExistGatewayWithInstanceUUID(ctx context.CsmContext, instanceUUID string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExistGatewayWithInstanceUUID", ctx, instanceUUID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExistGatewayWithInstanceUUID indicates an expected call of ExistGatewayWithInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) ExistGatewayWithInstanceUUID(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExistGatewayWithInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).ExistGatewayWithInstanceUUID), ctx, instanceUUID)
}

// GenerateGatewayID mocks base method.
func (m *MockServiceInterface) GenerateGatewayID(ctx context.CsmContext) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateGatewayID", ctx)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateGatewayID indicates an expected call of GenerateGatewayID.
func (mr *MockServiceInterfaceMockRecorder) GenerateGatewayID(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateGatewayID", reflect.TypeOf((*MockServiceInterface)(nil).GenerateGatewayID), ctx)
}

// GetGatewayBlbList mocks base method.
func (m *MockServiceInterface) GetGatewayBlbList(ctx context.CsmContext, instanceUUID string) (*meta.GatewayBlbListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGatewayBlbList", ctx, instanceUUID)
	ret0, _ := ret[0].(*meta.GatewayBlbListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGatewayBlbList indicates an expected call of GetGatewayBlbList.
func (mr *MockServiceInterfaceMockRecorder) GetGatewayBlbList(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGatewayBlbList", reflect.TypeOf((*MockServiceInterface)(nil).GetGatewayBlbList), ctx, instanceUUID)
}

// GetGatewayDetail mocks base method.
func (m *MockServiceInterface) GetGatewayDetail(ctx context.CsmContext, instanceUUID, gatewayUUID string) (*meta.GatewayDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGatewayDetail", ctx, instanceUUID, gatewayUUID)
	ret0, _ := ret[0].(*meta.GatewayDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGatewayDetail indicates an expected call of GetGatewayDetail.
func (mr *MockServiceInterfaceMockRecorder) GetGatewayDetail(ctx, instanceUUID, gatewayUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGatewayDetail", reflect.TypeOf((*MockServiceInterface)(nil).GetGatewayDetail), ctx, instanceUUID, gatewayUUID)
}

// GetGatewayDomainList mocks base method.
func (m *MockServiceInterface) GetGatewayDomainList(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.GatewayDomainListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGatewayDomainList", ctx, mrp)
	ret0, _ := ret[0].(*meta.GatewayDomainListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGatewayDomainList indicates an expected call of GetGatewayDomainList.
func (mr *MockServiceInterfaceMockRecorder) GetGatewayDomainList(ctx, mrp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGatewayDomainList", reflect.TypeOf((*MockServiceInterface)(nil).GetGatewayDomainList), ctx, mrp)
}

// GetGatewayIngress mocks base method.
func (m *MockServiceInterface) GetGatewayIngress(ctx context.CsmContext, instanceUUID, gatewayID string) (*meta.IngressParam, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGatewayIngress", ctx, instanceUUID, gatewayID)
	ret0, _ := ret[0].(*meta.IngressParam)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGatewayIngress indicates an expected call of GetGatewayIngress.
func (mr *MockServiceInterfaceMockRecorder) GetGatewayIngress(ctx, instanceUUID, gatewayID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGatewayIngress", reflect.TypeOf((*MockServiceInterface)(nil).GetGatewayIngress), ctx, instanceUUID, gatewayID)
}

// GetGatewayList mocks base method.
func (m *MockServiceInterface) GetGatewayList(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.GatewayListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGatewayList", ctx, mrp)
	ret0, _ := ret[0].(*meta.GatewayListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGatewayList indicates an expected call of GetGatewayList.
func (mr *MockServiceInterfaceMockRecorder) GetGatewayList(ctx, mrp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGatewayList", reflect.TypeOf((*MockServiceInterface)(nil).GetGatewayList), ctx, mrp)
}

// ModifyGatewayBlsTask mocks base method.
func (m *MockServiceInterface) ModifyGatewayBlsTask(ctx context.CsmContext, logConf *meta.LogConf) (*meta.Log, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyGatewayBlsTask", ctx, logConf)
	ret0, _ := ret[0].(*meta.Log)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyGatewayBlsTask indicates an expected call of ModifyGatewayBlsTask.
func (mr *MockServiceInterfaceMockRecorder) ModifyGatewayBlsTask(ctx, logConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyGatewayBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).ModifyGatewayBlsTask), ctx, logConf)
}

// ModifyGatewayDomain mocks base method.
func (m *MockServiceInterface) ModifyGatewayDomain(ctx context.CsmContext, domainConf *meta.DomainConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyGatewayDomain", ctx, domainConf)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyGatewayDomain indicates an expected call of ModifyGatewayDomain.
func (mr *MockServiceInterfaceMockRecorder) ModifyGatewayDomain(ctx, domainConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyGatewayDomain", reflect.TypeOf((*MockServiceInterface)(nil).ModifyGatewayDomain), ctx, domainConf)
}

// ModifyGatewayHPA mocks base method.
func (m *MockServiceInterface) ModifyGatewayHPA(ctx context.CsmContext, hpaConf *meta.HpaConf) (*meta.HPA, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyGatewayHPA", ctx, hpaConf)
	ret0, _ := ret[0].(*meta.HPA)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyGatewayHPA indicates an expected call of ModifyGatewayHPA.
func (mr *MockServiceInterfaceMockRecorder) ModifyGatewayHPA(ctx, hpaConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyGatewayHPA", reflect.TypeOf((*MockServiceInterface)(nil).ModifyGatewayHPA), ctx, hpaConf)
}

// ModifyGatewayIngress mocks base method.
func (m *MockServiceInterface) ModifyGatewayIngress(ctx context.CsmContext, instanceUUID, gatewayID string, ingressParam *meta.IngressParam) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyGatewayIngress", ctx, instanceUUID, gatewayID, ingressParam)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyGatewayIngress indicates an expected call of ModifyGatewayIngress.
func (mr *MockServiceInterfaceMockRecorder) ModifyGatewayIngress(ctx, instanceUUID, gatewayID, ingressParam interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyGatewayIngress", reflect.TypeOf((*MockServiceInterface)(nil).ModifyGatewayIngress), ctx, instanceUUID, gatewayID, ingressParam)
}

// ModifyGatewayMonitor mocks base method.
func (m *MockServiceInterface) ModifyGatewayMonitor(ctx context.CsmContext, instanceUUID, gatewayID string, monitorConf *meta.Monitor) (*meta.Monitor, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyGatewayMonitor", ctx, instanceUUID, gatewayID, monitorConf)
	ret0, _ := ret[0].(*meta.Monitor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyGatewayMonitor indicates an expected call of ModifyGatewayMonitor.
func (mr *MockServiceInterfaceMockRecorder) ModifyGatewayMonitor(ctx, instanceUUID, gatewayID, monitorConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyGatewayMonitor", reflect.TypeOf((*MockServiceInterface)(nil).ModifyGatewayMonitor), ctx, instanceUUID, gatewayID, monitorConf)
}

// ModifyGatewayResourceQuota mocks base method.
func (m *MockServiceInterface) ModifyGatewayResourceQuota(ctx context.CsmContext, resourceQuotaConf *meta.ResourceQuotaConf) (*meta.ResourceQuota, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyGatewayResourceQuota", ctx, resourceQuotaConf)
	ret0, _ := ret[0].(*meta.ResourceQuota)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyGatewayResourceQuota indicates an expected call of ModifyGatewayResourceQuota.
func (mr *MockServiceInterfaceMockRecorder) ModifyGatewayResourceQuota(ctx, resourceQuotaConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyGatewayResourceQuota", reflect.TypeOf((*MockServiceInterface)(nil).ModifyGatewayResourceQuota), ctx, resourceQuotaConf)
}

// ModifyGatewayTLSAcceleration mocks base method.
func (m *MockServiceInterface) ModifyGatewayTLSAcceleration(ctx context.CsmContext, tlsAccConf *meta.TLSAccConf) (*meta.TLSAcc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyGatewayTLSAcceleration", ctx, tlsAccConf)
	ret0, _ := ret[0].(*meta.TLSAcc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyGatewayTLSAcceleration indicates an expected call of ModifyGatewayTLSAcceleration.
func (mr *MockServiceInterfaceMockRecorder) ModifyGatewayTLSAcceleration(ctx, tlsAccConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyGatewayTLSAcceleration", reflect.TypeOf((*MockServiceInterface)(nil).ModifyGatewayTLSAcceleration), ctx, tlsAccConf)
}

// NewGateway mocks base method.
func (m *MockServiceInterface) NewGateway(ctx context.CsmContext, gateway *meta.GatewayModel, hpaConf *meta.HpaConf, blbConf *meta.BlbConf, logConf *meta.LogConf, tlsAccConf *meta.TLSAccConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewGateway", ctx, gateway, hpaConf, blbConf, logConf, tlsAccConf)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewGateway indicates an expected call of NewGateway.
func (mr *MockServiceInterfaceMockRecorder) NewGateway(ctx, gateway, hpaConf, blbConf, logConf, tlsAccConf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewGateway", reflect.TypeOf((*MockServiceInterface)(nil).NewGateway), ctx, gateway, hpaConf, blbConf, logConf, tlsAccConf)
}
