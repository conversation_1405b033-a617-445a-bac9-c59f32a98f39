package gateway

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	NewGateway(ctx context.CsmContext, gateway *meta.GatewayModel, hpaConf *meta.HpaConf, blbConf *meta.BlbConf,
		logConf *meta.LogConf, tlsAccConf *meta.TLSAccConf) error
	GenerateGatewayID(ctx context.CsmContext) (string, error)
	DeleteGateway(ctx context.CsmContext, instanceUUID, gatewayUUID string) error
	GetGatewayList(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.GatewayListResponse, error)
	GetGatewayDetail(ctx context.CsmContext, instanceUUID, gatewayUUID string) (*meta.GatewayDetailResponse, error)
	GetGatewayBlbList(ctx context.CsmContext, instanceUUID string) (*meta.GatewayBlbListResponse, error)
	ExistGatewayWithInstanceUUID(ctx context.CsmContext, instanceUUID string) (bool, error)

	GetGatewayDomainList(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.GatewayDomainListResponse, error)
	AddGatewayDomain(ctx context.CsmContext, domainConf *meta.DomainConf) error
	DeleteGatewayDomain(ctx context.CsmContext, domainConf *meta.DomainConf) error
	ModifyGatewayDomain(ctx context.CsmContext, domainConf *meta.DomainConf) error

	ModifyGatewayBlsTask(ctx context.CsmContext, logConf *meta.LogConf) (*meta.Log, error)
	ModifyGatewayHPA(ctx context.CsmContext, hpaConf *meta.HpaConf) (*meta.HPA, error)
	ModifyGatewayMonitor(ctx context.CsmContext, instanceUUID, gatewayID string, monitorConf *meta.Monitor) (monitor *meta.Monitor, err error)
	ModifyGatewayTLSAcceleration(ctx context.CsmContext, tlsAccConf *meta.TLSAccConf) (*meta.TLSAcc, error)
	ModifyGatewayResourceQuota(ctx context.CsmContext, resourceQuotaConf *meta.ResourceQuotaConf) (*meta.ResourceQuota, error)
	ModifyGatewayIngress(ctx context.CsmContext, instanceUUID, gatewayID string, ingressParam *meta.IngressParam) error
	GetGatewayIngress(ctx context.CsmContext, instanceUUID, gatewayID string) (ingressParam *meta.IngressParam, err error)
}
