package gateway

import (
	"context"
	"errors"
	"fmt"
	"math"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/baidubce/bce-sdk-go/services/appblb"
	"istio.io/api/networking/v1beta1"
	netv1beta1 "istio.io/client-go/pkg/apis/networking/v1beta1"
	v1 "k8s.io/api/core/v1"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blb"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/bls"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cert"
	clusterModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/gateway"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	monitorModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	gatewayDeployer "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/deploy/gateway"
	instanceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/monitor"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil/rollback"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/uuid"
)

type Service struct {
	opt             *Option
	cceService      cce.ClientInterface
	gatewayModel    gateway.ServiceInterface
	instancesModel  instances.ServiceInterface
	instanceService instanceService.ServiceInterface
	deployerService gatewayDeployer.ServiceInterface
	blbModel        blb.ServiceInterface
	certModel       cert.ServiceInterface
	blsModel        bls.ServiceInterface
	monitorService  monitor.ServiceInterface
	// TODO 后续重新规划service层和model层的方法
	monitorModel monitorModel.ServiceInterface
	clusterModel clusterModel.ServiceInterface
}

func NewGatewayService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:             option,
		cceService:      cce.NewClientService(),
		gatewayModel:    gateway.NewGatewayService(gateway.NewOption(gormDB)),
		instancesModel:  instances.NewInstancesService(instances.NewOption(gormDB)),
		instanceService: instanceService.NewInstanceService(instanceService.NewOption(gormDB)),
		deployerService: gatewayDeployer.NewDeployerService(),
		blbModel:        blb.NewService(blb.NewOption()),
		certModel:       cert.NewCertService(cert.NewOption(gormDB)),
		blsModel:        bls.NewService(bls.NewOption()),
		monitorService:  monitor.NewMonitorService(monitor.NewOption(gormDB)),
		monitorModel:    monitorModel.NewService(monitorModel.NewOption()),
		clusterModel:    clusterModel.NewClusterService(clusterModel.NewOption(gormDB)),
	}
}

func (s *Service) resetBlb(ctx csmContext.CsmContext, blbID, region string) error {
	// 删除监听器
	for _, listenerPort := range GwListenerPortList {
		createDeleteAppListenersArgs := &appblb.DeleteAppListenersArgs{
			ClientToken: util.GetClientToken(),
			PortList:    []uint16{listenerPort},
		}
		err := s.blbModel.DeleteAllAppListeners(ctx, blbID, region, createDeleteAppListenersArgs)
		ctx.CsmLogger().Errorf("fail to delete AppListeners on port %d: %v", listenerPort, err)
	}

	// 删除目标IP组
	ipGroups, err := s.blbModel.GetAllAppIpGroups(ctx, blbID, region)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get all AppIPGroups: %v", err)
		return err
	}
	for _, ipGroup := range ipGroups.AppIpGroupList {
		createDeleteAppIpGroupArgs := &appblb.DeleteAppIpGroupArgs{
			IpGroupId:   ipGroup.Id,
			ClientToken: util.GetClientToken(),
		}
		err = s.blbModel.DeleteAppIpGroup(ctx, blbID, region, createDeleteAppIpGroupArgs)
		if err != nil {
			ctx.CsmLogger().Errorf("fail to delete all AppIPGroups: %v", err)
			return err
		}
	}
	return nil
}

// NewGateway 创建托管网关
func (s *Service) NewGateway(ctx csmContext.CsmContext, gateway *meta.GatewayModel, hpaConf *meta.HpaConf,
	blbConf *meta.BlbConf, logConf *meta.LogConf, tlsAccConf *meta.TLSAccConf) (err error) {
	if gateway == nil {
		ctx.CsmLogger().Errorf("invalid nil gateway error %v", err)
		return err
	}

	// 获取部署网关所需网格实例和Istiod集群的一些额外信息
	instanceInfo, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, gateway.InstanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get mesh instance info %v", err)
		return err
	}
	gateway.Namespace = instanceInfo.IstioInstallNamespace
	gateway.IstioVersion = instanceInfo.IstioVersion

	clusterInfo, _, err := s.instancesModel.GetInstanceIstiodCluster(ctx, gateway.InstanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get istiod cluster info %v", err)
		return err
	}
	gateway.ClusterUUID = clusterInfo.ClusterUUID
	gateway.ClusterName = clusterInfo.ClusterName

	// 开启CProm监控
	if gateway.MonitorEnabled != nil && *gateway.MonitorEnabled {
		monitorInstance := &meta.CPromInstance{
			InstanceId: gateway.MonitorInstanceID,
			Region:     gateway.MonitorRegion,
		}
		cpromGatewayInfo, updateErr := s.monitorService.CreateGatewayMonitor(ctx, gateway.InstanceUUID, gateway.VpcNetworkId, monitorInstance)
		if updateErr != nil {
			return updateErr
		}
		gateway.MonitorJobID = cpromGatewayInfo.ScrapeJobID
		gateway.MonitorAgentID = cpromGatewayInfo.AgentID
	}

	// 添加到数据库
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	// 校验是否存在已创建的网关实例，一个网格实例仅允许创建一个网关
	mrp := &meta.CsmMeshRequestParams{
		InstanceUUID: instanceInfo.InstanceUUID,
		Region:       instanceInfo.Region,
		AccountID:    instanceInfo.AccountId,
	}
	gwList, err := s.gatewayModel.GetGatewayList(ctx, mrp)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to check whether a gateway is already exist %v", err)
		return err
	} else if len(*gwList) > 0 {
		ctx.CsmLogger().Warn("fail to create gateway because a gateway instance is already exist")
		tx.Commit()
		return nil
	}
	gatewayService := s.gatewayModel.WithTx(dbutil.NewDB(tx))
	if err = gatewayService.NewGateway(ctx, gateway); err != nil {
		return err
	}
	tx.Commit()

	// 安装托管网关
	go s.NewGatewayImpl(ctx, gateway, hpaConf, blbConf, logConf, tlsAccConf)

	return nil
}

func (s *Service) NewGatewayImpl(ctx csmContext.CsmContext, gateway *meta.GatewayModel, hpaConf *meta.HpaConf,
	blbConf *meta.BlbConf, logConf *meta.LogConf, tlsAccConf *meta.TLSAccConf) {
	go func() {
		// 重置BLB
		blbID := blbConf.BlbID
		region := gateway.Region
		if err := s.resetBlb(ctx, blbID, region); err != nil {
			ctx.CsmLogger().Errorf("fail to reset blb %v", err)
			return
		}

		// 预创建监听器
		for _, port := range GwListenerPortList {
			createAppTCPListenerArgs := &appblb.CreateAppTCPListenerArgs{
				ListenerPort:      port,
				Scheduler:         "RoundRobin",
				TcpSessionTimeout: 900,
			}
			err := s.blbModel.CreateAppTCPListener(ctx, blbID, region, createAppTCPListenerArgs)
			if err != nil {
				ctx.CsmLogger().Errorf("fail to create blb listener: %v", err)
				return
			}
		}
	}()

	// 创建托管网关资源
	err := s.deployerService.Init(ctx, gateway.Region, gateway.ClusterUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to initialize gateway deployer: %v", err)
		return
	}
	err = s.deployerService.InstallCCEIngressGateway(ctx, gateway, blbConf, tlsAccConf)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to install gateway: %v", err)
		return
	}

	// 部署HPA
	if hpaConf.Enabled {
		hpaConf.Namespace = gateway.Namespace
		hpaErr := s.deployerService.DeployHPA(ctx, hpaConf)
		if hpaErr != nil {
			ctx.CsmLogger().Errorf("fail to deploy HPA: %v", hpaErr)
			return
		}
	}

	// 部署Gateway和VirtualService
	err = s.deployerService.DeployGwVs(ctx, gateway.GatewayUUID, gateway.Namespace)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to deploy gw-vs crd: %v", err)
		return
	}

	// 创建特权BLS日志传输任务
	if logConf.Enabled {
		logConf.Namespace = gateway.Namespace
		taskID, logErr := s.blsModel.CreateHostingBlsTask(ctx, logConf, gateway.Region)
		if logErr != nil {
			ctx.CsmLogger().Errorf("fail to create hosting bls task: %v", logErr)
			return
		}
		logErr = s.deployerService.DeployTelemetry(ctx, taskID, gateway.GatewayUUID, gateway.Namespace)
		if logErr != nil {
			ctx.CsmLogger().Errorf("fail to deploy gateway telemetry crd: %v", logErr)
			return
		}
	}
}

// GenerateGatewayID
// TODO: 做成工具方法放在utils里面
func (s *Service) GenerateGatewayID(_ csmContext.CsmContext) (string, error) {
	var result string
	for i := 0; i < 8; i++ {
		result = uuid.GetGatewayUUID()
		var gateway meta.GatewayModel
		notFound := s.opt.DB.Where("gateway_uuid = ?", result).Find(&gateway).RecordNotFound()
		if notFound {
			return result, nil
		}
	}
	return "", fmt.Errorf("duplicate gateway id in database")
}

func (s *Service) getBlbIdFromGatewaySvc(ctx csmContext.CsmContext, gatewaySvc *v1.Service) (string, string) {
	var blbID string
	annotations := gatewaySvc.ObjectMeta.Annotations
	if annotations != nil {
		blbID = annotations[constants.CceLoadBalancerID]
	}
	region := ctx.Get(reg.ContextRegion).(string)
	return blbID, region
}

func (s *Service) DeleteGateway(ctx csmContext.CsmContext, instanceUUID, gatewayUUID string) (err error) {
	gatewayInfo, err := s.gatewayModel.GetGatewayInfo(ctx, instanceUUID, gatewayUUID)
	if err != nil {
		return err
	}
	// 删除监控
	if gatewayInfo.MonitorEnabled != nil && *gatewayInfo.MonitorEnabled {
		deleteErr := s.monitorModel.DeleteScrapeJob(ctx, gatewayInfo.MonitorRegion, gatewayInfo.MonitorJobID,
			gatewayInfo.MonitorInstanceID, gatewayInfo.MonitorAgentID)
		if deleteErr != nil {
			return deleteErr
		}
	}
	// 删除higress
	if gatewayInfo.RemoteIngressEnabled != nil && *gatewayInfo.RemoteIngressEnabled {
		ingressParam := meta.IngressParam{Enabled: false}
		err = s.deployerService.UnDeployIngressController(ctx, gatewayInfo, &ingressParam)
		if err != nil {
			return err
		}
	}

	// 从数据库中删除
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	gatewayService := s.gatewayModel.WithTx(dbutil.NewDB(tx))
	if err = gatewayService.DeleteGateway(ctx, gatewayInfo.InstanceUUID, gatewayInfo.GatewayUUID); err != nil {
		ctx.CsmLogger().Errorf("fail to remove gateway from database: %v", err)
		return err
	}
	tx.Commit()

	// 移除Service中的Finalizer
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer cancel()

	client, lbErr := s.cceService.NewClient(ctx, gatewayInfo.Region, gatewayInfo.ClusterUUID, meta.HostingMeshType)
	if lbErr != nil {
		ctx.CsmLogger().Errorf("fail to get NewClient when deleting gateway: %v", lbErr)
		return lbErr
	}
	gatewaySvc, lbErr := client.Kube().CoreV1().Services(gatewayInfo.Namespace).Get(cxt,
		constants.IngressGatewayResourceName, metav1.GetOptions{})
	if lbErr != nil || gatewaySvc == nil {
		ctx.CsmLogger().Errorf("fail to get gateway service: %v", lbErr)
		return lbErr
	}
	gatewaySvc.Finalizers = []string{}
	newGatewaySvc, lbErr := client.Kube().CoreV1().Services(gatewayInfo.Namespace).Update(cxt, gatewaySvc, metav1.UpdateOptions{})
	if lbErr != nil {
		ctx.CsmLogger().Errorf("fail to remove finalizer: %v", lbErr)
		return lbErr
	}
	if newGatewaySvc == nil {
		lbErr = errors.New("get an unexpected nil newGatewaySvc")
		return lbErr
	}

	go func() {
		// 重置BLB
		blbID, region := s.getBlbIdFromGatewaySvc(ctx, newGatewaySvc)
		if lbErr = s.resetBlb(ctx, blbID, region); lbErr != nil {
			ctx.CsmLogger().Warn("fail to reset blb when deleting gateway, you may need to reset it by yourself")
		}
	}()

	// 卸载托管网关
	go s.DeleteGatewayImpl(ctx, gatewayInfo)

	return nil
}

func (s *Service) DeleteGatewayImpl(ctx csmContext.CsmContext, gatewayInfo *meta.GatewayModel) {
	err := s.deployerService.Init(ctx, gatewayInfo.Region, gatewayInfo.ClusterUUID)
	if err != nil {
		ctx.CsmLogger().Error("fail to initialize gateway")
		return
	}
	err = s.deployerService.UninstallCCEIngressGateway(ctx, gatewayInfo)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to uninstall gateway: %v", err)
		return
	}

	tmpHpaConf := &meta.HpaConf{
		MinReplicas: 1,
		MaxReplicas: 2,
		Namespace:   gatewayInfo.Namespace,
		GatewayUUID: gatewayInfo.GatewayUUID,
	}
	err = s.deployerService.UndeployHPA(ctx, tmpHpaConf)
	if err != nil {
		ctx.CsmLogger().Error("fail to delete gateway hpa resource")
		return
	}

	// 删除Gateway和VirtualService
	err = s.deployerService.UndeployGwVs(ctx, gatewayInfo.GatewayUUID, gatewayInfo.Namespace)
	if err != nil {
		ctx.CsmLogger().Error("fail to delete gw-vs crd")
		return
	}

	// 删除Telemetry
	taskID, err := s.deployerService.UndeployTelemetry(ctx, gatewayInfo.GatewayUUID, gatewayInfo.Namespace)
	if err != nil {
		ctx.CsmLogger().Error("fail to delete telemetry crd")
		return
	}
	err = s.blsModel.DeleteHostingBlsTask(ctx, taskID, gatewayInfo.Region)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to delete bls task: %s", taskID)
		return
	}
}

// GetGatewayList 获取网关列表
func (s *Service) GetGatewayList(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.GatewayListResponse, error) {
	gatewayList, err := s.gatewayModel.GetGatewayList(ctx, mrp)
	if err != nil {
		return nil, err
	}

	// TODO: 考虑并发
	var tmpResult []meta.GatewayDisplay
	for _, gw := range *gatewayList {
		gwd := meta.GatewayDisplay{
			CreateTime:    *gw.CreateTime,
			GatewayName:   gw.GatewayName,
			GatewayId:     gw.GatewayUUID,
			Status:        "",
			BillingModel:  constants.BillingModel,
			DeployMode:    gw.DeployMode,
			ResourceQuota: gw.ResourceQuota,
			Replicas:      0,
		}
		gwd.Status, gwd.Replicas, _, _, _, _, err = s.getGatewayBasicConfig(ctx, &gw)
		if err != nil {
			ctx.CsmLogger().Warn("fail to get ingress gateway's status")
		}
		tmpResult = append(tmpResult, gwd)
	}

	response := &meta.GatewayListResponse{
		PageNo:     mrp.PageNo,
		PageSize:   mrp.PageSize,
		TotalCount: int64(len(tmpResult)),
		Order:      mrp.Order,
		OrderBy:    mrp.OrderBy,
		Result:     tmpResult,
	}

	return response, nil
}

func (s *Service) GetGatewayDetail(ctx csmContext.CsmContext, instanceUUID, gatewayUUID string) (*meta.GatewayDetailResponse, error) {
	gatewayInfo, err := s.gatewayModel.GetGatewayInfo(ctx, instanceUUID, gatewayUUID)
	if err != nil {
		return nil, err
	}

	vpcAndSubnetValue, vpcAndSubnetErr := s.instanceService.GetVpcAndSubnetInfo(ctx, gatewayInfo.VpcNetworkId,
		gatewayInfo.SubnetId, gatewayInfo.Region)
	if vpcAndSubnetErr != nil {
		ctx.CsmLogger().Errorf("get vpc and subnet error %v", vpcAndSubnetErr)
	}

	status, replicas, hpa, log, monitor, tlsAcc, err := s.getGatewayBasicConfig(ctx, gatewayInfo)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get ingress gateway's basic config", err)
		return nil, err
	}

	response := &meta.GatewayDetailResponse{
		BasicConfig: &meta.BasicConfig{
			GatewayName:   gatewayInfo.GatewayName,
			GatewayUUID:   gatewayInfo.GatewayUUID,
			Status:        status,
			BillingModel:  constants.BillingModel,
			DeployMode:    gatewayInfo.DeployMode,
			GatewayType:   gatewayInfo.GatewayType,
			ResourceQuota: gatewayInfo.ResourceQuota,
			Replicas:      replicas,
			HPA:           hpa,
			Log:           log,
			Monitor:       monitor,
			TLSAcc:        tlsAcc,
		},
		NetworkConfig: &meta.NetworkConfig{
			NetworkType: &meta.NetworkType{
				VpcNetworkId:   gatewayInfo.VpcNetworkId,
				VpcNetworkName: vpcAndSubnetValue.VpcNetworkName,
				VpcNetworkCidr: vpcAndSubnetValue.VpcNetworkCidr,
				SubnetId:       gatewayInfo.SubnetId,
				SubnetName:     vpcAndSubnetValue.SubnetName,
				SubnetCidr:     vpcAndSubnetValue.SubnetCidr,
			},
		},
	}

	return response, nil
}

func (s *Service) GetGatewayBlbList(ctx csmContext.CsmContext, instanceUUID string) (*meta.GatewayBlbListResponse, error) {
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer cancel()

	istiodCluster, meshType, err := s.instancesModel.GetInstanceIstiodCluster(ctx, instanceUUID)
	if err != nil {
		return nil, err
	}
	client, err := s.cceService.NewClient(ctx, istiodCluster.Region, istiodCluster.ClusterUUID, meshType)
	if err != nil {
		return nil, err
	}

	gatewaySvc, err := client.Kube().CoreV1().Services(istiodCluster.IstioInstallNamespace).Get(cxt,
		constants.IngressGatewayResourceName, metav1.GetOptions{})
	if err != nil || gatewaySvc == nil {
		return nil, err
	}

	blbID, region := s.getBlbIdFromGatewaySvc(ctx, gatewaySvc)
	blbInfo, err := s.blbModel.GetBlb(ctx, blbID, region)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get gateway blb info, %v", err)
		return nil, err
	}

	response := &meta.GatewayBlbListResponse{
		TotalCount: 1,
		Result: []meta.BlbDescribe{
			{
				ID:           blbInfo.BlbId,
				Name:         blbInfo.Name,
				PublicIP:     blbInfo.PublicIp,
				InternalIP:   blbInfo.Address,
				Status:       string(blbInfo.Status),
				CreateTime:   blbInfo.CreateTime,
				ListenerList: blbInfo.Listener,
			},
		},
	}
	return response, nil
}

func (s *Service) getGatewayBasicConfig(ctx csmContext.CsmContext, gatewayInfo *meta.GatewayModel) (string, int16,
	*meta.HPA, *meta.Log, *meta.Monitor, *meta.TLSAcc, error) {
	if gatewayInfo == nil {
		return "", -1, nil, nil, nil, nil, csmErr.NewInvalidParameterValueException("gatewayInfo is nil")
	}
	instanceUUID := gatewayInfo.InstanceUUID

	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer cancel()

	gatewayStatus := constants.SmiAbnormal
	replicas := int16(0)
	resHpa := &meta.HPA{
		Enabled: false,
	}
	resLog := &meta.Log{
		Enabled: false,
	}
	resMonitor := &meta.Monitor{
		Enabled: false,
	}
	resTLSAcc := &meta.TLSAcc{
		Enabled: false,
	}

	istiodCluster, meshType, err := s.instancesModel.GetInstanceIstiodCluster(ctx, instanceUUID)
	if err != nil {
		return gatewayStatus, replicas, resHpa, resLog, resMonitor, resTLSAcc, err
	}
	istiodNamespace := istiodCluster.IstioInstallNamespace
	client, err := s.cceService.NewClient(ctx, istiodCluster.Region, istiodCluster.ClusterUUID, meshType)
	if err != nil {
		return gatewayStatus, replicas, resHpa, resLog, resMonitor, resTLSAcc, err
	}
	gatewayPods, err := client.Kube().CoreV1().Pods(istiodNamespace).List(
		cxt, metav1.ListOptions{LabelSelector: constants.IngressGatewayLabelSelector})
	if err != nil {
		return gatewayStatus, replicas, resHpa, resLog, resMonitor, resTLSAcc, err
	}

	gatewayStatus = constants.SmiRunning
	if len(gatewayPods.Items) == 0 {
		gatewayStatus = constants.SmiDeploying
	} else {
		gatewayStatus = constants.SmiRunning
		for _, pod := range gatewayPods.Items {
			status := pod.Status.Phase
			if status == v1.PodRunning {
				continue
			}
			if status == v1.PodPending {
				gatewayStatus = constants.SmiDeploying
			} else {
				reason := strings.ToLower(pod.Status.Reason)
				if reason == constants.SmiEvicted {
					continue
				}
				gatewayStatus = constants.SmiAbnormal
			}
			break
		}
	}
	// 获取网关副本数
	for _, pod := range gatewayPods.Items {
		reason := strings.ToLower(pod.Status.Reason)
		if reason == constants.SmiEvicted {
			continue
		}
		replicas++
	}
	// 获取网关HPA信息
	hpa, err := client.Kube().AutoscalingV1().HorizontalPodAutoscalers(istiodNamespace).Get(
		cxt, constants.IngressGatewayResourceName, metav1.GetOptions{})
	if err == nil {
		resHpa.Enabled = true
		resHpa.MaxReplicas = hpa.Spec.MaxReplicas
		if hpa.Spec.MinReplicas != nil {
			resHpa.MinReplicas = *hpa.Spec.MinReplicas
		} else {
			resHpa.MinReplicas = 1
		}
	}

	if gatewayInfo.MonitorEnabled != nil && *gatewayInfo.MonitorEnabled {
		cpromInstance, getErr := s.monitorModel.GetCPromInstanceById(ctx, gatewayInfo.MonitorRegion, gatewayInfo.MonitorInstanceID)
		if getErr != nil {
			ctx.CsmLogger().Errorf("fail to get gateway cProm instance by id: %v", err)
		} else {
			monitorInstances := []meta.MonitorInstance{{
				Region: gatewayInfo.MonitorRegion,
				ID:     gatewayInfo.MonitorInstanceID,
				Name:   cpromInstance.InstanceName,
			}}
			resMonitor.Enabled = true
			resMonitor.Instances = monitorInstances
		}
	}

	// 获取TLS加速开启信息
	gatewayDeploy, err := client.Kube().AppsV1().Deployments(istiodNamespace).Get(context.TODO(),
		constants.IngressGatewayResourceName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get gateway deployment: %v", err)
	} else {
		if _, ok := gatewayDeploy.Spec.Template.ObjectMeta.Annotations[constants.TLSAccelerationAnnotationKey]; ok {
			resTLSAcc.Enabled = true
		}
	}

	// 获取日志开启信息
	telemetry, err := client.Istio().TelemetryV1alpha1().Telemetries(istiodNamespace).Get(
		cxt, constants.GwTelemetryCrdName, metav1.GetOptions{})
	if err != nil {
		// 忽略此error，返回关闭状态
		if !kubeErrors.IsNotFound(err) {
			ctx.CsmLogger().Errorf("fail to get gateway telemetry crd: %v", err)
		}
		return gatewayStatus, replicas, resHpa, resLog, resMonitor, resTLSAcc, nil
	}
	taskID := telemetry.ObjectMeta.Labels[constants.GwTelemetryLabelKey]
	res, logErr := s.blsModel.GetHostingBlsTaskDetail(ctx, taskID, istiodCluster.Region)
	if logErr != nil {
		// 忽略此error，返回关闭状态
		ctx.CsmLogger().Errorf("fail to get bls task's detail of %s: %v", taskID, err)
		return gatewayStatus, replicas, resHpa, resLog, resMonitor, resTLSAcc, nil
	}
	resLog = res

	return gatewayStatus, replicas, resHpa, resLog, resMonitor, resTLSAcc, nil
}

func (s *Service) ExistGatewayWithInstanceUUID(ctx csmContext.CsmContext, instanceUUID string) (bool, error) {
	instanceInfo, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return false, err
	}
	mrp := meta.NewCsmMeshRequestParams()
	mrp.InstanceUUID = instanceUUID
	mrp.Region = instanceInfo.Region
	mrp.AccountID = instanceInfo.AccountId

	gatewayList, err := s.gatewayModel.GetGatewayList(ctx, mrp)
	if err != nil {
		ctx.CsmLogger().Errorf("get gateway error %v", err)
		return false, err
	}
	gateways := len(*gatewayList)
	ctx.CsmLogger().Infof("the number of gateways is %v", gateways)
	return gateways > 0, nil
}

// pageSlice 按照分页信息返回一个实例列表切片 TODO: move it to utils
func pageSlice(domainList []meta.DomainDescribe, mrp *meta.CsmMeshRequestParams) ([]meta.DomainDescribe, int64) {
	// filter with gateway port protocol
	if mrp.Protocol != "" {
		l := 0
		for _, dm := range domainList {
			if dm.Port.Protocol == mrp.Protocol {
				domainList[l] = dm
				l++
			}
		}
		domainList = domainList[:l]
	}

	// order the instList by its updateTime first with desc
	sort.Slice(domainList, func(i, j int) bool {
		return domainList[i].UpdateTime.Unix() > domainList[j].UpdateTime.Unix()
	})
	if strings.ToUpper(mrp.Order) == constants.DBOrderASC {
		// reverse the instList
		for i, j := 0, len(domainList)-1; i < j; i, j = i+1, j-1 {
			domainList[i], domainList[j] = domainList[j], domainList[i]
		}
	}

	totalCount := int64(len(domainList))
	realPageSize := int64(math.Max(float64(mrp.PageSize), float64(constants.MinPageSize)))

	maxPageNo := (totalCount + realPageSize - 1) / realPageSize
	realPageNo := int64(math.Min(float64(mrp.PageNo), math.Max(float64(maxPageNo), float64(constants.MinPageNo))))

	startItem := (realPageNo - 1) * realPageSize

	return domainList[startItem:int64(math.Min(float64(startItem+realPageSize), float64(totalCount)))], totalCount
}

func splitPortNameAndUpdateTime(gwSvrName string) (string, time.Time) {
	idx := strings.LastIndex(gwSvrName, "_")
	if idx == -1 {
		return gwSvrName, time.Now()
	}
	ut, utErr := strconv.Atoi(gwSvrName[idx+1:])
	if utErr != nil {
		return gwSvrName, time.Now()
	}
	return gwSvrName[:idx], time.Unix(int64(ut), 0)
}

func (s *Service) getTargetGatewayCrd(ctx csmContext.CsmContext, instanceUUID string) (kube.Client, *netv1beta1.Gateway, string, error) {
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer cancel()

	istiodCluster, meshType, err := s.instancesModel.GetInstanceIstiodCluster(ctx, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get istiodCluster: v%", err)
		return nil, nil, "", err
	}
	client, err := s.cceService.NewClient(ctx, istiodCluster.Region, istiodCluster.ClusterUUID, meshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get NewClient: v%", err)
		return nil, nil, "", err
	}
	targetGatewayCrd, err := client.Istio().NetworkingV1beta1().Gateways(istiodCluster.IstioInstallNamespace).Get(
		cxt, constants.GwCrdName, metav1.GetOptions{})
	if err != nil {
		return nil, nil, "", err
	}
	return client, targetGatewayCrd, istiodCluster.IstioInstallNamespace, nil
}

func realPortNumber(portNum int) int {
	switch portNum {
	case 80:
		return 8080
	case 443:
		return 8443
	case 8080:
		return 80
	case 8443:
		return 443
	}
	return 0
}

func (s *Service) GetGatewayDomainList(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.GatewayDomainListResponse, error) {
	_, targetGatewayCrd, _, err := s.getTargetGatewayCrd(ctx, mrp.InstanceUUID)
	if err != nil {
		return nil, err
	}

	var tmpResult []meta.DomainDescribe
	for _, srv := range targetGatewayCrd.Spec.Servers {
		portName, updateTime := splitPortNameAndUpdateTime(srv.Port.Name)
		domain := meta.DomainDescribe{
			Port: meta.DomainPort{
				Number:   int32(realPortNumber(int(srv.Port.Number))),
				Name:     portName,
				Protocol: srv.Port.Protocol,
			},
			Domains:    srv.Hosts,
			UpdateTime: updateTime,
		}
		if srv.Tls != nil {
			switch srv.Port.Protocol {
			case constants.ProtocolHTTP:
				domain.IsForceHTTPS = srv.Tls.HttpsRedirect
			case constants.ProtocolHTTPS:
				// TODO: 增加证书名字长度校验
				domain.Cert = meta.DomainCert{
					ID:   srv.Tls.CredentialName[:CertIDLength],
					Name: srv.Tls.CredentialName[CertIDLength+1:],
				}
			}
		}
		tmpResult = append(tmpResult, domain)
	}

	response := &meta.GatewayDomainListResponse{
		PageNo:     mrp.PageNo,
		PageSize:   mrp.PageSize,
		TotalCount: 0,
		Order:      mrp.Order,
		OrderBy:    mrp.OrderBy,
		Result:     []meta.DomainDescribe{},
	}
	// pageSlice
	response.Result, response.TotalCount = pageSlice(tmpResult, mrp)

	return response, nil
}

func (s *Service) modifyBlbListenersAndIPGroups(ctx csmContext.CsmContext, client kube.Client, istiodNamespace string,
	gatewayServers []*v1beta1.Server) error {
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout*3)
	defer cancel()

	gatewayService, err := client.Kube().CoreV1().Services(istiodNamespace).Get(cxt, constants.IngressGatewayResourceName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get gateway service: %v", err)
		return err
	}
	blbID, region := s.getBlbIdFromGatewaySvc(ctx, gatewayService)

	remainServerPortsMap := map[uint32]int{constants.GwStatusPort: 1}
	for _, gwServer := range gatewayServers {
		remainServerPortsMap[uint32(realPortNumber(int(gwServer.Port.Number)))] = 1
	}

	targetDeletePorts := []int{}
	remainServicePortsMap := make(map[uint32]int32)

	var newServicePorts []v1.ServicePort
	for _, svcPort := range gatewayService.Spec.Ports {
		if _, ok := remainServerPortsMap[uint32(svcPort.Port)]; ok {
			newServicePorts = append(newServicePorts, svcPort)
			remainServicePortsMap[uint32(svcPort.Port)] = 1
		} else {
			targetDeletePorts = append(targetDeletePorts, int(svcPort.Port))
		}
	}

	// 添加新的BLB Listener
	for _, gwServer := range gatewayServers {
		if _, ok := remainServicePortsMap[uint32(realPortNumber(int(gwServer.Port.Number)))]; !ok {
			addPort := int32(realPortNumber(int(gwServer.Port.Number)))
			newServicePorts = append(newServicePorts, GwServicePorts[addPort])
			createAppTCPListenerArgs := &appblb.CreateAppTCPListenerArgs{
				ListenerPort:      uint16(addPort),
				Scheduler:         "RoundRobin",
				TcpSessionTimeout: 900,
			}
			err = s.blbModel.CreateAppTCPListener(ctx, blbID, region, createAppTCPListenerArgs)
			if err != nil {
				ctx.CsmLogger().Errorf("fail to add target blb app listener at: %d, : %v", addPort, err)
			}
		}
	}

	// 更新Gateway Service
	gatewayService.Spec.Ports = newServicePorts
	_, tmpErr := client.Kube().CoreV1().Services(istiodNamespace).Update(cxt, gatewayService, metav1.UpdateOptions{})
	if tmpErr != nil {
		ctx.CsmLogger().Errorf("fail to update gateway service ports: %v", tmpErr)
		return tmpErr
	}

	// 删除旧的BLB Listener和IP Group
	for _, port := range targetDeletePorts {
		// 删除对应rs
		ipGroups, lbErr := s.blbModel.GetAllAppIpGroups(ctx, blbID, region)
		if lbErr != nil {
			ctx.CsmLogger().Errorf("fail to get all ip groups: %v", lbErr)
			return lbErr
		}
		for _, ipGroup := range ipGroups.AppIpGroupList {
			if ipGroup.Name == "TCP-"+strconv.Itoa(port) {
				createDeleteAppListenersArgs := &appblb.DeleteAppListenersArgs{
					ClientToken: util.GetClientToken(),
					PortList:    []uint16{uint16(port)},
				}
				lbErr = s.blbModel.DeleteAllAppListeners(ctx, blbID, region, createDeleteAppListenersArgs)
				if lbErr != nil {
					ctx.CsmLogger().Errorf("fail to delete target blb app listener at: %d, : %v", port, lbErr)
				}

				createDeleteAppIpGroupArgs := &appblb.DeleteAppIpGroupArgs{
					IpGroupId:   ipGroup.Id,
					ClientToken: util.GetClientToken(),
				}
				lbErr = s.blbModel.DeleteAppIpGroup(ctx, blbID, region, createDeleteAppIpGroupArgs)
				if lbErr != nil {
					ctx.CsmLogger().Errorf("fail to delete blb ip group: %s, : %v", ipGroup.Id, lbErr)
					return lbErr
				}
			}
		}
	}

	return nil
}

func (s *Service) AddGatewayDomain(ctx csmContext.CsmContext, domainConf *meta.DomainConf) error {
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer cancel()

	client, targetGatewayCrd, istiodNamespace, err := s.getTargetGatewayCrd(ctx, domainConf.InstanceUUID)
	if err != nil {
		return err
	}

	for _, srv := range targetGatewayCrd.Spec.Servers {
		if reflect.DeepEqual(srv.Hosts, domainConf.Domains) && srv.Port.Protocol == domainConf.Port.Protocol {
			return csmErr.NewResourceConflictException("the host under the specific protocol is already exist", err)
		}
	}

	updateTimePostfix := "_" + strconv.FormatInt(time.Now().Unix(), 10)
	newServer := &v1beta1.Server{
		Hosts: domainConf.Domains,
		Port: &v1beta1.Port{
			Number:   uint32(realPortNumber(int(domainConf.Port.Number))),
			Name:     domainConf.Port.Name + updateTimePostfix,
			Protocol: domainConf.Port.Protocol,
		},
	}

	if domainConf.Port.Protocol == constants.ProtocolHTTPS {
		if domainConf.Cert == nil || domainConf.Cert.ID == "" || domainConf.Cert.Name == "" {
			tmpErr := errors.New("https needs a certification")
			return tmpErr
		}
		// 创建tls secret
		secretName, certErr := s.certModel.GenerateTlsSecret(ctx, client, istiodNamespace, domainConf.Cert)
		if certErr != nil {
			if kubeErrors.IsAlreadyExists(certErr) {
				return csmErr.NewResourceConflictException("the certificate is already in used", certErr)
			}
			ctx.CsmLogger().Errorf("fail to generate tls secret: %v", certErr)
			return certErr
		}
		newServer.Tls = &v1beta1.ServerTLSSettings{
			Mode:           v1beta1.ServerTLSSettings_SIMPLE,
			CredentialName: secretName,
		}
		if domainConf.IsForceHTTPS {
			isHTTPServerExist := false
			for idx, srv := range targetGatewayCrd.Spec.Servers {
				if reflect.DeepEqual(srv.Hosts, domainConf.Domains) && srv.Port.Protocol == constants.ProtocolHTTP {
					targetGatewayCrd.Spec.Servers[idx].Tls = &v1beta1.ServerTLSSettings{
						HttpsRedirect: true,
					}
					isHTTPServerExist = true
					break
				}
			}
			if !isHTTPServerExist {
				tmpHTTPServer := &v1beta1.Server{
					Hosts: domainConf.Domains,
					Port: &v1beta1.Port{
						Number:   uint32(realPortNumber(80)),
						Name:     domainConf.Port.Name + updateTimePostfix,
						Protocol: constants.ProtocolHTTP,
					},
					Tls: &v1beta1.ServerTLSSettings{
						HttpsRedirect: true,
					},
				}
				targetGatewayCrd.Spec.Servers = append(targetGatewayCrd.Spec.Servers, tmpHTTPServer)
			}
		}
	}

	targetGatewayCrd.Spec.Servers = append(targetGatewayCrd.Spec.Servers, newServer)
	_, err = client.Istio().NetworkingV1beta1().Gateways(istiodNamespace).Update(cxt, targetGatewayCrd, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to add a new server: %v", err)
		return err
	}

	// 匹配BLB后端Rs
	err = s.modifyBlbListenersAndIPGroups(ctx, client, istiodNamespace, targetGatewayCrd.Spec.Servers)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to modify blb's backend rs: %v", err)
		return err
	}

	return nil
}

func (s *Service) DeleteGatewayDomain(ctx csmContext.CsmContext, domainConf *meta.DomainConf) error {
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer cancel()

	client, targetGatewayCrd, istiodNamespace, err := s.getTargetGatewayCrd(ctx, domainConf.InstanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get targetGatewayCrd: %v", err)
		return err
	}

	targetProtocol := func() string {
		if domainConf.Cert != nil && domainConf.Cert.ID != "" && domainConf.Cert.Name != "" {
			return constants.ProtocolHTTPS
		}
		return constants.ProtocolHTTP
	}

	newServers := []*v1beta1.Server{}
	for _, srv := range targetGatewayCrd.Spec.Servers {
		if reflect.DeepEqual(srv.Hosts, domainConf.Domains) && srv.Port.Protocol == targetProtocol() {
			continue
		}
		newServers = append(newServers, srv)
	}
	targetGatewayCrd.Spec.Servers = newServers
	_, err = client.Istio().NetworkingV1beta1().Gateways(istiodNamespace).Update(cxt, targetGatewayCrd, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to delete a new host: %v", err)
		return err
	}

	// 删除相关证书
	if targetProtocol() == constants.ProtocolHTTPS {
		secretName := domainConf.Cert.ID + "-" + domainConf.Cert.Name
		err = client.Kube().CoreV1().Secrets(istiodNamespace).Delete(cxt, secretName, metav1.DeleteOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("fail to delete %s' tls secret: %v", secretName, err)
		}
	}

	// 匹配BLB后端Rs
	err = s.modifyBlbListenersAndIPGroups(ctx, client, istiodNamespace, newServers)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to modify blb's backend rs: %v", err)
	}

	return nil
}

func (s *Service) ModifyGatewayDomain(ctx csmContext.CsmContext, domainConf *meta.DomainConf) error {
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer cancel()

	client, targetGatewayCrd, istiodNamespace, err := s.getTargetGatewayCrd(ctx, domainConf.InstanceUUID)
	if err != nil {
		return err
	}

	preProtocol := func() string {
		if domainConf.Port.PreNumber == 443 {
			return constants.ProtocolHTTPS
		}
		return constants.ProtocolHTTP
	}

	updateTimePostfix := "_" + strconv.FormatInt(time.Now().Unix(), 10)
	isBlbChanged := false
	isServerNeedDelete := false

	var preServer *v1beta1.Server
	var tarServer *v1beta1.Server
	preIdx, tarIdx := -1, -1
	for idx, srv := range targetGatewayCrd.Spec.Servers {
		if reflect.DeepEqual(srv.Hosts, domainConf.Domains) {
			if srv.Port.Protocol == preProtocol() {
				preServer = srv
				preIdx = idx
			} else {
				tarServer = srv
				tarIdx = idx
			}
		}
	}

	if preServer == nil {
		return csmErr.NewResourceNotFoundException("the host under the specific protocol is not found", err)
	}

	newServer := &v1beta1.Server{
		Hosts: domainConf.Domains,
		Port: &v1beta1.Port{
			Number:   uint32(realPortNumber(int(domainConf.Port.Number))),
			Name:     domainConf.Port.Name + updateTimePostfix,
			Protocol: domainConf.Port.Protocol,
		},
	}

	if preProtocol() == constants.ProtocolHTTPS {
		if domainConf.Port.Protocol == constants.ProtocolHTTP {
			// HTTPS to HTTP
			// 删除旧证书
			err = client.Kube().CoreV1().Secrets(istiodNamespace).Delete(cxt, preServer.Tls.CredentialName, metav1.DeleteOptions{})
			if err != nil {
				ctx.CsmLogger().Errorf("fail to delete %s' tls secret: %v", preServer.Tls.CredentialName, err)
				return err
			}
			if tarServer != nil {
				isServerNeedDelete = true
			}
			isBlbChanged = true
		} else {
			// HTTPS to HTTPS
			// 判断新旧证书是否相同
			preCert := &meta.DomainCert{}
			if len(preServer.Tls.CredentialName) > CertIDLength {
				preCert = &meta.DomainCert{
					ID:   preServer.Tls.CredentialName[:CertIDLength],
					Name: preServer.Tls.CredentialName[CertIDLength+1:],
				}
			}
			if !reflect.DeepEqual(domainConf.Cert, preCert) {
				// 创建新证书
				secretName, certErr := s.certModel.GenerateTlsSecret(ctx, client, istiodNamespace, domainConf.Cert)
				if certErr != nil {
					if kubeErrors.IsAlreadyExists(certErr) {
						return csmErr.NewResourceConflictException("the certificate is already in used", certErr)
					}
					ctx.CsmLogger().Errorf("fail to generate tls secret: %v", certErr)
					return certErr
				}
				newServer.Tls = &v1beta1.ServerTLSSettings{
					Mode:           v1beta1.ServerTLSSettings_SIMPLE,
					CredentialName: secretName,
				}
				// 删除旧证书
				err = client.Kube().CoreV1().Secrets(istiodNamespace).Delete(cxt, preServer.Tls.CredentialName, metav1.DeleteOptions{})
				if err != nil {
					ctx.CsmLogger().Errorf("fail to delete %s' tls secret: %v", preServer.Tls.CredentialName, err)
					return err
				}
			} else {
				newServer.Tls = preServer.Tls
			}

			if tarServer != nil {
				targetGatewayCrd.Spec.Servers[tarIdx].Tls = &v1beta1.ServerTLSSettings{
					HttpsRedirect: domainConf.IsForceHTTPS,
				}
			}

			if domainConf.IsForceHTTPS && tarServer == nil {
				tmpHTTPServer := &v1beta1.Server{
					Hosts: domainConf.Domains,
					Port: &v1beta1.Port{
						Number:   uint32(realPortNumber(80)),
						Name:     domainConf.Port.Name + updateTimePostfix,
						Protocol: constants.ProtocolHTTP,
					},
					Tls: &v1beta1.ServerTLSSettings{
						HttpsRedirect: true,
					},
				}
				targetGatewayCrd.Spec.Servers = append(targetGatewayCrd.Spec.Servers, tmpHTTPServer)
				isBlbChanged = true
			}
		}
	} else {
		if domainConf.Port.Protocol == constants.ProtocolHTTPS {
			// HTTP to HTTPS
			if tarServer != nil {
				if domainConf.IsForceHTTPS {
					newServer = preServer
					newServer.Tls = &v1beta1.ServerTLSSettings{
						HttpsRedirect: true,
					}
				} else {
					isBlbChanged = true
					isServerNeedDelete = true
				}
			} else {
				secretName, certErr := s.certModel.GenerateTlsSecret(ctx, client, istiodNamespace, domainConf.Cert)
				if certErr != nil {
					if kubeErrors.IsAlreadyExists(certErr) {
						return csmErr.NewResourceConflictException("the certificate is already in used", certErr)
					}
					ctx.CsmLogger().Errorf("fail to generate tls secret: %v", certErr)
					return certErr
				}
				newServer.Tls = &v1beta1.ServerTLSSettings{
					Mode:           v1beta1.ServerTLSSettings_SIMPLE,
					CredentialName: secretName,
				}

				if domainConf.IsForceHTTPS {
					preServer.Tls = &v1beta1.ServerTLSSettings{
						HttpsRedirect: true,
					}
					targetGatewayCrd.Spec.Servers = append(targetGatewayCrd.Spec.Servers, preServer)
				}
				isBlbChanged = true
			}
		} else {
			// HTTP to HTTP
			newServer.Tls = preServer.Tls
		}
	}
	if isServerNeedDelete {
		targetGatewayCrd.Spec.Servers[preIdx] = targetGatewayCrd.Spec.Servers[len(targetGatewayCrd.Spec.Servers)-1]
		targetGatewayCrd.Spec.Servers = targetGatewayCrd.Spec.Servers[:len(targetGatewayCrd.Spec.Servers)-1]
	} else {
		targetGatewayCrd.Spec.Servers[preIdx] = newServer
	}

	latestGatewayCrd, err := client.Istio().NetworkingV1beta1().Gateways(istiodNamespace).Update(cxt, targetGatewayCrd, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to modify the given host: %v", err)
		return err
	}

	if isBlbChanged {
		// 匹配BLB后端Rs
		// TODO: 优化返回错误信息或者使用协程
		err = s.modifyBlbListenersAndIPGroups(ctx, client, istiodNamespace, latestGatewayCrd.Spec.Servers)
		if err != nil {
			ctx.CsmLogger().Errorf("fail to modify blb's backend rs: %v", err)
			return err
		}
	}
	return nil
}

func (s *Service) ModifyGatewayBlsTask(ctx csmContext.CsmContext, logConf *meta.LogConf) (*meta.Log, error) {
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer cancel()

	istiodCluster, meshType, err := s.instancesModel.GetInstanceIstiodCluster(ctx, logConf.InstanceUUID)
	if err != nil {
		return nil, err
	}
	region := istiodCluster.Region
	istiodNamespace := istiodCluster.IstioInstallNamespace

	err = s.deployerService.Init(ctx, region, istiodCluster.ClusterUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to initialize gateway deployer: %v", err)
		return nil, err
	}
	client, err := s.cceService.NewClient(ctx, region, istiodCluster.ClusterUUID, meshType)
	if err != nil {
		return nil, err
	}

	// 获取日志开启信息
	logConf.Namespace = istiodNamespace
	telemetry, err := client.Istio().TelemetryV1alpha1().Telemetries(istiodNamespace).Get(
		cxt, constants.GwTelemetryCrdName, metav1.GetOptions{})
	if err != nil {
		if logConf.Enabled {
			// 新建传输任务
			taskID, logErr := s.blsModel.CreateHostingBlsTask(ctx, logConf, region)
			if logErr != nil {
				ctx.CsmLogger().Error("fail to create hosting bls task")
				return nil, logErr
			}
			logErr = s.deployerService.DeployTelemetry(ctx, taskID, gateway.GatewayUUID, istiodNamespace)
			if logErr != nil {
				ctx.CsmLogger().Error("fail to deploy gateway telemetry crd")
				return nil, logErr
			}
		}
	} else {
		taskID := telemetry.ObjectMeta.Labels[constants.GwTelemetryLabelKey]
		currentLogConf, logErr := s.blsModel.GetHostingBlsTaskDetail(ctx, taskID, region)
		if logErr != nil {
			// TODO: 创建新的传输任务？
			ctx.CsmLogger().Errorf("fail to get bls task's detail of %s: %v", taskID, err)
			return nil, logErr
		}
		if currentLogConf.Enabled {
			if !logConf.Enabled {
				// 暂停传输任务
				logErr = s.blsModel.PauseHostingBlsTask(ctx, taskID, region)
				if logErr != nil {
					ctx.CsmLogger().Error("fail to pause hosting bls task")
					return nil, logErr
				}
			} else if currentLogConf.LogFile != logConf.LogFile {
				// 修改日志集
				logErr = s.blsModel.UpdateHostingBlsTask(ctx, logConf, taskID, region)
				if logErr != nil {
					ctx.CsmLogger().Error("fail to update hosting bls task")
					return nil, logErr
				}
			}
		} else {
			if logConf.Enabled {
				// 启动传输任务
				logErr = s.blsModel.StartHostingBlsTask(ctx, taskID, region)
				if logErr != nil {
					ctx.CsmLogger().Error("fail to start hosting bls task")
					return nil, logErr
				}
				if currentLogConf.LogFile != logConf.LogFile {
					// 修改日志集
					logErr = s.blsModel.UpdateHostingBlsTask(ctx, logConf, taskID, region)
					if logErr != nil {
						ctx.CsmLogger().Error("fail to update hosting bls task")
						return nil, logErr
					}
				}
			}
		}
	}
	resLog := &meta.Log{
		Enabled: logConf.Enabled,
		Type:    logConf.Type,
		LogFile: logConf.LogFile,
	}
	return resLog, nil
}

func (s *Service) ModifyGatewayHPA(ctx csmContext.CsmContext, hpaConf *meta.HpaConf) (*meta.HPA, error) {
	istiodCluster, _, err := s.instancesModel.GetInstanceIstiodCluster(ctx, hpaConf.InstanceUUID)
	if err != nil {
		return nil, err
	}

	err = s.deployerService.Init(ctx, istiodCluster.Region, istiodCluster.ClusterUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to initialize gateway deployer: %v", err)
		return nil, err
	}

	if hpaConf.Enabled {
		hpaConf.Namespace = istiodCluster.IstioInstallNamespace
		hpaErr := s.deployerService.DeployHPA(ctx, hpaConf)
		if hpaErr != nil {
			ctx.CsmLogger().Errorf("fail to deploy gateway hpa: %v", hpaErr)
			return nil, hpaErr
		}
	} else {
		tmpHpaConf := &meta.HpaConf{
			MinReplicas: 1,
			MaxReplicas: 1,
			Namespace:   istiodCluster.IstioInstallNamespace,
			GatewayUUID: hpaConf.GatewayUUID,
		}
		hpaErr := s.deployerService.UndeployHPA(ctx, tmpHpaConf)
		if hpaErr != nil {
			ctx.CsmLogger().Errorf("fail to delete gateway hpa: %v", hpaErr)
			return nil, hpaErr
		}
	}
	resHpa := &meta.HPA{
		Enabled:     hpaConf.Enabled,
		MinReplicas: hpaConf.MinReplicas,
		MaxReplicas: hpaConf.MaxReplicas,
	}

	return resHpa, nil
}

func (s *Service) ModifyGatewayMonitor(ctx csmContext.CsmContext, instanceUUID, gatewayID string,
	monitorConf *meta.Monitor) (monitor *meta.Monitor, err error) {
	if len(instanceUUID) == 0 || len(gatewayID) == 0 {
		return nil, csmErr.NewInvalidParameterInputValueException("instanceUUID and gatewayID are empty")
	}

	if monitorConf == nil {
		return nil, csmErr.NewInvalidParameterInputValueException("monitorConf is nil")
	}

	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	gatewayModel := s.gatewayModel.WithTx(dbutil.NewDB(tx))
	gatewayInfo, gateErr := gatewayModel.GetGatewayInfo(ctx, instanceUUID, gatewayID)
	if gateErr != nil {
		return nil, gateErr
	}

	if !monitorConf.Enabled {
		if gatewayInfo.MonitorEnabled != nil && *gatewayInfo.MonitorEnabled {
			// 删除原有的采集任务
			deleteErr := s.monitorModel.DeleteScrapeJob(ctx, gatewayInfo.MonitorRegion, gatewayInfo.MonitorJobID,
				gatewayInfo.MonitorInstanceID, gatewayInfo.MonitorAgentID)
			// ignore not found error
			if deleteErr != nil && !isAcceptExceptionForCProm(deleteErr) {
				return nil, deleteErr
			}
		}

		// 删除service.annotation上的网关监控信息
		cpromGatewayInfo := &meta.CPromGatewayInfo{
			ID:          "",
			AgentID:     "",
			ScrapeJobID: "",
			Region:      "",
		}
		// 更新service.annotation
		updateAnnoErr := s.deployerService.UpdateMonitorInfoInAnnotation(ctx, gatewayInfo, cpromGatewayInfo)
		if updateAnnoErr != nil {
			return nil, updateAnnoErr
		}
		// 更新gateway
		newGateway := gatewayInfo
		newGateway.MonitorInstanceID = cpromGatewayInfo.ID
		newGateway.MonitorRegion = cpromGatewayInfo.Region
		newGateway.MonitorJobID = cpromGatewayInfo.ScrapeJobID
		newGateway.MonitorAgentID = cpromGatewayInfo.AgentID
		newGateway.MonitorEnabled = csm.Bool(false)

		updateErr := gatewayModel.UpdateGateway(ctx, gatewayInfo, newGateway)
		if updateErr != nil {
			return nil, updateErr
		}

		tx.Commit()

		return nil, nil
	}

	// monitorConf.Enabled为true
	if gatewayInfo.MonitorEnabled != nil && *gatewayInfo.MonitorEnabled {
		if gatewayInfo.MonitorInstanceID == monitorConf.Instances[0].ID {
			tx.Commit()
			return monitorConf, nil
		}
		// 删除旧的采集任务
		deleteErr := s.monitorModel.DeleteScrapeJob(ctx, gatewayInfo.MonitorRegion, gatewayInfo.MonitorJobID,
			gatewayInfo.MonitorInstanceID, gatewayInfo.MonitorAgentID)
		// ignore not found error
		if deleteErr != nil && !isAcceptExceptionForCProm(deleteErr) {
			return nil, deleteErr
		}
	}

	// 新的监控实例上创建采集任务
	monitorInstance := &meta.CPromInstance{
		InstanceId: monitorConf.Instances[0].ID,
		Region:     monitorConf.Instances[0].Region,
	}
	cpromGatewayInfo, createErr := s.monitorService.CreateGatewayMonitor(ctx, gatewayInfo.InstanceUUID,
		gatewayInfo.VpcNetworkId, monitorInstance)
	if createErr != nil {
		return nil, createErr
	}

	// 更新gateway
	newGateway := gatewayInfo
	newGateway.MonitorInstanceID = cpromGatewayInfo.ID
	newGateway.MonitorRegion = cpromGatewayInfo.Region
	newGateway.MonitorJobID = cpromGatewayInfo.ScrapeJobID
	newGateway.MonitorAgentID = cpromGatewayInfo.AgentID
	newGateway.MonitorEnabled = csm.Bool(true)

	updateErr := gatewayModel.UpdateGateway(ctx, gatewayInfo, newGateway)
	if updateErr != nil {
		return nil, updateErr
	}

	// 更新service.annotation
	updateAnnoErr := s.deployerService.UpdateMonitorInfoInAnnotation(ctx, gatewayInfo, cpromGatewayInfo)
	if updateAnnoErr != nil {
		return nil, updateAnnoErr
	}

	tx.Commit()

	return monitorConf, nil
}

func isAcceptExceptionForCProm(cpromErr error) bool {
	if strings.Contains(cpromErr.Error(), csmErr.CPromScrapeJobNotFoundException) ||
		strings.Contains(cpromErr.Error(), csmErr.CPromAgentNotFoundException) ||
		strings.Contains(cpromErr.Error(), csmErr.CPromInstanceNotFoundException) {
		return true
	}
	return false
}

func (s *Service) ModifyGatewayTLSAcceleration(ctx csmContext.CsmContext, tlsAccConf *meta.TLSAccConf) (*meta.TLSAcc, error) {
	istiodCluster, _, err := s.instancesModel.GetInstanceIstiodCluster(ctx, tlsAccConf.InstanceUUID)
	if err != nil {
		return nil, err
	}

	err = s.deployerService.Init(ctx, istiodCluster.Region, istiodCluster.ClusterUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to initialize gateway deployer: %v", err)
		return nil, err
	}

	if tlsAccConf.Enabled {
		tlsErr := s.deployerService.EnableTLSAcceleration(ctx, istiodCluster.IstioInstallNamespace)
		if tlsErr != nil {
			ctx.CsmLogger().Errorf("fail to enable gateway tls acceleration: %v", tlsErr)
			return nil, tlsErr
		}
		return &meta.TLSAcc{Enabled: true}, nil
	}
	err = s.deployerService.DisableTLSAcceleration(ctx, istiodCluster.IstioInstallNamespace)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to disable gateway tls acceleration: %v", err)
		return nil, err
	}
	return &meta.TLSAcc{Enabled: false}, nil
}

func (s *Service) ModifyGatewayResourceQuota(ctx csmContext.CsmContext, resourceQuotaConf *meta.ResourceQuotaConf) (*meta.ResourceQuota, error) {
	gatewayInfo, err := s.gatewayModel.GetGatewayInfo(ctx, resourceQuotaConf.InstanceUUID, resourceQuotaConf.GatewayUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get gateway info: %v", err)
		return nil, err
	}
	res := &meta.ResourceQuota{ResourceQuota: gatewayInfo.ResourceQuota}

	istiodCluster, _, err := s.instancesModel.GetInstanceIstiodCluster(ctx, resourceQuotaConf.InstanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get istiod cluster: %v", err)
		return res, err
	}

	err = s.deployerService.Init(ctx, istiodCluster.Region, istiodCluster.ClusterUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to initialize gateway deployer: %v", err)
		return res, err
	}
	err = s.deployerService.UpdateResourceQuota(ctx, resourceQuotaConf.ResourceQuota, istiodCluster.IstioInstallNamespace)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to update gateway's resource quota: %v", err)
		return res, err
	}

	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	gatewayModel := s.gatewayModel.WithTx(dbutil.NewDB(tx))
	newGateway := gatewayInfo
	newGateway.ResourceQuota = resourceQuotaConf.ResourceQuota

	err = gatewayModel.UpdateGateway(ctx, gatewayInfo, newGateway)
	if err != nil {
		return res, err
	}
	tx.Commit()

	res.ResourceQuota = resourceQuotaConf.ResourceQuota
	return res, nil
}

func (s *Service) ModifyGatewayIngress(ctx csmContext.CsmContext, instanceUUID, gatewayID string, ingressParam *meta.IngressParam) (err error) {
	if ingressParam == nil {
		return csmErr.NewMissingParametersException("ingressParam is nil")
	}

	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	gatewayModel := s.gatewayModel.WithTx(dbutil.NewDB(tx))
	clusterModel := s.clusterModel.WithTx(dbutil.NewDB(tx))

	gatewayInfo, err := gatewayModel.GetGatewayInfo(ctx, instanceUUID, gatewayID)
	if err != nil {
		return err
	}
	istiodCluster, istioType, istioErr := s.instancesModel.GetInstanceIstiodCluster(ctx, instanceUUID)
	if istioErr != nil {
		return istioErr
	}

	// 目前只支持托管架构
	if istioType != meta.HostingMeshType {
		return csmErr.NewInvalidParameterValueException("sync remote cluster's ingress only support hosting")
	}

	cl, clusterErr := clusterModel.GetAllRemoteClusterByInstanceUUID(ctx, instanceUUID)
	if clusterErr != nil {
		return clusterErr
	}

	// 创建higress相关k8s资源
	err = s.deployerService.Init(ctx, istiodCluster.Region, istiodCluster.ClusterUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to initialize higress controller deployer: %v", err)
		return err
	}
	// 兼容RemoteIngressEnabled为空情况，为空时表示关闭状态
	if gatewayInfo.RemoteIngressEnabled == nil {
		gatewayInfo.RemoteIngressEnabled = csm.Bool(false)
	}

	if ingressParam.Enabled {
		if !*gatewayInfo.RemoteIngressEnabled {
			// 统一开启remote集群的ingres权限，通过svc上clusterID标签来判断同步哪个集群的ingress，否则每次变更监听集群都需要重启higress
			err = s.deployerService.UpdateIngressAccess(ctx,
				constants.RemoteClusterRolePrefic+"-"+gatewayInfo.Namespace, true, *cl)
			if err != nil {
				return err
			}
			// 部署higress-controller
			deployErr := s.deployerService.DeployIngressController(ctx, gatewayInfo, ingressParam)
			if deployErr != nil {
				return deployErr
			}
		}
		// 更新svc上的注解，开启remote的ingress同步
		err = s.deployerService.UpdateIngressInfoInAnnotation(ctx, gatewayInfo.Namespace, ingressParam)
		if err != nil {
			return err
		}
	} else {
		if *gatewayInfo.RemoteIngressEnabled {
			// 关闭remote集群的ingres权限
			err = s.deployerService.UpdateIngressAccess(ctx,
				constants.RemoteClusterRolePrefic+"-"+gatewayInfo.Namespace, false, *cl)
			if err != nil {
				return err
			}
			// 卸载higress-controller
			deployErr := s.deployerService.UnDeployIngressController(ctx, gatewayInfo, ingressParam)
			if deployErr != nil {
				return deployErr
			}
		}
	}

	// 开启同步的remote都为true
	remoteParamMap := make(map[string]bool, 0)
	for i := range ingressParam.CLusterList {
		tempCluster := ingressParam.CLusterList[i]
		remoteParamMap[tempCluster.ClusterID] = tempCluster.Enabled
	}
	// 同步remote集群状态，
	for i := range *cl {
		updateCluster := (*cl)[i]
		IngressSyncEnabled := false
		if remoteParamMap != nil && remoteParamMap[updateCluster.ClusterUUID] {
			IngressSyncEnabled = true
		}
		// 兼容 IngressSyncEnabled 为空的情况，为空时表示关闭状态
		if updateCluster.IngressSyncEnabled == nil {
			updateCluster.IngressSyncEnabled = csm.Bool(false)
		}

		// 状态一致则跳过更新
		if *updateCluster.IngressSyncEnabled == IngressSyncEnabled {
			continue
		}
		// 状态不一致则更新数据库
		updateCluster.IngressSyncEnabled = &IngressSyncEnabled
		updateClusterErr := clusterModel.UpdateCluster(ctx, updateCluster.InstanceUUID, updateCluster.ClusterUUID, updateCluster.Region, &updateCluster)
		if updateClusterErr != nil {
			return updateClusterErr
		}
	}

	// 更新网关状态
	updateGateway := gatewayInfo
	updateGateway.RemoteIngressEnabled = &ingressParam.Enabled
	err = gatewayModel.UpdateGateway(ctx, gatewayInfo, updateGateway)
	if err != nil {
		return err
	}
	tx.Commit()

	return nil
}

// GetGatewayIngress 获取网关remote集群监听ingress的状态
func (s *Service) GetGatewayIngress(ctx csmContext.CsmContext, instanceUUID, gatewayID string) (ingressParam *meta.IngressParam, err error) {
	if instanceUUID == "" || gatewayID == "" {
		return nil, csmErr.NewInvalidParameterValueException("instanceUUID or gatewayID can not be empty")
	}
	var remoteList = make([]meta.RemoteCluster, 0)

	istiodCluster, meshType, err := s.instancesModel.GetInstanceIstiodCluster(ctx, instanceUUID)
	if err != nil {
		return nil, err
	}

	client, err := s.cceService.NewClient(ctx, istiodCluster.Region, istiodCluster.ClusterUUID, meshType)
	if err != nil {
		return nil, err
	}

	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer cancel()

	// 获取remote集群监听ingress列表
	cl, err := s.clusterModel.GetAllRemoteClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return nil, err
	}

	// 从svc上获取真实的监听情况
	realRemoteList, err := s.getHigressSvcAnnotation(client, cxt, istiodCluster.IstioInstallNamespace)
	if err != nil {
		return nil, err
	}

	for index := range *cl {
		remoteCluster := (*cl)[index]
		remoteList = append(remoteList, meta.RemoteCluster{
			ClusterID:   remoteCluster.ClusterUUID,
			ClusterName: remoteCluster.ClusterName,
			Region:      remoteCluster.Region,
			Enabled:     strings.Contains(realRemoteList, fmt.Sprintf("-%s", remoteCluster.ClusterUUID)),
		})
	}

	result := &meta.IngressParam{
		CLusterList: remoteList,
	}

	return result, nil
}

func (s *Service) getHigressPodStatus(client kube.Client, cxt context.Context, namespace string) (string, error) {
	higressStatus := constants.SmiUnknown
	higressPod, err := client.Kube().CoreV1().Pods(namespace).List(cxt,
		metav1.ListOptions{LabelSelector: constants.HigressLabelSelector})
	if err != nil {
		if kubeErrors.IsNotFound(err) {
			return higressStatus, nil
		}
		return higressStatus, err
	}

	if len(higressPod.Items) == 0 {
		higressStatus = constants.SmiDeploying
	} else {
		higressStatus = constants.SmiRunning
		for _, pod := range higressPod.Items {
			status := pod.Status.Phase
			if status == v1.PodRunning {
				continue
			}
			if status == v1.PodPending {
				higressStatus = constants.SmiDeploying
			} else {
				reason := strings.ToLower(pod.Status.Reason)
				if reason == constants.SmiEvicted {
					continue
				}
				higressStatus = constants.SmiAbnormal
			}
			break
		}
	}
	return higressStatus, nil
}

func (s *Service) getHigressSvcAnnotation(client kube.Client, cxt context.Context, namespace string) (string, error) {
	realRemoteList := ""
	// 从svc上获取真实的监听情况
	svcHigress, err := client.Kube().CoreV1().Services(namespace).Get(cxt, constants.ServiceNameHigress, metav1.GetOptions{})
	if err != nil {
		if kubeErrors.IsNotFound(err) {
			return realRemoteList, nil
		}
		return realRemoteList, err
	}

	if v, ok := svcHigress.Annotations[constants.AnnotationSyncClusterIDs]; ok {
		realRemoteList = v
	}
	return realRemoteList, nil
}
