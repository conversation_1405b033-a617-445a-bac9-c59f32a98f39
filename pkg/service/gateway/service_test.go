package gateway

import (
	"context"
	"errors"
	"os"
	"path"
	"path/filepath"
	"reflect"
	"runtime"
	"testing"
	"time"

	"github.com/baidubce/bce-sdk-go/services/appblb"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	"github.com/stretchr/testify/assert"
	"istio.io/api/networking/v1beta1"
	netv1beta1 "istio.io/client-go/pkg/apis/networking/v1beta1"
	"istio.io/client-go/pkg/apis/telemetry/v1alpha1"
	hpav1 "k8s.io/api/autoscaling/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	blbMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blb/mock"
	blsMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/bls/mock"
	certMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cert/mock"
	clusterMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	gatewayMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/gateway/mock"
	instanceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	monitorModelMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor/mock"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	cceServiceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	gatewayDeployerMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/deploy/gateway/mock"
	instanceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances/mock"
	monitorMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/monitor/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/file"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
)

var (
	mockDB, _         = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	testIstiodCluster = meta.Cluster{
		InstanceUUID:          "inst-1",
		Region:                "bj",
		ClusterUUID:           "clu-1",
		IstioInstallNamespace: "ns-1",
	}
	instanceNetworkType = meta.InstanceNetworkType{
		VpcNetworkName: "test-VpcNetworkName",
		VpcNetworkId:   "test-VpcNetworkId",
		VpcNetworkCidr: "test-VpcNetworkCidr",
		SubnetName:     "test-SubnetName",
		SubnetId:       "test-SubnetId",
		SubnetCidr:     "test-SubnetCidr",
	}
)

func buildTestGateway() *meta.GatewayModel {
	return &meta.GatewayModel{
		InstanceUUID:  "inst-1",
		ClusterUUID:   "",
		ClusterName:   "",
		GatewayUUID:   "gw-id-1",
		GatewayName:   "gw-1",
		Namespace:     "ns-1",
		Region:        "bj",
		Replicas:      2,
		ResourceQuota: "2c4g",
		IstioVersion:  "v1",
		SubnetId:      "sub-1",
		AccountId:     "123",
	}
}

func buildTestInstanceInfo() *meta.Instances {
	return &meta.Instances{
		InstanceUUID:          "inst-1",
		IstioInstallNamespace: "ns-1",
		IstioVersion:          testdata.Version1146,
		Region:                "bj",
		AccountId:             "abc",
	}
}

func buildTestClusterInfo() *meta.Cluster {
	return &meta.Cluster{
		ClusterName:           "clu-1",
		ClusterUUID:           "clu-id-1",
		Region:                "bj",
		IstioInstallNamespace: "ns-1",
	}
}

func buildTestTelemetry() *v1alpha1.Telemetry {
	return &v1alpha1.Telemetry{
		ObjectMeta: metav1.ObjectMeta{
			Labels:    map[string]string{constants.GwTelemetryLabelKey: "abc"},
			Name:      constants.GwTelemetryCrdName,
			Namespace: testIstiodCluster.IstioInstallNamespace,
		},
	}
}

func buildTestHpaInfo(enabled bool, minNum, maxNum int32) *meta.HpaConf {
	return &meta.HpaConf{
		InstanceUUID: "inst-1",
		GatewayUUID:  "gw-1",
		Enabled:      enabled,
		MinReplicas:  minNum,
		MaxReplicas:  maxNum,
	}
}

func buildTestTLSInfo(enabled bool) *meta.TLSAccConf {
	return &meta.TLSAccConf{
		InstanceUUID: "inst-1",
		Enabled:      enabled,
	}
}

func buildTestLogInfo(enabled bool, logFile string) *meta.LogConf {
	return &meta.LogConf{
		InstanceUUID: "inst-1",
		GatewayUUID:  "gw-1",
		Enabled:      enabled,
		Type:         "BLS",
		LogFile:      logFile,
	}
}

func buildTestBlsTaskInfo(enabled bool, logFile string) *meta.Log {
	return &meta.Log{
		Enabled: enabled,
		Type:    "BLS",
		LogFile: logFile,
	}
}

func TestNewGateway(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name       string
		gateway    *meta.GatewayModel
		hpaConf    *meta.HpaConf
		blbConf    *meta.BlbConf
		logConf    *meta.LogConf
		tlsAccConf *meta.TLSAccConf
		instInfo   *meta.Instances
		clusInfo   *meta.Cluster
		gwErr      error
		vsErr      error
		initErr    error
		insErr     error
		hpaErr     error
		expectErr  error
	}{
		{
			name:    "correct-NewGateway",
			gateway: buildTestGateway(),
			hpaConf: &meta.HpaConf{
				Enabled: false,
			},
			blbConf: &meta.BlbConf{
				BlbID: "lb-123",
				EipConf: &meta.EipConf{
					Enabled: true,
					Type:    "BIND",
					ID:      "123",
					IP:      "*******",
				},
			},
			logConf: &meta.LogConf{
				Enabled: false,
			},
			instInfo:  buildTestInstanceInfo(),
			clusInfo:  buildTestClusterInfo(),
			gwErr:     nil,
			expectErr: nil,
		},
		{
			name:    "failed-NewGateway",
			gateway: buildTestGateway(),
			hpaConf: &meta.HpaConf{
				Enabled: false,
			},
			blbConf: &meta.BlbConf{
				BlbID: "lb-123",
				EipConf: &meta.EipConf{
					Enabled: true,
					Type:    "BIND",
					ID:      "123",
					IP:      "*******",
				},
			},
			logConf: &meta.LogConf{
				Enabled: false,
			},
			instInfo:  buildTestInstanceInfo(),
			clusInfo:  buildTestClusterInfo(),
			gwErr:     nil,
			insErr:    errors.New("insErr"),
			expectErr: nil,
		},
		{
			name:    "correct-DeployHPA",
			gateway: buildTestGateway(),
			hpaConf: &meta.HpaConf{
				Enabled:     true,
				MinReplicas: 1,
				MaxReplicas: 3,
				Namespace:   "ns-1",
				GatewayUUID: "gw-id-1",
			},
			blbConf: &meta.BlbConf{
				BlbID: "lb-123",
				EipConf: &meta.EipConf{
					Enabled: true,
					Type:    "BIND",
					ID:      "123",
					IP:      "*******",
				},
			},
			logConf: &meta.LogConf{
				Enabled: false,
			},
			instInfo:  buildTestInstanceInfo(),
			clusInfo:  buildTestClusterInfo(),
			gwErr:     nil,
			expectErr: nil,
		},
		{
			name:    "failed-DeployHPA",
			gateway: buildTestGateway(),
			hpaConf: &meta.HpaConf{
				Enabled:     true,
				MinReplicas: 1,
				MaxReplicas: 3,
				Namespace:   "ns-1",
				GatewayUUID: "gw-id-1",
			},
			blbConf: &meta.BlbConf{
				BlbID: "lb-123",
				EipConf: &meta.EipConf{
					Enabled: true,
					Type:    "BIND",
					ID:      "123",
					IP:      "*******",
				},
			},
			logConf: &meta.LogConf{
				Enabled: false,
			},
			instInfo:  buildTestInstanceInfo(),
			clusInfo:  buildTestClusterInfo(),
			gwErr:     nil,
			hpaErr:    errors.New("hpaErr"),
			expectErr: nil,
		},
		{
			name:    "failed-CheckGatewayExist",
			gateway: buildTestGateway(),
			hpaConf: &meta.HpaConf{
				Enabled: false,
			},
			blbConf: &meta.BlbConf{
				BlbID: "lb-123",
				EipConf: &meta.EipConf{
					Enabled: false,
				},
			},
			logConf: &meta.LogConf{
				Enabled: false,
			},
			instInfo:  buildTestInstanceInfo(),
			clusInfo:  buildTestClusterInfo(),
			gwErr:     errors.New("gwErr"),
			expectErr: errors.New("gwErr"),
		},
		{
			name:    "correct-DeployTelemetry",
			gateway: buildTestGateway(),
			hpaConf: &meta.HpaConf{
				Enabled:     true,
				MinReplicas: 1,
				MaxReplicas: 3,
				Namespace:   "ns-1",
				GatewayUUID: "gw-id-1",
			},
			blbConf: &meta.BlbConf{
				BlbID: "lb-123",
				EipConf: &meta.EipConf{
					Enabled: true,
					Type:    "BIND",
					ID:      "123",
					IP:      "*******",
				},
			},
			logConf: &meta.LogConf{
				Enabled: true,
			},
			instInfo:  buildTestInstanceInfo(),
			clusInfo:  buildTestClusterInfo(),
			gwErr:     nil,
			expectErr: nil,
		},
		{
			name: "NewGateway-with-CProm",
			gateway: &meta.GatewayModel{
				InstanceUUID:   "inst-1",
				ClusterUUID:    "",
				ClusterName:    "",
				GatewayUUID:    "gw-id-1",
				GatewayName:    "gw-1",
				Namespace:      "ns-1",
				Region:         "bj",
				Replicas:       2,
				ResourceQuota:  "2c4g",
				IstioVersion:   "v1",
				SubnetId:       "sub-1",
				AccountId:      "123",
				MonitorEnabled: csm.Bool(true),
			},
			hpaConf: &meta.HpaConf{
				Enabled: false,
			},
			blbConf: &meta.BlbConf{
				BlbID: "lb-123",
				EipConf: &meta.EipConf{
					Enabled: true,
					Type:    "BIND",
					ID:      "123",
					IP:      "*******",
				},
			},
			logConf: &meta.LogConf{
				Enabled: false,
			},
			instInfo:  buildTestInstanceInfo(),
			clusInfo:  buildTestClusterInfo(),
			gwErr:     nil,
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockGatewayModel := gatewayMock.NewMockServiceInterface(ctrl)
		mockGatewayDeployer := gatewayDeployerMock.NewMockServiceInterface(ctrl)
		mockBlbModel := blbMock.NewMockServiceInterface(ctrl)
		mockBlsModel := blsMock.NewMockServiceInterface(ctrl)
		mockMonitor := monitorMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:             NewOption(mockDB),
			instancesModel:  mockInstancesModel,
			gatewayModel:    mockGatewayModel,
			deployerService: mockGatewayDeployer,
			blbModel:        mockBlbModel,
			blsModel:        mockBlsModel,
			monitorService:  mockMonitor,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()

			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(testInfo.instInfo, nil)
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(testInfo.clusInfo, meta.HostingMeshType, nil)

			mockMonitor.EXPECT().CreateGatewayMonitor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(buildCPromGatewayInfo(), nil)

			mockGatewayModel.EXPECT().GetGatewayList(mockCtx, gomock.Any()).Return(&[]meta.GatewayModel{}, testInfo.gwErr)
			if testInfo.gwErr == nil {
				mockGatewayModel.EXPECT().WithTx(gomock.Any()).Return(mockGatewayModel)
				mockGatewayModel.EXPECT().NewGateway(mockCtx, gomock.Any()).Return(nil)

				mockGatewayDeployer.EXPECT().Init(mockCtx, gomock.Any(), gomock.Any()).Return(nil)
				mockGatewayDeployer.EXPECT().InstallCCEIngressGateway(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(testInfo.insErr)
				if testInfo.insErr == nil {
					if testInfo.hpaConf.Enabled {
						mockGatewayDeployer.EXPECT().DeployHPA(mockCtx, gomock.Any()).Return(testInfo.hpaErr)
					}
					if testInfo.hpaErr == nil {
						mockGatewayDeployer.EXPECT().DeployGwVs(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.vsErr)
					}
					if testInfo.hpaErr == nil && testInfo.vsErr == nil && testInfo.logConf.Enabled {
						mockBlsModel.EXPECT().CreateHostingBlsTask(mockCtx, gomock.Any(), gomock.Any()).Return("task-1", nil)
						mockGatewayDeployer.EXPECT().DeployTelemetry(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
					}
				}

				mockBlbModel.EXPECT().DeleteAllAppListeners(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(2)
				mockBlbModel.EXPECT().GetAllAppIpGroups(mockCtx, gomock.Any(), gomock.Any()).Return(&appblb.DescribeAppIpGroupResult{
					AppIpGroupList: []appblb.AppIpGroup{
						{
							Id: "ip-group-123",
						},
					},
				}, nil)
				mockBlbModel.EXPECT().DeleteAppIpGroup(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				mockBlbModel.EXPECT().CreateAppTCPListener(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(2)
			}

			err := service.NewGateway(mockCtx, testInfo.gateway, testInfo.hpaConf, testInfo.blbConf, testInfo.logConf, testInfo.tlsAccConf)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}

			time.Sleep(100 * time.Millisecond)
		})
		// TODO: 待优化
		_, filename, _, _ := runtime.Caller(0)
		configPath := path.Join(path.Dir(filename), "../../../", constants.Templates)
		ingressGatewayDstPath := path.Join(configPath, constants.BaseGatewayTemplate, testInfo.gateway.GatewayUUID+"-"+constants.IngressGatewayYaml)
		file.RemoveFile(ingressGatewayDstPath)
	}
}

func buildCPromGatewayInfo() *meta.CPromGatewayInfo {
	return &meta.CPromGatewayInfo{
		ScrapeJobID: "test-scrape-job-id",
		AgentID:     "agent-id",
	}
}

func buildCPromInstance() *meta.CPromInstance {
	return &meta.CPromInstance{
		InstanceName: "test-name",
		InstanceId:   "test-id",
		Region:       "gz",
	}
}

func TestDeleteGateway(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name        string
		gateway     *meta.GatewayModel
		testGwSvc   *v1.Service
		resetBlbErr error
		expectErr   error
	}{
		{
			name:    "correct-DeleteGateway",
			gateway: buildTestGateway(),
			testGwSvc: &v1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      map[string]string{"app": "istio-ingressgateway"},
					Annotations: map[string]string{constants.CceLoadBalancerID: "lb-123"},
					Name:        constants.IngressGatewayResourceName,
					Namespace:   testIstiodCluster.IstioInstallNamespace,
				},
			},
			resetBlbErr: nil,
			expectErr:   nil,
		},
		{
			name: "DeleteGateway with higress controller",
			gateway: &meta.GatewayModel{
				InstanceUUID:         "inst-1",
				ClusterUUID:          "",
				ClusterName:          "",
				GatewayUUID:          "gw-id-1",
				GatewayName:          "gw-1",
				Namespace:            "ns-1",
				Region:               "bj",
				Replicas:             2,
				ResourceQuota:        "2c4g",
				IstioVersion:         "v1",
				SubnetId:             "sub-1",
				AccountId:            "123",
				RemoteIngressEnabled: csm.Bool(true),
			},
			testGwSvc: &v1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      map[string]string{"app": "istio-ingressgateway"},
					Annotations: map[string]string{constants.CceLoadBalancerID: "lb-123"},
					Name:        constants.IngressGatewayResourceName,
					Namespace:   testIstiodCluster.IstioInstallNamespace,
				},
			},
			resetBlbErr: nil,
			expectErr:   nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayModel := gatewayMock.NewMockServiceInterface(ctrl)
		mockGatewayDeployer := gatewayDeployerMock.NewMockServiceInterface(ctrl)
		mockBlbModel := blbMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		mockBlsModel := blsMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:             NewOption(mockDB),
			gatewayModel:    mockGatewayModel,
			deployerService: mockGatewayDeployer,
			blbModel:        mockBlbModel,
			blsModel:        mockBlsModel,
			cceService:      mockCceService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")
			fakeClient := kube.NewFakeClient()
			if testInfo.testGwSvc != nil {
				_, _ = fakeClient.Kube().CoreV1().Services(testIstiodCluster.IstioInstallNamespace).Create(context.TODO(),
					testInfo.testGwSvc, metav1.CreateOptions{})
			}

			mockGatewayModel.EXPECT().GetGatewayInfo(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.gateway, nil)
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			mockBlbModel.EXPECT().DeleteAllAppListeners(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(2)
			mockBlbModel.EXPECT().GetAllAppIpGroups(mockCtx, gomock.Any(), gomock.Any()).Return(&appblb.DescribeAppIpGroupResult{
				AppIpGroupList: []appblb.AppIpGroup{
					{
						Id: "ip-group-123",
					},
				},
			}, nil)
			mockBlbModel.EXPECT().DeleteAppIpGroup(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(testInfo.resetBlbErr).Times(1)

			mockGatewayModel.EXPECT().WithTx(gomock.Any()).Return(mockGatewayModel)
			mockGatewayModel.EXPECT().DeleteGateway(mockCtx, gomock.Any(), gomock.Any()).Return(nil)

			mockGatewayDeployer.EXPECT().UnDeployIngressController(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			if testInfo.resetBlbErr == nil {
				mockGatewayDeployer.EXPECT().Init(mockCtx, gomock.Any(), gomock.Any()).Return(nil)
				mockGatewayDeployer.EXPECT().UninstallCCEIngressGateway(mockCtx, gomock.Any()).Return(nil)
				mockGatewayDeployer.EXPECT().UndeployHPA(mockCtx, gomock.Any()).Return(nil)
				mockGatewayDeployer.EXPECT().UndeployGwVs(mockCtx, gomock.Any(), gomock.Any()).Return(nil)
				mockGatewayDeployer.EXPECT().UndeployTelemetry(mockCtx, gomock.Any(), gomock.Any()).Return("task-1", nil)
				mockBlsModel.EXPECT().DeleteHostingBlsTask(mockCtx, gomock.Any(), gomock.Any()).Return(nil)
			}

			err := service.DeleteGateway(mockCtx, testInfo.gateway.InstanceUUID, testInfo.gateway.ClusterUUID)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}

			time.Sleep(100 * time.Millisecond)
		})
	}
}

func isArrayDesc(arr []meta.GatewayDisplay, length int64) bool {
	if length == 1 {
		return true
	}
	if arr[length-1].CreateTime.Unix() > arr[length-2].CreateTime.Unix() {
		return false
	}
	return isArrayDesc(arr, length-1)
}

func isArrayAsc(arr []meta.GatewayDisplay, length int64) bool {
	if length == 1 {
		return true
	}
	if arr[length-1].CreateTime.Unix() < arr[length-2].CreateTime.Unix() {
		return false
	}
	return isArrayAsc(arr, length-1)
}

func buildTestGatewayList() []meta.GatewayModel {
	var testGatewayList []meta.GatewayModel
	tmpTime := time.Now()
	gatewayInst := meta.GatewayModel{
		BaseModel: dbutil.BaseModel{
			CreateTime: &tmpTime,
		},
		InstanceUUID:  "inst-1",
		GatewayName:   "gw-1",
		GatewayUUID:   "gw-id-1",
		DeployMode:    "hosting",
		ResourceQuota: "2c4g",
		Replicas:      3,
	}
	testGatewayList = append(testGatewayList, gatewayInst)
	return testGatewayList
}

func TestGetGatewayList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testGatewayList := buildTestGatewayList()
	testInfos := []struct {
		name         string
		mrp          *meta.CsmMeshRequestParams
		expectRes    *[]meta.GatewayModel
		expectNum    int64
		needAscOrder bool
	}{
		{
			name: "test_pageSlice",
			mrp: &meta.CsmMeshRequestParams{
				PageSize: 4,
				PageNo:   1,
			},
			expectNum:    4,
			needAscOrder: false,
		},
		{
			name: "test_pageSlice2",
			mrp: &meta.CsmMeshRequestParams{
				PageSize: 4,
				PageNo:   2,
			},
			expectNum:    2,
			needAscOrder: false,
		},
		{
			name: "test_order",
			mrp: &meta.CsmMeshRequestParams{
				PageSize: 3,
				PageNo:   1,
				Order:    "Asc",
			},
			expectNum:    3,
			needAscOrder: true,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayModel := gatewayMock.NewMockServiceInterface(ctrl)
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		service := &Service{
			opt:            NewOption(mockDB),
			cceService:     mockCceService,
			gatewayModel:   mockGatewayModel,
			instancesModel: mockInstancesModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			mockCtx := csmContext.MockNewCsmContext()
			mockGatewayModel.EXPECT().GetGatewayList(mockCtx, testInfo.mrp).Return(&testGatewayList, nil)
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&testIstiodCluster,
				meta.HostingMeshType, nil).Times(len(testGatewayList))
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient,
				nil).Times(len(testGatewayList))

			res, _ := service.GetGatewayList(mockCtx, testInfo.mrp)

			assert.Equal(t, 1, len(res.Result))

			//if testInfo.needAscOrder {
			//	assert.True(t, isArrayAsc(res.Result, int64(len(res.Result))))
			//} else {
			//	assert.True(t, isArrayDesc(res.Result, int64(len(res.Result))))
			//}
		})
	}
}

func buildGatewayModel() *meta.GatewayModel {
	return &meta.GatewayModel{
		InstanceUUID:  "inst-1",
		ClusterUUID:   "clu-1",
		GatewayName:   "gw-1",
		GatewayUUID:   "gw-id-1",
		DeployMode:    "hosting",
		GatewayType:   "ingress",
		Namespace:     "ns-1",
		Replicas:      2,
		ResourceQuota: "2c4g",
		IstioVersion:  "v1",
		VpcNetworkId:  "vpc-1",
		SubnetId:      "sub-1",
		AccountId:     "123",
	}
}

func TestGetGatewayDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name          string
		gateway       *meta.GatewayModel
		testHpa       *hpav1.HorizontalPodAutoscaler
		testTm        *v1alpha1.Telemetry
		testLog       *meta.Log
		getClusterErr error
		newClientErr  error
		expectErr     error
	}{
		{
			name:          "error-GetGatewayDetail",
			gateway:       buildGatewayModel(),
			getClusterErr: errors.New("getClusterErr"),
			newClientErr:  nil,
			expectErr:     errors.New("getClusterErr"),
		},
		{
			name:          "error-GetGatewayDetail",
			gateway:       buildGatewayModel(),
			getClusterErr: nil,
			newClientErr:  errors.New("newClientErr"),
			expectErr:     errors.New("newClientErr"),
		},
		{
			name:    "correct-GetGatewayDetail",
			gateway: buildGatewayModel(),
			testHpa: &hpav1.HorizontalPodAutoscaler{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IngressGatewayResourceName,
					Namespace: testIstiodCluster.IstioInstallNamespace,
				}, Spec: hpav1.HorizontalPodAutoscalerSpec{
					MaxReplicas: 3,
				},
			},
			testTm: &v1alpha1.Telemetry{
				ObjectMeta: metav1.ObjectMeta{
					Labels:    map[string]string{constants.GwTelemetryLabelKey: "task-123"},
					Name:      constants.GwTelemetryCrdName,
					Namespace: testIstiodCluster.IstioInstallNamespace,
				},
			},
			testLog: &meta.Log{
				Type:    "BLS",
				LogFile: "a",
			},
			getClusterErr: nil,
			newClientErr:  nil,
			expectErr:     nil,
		},
		{
			name: "correct-GetGatewayDetail",
			gateway: &meta.GatewayModel{
				InstanceUUID:      "inst-1",
				ClusterUUID:       "clu-1",
				GatewayName:       "gw-1",
				GatewayUUID:       "gw-id-1",
				DeployMode:        "hosting",
				GatewayType:       "ingress",
				Namespace:         "ns-1",
				Replicas:          2,
				ResourceQuota:     "2c4g",
				IstioVersion:      "v1",
				VpcNetworkId:      "vpc-1",
				SubnetId:          "sub-1",
				AccountId:         "123",
				MonitorEnabled:    csm.Bool(true),
				MonitorInstanceID: "ins-id",
				MonitorRegion:     "gz",
			},
			testHpa: &hpav1.HorizontalPodAutoscaler{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IngressGatewayResourceName,
					Namespace: testIstiodCluster.IstioInstallNamespace,
				}, Spec: hpav1.HorizontalPodAutoscalerSpec{
					MaxReplicas: 3,
				},
			},
			testTm: &v1alpha1.Telemetry{
				ObjectMeta: metav1.ObjectMeta{
					Labels:    map[string]string{constants.GwTelemetryLabelKey: "task-123"},
					Name:      constants.GwTelemetryCrdName,
					Namespace: testIstiodCluster.IstioInstallNamespace,
				},
			},
			testLog: &meta.Log{
				Type:    "BLS",
				LogFile: "a",
			},
			getClusterErr: nil,
			newClientErr:  nil,
			expectErr:     nil,
		},
	}
	for _, testInfo := range testInfos {
		mockGatewayModel := gatewayMock.NewMockServiceInterface(ctrl)
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		mockBlsModel := blsMock.NewMockServiceInterface(ctrl)
		mockMonitorModel := monitorModelMock.NewMockServiceInterface(ctrl)
		mockInstanceService := instanceService.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:             NewOption(mockDB),
			cceService:      mockCceService,
			gatewayModel:    mockGatewayModel,
			instancesModel:  mockInstancesModel,
			instanceService: mockInstanceService,
			blsModel:        mockBlsModel,
			monitorModel:    mockMonitorModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			mockCtx := csmContext.MockNewCsmContext()
			mockGatewayModel.EXPECT().GetGatewayInfo(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.gateway, nil)
			mockInstanceService.EXPECT().GetVpcAndSubnetInfo(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&instanceNetworkType, nil)
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&testIstiodCluster,
				meta.HostingMeshType, testInfo.getClusterErr)
			if testInfo.getClusterErr == nil {
				mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient,
					testInfo.newClientErr)
			}
			if testInfo.testHpa != nil {
				_, _ = fakeClient.Kube().AutoscalingV1().HorizontalPodAutoscalers(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.testHpa, metav1.CreateOptions{})
			}
			if testInfo.testTm != nil {
				_, _ = fakeClient.Istio().TelemetryV1alpha1().Telemetries(testIstiodCluster.IstioInstallNamespace).Create(context.TODO(),
					testInfo.testTm, metav1.CreateOptions{})
				mockBlsModel.EXPECT().GetHostingBlsTaskDetail(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.testLog, nil)
			}
			mockMonitorModel.EXPECT().GetCPromInstanceById(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(buildCPromInstance(), nil)

			res, err := service.GetGatewayDetail(mockCtx, testInfo.gateway.InstanceUUID, testInfo.gateway.ClusterUUID)

			if testInfo.expectErr == nil {
				assert.Nil(t, err)
				assert.Equal(t, testInfo.gateway.GatewayName, res.BasicConfig.GatewayName)
			} else {
				assert.Nil(t, res)
				assert.Contains(t, testInfo.expectErr.Error(), err.Error())
			}
		})
	}
}

func TestGetGatewayStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectRes string
		testPod   *v1.Pod
		testHpa   *hpav1.HorizontalPodAutoscaler
		expectHpa *meta.HPA
		expectErr error
	}{
		{
			name:      "correct-GetGatewayStatus-Running",
			expectRes: constants.SmiRunning,
			testPod: &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{"app": "istio-ingressgateway"},
				},
				Status: v1.PodStatus{
					Phase: v1.PodPhase("Running"),
				},
			},
			testHpa: nil,
			expectHpa: &meta.HPA{
				Enabled:     false,
				MinReplicas: 0,
				MaxReplicas: 0,
			},
			expectErr: nil,
		},
		{
			name:      "correct-GetGatewayStatus-Deploying",
			expectRes: constants.SmiDeploying,
			testPod:   nil,
			testHpa: &hpav1.HorizontalPodAutoscaler{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.IngressGatewayResourceName,
					Namespace: testIstiodCluster.IstioInstallNamespace,
				},
				Spec: hpav1.HorizontalPodAutoscalerSpec{
					MaxReplicas: 3,
				},
			},
			expectHpa: &meta.HPA{
				Enabled:     true,
				MaxReplicas: 3,
				MinReplicas: 1,
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		service := &Service{
			opt:            NewOption(mockDB),
			cceService:     mockCceService,
			instancesModel: mockInstancesModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			if testInfo.testPod != nil {
				_, _ = fakeClient.Kube().CoreV1().Pods(testIstiodCluster.IstioInstallNamespace).Create(context.TODO(),
					testInfo.testPod, metav1.CreateOptions{})
			}
			if testInfo.testHpa != nil {
				_, _ = fakeClient.Kube().AutoscalingV1().HorizontalPodAutoscalers(
					testIstiodCluster.IstioInstallNamespace).Create(context.TODO(), testInfo.testHpa, metav1.CreateOptions{})
			}
			mockCtx := csmContext.MockNewCsmContext()
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&testIstiodCluster, meta.HostingMeshType, nil)
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			gatewayModel := buildGatewayModel()
			gatewayModel.InstanceUUID = testIstiodCluster.InstanceUUID
			res, _, hpa, _, _, _, err := service.getGatewayBasicConfig(mockCtx, gatewayModel)

			assert.Equal(t, testInfo.expectHpa, hpa)

			assert.Equal(t, testInfo.expectRes, res)

			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestModifyGatewayMonitor(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockCtx := csmContext.MockNewCsmContext()

	testInfos := []struct {
		name               string
		monitor            *meta.Monitor
		gatewayModel       *meta.GatewayModel
		deleteScrapeJobErr error
		success            bool
	}{
		{
			name:    "close cprom",
			monitor: buildMonitor(),
			gatewayModel: &meta.GatewayModel{
				InstanceUUID:      "inst-1",
				ClusterUUID:       "clu-1",
				GatewayName:       "gw-1",
				GatewayUUID:       "gw-id-1",
				DeployMode:        "hosting",
				GatewayType:       "ingress",
				Namespace:         "ns-1",
				Replicas:          2,
				ResourceQuota:     "2c4g",
				IstioVersion:      "v1",
				VpcNetworkId:      "vpc-1",
				SubnetId:          "sub-1",
				AccountId:         "123",
				MonitorEnabled:    csm.Bool(true),
				MonitorRegion:     "gz",
				MonitorInstanceID: "instance-id",
				MonitorAgentID:    "agent-id",
				MonitorJobID:      "job-id",
			},
			success: true,
		},
		{
			name:    "close cprom with not found agent",
			monitor: buildMonitor(),
			gatewayModel: &meta.GatewayModel{
				InstanceUUID:      "inst-1",
				ClusterUUID:       "clu-1",
				GatewayName:       "gw-1",
				GatewayUUID:       "gw-id-1",
				DeployMode:        "hosting",
				GatewayType:       "ingress",
				Namespace:         "ns-1",
				Replicas:          2,
				ResourceQuota:     "2c4g",
				IstioVersion:      "v1",
				VpcNetworkId:      "vpc-1",
				SubnetId:          "sub-1",
				AccountId:         "123",
				MonitorEnabled:    csm.Bool(true),
				MonitorRegion:     "gz",
				MonitorInstanceID: "instance-id",
				MonitorAgentID:    "agent-id",
				MonitorJobID:      "job-id",
			},
			deleteScrapeJobErr: errors.New(csmErr.CPromAgentNotFoundException),
			success:            true,
		},
		{
			name: "open cprom",
			monitor: &meta.Monitor{
				Enabled: true,
				Instances: []meta.MonitorInstance{
					{
						ID:     "id1",
						Region: "gz",
						Name:   "name",
					},
				},
			},
			gatewayModel: &meta.GatewayModel{
				InstanceUUID:      "inst-1",
				ClusterUUID:       "clu-1",
				GatewayName:       "gw-1",
				GatewayUUID:       "gw-id-1",
				DeployMode:        "hosting",
				GatewayType:       "ingress",
				Namespace:         "ns-1",
				Replicas:          2,
				ResourceQuota:     "2c4g",
				IstioVersion:      "v1",
				VpcNetworkId:      "vpc-1",
				SubnetId:          "sub-1",
				AccountId:         "123",
				MonitorEnabled:    csm.Bool(true),
				MonitorRegion:     "gz",
				MonitorInstanceID: "instance-id",
				MonitorAgentID:    "agent-id",
				MonitorJobID:      "job-id",
			},
			success: true,
		},
		{
			name: "open cprom with dup InstanceID",
			monitor: &meta.Monitor{
				Enabled: true,
				Instances: []meta.MonitorInstance{
					{
						ID:     "id1",
						Region: "gz",
						Name:   "name",
					},
				},
			},
			gatewayModel: &meta.GatewayModel{
				InstanceUUID:      "inst-1",
				ClusterUUID:       "clu-1",
				GatewayName:       "gw-1",
				GatewayUUID:       "gw-id-1",
				DeployMode:        "hosting",
				GatewayType:       "ingress",
				Namespace:         "ns-1",
				Replicas:          2,
				ResourceQuota:     "2c4g",
				IstioVersion:      "v1",
				VpcNetworkId:      "vpc-1",
				SubnetId:          "sub-1",
				AccountId:         "123",
				MonitorEnabled:    csm.Bool(true),
				MonitorRegion:     "gz",
				MonitorInstanceID: "id1",
				MonitorAgentID:    "agent-id",
				MonitorJobID:      "job-id",
			},
			success: true,
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			mockGateway := gatewayMock.NewMockServiceInterface(ctrl)
			mockMonitorModel := monitorModelMock.NewMockServiceInterface(ctrl)
			mockMonitor := monitorMock.NewMockServiceInterface(ctrl)
			mockDeploy := gatewayDeployerMock.NewMockServiceInterface(ctrl)

			mockGateway.EXPECT().WithTx(gomock.Any()).Return(mockGateway)
			mockGateway.EXPECT().GetGatewayInfo(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(testInfo.gatewayModel, nil)

			mockMonitorModel.EXPECT().DeleteScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
				gomock.Any()).AnyTimes().Return(testInfo.deleteScrapeJobErr)

			mockMonitor.EXPECT().CreateGatewayMonitor(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(buildCPromGatewayInfo(), nil)

			mockGateway.EXPECT().UpdateGateway(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil)

			mockDeploy.EXPECT().UpdateMonitorInfoInAnnotation(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil)

			service := &Service{
				opt:             NewOption(mockDB),
				gatewayModel:    mockGateway,
				monitorModel:    mockMonitorModel,
				monitorService:  mockMonitor,
				deployerService: mockDeploy,
			}

			_, err := service.ModifyGatewayMonitor(mockCtx, "id1", "ID2", testInfo.monitor)
			if testInfo.success {
				assert.NoError(t, err)
			}
		})
	}

}

func buildMonitor() *meta.Monitor {
	return &meta.Monitor{
		Enabled: false,
	}
}

func TestExistGatewayWithInstanceUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
	mockCtx := csmContext.MockNewCsmContext()
	mockGatewayModel := gatewayMock.NewMockServiceInterface(ctrl)

	testGatewayList := buildTestGatewayList()

	testInfos := []struct {
		name       string
		instanceId string
		instInfo   *meta.Instances
		gateModels []meta.GatewayModel
		expectRes  bool
		expectErr  error
	}{
		{
			name: "existGatewayWithInstanceUUID-ok",
			instInfo: &meta.Instances{
				InstanceUUID: "xxx",
				Region:       "aaa",
				AccountId:    "bbb",
			},
			gateModels: testGatewayList,
			expectRes:  true,
			expectErr:  nil,
		},
	}
	for _, testInfo := range testInfos {
		service := &Service{
			opt:            NewOption(mockDB),
			instancesModel: mockInstancesModel,
			gatewayModel:   mockGatewayModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(testInfo.instInfo, nil)
			mockGatewayModel.EXPECT().GetGatewayList(gomock.Any(), gomock.Any()).Return(&testInfo.gateModels, nil)
			res, err := service.ExistGatewayWithInstanceUUID(mockCtx, testInfo.instanceId)
			assert.Equal(t, testInfo.expectRes, res)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestGetGatewayBlbList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name          string
		istiodCluster *meta.Cluster
		testGwSvc     *v1.Service
		testBlbInfo   *appblb.DescribeLoadBalancerDetailResult
		expectErr     error
	}{
		{
			name: "correct-GetGatewayBlbList",
			istiodCluster: &meta.Cluster{
				ClusterUUID:           "clu-123",
				Region:                "bj",
				IstioInstallNamespace: "ns-1",
			},
			testGwSvc: &v1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      map[string]string{"app": "istio-ingressgateway"},
					Annotations: map[string]string{constants.CceLoadBalancerID: "lb-123"},
					Name:        constants.IngressGatewayResourceName,
					Namespace:   testIstiodCluster.IstioInstallNamespace,
				},
			},
			testBlbInfo: &appblb.DescribeLoadBalancerDetailResult{
				BlbId:      "lb-123",
				PublicIp:   "*******",
				Address:    "*******",
				Status:     appblb.BLBStatusAvailable,
				CreateTime: "2022.11-28",
				Listener: []appblb.ListenerModel{
					{
						Port: "8080",
						Type: "TCP",
					},
				},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		mockBlbModel := blbMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:            NewOption(mockDB),
			cceService:     mockCceService,
			instancesModel: mockInstancesModel,
			blbModel:       mockBlbModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			if testInfo.testGwSvc != nil {
				_, _ = fakeClient.Kube().CoreV1().Services(testIstiodCluster.IstioInstallNamespace).Create(context.TODO(),
					testInfo.testGwSvc, metav1.CreateOptions{})
			}
			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&testIstiodCluster, meta.HostingMeshType, nil)
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			mockBlbModel.EXPECT().GetBlb(mockCtx, "lb-123", "bj").Return(testInfo.testBlbInfo, nil)

			res, err := service.GetGatewayBlbList(mockCtx, testIstiodCluster.InstanceUUID)
			assert.Equal(t, res.TotalCount, int64(1))

			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func buildGatewayCrd80443() *netv1beta1.Gateway {
	return &netv1beta1.Gateway{
		ObjectMeta: metav1.ObjectMeta{
			Labels:    map[string]string{"app": "istio-ingressgateway"},
			Name:      constants.GwCrdName,
			Namespace: testIstiodCluster.IstioInstallNamespace,
		},
		Spec: v1beta1.Gateway{
			Servers: []*v1beta1.Server{
				{
					Hosts: []string{"*"},
					Port: &v1beta1.Port{
						Name:     "http_1672317078",
						Number:   8080,
						Protocol: "HTTP",
					},
				},
				{
					Hosts: []string{"*"},
					Port: &v1beta1.Port{
						Name:     "https_1672317078",
						Number:   8443,
						Protocol: "HTTPS",
					},
					Tls: &v1beta1.ServerTLSSettings{
						Mode:           v1beta1.ServerTLSSettings_SIMPLE,
						CredentialName: "cert-0n3pjjjue21g-helloworld",
					},
				},
			},
		},
	}
}

func buildDiffHostsGatewayCrd80443() *netv1beta1.Gateway {
	return &netv1beta1.Gateway{
		ObjectMeta: metav1.ObjectMeta{
			Labels:    map[string]string{"app": "istio-ingressgateway"},
			Name:      constants.GwCrdName,
			Namespace: testIstiodCluster.IstioInstallNamespace,
		},
		Spec: v1beta1.Gateway{
			Servers: []*v1beta1.Server{
				{
					Hosts: []string{"*"},
					Port: &v1beta1.Port{
						Name:     "http_1672317078",
						Number:   8080,
						Protocol: "HTTP",
					},
				},
				{
					Hosts: []string{"*2"},
					Port: &v1beta1.Port{
						Name:     "https_1672317078",
						Number:   8443,
						Protocol: "HTTPS",
					},
					Tls: &v1beta1.ServerTLSSettings{
						Mode:           v1beta1.ServerTLSSettings_SIMPLE,
						CredentialName: "cert-0n3pjjjue21g-helloworld",
					},
				},
			},
		},
	}
}

func buildGatewayCrd443() *netv1beta1.Gateway {
	return &netv1beta1.Gateway{
		ObjectMeta: metav1.ObjectMeta{
			Labels:    map[string]string{"app": "istio-ingressgateway"},
			Name:      constants.GwCrdName,
			Namespace: testIstiodCluster.IstioInstallNamespace,
		},
		Spec: v1beta1.Gateway{
			Servers: []*v1beta1.Server{
				{
					Hosts: []string{"*"},
					Port: &v1beta1.Port{
						Name:     "https_1672317078",
						Number:   8443,
						Protocol: "HTTPS",
					},
					Tls: &v1beta1.ServerTLSSettings{
						Mode:           v1beta1.ServerTLSSettings_SIMPLE,
						CredentialName: "cert-0n3pjjjue21g-helloworld",
					},
				},
			},
		},
	}
}

func buildDefaultCluster() *meta.Cluster {
	return &meta.Cluster{
		ClusterUUID:           "clu-123",
		Region:                "bj",
		IstioInstallNamespace: "ns-1",
	}
}

func buildIPGroups80() *appblb.DescribeAppIpGroupResult {
	return &appblb.DescribeAppIpGroupResult{
		AppIpGroupList: []appblb.AppIpGroup{
			{
				Id:   "ip-group-123",
				Name: "TCP-80",
			},
		},
	}
}

func buildIPGroups443() *appblb.DescribeAppIpGroupResult {
	return &appblb.DescribeAppIpGroupResult{
		AppIpGroupList: []appblb.AppIpGroup{
			{
				Id:   "ip-group-456",
				Name: "TCP-443",
			},
		},
	}
}

func buildIPGroups80443() *appblb.DescribeAppIpGroupResult {
	return &appblb.DescribeAppIpGroupResult{
		AppIpGroupList: []appblb.AppIpGroup{
			{
				Id:   "ip-group-123",
				Name: "TCP-80",
			},
			{
				Id:   "ip-group-456",
				Name: "TCP-443",
			},
		},
	}
}

func TestGetGatewayDomainList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name          string
		istiodCluster *meta.Cluster
		testGwCrd     *netv1beta1.Gateway
		expectErr     error
	}{
		{
			name:          "correct-GetGatewayDomainList",
			istiodCluster: buildDefaultCluster(),
			testGwCrd:     buildGatewayCrd80443(),
			expectErr:     nil,
		},
		{
			name:          "failed-GetGatewayDomainList",
			istiodCluster: buildDefaultCluster(),
			testGwCrd:     nil,
			expectErr:     errors.New("gateways.networking.istio.io \"gateway-gw\" not found"),
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		mockBlbModel := blbMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:            NewOption(mockDB),
			cceService:     mockCceService,
			instancesModel: mockInstancesModel,
			blbModel:       mockBlbModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			if testInfo.testGwCrd != nil {
				_, _ = fakeClient.Istio().NetworkingV1beta1().Gateways(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.testGwCrd, metav1.CreateOptions{})
			}
			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&testIstiodCluster, meta.HostingMeshType, nil)
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)

			res, err := service.GetGatewayDomainList(mockCtx, &meta.CsmMeshRequestParams{
				PageNo: 1, PageSize: 10, Order: "ASC", Protocol: ""})

			if testInfo.expectErr == nil {
				assert.Nil(t, err)
				assert.Equal(t, int64(2), res.TotalCount)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func buildTestGwSvcNoPorts() *v1.Service {
	return &v1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Labels:      map[string]string{"app": "istio-ingressgateway"},
			Annotations: map[string]string{constants.CceLoadBalancerID: "lb-123"},
			Name:        constants.IngressGatewayResourceName,
			Namespace:   testIstiodCluster.IstioInstallNamespace,
		},
	}
}

func buildTestGwSvc80() *v1.Service {
	return &v1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Labels:      map[string]string{"app": "istio-ingressgateway"},
			Annotations: map[string]string{constants.CceLoadBalancerID: "lb-123"},
			Name:        constants.IngressGatewayResourceName,
			Namespace:   testIstiodCluster.IstioInstallNamespace,
		},
		Spec: v1.ServiceSpec{
			Ports: []v1.ServicePort{
				{
					Name:     "http2",
					Port:     80,
					Protocol: v1.ProtocolTCP,
					TargetPort: intstr.IntOrString{
						Type:   intstr.Int,
						IntVal: 8080,
						StrVal: "0",
					},
				},
			},
		},
	}
}

func buildTestGwSvc443() *v1.Service {
	return &v1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Labels:      map[string]string{"app": "istio-ingressgateway"},
			Annotations: map[string]string{constants.CceLoadBalancerID: "lb-123"},
			Name:        constants.IngressGatewayResourceName,
			Namespace:   testIstiodCluster.IstioInstallNamespace,
		},
		Spec: v1.ServiceSpec{
			Ports: []v1.ServicePort{
				{
					Name:     "https",
					Port:     443,
					Protocol: "TCP",
					TargetPort: intstr.IntOrString{
						Type:   intstr.Int,
						IntVal: 8443,
						StrVal: "0",
					},
				},
			},
		},
	}
}

func buildTestGwSvc80443() *v1.Service {
	return &v1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Labels:      map[string]string{"app": "istio-ingressgateway"},
			Annotations: map[string]string{constants.CceLoadBalancerID: "lb-123"},
			Name:        constants.IngressGatewayResourceName,
			Namespace:   testIstiodCluster.IstioInstallNamespace,
		},
		Spec: v1.ServiceSpec{
			Ports: []v1.ServicePort{
				{
					Name:     "http2",
					Port:     80,
					Protocol: v1.ProtocolTCP,
					TargetPort: intstr.IntOrString{
						Type:   intstr.Int,
						IntVal: 8080,
						StrVal: "0",
					},
				},
				{
					Name:     "https",
					Port:     443,
					Protocol: "TCP",
					TargetPort: intstr.IntOrString{
						Type:   intstr.Int,
						IntVal: 8443,
						StrVal: "0",
					},
				},
			},
		},
	}
}

func buildTestSecret() *v1.Secret {
	return &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "cert-0n3pjjjue21g-helloworld",
			Namespace: testIstiodCluster.IstioInstallNamespace,
		},
	}
}

func buildDomainConf80(preNumber int32) *meta.DomainConf {
	return &meta.DomainConf{
		Domains: []string{"*"},
		Port: &meta.DomainPort{
			Name:      "http",
			Number:    80,
			Protocol:  "HTTP",
			PreNumber: preNumber,
		},
	}
}

func buildDomainConf443(forceHTTPS, hasCert, changeCert bool, preNumber int32) *meta.DomainConf {
	testDomainConf := &meta.DomainConf{
		Domains: []string{"*"},
		Port: &meta.DomainPort{
			Name:      "https",
			Number:    443,
			Protocol:  "HTTPS",
			PreNumber: preNumber,
		},
		IsForceHTTPS: forceHTTPS,
	}
	if hasCert {
		testDomainConf.Cert = &meta.DomainCert{
			ID:   "cert-0n3pjjjue21g",
			Name: "helloworld",
		}
	}
	if changeCert {
		testDomainConf.Cert = &meta.DomainCert{
			ID:   "cert-0n3pjjjue2xx",
			Name: "helloworld",
		}
	}
	return testDomainConf
}

func buildGatewayCrdNoPorts() *netv1beta1.Gateway {
	return &netv1beta1.Gateway{
		ObjectMeta: metav1.ObjectMeta{
			Name:      constants.GwCrdName,
			Namespace: testIstiodCluster.IstioInstallNamespace,
		},
	}
}

func buildGatewayCrd80() *netv1beta1.Gateway {
	return &netv1beta1.Gateway{
		ObjectMeta: metav1.ObjectMeta{
			Name:      constants.GwCrdName,
			Namespace: testIstiodCluster.IstioInstallNamespace,
		},
		Spec: v1beta1.Gateway{
			Servers: []*v1beta1.Server{
				{
					Hosts: []string{"*"},
					Port: &v1beta1.Port{
						Name:     "http_1672317078",
						Number:   8080,
						Protocol: "HTTP",
					},
				},
			},
		},
	}
}

func TestAddGatewayDomain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name           string
		istiodCluster  *meta.Cluster
		testDomainConf *meta.DomainConf
		testGwSvc      *v1.Service
		testGwCrd      *netv1beta1.Gateway
		addCount       int
		updateBlbRs    bool
		isDomainExist  bool
		expectErr      error
	}{
		{
			name:           "failed-NilGatewayList",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvcNoPorts(),
			testDomainConf: buildDomainConf80(80),
			testGwCrd:      nil,
			addCount:       1,
			updateBlbRs:    false,
			expectErr:      errors.New("gateways.networking.istio.io \"gateway-gw\" not found"),
		},
		{
			name:           "failed-GatewayToBeAddedIsAlreadyExist",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80(),
			testDomainConf: buildDomainConf80(80),
			testGwCrd:      buildGatewayCrd80(),
			addCount:       1,
			updateBlbRs:    false,
			expectErr:      csmErr.NewResourceConflictException("the host under the specific protocol is already exist"),
		},
		{
			name:           "correct-AddGateway-HTTP",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvcNoPorts(),
			testDomainConf: buildDomainConf80(80),
			testGwCrd:      buildGatewayCrdNoPorts(),
			addCount:       1,
			updateBlbRs:    true,
			expectErr:      nil,
		},
		{
			name:           "correct-AddGateway-HTTPS-NotForceHTTPS",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvcNoPorts(),
			testDomainConf: buildDomainConf443(false, true, false, 443),
			testGwCrd:      buildGatewayCrdNoPorts(),
			addCount:       1,
			updateBlbRs:    true,
			expectErr:      nil,
		},
		{
			name:           "correct-AddGateway-HTTPS-ForceHTTPS",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvcNoPorts(),
			testDomainConf: buildDomainConf443(true, true, false, 443),
			testGwCrd:      buildGatewayCrdNoPorts(),
			updateBlbRs:    true,
			addCount:       2,
			expectErr:      nil,
		},
		{
			name:           "correct-AddGateway-HTTPS-ForceHTTPS-Has80",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80(),
			testDomainConf: buildDomainConf443(true, true, false, 443),
			testGwCrd:      buildGatewayCrd80(),
			addCount:       1,
			updateBlbRs:    true,
			expectErr:      nil,
		},
		{
			name:           "correct-AddGateway-HTTPS-ForceHTTPS-Has80443",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf443(true, false, true, 443),
			testGwCrd:      buildGatewayCrd80443(),
			addCount:       0,
			updateBlbRs:    false,
			isDomainExist:  true,
			expectErr:      csmErr.NewResourceConflictException("the host under the specific protocol is already exist"),
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		mockBlbModel := blbMock.NewMockServiceInterface(ctrl)
		mockCertModel := certMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:            NewOption(mockDB),
			cceService:     mockCceService,
			instancesModel: mockInstancesModel,
			blbModel:       mockBlbModel,
			certModel:      mockCertModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			if testInfo.testGwCrd != nil {
				_, _ = fakeClient.Istio().NetworkingV1beta1().Gateways(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.testGwCrd, metav1.CreateOptions{})
			}
			if testInfo.testGwSvc != nil {
				_, _ = fakeClient.Kube().CoreV1().Services(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.testGwSvc, metav1.CreateOptions{})
			}

			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&testIstiodCluster, meta.HostingMeshType, nil)
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			if testInfo.updateBlbRs {
				mockBlbModel.EXPECT().CreateAppTCPListener(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(testInfo.addCount)
			}
			if testInfo.testDomainConf.Port.Protocol == "HTTPS" && testInfo.expectErr == nil && !testInfo.isDomainExist {
				mockCertModel.EXPECT().GenerateTlsSecret(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return("cert-1", nil)
			}

			err := service.AddGatewayDomain(mockCtx, testInfo.testDomainConf)

			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, testInfo.expectErr.Error(), err.Error())
			}
		})
	}
}

func TestDeleteGatewayDomain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name           string
		istiodCluster  *meta.Cluster
		testDomainConf *meta.DomainConf
		testGwSvc      *v1.Service
		testGwCrd      *netv1beta1.Gateway
		testSecret     *v1.Secret
		getGwErr       error
		expectErr      error
	}{
		{
			name:           "correct-DeleteServer-HTTP",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf80(80),
			testGwCrd:      buildGatewayCrd80443(),
			testSecret:     nil,
			expectErr:      nil,
		},
		{
			name:           "correct-DeleteServer-HTTPS",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf443(true, true, false, 443),
			testGwCrd:      buildGatewayCrd80443(),
			testSecret:     buildTestSecret(),
			expectErr:      nil,
		},
		{
			name:           "correct-DeleteServer-HTTPS",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf443(false, true, false, 443),
			testGwCrd:      buildGatewayCrd80443(),
			testSecret:     buildTestSecret(),
			expectErr:      nil,
		},
		{
			name:           "correct-DeleteServer-HTTPS-WithoutSecret",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf443(false, true, false, 80),
			testGwCrd:      buildGatewayCrd80443(),
			testSecret:     nil,
			expectErr:      nil,
		},
		{
			name:           "failed-DeleteServer-HTTPS-NoGwCrd",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf443(false, true, false, 80),
			testGwCrd:      nil,
			testSecret:     buildTestSecret(),
			getGwErr:       errors.New("gateways.networking.istio.io \"gateway-gw\" not found"),
			expectErr:      errors.New("gateways.networking.istio.io \"gateway-gw\" not found"),
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		mockBlbModel := blbMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:            NewOption(mockDB),
			cceService:     mockCceService,
			instancesModel: mockInstancesModel,
			blbModel:       mockBlbModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			if testInfo.testGwCrd != nil {
				_, _ = fakeClient.Istio().NetworkingV1beta1().Gateways(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.testGwCrd, metav1.CreateOptions{})
			}
			if testInfo.testGwSvc != nil {
				_, _ = fakeClient.Kube().CoreV1().Services(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.testGwSvc, metav1.CreateOptions{})
			}
			if testInfo.testSecret != nil {
				_, _ = fakeClient.Kube().CoreV1().Secrets(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.testSecret, metav1.CreateOptions{})
			}

			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&testIstiodCluster, meta.HostingMeshType, nil)
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)

			if testInfo.getGwErr == nil {
				mockBlbModel.EXPECT().GetAllAppIpGroups(mockCtx, gomock.Any(), gomock.Any()).Return(buildIPGroups80443(), nil)
				mockBlbModel.EXPECT().DeleteAllAppListeners(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockBlbModel.EXPECT().DeleteAppIpGroup(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			}

			err := service.DeleteGatewayDomain(mockCtx, testInfo.testDomainConf)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestModifyGatewayDomain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name           string
		istiodCluster  *meta.Cluster
		testDomainConf *meta.DomainConf
		testGwSvc      *v1.Service
		testGwCrd      *netv1beta1.Gateway
		testSecret     *v1.Secret
		testIPGroups   *appblb.DescribeAppIpGroupResult
		needDelete     bool
		needAdd        bool
		certChanged    bool
		expectErr      error
	}{
		{
			name:           "correct-HTTP-To-HTTP",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf80(80),
			testGwCrd:      buildGatewayCrd80443(),
			testSecret:     nil,
			testIPGroups:   buildIPGroups80443(),
			needDelete:     false,
			needAdd:        false,
			certChanged:    false,
			expectErr:      nil,
		},
		{
			name:           "correct-HTTP-To-HTTPS-Delete80",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf443(false, true, false, 80),
			testGwCrd:      buildGatewayCrd80443(),
			testSecret:     nil,
			testIPGroups:   buildIPGroups80443(),
			needDelete:     true,
			needAdd:        false,
			certChanged:    false,
			expectErr:      nil,
		},

		{
			name:           "correct-HTTP-To-HTTPS-NotDelete80",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf443(true, true, false, 80),
			testGwCrd:      buildGatewayCrd80443(),
			testSecret:     nil,
			testIPGroups:   buildIPGroups80443(),
			needDelete:     false,
			needAdd:        false,
			certChanged:    false,
			expectErr:      nil,
		},
		{
			name:           "correct-HTTP-To-HTTPS-Delete80",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf443(false, true, false, 80),
			testGwCrd:      buildDiffHostsGatewayCrd80443(),
			testSecret:     nil,
			testIPGroups:   buildIPGroups80443(),
			needDelete:     true,
			needAdd:        false,
			certChanged:    true,
			expectErr:      nil,
		},
		{
			name:           "correct-HTTP-To-HTTPS-Add443",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80(),
			testDomainConf: buildDomainConf443(true, true, false, 80),
			testGwCrd:      buildGatewayCrd80(),
			testSecret:     nil,
			testIPGroups:   buildIPGroups80(),
			needDelete:     false,
			needAdd:        true,
			certChanged:    true,
			expectErr:      nil,
		},
		{
			name:           "correct-HTTP-To-HTTPS-Add443-Delete80",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80(),
			testDomainConf: buildDomainConf443(false, true, false, 80),
			testGwCrd:      buildGatewayCrd80(),
			testSecret:     nil,
			testIPGroups:   buildIPGroups80(),
			needDelete:     true,
			needAdd:        true,
			certChanged:    true,
			expectErr:      nil,
		},
		{
			name:           "correct-HTTPS-To-HTTP-Delete443",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf80(443),
			testGwCrd:      buildGatewayCrd80443(),
			testSecret:     buildTestSecret(),
			testIPGroups:   buildIPGroups80443(),
			needDelete:     true,
			needAdd:        false,
			certChanged:    false,
			expectErr:      nil,
		},
		{
			name:           "correct-HTTPS-To-HTTP-Delete443-Add80",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc443(),
			testDomainConf: buildDomainConf80(443),
			testGwCrd:      buildGatewayCrd443(),
			testSecret:     buildTestSecret(),
			testIPGroups:   buildIPGroups443(),
			needDelete:     true,
			needAdd:        true,
			certChanged:    false,
			expectErr:      nil,
		},
		{
			name:           "correct-HTTPS-To-HTTPS-ChangeCert",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc443(),
			testDomainConf: buildDomainConf443(false, true, true, 443),
			testGwCrd:      buildGatewayCrd443(),
			testSecret:     buildTestSecret(),
			testIPGroups:   buildIPGroups443(),
			needDelete:     false,
			needAdd:        false,
			certChanged:    true,
			expectErr:      nil,
		},
		{
			name:           "correct-HTTPS-To-HTTPS-ChangeCert-Add80",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc443(),
			testDomainConf: buildDomainConf443(true, true, true, 443),
			testGwCrd:      buildGatewayCrd443(),
			testSecret:     buildTestSecret(),
			testIPGroups:   buildIPGroups443(),
			needDelete:     false,
			needAdd:        true,
			certChanged:    true,
			expectErr:      nil,
		},
		{
			name:           "correct-HTTPS-To-HTTPS-NotChangeCert-NotAdd80",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf443(true, true, false, 443),
			testGwCrd:      buildGatewayCrd80443(),
			testSecret:     buildTestSecret(),
			testIPGroups:   buildIPGroups443(),
			needDelete:     false,
			needAdd:        false,
			certChanged:    false,
			expectErr:      nil,
		},
		{
			name:           "correct-HTTPS-To-HTTPS-NotForceHTTPS",
			istiodCluster:  buildDefaultCluster(),
			testGwSvc:      buildTestGwSvc80443(),
			testDomainConf: buildDomainConf443(false, true, false, 443),
			testGwCrd:      buildGatewayCrd80443(),
			testSecret:     buildTestSecret(),
			testIPGroups:   buildIPGroups443(),
			needDelete:     false,
			needAdd:        false,
			certChanged:    false,
			expectErr:      nil,
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		mockBlbModel := blbMock.NewMockServiceInterface(ctrl)
		mockCertModel := certMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:            NewOption(mockDB),
			cceService:     mockCceService,
			instancesModel: mockInstancesModel,
			blbModel:       mockBlbModel,
			certModel:      mockCertModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			if testInfo.testGwCrd != nil {
				_, _ = fakeClient.Istio().NetworkingV1beta1().Gateways(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.testGwCrd, metav1.CreateOptions{})
			}
			if testInfo.testGwSvc != nil {
				_, _ = fakeClient.Kube().CoreV1().Services(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.testGwSvc, metav1.CreateOptions{})
			}
			if testInfo.testSecret != nil {
				_, _ = fakeClient.Kube().CoreV1().Secrets(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.testSecret, metav1.CreateOptions{})
			}

			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&testIstiodCluster, meta.HostingMeshType, nil)
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)

			if testInfo.needDelete {
				mockBlbModel.EXPECT().GetAllAppIpGroups(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.testIPGroups, nil)
				mockBlbModel.EXPECT().DeleteAllAppListeners(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockBlbModel.EXPECT().DeleteAppIpGroup(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			}
			if testInfo.needAdd {
				mockBlbModel.EXPECT().CreateAppTCPListener(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			}

			if testInfo.testDomainConf.Port.Protocol == constants.ProtocolHTTPS && testInfo.certChanged {
				mockCertModel.EXPECT().GenerateTlsSecret(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return("cert-1", nil)
			}

			err := service.ModifyGatewayDomain(mockCtx, testInfo.testDomainConf)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestModifyGatewayBlsTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		logConf   *meta.LogConf
		clusInfo  *meta.Cluster
		telemetry *v1alpha1.Telemetry
		taskInfo  *meta.Log
		cluErr    error
		initErr   error
		createErr error
		deployErr error
		detailErr error
		pauseErr  error
		updateErr error
		startErr  error
		expectErr error
	}{
		{
			name:      "failed-getIstiodCluster",
			logConf:   buildTestLogInfo(true, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: nil,
			taskInfo:  nil,
			cluErr:    errors.New("failed to get istiod cluster"),
			expectErr: errors.New("failed to get istiod cluster"),
		},
		{
			name:      "failed-initializeDeployer",
			logConf:   buildTestLogInfo(true, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: nil,
			taskInfo:  nil,
			initErr:   errors.New("failed to init deployer"),
			expectErr: errors.New("failed to init deployer"),
		},
		{
			name:      "failed-createBLSTask",
			logConf:   buildTestLogInfo(true, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: nil,
			taskInfo:  nil,
			createErr: errors.New("failed to create bls task"),
			expectErr: errors.New("failed to create bls task"),
		},
		{
			name:      "failed-getBLSTaskDetail",
			logConf:   buildTestLogInfo(true, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: nil,
			taskInfo:  nil,
			createErr: errors.New("failed to get bls task detail"),
			expectErr: errors.New("failed to get bls task detail"),
		},
		{
			name:      "failed-startBLSTask",
			logConf:   buildTestLogInfo(true, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: buildTestTelemetry(),
			taskInfo:  buildTestBlsTaskInfo(false, "a"),
			startErr:  errors.New("failed to start bls task"),
			expectErr: errors.New("failed to start bls task"),
		},
		{
			name:      "failed-pauseBLSTask",
			logConf:   buildTestLogInfo(false, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: buildTestTelemetry(),
			taskInfo:  buildTestBlsTaskInfo(true, "a"),
			pauseErr:  errors.New("failed to pause bls task"),
			expectErr: errors.New("failed to pause bls task"),
		},
		{
			name:      "failed-updateBLSTask",
			logConf:   buildTestLogInfo(true, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: buildTestTelemetry(),
			taskInfo:  buildTestBlsTaskInfo(false, "b"),
			updateErr: errors.New("failed to update bls task"),
			expectErr: errors.New("failed to update bls task"),
		},
		{
			name:      "create-log",
			logConf:   buildTestLogInfo(true, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: nil,
			taskInfo:  nil,
			expectErr: nil,
		},
		{
			name:      "stop-log",
			logConf:   buildTestLogInfo(false, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: buildTestTelemetry(),
			taskInfo:  buildTestBlsTaskInfo(true, "a"),
			expectErr: nil,
		},
		{
			name:      "start-log",
			logConf:   buildTestLogInfo(true, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: buildTestTelemetry(),
			taskInfo:  buildTestBlsTaskInfo(false, "a"),
			expectErr: nil,
		},
		{
			name:      "start-log-and-change-logFile",
			logConf:   buildTestLogInfo(true, "b"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: buildTestTelemetry(),
			taskInfo:  buildTestBlsTaskInfo(false, "a"),
			expectErr: nil,
		},
		{
			name:      "pause-log",
			logConf:   buildTestLogInfo(false, "a"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: buildTestTelemetry(),
			taskInfo:  buildTestBlsTaskInfo(true, "a"),
			expectErr: nil,
		},
		{
			name:      "pause-log-and-change-logFile",
			logConf:   buildTestLogInfo(false, "b"),
			clusInfo:  buildTestClusterInfo(),
			telemetry: buildTestTelemetry(),
			taskInfo:  buildTestBlsTaskInfo(true, "a"),
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		mockGatewayDeployer := gatewayDeployerMock.NewMockServiceInterface(ctrl)
		mockBlsModel := blsMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:             NewOption(mockDB),
			instancesModel:  mockInstancesModel,
			deployerService: mockGatewayDeployer,
			cceService:      mockCceService,
			blsModel:        mockBlsModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")

			fakeClient := kube.NewFakeClient()
			if testInfo.telemetry != nil {
				_, _ = fakeClient.Istio().TelemetryV1alpha1().Telemetries(testIstiodCluster.IstioInstallNamespace).Create(
					context.TODO(), testInfo.telemetry, metav1.CreateOptions{})
			}
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(testInfo.clusInfo, meta.HostingMeshType, testInfo.cluErr)
			if testInfo.cluErr == nil {
				mockGatewayDeployer.EXPECT().Init(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.initErr)
				if testInfo.initErr == nil {
					mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)

					if testInfo.telemetry == nil {
						if testInfo.logConf.Enabled {
							mockBlsModel.EXPECT().CreateHostingBlsTask(mockCtx, gomock.Any(), gomock.Any()).Return("task-1", testInfo.createErr)
							if testInfo.createErr == nil {
								mockGatewayDeployer.EXPECT().DeployTelemetry(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
							}
						}
					} else {
						mockBlsModel.EXPECT().GetHostingBlsTaskDetail(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.taskInfo, testInfo.detailErr)
						if testInfo.detailErr == nil {
							if testInfo.taskInfo.Enabled {
								if !testInfo.logConf.Enabled {
									mockBlsModel.EXPECT().PauseHostingBlsTask(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.pauseErr)
								} else if testInfo.logConf.LogFile != testInfo.taskInfo.LogFile {
									mockBlsModel.EXPECT().UpdateHostingBlsTask(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(testInfo.updateErr)
								}
							} else {
								if testInfo.logConf.Enabled {
									mockBlsModel.EXPECT().StartHostingBlsTask(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.startErr)
									if testInfo.logConf.LogFile != testInfo.taskInfo.LogFile {
										mockBlsModel.EXPECT().UpdateHostingBlsTask(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(testInfo.updateErr)
									}
								}
							}
						}
					}
				}
			}

			_, err := service.ModifyGatewayBlsTask(mockCtx, testInfo.logConf)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestModifyGatewayHPA(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		hpaConf   *meta.HpaConf
		clusInfo  *meta.Cluster
		expectErr error
	}{
		{
			name:      "enable-hpa",
			hpaConf:   buildTestHpaInfo(true, 1, 3),
			clusInfo:  buildTestClusterInfo(),
			expectErr: nil,
		},
		{
			name:      "disable-hpa",
			hpaConf:   buildTestHpaInfo(false, 1, 3),
			clusInfo:  buildTestClusterInfo(),
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockGatewayDeployer := gatewayDeployerMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:             NewOption(mockDB),
			instancesModel:  mockInstancesModel,
			deployerService: mockGatewayDeployer,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")

			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(testInfo.clusInfo, meta.HostingMeshType, nil)
			mockGatewayDeployer.EXPECT().Init(mockCtx, gomock.Any(), gomock.Any()).Return(nil)

			if testInfo.hpaConf.Enabled {
				mockGatewayDeployer.EXPECT().DeployHPA(mockCtx, gomock.Any()).Return(nil)
			} else {
				mockGatewayDeployer.EXPECT().UndeployHPA(mockCtx, gomock.Any()).Return(nil)
			}

			_, err := service.ModifyGatewayHPA(mockCtx, testInfo.hpaConf)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestModifyGatewayTLSAcc(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name       string
		tlsAccConf *meta.TLSAccConf
		clusInfo   *meta.Cluster
		expectErr  error
	}{
		{
			name:       "enable-tlsAcc",
			tlsAccConf: buildTestTLSInfo(true),
			clusInfo:   buildTestClusterInfo(),
			expectErr:  nil,
		},
		{
			name:       "disable-tlsAcc",
			tlsAccConf: buildTestTLSInfo(false),
			clusInfo:   buildTestClusterInfo(),
			expectErr:  nil,
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockGatewayDeployer := gatewayDeployerMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:             NewOption(mockDB),
			instancesModel:  mockInstancesModel,
			deployerService: mockGatewayDeployer,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")

			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(testInfo.clusInfo, meta.HostingMeshType, nil)
			mockGatewayDeployer.EXPECT().Init(mockCtx, gomock.Any(), gomock.Any()).Return(nil)

			if testInfo.tlsAccConf.Enabled {
				mockGatewayDeployer.EXPECT().EnableTLSAcceleration(mockCtx, gomock.Any()).Return(nil)
			} else {
				mockGatewayDeployer.EXPECT().DisableTLSAcceleration(mockCtx, gomock.Any()).Return(nil)
			}

			_, err := service.ModifyGatewayTLSAcceleration(mockCtx, testInfo.tlsAccConf)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestService_ModifyGatewayResourceQuota(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name              string
		want              *meta.ResourceQuota
		gateway           *meta.GatewayModel
		resourceQuotaConf *meta.ResourceQuotaConf
		clusInfo          *meta.Cluster
		wantErr           bool
	}{
		{
			name: "test-modifyGatewayResourceQuota",
			want: &meta.ResourceQuota{
				ResourceQuota: "2C4G",
			},
			gateway:  buildTestGateway(),
			clusInfo: buildTestClusterInfo(),
			resourceQuotaConf: &meta.ResourceQuotaConf{
				InstanceUUID:  "inst-1",
				GatewayUUID:   "gw-id-1",
				ResourceQuota: "2C4G",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockGatewayDeployer := gatewayDeployerMock.NewMockServiceInterface(ctrl)
		mockGatewayModel := gatewayMock.NewMockServiceInterface(ctrl)
		service := &Service{
			opt:             NewOption(mockDB),
			instancesModel:  mockInstancesModel,
			deployerService: mockGatewayDeployer,
			gatewayModel:    mockGatewayModel,
		}

		t.Run(tt.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")

			mockGatewayModel.EXPECT().GetGatewayInfo(mockCtx, gomock.Any(), gomock.Any()).Return(tt.gateway, nil)

			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(tt.clusInfo, meta.HostingMeshType, nil)
			mockGatewayDeployer.EXPECT().Init(mockCtx, gomock.Any(), gomock.Any()).Return(nil)
			mockGatewayDeployer.EXPECT().UpdateResourceQuota(mockCtx, gomock.Any(), gomock.Any()).Return(nil)
			mockGatewayModel.EXPECT().WithTx(gomock.Any()).Return(mockGatewayModel)
			mockGatewayModel.EXPECT().UpdateGateway(mockCtx, gomock.Any(), gomock.Any()).Return(nil)

			got, err := service.ModifyGatewayResourceQuota(mockCtx, tt.resourceQuotaConf)
			if (err != nil) != tt.wantErr {
				t.Errorf("ModifyGatewayResourceQuota() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ModifyGatewayResourceQuota() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_ModifyGatewayIngress(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name              string
		gateway           *meta.GatewayModel
		meshType          meta.MeshType
		ingressParam      *meta.IngressParam
		remoteCluster     *[]meta.Cluster
		resourceQuotaConf *meta.ResourceQuotaConf
		clusInfo          *meta.Cluster
		wantErr           bool
	}{
		{
			name:     "deploy-ModifyGatewayIngress",
			gateway:  buildTestGateway(),
			meshType: meta.HostingMeshType,
			clusInfo: buildTestClusterInfo(),
			ingressParam: &meta.IngressParam{
				Enabled: true,
				CLusterList: []meta.RemoteCluster{
					{
						ClusterID: "clu-1",
						Region:    "bj",
						Enabled:   true,
					},
				},
			},
			remoteCluster: &[]meta.Cluster{
				{
					InstanceUUID:       "ins-1",
					ClusterUUID:        "clu-1",
					Region:             "bj",
					IngressSyncEnabled: csm.Bool(false),
				},
			},
			wantErr: true,
		},
		{
			name: "undeploy-ModifyGatewayIngress",
			gateway: &meta.GatewayModel{
				InstanceUUID:         "inst-1",
				ClusterUUID:          "",
				ClusterName:          "",
				GatewayUUID:          "gw-id-1",
				GatewayName:          "gw-1",
				Namespace:            "ns-1",
				Region:               "bj",
				Replicas:             2,
				ResourceQuota:        "2c4g",
				IstioVersion:         "v1",
				SubnetId:             "sub-1",
				AccountId:            "123",
				RemoteIngressEnabled: csm.Bool(true),
			},
			meshType: meta.HostingMeshType,
			clusInfo: buildTestClusterInfo(),
			ingressParam: &meta.IngressParam{
				Enabled: false,
				CLusterList: []meta.RemoteCluster{
					{
						ClusterID: "clu-1",
						Region:    "bj",
						Enabled:   false,
					},
				},
			},
			remoteCluster: &[]meta.Cluster{
				{
					InstanceUUID:       "ins-1",
					ClusterUUID:        "clu-1",
					Region:             "bj",
					IngressSyncEnabled: csm.Bool(true),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
			mockGatewayDeployer := gatewayDeployerMock.NewMockServiceInterface(ctrl)
			mockGatewayModel := gatewayMock.NewMockServiceInterface(ctrl)
			mockClusterModel := clusterMock.NewMockServiceInterface(ctrl)
			service := &Service{
				opt:             NewOption(mockDB),
				instancesModel:  mockInstancesModel,
				deployerService: mockGatewayDeployer,
				gatewayModel:    mockGatewayModel,
				clusterModel:    mockClusterModel,
			}

			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")
			mockGatewayModel.EXPECT().WithTx(gomock.Any()).Return(mockGatewayModel)
			mockClusterModel.EXPECT().WithTx(gomock.Any()).Return(mockClusterModel)

			mockGatewayModel.EXPECT().GetGatewayInfo(mockCtx, gomock.Any(), gomock.Any()).Return(tt.gateway, nil)

			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(tt.clusInfo, tt.meshType, nil)

			mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(gomock.Any(), gomock.Any()).Return(tt.remoteCluster, nil).AnyTimes()

			mockClusterModel.EXPECT().UpdateCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mockGatewayDeployer.EXPECT().UpdateIngressAccess(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			mockGatewayDeployer.EXPECT().Init(mockCtx, gomock.Any(), gomock.Any()).Return(nil)

			mockGatewayDeployer.EXPECT().DeployIngressController(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mockGatewayDeployer.EXPECT().UnDeployIngressController(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mockGatewayDeployer.EXPECT().UpdateIngressInfoInAnnotation(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			mockGatewayModel.EXPECT().UpdateGateway(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			err := service.ModifyGatewayIngress(mockCtx, "ins-1", "gateway-1", tt.ingressParam)
			if tt.wantErr {
				assert.Nil(t, err)
				return
			} else {
				t.Errorf("ModifyGatewayIngress() error is %s, wantErr nil", err.Error())
			}
		})
	}
}

func TestService_GetGatewayIngress(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name              string
		gateway           *meta.GatewayModel
		meshType          meta.MeshType
		testService       *v1.Service
		testPod           *v1.Pod
		remoteCluster     *[]meta.Cluster
		resourceQuotaConf *meta.ResourceQuotaConf
		clusInfo          *meta.Cluster
		wantErr           bool
	}{
		{
			name:     "GetGatewayIngress",
			gateway:  buildTestGateway(),
			meshType: meta.HostingMeshType,
			clusInfo: buildTestClusterInfo(),
			testService: &v1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name:      constants.ServiceNameHigress,
					Namespace: constants.LogBeatNamespace,
				},
				Spec: v1.ServiceSpec{},
			},
			testPod: &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": "higress-controller",
					},
				},
				Status: v1.PodStatus{
					Phase: v1.PodRunning,
				},
			},

			remoteCluster: &[]meta.Cluster{
				{
					InstanceUUID:       "ins-1",
					ClusterUUID:        "clu-1",
					Region:             "bj",
					IngressSyncEnabled: csm.Bool(false),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
			mockClusterModel := clusterMock.NewMockServiceInterface(ctrl)
			mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
			mockGatewayModel := gatewayMock.NewMockServiceInterface(ctrl)
			service := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstancesModel,
				clusterModel:   mockClusterModel,
				cceService:     mockCceService,
				gatewayModel:   mockGatewayModel,
			}

			mockCtx := csmContext.MockNewCsmContext()
			mockCtx.Set(reg.ContextRegion, "bj")
			tt.clusInfo.IstioInstallNamespace = constants.LogBeatNamespace
			mockInstancesModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(tt.clusInfo, tt.meshType, nil)

			mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(gomock.Any(), gomock.Any()).Return(tt.remoteCluster, nil).AnyTimes()
			mockGatewayModel.EXPECT().GetGatewayInfo(mockCtx, gomock.Any(), gomock.Any()).Return(tt.gateway, nil).AnyTimes()

			fakeClient := kube.NewFakeClient()

			_, _ = fakeClient.Kube().CoreV1().Services(constants.LogBeatNamespace).Create(context.TODO(),
				tt.testService, metav1.CreateOptions{})
			_, _ = fakeClient.Kube().CoreV1().Pods(constants.LogBeatNamespace).Create(context.TODO(),
				tt.testPod, metav1.CreateOptions{})

			mockCceService.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()

			_, err := service.GetGatewayIngress(mockCtx, "ins-1", "gateway-1")
			if tt.wantErr {
				assert.Nil(t, err)
			} else {
				t.Errorf("GetGatewayIngress error is %s, wantErr nil", err.Error())
			}
		})
	}
}
