package gateway

import (
	"github.com/jinzhu/gorm"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

var (
	GwListenerPortList = []uint16{80, 443}
	GwServicePorts     = map[int32]v1.ServicePort{
		80: {
			Name:     "http2",
			Port:     80,
			Protocol: v1.ProtocolTCP,
			TargetPort: intstr.IntOrString{
				Type:   intstr.Int,
				IntVal: 8080,
				StrVal: "0",
			},
		},
		443: {
			Name:     "https",
			Port:     443,
			Protocol: "TCP",
			TargetPort: intstr.IntOrString{
				Type:   intstr.Int,
				IntVal: 8443,
				StrVal: "0",
			},
		},
	}

	CertIDLength = 17 // cert-abcdefghijkl
)

type Option struct {
	DB *dbutil.DB
}

func NewOption(d *gorm.DB) *Option {
	return &Option{
		DB: dbutil.NewDB(d),
	}
}
