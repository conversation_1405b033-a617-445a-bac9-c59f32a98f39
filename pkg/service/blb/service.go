package blb

import (
	"context"
	"fmt"
	"strings"

	"github.com/baidubce/bce-sdk-go/model"
	"github.com/baidubce/bce-sdk-go/services/appblb"
	"github.com/baidubce/bce-sdk-go/services/eip"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/csm/domain"
	blbService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/util"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	modelBlb "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blb"
	modelEip "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/eip"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/gateway"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

type Service struct {
	modelBlb      modelBlb.ServiceInterface
	modelEip      modelEip.ServiceInterface
	modelGateway  gateway.ServiceInterface
	modelInstance instances.ServiceInterface
	cceService    cce.ClientInterface
}

func NewBlbService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		modelBlb:      modelBlb.NewService(modelBlb.NewOption()),
		modelEip:      modelEip.NewService(modelEip.NewOption()),
		modelGateway:  gateway.NewGatewayService(gateway.NewOption(gormDB)),
		modelInstance: instances.NewInstancesService(instances.NewOption(gormDB)),
		cceService:    cce.NewClientService(),
	}
}

func (service *Service) BindBlb(ctx csmContext.CsmContext, bi *domain.BlbInfo) error {
	// 获取 gateway pod ip, 当前只支持 gateway
	// TODO: 后续支持托管网格调用
	// TODO: 添加事务
	gateway, err := service.modelGateway.GetGatewayInfo(ctx, bi.MeshInstanceID, bi.GatewayID)
	if err != nil {
		return err
	}

	// TODO: 这里的实例类型需要注意下
	client, err := service.cceService.NewClient(ctx, gateway.Region, gateway.ClusterUUID, meta.HostingMeshType)
	if err != nil {
		return err
	}

	clientset := client.Kube()

	// 获取 gateway 所有的 pod
	podList, _ := clientset.CoreV1().Pods(gateway.Namespace).List(context.TODO(),
		metav1.ListOptions{
			LabelSelector: constants.IngressGatewayLabelSelector,
		})

	// 获取 gateway service
	svcList, _ := clientset.CoreV1().Services(gateway.Namespace).List(context.TODO(),
		metav1.ListOptions{
			LabelSelector: constants.IngressGatewayLabelSelector,
		})

	// 获取每个 pod ip
	memberList := make([]appblb.AppIpGroupMember, 0)
	for _, item := range podList.Items {
		ip := item.Status.PodIP
		for _, i := range svcList.Items {
			for _, p := range i.Spec.Ports {
				appIpGroupMember := appblb.AppIpGroupMember{
					Ip:     ip,
					Port:   int(p.Port),
					Weight: 100,
				}
				memberList = append(memberList, appIpGroupMember)
			}
		}
	}

	createAppIpGroupArgs := &appblb.CreateAppIpGroupArgs{
		Name:       bi.GatewayID,
		Desc:       "Create By Csm",
		MemberList: memberList,
	}

	blbRegion := ctx.Get(reg.ContextRegion).(string)
	// 为 BLB 创建 IP 组
	_, err = service.modelBlb.CreateAppIpGroup(ctx, bi.BlbID, blbRegion, createAppIpGroupArgs)
	if err != nil {
		return err
	}

	for _, i := range svcList.Items {
		for _, p := range i.Spec.Ports {
			// 为 BLB 设置监听，并将该监听绑定 IP 组
			createAppTCPListenerArgs := &appblb.CreateAppTCPListenerArgs{
				ListenerPort:      uint16(p.Port),
				TcpSessionTimeout: 9000,
			}
			err = service.modelBlb.CreateAppTCPListener(ctx, bi.BlbID, blbRegion, createAppTCPListenerArgs)
			if err != nil {
				return err
			}
		}
	}

	blbDetail, err := service.modelBlb.GetBlb(ctx, bi.BlbID, blbRegion)
	if err != nil {
		return err
	}

	if blbDetail == nil {
		return csmErr.NewResourceNotFoundException(fmt.Sprintf("blb not found. blbId: %s, region: %s",
			bi.BlbID, blbRegion))
	}

	if blbDetail.Tags != nil && func() bool {
		for _, tag := range blbDetail.Tags {
			if strings.EqualFold(tag.TagKey, constants.GatewayTagKey) {
				return true
			}
		}
		return false
	}() {
		return csmErr.NewInvalidParameterInputValueException("gatewayId tag already exists")
	}

	// TODO 为 BLB 创建 Policy

	// 为 BLB 打标签
	return service.modelBlb.BindTag(ctx, bi.BlbID, blbRegion, &model.TagModel{
		TagKey:   constants.GatewayTagKey,
		TagValue: bi.GatewayID,
	})
}

func (service *Service) UnBindBlb(ctx csmContext.CsmContext, bi *domain.BlbInfo, isReleaseBlb, isReleaseEip bool) error {
	blbRegion := ctx.Get(reg.ContextRegion).(string)

	// 先删除标签
	err := service.modelBlb.UnBindTag(ctx, bi.BlbID, blbRegion, &model.TagModel{
		TagKey:   constants.GatewayTagKey,
		TagValue: bi.GatewayID,
	})
	if err != nil {
		return err
	}

	args := &eip.ListEipArgs{
		InstanceType: string(meta.BLBInstanceType),
		InstanceId:   bi.BlbID,
	}

	// 解绑 EIP
	if isReleaseEip {
		// 检查是否绑了 EIP
		isBind, ip, err := service.modelEip.IsEipBinded(ctx, args, blbRegion)
		if err != nil {
			return err
		}
		if !isBind {
			ctx.CsmLogger().Infof("there is no eip binding for blb %s", bi.BlbID)
		} else {
			// 解绑 EIP
			err = service.modelEip.UnbindEip(ctx, ip, blbRegion)
			if err != nil {
				return err
			}
			// 释放 EIP
			err = service.modelEip.ReleaseEip(ctx, ip, blbRegion)
			if err != nil {
				ctx.CsmLogger().Errorf("release eip failed. eip : %s , blb : %s, err : %v", ip, bi.BlbID, err)
			}
		}
	}

	// 解绑 BLB
	if isReleaseBlb {
		return service.modelBlb.ReleaseBlb(ctx, bi.BlbID, blbRegion)
	}

	return nil
}

// ListAvailableBlb 可选的BLB实例列表
func (service *Service) ListAvailableBlb(ctx csmContext.CsmContext, instanceUUID, region string) (*meta.AvailableBlbListResponse, error) {
	lbArgs := &appblb.DescribeLoadBalancersArgs{}
	blbListResult, err := service.modelBlb.GetAllBlb(ctx, region, lbArgs)
	if err != nil {
		return nil, err
	}
	instanceInfo, err := service.modelInstance.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return nil, err
	}

	vpcID := instanceInfo.VpcNetworkId

	var tmpBlbList []meta.BlbDisplay
	lsArgs := &appblb.DescribeAppListenerArgs{}
	for _, lb := range blbListResult.BlbList {
		if lb.VpcId != vpcID || lb.Status != appblb.BLBStatusAvailable || strings.HasPrefix(lb.Name, "CCE/") {
			continue
		}
		res, ipErr := service.modelBlb.GetAllAppListeners(ctx, lb.BlbId, region, lsArgs)
		if ipErr != nil || len(res.ListenerList) != 0 {
			continue
		}
		tmpBlb := meta.BlbDisplay{
			ID:       lb.BlbId,
			Name:     lb.Name,
			PublicIP: lb.PublicIp,
			EipType:  lb.EipRouteType,
		}
		tmpBlbList = append(tmpBlbList, tmpBlb)
	}

	response := &meta.AvailableBlbListResponse{
		TotalCount: int64(len(tmpBlbList)),
		Result:     tmpBlbList,
	}

	return response, nil
}

// CreateBlbService BLB 创建服务发布点
func (service *Service) CreateBlbService(ctx csmContext.CsmContext, args *blbService.CreateBlbServiceArgs,
	region string) (*blbService.CreateBlbServiceResult, error) {
	return service.modelBlb.CreateBlbService(ctx, args, region)
}

// DeleteBlbService BLB 删除服务发布点
func (service *Service) DeleteBlbService(ctx csmContext.CsmContext, args *blbService.DeleteBlbServiceArgs,
	region string) error {
	if len(args.ClientToken) == 0 {
		args.ClientToken = util.GetClientToken()
	}
	return service.modelBlb.DeleteBlbService(ctx, args, region)
}

// GetBlbService BLB 查询服务发布点详情
func (service *Service) GetBlbService(ctx csmContext.CsmContext, args *blbService.GetBlbServiceArgs,
	region string) (*blbService.GetBlbServiceResult, error) {
	if len(args.ClientToken) == 0 {
		args.ClientToken = util.GetClientToken()
	}
	return service.modelBlb.GetBlbService(ctx, args, region)
}
