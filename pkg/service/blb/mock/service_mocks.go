// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	domain "icode.baidu.com/baidu/bce-api/api-logic-csm/internal/csm/domain"
	service "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// BindBlb mocks base method.
func (m *MockServiceInterface) BindBlb(ctx context.CsmContext, bi *domain.BlbInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindBlb", ctx, bi)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindBlb indicates an expected call of BindBlb.
func (mr *MockServiceInterfaceMockRecorder) BindBlb(ctx, bi interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindBlb", reflect.TypeOf((*MockServiceInterface)(nil).BindBlb), ctx, bi)
}

// CreateBlbService mocks base method.
func (m *MockServiceInterface) CreateBlbService(ctx context.CsmContext, args *service.CreateBlbServiceArgs, region string) (*service.CreateBlbServiceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBlbService", ctx, args, region)
	ret0, _ := ret[0].(*service.CreateBlbServiceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBlbService indicates an expected call of CreateBlbService.
func (mr *MockServiceInterfaceMockRecorder) CreateBlbService(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBlbService", reflect.TypeOf((*MockServiceInterface)(nil).CreateBlbService), ctx, args, region)
}

// DeleteBlbService mocks base method.
func (m *MockServiceInterface) DeleteBlbService(ctx context.CsmContext, args *service.DeleteBlbServiceArgs, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBlbService", ctx, args, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBlbService indicates an expected call of DeleteBlbService.
func (mr *MockServiceInterfaceMockRecorder) DeleteBlbService(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBlbService", reflect.TypeOf((*MockServiceInterface)(nil).DeleteBlbService), ctx, args, region)
}

// GetBlbService mocks base method.
func (m *MockServiceInterface) GetBlbService(ctx context.CsmContext, args *service.GetBlbServiceArgs, region string) (*service.GetBlbServiceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlbService", ctx, args, region)
	ret0, _ := ret[0].(*service.GetBlbServiceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlbService indicates an expected call of GetBlbService.
func (mr *MockServiceInterfaceMockRecorder) GetBlbService(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlbService", reflect.TypeOf((*MockServiceInterface)(nil).GetBlbService), ctx, args, region)
}

// ListAvailableBlb mocks base method.
func (m *MockServiceInterface) ListAvailableBlb(ctx context.CsmContext, instanceUUID, region string) (*meta.AvailableBlbListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAvailableBlb", ctx, instanceUUID, region)
	ret0, _ := ret[0].(*meta.AvailableBlbListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAvailableBlb indicates an expected call of ListAvailableBlb.
func (mr *MockServiceInterfaceMockRecorder) ListAvailableBlb(ctx, instanceUUID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAvailableBlb", reflect.TypeOf((*MockServiceInterface)(nil).ListAvailableBlb), ctx, instanceUUID, region)
}

// UnBindBlb mocks base method.
func (m *MockServiceInterface) UnBindBlb(ctx context.CsmContext, bi *domain.BlbInfo, isReleaseBlb, isReleaseEip bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnBindBlb", ctx, bi, isReleaseBlb, isReleaseEip)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnBindBlb indicates an expected call of UnBindBlb.
func (mr *MockServiceInterfaceMockRecorder) UnBindBlb(ctx, bi, isReleaseBlb, isReleaseEip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnBindBlb", reflect.TypeOf((*MockServiceInterface)(nil).UnBindBlb), ctx, bi, isReleaseBlb, isReleaseEip)
}
