package blb

import (
	"errors"
	"testing"

	"github.com/baidubce/bce-sdk-go/model"
	"github.com/baidubce/bce-sdk-go/services/appblb"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"golang.org/x/net/context"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/csm/domain"
	blbMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blb/mock"
	eipMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/eip/mock"
	gatewayMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/gateway/mock"
	instanceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	cceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

var (
	pod1 = v1.Pod{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{"app": "istio-ingressgateway"},
		},
		Spec: v1.PodSpec{},
		Status: v1.PodStatus{
			PodIP: "***********",
		},
	}

	svc1 = v1.Service{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{"app": "istio-ingressgateway"},
		},
		Spec: v1.ServiceSpec{
			Ports: []v1.ServicePort{{
				Name:     "tcp-1",
				Protocol: "TCP",
				Port:     8080,
			}},
		},
		Status: v1.ServiceStatus{},
	}

	fakeClient = kube.NewFakeClient()
)

func TestBindBlb(t *testing.T) {
	type MockResults struct {
		gateway    *meta.GatewayModel
		gatewayErr error
		blbDetail  *appblb.DescribeLoadBalancerDetailResult
	}

	type Args struct {
		bi *domain.BlbInfo
	}

	testCases := []struct {
		name           string
		args           *Args
		expected       error
		expectedErrMsg string
		mr             *MockResults
	}{
		{
			name: "",
			args: &Args{
				bi: &domain.BlbInfo{},
			},
			expected:       nil,
			expectedErrMsg: "",
			mr: &MockResults{
				gateway: &meta.GatewayModel{
					Region:      "bj",
					Namespace:   "namespace-1",
					ClusterUUID: "cluster-id-1",
				},
				gatewayErr: nil,
				blbDetail: &appblb.DescribeLoadBalancerDetailResult{
					BlbId: "blb-id-1",
					Tags:  []model.TagModel{{
						// TagKey:   constants.GatewayTagKey,
						// TagValue: "gateway-id-1",
					}},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx, _ := csmContext.NewCsmContextMock()
			ctx.Set(reg.ContextRegion, "bj")

			ctrl := gomock.NewController(t)
			mc := cceMock.NewMockClientInterface(ctrl)
			mc.EXPECT().
				NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(fakeClient, nil)

			_, _ = fakeClient.Kube().CoreV1().Pods(tc.mr.gateway.Namespace).Create(context.TODO(), &pod1, metav1.CreateOptions{})
			_, _ = fakeClient.Kube().CoreV1().Services(tc.mr.gateway.Namespace).Create(context.TODO(), &svc1, metav1.CreateOptions{})

			mg := gatewayMock.NewMockServiceInterface(ctrl)
			mg.EXPECT().
				GetGatewayInfo(ctx, tc.args.bi.MeshInstanceID, tc.args.bi.GatewayID).
				Return(tc.mr.gateway, tc.mr.gatewayErr)

			mb := blbMock.NewMockServiceInterface(ctrl)
			mb.EXPECT().
				CreateAppIpGroup(ctx, tc.args.bi.BlbID, gomock.Any(), gomock.Any()).
				Return(nil, nil)
			mb.EXPECT().
				CreateAppTCPListener(ctx, tc.args.bi.BlbID, gomock.Any(), gomock.Any()).
				Return(nil)
			mb.EXPECT().
				GetBlb(ctx, tc.args.bi.BlbID, gomock.Any()).
				Return(tc.mr.blbDetail, nil)
			mb.EXPECT().
				BindTag(ctx, tc.args.bi.BlbID, gomock.Any(), gomock.Any()).
				Return(nil).
				AnyTimes()

			service := &Service{
				modelBlb:     mb,
				modelGateway: mg,
				cceService:   mc,
			}

			err := service.BindBlb(ctx, tc.args.bi)
			if err != nil {
				assert.NotEmpty(t, tc.expectedErrMsg)
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestUnBindBlb(t *testing.T) {
	type MockResults struct {
		isBind        bool
		ip            string
		isEipBindErr  error
		unbindEipErr  error
		releaseEipErr error
		releaseBlbErr error
	}

	type Args struct {
		bi           *domain.BlbInfo
		isReleaseBlb bool
		isReleaseEip bool
	}

	testCases := []struct {
		name           string
		args           *Args
		expected       error
		expectedErrMsg string
		mr             *MockResults
	}{
		{
			name: "unbind success",
			args: &Args{
				bi:           &domain.BlbInfo{},
				isReleaseBlb: false,
				isReleaseEip: false,
			},
			expected:       nil,
			expectedErrMsg: "",
			mr:             &MockResults{},
		},
		{
			name: "release eip",
			args: &Args{
				bi:           &domain.BlbInfo{},
				isReleaseBlb: false,
				isReleaseEip: true,
			},
			expected:       nil,
			expectedErrMsg: "",
			mr: &MockResults{
				isBind:        true,
				ip:            "*******",
				isEipBindErr:  nil,
				unbindEipErr:  nil,
				releaseEipErr: nil,
				releaseBlbErr: nil,
			},
		},
		{
			name: "release blb",
			args: &Args{
				bi:           &domain.BlbInfo{},
				isReleaseBlb: true,
				isReleaseEip: false,
			},
			expected:       nil,
			expectedErrMsg: "",
			mr: &MockResults{
				isBind:        false,
				ip:            "",
				isEipBindErr:  nil,
				unbindEipErr:  nil,
				releaseEipErr: nil,
				releaseBlbErr: nil,
			},
		},
		{
			name: "release eip but unbind",
			args: &Args{
				bi:           &domain.BlbInfo{},
				isReleaseBlb: true,
				isReleaseEip: true,
			},
			expected:       nil,
			expectedErrMsg: "",
			mr: &MockResults{
				isBind:        false,
				ip:            "",
				isEipBindErr:  nil,
				unbindEipErr:  nil,
				releaseEipErr: nil,
				releaseBlbErr: nil,
			},
		},
		{
			name: "release eip but unbind failed",
			args: &Args{
				bi:           &domain.BlbInfo{},
				isReleaseBlb: true,
				isReleaseEip: true,
			},
			expected:       nil,
			expectedErrMsg: "unbind eip failed",
			mr: &MockResults{
				isBind:        true,
				ip:            "*******",
				isEipBindErr:  nil,
				unbindEipErr:  errors.New("unbind eip failed"),
				releaseEipErr: nil,
				releaseBlbErr: nil,
			},
		},
		{
			name: "release eip but release failed",
			args: &Args{
				bi:           &domain.BlbInfo{},
				isReleaseBlb: true,
				isReleaseEip: true,
			},
			expected:       nil,
			expectedErrMsg: "",
			mr: &MockResults{
				isBind:        true,
				ip:            "*******",
				isEipBindErr:  nil,
				unbindEipErr:  nil,
				releaseEipErr: errors.New("release eip failed"),
				releaseBlbErr: nil,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx, _ := csmContext.NewCsmContextMock()
			ctx.Set(reg.ContextRegion, "bj")

			ctrl := gomock.NewController(t)
			mb := blbMock.NewMockServiceInterface(ctrl)
			mb.EXPECT().
				ReleaseBlb(ctx, tc.args.bi.BlbID, gomock.Any()).
				Return(nil).
				AnyTimes()
			mb.EXPECT().
				UnBindTag(ctx, tc.args.bi.BlbID, gomock.Any(), gomock.Any()).
				Return(nil).
				AnyTimes()

			me := eipMock.NewMockServiceInterface(ctrl)
			me.EXPECT().
				IsEipBinded(ctx, gomock.Any(), gomock.Any()).
				Return(tc.mr.isBind, tc.mr.ip, tc.mr.isEipBindErr).
				AnyTimes()
			me.EXPECT().
				UnbindEip(ctx, gomock.Any(), gomock.Any()).
				Return(tc.mr.unbindEipErr).
				AnyTimes()
			me.EXPECT().
				ReleaseEip(ctx, gomock.Any(), gomock.Any()).
				Return(tc.mr.releaseEipErr).
				AnyTimes()

			service := &Service{
				modelBlb: mb,
				modelEip: me,
			}

			err := service.UnBindBlb(ctx, tc.args.bi, tc.args.isReleaseBlb, tc.args.isReleaseEip)
			if err != nil {
				assert.NotEmpty(t, tc.expectedErrMsg)
				assert.Containsf(t, err.Error(), tc.expectedErrMsg,
					"expected error: %v, got %v", tc.expectedErrMsg, err.Error())
			}
		})
	}
}

func TestListAvailableBlb(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectRes *meta.AvailableBlbListResponse
		expectVpc string
		expectErr error
	}{
		{
			name: "correct-ListAvailableBlb",
			expectRes: &meta.AvailableBlbListResponse{
				TotalCount: 1,
				Result: []meta.BlbDisplay{
					{
						ID:              "lb-123",
						Name:            "abc",
						PublicIP:        "ip-123",
						EipType:         "xyz",
						EipAllocationID: "",
					},
				},
			},
			expectVpc: "vpc-123",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockInstancesModel := instanceMock.NewMockServiceInterface(ctrl)
		mockBlbModel := blbMock.NewMockServiceInterface(ctrl)
		service := &Service{
			modelInstance: mockInstancesModel,
			modelBlb:      mockBlbModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := csmContext.MockNewCsmContext()
			mockBlbModel.EXPECT().GetAllBlb(mockCtx, gomock.Any(), gomock.Any()).Return(&appblb.DescribeLoadBalancersResult{
				BlbList: []appblb.AppBLBModel{
					{
						BlbId:        "lb-123",
						Name:         "abc",
						PublicIp:     "ip-123",
						EipRouteType: "xyz",
						Status:       appblb.BLBStatusAvailable,
						VpcId:        testInfo.expectVpc,
					},
				},
			}, nil)
			mockBlbModel.EXPECT().GetAllAppListeners(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&appblb.DescribeAppAllListenersResult{
				ListenerList: []appblb.AppAllListenerModel{},
			}, nil)
			mockInstancesModel.EXPECT().GetInstanceByInstanceUUID(mockCtx, gomock.Any()).Return(&meta.Instances{
				VpcNetworkId: testInfo.expectVpc,
			}, nil)

			res, err := service.ListAvailableBlb(mockCtx, "abc", "bj")

			assert.Equal(t, testInfo.expectRes, res)

			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}
