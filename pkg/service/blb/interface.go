package blb

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/internal/csm/domain"
	blbService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	BindBlb(ctx csmContext.CsmContext, bi *domain.BlbInfo) error

	UnBindBlb(ctx csmContext.CsmContext, bi *domain.BlbInfo, isReleaseBlb, isReleaseEip bool) error

	ListAvailableBlb(ctx csmContext.CsmContext, instanceUUID, region string) (*meta.AvailableBlbListResponse, error)

	// CreateBlbService 创建服务发布点
	CreateBlbService(ctx csmContext.CsmContext, args *blbService.CreateBlbServiceArgs, region string) (*blbService.CreateBlbServiceResult, error)

	// DeleteBlbService 删除服务发布点
	DeleteBlbService(ctx csmContext.CsmContext, args *blbService.DeleteBlbServiceArgs, region string) error

	// GetBlbService 查询服务发布点详情
	GetBlbService(ctx csmContext.CsmContext, args *blbService.GetBlbServiceArgs,
		region string) (*blbService.GetBlbServiceResult, error)
}
