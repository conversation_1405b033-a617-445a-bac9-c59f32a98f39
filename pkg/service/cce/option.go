package cce

import "github.com/spf13/viper"

const (
	cceEndpoint    = "cloud.cce.endpoint"
	cceAccessKey   = "cloud.hosting.access_key"
	cceSecretKey   = "cloud.hosting.secret_key"
	BceIamProfile  = "cloud.iamProfile"
	BceServiceRole = "bceServiceRole"
)

// Option CCE 配置
type Option struct {
	// cce endpoint
	CceEndpoint string
	// AccessKey 资源账号托管集群使用的 ak
	AccessKey string
	// SecretKey 资源账号托管集群使用的 sk
	SecretKey string

	RoleName      string
	ClientProfile string
}

// NewOption 初始化 cce 配置
func NewOption() *Option {
	return &Option{
		CceEndpoint:   viper.GetString(cceEndpoint),
		AccessKey:     viper.GetString(cceAccessKey),
		SecretKey:     viper.GetString(cceSecretKey),
		RoleName:      viper.GetString(BceServiceRole),
		ClientProfile: viper.GetString(BceIamProfile),
	}
}
