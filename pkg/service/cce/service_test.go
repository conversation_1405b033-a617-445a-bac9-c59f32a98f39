package cce

import (
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	cce_v1 "github.com/baidubce/bce-sdk-go/services/cce"
	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/cce"
	model_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csm_context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	service_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
)

var (
	region         = "bj"
	clusterID      = "test-123456"
	clusterName    = "istio-test01"
	clusterType    = "normal"
	version        = "1.13.2"
	vpcId          = "aaaaaaaa"
	vpcName        = "test-sss"
	k8sVersion     = "1.20.0"
	mockContext, _ = csm_context.NewCsmContextMock()
	fakeCceClient  *cce.Client
)

func init() {
	fakeCceClient = &cce.Client{
		BceClient:   &bce_sdk.BceClient{},
		CceClientV2: &cce_v2.Client{},
		CceClientV1: &cce_v1.Client{},
	}
}

func TestNewClient(t *testing.T) {
	tests := []struct {
		name      string
		region    string
		clusterID string
		local     bool
		meshType  model_meta.MeshType
		err       error
	}{
		{
			name:      "NewClient-standalone",
			region:    region,
			clusterID: clusterID,
			local:     true,
			meshType:  model_meta.StandaloneMeshType,
			err:       fmt.Errorf("illegal base64"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdata.MockLocalEnv(tt.local)
			testdata.MockLocalAKSK()
			viper.Set(cceEndpoint, testdata.CceEndpoint)
			defer viper.Reset()
			if model_meta.StandaloneMeshType == tt.meshType {
				patches := gomonkey.ApplyFunc(cce.GetClient,
					func(_ csm_context.CsmContext, _ string) (*cce.Client, error) {
						return fakeCceClient, nil
					})
				patches.ApplyPrivateMethod(fakeCceClient, "GetAdminKubeConfig",
					func(_ *cce_v2.GetKubeConfigArgs) (*cce_v2.GetKubeConfigResponse, error) {
						return testdata.MockBuildGetKubeConfigResponse(), nil
					})
				defer patches.Reset()
			}
			service := NewClientService()
			_, err := service.NewClient(mockContext, tt.region, tt.clusterID, tt.meshType)
			assert.Contains(t, err.Error(), tt.err.Error())
		})
	}
}

func TestGetCCEClustersV2(t *testing.T) {
	tests := []struct {
		name      string
		region    string
		clusterID string
		local     bool
		meshType  model_meta.MeshType
		err       error
	}{
		{
			name:      "GetCCEClustersV2-standalone",
			region:    region,
			clusterID: clusterID,
			local:     true,
			meshType:  model_meta.StandaloneMeshType,
			err:       nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdata.MockLocalEnv(tt.local)
			testdata.MockLocalAKSK()
			viper.Set(cceEndpoint, testdata.CceEndpoint)
			defer viper.Reset()
			if model_meta.StandaloneMeshType == tt.meshType {
				patches := gomonkey.ApplyFunc(cce.GetClient,
					func(_ csm_context.CsmContext, _ string) (*cce.Client, error) {
						return fakeCceClient, nil
					})
				patches.ApplyPrivateMethod(fakeCceClient.CceClientV2, "ListClusters",
					func(_ *cce_v2.ListClustersArgs) (*cce_v2.ListClustersResponse, error) {
						return nil, nil
					})
				defer patches.Reset()
			}
			service := NewClientService()
			_, err := service.GetCCEClustersV2(mockContext, tt.region, "", "")
			if err != nil {
				assert.Contains(t, err.Error(), tt.err.Error())
			}
		})
	}
}

func TestGetCCEClusterV2(t *testing.T) {
	tests := []struct {
		name      string
		region    string
		clusterID string
		local     bool
		meshType  model_meta.MeshType
		err       error
	}{
		{
			name:      "GetCCEClusterV2-standalone",
			region:    region,
			clusterID: clusterID,
			local:     true,
			meshType:  model_meta.StandaloneMeshType,
			err:       nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdata.MockLocalEnv(tt.local)
			testdata.MockLocalAKSK()
			viper.Set(cceEndpoint, testdata.CceEndpoint)
			defer viper.Reset()
			if model_meta.StandaloneMeshType == tt.meshType {
				patches := gomonkey.ApplyFunc(cce.GetClient,
					func(_ csm_context.CsmContext, _ string) (*cce.Client, error) {
						return fakeCceClient, nil
					})
				patches.ApplyPrivateMethod(fakeCceClient.CceClientV2, "GetCluster",
					func(_ string) (*cce_v2.GetClusterResponse, error) {
						return nil, nil
					})
				defer patches.Reset()
			}
			service := NewClientService()
			_, err := service.GetCCEClusterV2(mockContext, tt.region, clusterID)
			if err != nil {
				assert.Contains(t, err.Error(), tt.err.Error())
			}
		})
	}
}

func buildExpectClusters() []service_meta.MeshCluster {
	meshClusters := make([]service_meta.MeshCluster, 0)
	meshCluster := service_meta.MeshCluster{
		ClusterUuid: clusterID,
		ClusterName: clusterName,
		ClusterType: clusterType,
		Region:      region,
		Version:     k8sVersion,
	}
	meshClusters = append(meshClusters, meshCluster)
	return meshClusters
}

func buildMockListClustersResponse() *cce_v2.ListClustersResponse {
	return &cce_v2.ListClustersResponse{
		ClusterPage: &cce_v2.ClusterPage{
			PageNo:     1,
			PageSize:   100,
			TotalCount: 1,
			ClusterList: []*cce_v2.Cluster{
				{
					Spec: &cce_v2.ClusterSpec{
						ClusterID:   clusterID,
						ClusterName: clusterName,
						ClusterType: types.ClusterType(clusterType),
						K8SVersion:  types.K8SVersion(k8sVersion),
					},
				},
			},
		},
		RequestID: "xxxx",
	}
}

func TestGetCCEClusterList(t *testing.T) {
	tests := []struct {
		name      string
		region    string
		clusterID string
		local     bool
		meshType  model_meta.MeshType
		err       error
	}{
		{
			name:      "GetCCEClusterList-standalone",
			region:    region,
			clusterID: clusterID,
			local:     true,
			meshType:  model_meta.StandaloneMeshType,
			err:       nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdata.MockLocalEnv(tt.local)
			testdata.MockLocalAKSK()
			viper.Set(cceEndpoint, testdata.CceEndpoint)
			defer viper.Reset()
			if model_meta.StandaloneMeshType == tt.meshType {
				patches := gomonkey.ApplyFunc(cce.GetClient,
					func(_ csm_context.CsmContext, _ string) (*cce.Client, error) {
						return fakeCceClient, nil
					})
				patches.ApplyPrivateMethod(fakeCceClient.CceClientV2, "ListClusters",
					func(_ *cce_v2.ListClustersArgs) (*cce_v2.ListClustersResponse, error) {
						return buildMockListClustersResponse(), nil
					})
				defer patches.Reset()
			}
			service := NewClientService()
			result, err := service.GetCCEClusterList(mockContext, tt.region)
			if err != nil {
				assert.Contains(t, err.Error(), tt.err.Error())
			}
			expect := buildExpectClusters()
			assert.Equal(t, expect, result)
		})
	}
}

func TestGetCCECluster(t *testing.T) {
	tests := []struct {
		name      string
		region    string
		clusterID string
		local     bool
		meshType  model_meta.MeshType
		err       error
	}{
		{
			name:      "GetCCECluster-standalone",
			region:    region,
			clusterID: clusterID,
			local:     true,
			meshType:  model_meta.StandaloneMeshType,
			err:       nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdata.MockLocalEnv(tt.local)
			testdata.MockLocalAKSK()
			viper.Set(cceEndpoint, testdata.CceEndpoint)
			defer viper.Reset()
			if model_meta.StandaloneMeshType == tt.meshType {
				patches := gomonkey.ApplyFunc(cce.GetClient,
					func(_ csm_context.CsmContext, _ string) (*cce.Client, error) {
						return fakeCceClient, nil
					})
				patches.ApplyPrivateMethod(fakeCceClient.CceClientV1, "GetCluster",
					func(_ string) (*cce_v1.GetClusterResult, error) {
						return nil, nil
					})
				defer patches.Reset()
			}
			service := NewClientService()
			_, err := service.GetCCECluster(mockContext, tt.region, tt.clusterID)
			if err != nil {
				assert.Contains(t, err.Error(), tt.err.Error())
			}
		})
	}
}

func TestService_NewSugarClient(t *testing.T) {
	tests := []struct {
		name      string
		region    string
		clusterID string
		local     bool
		accountID string
		meshType  model_meta.MeshType
		err       error
	}{
		{
			name:      "newSugarClient-test",
			region:    region,
			clusterID: clusterID,
			accountID: "testAccountID",
			local:     true,
			meshType:  model_meta.StandaloneMeshType,
			err:       fmt.Errorf("illegal base64 data at input byte 0"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdata.MockLocalEnv(tt.local)
			testdata.MockLocalAKSK()

			viper.Set(cceEndpoint, testdata.CceEndpoint)
			defer viper.Reset()

			patches := gomonkey.ApplyFunc(cce.GetSugarClient,
				func(_ csm_context.CsmContext, _ string) (*cce.Client, error) {
					return fakeCceClient, nil
				})
			patches.ApplyPrivateMethod(fakeCceClient, "GetAdminKubeConfig",
				func(_ *cce_v2.GetKubeConfigArgs) (*cce_v2.GetKubeConfigResponse, error) {
					return testdata.MockBuildGetKubeConfigResponse(), nil
				})
			defer patches.Reset()
			service := NewClientService()
			_, err := service.NewSugarClient(mockContext, tt.accountID, tt.region, tt.clusterID, tt.meshType)
			assert.Contains(t, err.Error(), tt.err.Error())
		})
	}
}

func TestService_GetSugarCceClient(t *testing.T) {
	tests := []struct {
		name      string
		accountID string
		region    string
		want      *cce.Client
		wantErr   bool
	}{
		{
			name:      "getSugarCceClient-test",
			accountID: "testAccountID",
			region:    region,
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := NewClientService()
			viper.Set("local.dev", true)
			got, _ := service.GetSugarCceClient(mockContext, tt.accountID, tt.region)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSugarCceClient() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetClusterAddonByClusterId(t *testing.T) {
	service := NewClientService()
	patches := gomonkey.ApplyFunc(cce.GetClient,
		func(_ csm_context.CsmContext, _ string) (*cce.Client, error) {
			return fakeCceClient, nil
		})
	// 使用gomonkey模拟bce.NewRequestBuilder
	patches = gomonkey.ApplyFunc(bce_sdk.NewRequestBuilder, func(_ interface{}) *bce_sdk.RequestBuilder {
		return &bce_sdk.RequestBuilder{}
	})

	// 模拟RequestBuilder的Do方法总是返回nil错误
	var rb *bce_sdk.RequestBuilder
	patches.ApplyMethod(rb, "Do", func(_ *bce_sdk.RequestBuilder) error {
		return nil
	})
	defer patches.Reset()

	_, err := service.GetClusterAddonByClusterId(mockContext, region, clusterID, model_meta.StandaloneMeshType, "")
	assert.Nil(t, err)
}

func TestService_GetCCEClusterInstances(t *testing.T) {
	tests := []struct {
		name      string
		local     bool
		meshType  model_meta.MeshType
		region    string
		clusterID string
		want      *cce_v2.ListInstancesResponse
		wantErr   bool
	}{
		{
			name:      "GetCCEClusterInstances-test",
			local:     true,
			meshType:  model_meta.StandaloneMeshType,
			region:    region,
			clusterID: clusterID,
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testdata.MockLocalEnv(tt.local)
			testdata.MockLocalAKSK()
			viper.Set(cceEndpoint, testdata.CceEndpoint)
			defer viper.Reset()
			if model_meta.StandaloneMeshType == tt.meshType {
				patches := gomonkey.ApplyFunc(cce.GetClient,
					func(_ csm_context.CsmContext, _ string) (*cce.Client, error) {
						return fakeCceClient, nil
					})
				patches.ApplyPrivateMethod(fakeCceClient.CceClientV2, "ListInstancesByPage",
					func(_ string) (*cce_v2.ListInstancesResponse, error) {
						return nil, nil
					})
				defer patches.Reset()
			}
			service := NewClientService()
			_, err := service.GetCCEClusterInstances(mockContext, tt.region, tt.clusterID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCCEClusterInstances() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
