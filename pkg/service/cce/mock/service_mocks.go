// Code generated by MockGen. DO NOT EDIT.
// Source: ./interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	cce "github.com/baidubce/bce-sdk-go/services/cce"
	v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	gomock "github.com/golang/mock/gomock"
	cce0 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/cce"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	meta0 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	kube "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

// MockClientInterface is a mock of ClientInterface interface.
type MockClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClientInterfaceMockRecorder
}

// MockClientInterfaceMockRecorder is the mock recorder for MockClientInterface.
type MockClientInterfaceMockRecorder struct {
	mock *MockClientInterface
}

// NewMockClientInterface creates a new mock instance.
func NewMockClientInterface(ctrl *gomock.Controller) *MockClientInterface {
	mock := &MockClientInterface{ctrl: ctrl}
	mock.recorder = &MockClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientInterface) EXPECT() *MockClientInterfaceMockRecorder {
	return m.recorder
}

// GetAdminKubeConfig mocks base method.
func (m *MockClientInterface) GetAdminKubeConfig(ctx context.CsmContext, region, clusterID string, kct v2.KubeConfigType, meshType meta.MeshType) (*v2.GetKubeConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdminKubeConfig", ctx, region, clusterID, kct, meshType)
	ret0, _ := ret[0].(*v2.GetKubeConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdminKubeConfig indicates an expected call of GetAdminKubeConfig.
func (mr *MockClientInterfaceMockRecorder) GetAdminKubeConfig(ctx, region, clusterID, kct, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdminKubeConfig", reflect.TypeOf((*MockClientInterface)(nil).GetAdminKubeConfig), ctx, region, clusterID, kct, meshType)
}

// GetCCECluster mocks base method.
func (m *MockClientInterface) GetCCECluster(cc context.CsmContext, region, clusterUUID string) (*cce.GetClusterResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCCECluster", cc, region, clusterUUID)
	ret0, _ := ret[0].(*cce.GetClusterResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCCECluster indicates an expected call of GetCCECluster.
func (mr *MockClientInterfaceMockRecorder) GetCCECluster(cc, region, clusterUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCCECluster", reflect.TypeOf((*MockClientInterface)(nil).GetCCECluster), cc, region, clusterUUID)
}

// GetCCEClusterInstances mocks base method.
func (m *MockClientInterface) GetCCEClusterInstances(cc context.CsmContext, region, clusterId string) (*v2.ListInstancesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCCEClusterInstances", cc, region, clusterId)
	ret0, _ := ret[0].(*v2.ListInstancesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCCEClusterInstances indicates an expected call of GetCCEClusterInstances.
func (mr *MockClientInterfaceMockRecorder) GetCCEClusterInstances(cc, region, clusterId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCCEClusterInstances", reflect.TypeOf((*MockClientInterface)(nil).GetCCEClusterInstances), cc, region, clusterId)
}

// GetCCEClusterKubeConfigByClusterUUID mocks base method.
func (m *MockClientInterface) GetCCEClusterKubeConfigByClusterUUID(cc context.CsmContext, region, clusterUuid string, kct v2.KubeConfigType, meshType meta.MeshType) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCCEClusterKubeConfigByClusterUUID", cc, region, clusterUuid, kct, meshType)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCCEClusterKubeConfigByClusterUUID indicates an expected call of GetCCEClusterKubeConfigByClusterUUID.
func (mr *MockClientInterfaceMockRecorder) GetCCEClusterKubeConfigByClusterUUID(cc, region, clusterUuid, kct, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCCEClusterKubeConfigByClusterUUID", reflect.TypeOf((*MockClientInterface)(nil).GetCCEClusterKubeConfigByClusterUUID), cc, region, clusterUuid, kct, meshType)
}

// GetCCEClusterList mocks base method.
func (m *MockClientInterface) GetCCEClusterList(ctx context.CsmContext, region string) ([]meta0.MeshCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCCEClusterList", ctx, region)
	ret0, _ := ret[0].([]meta0.MeshCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCCEClusterList indicates an expected call of GetCCEClusterList.
func (mr *MockClientInterfaceMockRecorder) GetCCEClusterList(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCCEClusterList", reflect.TypeOf((*MockClientInterface)(nil).GetCCEClusterList), ctx, region)
}

// GetCCEClusterV2 mocks base method.
func (m *MockClientInterface) GetCCEClusterV2(cc context.CsmContext, region, clusterId string) (*v2.GetClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCCEClusterV2", cc, region, clusterId)
	ret0, _ := ret[0].(*v2.GetClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCCEClusterV2 indicates an expected call of GetCCEClusterV2.
func (mr *MockClientInterfaceMockRecorder) GetCCEClusterV2(cc, region, clusterId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCCEClusterV2", reflect.TypeOf((*MockClientInterface)(nil).GetCCEClusterV2), cc, region, clusterId)
}

// GetCCEClustersV2 mocks base method.
func (m *MockClientInterface) GetCCEClustersV2(ctx context.CsmContext, region, keywordType, keyword string) (*v2.ListClustersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCCEClustersV2", ctx, region, keywordType, keyword)
	ret0, _ := ret[0].(*v2.ListClustersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCCEClustersV2 indicates an expected call of GetCCEClustersV2.
func (mr *MockClientInterfaceMockRecorder) GetCCEClustersV2(ctx, region, keywordType, keyword interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCCEClustersV2", reflect.TypeOf((*MockClientInterface)(nil).GetCCEClustersV2), ctx, region, keywordType, keyword)
}

// GetClusterAddonByClusterId mocks base method.
func (m *MockClientInterface) GetClusterAddonByClusterId(ctx context.CsmContext, region, clusterID string, meshType meta.MeshType, addons string) (*cce0.AddonResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterAddonByClusterId", ctx, region, clusterID, meshType, addons)
	ret0, _ := ret[0].(*cce0.AddonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterAddonByClusterId indicates an expected call of GetClusterAddonByClusterId.
func (mr *MockClientInterfaceMockRecorder) GetClusterAddonByClusterId(ctx, region, clusterID, meshType, addons interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterAddonByClusterId", reflect.TypeOf((*MockClientInterface)(nil).GetClusterAddonByClusterId), ctx, region, clusterID, meshType, addons)
}

// GetSugarCceClient mocks base method.
func (m *MockClientInterface) GetSugarCceClient(ctx context.CsmContext, accountID, region string) (*cce0.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSugarCceClient", ctx, accountID, region)
	ret0, _ := ret[0].(*cce0.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSugarCceClient indicates an expected call of GetSugarCceClient.
func (mr *MockClientInterfaceMockRecorder) GetSugarCceClient(ctx, accountID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSugarCceClient", reflect.TypeOf((*MockClientInterface)(nil).GetSugarCceClient), ctx, accountID, region)
}

// NewClient mocks base method.
func (m *MockClientInterface) NewClient(ctx context.CsmContext, region, clusterUUID string, meshType meta.MeshType) (kube.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewClient", ctx, region, clusterUUID, meshType)
	ret0, _ := ret[0].(kube.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewClient indicates an expected call of NewClient.
func (mr *MockClientInterfaceMockRecorder) NewClient(ctx, region, clusterUUID, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewClient", reflect.TypeOf((*MockClientInterface)(nil).NewClient), ctx, region, clusterUUID, meshType)
}

// NewSugarClient mocks base method.
func (m *MockClientInterface) NewSugarClient(ctx context.CsmContext, accountID, region, clusterUUID string, meshType meta.MeshType) (kube.Client, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSugarClient", ctx, accountID, region, clusterUUID, meshType)
	ret0, _ := ret[0].(kube.Client)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewSugarClient indicates an expected call of NewSugarClient.
func (mr *MockClientInterfaceMockRecorder) NewSugarClient(ctx, accountID, region, clusterUUID, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSugarClient", reflect.TypeOf((*MockClientInterface)(nil).NewSugarClient), ctx, accountID, region, clusterUUID, meshType)
}
