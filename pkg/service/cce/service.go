package cce

import (
	"fmt"

	"github.com/spf13/viper"

	cce_v1 "github.com/baidubce/bce-sdk-go/services/cce"
	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	model_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	service_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

// Service for cce
type Service struct {
	option *Option
}

// NewClientService 初始化某个地域的 cce client
func NewClientService() *Service {
	svc := &Service{
		option: NewOption(),
	}
	return svc
}

func (service *Service) getCceClient(ctx csmContext.CsmContext, region string, meshType model_meta.MeshType) (*cce.Client, error) {
	endpoint := fmt.Sprintf(service.option.CceEndpoint, region)
	ctx.CsmLogger().Infof("getCceClient endpoint=%s ", endpoint)
	client := &cce.Client{}
	var err error
	if meshType == model_meta.HostingMeshType {
		ctx.CsmLogger().Infof("*** get GetAdminKubeConfig with aksk ***")
		client, err = cce.GetClientWithAkSk(ctx, service.option.AccessKey, service.option.SecretKey, endpoint)
	} else {
		ctx.CsmLogger().Infof("*** get GetAdminKubeConfig with sts ***")
		client, err = cce.GetClient(ctx, endpoint)
	}
	return client, err
}

func (service *Service) GetSugarCceClient(ctx csmContext.CsmContext, accountID, region string) (*cce.Client, error) {
	endpoint := fmt.Sprintf(service.option.CceEndpoint, region)
	client := &cce.Client{}
	var err error
	user := &sdkIAM.User{
		ID: accountID,
		Domain: sdkIAM.UserDomain{
			ID: accountID,
		},
	}
	ctx.Set(iam.ContextIAMUser, user)
	ctx.CsmLogger().Infof("*** accountID %s get sugar client ***", accountID)
	client, err = cce.GetSugarClient(ctx, service.option.RoleName, service.option.ClientProfile, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("accountID %s GetClient error is %v", accountID, err)
	}
	return client, err
}

// NewClient creates a Kubernetes client for CCE.
func (service *Service) NewClient(ctx csmContext.CsmContext, region, clusterUUID string, meshType model_meta.MeshType) (kube.Client, error) {
	kct := cce_v2.KubeConfigTypeInternal
	if viper.GetBool("local.dev") {
		ctx.CsmLogger().Infof("*** local develop mode to get NewClient ***")
		kct = cce_v2.KubeConfigTypePublic
	}
	kubeConfig, err := service.GetAdminKubeConfig(ctx, region, clusterUUID, kct, meshType)
	if err != nil {
		return nil, err
	}
	return kube.NewClientWithKubeConfigBytes([]byte(kubeConfig.KubeConfig))
}

func (service *Service) NewSugarClient(ctx csmContext.CsmContext, accountID, region, clusterUUID string, meshType model_meta.MeshType) (kube.Client, error) {
	kct := cce_v2.KubeConfigTypeInternal
	client, err := service.GetSugarCceClient(ctx, accountID, region)
	if err != nil {
		return nil, err
	}
	getKubeConfigArgs := &cce_v2.GetKubeConfigArgs{
		ClusterID:      clusterUUID,
		KubeConfigType: kct,
	}
	kubeConfig, err := client.GetAdminKubeConfig(getKubeConfigArgs)
	if err != nil {
		return nil, err
	}
	return kube.NewClientWithKubeConfigBytes([]byte(kubeConfig.KubeConfig))
}

// GetCCEClusterKubeConfigByClusterUUID 通过 cce cluster_uuid 查询对应的 kubeconfig
// TODO 我们应当使用 GetAdminKubeConfig
func (service *Service) GetCCEClusterKubeConfigByClusterUUID(cc csmContext.CsmContext, region, clusterUuid string,
	kct cce_v2.KubeConfigType, meshType model_meta.MeshType) ([]byte, error) {
	result, err := service.GetAdminKubeConfig(cc, region, clusterUuid, kct, meshType)
	if err != nil {
		return nil, err
	}
	return []byte(result.KubeConfig), nil
}

func (service *Service) GetAdminKubeConfig(ctx csmContext.CsmContext, region, clusterID string,
	kct cce_v2.KubeConfigType, meshType model_meta.MeshType) (*cce_v2.GetKubeConfigResponse, error) {
	client, err := service.getCceClient(ctx, region, meshType)
	if err != nil {
		return nil, err
	}
	getKubeConfigArgs := &cce_v2.GetKubeConfigArgs{
		ClusterID:      clusterID,
		KubeConfigType: kct,
	}
	return client.GetAdminKubeConfig(getKubeConfigArgs)
}

// GetCCECluster 获取集群的详细信息
func (service *Service) GetCCECluster(ctx csmContext.CsmContext, region, clusterID string) (*cce_v1.GetClusterResult, error) {
	client, err := service.getCceClient(ctx, region, model_meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}
	// v2 版本的接口缺少 vpcName，所以使用 v1 版本接口
	return client.CceClientV1.GetCluster(clusterID)
}

// GetCCEClusterList 通过 cce api 获取 cce 集群列表
func (service *Service) GetCCEClusterList(ctx csmContext.CsmContext, region string) ([]service_meta.MeshCluster, error) {
	client, err := service.getCceClient(ctx, region, model_meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}
	// 获取所有的集群，目前 CCE 集群数量不会超过 100
	args := &cce_v2.ListClustersArgs{
		PageNum:  1,
		PageSize: 100,
	}
	result, err := client.CceClientV2.ListClusters(args)
	if err != nil {
		return nil, err
	}
	var meshCluster []service_meta.MeshCluster
	if result != nil && result.ClusterPage != nil {
		clusterList := result.ClusterPage.ClusterList
		for _, cce := range clusterList {
			cceCluster := service_meta.MeshCluster{
				ClusterName: cce.Spec.ClusterName,
				ClusterType: string(cce.Spec.ClusterType),
				ClusterUuid: cce.Spec.ClusterID,
				Version:     string(cce.Spec.K8SVersion),
				Region:      region,
			}
			meshCluster = append(meshCluster, cceCluster)
		}
	}
	return meshCluster, nil
}

func (service *Service) GetCCEClusterV2(ctx csmContext.CsmContext, region, clusterID string) (*cce_v2.GetClusterResponse, error) {
	client, err := service.getCceClient(ctx, region, model_meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}
	return client.CceClientV2.GetCluster(clusterID)
}

func (service *Service) GetCCEClustersV2(ctx csmContext.CsmContext, region,
	keywordType, keyword string) (*cce_v2.ListClustersResponse, error) {
	client, err := service.getCceClient(ctx, region, model_meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}
	args := &cce_v2.ListClustersArgs{
		PageNum:     1,
		PageSize:    100,
		KeywordType: cce_v2.ClusterKeywordType(keywordType),
		Keyword:     keyword,
	}

	return client.CceClientV2.ListClusters(args)
}

// GetClusterAddonByClusterId 根据集群ID获取集群插件列表
//
// 参数：
//
//	service: Service 结构体指针，包含了需要使用的服务实例
//	ctx: CsmContext 结构体指针，表示上下文信息
//	region: string 类型，表示地域信息
//	clusterID: string 类型，表示集群ID
//	kct: KubeConfigType 类型，表示KubeConfig类型
//	meshType: MeshType 类型，表示网格类型
//	addons: string 类型，表示插件名称
//
// 返回值：
//
//	*AddonResponse: AddonResponse 结构体指针，表示插件列表信息
//	error: 错误信息，如果执行成功则为nil
func (service *Service) GetClusterAddonByClusterId(ctx csmContext.CsmContext, region, clusterID string,
	meshType model_meta.MeshType, addons string) (*cce.AddonResponse, error) {
	ctx.CsmLogger().Infof("Service GetClusterAddonByClusterId getCceClient start, clusterID: %s", clusterID)
	client, err := service.getCceClient(ctx, region, meshType)
	if err != nil {
		return nil, err
	}
	ctx.CsmLogger().Infof("GetClusterAddonByClusterId start, clusterID: %s", clusterID)
	result, err := client.GetClusterAddonByClusterId(clusterID, addons)
	if err != nil {
		ctx.CsmLogger().Warnf("GetClusterAddonByClusterId error, clusterID: %s", clusterID)

		return nil, err
	}
	ctx.CsmLogger().Infof("GetClusterAddonByClusterId successful, clusterID: %s", clusterID)
	return result, nil
}

// GetCCEClusterInstances 获取集群节点列表
func (service *Service) GetCCEClusterInstances(ctx csmContext.CsmContext, region, clusterID string) (*cce_v2.ListInstancesResponse, error) {
	client, err := service.getCceClient(ctx, region, model_meta.StandaloneMeshType)
	if err != nil {
		return nil, err
	}
	args := &cce_v2.ListInstancesByPageArgs{
		ClusterID: clusterID,
		Params:    &cce_v2.ListInstancesByPageParams{},
	}
	return client.CceClientV2.ListInstancesByPage(args)
}
