package cce

import (
	cce_v1 "github.com/baidubce/bce-sdk-go/services/cce"
	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/cce"
	model_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	service_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

type ClientInterface interface {
	NewClient(ctx csmContext.CsmContext, region, clusterUUID string, meshType model_meta.MeshType) (kube.Client, error)
	GetCCEClusterKubeConfigByClusterUUID(cc csmContext.CsmContext, region, clusterUuid string, kct cce_v2.KubeConfigType,
		meshType model_meta.MeshType) ([]byte, error)
	GetCCEClusterV2(cc csmContext.CsmContext, region, clusterId string) (*cce_v2.GetClusterResponse, error)
	GetCCEClustersV2(ctx csmContext.CsmContext, region, keywordType, keyword string) (*cce_v2.ListClustersResponse, error)
	GetCCEClusterList(ctx csmContext.CsmContext, region string) ([]service_meta.MeshCluster, error)
	GetCCECluster(cc csmContext.CsmContext, region, clusterUUID string) (*cce_v1.GetClusterResult, error)
	GetAdminKubeConfig(ctx csmContext.CsmContext, region, clusterID string,
		kct cce_v2.KubeConfigType, meshType model_meta.MeshType) (*cce_v2.GetKubeConfigResponse, error)
	NewSugarClient(ctx csmContext.CsmContext, accountID, region, clusterUUID string, meshType model_meta.MeshType) (kube.Client, error)
	GetSugarCceClient(ctx csmContext.CsmContext, accountID, region string) (*cce.Client, error)
	GetClusterAddonByClusterId(ctx csmContext.CsmContext, region, clusterID string,
		meshType model_meta.MeshType, addons string) (*cce.AddonResponse, error)
	GetCCEClusterInstances(cc csmContext.CsmContext, region, clusterId string) (*cce_v2.ListInstancesResponse, error)
}
