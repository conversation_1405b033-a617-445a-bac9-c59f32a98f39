package cluster

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetManagedClusters(ctx context.CsmContext, instanceId string, pageParam *meta.Page, lf *meta.ListFilter) (
		[]*meta.ManagedCluster, *meta.PageResult, error)
	GetCandidateClusters(ctx context.CsmContext, instanceId string, pageParam *meta.Page, lf *meta.ListFilter) (
		[]meta.CandidateCluster, *meta.PageResult, error)
	AddClusters(ctx context.CsmContext, instanceId string, clusters []meta.CandidateCluster) error
	RemoveClusters(ctx context.CsmContext, instancesId string, clusters []meta.ManagedCluster) error
	ExistRemoteClusters(ctx context.CsmContext, instancesId string) (bool, error)
	IsRemoteConfigClusters(ctx context.CsmContext, instancesID, clusterID, region string) (bool, error)
}
