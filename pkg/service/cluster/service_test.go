package cluster

import (
	"context"
	"testing"
	"time"

	cce_v1 "github.com/baidubce/bce-sdk-go/services/cce"
	cce_v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	cce_v2_types "github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	mockCluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	mockModelInstance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	mockMonitorModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor/mock"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockBlsService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/bls/mock"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	mockServiceInstance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

const (
	regions = "cloud.regions"
)

var (
	namespace = "istio-system"
)

var (
	mockDB     *gorm.DB
	mockCtx, _ = ctxCsm.NewCsmContextMock()

	mockRegions                = []string{"bj"}
	instanceUUID               = "csm-nxzqosxx"
	instanceName               = "tanjunchen-test"
	instanceType               = "standalone"
	instanceHostingType        = "hosting"
	istioVersion               = "1.13.2"
	region                     = "bj"
	accountId                  = "1"
	discoverySelectorEnabled   = true
	discoverySelectorLabels    = "{\"user\": \"test\"}"
	discoverySelectorLabelsMap = map[string]string{"user": "test"}
	istioInstallNamespace      = "istio-system"
	InstanceManageScope        = "cluster"
	Status                     = ""
	istiodName                 = "istiod-6d5cccd675-tc765"

	clusterID   = "cce-123456"
	clusterName = "test-123456"
	clusterType = "normal"
	k8sVersion  = "1.20.0"

	clusterID2   = "cce-123456-x"
	clusterName2 = "test-123456-x"

	testInstanceUUID          = "csm-123456"
	testInstanceType          = "standalone"
	testIstioVersion          = "1.13.2"
	testInstanceStatus        = "running"
	testClusterName           = "test-cluster"
	testClusterUUID           = "cce-123456"
	testRegion                = "bj"
	testIstioInstallNamespace = "istio-system"

	agentId = "agent-id"
)

func InitRegion() {
	viper.Set(regions, mockRegions)
}

func buildMockInstance() *meta.Instances {
	instances := &meta.Instances{
		InstanceUUID:             instanceUUID,
		InstanceName:             instanceName,
		InstanceType:             instanceHostingType,
		IstioVersion:             istioVersion,
		Region:                   region,
		AccountId:                accountId,
		DiscoverySelectorEnabled: csm.Bool(discoverySelectorEnabled),
		DiscoverySelectorLabels:  discoverySelectorLabels,
		IstioInstallNamespace:    istioInstallNamespace,
		InstanceManageScope:      InstanceManageScope,
		Status:                   Status,
		MonitorEnabled:           csm.Bool(true),
	}
	return instances
}

func buildManagedCluster() []meta.ManagedCluster {
	var managedCluster []meta.ManagedCluster
	managedCluster = append(managedCluster, meta.ManagedCluster{
		InstanceId:  instanceUUID,
		ClusterId:   clusterID,
		ClusterName: clusterName,
		ClusterType: meta.ClusterTypePrimary,
		Region:      "bj",
		AccountId:   accountId,
		Status:      meta.ClusterStatusRunning,
		Version:     "1.13.2",
	})
	return managedCluster
}

func buildMockListClustersResponse() *cce_v2.ListClustersResponse {
	return &cce_v2.ListClustersResponse{
		ClusterPage: &cce_v2.ClusterPage{
			PageNo:     1,
			PageSize:   100,
			TotalCount: 1,
			ClusterList: []*cce_v2.Cluster{
				{
					Spec: &cce_v2.ClusterSpec{
						ClusterID:   clusterID,
						ClusterName: clusterName,
						ClusterType: types.ClusterType(clusterType),
						K8SVersion:  types.K8SVersion(k8sVersion),
					},
					Status: &cce_v2.ClusterStatus{
						ClusterPhase: cce_v2_types.ClusterPhaseRunning,
						NodeNum:      1,
					},
				},
				{
					Spec: &cce_v2.ClusterSpec{
						ClusterID:   clusterID2,
						ClusterName: clusterName2,
						ClusterType: types.ClusterType(clusterType),
						K8SVersion:  types.K8SVersion(k8sVersion),
					},
					Status: &cce_v2.ClusterStatus{
						ClusterPhase: cce_v2_types.ClusterPhaseRunning,
						NodeNum:      1,
					},
				},
			},
		},
		RequestID: "xxxx",
	}
}

func TestGetCandidateClusters(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockModelInstance.NewMockServiceInterface(ctrl)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)

	tests := []struct {
		name                 string
		initRegion           bool
		instanceId           string
		page                 *meta.Page
		listFilter           *meta.ListFilter
		wantCandidateCluster []meta.CandidateCluster
		wantPageResult       *meta.PageResult
		wantErr              error
	}{
		{
			name:       "GetCandidateClusters-region",
			initRegion: true,
			instanceId: instanceUUID,
			page:       nil,
			listFilter: nil,
			wantCandidateCluster: []meta.CandidateCluster{
				{
					ClusterId:   clusterID2,
					ClusterName: clusterName2,
					Version:     k8sVersion,
					Region:      region,
					Status:      meta.ClusterStatusRunning,
					Available:   true,
				},
			},
			wantPageResult: &meta.PageResult{
				PageSize:   100,
				PageNo:     1,
				Order:      "",
				OrderBy:    "",
				TotalCount: 1,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt:           NewOption(mockDB),
				clusterModel:  mockClusterModel,
				instanceModel: mockInstanceModel,
				cceService:    mockCceService,
			}

			if tt.initRegion {
				InitRegion()
				mockBjCCEClusterV2 := buildMockListClustersResponse()
				mockCceService.EXPECT().GetCCEClustersV2(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).
					Return(mockBjCCEClusterV2, nil).AnyTimes()
			}

			mockInstances := buildMockInstance()
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(mockInstances, nil)

			mockManagedCluster := buildManagedCluster()
			mockClusterModel.EXPECT().GetManagedClustersByUser(mockCtx).Return(mockManagedCluster, nil)

			gotCandidateCluster, gotPageResult, err := service.GetCandidateClusters(mockCtx, tt.instanceId, tt.page, tt.listFilter)
			if err != nil {
				assert.Contains(t, err, tt.wantErr)
			} else {
				assert.Equal(t, tt.wantCandidateCluster, gotCandidateCluster)
				assert.Equal(t, tt.wantPageResult, gotPageResult)
			}
		})
	}
}

func buildMockCluster() meta.Cluster {
	mockCluster := meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}
	return mockCluster
}

func buildMetaCluster() *meta.Cluster {
	cluster := &meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}
	return cluster
}

// TestRemoveClusters 测试 RemoveClusters 函数
// 该函数用于删除指定实例的集群信息
// 参数：
//   - t *testing.T: 测试对象，用于记录测试结果
//   - tt struct{...}: 包含多个字段的结构体，包括测试名称、实例ID、实例类型、集群列表、模拟的集群、元模型中的集群、预期错误等信息
//
// 返回值：
//   - none
func TestRemoveClusters(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockModelInstance.NewMockServiceInterface(ctrl)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
	mockInstanceService := mockServiceInstance.NewMockServiceInterface(ctrl)
	mockBlsService := mockBlsService.NewMockServiceInterface(ctrl)

	tests := []struct {
		name         string
		instanceId   string
		instanceType string
		clusters     []meta.ManagedCluster
		cluster      meta.Cluster
		metaCluster  *meta.Cluster
		wantErr      error
	}{
		{
			name:         "RemoveClusters-ok",
			instanceId:   instanceUUID,
			instanceType: instanceType,
			clusters:     buildManagedCluster(),
			cluster:      buildMockCluster(),
			metaCluster:  buildMetaCluster(),
			wantErr:      nil,
		},
		{
			name:         "hosting RemoveClusters",
			instanceId:   instanceUUID,
			instanceType: "hosting",
			clusters: []meta.ManagedCluster{{
				InstanceId:  testInstanceUUID,
				ClusterId:   testClusterUUID,
				ClusterName: testClusterName,
				Region:      testRegion,
			}},
			cluster: meta.Cluster{
				InstanceUUID:          testInstanceUUID,
				ClusterUUID:           testClusterUUID,
				ClusterName:           testClusterName,
				Region:                testRegion,
				IstioInstallNamespace: testIstioInstallNamespace,
				MonitorAgentID:        agentId,
				MonitorRegion:         region,
				MonitorInstanceId:     instanceUUID,
				MonitorJobIds:         "job-id",
			},
			metaCluster: buildMetaCluster(),
			wantErr:     nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			monitorService := mockMonitorModel.NewMockServiceInterface(ctrl)
			service := &Service{
				opt:             NewOption(mockDB),
				clusterModel:    mockClusterModel,
				instanceModel:   mockInstanceModel,
				instanceService: mockInstanceService,
				blsService:      mockBlsService,
				monitorModel:    monitorService,
			}

			mockInstances := buildMockInstance()
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(mockInstances, nil)

			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&tt.cluster, meta.MeshType(instanceType), nil)

			mockClusterModel.EXPECT().GetClusterByIdAndRegion(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.metaCluster, nil)
			mockClusterModel.EXPECT().DeleteCluster(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mockInstanceService.EXPECT().RemoveRemoteUserMeshCluster(gomock.Any(), gomock.Any()).Return(nil)
			mockBlsService.EXPECT().BlsRemoveCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil).AnyTimes()

			monitorService.EXPECT().DeleteScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			monitorService.EXPECT().GetCPromAgent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(buildCPromAgent(), nil).AnyTimes()

			err := service.RemoveClusters(mockCtx, tt.instanceId, tt.clusters)
			// todo better way to consider
			time.Sleep(1 * time.Second)
			assert.Nil(t, err)
		})
	}
}

func buildCPromAgent() *meta.CPromAgent {
	return &meta.CPromAgent{
		AgentID: agentId,
	}
}

func buildCandidateCluster() []meta.CandidateCluster {
	var candidateClusters []meta.CandidateCluster
	candidateCluster := meta.CandidateCluster{
		ClusterId:   "cce-123456-x",
		ClusterName: "test-123456-x",
		Version:     "1.20.0",
		Region:      "bj",
		Available:   true,
	}
	candidateClusters = append(candidateClusters, candidateCluster)
	return candidateClusters
}

func buildClusterDetail() *cce_v1.GetClusterResult {
	return &cce_v1.GetClusterResult{
		ClusterUuid: clusterID,
		ClusterName: clusterName,
		Version:     testIstioVersion,
		Region:      region,
	}
}

func buildIstioService() *v1.Service {
	return &v1.Service{
		Status: v1.ServiceStatus{
			LoadBalancer: v1.LoadBalancerStatus{
				Ingress: []v1.LoadBalancerIngress{
					{
						IP:       "*******",
						Hostname: "test",
					},
				},
			},
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      constants.IstiodServiceName,
			Namespace: namespace,
			Annotations: map[string]string{
				constants.BLBId: "xxx",
			},
		},
	}
}

// TestAddClusters 测试函数，用于添加集群
// 参数t：*testing.T类型，表示当前测试用例的对象指针
// 返回值error，表示错误信息，如果没有错误则为nil
func TestAddClusters(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockModelInstance.NewMockServiceInterface(ctrl)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
	mockInstanceService := mockServiceInstance.NewMockServiceInterface(ctrl)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockBlsService := mockBlsService.NewMockServiceInterface(ctrl)
	mockMonitorServer := mockMonitorModel.NewMockServiceInterface(ctrl)

	tests := []struct {
		name         string
		instanceId   string
		instanceType string
		wantErr      error
	}{
		{
			name:         "AddClusters-ok",
			instanceId:   instanceUUID,
			instanceType: instanceType,
			wantErr:      nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt:             NewOption(mockDB),
				clusterModel:    mockClusterModel,
				instanceModel:   mockInstanceModel,
				instanceService: mockInstanceService,
				cceService:      mockCceService,
				blsService:      mockBlsService,
				monitorModel:    mockMonitorServer,
			}
			fakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			fakeClient.Kube().CoreV1().Services(namespace).Create(context.TODO(), buildIstioService(), metav1.CreateOptions{})

			mockInstances := buildMockInstance()
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(mockInstances, nil)

			mockInstanceCluster := buildMockCluster()
			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(mockCtx, gomock.Any()).Return(&mockInstanceCluster, meta.MeshType(instanceType), nil)

			mockCceService.EXPECT().GetCCECluster(mockCtx, gomock.Any(), gomock.Any()).Return(buildClusterDetail(), nil)

			mockInstanceService.EXPECT().AddRemoteUserMeshCluster(gomock.Any(), gomock.Any()).Return(nil)

			mockClusterModel.EXPECT().NewCluster(gomock.Any(), gomock.Any()).Return(nil)

			mockBlsService.EXPECT().BlsAddCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil).AnyTimes()

			mockMonitorServer.EXPECT().GetCPromAgent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				AnyTimes().Return(buildCPromAgent(), nil)
			mockMonitorServer.EXPECT().CreateEnvoyScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return("jobId-2", nil).AnyTimes()

			err := service.AddClusters(mockCtx, tt.instanceId, buildCandidateCluster())
			// todo better way to consider
			time.Sleep(1 * time.Second)
			assert.Nil(t, err)
		})
	}
}

func buildMockClusters() *[]meta.Cluster {
	clusters := make([]meta.Cluster, 0)
	clusters1 := meta.Cluster{
		InstanceUUID:          instanceUUID,
		ClusterUUID:           "aaa",
		ClusterName:           "bbb",
		ClusterType:           string(meta.ClusterTypeRemote),
		Region:                region,
		AccountId:             accountId,
		IstioInstallNamespace: istioInstallNamespace,
	}
	clusters = append(clusters, clusters1)
	return &clusters
}

func TestExistRemoteClusters(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)

	tests := []struct {
		name       string
		instanceId string
		res        bool
		wantErr    error
	}{
		{
			name:       "ExistRemoteClusters-ok",
			instanceId: instanceUUID,
			res:        true,
			wantErr:    nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt:          NewOption(mockDB),
				clusterModel: mockClusterModel,
			}
			mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(gomock.Any(), gomock.Any()).Return(buildMockClusters(), nil)
			gotRes, err := service.ExistRemoteClusters(mockCtx, tt.instanceId)
			assert.Nil(t, err)
			assert.Equal(t, tt.res, gotRes)
		})
	}
}

func buildMockManagedCluster() []*meta.ManagedCluster {
	var managedClusters []*meta.ManagedCluster
	managedClusters = append(managedClusters, &meta.ManagedCluster{
		InstanceId:  instanceUUID,
		ClusterId:   clusterID,
		ClusterName: clusterName,
		ClusterType: meta.ClusterTypePrimary,
		Region:      region,
		AccountId:   accountId,
		Status:      meta.ClusterStatusRunning,
		Version:     k8sVersion,
	})
	return managedClusters
}

func buildMockGetClusterResponse() *cce_v2.GetClusterResponse {
	return &cce_v2.GetClusterResponse{
		Cluster: &cce_v2.Cluster{
			Spec: &cce_v2.ClusterSpec{
				ClusterID:   clusterID,
				ClusterName: clusterName,
				K8SVersion:  cce_v2_types.K8SVersion(k8sVersion),
			},
			Status: &cce_v2.ClusterStatus{
				ClusterPhase: cce_v2_types.ClusterPhaseRunning,
				NodeNum:      1,
			},
		},
	}
}

func buildMockExpectManagedCluster() []*meta.ManagedCluster {
	var managedClusters []*meta.ManagedCluster
	cs := meta.ClusterConnectedState
	managedClusters = append(managedClusters, &meta.ManagedCluster{
		InstanceId:      instanceUUID,
		ClusterId:       clusterID,
		ClusterName:     clusterName,
		ClusterType:     meta.ClusterTypePrimary,
		Region:          region,
		Status:          meta.ClusterStatusRunning,
		ConnectionState: &cs,
		AccountId:       accountId,
		Version:         k8sVersion,
	})
	return managedClusters
}

func TestGetManagedClusters(t *testing.T) {
	tests := []struct {
		name                               string
		instanceId                         string
		page                               *meta.Page
		listFilter                         *meta.ListFilter
		GetManagedClustersByPageClusters   []*meta.ManagedCluster
		GetManagedClustersByPageTotalCount int64
		GetManagedClustersByPageErr        error
		GetCCEClusterV2CCEClusterV2Detail  *cce_v2.GetClusterResponse
		GetCCEClusterV2Error               error

		expectManagedCluster []*meta.ManagedCluster
		expectPageResult     *meta.PageResult
		expectErr            error
	}{
		{
			name:                               "GetManagedClusters-success",
			instanceId:                         instanceUUID,
			GetManagedClustersByPageClusters:   buildMockManagedCluster(),
			GetManagedClustersByPageTotalCount: int64(len(buildMockManagedCluster())),
			GetManagedClustersByPageErr:        nil,
			GetCCEClusterV2CCEClusterV2Detail:  buildMockGetClusterResponse(),
			GetCCEClusterV2Error:               nil,

			expectPageResult: &meta.PageResult{
				PageSize:   100,
				PageNo:     1,
				TotalCount: 1,
			},
			expectManagedCluster: buildMockExpectManagedCluster(),
			expectErr:            nil,
		},
	}
	ctrl := gomock.NewController(t)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt:          NewOption(mockDB),
				clusterModel: mockClusterModel,
				cceService:   mockCceService,
			}

			mockClusterModel.EXPECT().GetManagedClustersByPage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(tt.GetManagedClustersByPageClusters, tt.GetManagedClustersByPageTotalCount, tt.GetManagedClustersByPageErr)
			mockCceService.EXPECT().GetCCEClusterV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.GetCCEClusterV2CCEClusterV2Detail, tt.GetCCEClusterV2Error)
			managedCluster, pageResult, err := service.GetManagedClusters(mockCtx, tt.instanceId, tt.page, tt.listFilter)
			if tt.expectErr == nil {
				assert.Nil(t, err)
				assert.Equal(t, tt.expectManagedCluster, managedCluster)
				assert.Equal(t, tt.expectPageResult, pageResult)
			} else {
				assert.Equal(t, err, tt.expectErr.Error())
			}
		})
	}
}
