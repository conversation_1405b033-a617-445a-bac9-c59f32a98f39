// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// AddClusters mocks base method.
func (m *MockServiceInterface) AddClusters(ctx context.CsmContext, instanceId string, clusters []meta.CandidateCluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddClusters", ctx, instanceId, clusters)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddClusters indicates an expected call of AddClusters.
func (mr *MockServiceInterfaceMockRecorder) AddClusters(ctx, instanceId, clusters interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddClusters", reflect.TypeOf((*MockServiceInterface)(nil).AddClusters), ctx, instanceId, clusters)
}

// ExistRemoteClusters mocks base method.
func (m *MockServiceInterface) ExistRemoteClusters(ctx context.CsmContext, instancesId string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExistRemoteClusters", ctx, instancesId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExistRemoteClusters indicates an expected call of ExistRemoteClusters.
func (mr *MockServiceInterfaceMockRecorder) ExistRemoteClusters(ctx, instancesId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExistRemoteClusters", reflect.TypeOf((*MockServiceInterface)(nil).ExistRemoteClusters), ctx, instancesId)
}

// GetCandidateClusters mocks base method.
func (m *MockServiceInterface) GetCandidateClusters(ctx context.CsmContext, instanceId string, pageParam *meta.Page, lf *meta.ListFilter) ([]meta.CandidateCluster, *meta.PageResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCandidateClusters", ctx, instanceId, pageParam, lf)
	ret0, _ := ret[0].([]meta.CandidateCluster)
	ret1, _ := ret[1].(*meta.PageResult)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCandidateClusters indicates an expected call of GetCandidateClusters.
func (mr *MockServiceInterfaceMockRecorder) GetCandidateClusters(ctx, instanceId, pageParam, lf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCandidateClusters", reflect.TypeOf((*MockServiceInterface)(nil).GetCandidateClusters), ctx, instanceId, pageParam, lf)
}

// GetManagedClusters mocks base method.
func (m *MockServiceInterface) GetManagedClusters(ctx context.CsmContext, instanceId string, pageParam *meta.Page, lf *meta.ListFilter) ([]*meta.ManagedCluster, *meta.PageResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManagedClusters", ctx, instanceId, pageParam, lf)
	ret0, _ := ret[0].([]*meta.ManagedCluster)
	ret1, _ := ret[1].(*meta.PageResult)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetManagedClusters indicates an expected call of GetManagedClusters.
func (mr *MockServiceInterfaceMockRecorder) GetManagedClusters(ctx, instanceId, pageParam, lf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManagedClusters", reflect.TypeOf((*MockServiceInterface)(nil).GetManagedClusters), ctx, instanceId, pageParam, lf)
}

// IsRemoteConfigClusters mocks base method.
func (m *MockServiceInterface) IsRemoteConfigClusters(ctx context.CsmContext, instancesID, clusterID, region string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsRemoteConfigClusters", ctx, instancesID, clusterID, region)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsRemoteConfigClusters indicates an expected call of IsRemoteConfigClusters.
func (mr *MockServiceInterfaceMockRecorder) IsRemoteConfigClusters(ctx, instancesID, clusterID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRemoteConfigClusters", reflect.TypeOf((*MockServiceInterface)(nil).IsRemoteConfigClusters), ctx, instancesID, clusterID, region)
}

// RemoveClusters mocks base method.
func (m *MockServiceInterface) RemoveClusters(ctx context.CsmContext, instancesId string, clusters []meta.ManagedCluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveClusters", ctx, instancesId, clusters)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveClusters indicates an expected call of RemoveClusters.
func (mr *MockServiceInterfaceMockRecorder) RemoveClusters(ctx, instancesId, clusters interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveClusters", reflect.TypeOf((*MockServiceInterface)(nil).RemoveClusters), ctx, instancesId, clusters)
}
