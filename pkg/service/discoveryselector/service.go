package discoveryselector

import (
	"context"
	"encoding/json"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csm_context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	servicemeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

type Service struct {
	opt *Option

	instancesModel instances.ServiceInterface
	clusterModel   cluster.ServiceInterface
	cceService     cce.ClientInterface
}

func NewDiscoverySelectorService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:            option,
		instancesModel: instances.NewInstancesService(instances.NewOption(gormDB)),
		clusterModel:   cluster.NewClusterService(cluster.NewOption(gormDB)),
		cceService:     cce.NewClientService(),
	}
}

func (s *Service) UpdateHostingDiscoverySelector(ctx csm_context.CsmContext, eks bool, oldInstance *meta.Instances, selector *servicemeta.DiscoverySelector) error {
	instanceUUID := oldInstance.InstanceUUID
	// 根据实例 id 获取所有的用户集群
	allClusters, err := s.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return err
	}
	var allRemoteCluster []meta.Cluster
	var externalCluster meta.Cluster
	for _, clu := range *allClusters {
		if clu.ClusterType == string(meta.ClusterTypeRemote) || clu.ClusterType == string(meta.ClusterTypeConfig) {
			allRemoteCluster = append(allRemoteCluster, clu)
		}
		if clu.ClusterType == string(meta.ClusterTypeExternal) {
			externalCluster = clu
		}
	}
	// 更新托管集群的 istiod 使用的 istio configmap
	// TODO: 这里的实例类型需要注意下
	hostingClient, externalClientErr := s.cceService.NewClient(ctx, externalCluster.Region, externalCluster.ClusterUUID, meta.HostingMeshType)
	if externalClientErr != nil {
		return externalClientErr
	}
	// add DiscoverySelectors
	label := make(map[string]string)
	if selector.Enabled {
		label = selector.MatchLabels
	}
	err = s.UpdateDiscoverySelectorsConfigMapIstio(hostingClient, instanceUUID, externalCluster.IstioInstallNamespace, label)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("start reboot istiod for UpdateHostingDiscoverySelector")
	err = s.RebootIstiod(ctx, hostingClient, externalCluster.IstioInstallNamespace)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("reboot istiod for UpdateHostingDiscoverySelector success")

	// TODO 目前istio 1.14.6 暂不支持crd复用DiscoverySelector参数，目前依然是config集群的所有命名空间
	// 更新用户集群的 MutatingWebhookConfiguration
	var errs util.Errors
	for _, clu := range allRemoteCluster {
		// TODO: 这里的实例类型需要注意下
		client, clientErr := s.cceService.NewClient(ctx, clu.Region, clu.ClusterUUID, meta.StandaloneMeshType)
		if clientErr != nil {
			errs = append(errs, clientErr)
		}
		webhookLabels := selector.MatchLabels
		if !selector.Enabled {
			webhookLabels = nil
		}
		err = s.UpdateMutatingWebhookConfiguration(client, constants.IstioSidecarInjectorMutatingWebhookConfiguration+"-"+clu.IstioInstallNamespace, webhookLabels)
		if err != nil {
			ctx.CsmLogger().Errorf("update MutatingWebhookConfiguration error %v", err)
			errs = append(errs, err)
		}
	}

	// 更新数据库
	updateInstance := &meta.Instances{}
	updateInstance.InstanceUUID = instanceUUID
	// update t_service_mesh_instance discovery_selector_labels
	if selector.Enabled {
		// update db
		labels, marshalErr := json.Marshal(selector.MatchLabels)
		if marshalErr != nil {
			return marshalErr
		}
		updateInstance.DiscoverySelectorLabels = string(labels)
		updateInstance.DiscoverySelectorEnabled = csm.Bool(true)
	} else {
		// remove db
		updateInstance.DiscoverySelectorEnabled = csm.Bool(false)
	}
	allError := errs.ToError()
	if allError != nil {
		return allError
	}

	_, err = s.instancesModel.UpdateInstance(ctx, oldInstance, updateInstance)
	return err
}

// UpdateDiscoverySelector 更新网格实例安装的  istio configmap discoverySelectors 配置
func (s *Service) UpdateDiscoverySelector(ctx csm_context.CsmContext, eks bool, oldInstance *meta.Instances, selector *servicemeta.DiscoverySelector) error {
	meshType := oldInstance.InstanceType
	ctx.CsmLogger().Infof("the type of mesh instance is %s", meshType)
	if meshType == string(version.HostingVersionType) {
		return s.UpdateHostingDiscoverySelector(ctx, eks, oldInstance, selector)
	}

	instanceUUID := oldInstance.InstanceUUID
	// 根据实例 id 获取所有的主集群与从集群
	allClusters, err := s.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return err
	}
	var errs util.Errors
	for _, cluster := range *allClusters {
		// TODO: 这里的实例类型需要注意下
		client, clientErr := s.cceService.NewClient(ctx, cluster.Region, cluster.ClusterUUID, meta.StandaloneMeshType)
		if clientErr != nil {
			errs = append(errs, clientErr)
		}
		// add DiscoverySelectors
		matchLabels := selector.MatchLabels
		if !selector.Enabled {
			matchLabels = nil
		}
		err = s.UpdateDiscoverySelectorsConfigMapIstio(client, instanceUUID, cluster.IstioInstallNamespace, matchLabels)
		if err != nil {
			errs = append(errs, err)
		}

		ctx.CsmLogger().Infof("start reboot istiod for UpdateDiscoverySelector")
		err = s.RebootIstiod(ctx, client, cluster.IstioInstallNamespace)
		if err != nil {
			errs = append(errs, err)
		}
		ctx.CsmLogger().Infof("reboot istiod for UpdateDiscoverySelector success")
		if eks {
			webhookLabels := selector.MatchLabels
			if !selector.Enabled {
				webhookLabels = nil
			}
			err = s.UpdateMutatingWebhookConfiguration(client,
				constants.IstioSidecarInjectorMutatingWebhookConfiguration+"-"+cluster.IstioInstallNamespace, webhookLabels)
			if err != nil {
				ctx.CsmLogger().Errorf("update MutatingWebhookConfiguration error %v on eks", err)
				errs = append(errs, err)
			}

			err = s.UpdateValidatingWebhookConfiguration(client,
				constants.IstioSidecarInjectorValidatingWebhookConfiguration+"-"+cluster.IstioInstallNamespace, webhookLabels)
			if err != nil {
				ctx.CsmLogger().Errorf("update ValidatingWebhookConfiguration error %v on eks", err)
				errs = append(errs, err)
			}
		}
	}
	updateInstance := &meta.Instances{}
	updateInstance.InstanceUUID = instanceUUID
	// update t_service_mesh_instance discovery_selector_labels
	if selector.Enabled {
		// update db
		labels, marshalErr := json.Marshal(selector.MatchLabels)
		if marshalErr != nil {
			return marshalErr
		}
		updateInstance.DiscoverySelectorLabels = string(labels)
		updateInstance.DiscoverySelectorEnabled = csm.Bool(true)
	} else {
		// remove db
		updateInstance.DiscoverySelectorEnabled = csm.Bool(false)
	}
	allError := errs.ToError()
	if allError != nil {
		return allError
	}

	_, err = s.instancesModel.UpdateInstance(ctx, oldInstance, updateInstance)
	return err
}

// GetDiscoverySelector 获取网格实例安装的 istio 配置
func (s *Service) GetDiscoverySelector(ctx csm_context.CsmContext, instanceUUID string) (
	*servicemeta.DiscoverySelector, error) {
	// 获取实例详情信息
	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return nil, err
	}

	discoverySelectorLabels := make(map[string]string)
	if *instance.DiscoverySelectorEnabled {
		err = json.Unmarshal([]byte(instance.DiscoverySelectorLabels), &discoverySelectorLabels)
		if err != nil {
			return nil, err
		}
	}
	selector := &servicemeta.DiscoverySelector{
		Enabled:     *instance.DiscoverySelectorEnabled,
		MatchLabels: discoverySelectorLabels,
	}

	return selector, nil
}

func (s Service) getLabelSelector(namespaceSelector *metav1.LabelSelector, updateNamespaceSelector *metav1.LabelSelector) *metav1.LabelSelector {
	if namespaceSelector == nil {
		namespaceSelector = new(metav1.LabelSelector)
	}
	matchExpressions := namespaceSelector.MatchExpressions
	if matchExpressions == nil {
		matchExpressions = make([]metav1.LabelSelectorRequirement, 0)
	}
	lsrs := make([]metav1.LabelSelectorRequirement, 0)
	for _, value := range matchExpressions {
		if value.Key == constants.IstioInjection || value.Key == constants.IstioIoRev {
			lsrs = append(lsrs, value)
		}
	}
	lsrs = append(lsrs, updateNamespaceSelector.MatchExpressions...)
	namespaceSelector.MatchExpressions = lsrs
	return namespaceSelector
}

func (s *Service) UpdateMutatingWebhookConfiguration(client kube.Client, name string, labels map[string]string) error {
	mutatingWebhookConfigurations, err := client.Kube().AdmissionregistrationV1().MutatingWebhookConfigurations().Get(context.TODO(), name, metav1.GetOptions{})
	if err != nil {
		return err
	}
	matchExpression := make(map[string]string, 0)
	for k, v := range labels {
		matchExpression[k] = v
	}
	ls := servicemeta.GetDiscoverySelectorsWithMap(servicemeta.LabelSelectorOpIn, matchExpression, labels)
	for i, mwc := range mutatingWebhookConfigurations.Webhooks {
		if mwc.Name == constants.MeshMutatingWebhookConfigurationInstanceId {
			continue
		}
		mwc.NamespaceSelector = s.getLabelSelector(mwc.NamespaceSelector, ls)
		mutatingWebhookConfigurations.Webhooks[i] = mwc
	}
	_, err = client.Kube().AdmissionregistrationV1().MutatingWebhookConfigurations().Update(context.TODO(), mutatingWebhookConfigurations, metav1.UpdateOptions{})
	if err != nil {
		return err
	}
	return nil
}

func (s *Service) UpdateValidatingWebhookConfiguration(client kube.Client, name string, labels map[string]string) error {
	validatingWebhookConfigurations, err := client.Kube().AdmissionregistrationV1().ValidatingWebhookConfigurations().Get(context.TODO(), name, metav1.GetOptions{})
	if err != nil {
		return err
	}
	matchExpression := make(map[string]string, 0)
	for k, v := range labels {
		matchExpression[k] = v
	}
	ls := servicemeta.GetDiscoverySelectorsWithMap(servicemeta.LabelSelectorOpIn, matchExpression, labels)
	for i, vwc := range validatingWebhookConfigurations.Webhooks {
		vwc.NamespaceSelector = s.getLabelSelector(vwc.NamespaceSelector, ls)
		if labels == nil || len(labels) == 0 {
			vwc.NamespaceSelector = nil
		}
		validatingWebhookConfigurations.Webhooks[i] = vwc
	}
	_, err = client.Kube().AdmissionregistrationV1().ValidatingWebhookConfigurations().Update(context.TODO(), validatingWebhookConfigurations, metav1.UpdateOptions{})
	if err != nil {
		return err
	}
	return nil
}

// UpdateDiscoverySelectorsConfigMapIstio 更新 istio configmap 中的 discovery_selector_labels
func (s *Service) UpdateDiscoverySelectorsConfigMapIstio(client kube.Client, instanceUUID, namespace string, labels map[string]string) error {
	cm, err := client.Kube().CoreV1().ConfigMaps(namespace).Get(context.TODO(), constants.IstioConfimapName, metav1.GetOptions{})
	if err != nil {
		return err
	}
	// todo not use meshconfig "istio.io/api/mesh/v1alpha1" due to meshconfig relate to version, so user map interface
	newConfigMap := cm.DeepCopy()
	oldData := newConfigMap.Data[constants.IstioConfigMapMeshName]
	mp := map[string]interface{}{}
	if err = yaml.Unmarshal([]byte(oldData), &mp); err != nil {
		return err
	}
	_, ok := mp[constants.DiscoverySelectorsName]
	// labels 为空表示删除
	if len(labels) == 0 {
		// delete
		if !ok {
			return nil
		}
		delete(mp, constants.DiscoverySelectorsName)
	} else {
		lss := make([]*metav1.LabelSelector, 0)
		matchLabels := make(map[string]string, 0)
		matchLabels[constants.MeshInstanceId] = instanceUUID
		ls1 := &metav1.LabelSelector{
			MatchLabels: matchLabels,
		}
		lss = append(lss, ls1)

		matchExpression := make(map[string][]string, 0)
		for k, v := range labels {
			matchExpression[k] = []string{v}
		}
		ls2 := servicemeta.GetDiscoverySelectors(servicemeta.LabelSelectorOpIn, matchExpression, nil)
		lss = append(lss, ls2...)

		// todo we can update according to the result of composing old and new istio configmap
		mp[constants.DiscoverySelectorsName] = lss
	}
	updateData, err := yaml.Marshal(mp)
	if err != nil {
		return err
	}
	newConfigMap.Data[constants.IstioConfigMapMeshName] = string(updateData)
	// todo retry update?
	_, err = client.Kube().CoreV1().ConfigMaps(namespace).Update(context.TODO(), newConfigMap, metav1.UpdateOptions{})
	if err != nil {
		return err
	}
	return nil
}

// RebootIstiod 重新启动
// todo is there a better way to reboot istiod pod?
func (s *Service) RebootIstiod(ctx csm_context.CsmContext, client kube.Client, namespace string) error {
	pods, err := client.Kube().CoreV1().Pods(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return err
	}
	for _, pod := range pods.Items {
		if strings.Contains(pod.Name, constants.IstiodDeploymentName) {
			ctx.CsmLogger().Infof("delete pod %s in namespace %s", pod.Name, namespace)
			err = client.Kube().CoreV1().Pods(namespace).Delete(context.TODO(), pod.Name, metav1.DeleteOptions{})
			if err != nil {
				return err
			}
		}
	}
	return nil
}
