// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	meta0 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// GetDiscoverySelector mocks base method.
func (m *MockServiceInterface) GetDiscoverySelector(ctx context.CsmContext, instanceUUID string) (*meta0.DiscoverySelector, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiscoverySelector", ctx, instanceUUID)
	ret0, _ := ret[0].(*meta0.DiscoverySelector)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiscoverySelector indicates an expected call of GetDiscoverySelector.
func (mr *MockServiceInterfaceMockRecorder) GetDiscoverySelector(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiscoverySelector", reflect.TypeOf((*MockServiceInterface)(nil).GetDiscoverySelector), ctx, instanceUUID)
}

// UpdateDiscoverySelector mocks base method.
func (m *MockServiceInterface) UpdateDiscoverySelector(ctx context.CsmContext, eks bool, instance *meta.Instances, selector *meta0.DiscoverySelector) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDiscoverySelector", ctx, eks, instance, selector)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDiscoverySelector indicates an expected call of UpdateDiscoverySelector.
func (mr *MockServiceInterfaceMockRecorder) UpdateDiscoverySelector(ctx, eks, instance, selector interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDiscoverySelector", reflect.TypeOf((*MockServiceInterface)(nil).UpdateDiscoverySelector), ctx, eks, instance, selector)
}
