package overview

import (
	"os"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"

	mockCluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	mockInstanceModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockInstance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances/mock"
)

var (
	mockDB, _  = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx, _ = ctxCsm.NewCsmContextMock()
)

func TestService_GetSidecarsOverview(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockInstanceModel := mockInstanceModel.NewMockServiceInterface(ctrl)
	mockInstanceService := mockInstance.NewMockServiceInterface(ctrl)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)

	testInstanceIDs := []string{"id-0", "id-1"}
	testInstanceNames := []string{"inst-0", "inst-1"}
	testAccountID := []string{"12345", "2345678"}
	testInstanceType := "standalone"

	var mockInstancesList []meta.Instances
	for i := 0; i < len(testInstanceIDs); i++ {
		mockInstancesList = append(mockInstancesList, meta.Instances{
			InstanceUUID: testInstanceIDs[i],
			InstanceName: testInstanceNames[i],
			AccountId:    testAccountID[i],
			InstanceType: testInstanceType,
			Region:       "bj",
		})
	}

	tests := []struct {
		name    string
		want    []meta.SidecarsOverview
		wantErr bool
	}{
		{
			name: "test-GetSidecarsOverview",
			want: []meta.SidecarsOverview{
				{
					InstanceId:   "id-0",
					InstanceName: "inst-0",
					Num:          2,
				},
				{
					InstanceId:   "id-1",
					InstanceName: "inst-1",
					Num:          2,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				opt:             NewOption(mockDB),
				instanceModel:   mockInstanceModel,
				clusterModel:    mockClusterModel,
				instanceService: mockInstanceService,
			}

			mockCluster := []meta.Cluster{
				{
					InstanceUUID:          "id-0",
					ClusterUUID:           "test-cluster",
					ClusterType:           "primary",
					Region:                "bj",
					IstioInstallNamespace: "istio-test",
				},
				{
					InstanceUUID:          "id-1",
					ClusterUUID:           "test-cluster1",
					ClusterType:           "primary",
					Region:                "bj",
					IstioInstallNamespace: "istio-test",
				},
			}

			mockInstanceModel.EXPECT().GetInstancesByRegion(mockCtx, gomock.Any()).Return(mockInstancesList, nil)
			mockClusterModel.EXPECT().GetAllIstiodClusterByAccountId(mockCtx, gomock.Any()).Return(&mockCluster, nil)
			mockInstanceService.EXPECT().GetActiveSidecarNum(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).
				Return(2, nil).AnyTimes()

			got, err := service.GetSidecarsOverview(mockCtx, "bj")
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSidecarsOverview() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSidecarsOverview() got = %v, want %v", got, tt.want)
			}
		})
	}
}
