package overview

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetInstancesOverview(ctx context.CsmContext, region string) (*meta.InstancesOverview, error)
	GetSidecarsOverview(ctx context.CsmContext, region string) ([]meta.SidecarsOverview, error)
	GetInstancesDetailOverview(ctx context.CsmContext, region string) ([]*meta.InstanceDetailOverview, error)
}
