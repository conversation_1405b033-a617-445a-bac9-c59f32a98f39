package overview

import (
	"sort"
	"strings"
	"sync"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	instance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	instance_service "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/instances"
)

type Service struct {
	opt *Option

	clusterModel    cluster.ServiceInterface
	instanceModel   instance.ServiceInterface
	instanceService instance_service.ServiceInterface
}

func NewOverviewService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt: option,

		clusterModel:    cluster.NewClusterService(cluster.NewOption(gormDB)),
		instanceModel:   instance.NewInstancesService(instance.NewOption(gormDB)),
		instanceService: instance_service.NewInstanceService(instance_service.NewOption(gormDB)),
	}
}

func (s *Service) GetInstancesOverview(ctx context.CsmContext, region string) (*meta.InstancesOverview, error) {
	regionOverview, err := s.instanceModel.CountInstancesByRegion(ctx, region)
	if err != nil {
		return nil, err
	}
	fillZeroByRegion(regionOverview, region)

	// 获取所有网格实例
	instances, err := s.instanceModel.GetInstancesByRegion(ctx, region)
	if err != nil {
		return nil, err
	}

	statusOverview := make(map[meta.InstanceStatus]int64)
	sc := make(chan meta.InstanceStatus)
	var mutex = &sync.Mutex{}

	go func() {
		for status := range sc {
			mutex.Lock()
			if s, ok := statusOverview[status]; ok {
				statusOverview[status] = s + 1
			} else {
				statusOverview[status] = 1
			}
			mutex.Unlock()
		}

	}()

	var wg sync.WaitGroup
	for _, inst := range instances {
		instanceId := inst.InstanceUUID
		wg.Add(1)
		go func() {
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Errorf("goroutine exited abnormally, error %v", e)
				}
				wg.Done()
			}()
			// 获取实例状态
			instanceStatus, statusErr := s.instanceModel.GetInstanceStatus(ctx, instanceId, nil, "")
			if statusErr != nil {
				ctx.CsmLogger().Errorf("failed to get instance status, error %v", statusErr)
			}
			sc <- meta.ConvertInstanceStatus(instanceStatus)
		}()
	}
	wg.Wait()
	close(sc)

	fillZeroByStatus(statusOverview, mutex)

	overview := &meta.InstancesOverview{
		GroupByRegion: regionOverview,
		GroupByStatus: statusOverview,
	}
	return overview, nil
}

func fillZeroByRegion(overview map[string]int64, reg string) {
	if !strings.EqualFold(reg, region.GlobalRegion) {
		if _, ok := overview[reg]; !ok {
			overview[reg] = 0
		}
	}
}

func fillZeroByStatus(overview map[meta.InstanceStatus]int64, mutex *sync.Mutex) {
	if v, ok := overview[meta.InstanceStatus("")]; ok {
		overview[meta.Unknown] = v
		delete(overview, meta.InstanceStatus(""))
	}
	ss := meta.GetAllInstanceStatus()
	for _, s := range ss {
		mutex.Lock()
		if _, ok := overview[s]; !ok {
			overview[s] = 0
		}
		mutex.Unlock()
	}
}

func (s *Service) GetSidecarsOverview(ctx context.CsmContext, region string) ([]meta.SidecarsOverview, error) {
	instances, err := s.instanceModel.GetInstancesByRegion(ctx, region)
	if err != nil {
		return nil, err
	}
	allClusters, err := s.clusterModel.GetAllIstiodClusterByAccountId(ctx, "")
	if err != nil {
		return nil, err
	}

	sidecarsOverview := make([]meta.SidecarsOverview, 0, len(instances))
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 遍历服务实例列表，对于每个实例，筛选出主集群并查询连接的sidecar数量
	for _, i := range instances {
		wg.Add(1)
		go func(i meta.Instances) {
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Errorf("goroutine exited abnormally because: %v", e)
				}
				wg.Done()
			}()
			// 筛选当前实例的主集群，并查询连接sidecar数量
			for _, clusterInfo := range *allClusters {
				if clusterInfo.InstanceUUID == i.InstanceUUID {
					sidecarNum, err := s.instanceService.GetActiveSidecarNum(ctx, i.InstanceUUID, &clusterInfo, meta.MeshType(i.InstanceType))
					if err != nil {
						ctx.CsmLogger().Warnf("instance %s GetActiveSidecarNum has err %v:", i.InstanceUUID, err)
					}
					so := meta.SidecarsOverview{
						InstanceId:   i.InstanceUUID,
						InstanceName: i.InstanceName,
						Num:          sidecarNum,
					}
					mu.Lock()
					sidecarsOverview = append(sidecarsOverview, so)
					mu.Unlock()
					return
				}
			}
		}(i)
	}
	wg.Wait()

	// 排序
	sort.Slice(sidecarsOverview, func(i, j int) bool {
		return sidecarsOverview[i].Num >= sidecarsOverview[j].Num
	})
	return sidecarsOverview, nil
}

func (s *Service) GetInstancesDetailOverview(ctx context.CsmContext, region string) (
	[]*meta.InstanceDetailOverview, error) {
	// 获取网格实例列表
	instances, err := s.instanceModel.GetInstancesByRegion(ctx, region)
	if err != nil {
		return nil, err
	}

	idoList := make([]*meta.InstanceDetailOverview, 0, len(instances))
	var wg sync.WaitGroup
	for _, inst := range instances {
		// 访问数据库
		clusterList, instErr := s.clusterModel.GetAllClusterByInstanceUUID(ctx, inst.InstanceUUID)
		if err != nil {
			return nil, instErr
		}
		var istiodCluster *meta.Cluster
		var meshType meta.MeshType
		for _, c := range *clusterList {
			if c.ClusterType == string(meta.ClusterTypeExternal) || c.ClusterType == string(meta.ClusterTypePrimary) {
				istiodCluster = &c
				meshType = meta.MeshType(inst.InstanceType)
				break
			}
		}

		ido := &meta.InstanceDetailOverview{
			InstanceId:   inst.InstanceUUID,
			InstanceName: inst.InstanceName,
			Region:       inst.Region,
		}
		wg.Add(1)
		// TODO: 添加服务数
		go func() {
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Errorf("goroutine exited abnormally because: ", e)
				}
				wg.Done()
			}()
			// 获取实例状态
			instanceStatus, err := s.instanceModel.GetInstanceStatus(ctx, ido.InstanceId, istiodCluster, meshType)
			if err != nil {
				ctx.CsmLogger().Errorf("failed to get instance status because: ", err)
			}
			ido.Status = meta.ConvertInstanceStatus(instanceStatus)
			// 获取集群运行状态
			runningCluster, clusterCount, err := s.instanceModel.GetInstanceClustersCount(ctx, ido.InstanceId, clusterList)
			if err != nil {
				ctx.CsmLogger().Errorf("failed to get instance clusters count because: ", err)
			}
			ido.ClustersOverview.RunningNum, ido.ClustersOverview.Total = int(runningCluster), int(clusterCount)
			// 获取实例在线Sidecar数量
			sidecarCount, err := s.instanceService.GetActiveSidecarNum(ctx, ido.InstanceId, istiodCluster, meshType)
			if err != nil {
				ctx.CsmLogger().Errorf("failed to get instance online sidecar count because: ", err)
			}
			ido.SidecarNum = sidecarCount
		}()
		idoList = append(idoList, ido)
	}
	wg.Wait()
	return idoList, nil
}
