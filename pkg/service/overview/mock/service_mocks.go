// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// GetInstancesDetailOverview mocks base method.
func (m *MockServiceInterface) GetInstancesDetailOverview(ctx context.CsmContext, region string) ([]*meta.InstanceDetailOverview, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesDetailOverview", ctx, region)
	ret0, _ := ret[0].([]*meta.InstanceDetailOverview)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesDetailOverview indicates an expected call of GetInstancesDetailOverview.
func (mr *MockServiceInterfaceMockRecorder) GetInstancesDetailOverview(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesDetailOverview", reflect.TypeOf((*MockServiceInterface)(nil).GetInstancesDetailOverview), ctx, region)
}

// GetInstancesOverview mocks base method.
func (m *MockServiceInterface) GetInstancesOverview(ctx context.CsmContext, region string) (*meta.InstancesOverview, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesOverview", ctx, region)
	ret0, _ := ret[0].(*meta.InstancesOverview)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesOverview indicates an expected call of GetInstancesOverview.
func (mr *MockServiceInterfaceMockRecorder) GetInstancesOverview(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesOverview", reflect.TypeOf((*MockServiceInterface)(nil).GetInstancesOverview), ctx, region)
}

// GetSidecarsOverview mocks base method.
func (m *MockServiceInterface) GetSidecarsOverview(ctx context.CsmContext, region string) ([]meta.SidecarsOverview, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSidecarsOverview", ctx, region)
	ret0, _ := ret[0].([]meta.SidecarsOverview)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSidecarsOverview indicates an expected call of GetSidecarsOverview.
func (mr *MockServiceInterfaceMockRecorder) GetSidecarsOverview(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSidecarsOverview", reflect.TypeOf((*MockServiceInterface)(nil).GetSidecarsOverview), ctx, region)
}
