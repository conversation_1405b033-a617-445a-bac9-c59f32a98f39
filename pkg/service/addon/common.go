package addon

import (
	"fmt"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// supportedAddOn 已有注册的组件
var supportedAddOn = map[string]*AddonRecord{}

// RegisterNewAddOn 注册组件
func RegisterNewAddOn(name string, record *AddonRecord) {
	supportedAddOn[name] = record
}

// GetAddOnRecord 获取组件注册信息
func GetAddOnRecord(name string) (*AddonRecord, error) {
	record, ok := supportedAddOn[name]
	if !ok {
		return nil, fmt.Errorf("addon %s not found in registered addon list", name)
	}
	return record, nil
}

func ListAllAddons() []string {
	result := make([]string, 0)
	for name, _ := range supportedAddOn {
		result = append(result, name)
	}
	return result
}

func GetAddonMeta(ctx context.CsmContext, addonName string) (*AddOnInfo, error) {
	// 获取组件注册的信息
	addonRecord, err := GetAddOnRecord(addonName)
	if err != nil {
		ctx.CsmLogger().<PERSON><PERSON><PERSON>("Failed get addon record: %s", err)
		return nil, err
	}

	addOnInfo := &AddOnInfo{
		Meta: &Meta{
			Name:               addonRecord.Name,
			Type:               addonRecord.Type,
			ShortIntroduction:  addonRecord.ShortDescription,
			LatestVersion:      "",
			DetailIntroduction: "",
			InstallInfo:        InstallInfo{},
		},
		Instance: nil,
	}
	return addOnInfo, nil
}
