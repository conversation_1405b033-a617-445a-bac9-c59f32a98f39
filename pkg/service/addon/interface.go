package addon

import (
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type AddonsService interface {
	ListAllAddons(ctx csmContext.CsmContext, listStatusConfig *ListConfig) (*AddOnInfoResult, error)
	InstallAddons(ctx csmContext.CsmContext, installConfig *InstallConfig) error
	UnInstallAddons(ctx csmContext.CsmContext, uninstallConfig *UninstallConfig) error
}

type ServiceInterface interface {
	Status(ctx csmContext.CsmContext, listStatusConfig *ListConfig) (*AddOnInfo, error)
	Install(ctx csmContext.CsmContext, installConfig *InstallConfig) error
	Uninstall(ctx csmContext.CsmContext, uninstallConfig *UninstallConfig) error
	Upgrade(ctx csmContext.CsmContext, upgradeConfig *UpgradeConfig) error
	Update(ctx csmContext.CsmContext, updateConfig *UpdateConfig) error
}
