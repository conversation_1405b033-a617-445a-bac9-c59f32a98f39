package addon

import (
	"fmt"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strconv"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	mockInstance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	cce_mock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	helm_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

var (
	mockDB, _ = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
)

func TestListAllAddons(t *testing.T) {
	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../")
	helmOption := helm_meta.NewOption(rootPath, "", "")
	ctrl := gomock.NewController(t)
	cases := []struct {
		name           string
		option         *Option
		listConfig     *ListConfig
		cceService     cce.ClientInterface
		instancesModel instances.ServiceInterface
		expectErr      bool
	}{
		{
			name:   "normal",
			option: NewOption(mockDB, helmOption),
			listConfig: &ListConfig{
				InstanceUUID: instanceUUID,
				Addons:       Registry2IstioAddOnName,
			},
			instancesModel: func() instances.ServiceInterface {
				mockInstancesModel := mockInstance.NewMockServiceInterface(ctrl)
				mockInstancesModel.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).Return(
					&meta.Cluster{ClusterUUID: "test", Region: "gz"},
					meta.StandaloneMeshType, nil).AnyTimes()
				return mockInstancesModel
			}(),
			cceService: func() cce.ClientInterface {
				mockK8sClient := cce_mock.NewMockClientInterface(ctrl)
				fakeClient := kube.NewFakeClient()
				mockK8sClient.EXPECT().NewClient(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
				return mockK8sClient
			}(),
		},
	}
	for _, c := range cases {
		addonService := NewAddonService(c.option)
		addonService.instancesModel = c.instancesModel
		addonService.cceService = c.cceService

		_, err := addonService.ListAllAddons(ctx, c.listConfig)
		if (err != nil) != c.expectErr {
			if err != nil {
				fmt.Errorf("%s", err)
			}
			t.Errorf("Case %s ListAllAddons failed expect isError=%s but actual not",
				c.name, strconv.FormatBool(c.expectErr))
		}
	}
}
