// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	addon "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/addon"
)

// MockAddonsService is a mock of AddonsService interface.
type MockAddonsService struct {
	ctrl     *gomock.Controller
	recorder *MockAddonsServiceMockRecorder
}

// MockAddonsServiceMockRecorder is the mock recorder for MockAddonsService.
type MockAddonsServiceMockRecorder struct {
	mock *MockAddonsService
}

// NewMockAddonsService creates a new mock instance.
func NewMockAddonsService(ctrl *gomock.Controller) *MockAddonsService {
	mock := &MockAddonsService{ctrl: ctrl}
	mock.recorder = &MockAddonsServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAddonsService) EXPECT() *MockAddonsServiceMockRecorder {
	return m.recorder
}

// InstallAddons mocks base method.
func (m *MockAddonsService) InstallAddons(ctx context.CsmContext, installConfig *addon.InstallConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallAddons", ctx, installConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// InstallAddons indicates an expected call of InstallAddons.
func (mr *MockAddonsServiceMockRecorder) InstallAddons(ctx, installConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallAddons", reflect.TypeOf((*MockAddonsService)(nil).InstallAddons), ctx, installConfig)
}

// ListAllAddons mocks base method.
func (m *MockAddonsService) ListAllAddons(ctx context.CsmContext, listStatusConfig *addon.ListConfig) (*addon.AddOnInfoResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllAddons", ctx, listStatusConfig)
	ret0, _ := ret[0].(*addon.AddOnInfoResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllAddons indicates an expected call of ListAllAddons.
func (mr *MockAddonsServiceMockRecorder) ListAllAddons(ctx, listStatusConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllAddons", reflect.TypeOf((*MockAddonsService)(nil).ListAllAddons), ctx, listStatusConfig)
}

// UnInstallAddons mocks base method.
func (m *MockAddonsService) UnInstallAddons(ctx context.CsmContext, uninstallConfig *addon.UninstallConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnInstallAddons", ctx, uninstallConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnInstallAddons indicates an expected call of UnInstallAddons.
func (mr *MockAddonsServiceMockRecorder) UnInstallAddons(ctx, uninstallConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnInstallAddons", reflect.TypeOf((*MockAddonsService)(nil).UnInstallAddons), ctx, uninstallConfig)
}

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// Install mocks base method.
func (m *MockServiceInterface) Install(ctx context.CsmContext, installConfig *addon.InstallConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Install", ctx, installConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// Install indicates an expected call of Install.
func (mr *MockServiceInterfaceMockRecorder) Install(ctx, installConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Install", reflect.TypeOf((*MockServiceInterface)(nil).Install), ctx, installConfig)
}

// Status mocks base method.
func (m *MockServiceInterface) Status(ctx context.CsmContext, listStatusConfig *addon.ListConfig) (*addon.AddOnInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Status", ctx, listStatusConfig)
	ret0, _ := ret[0].(*addon.AddOnInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Status indicates an expected call of Status.
func (mr *MockServiceInterfaceMockRecorder) Status(ctx, listStatusConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Status", reflect.TypeOf((*MockServiceInterface)(nil).Status), ctx, listStatusConfig)
}

// Uninstall mocks base method.
func (m *MockServiceInterface) Uninstall(ctx context.CsmContext, uninstallConfig *addon.UninstallConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Uninstall", ctx, uninstallConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// Uninstall indicates an expected call of Uninstall.
func (mr *MockServiceInterfaceMockRecorder) Uninstall(ctx, uninstallConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Uninstall", reflect.TypeOf((*MockServiceInterface)(nil).Uninstall), ctx, uninstallConfig)
}

// Update mocks base method.
func (m *MockServiceInterface) Update(ctx context.CsmContext, updateConfig *addon.UpdateConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, updateConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockServiceInterfaceMockRecorder) Update(ctx, updateConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockServiceInterface)(nil).Update), ctx, updateConfig)
}

// Upgrade mocks base method.
func (m *MockServiceInterface) Upgrade(ctx context.CsmContext, upgradeConfig *addon.UpgradeConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upgrade", ctx, upgradeConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upgrade indicates an expected call of Upgrade.
func (mr *MockServiceInterfaceMockRecorder) Upgrade(ctx, upgradeConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upgrade", reflect.TypeOf((*MockServiceInterface)(nil).Upgrade), ctx, upgradeConfig)
}
