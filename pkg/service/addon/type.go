package addon

import (
	"github.com/jinzhu/gorm"

	helm_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Option struct {
	DB         *dbutil.DB
	HelmOption *helm_meta.HelmOption
}

func NewOption(d *gorm.DB, helmOption *helm_meta.HelmOption) *Option {
	return &Option{
		HelmOption: helmOption,
		DB:         dbutil.NewDB(d),
	}
}

type AddonRecord struct {
	// 组件的名称
	Name string
	// 组件的 HelmChart Name
	HelmChartName string
	// 组件简介
	ShortDescription string
	// 组件类型
	Type AddOnType
}

type AddOnType string

const (
	// TypeRegistry2Istio is the registry type for CSM add-ons.
	TypeRegistry2Istio AddOnType = "Registry2Istio"
)

type AddOnInstancePhase string

const (
	InstancePhaseRunning    AddOnInstancePhase = "Running"
	InstancePhaseInstalling AddOnInstancePhase = "Installing"
	InstancePhaseAbnormal   AddOnInstancePhase = "Abnormal"
)

// AddOnInfoResult 前后端交互返回的信息
type AddOnInfoResult struct {
	AddOnInfo []*AddOnInfo `json:"result"`
}

// AddOnInfo 表示集群中某个组件的完整信息
type AddOnInfo struct {
	Meta *Meta `json:"meta,omitempty"`
	// 组件的安装实例信息
	Instance *AddOnInstance `json:"instance,omitempty"`
}

// Meta 表示集群中某个组件的元信息
type Meta struct {
	// 组件名称
	Name string `json:"name,omitempty"`
	// 组件类型
	Type AddOnType `json:"type,omitempty"`
	// 组件最新版本
	LatestVersion string `json:"latestVersion,omitempty"`
	// 组件简要介绍
	ShortIntroduction string `json:"shortIntroduction,omitempty"`
	// 组件详细介绍
	DetailIntroduction string `json:"detailIntroduction,omitempty"`
	// 默认参数
	DefaultParams string `json:"defaultParams,omitempty"`
	// 集群是否满足组件安装条件
	InstallInfo InstallInfo `json:"installInfo,omitempty"`
}

// AddOnInstance 表示集群中某个组件的实例部署信息
type AddOnInstance struct {
	// 组件实例的名称
	AddOnInstanceName string `json:"name,omitempty"`
	// 当前组件实例的版本
	InstalledVersion string `json:"installedVersion,omitempty"`
	// 当前组件实例的参数
	Params string `json:"params,omitempty"`
	// Status 组件当前的状态
	Status AddonInstanceStatus `json:"status,omitempty"`
	// UninstallInfo 该组件实例卸载方面的信息(能否卸载,原因)
	UninstallInfo UninstallInfo `json:"uninstallInfo,omitempty"`
	// UpgradeInfo 该组件实例升级方面的信息(能否升级,原因)
	UpgradeInfo UpgradeInfo `json:"upgradeInfo,omitempty"`
}

type AddonInstanceStatus struct {
	Phase   AddOnInstancePhase `json:"phase,omitempty"`
	Code    string             `json:"code,omitempty"`
	TraceID string             `json:"traceID,omitempty"`
	Message string             `json:"message,omitempty"`
}

type InstallInfo struct {
	AllowInstall bool   `json:"allowInstall"`
	Message      string `json:"message,omitempty"`
}

type UninstallInfo struct {
	AllowUninstall bool   `json:"allowUninstall"`
	Message        string `json:"message,omitempty"`
}

type UpgradeInfo struct {
	AllowUpgrade bool   `json:"allowUpgrade"`
	NextVersion  string `json:"nextVersion"`
	Message      string `json:"message,omitempty"`
}

type UpdateInfo struct {
	AllowUpdate bool   `json:"allowUpdate"`
	Message     string `json:"message,omitempty"`
}

type ListConfig struct {
	InstanceUUID      string
	Addons            string
	AddOnName         string
	AddOnInstanceName string
}

type InstallConfig struct {
	InstanceUUID string
	Name         string
	Version      string
	Params       string
}

type UninstallConfig struct {
	InstanceUUID string
	Name         string
}

type UpgradeConfig struct {
	Name          string
	TargetVersion string
	Params        string
}

type UpdateConfig struct {
	Name   string
	Params string
}
