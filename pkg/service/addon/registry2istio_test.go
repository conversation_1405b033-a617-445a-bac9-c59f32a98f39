package addon

import (
	"context"
	"fmt"
	"path"
	"runtime"
	"strconv"
	"testing"

	"github.com/golang/mock/gomock"
	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/helm"
	helmmock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/helm/mock"
	helm_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	kubemock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube/mock"
)

var (
	ctx, _       = csmContext.NewCsmContextMock()
	instanceUUID = "csm-nxzqosxx"
	instanceName = "tanjunchen-test"
)

func buildMockDeployment(name, namespace string) *v1.Deployment {
	return &v1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Spec: v1.DeploymentSpec{
			Selector: &metav1.LabelSelector{
				MatchLabels: nil,
			},
		},
	}
}

func buildMockPod(name, namespace string) *corev1.Pod {
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name:  name,
					Image: "1.0.0",
				},
			},
		},
	}
}

func TestNewRegistry2Istio(t *testing.T) {
	ctrl := gomock.NewController(t)
	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../")
	helmOption := helm_meta.NewOption(rootPath, "", "")
	cases := []struct {
		name        string
		k8SClient   kube.Client
		helmService helm.ServiceInterface
		helmOption  *helm_meta.HelmOption
		expectErr   bool
	}{
		{
			name:      "nil k8sclient",
			k8SClient: nil,
			expectErr: true,
		},
		{
			name:      "nil helmClient",
			k8SClient: kubemock.NewMockClient(ctrl),
			expectErr: true,
		},
		{
			name:        "normal",
			k8SClient:   kubemock.NewMockClient(ctrl),
			helmService: helmmock.NewMockServiceInterface(ctrl),
			helmOption:  helmOption,
			expectErr:   false,
		},
	}

	for _, c := range cases {
		_, err := NewRegistry2Istio(c.k8SClient, c.helmService, c.helmOption)
		if (err != nil) != c.expectErr {
			t.Errorf("Case %s TestNewRegistry2Istio failed expect isError=%s but actual not", c.name, strconv.FormatBool(c.expectErr))
		}
	}
}

func TestInstall(t *testing.T) {
	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../")
	helmOption := helm_meta.NewOption(rootPath, "", "")
	ctrl := gomock.NewController(t)
	cases := []struct {
		name          string
		k8SClient     kube.Client
		helmService   helm.ServiceInterface
		helmOption    *helm_meta.HelmOption
		installConfig *InstallConfig
		expectErr     bool
	}{
		{
			name:       "normal",
			k8SClient:  kubemock.NewMockClient(ctrl),
			helmOption: helmOption,
			helmService: func() helm.ServiceInterface {
				mockHelmService := helmmock.NewMockServiceInterface(ctrl)
				mockHelmService.EXPECT().YAML(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				return mockHelmService
			}(),
			installConfig: &InstallConfig{
				InstanceUUID: instanceUUID,
				Name:         Registry2IstioAddOnName,
				Params:       "consulAddress: consul:8500",
			},
			expectErr: false,
		},
	}
	for _, c := range cases {
		registry2Istio, err := NewRegistry2Istio(c.k8SClient, c.helmService, c.helmOption)
		if err != nil {
			panic(err)
		}
		err = registry2Istio.Install(ctx, c.installConfig)
		if (err != nil) != c.expectErr {
			if err != nil {
				fmt.Errorf("%s", err)
			}
			t.Errorf("Case %s Install failed expect isError=%s but actual not", c.name, strconv.FormatBool(c.expectErr))
		}
	}
}

func TestUnInstall(t *testing.T) {
	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../")
	helmOption := helm_meta.NewOption(rootPath, "", "")
	ctrl := gomock.NewController(t)
	cases := []struct {
		name          string
		k8SClient     kube.Client
		helmService   helm.ServiceInterface
		helmOption    *helm_meta.HelmOption
		installConfig *UninstallConfig
		expectErr     bool
	}{
		{
			name:       "normal",
			k8SClient:  kubemock.NewMockClient(ctrl),
			helmOption: helmOption,
			helmService: func() helm.ServiceInterface {
				mockHelmService := helmmock.NewMockServiceInterface(ctrl)
				mockHelmService.EXPECT().YAML(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				return mockHelmService
			}(),
			installConfig: &UninstallConfig{
				InstanceUUID: instanceUUID,
				Name:         Registry2IstioAddOnName,
			},
			expectErr: false,
		},
	}
	for _, c := range cases {
		registry2Istio, err := NewRegistry2Istio(c.k8SClient, c.helmService, c.helmOption)
		if err != nil {
			panic(err)
		}
		err = registry2Istio.Uninstall(ctx, c.installConfig)
		if (err != nil) != c.expectErr {
			if err != nil {
				fmt.Errorf("%s", err)
			}
			t.Errorf("Case %s UnInstall failed expect isError=%s but actual not", c.name, strconv.FormatBool(c.expectErr))
		}
	}
}

func TestStatus(t *testing.T) {
	_, filename, _, _ := runtime.Caller(0)
	rootPath := path.Join(path.Dir(filename), "../../../")
	helmOption := helm_meta.NewOption(rootPath, "", "")
	ctrl := gomock.NewController(t)
	cases := []struct {
		name        string
		k8SClient   kube.Client
		helmService helm.ServiceInterface
		helmOption  *helm_meta.HelmOption
		listConfig  *ListConfig
		expectErr   bool
	}{
		{
			name: "normal",
			k8SClient: func() kube.ExtendedClient {
				mockK8sClient := kube.NewFakeClient()
				mockDeploy := buildMockDeployment(Registry2IstioAddOnName, rootNamespaceName)
				_, _ = mockK8sClient.Kube().AppsV1().Deployments(rootNamespaceName).Create(context.TODO(), mockDeploy, metav1.CreateOptions{})

				mockPod := buildMockPod(Registry2IstioAddOnName, rootNamespaceName)
				_, _ = mockK8sClient.Kube().CoreV1().Pods(rootNamespaceName).Create(context.TODO(), mockPod, metav1.CreateOptions{})
				return mockK8sClient
			}(),
			helmOption: helmOption,
			helmService: func() helm.ServiceInterface {
				mockHelmService := helmmock.NewMockServiceInterface(ctrl)
				mockHelmService.EXPECT().YAML(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				return mockHelmService
			}(),
			listConfig: &ListConfig{
				InstanceUUID: instanceUUID,
				AddOnName:    Registry2IstioAddOnName,
			},
			expectErr: false,
		},
	}
	for _, c := range cases {
		registry2Istio, err := NewRegistry2Istio(c.k8SClient, c.helmService, c.helmOption)
		if err != nil {
			panic(err)
		}
		_, err = registry2Istio.Status(ctx, c.listConfig)
		if (err != nil) != c.expectErr {
			if err != nil {
				fmt.Errorf("%s", err)
			}
			t.Errorf("Case %s Status failed expect isError=%s but actual not", c.name, strconv.FormatBool(c.expectErr))
		}
	}
}
