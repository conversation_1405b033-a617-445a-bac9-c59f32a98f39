package addon

import (
	"fmt"
	"sort"
	"strings"
	"sync"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/helm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

// Service for addon
type Service struct {
	option         *Option
	cceService     cce.ClientInterface
	instancesModel instances.ServiceInterface
	addonsService  ServiceInterface
}

// NewAddonService 初始化 addon
func NewAddonService(option *Option) *Service {
	gormDB := option.DB.DB
	if option.HelmOption == nil {
		option.HelmOption = meta.NewOption("", "", "")
	}
	addonService := &Service{
		option:         NewOption(gormDB, option.HelmOption),
		cceService:     cce.NewClientService(),
		instancesModel: instances.NewInstancesService(instances.NewOption(gormDB)),
	}
	return addonService
}

func (s *Service) ListAllAddons(ctx csmContext.CsmContext, listConfig *ListConfig) (*AddOnInfoResult, error) {
	addOnInfoResults := &AddOnInfoResult{}

	// 获取查询参数
	addonsStr := listConfig.Addons

	// 整理查询参数
	targetAddOns := make([]string, 0)
	if addonsStr != "" {
		targetAddOns = strings.Split(addonsStr, ",")
	} else {
		targetAddOns = ListAllAddons()
	}

	ctx.CsmLogger().Infof("Target AddOns: %s", targetAddOns)

	addonLen := len(targetAddOns)

	wait := sync.WaitGroup{}
	wait.Add(addonLen)
	mu := &sync.Mutex{}

	result := make([]*AddOnInfo, 0)

	errCh := make(chan error, addonLen)
	for _, addonName := range targetAddOns {
		localAddonName := addonName
		go func() {
			defer wait.Done()
			addOn, err := s.getAddOn(ctx, listConfig.InstanceUUID, localAddonName)
			if err != nil {
				ctx.CsmLogger().Errorf("Failed to get addon: %v", err)
				errCh <- err
				return
			}
			lc := ListConfig{AddOnName: localAddonName}
			addOnInfo, err := addOn.Status(ctx, &lc)
			if err != nil {
				ctx.CsmLogger().Errorf("Failed to get addon status: %v", err)
				errCh <- err
				return
			}
			mu.Lock()
			result = append(result, addOnInfo)
			mu.Unlock()
		}()

	}
	wait.Wait()

	// 按组件类型和名称排序
	sort.Slice(result, func(i, j int) bool {
		if result[i].Meta.Type != result[j].Meta.Type {
			return string(result[i].Meta.Type) < string(result[j].Meta.Type)
		}
		return result[i].Meta.Name < result[j].Meta.Name
	})
	addOnInfoResults.AddOnInfo = result
	return addOnInfoResults, nil
}

func (s *Service) getAddOn(ctx csmContext.CsmContext, instanceUUID, addonName string) (ServiceInterface, error) {
	// 主集群实例对应的 instanceID
	instanceCluster, meshType, err := s.instancesModel.GetInstanceIstiodCluster(ctx, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("GetInstanceIstiodCluster error %v", err)
		return nil, err
	}

	// 用户集群 k8s client
	k8sClient, err := s.cceService.NewClient(ctx, instanceCluster.Region, instanceCluster.ClusterUUID, meshType)
	if err != nil {
		ctx.CsmLogger().Errorf("cce primary NewClient error %v", err)
		return nil, err
	}

	// helm 工具
	helmService := helm.NewService(s.option.HelmOption)

	switch addonName {
	case Registry2IstioAddOnName:
		registry2Istio, registry2IstioErr := NewRegistry2Istio(k8sClient, helmService, s.option.HelmOption)
		if registry2IstioErr != nil {
			return nil, registry2IstioErr
		}
		return registry2Istio, nil
	default:
		return nil, fmt.Errorf("unsupport addon %s", addonName)
	}
}

func (s *Service) InstallAddons(ctx csmContext.CsmContext, installConfig *InstallConfig) error {
	addOn, err := s.getAddOn(ctx, installConfig.InstanceUUID, installConfig.Name)
	if err != nil {
		ctx.CsmLogger().Errorf("Fail to get addon error %v", err)
		return err
	}

	err = addOn.Install(ctx, installConfig)
	if err != nil {
		ctx.CsmLogger().Errorf("Fail to install addon %s: %v", installConfig.Name, err)
		return err
	}
	return nil
}

func (s *Service) UnInstallAddons(ctx csmContext.CsmContext, uninstallConfig *UninstallConfig) error {
	addOn, err := s.getAddOn(ctx, uninstallConfig.InstanceUUID, uninstallConfig.Name)
	if err != nil {
		ctx.CsmLogger().Errorf("Fail to get addon error %v", err)
		return err
	}

	err = addOn.Uninstall(ctx, uninstallConfig)
	if err != nil {
		ctx.CsmLogger().Errorf("Fail to uninstall addon %s: %v", uninstallConfig.Name, err)
		return err
	}
	return nil
}
