package addon

import (
	"context"
	"fmt"
	"strings"

	"gopkg.in/yaml.v3"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/helm"
	helm_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

const (
	Registry2IstioAddOnName          = "registry2istio"
	Registry2IstioAddOnHelmChartName = "registry2istio"
	rootNamespaceName                = "istio-system"
)

func init() {
	RegisterNewAddOn(Registry2IstioAddOnName,
		&AddonRecord{
			Name:             Registry2IstioAddOnName,
			HelmChartName:    Registry2IstioAddOnHelmChartName,
			ShortDescription: "registry2istio 支持注册中心（consul），通过 Service Entry 管理注册中心中的服务",
			Type:             TypeRegistry2Istio,
		})
}

type Registry2IstioParams struct {
	Token         string `yaml:"token"`
	ConsulAddress string `yaml:"consulAddress"`
}

// Registry2IstioService for addon
type Registry2IstioService struct {
	helmOption  *helm_meta.HelmOption
	k8sClient   kube.Client
	helmService helm.ServiceInterface
}

// NewRegistry2Istio 初始化 registry2istio addon
func NewRegistry2Istio(k8SClient kube.Client, helmService helm.ServiceInterface, helmOption *helm_meta.HelmOption) (*Registry2IstioService, error) {
	if k8SClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}

	if helmService == nil {
		return nil, fmt.Errorf("user helm service is nil")
	}

	if helmOption == nil {
		helmOption = helm_meta.NewOption("", "", "")
	}

	registry2IstioService := &Registry2IstioService{
		helmOption:  helmOption,
		k8sClient:   k8SClient,
		helmService: helmService,
	}
	return registry2IstioService, nil
}

func (s *Registry2IstioService) Status(ctx csmContext.CsmContext, listConfig *ListConfig) (*AddOnInfo, error) {
	// 1. 获取组件基本信息
	addOnInfo, err := GetAddonMeta(ctx, listConfig.AddOnName)
	if err != nil {
		return nil, err
	}
	addOnInfo.Meta.InstallInfo = InstallInfo{
		AllowInstall: true,
	}

	isExist, imageTag, status := s.checkRegistry2IstioStatus(ctx, listConfig)
	var addonInstance *AddOnInstance
	if isExist {
		addonInstance = &AddOnInstance{
			AddOnInstanceName: listConfig.Addons,
			InstalledVersion:  imageTag,
			Params:            "# No Params",
			Status:            *status,
			UninstallInfo: UninstallInfo{
				AllowUninstall: true,
				Message:        "",
			},
		}
	}
	addOnInfo.Instance = addonInstance

	return addOnInfo, nil
}

func (s *Registry2IstioService) checkRegistry2IstioStatus(ctx csmContext.CsmContext, listConfig *ListConfig) (bool, string, *AddonInstanceStatus) {
	// 检查 Deployment
	deploy, err := s.k8sClient.Kube().AppsV1().Deployments(rootNamespaceName).Get(context.TODO(), listConfig.AddOnName, metav1.GetOptions{})
	if err != nil {
		return false, "", nil
	}

	// 镜像
	imageTag := ""
	for _, container := range deploy.Spec.Template.Spec.Containers {
		if container.Name == listConfig.AddOnName {
			imageID := container.Image
			imageIDSlice := strings.SplitN(imageID, ":", 2)
			if len(imageIDSlice) == 2 {
				imageTag = imageIDSlice[1]
			}
			break
		}
	}

	// 检查 Pod 是否 Ready
	podLabelSelector := deploy.Spec.Selector
	pods, err := s.k8sClient.Kube().CoreV1().Pods(rootNamespaceName).List(context.TODO(),
		metav1.ListOptions{ResourceVersion: "0", LabelSelector: labels.Set(podLabelSelector.MatchLabels).String()})
	if err != nil {
		ctx.CsmLogger().Errorf("Get get plugin related pods: %w", err)
		return true, imageTag, &AddonInstanceStatus{
			Phase:   InstancePhaseAbnormal,
			Code:    "InternalServerError",
			TraceID: ctx.RequestID(),
			Message: "fail to find registry2istio pods",
		}
	}

	if len(pods.Items) > 0 {
		for _, curPod := range pods.Items {
			if util.CheckPodReady(&curPod) != nil {
				return true, imageTag, &AddonInstanceStatus{
					Phase:   InstancePhaseAbnormal,
					Code:    "PodNotReady",
					TraceID: ctx.RequestID(),
					Message: "registry2istio pods not ready",
				}
			}
		}
	}
	return true, imageTag, &AddonInstanceStatus{
		Phase: InstancePhaseRunning,
	}
}

func (s *Registry2IstioService) Install(ctx csmContext.CsmContext, installConfig *InstallConfig) error {
	installArgs := []string{"-n ", rootNamespaceName}
	// 解析特定组件安装参数
	var yamlConfig Registry2IstioParams
	if err := yaml.Unmarshal([]byte(installConfig.Params), &yamlConfig); err != nil {
		return fmt.Errorf("error parsing yaml params: %w", err)
	}
	if len(yamlConfig.ConsulAddress) == 0 {
		return fmt.Errorf("consulAddress is nil")
	}
	args := fmt.Sprintf(" --set registry2istio.consulAddress=%s "+
		" --set registry2istio.token=%s", yamlConfig.ConsulAddress, yamlConfig.Token)
	installArgs = append(installArgs, args)

	installYaml, err := s.helmService.YAML(ctx, s.helmOption, installConfig.Name, installArgs)
	if err != nil {
		outError := fmt.Errorf("fail to generate addon %s install yaml", installConfig.Name)
		return outError
	}
	err = kube.CreateOrUpdateK8sResource(ctx, s.k8sClient, installYaml)
	if err != nil {
		return err
	}
	return nil
}

func (s *Registry2IstioService) Uninstall(ctx csmContext.CsmContext, uninstallConfig *UninstallConfig) error {
	installArgs := []string{" -n ", rootNamespaceName}
	installYaml, err := s.helmService.YAML(ctx, s.helmOption, uninstallConfig.Name, installArgs)
	if err != nil {
		return err
	}
	err = kube.DeleteK8sResource(ctx, s.k8sClient, installYaml)
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("Uninstall addons %s success", uninstallConfig.Name)
	return nil
}

func (s *Registry2IstioService) Upgrade(ctx csmContext.CsmContext, upgradeConfig *UpgradeConfig) error {
	return fmt.Errorf("not support upgrade")
}

func (s *Registry2IstioService) Update(ctx csmContext.CsmContext, updateConfig *UpdateConfig) error {
	return fmt.Errorf("not support update")
}
