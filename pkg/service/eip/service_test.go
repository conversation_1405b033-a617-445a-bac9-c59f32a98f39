package eip

import (
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	eipMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/eip/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

func TestBindEIP(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		eip       string
		expectErr error
	}{
		{
			name:      "correct-BindEip",
			eip:       "*******",
			expectErr: nil,
		},
		{
			name:      "correct-BindEip",
			eip:       "",
			expectErr: errors.New("invalid eip binding parameters"),
		},
	}

	for _, testInfo := range testInfos {
		mockEipModel := eipMock.NewMockServiceInterface(ctrl)
		service := &Service{
			eipModel: mockEipModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			if testInfo.eip != "" {
				mockEipModel.EXPECT().BindEip(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			}
			err := service.BindEIP(mockCtx, "BLB", "blb1", testInfo.eip, "bj")
			if testInfo.eip == "" {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestIsEIPBinded(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectRes bool
		expectErr error
	}{
		{
			name:      "correct-IsEipBinded",
			expectRes: true,
			expectErr: nil,
		},
	}

	for _, testInfo := range testInfos {
		mockEipModel := eipMock.NewMockServiceInterface(ctrl)
		service := &Service{
			eipModel: mockEipModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockEipModel.EXPECT().IsEipBinded(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.expectRes, "", nil)
			res, err := service.IsEIPBinded(mockCtx, "BLB", "blb1", "*******", "bj")

			if testInfo.expectRes {
				assert.True(t, res)
			}
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestListEIP(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "success",
			expectErr: nil,
		},
	}

	for _, testInfo := range testInfos {
		mockEipModel := eipMock.NewMockServiceInterface(ctrl)
		service := &Service{
			eipModel: mockEipModel,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockEipModel.EXPECT().ListEip(mockCtx, gomock.Any(), gomock.Any()).Return(nil, nil)
			_, err := service.ListEIP(mockCtx, "gz")

			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}
