package eip

import (
	eipSDK "github.com/baidubce/bce-sdk-go/services/eip"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	CreateEIP(ctx context.CsmContext, args *eipSDK.CreateEipArgs, region string) (*eipSDK.CreateEipResult, error)
	BindEIP(ctx context.CsmContext, instanceType, instanceId, eip, region string) error
	IsEIPBinded(ctx context.CsmContext, instanceType, instanceId, eip, region string) (bool, error)
	ListEIP(ctx context.CsmContext, region string) (*eipSDK.ListEipResult, error)
}
