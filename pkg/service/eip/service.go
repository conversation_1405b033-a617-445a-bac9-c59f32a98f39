package eip

import (
	"errors"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"

	eipSDK "github.com/baidubce/bce-sdk-go/services/eip"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/eip"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type Service struct {
	eipModel eip.ServiceInterface
}

func NewEIPService() *Service {
	return &Service{
		eipModel: eip.NewService(eip.NewOption()),
	}
}

func (s *Service) CreateEIP(ctx context.CsmContext, args *eipSDK.CreateEipArgs, region string) (*eipSDK.CreateEipResult, error) {
	if args == nil {
		return nil, csmErr.NewInvalidParameterInputValueException("args")
	}
	return s.eipModel.CreateEip(ctx, args, region)
}

func (s *Service) BindEIP(ctx context.CsmContext, instanceType, instanceId, ip, region string) (err error) {
	if len(instanceType) == 0 || len(instanceId) == 0 || len(ip) == 0 || len(region) == 0 {
		errMsg := "invalid eip binding parameters"
		err = errors.New(errMsg)
		ctx.CsmLogger().Errorf(errMsg)
		return err
	}
	args := &eipSDK.BindEipArgs{
		InstanceType: instanceType,
		InstanceId:   instanceId,
	}
	return s.eipModel.BindEip(ctx, args, ip, region)
}

func (s *Service) IsEIPBinded(ctx context.CsmContext, instanceType, instanceId, ip, region string) (res bool, err error) {
	if len(instanceType) == 0 || len(instanceId) == 0 || len(ip) == 0 || len(region) == 0 {
		errMsg := "invalid eip status checking parameters"
		err = errors.New(errMsg)
		ctx.CsmLogger().Errorf(errMsg)
		return false, err
	}
	args := &eipSDK.ListEipArgs{
		Eip:          ip,
		InstanceType: instanceType,
		InstanceId:   instanceId,
		Status:       string(meta.BindedEIPStatus),
	}
	isBinded, _, err := s.eipModel.IsEipBinded(ctx, args, region)
	return isBinded, err
}

func (s *Service) ListEIP(ctx context.CsmContext, region string) (*eipSDK.ListEipResult, error) {
	args := &eipSDK.ListEipArgs{
		Status: string(meta.BindedEIPStatus),
	}
	return s.eipModel.ListEip(ctx, args, region)
}
