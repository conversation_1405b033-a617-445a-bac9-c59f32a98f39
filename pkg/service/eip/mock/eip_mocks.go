// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	eip "github.com/baidubce/bce-sdk-go/services/eip"
	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// BindEIP mocks base method.
func (m *MockServiceInterface) BindEIP(ctx context.CsmContext, instanceType, instanceId, eip, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindEIP", ctx, instanceType, instanceId, eip, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindEIP indicates an expected call of BindEIP.
func (mr *MockServiceInterfaceMockRecorder) BindEIP(ctx, instanceType, instanceId, eip, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindEIP", reflect.TypeOf((*MockServiceInterface)(nil).BindEIP), ctx, instanceType, instanceId, eip, region)
}

// CreateEIP mocks base method.
func (m *MockServiceInterface) CreateEIP(ctx context.CsmContext, args *eip.CreateEipArgs, region string) (*eip.CreateEipResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEIP", ctx, args, region)
	ret0, _ := ret[0].(*eip.CreateEipResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEIP indicates an expected call of CreateEIP.
func (mr *MockServiceInterfaceMockRecorder) CreateEIP(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEIP", reflect.TypeOf((*MockServiceInterface)(nil).CreateEIP), ctx, args, region)
}

// IsEIPBinded mocks base method.
func (m *MockServiceInterface) IsEIPBinded(ctx context.CsmContext, instanceType, instanceId, eip, region string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsEIPBinded", ctx, instanceType, instanceId, eip, region)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsEIPBinded indicates an expected call of IsEIPBinded.
func (mr *MockServiceInterfaceMockRecorder) IsEIPBinded(ctx, instanceType, instanceId, eip, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsEIPBinded", reflect.TypeOf((*MockServiceInterface)(nil).IsEIPBinded), ctx, instanceType, instanceId, eip, region)
}

// ListEIP mocks base method.
func (m *MockServiceInterface) ListEIP(ctx context.CsmContext, region string) (*eip.ListEipResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListEIP", ctx, region)
	ret0, _ := ret[0].(*eip.ListEipResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEIP indicates an expected call of ListEIP.
func (mr *MockServiceInterfaceMockRecorder) ListEIP(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEIP", reflect.TypeOf((*MockServiceInterface)(nil).ListEIP), ctx, region)
}
