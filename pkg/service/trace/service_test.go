package trace

import (
	"context"
	"os"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	_ "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	clusterMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	instanceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	contextCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

var (
	ContextIAMUser = "User"
	mockDB, _      = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
)

var valuesData = `{
      "defaultConfig": {
        "discoveryAddress": "***********:15012",
        "holdApplicationUntilProxyStarts": true,
        "meshId": "csm-488iuxrh",
        "proxyMetadata": {},
        "tracing": {
          "sampling": 1,
          "zipkin": {
            "address": "zipkin.istio-system:9411"
          }
        }
      },
      "enablePrometheusMerge": true,
      "rootNamespace": "istio-system",
      "trustDomain": "cluster.local"
    }`

// buildMockConfigmap 生成一个包含指定数据的 ConfigMap，用于测试。
// 参数：
//   - name (string)：ConfigMap 的名称。
//   - namespace (string)：ConfigMap 所在的命名空间。
//   - data (string)：ConfigMap 中需要存储的数据。
//
// 返回值（*v1.ConfigMap）：生成的 ConfigMap 对象。
func buildMockConfigmap(name, namespace, data string) *v1.ConfigMap {
	return &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Data: map[string]string{
			constants.IstioConfigMapMeshName: data,
		},
	}
}

const (
	testInstanceUUID          = "csm-xxxxxx"
	testInstanceName          = "test-xxxx"
	testInstanceManageScope   = "cluster"
	testClusterUUID           = "cce-cccccc"
	testClusterName           = "test01-xxx"
	testRegion                = "bj"
	testInstanceType          = "standalone"
	testIstioInstallNamespace = "istio-system"
)

// TestService_UpdateTrace 测试函数UpdateTrace，更新追踪信息。
// 参数t *testing.T：表示当前测试用例的对象，用于记录测试过程中发生的错误。
// 返回值bool：表示是否出现错误，如果出现错误则返回true，否则返回false。
func TestService_UpdateTrace(t *testing.T) {
	instance := meta.Instances{
		InstanceUUID:          testInstanceUUID,
		InstanceName:          testInstanceName,
		IstioInstallNamespace: testIstioInstallNamespace,
		InstanceManageScope:   testInstanceManageScope,
		InstanceType:          testInstanceType,
		TraceEnabled:          csm.Bool(true),
	}
	cluster := meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}
	traceInfo := meta.TraceInfo{
		TraceEnabled: true,
		SamplingRate: 1,
		Service:      "Jaeger/Zipkin",
		Address:      "zipkin.istio-system:9411",
	}

	tests := []struct {
		name       string
		instanceID string
		traceInfo  *meta.TraceInfo
		want       *meta.TraceInfo
		wantErr    bool
	}{
		{
			name:       "test-UpdateTrace",
			instanceID: testInstanceUUID,
			traceInfo:  &traceInfo,
			want:       &traceInfo,
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := contextCsm.MockNewCsmContext()
			ctx.Set(ContextIAMUser, &iam.User{
				ID:   "",
				Name: "",
				Domain: &iam.Domain{
					ID:   "123",
					Name: "",
				},
				Password: "",
			})
			ctrl := gomock.NewController(t)
			mockInstanceModel := instanceMock.NewMockServiceInterface(ctrl)
			mockCceClientService := mockCceService.NewMockClientInterface(ctrl)
			mockClusterModel := clusterMock.NewMockServiceInterface(ctrl)
			service := &Service{
				opt:            NewOption(mockDB),
				clusterModel:   mockClusterModel,
				instancesModel: mockInstanceModel,
				cceService:     mockCceClientService,
			}
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(ctx, gomock.Any()).Return(&instance, nil)
			mockClusterModel.EXPECT().GetIstiodCluster(ctx, gomock.Any(), gomock.Any()).Return(&cluster, nil)

			fakeClient := kube.NewFakeClient()
			mockCceClientService.EXPECT().NewClient(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			_, err := fakeClient.Kube().CoreV1().ConfigMaps(testIstioInstallNamespace).
				Create(context.TODO(), buildMockConfigmap(constants.IstioConfimapName, testIstioInstallNamespace, valuesData),
					metav1.CreateOptions{})
			if err != nil {
				return
			}

			got, err := service.UpdateTrace(ctx, tt.instanceID, tt.traceInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateTrace() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateTrace() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestService_GetTrace 测试函数TestService_GetTrace，用于获取追踪信息
// 参数t *testing.T：表示单元测试的对象，用于输出日志和报错信息
// 参数instance meta.Instances：包含实例的相关信息，包括实例UUID、名称、管理范围等
// 参数cluster meta.Cluster：包含集群的相关信息，包括集群UUID、名称、区域等
// 参数traceInfo *meta.TraceInfo：包含追踪信息，包括是否开启追踪、采样率等
// 参数want *meta.TraceInfo：期望返回的追踪信息，包括是否开启追踪、采样率等
// 参数wantErr bool：期望返回的错误类型，如果为false则表示没有错误，如果为true则表示有错误
// 返回值bool：无返回值
func TestService_GetTrace(t *testing.T) {
	instance := meta.Instances{
		InstanceUUID:          testInstanceUUID,
		InstanceName:          testInstanceName,
		IstioInstallNamespace: testIstioInstallNamespace,
		InstanceManageScope:   testInstanceManageScope,
		InstanceType:          testInstanceType,
		TraceEnabled:          csm.Bool(true),
	}
	cluster := meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}
	traceInfo := meta.TraceInfo{
		TraceEnabled: true,
		SamplingRate: 1,
		Service:      "Jaeger/Zipkin",
		Address:      "zipkin.istio-system:9411",
	}

	tests := []struct {
		name       string
		instanceID string
		traceInfo  *meta.TraceInfo
		want       *meta.TraceInfo
		wantErr    bool
	}{
		{
			name:       "test-UpdateTrace",
			instanceID: testInstanceUUID,
			traceInfo:  &traceInfo,
			want:       &traceInfo,
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := contextCsm.MockNewCsmContext()
			ctx.Set(ContextIAMUser, &iam.User{
				ID:   "",
				Name: "",
				Domain: &iam.Domain{
					ID:   "123",
					Name: "",
				},
				Password: "",
			})
			ctrl := gomock.NewController(t)
			mockInstanceModel := instanceMock.NewMockServiceInterface(ctrl)
			mockCceClientService := mockCceService.NewMockClientInterface(ctrl)
			mockClusterModel := clusterMock.NewMockServiceInterface(ctrl)
			service := &Service{
				opt:            NewOption(mockDB),
				clusterModel:   mockClusterModel,
				instancesModel: mockInstanceModel,
				cceService:     mockCceClientService,
			}
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(ctx, gomock.Any()).Return(&instance, nil)
			mockClusterModel.EXPECT().GetIstiodCluster(ctx, gomock.Any(), gomock.Any()).Return(&cluster, nil)

			fakeClient := kube.NewFakeClient()
			mockCceClientService.EXPECT().NewClient(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			_, err := fakeClient.Kube().CoreV1().ConfigMaps(testIstioInstallNamespace).
				Create(context.TODO(), buildMockConfigmap(constants.IstioConfimapName, testIstioInstallNamespace, valuesData),
					metav1.CreateOptions{})
			if err != nil {
				return
			}

			gotTraceInfo, err := service.GetTrace(ctx, tt.instanceID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTrace() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotTraceInfo, tt.want) {
				t.Errorf("GetTrace() gotTraceInfo = %v, want %v", gotTraceInfo, tt.want)
			}
		})
	}
}
