package trace

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/jinzhu/copier"
	"gopkg.in/yaml.v3"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil/rollback"
)

type Service struct {
	opt            *Option
	instancesModel instances.ServiceInterface
	clusterModel   cluster.ServiceInterface
	cceService     cce.ClientInterface
}

func NewTraceService(option *Option) *Service {
	gormDB := option.DB.DB
	svc := &Service{
		opt:            option,
		instancesModel: instances.NewInstancesService(instances.NewOption(gormDB)),
		clusterModel:   cluster.NewClusterService(cluster.NewOption(gormDB)),
		cceService:     cce.NewClientService(),
	}
	return svc
}

// UpdateTrace 更新链路追踪
func (service *Service) UpdateTrace(ctx csmContext.CsmContext, instanceID string, traceRequest *meta.TraceInfo) (newTraceInfo *meta.TraceInfo, err error) {
	tx := service.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	instance, err := service.instancesModel.GetInstanceByInstanceUUID(ctx, instanceID)
	if err != nil {
		return nil, err
	}
	if *instance.TraceEnabled != traceRequest.TraceEnabled {
		newInstance := &meta.Instances{}
		err = copier.Copy(newInstance, instance)
		if err != nil {
			return nil, err
		}

		newInstance.TraceEnabled = &traceRequest.TraceEnabled
		if _, err = service.instancesModel.UpdateInstance(ctx, instance, newInstance); err != nil {
			return nil, err
		}
	}
	if !traceRequest.TraceEnabled {
		traceRequest.Address = constants.Zipkin + "." + instance.IstioInstallNamespace + constants.Port
	}
	istiodCluster, err := service.clusterModel.GetIstiodCluster(ctx, instance.InstanceUUID, instance.InstanceType)
	if err != nil {
		return nil, err
	}
	tx.Commit()

	// 修改 configmap trace参数
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer func() {
		cancel()
	}()
	client, err := service.cceService.NewClient(ctx, istiodCluster.Region, istiodCluster.ClusterUUID, meta.MeshType(instance.InstanceType))
	if err != nil {
		return nil, err
	}
	istioConfigMap, err := client.Kube().CoreV1().ConfigMaps(istiodCluster.IstioInstallNamespace).Get(cxt,
		constants.IstioConfimapName, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	configmap, err := ModifyConfigmap(istioConfigMap, traceRequest)
	if err != nil {
		return nil, err
	}

	newConfigmap, err := client.Kube().CoreV1().ConfigMaps(instance.IstioInstallNamespace).
		Update(cxt, configmap, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	newTraceInfo = &meta.TraceInfo{
		TraceEnabled: traceRequest.TraceEnabled,
	}
	traceInfo, err := ParseConfigmap(newConfigmap, newTraceInfo)
	if err != nil {
		return nil, err
	}
	return traceInfo, nil
}

// GetTrace 查看链路追踪
func (service *Service) GetTrace(ctx csmContext.CsmContext, instanceID string) (traceInfo *meta.TraceInfo, err error) {
	tx := service.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()

	instance, err := service.instancesModel.GetInstanceByInstanceUUID(ctx, instanceID)
	if err != nil {
		return nil, err
	}
	if instance == nil {
		return nil, csmErr.NewInvalidParameterValueException(
			fmt.Sprintf("invalid instanceID %s, instance is empty", instanceID))
	}
	traceInfo = &meta.TraceInfo{
		TraceEnabled: *instance.TraceEnabled,
	}

	if *instance.TraceEnabled {
		istiodCluster, err := service.clusterModel.GetIstiodCluster(ctx, instance.InstanceUUID, instance.InstanceType)
		if err != nil {
			return nil, err
		}
		tx.Commit()
		client, err := service.cceService.NewClient(ctx, istiodCluster.Region, istiodCluster.ClusterUUID, meta.MeshType(instance.InstanceType))
		if err != nil {
			return nil, err
		}
		istioConfigMap, err := client.Kube().CoreV1().ConfigMaps(instance.IstioInstallNamespace).Get(context.TODO(),
			constants.IstioConfimapName, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		traceInfo, err = ParseConfigmap(istioConfigMap, traceInfo)
		if err != nil {
			return nil, err
		}
		return traceInfo, nil
	}
	return traceInfo, nil
}

// ModifyConfigmap 修改 Configmap 中的 Istio 配置，包括追踪采样率和 Zipkin 地址等信息
// 参数：configmap *v1.ConfigMap - 需要修改的 Configmap，包含 Istio 配置信息
//
//	traceRequest *meta.TraceInfo - 追踪请求信息，包括采样率和 Zipkin 地址
//
// 返回值：*v1.ConfigMap - 修改后的 Configmap，包含更新后的 Istio 配置信息
//
//	error - 如果发生错误则返回错误
func ModifyConfigmap(configmap *v1.ConfigMap, traceRequest *meta.TraceInfo) (*v1.ConfigMap, error) {
	meshValue := configmap.Data[constants.IstioConfigMapMeshName]
	mapData := make(map[string]any)
	err := yaml.Unmarshal([]byte(meshValue), &mapData)
	if err != nil {
		return nil, err
	}
	// 更新 istio configmap trace 信息
	((mapData[constants.IstiodefaultConfigName].(map[string]any))[constants.Tracing].(map[string]any))[constants.Sampling] =
		traceRequest.SamplingRate
	(((mapData[constants.IstiodefaultConfigName].(map[string]any))[constants.Tracing].(map[string]any))[constants.Zipkin].
	(map[string]any))[constants.Address] = traceRequest.Address

	newValue, err := json.Marshal(mapData)
	if err != nil {
		return nil, err
	}
	configmap.Data[constants.IstioConfigMapMeshName] = string(newValue)

	return configmap, nil
}

// ParseConfigmap 函数ParseConfigmap解析一个v1.ConfigMap类型的配置文件，并返回一个*meta.TraceInfo类型的跟踪信息和一个error类型的错误信息
func ParseConfigmap(configmap *v1.ConfigMap, traceInfo *meta.TraceInfo) (*meta.TraceInfo, error) {
	meshValue := configmap.Data[constants.IstioConfigMapMeshName]
	mapData := make(map[string]any)
	err := yaml.Unmarshal([]byte(meshValue), &mapData)
	if err != nil {
		return nil, err
	}
	sampling := ((mapData[constants.IstiodefaultConfigName].(map[string]any))[constants.Tracing].(map[string]any))[constants.Sampling]
	switch sampling.(type) {
	case float64:
		traceInfo.SamplingRate = sampling.(float64)
	case int:
		traceInfo.SamplingRate = float64(sampling.(int))
	}
	traceInfo.Address = (((mapData[constants.IstiodefaultConfigName].(map[string]any))[constants.Tracing].(map[string]any))[constants.Zipkin].
	(map[string]any))[constants.Address].(string)
	traceInfo.Service = "Jaeger/Zipkin"
	return traceInfo, nil
}
