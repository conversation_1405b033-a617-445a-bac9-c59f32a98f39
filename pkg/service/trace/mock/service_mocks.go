// Code generated by MockGen. DO NOT EDIT.
// Source: ./interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// GetTrace mocks base method.
func (m *MockServiceInterface) GetTrace(ctx context.CsmContext, instanceID string) (*meta.TraceInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTrace", ctx, instanceID)
	ret0, _ := ret[0].(*meta.TraceInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTrace indicates an expected call of GetTrace.
func (mr *MockServiceInterfaceMockRecorder) GetTrace(ctx, instanceID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrace", reflect.TypeOf((*MockServiceInterface)(nil).GetTrace), ctx, instanceID)
}

// UpdateTrace mocks base method.
func (m *MockServiceInterface) UpdateTrace(ctx context.CsmContext, instanceID string, traceInfo *meta.TraceInfo) (*meta.TraceInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTrace", ctx, instanceID, traceInfo)
	ret0, _ := ret[0].(*meta.TraceInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTrace indicates an expected call of UpdateTrace.
func (mr *MockServiceInterfaceMockRecorder) UpdateTrace(ctx, instanceID, traceInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTrace", reflect.TypeOf((*MockServiceInterface)(nil).UpdateTrace), ctx, instanceID, traceInfo)
}
