// Code generated by MockGen. DO NOT EDIT.
// Source: ./interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// GetSidecarQuota mock base method.
func (m *MockServiceInterface) GetSidecarQuota(ctx context.CsmContext, instanceId string) (*meta.SidecarQuota, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSidecarQuota", ctx, instanceId)
	ret0, _ := ret[0].(*meta.SidecarQuota)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSidecarQuota indicates an expected call of GetSidecarQuota.
func (mr *MockServiceInterfaceMockRecorder) GetSidecarQuota(ctx, instanceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSidecarQuota", reflect.TypeOf((*MockServiceInterface)(nil).GetSidecarQuota), ctx, instanceId)
}

// SidecarInjection mock base method.
func (m *MockServiceInterface) SidecarInjection(ctx context.CsmContext, injectionParam *meta.InjectionParam) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SidecarInjection", ctx, injectionParam)
	ret0, _ := ret[0].(error)
	return ret0
}

// SidecarInjection indicates an expected call of SidecarInjection.
func (mr *MockServiceInterfaceMockRecorder) SidecarInjection(ctx, injectionParam interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SidecarInjection", reflect.TypeOf((*MockServiceInterface)(nil).SidecarInjection), ctx, injectionParam)
}

// UpdateSidecarQuota mock base method.
func (m *MockServiceInterface) UpdateSidecarQuota(ctx context.CsmContext, quota *meta.QuotaParam, updateType string, paasType meta.PaaSType) (*meta.SidecarQuota, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSidecarQuota", ctx, quota, updateType, paasType)
	ret0, _ := ret[0].(*meta.SidecarQuota)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSidecarQuota indicates an expected call of UpdateSidecarQuota.
func (mr *MockServiceInterfaceMockRecorder) UpdateSidecarQuota(ctx, quota, updateType, paasType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSidecarQuota", reflect.TypeOf((*MockServiceInterface)(nil).UpdateSidecarQuota), ctx, quota, updateType, paasType)
}
