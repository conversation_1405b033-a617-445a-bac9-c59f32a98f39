package sidecar

import (
	"context"
	"encoding/json"
	"math"
	"strconv"
	"sync"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil/rollback"
)

type Service struct {
	opt *Option

	instancesModel instances.ServiceInterface
	clusterModel   cluster.ServiceInterface
	cceService     cce.ClientInterface
}

func NewInstanceService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:            option,
		instancesModel: instances.NewInstancesService(instances.NewOption(gormDB)),
		clusterModel:   cluster.NewClusterService(cluster.NewOption(gormDB)),
		cceService:     cce.NewClientService(),
	}
}

// GetSidecarQuota 获取网格实例对应的istio sidecar资源配置
func (s *Service) GetSidecarQuota(ctx csmContext.CsmContext, instanceUUID string) (sidecarQuota *meta.SidecarQuota, err error) {
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()

	// 获取istiod Cluster
	istiodCluster, meshType, err := s.instancesModel.GetInstanceIstiodCluster(ctx, instanceUUID)
	tx.Commit()
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get instance primaryCluster because: ", err)
		return meta.DefaultSidecarQuota(), nil
	}
	namespace := istiodCluster.IstioInstallNamespace
	client, err := s.cceService.NewClient(ctx, istiodCluster.Region, istiodCluster.ClusterUUID, meshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create client because: ", err)
		return meta.DefaultSidecarQuota(), nil
	}
	configmap, err := client.Kube().CoreV1().ConfigMaps(namespace).Get(context.TODO(),
		constants.ConfigMapName, metav1.GetOptions{})
	if err != nil {
		return meta.DefaultSidecarQuota(), nil
	}
	sidecarQuota, err = s.SidecarQuota(ctx, configmap)
	if err != nil {
		ctx.CsmLogger().Errorf("Instance %v, SidecarQuota() error %v", instanceUUID, err)
		return meta.DefaultSidecarQuota(), nil
	}
	return sidecarQuota, nil
}

// CheckCpuSuf cpu配额单位验证
func CheckCpuSuf(suf []byte, num []byte) (cpuSuf string, cpuNum int, err error) {
	i, err2 := strconv.ParseInt(string(num), 10, 64)
	if err2 != nil {
		return "", 0, err
	}
	if string(suf) == "" {
		cpuSuf = constants.CpuQuotaSuf

		cpuNum, _ = strconv.Atoi(strconv.FormatInt(i*1000, 10))
	} else {
		cpuSuf = string(suf)
		cpuNum, err = strconv.Atoi(strconv.FormatInt(i, 10))
		if err != nil {
			return "", 0, err
		}
	}
	return cpuSuf, cpuNum, nil
}

// CheckMemSuf memory配额单位验证
func CheckMemSuf(suf []byte, num []byte) (memSuf string, memNum int, err error) {
	i, err2 := strconv.ParseInt(string(num), 10, 64)
	if err2 != nil {
		return "", 0, err
	}
	if string(suf) == constants.Gi {
		memSuf = constants.MemQuotaSuf
		memNum, _ = strconv.Atoi(strconv.FormatInt(i*1024, 10))
	} else {
		memSuf = string(suf)
		memNum, err = strconv.Atoi(strconv.FormatInt(i, 10))
		if err != nil {
			return "", 0, err
		}
	}
	return memSuf, memNum, nil
}

// SidecarQuota 格式化sidecar配额信息
func (s *Service) SidecarQuota(ctx csmContext.CsmContext, configmap *v1.ConfigMap) (sidecarQuota *meta.SidecarQuota,
	err error) {
	values := configmap.Data[constants.Value]
	if values == "" {
		return nil, err
	}
	var v meta.Values
	e1 := json.Unmarshal([]byte(values), &v)
	if e1 != nil {
		return nil, e1
	}
	cpuRequest := v.Global.Proxy.Resources.Requests.Cpu()
	memRequest := v.Global.Proxy.Resources.Requests.Memory()
	cpuLimits := v.Global.Proxy.Resources.Limits.Cpu()
	memLimits := v.Global.Proxy.Resources.Limits.Memory()
	//单位解析
	//cpuRequest
	cpuRequestResult := make([]byte, 0, constants.Int64QuantityExpectedBytes)
	cpuRequestNum, cpuRequestSuf := cpuRequest.CanonicalizeBytes(cpuRequestResult)
	cpuReqSuf, cpuReqNum, err := CheckCpuSuf(cpuRequestSuf, cpuRequestNum)
	if err != nil {
		ctx.CsmLogger().Errorf("failed because: ", err)
		return nil, err
	}

	//cpuLimits
	cpuLimitResult := make([]byte, 0, constants.Int64QuantityExpectedBytes)
	cpuLimitsNum, cpuLimitsSuf := cpuLimits.CanonicalizeBytes(cpuLimitResult)
	cpuLimiSuf, cpuLimiNum, err := CheckCpuSuf(cpuLimitsSuf, cpuLimitsNum)
	if err != nil {
		ctx.CsmLogger().Errorf("failed because: ", err)
		return nil, err
	}
	if cpuReqSuf == cpuLimiSuf {
		ctx.CsmLogger().Infof("unit %s ", cpuReqSuf)
	} else {
		return nil, err
	}

	//memoryLimits
	memoryLimitsResult := make([]byte, 0, constants.Int64QuantityExpectedBytes)
	memLimitsNum, memLimitsSuf := memLimits.CanonicalizeBytes(memoryLimitsResult)
	memLimiSuf, memLimiNum, err := CheckMemSuf(memLimitsSuf, memLimitsNum)
	if err != nil {
		ctx.CsmLogger().Errorf("failed because: ", err)
		return nil, err
	}

	//memoryRequest
	memoryRequestResult := make([]byte, 0, constants.Int64QuantityExpectedBytes)
	memRequestNum, memRequestSuf := memRequest.CanonicalizeBytes(memoryRequestResult)
	memReqSuf, memReqNum, err := CheckMemSuf(memRequestSuf, memRequestNum)
	if err != nil {
		ctx.CsmLogger().Errorf("failed because: ", err)
		return nil, err
	}
	if memLimiSuf == memReqSuf {
		ctx.CsmLogger().Infof("unit %s ", memLimiSuf)
	} else {
		return nil, err
	}

	cpuQuota := &meta.CpuQuota{
		Limit:   cpuLimiNum,
		Request: cpuReqNum,
		Unit:    cpuReqSuf,
	}
	memoryQuota := &meta.MemoryQuota{
		Limit:   memLimiNum,
		Request: memReqNum,
		Unit:    memLimiSuf,
	}
	sidecarQuota = &meta.SidecarQuota{
		CpuQuota:    *cpuQuota,
		MemoryQuota: *memoryQuota,
	}
	return sidecarQuota, nil
}

// UpdateSidecarQuota 编辑sidecar配额
func (s *Service) UpdateSidecarQuota(ctx csmContext.CsmContext, quota *meta.QuotaParam, updateType string, paasType meta.PaaSType) (
	sidecarQuota *meta.SidecarQuota, err error) {
	tx := s.opt.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	instance, err := s.instancesModel.GetInstanceByInstanceUUID(ctx, quota.InstanceUUID)
	if err != nil {
		return nil, err
	}
	allCluster := &[]meta.Cluster{}
	if instance.InstanceType == string(meta.StandaloneMeshType) || instance.InstanceType == string(meta.CnapMeshType) {
		//获取所有的cluster
		allCluster, err = s.clusterModel.GetAllClusterByInstanceUUID(ctx, quota.InstanceUUID)
		if err != nil {
			return nil, err
		}
	} else {
		// 获取Hosted Cluster
		hostedCluster, err := s.clusterModel.GetIstiodCluster(ctx, quota.InstanceUUID, string(version.HostingVersionType))
		if err != nil {
			return nil, err
		}
		*allCluster = append(*allCluster, *hostedCluster)

	}
	tx.Commit()

	var newSidecarQuota *meta.SidecarQuota
	var wg sync.WaitGroup
	var errs util.Errors
	for _, c := range *allCluster {
		wg.Add(1)
		go func(c meta.Cluster) {
			cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
			defer func() {
				cancel()
				wg.Done()
			}()
			clusterUuid := c.ClusterUUID
			region := c.Region
			namespace := c.IstioInstallNamespace

			client, err := s.cceService.NewClient(ctx, region, clusterUuid, meta.MeshType(instance.InstanceType))
			if err != nil {
				errs = append(errs, err)
			}

			oldConfigmap, err := client.Kube().CoreV1().ConfigMaps(namespace).Get(cxt,
				constants.ConfigMapName, metav1.GetOptions{})
			if err != nil {
				errs = append(errs, err)
			}

			configmap, err := s.ModifySidecarQuota(ctx, oldConfigmap, quota, updateType, paasType)
			if err != nil {
				ctx.CsmLogger().Errorf("Instance %v, ModifySidecarQuota() error %v", quota.InstanceUUID, err)
				errs = append(errs, err)
			}

			newConfigmap, err := client.Kube().CoreV1().ConfigMaps(namespace).
				Update(cxt, configmap, metav1.UpdateOptions{})
			if err != nil {
				ctx.CsmLogger().Errorf("Instance %v, update() error %v", quota.InstanceUUID, err)
				errs = append(errs, err)
			}

			newSidecarQuota, err = s.SidecarQuota(ctx, newConfigmap)
			if err != nil {
				ctx.CsmLogger().Errorf("Instance %v, SidecarQuota() error %v", quota.InstanceUUID, err)
				errs = append(errs, err)
			}
		}(c)
	}
	wg.Wait()
	return newSidecarQuota, nil
}

// ModifySidecarQuota 根据PaaSType给sidecar配额赋值
func (s *Service) ModifySidecarQuota(ctx csmContext.CsmContext, oldConfigmap *v1.ConfigMap, quota *meta.QuotaParam,
	updateType string, paasType meta.PaaSType) (*v1.ConfigMap, error) {
	value := make(map[string]interface{})
	oldValue := oldConfigmap.Data[constants.Value]
	err := json.Unmarshal([]byte(oldValue), &value)
	if err != nil {
		return nil, err
	}

	//根据输入配额值修改对应参数
	if updateType == constants.Cpu {
		((((value["global"].(map[string]interface{}))["proxy"].(map[string]interface{}))["resources"].
		(map[string]interface{}))["limits"].(map[string]interface{}))["cpu"] =
			resource.MustParse(strconv.Itoa(quota.CpuQuota.Limit) + constants.CpuQuotaSuf)
		((((value["global"].(map[string]interface{}))["proxy"].(map[string]interface{}))["resources"].
		(map[string]interface{}))["requests"].(map[string]interface{}))["cpu"] =
			resource.MustParse(strconv.Itoa(quota.CpuQuota.Request) + constants.CpuQuotaSuf)
	} else {
		((((value["global"].(map[string]interface{}))["proxy"].(map[string]interface{}))["resources"].
		(map[string]interface{}))["limits"].(map[string]interface{}))["memory"] =
			resource.MustParse(strconv.Itoa(quota.MemoryQuota.Limit) + constants.MemQuotaSuf)
		((((value["global"].(map[string]interface{}))["proxy"].(map[string]interface{}))["resources"].
		(map[string]interface{}))["requests"].(map[string]interface{}))["memory"] =
			resource.MustParse(strconv.Itoa(quota.MemoryQuota.Request) + constants.MemQuotaSuf)
	}
	// 适配eks，ephemeral-storage默认2Gi。当前一站式主集群为cce集群，不需要添加以下配置，只有remote集群（eks）需要
	if paasType == meta.PaaSTypeEKS {
		ctx.CsmLogger().Info("ModifySidecarQuota eks")
		((((value["global"].(map[string]interface{}))["proxy"].(map[string]interface{}))["resources"].
		(map[string]interface{}))["limits"].(map[string]interface{}))["ephemeral-storage"] =
			resource.MustParse("2" + constants.Gi)
		((((value["global"].(map[string]interface{}))["proxy"].(map[string]interface{}))["resources"].
		(map[string]interface{}))["requests"].(map[string]interface{}))["ephemeral-storage"] =
			resource.MustParse("2" + constants.Gi)
		eksCpuRequest := getEksCpuQuota(quota.CpuQuota.Request)
		eksCpuLimit := getEksCpuQuota(quota.CpuQuota.Limit)
		((((value["global"].(map[string]interface{}))["proxy"].(map[string]interface{}))["resources"].
		(map[string]interface{}))["limits"].(map[string]interface{}))["eks.baidu-int.com/cpu"] =
			resource.MustParse(strconv.Itoa(eksCpuLimit))
		((((value["global"].(map[string]interface{}))["proxy"].(map[string]interface{}))["resources"].
		(map[string]interface{}))["requests"].(map[string]interface{}))["eks.baidu-int.com/cpu"] =
			resource.MustParse(strconv.Itoa(eksCpuRequest))
		// 重新设置cpu
		((((value["global"].(map[string]interface{}))["proxy"].(map[string]interface{}))["resources"].
		(map[string]interface{}))["limits"].(map[string]interface{}))["cpu"] =
			resource.MustParse(strconv.Itoa(int(float64(eksCpuLimit)*1000/15)) + constants.CpuQuotaSuf)
		((((value["global"].(map[string]interface{}))["proxy"].(map[string]interface{}))["resources"].
		(map[string]interface{}))["requests"].(map[string]interface{}))["cpu"] =
			resource.MustParse(strconv.Itoa(int(float64(eksCpuRequest)*1000/15)) + constants.CpuQuotaSuf)
	}

	newValue, err := json.Marshal(value)
	if err != nil {
		return nil, err
	}
	oldConfigmap.Data[constants.Value] = string(newValue)

	return oldConfigmap, nil
}

func getEksCpuQuota(cpuQuota int) int {
	// 如果cpuQuota值无效，则返回默认值
	if cpuQuota <= 0 {
		return 15
	}
	result := float64(cpuQuota) * 15 / 1000
	return int(math.Ceil(result))
}

// SidecarInjection namespace级别sidecar自动注入
func (s *Service) SidecarInjection(ctx csmContext.CsmContext, injectionParam *meta.InjectionParam) error {
	region, err := s.clusterModel.GetRegionByClusterUUID(ctx, injectionParam.ClusterUUID)
	if err != nil {
		return err
	}
	client, err := s.cceService.NewClient(ctx, region, injectionParam.ClusterUUID, meta.StandaloneMeshType)
	if err != nil {
		return err
	}
	labels := make(map[string]string)
	if injectionParam.Enabled == "true" {
		labels[constants.SidecarInject] = constants.Enabled
	} else {
		labels[constants.SidecarInject] = constants.Disabled
	}
	metadata := map[string]interface{}{"metadata": map[string]map[string]string{"labels": labels}}
	patchData, err := json.Marshal(metadata)
	if err != nil {
		return err
	}
	newNamespace, err := client.Kube().CoreV1().Namespaces().Patch(context.TODO(), injectionParam.Namespace,
		types.StrategicMergePatchType, patchData, metav1.PatchOptions{})
	if err != nil {
		return err
	}
	ctx.CsmLogger().Infof("namespace labels is %v", newNamespace.Labels)
	return nil
}
