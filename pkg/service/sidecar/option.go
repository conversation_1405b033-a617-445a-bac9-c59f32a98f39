package sidecar

import (
	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Option struct {
	DB            *dbutil.DB
	EksAccountIds []string
}

func NewOption(d *gorm.DB) *Option {
	return &Option{
		DB:            dbutil.NewDB(d),
		EksAccountIds: viper.GetStringSlice(meta.EksAccountIds),
	}
}
