package sidecar

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetSidecarQuota(ctx context.CsmContext, instanceId string) (*meta.SidecarQuota, error)
	UpdateSidecarQuota(ctx context.CsmContext, quota *meta.QuotaParam, updateType string, paasType meta.PaaSType) (
		sidecarQuota *meta.SidecarQuota, err error)
	SidecarInjection(ctx context.CsmContext, injectionParam *meta.InjectionParam) error
}
