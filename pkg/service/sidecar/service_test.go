package sidecar

import (
	"context"
	"os"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	clusterMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	instanceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	contextCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	contextMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context/mock"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

const ContextIAMUser = "User"

var valuesData = `{
      "global": {
        "caAddress": "",
        "caName": "",
        "configCluster": false,
        "configValidation": true,
        "defaultNodeSelector": {},
        "defaultPodDisruptionBudget": {
          "enabled": true
        },
        "defaultResources": {
          "requests": {
            "cpu": "10m"
          }
        },
        "enabled": true,
        "externalIstiod": false,
        "hub": "registry.baidubce.com/csm-offline",
        "imagePullPolicy": "",
        "imagePullSecrets": [],
        "istioNamespace": "istio-systemcsm-mqlc4juc",
        "istiod": {
          "enableAnalysis": false
        },
        "jwtPolicy": "third-party-jwt",
        "logAsJson": false,
        "logging": {
          "level": "default:info"
        },
        "meshID": "csm-mqlc4juc",
        "meshNetworks": {},
        "mountMtlsCerts": false,
        "multiCluster": {
          "clusterName": "bj-cce-0k355plq",
          "enabled": false
        },
        "namespace": "istio-systemcsm-mqlc4juc",
        "network": "bj",
        "omitSidecarInjectorConfigMap": false,
        "oneNamespace": false,
        "operatorManageWebhooks": false,
        "pilotCertProvider": "istiod",
        "priorityClassName": "",
        "proxy": {
          "autoInject": "enabled",
          "clusterDomain": "cluster.local",
          "componentLogLevel": "misc:error",
          "enableCoreDump": false,
          "excludeIPRanges": "",
          "excludeInboundPorts": "",
          "excludeOutboundPorts": "",
          "holdApplicationUntilProxyStarts": false,
          "image": "proxyv2",
          "includeIPRanges": "*",
          "includeInboundPorts": "*",
          "includeOutboundPorts": "",
          "logLevel": "warning",
          "privileged": false,
          "readinessFailureThreshold": 30,
          "readinessInitialDelaySeconds": 1,
          "readinessPeriodSeconds": 2,
          "resources": {
            "limits": {
              "cpu": "2000m",
              "memory": "1024Mi"
            },
            "requests": {
              "cpu": "100m",
              "memory": "128Mi"
            }
          },
          "statusPort": 15020,
          "tracer": "zipkin"
        },
        "proxy_init": {
          "image": "proxyv2",
          "resources": {
            "limits": {
              "cpu": "2000m",
              "memory": "1024Mi"
            },
            "requests": {
              "cpu": "10m",
              "memory": "10Mi"
            }
          }
        },
        "remotePilotAddress": "",
        "sds": {
          "token": {
            "aud": "istio-ca"
          }
        },
        "sts": {
          "servicePort": 0
        },
        "tag": "1.13.2",
        "tracer": {
          "datadog": {
            "address": "$(HOST_IP):8126"
          },
          "lightstep": {
            "accessToken": "",
            "address": ""
          },
          "stackdriver": {
            "debug": false,
            "maxNumberOfAnnotations": 200,
            "maxNumberOfAttributes": 200,
            "maxNumberOfMessageEvents": 200
          },
          "zipkin": {
            "address": ""
          }
        },
        "useMCP": false
      },
      "istio_cni": {
        "enabled": false
      },
      "revision": "",
      "sidecarInjectorWebhook": {
        "alwaysInjectSelector": [],
        "defaultTemplates": [],
        "enableNamespacesByDefault": false,
        "injectedAnnotations": {},
        "neverInjectSelector": [],
        "objectSelector": {
          "autoInject": true,
          "enabled": true
        },
        "rewriteAppHTTPProbe": false,
        "templates": {}
      }
    }`
var (
	mockDB, _ = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx   = new(contextMock.CsmContext)
)

func buildMockConfigmap(name, namespace, data string) *v1.ConfigMap {
	return &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Data: map[string]string{
			constants.Value: data,
		},
	}
}

func TestUpdateSidecarQuota(t *testing.T) {
	testInstanceUUID := "csm-xxxxxx"
	testInstanceName := "test-xxxx"
	testClusterUUID := "cce-cccccc"
	testClusterName := "test01-xxx"
	testRegion := "bj"
	testIstioInstallNamespace := "istio-system"
	testInstanceManageScope := "cluster"
	testInstanceType := "hosting"

	ctx := contextCsm.MockNewCsmContext()
	ctx.Set(ContextIAMUser, &iam.User{
		ID:   "",
		Name: "",
		Domain: &iam.Domain{
			ID:   "123",
			Name: "",
		},
		Password: "",
	})

	cluster := meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}
	instance := meta.Instances{
		InstanceUUID:        testInstanceUUID,
		InstanceName:        testInstanceName,
		InstanceManageScope: testInstanceManageScope,
		InstanceType:        testInstanceType,
	}
	ctrl := gomock.NewController(t)
	mockInstanceModel := instanceMock.NewMockServiceInterface(ctrl)
	mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(ctx, gomock.Any()).Return(&instance, nil).AnyTimes()
	mockClusterModel := clusterMock.NewMockServiceInterface(ctrl)
	mockClusterModel.EXPECT().GetIstiodCluster(ctx, gomock.Any(), gomock.Any()).Return(&cluster, nil).AnyTimes()

	tests := []struct {
		name             string
		context          contextCsm.CsmContext
		quota            *meta.QuotaParam
		wantSidecarQuota *meta.SidecarQuota
		updateType       string
		paasType         meta.PaaSType
		wantErr          bool
	}{
		// TODO: Add test cases.
		{
			name:    "CpuQuota edit",
			context: ctx,
			quota: &meta.QuotaParam{
				InstanceUUID: "csm-xxxxxx",
				SidecarQuota: meta.SidecarQuota{
					CpuQuota: meta.CpuQuota{
						Request: 100,
						Limit:   1000,
					},
				},
			},
			wantSidecarQuota: &meta.SidecarQuota{
				CpuQuota: meta.CpuQuota{
					Request: 100,
					Limit:   1000,
					Unit:    "m",
				},
				MemoryQuota: meta.MemoryQuota{
					Request: 128,
					Limit:   1024,
					Unit:    "Mi",
				},
			},
			updateType: "cpu",
			paasType:   meta.PaaSTypeCCE,
			wantErr:    false,
		},
		{
			name:    "eks CpuQuota edit",
			context: ctx,
			quota: &meta.QuotaParam{
				InstanceUUID: "csm-xxxxxx",
				SidecarQuota: meta.SidecarQuota{
					CpuQuota: meta.CpuQuota{
						Request: 500,
						Limit:   500,
					},
				},
			},
			wantSidecarQuota: &meta.SidecarQuota{
				CpuQuota: meta.CpuQuota{
					Request: 533,
					Limit:   533,
					Unit:    "m",
				},
				MemoryQuota: meta.MemoryQuota{
					Request: 128,
					Limit:   1024,
					Unit:    "Mi",
				},
			},
			updateType: "cpu",
			paasType:   meta.PaaSTypeEKS,
			wantErr:    false,
		},
	}

	mockCceClientService := mockCceService.NewMockClientInterface(ctrl)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				clusterModel:   mockClusterModel,
				instancesModel: mockInstanceModel,
				cceService:     mockCceClientService,
			}
			fakeClient := kube.NewFakeClient()
			mockCceClientService.EXPECT().NewClient(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			fakeClient.Kube().CoreV1().ConfigMaps(testIstioInstallNamespace).
				Create(context.TODO(), buildMockConfigmap(constants.ConfigMapName, testIstioInstallNamespace, valuesData),
					metav1.CreateOptions{})

			gotSidecarQuota, err := s.UpdateSidecarQuota(ctx, tt.quota, tt.updateType, tt.paasType)
			if err != nil {
				t.Errorf("UpdateSidecarQuota() error = %v, wantErr %v", err, tt.wantErr)
			} else {
				assert.Equal(t, tt.wantSidecarQuota, gotSidecarQuota)
			}
		})
	}
}

func buildNameSpace() *v1.Namespace {
	return &v1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "sample",
		},
	}
}

func TestSidecarInjection(t *testing.T) {
	ctx := contextCsm.MockNewCsmContext()

	region := "bj"
	ctrl := gomock.NewController(t)
	mockClusterModel := clusterMock.NewMockServiceInterface(ctrl)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockInstanceModel := instanceMock.NewMockServiceInterface(ctrl)

	tests := []struct {
		name           string
		injectionParam *meta.InjectionParam
		wantErr        bool
	}{
		// TODO: Add test cases.
		{
			name: "sidecar inject",
			injectionParam: &meta.InjectionParam{
				InstanceUUID: "csm-xxxxx",
				Namespace:    "sample",
				ClusterUUID:  "cce-vuv3eh1o",
				Enabled:      "true",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		service := &Service{
			opt:            NewOption(mockDB),
			clusterModel:   mockClusterModel,
			cceService:     mockCceService,
			instancesModel: mockInstanceModel,
		}
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := kube.NewFakeClient()
			mockClusterModel.EXPECT().GetRegionByClusterUUID(ctx, gomock.Any()).Return(region, nil)
			mockCceService.EXPECT().NewClient(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			fakeClient.Kube().CoreV1().Namespaces().Create(context.TODO(), buildNameSpace(), metav1.CreateOptions{})

			if err := service.SidecarInjection(ctx, tt.injectionParam); (err != nil) != tt.wantErr {
				t.Errorf("SidecarInjection() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGetSidecarQuota(t *testing.T) {
	ctx, _ := contextCsm.NewCsmContextMock()
	testInstanceUUID := "instance-xxx"
	testClusterUUID := "cce-cccccc"
	testClusterName := "test01-xxx"
	testRegion := "bj"
	testIstioInstallNamespace := "istio-system"

	cluster := meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
	}

	ctrl := gomock.NewController(t)
	mockInstanceModel := instanceMock.NewMockServiceInterface(ctrl)
	mockCceService := mockCceService.NewMockClientInterface(ctrl)
	mockInstanceModel.EXPECT().GetInstanceIstiodCluster(ctx, gomock.Any()).Return(&cluster, meta.StandaloneMeshType, nil)

	tests := []struct {
		name             string
		instanceUUID     string
		wantSidecarQuota *meta.SidecarQuota
		wantErr          bool
	}{
		// TODO: Add test cases.
		{
			name:         "test getSidecarQuota",
			instanceUUID: testInstanceUUID,
			wantSidecarQuota: &meta.SidecarQuota{
				CpuQuota: meta.CpuQuota{
					Request: 100,
					Limit:   2000,
					Unit:    "m",
				},
				MemoryQuota: meta.MemoryQuota{
					Request: 128,
					Limit:   1024,
					Unit:    "Mi",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				opt:            NewOption(mockDB),
				instancesModel: mockInstanceModel,
				cceService:     mockCceService,
			}

			fakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			fakeClient.Kube().CoreV1().ConfigMaps(testIstioInstallNamespace).
				Create(context.TODO(), buildMockConfigmap(constants.ConfigMapName, testIstioInstallNamespace, valuesData),
					metav1.CreateOptions{})

			gotSidecarQuota, err := s.GetSidecarQuota(ctx, tt.instanceUUID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSidecarQuota() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotSidecarQuota, tt.wantSidecarQuota) {
				t.Errorf("GetSidecarQuota() gotSidecarQuota = %v, want %v", gotSidecarQuota, tt.wantSidecarQuota)
			}
		})
	}
}
