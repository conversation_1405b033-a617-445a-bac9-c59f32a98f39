// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// BlsAddCluster mocks base method.
func (m *MockServiceInterface) BlsAddCluster(ctx context.CsmContext, instanceUUID, cluster, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlsAddCluster", ctx, instanceUUID, cluster, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// BlsAddCluster indicates an expected call of BlsAddCluster.
func (mr *MockServiceInterfaceMockRecorder) BlsAddCluster(ctx, instanceUUID, cluster, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlsAddCluster", reflect.TypeOf((*MockServiceInterface)(nil).BlsAddCluster), ctx, instanceUUID, cluster, region)
}

// BlsClose mocks base method.
func (m *MockServiceInterface) BlsClose(ctx context.CsmContext, region, instanceUUID string) (*meta.BlsCloseResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlsClose", ctx, region, instanceUUID)
	ret0, _ := ret[0].(*meta.BlsCloseResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BlsClose indicates an expected call of BlsClose.
func (mr *MockServiceInterfaceMockRecorder) BlsClose(ctx, region, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlsClose", reflect.TypeOf((*MockServiceInterface)(nil).BlsClose), ctx, region, instanceUUID)
}

// BlsList mocks base method.
func (m *MockServiceInterface) BlsList(ctx context.CsmContext, region, instanceUUID string) (*meta.BlsListResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlsList", ctx, region, instanceUUID)
	ret0, _ := ret[0].(*meta.BlsListResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BlsList indicates an expected call of BlsList.
func (mr *MockServiceInterfaceMockRecorder) BlsList(ctx, region, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlsList", reflect.TypeOf((*MockServiceInterface)(nil).BlsList), ctx, region, instanceUUID)
}

// BlsOpen mocks base method.
func (m *MockServiceInterface) BlsOpen(ctx context.CsmContext, region, instanceUUID, name string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlsOpen", ctx, region, instanceUUID, name)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BlsOpen indicates an expected call of BlsOpen.
func (mr *MockServiceInterfaceMockRecorder) BlsOpen(ctx, region, instanceUUID, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlsOpen", reflect.TypeOf((*MockServiceInterface)(nil).BlsOpen), ctx, region, instanceUUID, name)
}

// BlsRemoveCluster mocks base method.
func (m *MockServiceInterface) BlsRemoveCluster(ctx context.CsmContext, instanceUUID, cluster, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlsRemoveCluster", ctx, instanceUUID, cluster, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// BlsRemoveCluster indicates an expected call of BlsRemoveCluster.
func (mr *MockServiceInterfaceMockRecorder) BlsRemoveCluster(ctx, instanceUUID, cluster, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlsRemoveCluster", reflect.TypeOf((*MockServiceInterface)(nil).BlsRemoveCluster), ctx, instanceUUID, cluster, region)
}

// CSMAgentCheck mocks base method.
func (m *MockServiceInterface) CSMAgentCheck(ctx context.CsmContext, instanceUUID string) (*meta.AgentCheckResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CSMAgentCheck", ctx, instanceUUID)
	ret0, _ := ret[0].(*meta.AgentCheckResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CSMAgentCheck indicates an expected call of CSMAgentCheck.
func (mr *MockServiceInterfaceMockRecorder) CSMAgentCheck(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CSMAgentCheck", reflect.TypeOf((*MockServiceInterface)(nil).CSMAgentCheck), ctx, instanceUUID)
}

// ClusterAgentCheck mocks base method.
func (m *MockServiceInterface) ClusterAgentCheck(ctx context.CsmContext, clusterID, region string) (*meta.AgentCheckResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterAgentCheck", ctx, clusterID, region)
	ret0, _ := ret[0].(*meta.AgentCheckResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClusterAgentCheck indicates an expected call of ClusterAgentCheck.
func (mr *MockServiceInterfaceMockRecorder) ClusterAgentCheck(ctx, clusterID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterAgentCheck", reflect.TypeOf((*MockServiceInterface)(nil).ClusterAgentCheck), ctx, clusterID, region)
}

// LogStoreList mocks base method.
func (m *MockServiceInterface) LogStoreList(ctx context.CsmContext, region string) (*meta.LogStoreListResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LogStoreList", ctx, region)
	ret0, _ := ret[0].(*meta.LogStoreListResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LogStoreList indicates an expected call of LogStoreList.
func (mr *MockServiceInterfaceMockRecorder) LogStoreList(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LogStoreList", reflect.TypeOf((*MockServiceInterface)(nil).LogStoreList), ctx, region)
}
