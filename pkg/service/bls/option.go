package bls

import (
	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

const (
	cloudHostingRegion            = "cloud.hostingRegion"
	cloudHostingRegionClusterId   = "clusterid"
	cloudHostingRegionClusterName = "clustername"
)

type Option struct {
	DB            *dbutil.DB
	HostingRegion interface{}
}

// NewOption NewOption 创建一个新的 Option 实例，并返回指向该实例的指针
// 参数 d *gorm.DB：GORM 数据库连接指针
// 返回值 *Option：指向 Option 类型的指针
func NewOption(d *gorm.DB) *Option {
	hostingRegion := viper.Get(cloudHostingRegion)
	return &Option{
		DB:            dbutil.NewDB(d),
		HostingRegion: hostingRegion,
	}
}

// GetHostingCluster 获取托管集群信息，包括集群ID和集群名称
// 参数：
//
//	region string - 地域名称，不能为空
//
// 返回值：
//
//	string - 集群ID，如果没有找到对应的集群则返回空字符串
//	string - 集群名称，如果没有找到对应的集群则返回空字符串 <MID>"""
func (opt *Option) GetHostingCluster(region string) (string, string) {
	if opt.HostingRegion == nil {
		return "", ""
	}
	allRegions := opt.HostingRegion.(map[string]interface{})
	if allRegions == nil {
		return "", ""
	}
	clusters := allRegions[region]
	if clusters == nil {
		return "", ""
	}
	cluster := clusters.(map[string]interface{})
	if cluster == nil {
		return "", ""
	}
	return cluster[cloudHostingRegionClusterId].(string), cluster[cloudHostingRegionClusterName].(string)
}
