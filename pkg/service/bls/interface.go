package bls

import (
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

type ServiceInterface interface {
	CSMAgentCheck(ctx csmContext.CsmContext, instanceUUID string) (*meta.AgentCheckResult, error)
	ClusterAgentCheck(ctx csmContext.CsmContext, clusterID, region string) (*meta.AgentCheckResult, error)
	LogStoreList(ctx csmContext.CsmContext, region string) (*meta.LogStoreListResult, error)
	BlsList(ctx csmContext.CsmContext, region, instanceUUID string) (res *meta.BlsListResult, err error)
	BlsClose(ctx csmContext.CsmContext, region, instanceUUID string) (res *meta.BlsCloseResult, err error)
	BlsOpen(ctx csmContext.CsmContext, region, instanceUUID, name string) (res bool, err error)
	BlsAddCluster(ctx csmContext.CsmContext, instanceUUID, cluster, region string) (err error)
	BlsRemoveCluster(ctx csmContext.CsmContext, instanceUUID, cluster, region string) (err error)
}
