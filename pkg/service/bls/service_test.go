package bls

import (
	"testing"

	"github.com/baidubce/bce-sdk-go/services/bls/api"
	bls_sdk "github.com/baidubce/bce-sdk-go/services/bls/api"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3"
	bce_cce "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	bls_mock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blsv3/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	cluster_mock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	instance_mock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	model_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	cce_mock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

var (
	testInstanceUUID = "csm-xxxxxx"
	ctx, _           = csmContext.NewCsmContextMock()
	region           = "bj"
	clusterUUID      = "test-cluster"
)

type Task struct {
	mockCluster  model_meta.Cluster
	mockClusters []model_meta.Cluster
	addonFlag    bool
	res          meta.AgentCheckResult
}

// TestNewBlsService 测试函数，用于创建一个新的BlsService实例。
// 参数：
//
//	t *testing.T - *testing.T类型，表示当前测试用例。
//
// 返回值：
//
//	None - 无返回值，但是会检查svc是否为非空指针。
func TestNewBlsService(t *testing.T) {
	svc := NewBlsService(NewOption(nil))
	assert.NotNil(t, svc)
}

// TestCSMAgentCheck 测试CSMAgentCheck函数，包括正常和异常情况下的返回值。
// 参数t是*testing.T类型，用于标记当前函数为单元测试。
// 返回值没有，使用assert库来校验函数的输出是否符合预期。
func TestCSMAgentCheck(t *testing.T) {
	mockClusters := []model_meta.Cluster{{
		Region:      "bj",
		ClusterUUID: "test-cluster1",
		ClusterType: string(model_meta.ClusterTypePrimary),
	}, {

		Region:      "bj",
		ClusterUUID: "test-cluster2",
		ClusterType: string(model_meta.ClusterTypePrimary),
	},
	}

	tasks := []Task{
		{
			mockClusters: mockClusters,
			addonFlag:    true,
			res: meta.AgentCheckResult{
				IsExist: true,
			},
		}, {
			mockClusters: mockClusters,
			addonFlag:    false,
			res: meta.AgentCheckResult{
				IsExist:     false,
				ClusterUUID: "test-cluster1",
			},
		},
	}
	for _, tt := range tasks {
		t.Run("", func(t *testing.T) {

			svc := NewBlsService(NewOption(nil))
			// fmt.Println(tt.addonFlag)
			svc.clusterModel = getMockClusterModel(t, tt)
			svc.cceService = getMockCCEModel(t, tt)
			res, err := svc.CSMAgentCheck(ctx, testInstanceUUID)
			assert.Nil(t, err)
			assert.Equal(t, tt.res, *res)
		})
	}

}

// TestClusterAgentCheckError 测试函数，用于测试ClusterAgentCheck方法返回异常的功能
// 参数t *testing.T：表示单元测试的上下文，不可缺省
// 返回值bool：无返回值
func TestClusterAgentCheck(t *testing.T) {
	mockClusters := model_meta.Cluster{
		Region:      "bj",
		ClusterUUID: "test-cluster1",
		ClusterType: string(model_meta.ClusterTypePrimary),
	}
	tasks := []Task{
		{
			mockCluster: mockClusters,
			addonFlag:   false,
			res: meta.AgentCheckResult{
				IsExist: false,
			},
		}, {
			mockCluster: mockClusters,
			addonFlag:   true,
			res: meta.AgentCheckResult{
				IsExist: true,
			},
		},
	}
	for _, tt := range tasks {
		t.Run("", func(t *testing.T) {
			svc := NewBlsService(NewOption(nil))
			svc.clusterModel = getMockClusterModel(t, tt)
			svc.cceService = getMockCCEModel(t, tt)
			res, err := svc.ClusterAgentCheck(ctx, clusterUUID, region)
			assert.Nil(t, err)
			assert.Equal(t, tt.res.IsExist, res.IsExist)
		})
	}
}

// getMockAddonResponse 返回一个包含bce_cce.AddonInfo的bce_cce.AddonResponse指针，根据参数haveItem确定是否有项目
// haveItem为true时，返回包含一个名称为"test-addon"的bce_cce.AddonInfo的Items列表
// haveItem为false时，返回一个空的Items列表
func getMockAddonResponse(haveItem bool) *bce_cce.AddonResponse {
	if haveItem {
		return &bce_cce.AddonResponse{
			Items: []bce_cce.AddonInfo{
				{
					Meta: bce_cce.Meta{
						Name: "test-addon",
					},
					Instance: bce_cce.AddonInstance{
						Status: bce_cce.AddonInstanceStatus{
							Phase: "Hello",
							Code:  "world",
						},
					},
				},
			},
		}
	} else {
		return &bce_cce.AddonResponse{
			Items: []bce_cce.AddonInfo{},
		}
	}
}

// getMockClusterModel 获取一个模拟的集群模型，用于测试任务类型 tt
// 参数：
//   - t *testing.T: 指向当前正在运行的单元测试的指针
//   - tt Task: 任务类型，包含了需要模拟的集群信息
//
// 返回值：
//   - cluster.ServiceInterface: 实现了 cluster.ServiceInterface 接口的模拟对象，可以被用来模拟集群相关操作
//   - error: 如果有错误发生，则返回该错误
func getMockClusterModel(t *testing.T, tt Task) cluster.ServiceInterface {
	ctrl := gomock.NewController(t)
	mockClusterModel := cluster_mock.NewMockServiceInterface(ctrl)
	mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(ctx, gomock.Any()).
		Return(&tt.mockClusters, nil).AnyTimes()
	return mockClusterModel
}

// getMockCCEModel 获取一个Mock的CCE模型，用于测试。参数为*testing.T和Task类型，返回值为cce.ClientInterface类型
// t: *testing.T，表示当前测试函数的上下文信息
// tt: Task类型，包含了任务相关的信息，包括addonFlag（是否添加插件）等
// 返回值：cce.ClientInterface类型，Mock的CCE模型，可以进行预期的调用
func getMockCCEModel(t *testing.T, tt Task) cce.ClientInterface {
	ctrl := gomock.NewController(t)
	mockCCEModel := cce_mock.NewMockClientInterface(ctrl)
	res := getMockAddonResponse(tt.addonFlag)
	mockCCEModel.EXPECT().GetClusterAddonByClusterId(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(res, nil).AnyTimes()
	// fmt.Println(tt.addonFlag, res.Items)
	return mockCCEModel
}

// TestLogStoreList 是用于测试LogStoreList函数的测试函数
func TestLogStoreList(t *testing.T) {

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		region string
		ctx    csmContext.CsmContext
	}
	type want struct {
		logStoreResult *bls_sdk.ListLogStoreResult
		err            error
	}
	type result struct {
		res *meta.LogStoreListResult
		err error
	}
	type tasks struct {
		name string
		arg  args
		want *want
		res  *result
	}
	task := []tasks{
		{
			name: "test-1",
			arg: args{
				region: "bj",
				ctx:    ctx,
			},
			want: &want{
				err: nil,
				logStoreResult: &bls_sdk.ListLogStoreResult{
					Order:      "asc",
					OrderBy:    "creationDateTime",
					PageNo:     1,
					PageSize:   10,
					TotalCount: 2,
					Result: []bls_sdk.LogStore{
						{
							CreationDateTime: "2023-04-01T12:00:00Z",
							LastModifiedTime: "2023-04-10T12:00:00Z",
							LogStoreName:     "LogStore1",
							Retention:        30,
						},
						{
							CreationDateTime: "2023-04-02T12:00:00Z",
							LastModifiedTime: "2023-04-11T12:00:00Z",
							LogStoreName:     "LogStore2",
							Retention:        60,
						},
					},
				},
			},
			res: &result{
				err: nil,
				res: &meta.LogStoreListResult{
					PageNo:     1,
					PageSize:   10,
					TotalCount: 2,
					Result: []meta.LogStoreDetail{
						{
							Name:      "LogStore1",
							Region:    "bj",
							Retention: 30,
						},
						{
							Name:      "LogStore2",
							Region:    "bj",
							Retention: 60,
						},
					},
				},
			},
		},
	}
	for _, test := range task {
		t.Run(test.name, func(t *testing.T) {

			mockBlsv3Service := bls_mock.NewMockServiceInterface(ctrl)
			mockBlsv3Service.EXPECT().WithLogOperation(gomock.Any(), gomock.Any()).
				Return(nil).AnyTimes()
			mockBlsv3Service.EXPECT().GetBlsLogs(gomock.Any()).
				Return(test.want.logStoreResult, test.want.err).AnyTimes()
			svc := NewBlsService(NewOption(nil))
			svc.blsv3Service = mockBlsv3Service
			res, err := svc.LogStoreList(test.arg.ctx, test.arg.region)
			if test.want != nil && test.want.err != nil {
				assert.Equal(t, test.want.err, err)
			} else {
				assert.Nil(t, err)
				assert.Equal(t, *test.res.res, *res)
			}
		})
	}
}

// TestBlsList 是一个测试函数，用于测试BlsList函数的正确性
func TestBlsList(t *testing.T) {
	type args struct {
		mockInstanceUUID *[]model_meta.Bls
		clusters         *[]model_meta.Cluster
		blsTasks         *[]model_meta.Bls
		taskInstance     *blsv3.TaskInstanceResponseParameters
		logDetails       *api.ListLogStoreResult
	}
	type testCases struct {
		name    string
		arg     *args
		mockres *meta.BlsListResult
		mockerr error
	}
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	cases := []testCases{
		{
			name: "test-1",
			arg: &args{
				mockInstanceUUID: &[]model_meta.Bls{
					{
						LogStoreName: "zzw-logstore",
					},
				},
				clusters: &[]model_meta.Cluster{
					{
						ClusterUUID: "cluster-1",
						Region:      "bj",
					},
					{
						ClusterUUID: "cluster-2",
						Region:      "bj",
					},
				},
				blsTasks: &[]model_meta.Bls{
					{
						TaskID: "task-1",
					},
				},
				taskInstance: &blsv3.TaskInstanceResponseParameters{
					Instances: []blsv3.TaskInstance{
						{
							Status: "Running",
						},
					},
				},
				logDetails: &api.ListLogStoreResult{
					Result: []api.LogStore{
						{
							LogStoreName:     "zzw-logstore",
							Retention:        30,
							CreationDateTime: "2023-04-10T12:00:00Z",
							LastModifiedTime: "2023-04-13T12:00:00Z",
						},
						{
							LogStoreName: "test-2",
						},
					},
				},
			},
			mockres: &meta.BlsListResult{
				Status: "Open",
				Result: []meta.BlsDetail{
					{
						Name:       "zzw-logstore",
						Retention:  30,
						CreateTime: "2023-04-10T12:00:00Z",
						UpdateTime: "2023-04-13T12:00:00Z",
					},
				},
			},
			mockerr: nil,
		},
	}
	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			mockInstanceService := instance_mock.NewMockServiceInterface(ctrl)
			mockInstanceService.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).
				Return(&model_meta.Instances{BlsEnabled: csm.Bool(true)}, nil).AnyTimes()

			mockBlsv3Service := bls_mock.NewMockServiceInterface(ctrl)
			mockBlsv3Service.EXPECT().GetAllBlsTasksByInstanceUUID(gomock.Any(), gomock.Any()).
				Return(tt.arg.mockInstanceUUID, nil).AnyTimes()
			mockBlsv3Service.EXPECT().WithTaskOperation(ctx, gomock.Any()).
				Return(nil).AnyTimes()
			mockBlsv3Service.EXPECT().GetAllBlsTasksByClusterUUID(ctx, gomock.Any()).
				Return(tt.arg.blsTasks, nil).AnyTimes()
			mockBlsv3Service.EXPECT().GetBlsTasksInstanceByTaskID(ctx, gomock.Any()).
				Return(tt.arg.taskInstance, nil).AnyTimes()
			mockBlsv3Service.EXPECT().WithLogOperation(ctx, gomock.Any()).
				Return(nil).AnyTimes()
			mockBlsv3Service.EXPECT().GetBlsLogs(ctx).
				Return(tt.arg.logDetails, nil).AnyTimes()

			mockClusterService := cluster_mock.NewMockServiceInterface(ctrl)
			mockClusterService.EXPECT().GetAllClusterByInstanceUUID(ctx, gomock.Any()).
				Return(tt.arg.clusters, nil).AnyTimes()

			svc := NewBlsService(NewOption(nil))
			svc.blsv3Service = mockBlsv3Service
			svc.clusterModel = mockClusterService
			svc.modelInstance = mockInstanceService
			res, err := svc.BlsList(ctx, "bj", "csmInstance-1")
			assert.Equal(t, *tt.mockres, *res)
			assert.Nil(t, err)
		})
	}
}
