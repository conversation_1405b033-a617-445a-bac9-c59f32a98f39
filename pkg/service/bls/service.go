package bls

import (
	"fmt"
	"sync"

	"github.com/jinzhu/copier"
	bce_model "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3"
	bce_cce "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blsv3"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	model_meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/version"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil/rollback"
)

type Service struct {
	option        *Option
	clusterModel  cluster.ServiceInterface
	modelInstance instances.ServiceInterface
	cceService    cce.ClientInterface
	blsv3Service  blsv3.ServiceInterface
}

// NewBlsService NewBlsService 创建一个新的 BlsService 实例，并返回指向该实例的指针
// 参数 option：包含了 GORM DB 对象的 Option 结构体指针，用于初始化 Service 实例
// 返回值：*Service，指向新创建的 BlsService 实例的指针
func NewBlsService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		option:        NewOption(gormDB),
		clusterModel:  cluster.NewClusterService(cluster.NewOption(gormDB)),
		modelInstance: instances.NewInstancesService(instances.NewOption(gormDB)),
		cceService:    cce.NewClientService(),
		blsv3Service:  blsv3.NewBlsService(blsv3.NewOption(gormDB)),
	}
}

// CSMAgentCheck 检查CSM Agent是否存在，如果不存在则返回false，否则返回true
//
// 参数：
//
//	ctx csmContext.CsmContext - CSM上下文对象
//	instanceUUID string - 实例UUID
//
// 返回值：
//
//	*meta.AgentCheckResult - AgentCheckResult结构体指针，包含一个bool类型的字段IsExit，用于标识Agent是否存在，true表示存在，false表示不存在
//	 error - 错误信息，如果没有错误则为nil
func (service *Service) CSMAgentCheck(ctx csmContext.CsmContext, instanceUUID string) (*meta.AgentCheckResult, error) {
	res := meta.AgentCheckResult{IsExist: true}
	ctx.CsmLogger().Infof("service/bls CSM logAgent Check start instanceUUID: %s", instanceUUID)
	//获取cluster信息
	allCluster, err := service.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		res.IsExist = false
		return &res, err
	}
	if allCluster == nil || len(*allCluster) == 0 {
		ctx.CsmLogger().Infof("service/bls CSM logAgent Check get cluster info is nil, instanceUUID: %s", instanceUUID)
		res.IsExist = true
		return &res, nil
	}
	var agentMtx sync.Mutex
	var agentWg sync.WaitGroup
	ctx.CsmLogger().Infof("service/bls CSM logAgent Check start check Clusters, instanceUUID: %s", instanceUUID)
	var zeroAddonInstance bce_cce.AddonInstanceStatus
	for _, cluster := range *allCluster {
		ctx.CsmLogger().Infof("service/bls CSM logAgent Check start check Cluster, instanceUUID: %s, clusterUUID:%s",
			instanceUUID, cluster.ClusterUUID)
		if cluster.ClusterType == string(model_meta.ClusterTypeExternal) {
			continue
		}
		agentWg.Add(1)
		go func(cluster model_meta.Cluster) {

			agentMtx.Lock()

			//获取cluster的agent信息 这里的model_meta.StandaloneMeshType，传递的参数不代表当前的集群格式，只是代表进行数据面的集群cce连接还是控制面的集群cce连接
			resAddon, err := service.cceService.GetClusterAddonByClusterId(ctx,
				cluster.Region, cluster.ClusterUUID, model_meta.StandaloneMeshType, string(model_meta.CCELogOperator))
			ctx.CsmLogger().Infof("service/bls CSM logAgent Check get cluster addon, instanceUUID: %s,addon.Instance:%v",
				instanceUUID, resAddon)
			if err != nil || resAddon == nil || len(resAddon.Items) == 0 {
				//ctx.CsmLogger().Infof("service/bls CSMAgentCheck has an error, instanceUUID: %s,error: %s", instanceUUID, err.Error())
				res.IsExist = false
				res.ClusterUUID = cluster.ClusterUUID
			} else if resAddon.Items[0].Instance.Status == zeroAddonInstance {
				res.IsExist = false
				res.ClusterUUID = cluster.ClusterUUID
			}
			agentMtx.Unlock()
			agentWg.Done()
		}(cluster)
	}
	agentWg.Wait()
	ctx.CsmLogger().Infof("service/bls CSM logAgent check check Clusters over, instanceUUID: %s", instanceUUID)
	return &res, nil
}

// ClusterAgentCheck 判断集群是否已经安装了BLS agent，如果已经安装则返回true，否则返回false
// ctx csmContext.CsmContext 上下文对象
// clusterID string 集群ID
// region string 地域信息
// 返回值 *meta.AgentCheckResult 包含是否已经安装了BLS agent的结果，error 错误信息
func (service *Service) ClusterAgentCheck(ctx csmContext.CsmContext, clusterID, region string) (*meta.AgentCheckResult, error) {
	// service.cceService
	//获取cluster的bls agent 安装信息
	ctx.CsmLogger().Infof("service/bls ClusterAgentCheck start to check cluster agent,clusterID: %s", clusterID)
	resAddon, err := service.cceService.GetClusterAddonByClusterId(ctx, region, clusterID, model_meta.StandaloneMeshType, string(model_meta.CCELogOperator))
	// fmt.Println("resAddon.Items:", resAddon.Items, len(resAddon.Items))
	var zeroAddonInstance bce_cce.AddonInstanceStatus
	if err != nil {
		ctx.CsmLogger().Errorf("get BLS agent resAddon error: %v", err)
		return &meta.AgentCheckResult{IsExist: false, ClusterUUID: clusterID}, err
	} else if resAddon == nil || len(resAddon.Items) == 0 {
		ctx.CsmLogger().Errorf("get BLS agent resAddon is nil")
		return &meta.AgentCheckResult{IsExist: false, ClusterUUID: clusterID}, nil
	} else if len(resAddon.Items) != 0 && resAddon.Items[0].Instance.Status == zeroAddonInstance {
		return &meta.AgentCheckResult{IsExist: false, ClusterUUID: clusterID}, nil
	}
	return &meta.AgentCheckResult{IsExist: true}, nil
}

// LogStoreList 返回给定 region 的所有日志库列表
func (service *Service) LogStoreList(ctx csmContext.CsmContext, region string) (*meta.LogStoreListResult, error) {
	err := service.blsv3Service.WithLogOperation(ctx, region)
	if err != nil {
		ctx.CsmLogger().Errorf("LogStoreList service init error: %v", err)
		return nil, nil
	}
	ctx.CsmLogger().Infof("LogStoreList service start to get log store list")
	logDetails, err := service.blsv3Service.GetBlsLogs(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("LogStoreList service get bls logs error: %v", err)
	}
	res := meta.LogStoreListResult{
		TotalCount: logDetails.TotalCount,
		PageNo:     logDetails.PageNo,
		PageSize:   logDetails.PageSize,
		Result:     []meta.LogStoreDetail{},
	}
	ctx.CsmLogger().Infof("LogStoreList service get log store list successful,res: %v", res.Result)
	for _, item := range logDetails.Result {
		res.Result = append(res.Result, meta.LogStoreDetail{
			Name:      item.LogStoreName,
			Retention: item.Retention,
			Region:    region,
		})
	}
	return &res, nil
}

// 获取BLS日志采集列表，并返回对应的状态
func (service *Service) BlsList(ctx csmContext.CsmContext, region, instanceUUID string) (res *meta.BlsListResult, err error) {
	ctx.CsmLogger().Infof("service/bls BlsList start to get bls list, instanceUUID: %s",
		instanceUUID)
	instance, err := service.modelInstance.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Info("service/bls BlsList get instance error")
		return nil, err
	} else if instance == nil {
		ctx.CsmLogger().Info("BlsList instance is nil")
		res = &meta.BlsListResult{
			Status: blsv3.Abnormal,
		}
		return res, fmt.Errorf("instance is nil")
	} else if !(*instance.BlsEnabled) {
		ctx.CsmLogger().Info("BlsList instance is not enabled, Close")
		res = &meta.BlsListResult{
			Status: blsv3.Close,
		}
		return res, nil
	}
	ctx.CsmLogger().Infof("service/bls BlsList start to get clusters list, instanceUUID: %s", instanceUUID)
	clusters, err := service.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return nil, err
	}
	if clusters == nil || len(*clusters) == 0 {
		//托管网格在没有集群的时候开启BLS，虽然没有Task，但是BLS是开启状态
		ctx.CsmLogger().Infof("BlsList clusters is nil, but Open, instanceUUID: %s", instanceUUID)
		res = &meta.BlsListResult{
			Status: blsv3.Open,
		}
		return res, nil
	}
	//ctx.CsmLogger().Infof("service/bls BlsList start to get bls list, clusters: %v", clusters)
	ctx.CsmLogger().Infof("service/bls BlsList start to get bls list, instanceUUID: %s", instanceUUID)
	// 获取BLS任务列表，进入异常判断，查看当前是否开启正常
	blsLists, err := service.blsv3Service.GetAllBlsTasksByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return nil, err
	} else if instance.InstanceType == string(version.HostingVersionType) &&
		(blsLists == nil || len(*blsLists) == 0) {
		//托管网格在没有集群的时候开启BLS，虽然没有Task，但是BLS是开启状态
		res = &meta.BlsListResult{
			Status: blsv3.Open,
		}
		return res, nil
	}

	/* 如果数据库列表和BLS对应的日志库列表不一致，
	或者数据库的日志集不唯一，则返回状态为异常*/
	ctx.CsmLogger().Infof("service/bls BlsList start to check blsStore list, instanceUUID: %s", instanceUUID)
	name := (*blsLists)[0].LogStoreName
	// 遍历BLS列表，日志集不唯一，则返回异常
	for i := range *blsLists {
		if (*blsLists)[i].LogStoreName != name {
			ctx.CsmLogger().Info("BlsList bls list is not unique,日志集不唯一，返回异常")
			res = &meta.BlsListResult{
				Status: blsv3.Abnormal,
			}
			return res, nil
		}
	}

	ctx.CsmLogger().Infof("service/bls BlsList check LogStoreName successful, instanceUUID: %s", instanceUUID)
	err = service.blsv3Service.WithTaskOperation(ctx, region)

	if err != nil {
		return &meta.BlsListResult{
			Status: blsv3.Abnormal,
		}, err
	}
	// 获取对应的Cluster信息，需求为：Cluster在数据库对应存在task，task在BLS存在对应内容
	allClusters, err := service.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("BlsList get all cluster error, return abnormal, err:%s", err.Error())
		return &meta.BlsListResult{
			Status: blsv3.Abnormal,
		}, err
	}
	for _, cluster := range *allClusters {
		if cluster.ClusterType == string(model_meta.ClusterTypeExternal) {
			continue
		}
		ctx.CsmLogger().Infof("service/bls BlsList start to check cluster %s", cluster.ClusterUUID)

		blsTasks, err := service.blsv3Service.GetAllBlsTasksByClusterUUID(ctx, cluster.ClusterUUID)
		//有 cluster不存在 task，则返回异常
		if err != nil || blsTasks == nil || len(*blsTasks) == 0 {
			ctx.CsmLogger().Errorf("BlsList get all bls tasks error, return abnormal, err:%s", err.Error())
			return &meta.BlsListResult{
				Status: blsv3.Abnormal,
			}, fmt.Errorf("LogStoreList service GetAllBlsTasksByClusterUUID error: %v", err)
		}
		for _, item := range *blsTasks {
			taskDetail, err := service.blsv3Service.GetBlsTasksInstanceByTaskID(ctx, item.TaskID)
			//返回异常或者不存在task，则返回异常
			if err != nil || taskDetail == nil {
				ctx.CsmLogger().Errorf("BlsList get bls tasks instance by task id error, return abnormal, err:%s", err.Error())
				return &meta.BlsListResult{
					Status: blsv3.Abnormal,
				}, fmt.Errorf("LogStoreList service GetBlsTaskByTaskId error: %v", err)
			}
			// 遍历task的实例，如果状态不是running，则返回异常
			for _, instance := range taskDetail.Instances {
				if instance.Status != blsv3.Running && instance.Status != blsv3.Unsynchronized {
					ctx.CsmLogger().Errorf("BlsList bls task instance status is not running, return abnormal,status:%s",
						instance.Status)
					res = &meta.BlsListResult{
						Status: blsv3.Abnormal,
					}
					return res, nil
				}
			}
		}
	}

	/* 数据库和BLS对应的体制列表一致，则返回状态为开启，并返回对应的BLS日志集细节*/
	err = service.blsv3Service.WithLogOperation(ctx, region)
	if err != nil {
		ctx.CsmLogger().Errorf("BlsList service init Log Operation error: %v", err)
		return nil, fmt.Errorf("LogStoreList service init Log Operation error: %v", err)
	}

	logDetails, err := service.blsv3Service.GetBlsLogs(ctx)
	if err != nil || logDetails == nil {
		ctx.CsmLogger().Errorf("BlsList service get bls logs error: %v", err)
		return nil, fmt.Errorf("LogStoreList service get bls logs error: %v", err)
	}

	//获取BLS日志集的详细信息
	for _, item := range logDetails.Result {
		if item.LogStoreName == name {
			res = &meta.BlsListResult{
				Status: blsv3.Open,
				Result: []meta.BlsDetail{
					{
						Name:       item.LogStoreName,
						Retention:  item.Retention,
						CreateTime: string(item.CreationDateTime),
						UpdateTime: string(item.LastModifiedTime),
					},
				},
			}
			return res, nil
		}
	}
	ctx.CsmLogger().Errorf("数据库和BLS对应的体制列表不一致,不存在对应的BLS日志集")
	// 数据库和BLS对应的体制列表不一致，不存在对应的BLS日志集
	return &meta.BlsListResult{
		Status: blsv3.Abnormal,
	}, nil
}

// BlsClose 函数名：BlsClose
// 功能：关闭BLS服务，包括删除所有与该实例相关联的任务和更新实例状态
// 输入参数：
//   - ctx csmContext.CsmContext  上下文对象，包含日志记录器等功能
//   - region string             区域标识符
//   - instanceUUID string       实例UUID
//
// 输出参数：
//   - res *meta.BlsCloseResult   结果对象，包含错误信息BlsError
//     BlsError struct {
//     ErrorMsg string     错误信息，如果为空字符串表示操作成功
//     }
//   - err error                  错误信息，如果为nil表示操作成功
//
// 返回值：无
func (service *Service) BlsClose(ctx csmContext.CsmContext, region,
	instanceUUID string) (res *meta.BlsCloseResult, err error) {
	ctx.CsmLogger().Infof("BlsClose service start, instanceUUID: %s", instanceUUID)
	instanceDetail, err := service.modelInstance.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("BlsOpen service: GetInstanceByInstanceUUID step error: %v", err)
		return &meta.BlsCloseResult{BlsError: meta.BlsCloseError{ErrorMsg: "instance Database error"}}, err
	}
	tasks, err := service.blsv3Service.GetAllBlsTasksByInstanceUUID(ctx, instanceUUID)
	if !(*instanceDetail.BlsEnabled) && err == nil && (tasks == nil || len(*tasks) == 0) {
		return nil, err
	}

	// 将instance的blsEnable设置为false
	tx := service.option.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}()
	instanceService := service.modelInstance.WithTx(dbutil.NewDB(tx))
	ctx.CsmLogger().Infof("BlsClose service get instance details, instanceUUID: %s", instanceUUID)
	if !(*instanceDetail.BlsEnabled) {
		ctx.CsmLogger().Infof(
			"BlsClose service: instance of enabled is 0 , BlsClose sercive in database have already been closed instanceUUID: %s",
			instanceUUID)
		//return &meta.BlsCloseResult{}, nil // 取消返回，为了避免数据库关闭后，instance实际上没有清除task
	}
	var enable bool = false
	//*enable = false
	newInstance := &model_meta.Instances{}
	err = copier.Copy(newInstance, instanceDetail)
	if err != nil {
		return &meta.BlsCloseResult{BlsError: meta.BlsCloseError{ErrorMsg: "instance Database error"}}, err
	}
	newInstance.BlsEnabled = csm.Bool(enable)

	// 更新instance信息
	if _, err = instanceService.UpdateInstance(ctx, instanceDetail, newInstance); err != nil {
		ctx.CsmLogger().Errorf("BlsOpen service: UpdateInstance step error: %v", err)
		return &meta.BlsCloseResult{BlsError: meta.BlsCloseError{ErrorMsg: "instance Database error"}}, err
	}

	ctx.CsmLogger().Infof("BlsClose method starts to get taskids, instanceUUID: %s", instanceUUID)
	taskIDs := bce_model.TaskIDs{}
	for i := range *tasks {
		taskIDs.TaskIDs = append(taskIDs.TaskIDs, bce_model.TaskID{TaskID: (*tasks)[i].TaskID})
	}
	// ctx.CsmLogger().Infof("BlsClose service get taskids over, instanceUUID: %s", instanceUUID)
	blsv3Service := service.blsv3Service.WithTx(dbutil.NewDB(tx))
	errMsg := ""
	if err = service.blsv3Service.WithTaskOperation(ctx, region); err != nil {
		return nil, err
	}
	// 批量关闭任务，成功则返回无异常，失败则慢处理
	if err = service.blsv3Service.CloseBlsTaskByTaskIDs(ctx, taskIDs); err == nil {
		ctx.CsmLogger().Infof("BlsClose service fast path has no error, "+
			"start to delate bls tasks, instanceUUID: %s", instanceUUID)
		if err = blsv3Service.DeleteBlsTaskByInstanceUUID(ctx, instanceUUID); err != nil {
			ctx.CsmLogger().Errorf("BlsClose service DeleteBlsTaskByInstanceUUID error: %v", err)
			errMsg = "DataBase Error, redo it later"
		}
		tx.Commit()
		if errMsg == "" {
			return nil, nil //关闭成功，并且数据库也成功
		}
		//关闭成功，但是可能存在数据库的问题
		return &meta.BlsCloseResult{BlsError: meta.BlsCloseError{ErrorMsg: errMsg}}, err
	}

	ctx.CsmLogger().Infof("BlsClose service fast path has an error,%v", err)

	//慢处理流程
	for _, item := range *tasks {
		err = service.blsv3Service.CloseBlsTaskByTaskID(ctx, item.TaskID)
		if err != nil {
			ctx.CsmLogger().Infof("LogClose service CloseBlsTaskByTaskID error: %v", err)
			errMsg = err.Error()
		}
	}
	if err = blsv3Service.DeleteBlsTaskByInstanceUUID(ctx, instanceUUID); err != nil {
		ctx.CsmLogger().Errorf("LogClose service DeleteBlsTaskByInstanceUUID error: %v", err)
		// errMsg = "DataBase Error, redo it later"
	}
	tx.Commit()
	if errMsg == "" {
		return nil, nil //关闭成功，并且数据库也成功
	}
	//Msg为处理过程用户侧有问题，err为数据库处理流程问题
	return &meta.BlsCloseResult{BlsError: meta.BlsCloseError{ErrorMsg: errMsg}}, err
}

// BlsOpen 打开Bls服务
//
// 参数：
//
//	ctx csmContext.CsmContext  上下文对象
//	region string              地域名称
//	instanceUUID string        实例UUID
//	name string               日志存储名称
//
// 返回值：
//
//	bool                      是否成功，true表示成功，false表示失败
//	error                     错误信息，如果成功为nil，如果失败包含错误原因
func (service *Service) BlsOpen(ctx csmContext.CsmContext, region, instanceUUID,
	name string) (res bool, err error) {
	ctx.CsmLogger().Infof("BlsOpen service start, instanceUUID: %s, logStoreName:%s", instanceUUID, name)
	// 清空之前的任务
	_, err = service.BlsClose(ctx, region, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("BlsOpen service: close step error: %v", err)
		return false, err
	}
	ctx.CsmLogger().Infof("BlsOpen service preclose over, instanceUUID: %s, logStoreName:%s", instanceUUID, name)
	// 创建任务
	if err = service.blsv3Service.WithTaskOperation(ctx, region); err != nil {
		ctx.CsmLogger().Errorf("BlsOpen service: WithTaskOperation step error: %v", err)
		return false, err
	}
	ctx.CsmLogger().Infof("BlsOpen service init task over, instanceUUID: %s, logStoreName:%s", instanceUUID, name)
	clusters, err := service.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("BlsOpen service: GetAllClusterByInstanceUUID step error: %v", err)
		return false, err
	}
	ctx.CsmLogger().Infof("BlsOpen service start to get instance, instanceUUID: %s, logStoreName:%s", instanceUUID, name)

	// 获取instance信息
	instanceDetail, err := service.modelInstance.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		ctx.CsmLogger().Errorf("BlsOpen service: GetInstanceByInstanceUUID step error: %v", err)
		return false, err
	}
	ctx.CsmLogger().Infof("BlsOpen service start to get tx, instanceUUID: %s, logStoreName:%s", instanceUUID, name)

	/* tx := service.option.DB.Begin()
	defer func() {
		rbErr := rollback.Rollback(ctx, tx, err, recover())
		if rbErr != nil {
			err = rbErr
		}
	}() */
	//blsv3Service := service.blsv3Service.WithTx(dbutil.NewDB(tx))
	ctx.CsmLogger().Infof("BlsOpen service start to update instance, instanceUUID: %s, logStoreName:%s", instanceUUID, name)

	//获取当前的instance bls开启状态，如果一开启则跳过修改数据库操作
	if instanceDetail.BlsEnabled != nil && *instanceDetail.BlsEnabled {
		ctx.CsmLogger().Infof("BlsOpen service: instance already open, skip")
		//return true, nil
	} else {
		//instanceService := service.modelInstance.WithTx(dbutil.NewDB(tx))

		newInstance := &model_meta.Instances{}
		err := copier.Copy(newInstance, instanceDetail)
		if err != nil {
			return false, err
		}
		newInstance.BlsEnabled = csm.Bool(true)
		// 更新instance信息
		if _, err = service.modelInstance.UpdateInstance(ctx, instanceDetail, newInstance); err != nil {
			ctx.CsmLogger().Errorf("BlsOpen service: UpdateInstance step error: %v", err)
			return false, err
		}
	}

	// instanceService
	ctx.CsmLogger().Infof("BlsOpen service start to get iam, instanceUUID: %s, logStoreName:%s", instanceUUID, name)
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return false, err
	}
	ctx.CsmLogger().Infof("BlsOpen service start to create task, instanceUUID: %s, logStoreName:%s", instanceUUID, name)
	// 创建任务
	res = true
	for _, cluster := range *clusters {
		if cluster.ClusterType == string(model_meta.ClusterTypeExternal) { //跳过外部集群
			continue
		}
		logconf := &bce_model.LogConf{
			LogStore:     name,
			Type:         "BLS",
			ClusterID:    cluster.ClusterUUID,
			InstanceUUID: instanceUUID,
		}
		taskID, err := service.blsv3Service.CreateTask(ctx, logconf, accountId)
		if err != nil {
			ctx.CsmLogger().Errorf("BlsOpen service: CreateTask step error: %v", err)
			res = false
			continue
		}

		ctx.CsmLogger().Infof("BlsOpen service: CreateTask success, taskID:%s", taskID)
		// 同步到数据库
		blsTask := &model_meta.Bls{
			InstanceUUID: instanceUUID,
			LogStoreName: name,
			TaskID:       taskID,
			AccountID:    accountId,
			ClusterUUID:  cluster.ClusterUUID,
		}
		if err = service.blsv3Service.NewBlsTask(ctx, blsTask); err != nil {
			ctx.CsmLogger().Errorf("BlsOpen service: CreateBlsTask step to database error: %v", err)
			//TODO： 数据库失败，删除对应的Task
		}
		//task 同步内容instance
		err = service.blsv3Service.BoundTaskWithInstances(ctx, taskID, logconf.ClusterID)
		if err != nil {
			ctx.CsmLogger().Errorf("BlsOpen service: BoundTaskWithInstances step error: %v", err)
			res = false
			continue
		}
	}
	// tx.Commit()
	return res, err
}

// BlsAddCluster 功能：将实例添加到指定集群中
// 参数：ctx csmContext.CsmContext - 上下文信息
//
//	instanceUUID string - 实例UUID
//	cluster string - 集群名称
//	region string - 地区名称
//
// 返回值：error - 错误信息，如果成功则为nil
func (service *Service) BlsAddCluster(ctx csmContext.CsmContext, instanceUUID, cluster, region string) (
	err error) {
	ctx.CsmLogger().Infof("BlsAddCluster service start to get instance, instanceUUID: %s, Cluster: %s",
		instanceUUID,
		cluster)
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// 获取当前的日志集名称
	blsLists, err := service.blsv3Service.GetAllBlsTasksByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return fmt.Errorf("BlsAddCluster service: GetAllBlsTasksByInstanceUUID step error: %v", err)
	} else if blsLists == nil || len(*blsLists) == 0 {
		return fmt.Errorf("BlsAddCluster service: GetAllBlsTasksByInstanceUUID step error: blsLists is nil, instanceUUID:%s",
			instanceUUID)
	}

	/* 如果数据库列表和BLS对应的日志库列表不一致，
	或者数据库的日志集不唯一，则返回状态为异常*/

	name := (*blsLists)[0].LogStoreName

	logconf := &bce_model.LogConf{
		LogStore:     name,
		Type:         "BLS",
		ClusterID:    cluster,
		InstanceUUID: instanceUUID,
	}

	if err = service.blsv3Service.WithTaskOperation(ctx, region); err != nil {
		return err
	}
	taskID, err := service.blsv3Service.CreateTask(ctx, logconf, accountId)
	if err != nil {
		ctx.CsmLogger().Errorf("BlsOpen service: CreateTask step error: %v", err)
	}

	ctx.CsmLogger().Infof("BlsOpen service: CreateTask success, taskID:%s", taskID)
	// 同步到数据库
	blsTask := &model_meta.Bls{
		InstanceUUID: instanceUUID,
		LogStoreName: name,
		TaskID:       taskID,
		AccountID:    accountId,
		ClusterUUID:  cluster,
	}
	if err = service.blsv3Service.NewBlsTask(ctx, blsTask); err != nil {
		ctx.CsmLogger().Errorf("BlsOpen service: CreateBlsTask step to database error: %v", err)
		//TODO： 数据库失败，删除对应的Task
	}
	//同步task到数据库后绑定instance
	err = service.blsv3Service.BoundTaskWithInstances(ctx, taskID, logconf.ClusterID)
	if err != nil {
		ctx.CsmLogger().Errorf("BlsOpen service: BoundTaskWithInstances step error: %v", err)
	}
	return nil
}

// BlsRemoveCluster 删除集群，包括关闭任务、删除任务记录
// ctx csmContext.CsmContext：上下文对象，包含日志等功能
// instanceUUID string：实例的uuid，用于获取任务列表
// cluster string：集群的uuid，用于关闭任务和删除任务记录
// region string：区域信息，用于操作任务
// 返回值 error：错误信息，如果成功则为nil
func (service *Service) BlsRemoveCluster(ctx csmContext.CsmContext, instanceUUID, cluster, region string) (
	err error) {
	ctx.CsmLogger().Infof("BlsRemoveCluster service start to get instance, instanceUUID: %s, Cluster: %s",
		instanceUUID, cluster)
	tasks, err := service.blsv3Service.GetAllBlsTasksByClusterUUID(ctx, cluster)
	if err != nil {
		return err
	} else if tasks == nil || len(*tasks) == 0 {
		return fmt.Errorf("BlsRemoveCluster service: GetAllBlsTasksByInstanceUUID step error: tasks is nil, instanceUUID:%s",
			instanceUUID)
	}
	if err = service.blsv3Service.WithTaskOperation(ctx, region); err != nil {
		return err
	}
	for _, item := range *tasks {
		err = service.blsv3Service.CloseBlsTaskByTaskID(ctx, item.TaskID)
		if err != nil {
			ctx.CsmLogger().Infof("LogClose service CloseBlsTaskByTaskID error: %v", err)

		}
	}
	if err = service.blsv3Service.DeleteBlsTaskByClusterUUID(ctx, cluster); err != nil {
		ctx.CsmLogger().Errorf("BlsRemoveCluster service DeleteBlsTaskByClusterUUID error: %v", err)
		// errMsg = "DataBase Error, redo it later"
	}
	return err
}
