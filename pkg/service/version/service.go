package version

import (
	"fmt"
	"sort"
	"strings"

	v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
)

type Service struct {
	opt *Option
}

func NewVersionService(option *Option) *Service {
	return &Service{opt: option}
}

// CompareTo 比较版本与排序
func (version *Service) CompareTo(v1, v2 string) int {
	v1Values := strings.Split(v1, ".")
	v2Values := strings.Split(v2, ".")
	for i := 0; i < len(v1Values) && i < len(v2Values); i++ {
		vNum := parseInt(v1Values[i])
		otherNum := parseInt(v2Values[i])

		if vNum < otherNum {
			return -1
		} else if vNum > otherNum {
			return 1
		}
	}

	if len(v1Values) < len(v2Values) {
		return -1
	} else if len(v1Values) > len(v2Values) {
		return 1
	}

	return 0
}

// parseInt 转换字符串为整数
func parseInt(s string) int {
	num := 0
	for i := 0; i < len(s); i++ {
		num = num*10 + int(s[i]-'0')
	}
	return num
}

// GetMeshVersionList 返回服务网格列表
func (version *Service) GetMeshVersionList(ctx csmContext.CsmContext, meshType string) (res []string, err error) {
	versions := make([]string, 0)
	if strings.EqualFold(string(HostingVersionType), meshType) {
		versions = version.opt.Hosting.IstioVersion
	}
	if strings.EqualFold(string(StandaloneVersionType), meshType) {
		versions = version.opt.Standalone.IstioVersion
	}
	if versions == nil || len(versions) == 0 {
		res = make([]string, 0)
	} else {
		res = versions
	}
	sort.Slice(res, func(i, j int) bool {
		return version.CompareTo(res[i], res[j]) > 0
	})
	return res, nil
}

func (version *Service) CheckVersionType(ctx csmContext.CsmContext, meshType string) error {
	if len(meshType) <= 0 {
		return fmt.Errorf("type is null")
	}
	if !strings.EqualFold(string(HostingVersionType), meshType) && !strings.EqualFold(string(StandaloneVersionType), meshType) {
		msg := fmt.Sprintf("not support version type [%s]", meshType)
		ctx.CsmLogger().Errorf(msg)
		return fmt.Errorf(msg)
	}
	return nil
}

func (version *Service) GetHubWithVersionType(ctx csmContext.CsmContext, meshType string) string {
	if strings.EqualFold(string(HostingVersionType), meshType) {
		return version.opt.Hosting.IopHub
	}

	if strings.EqualFold(string(StandaloneVersionType), meshType) {
		return version.opt.Standalone.IopHub
	}
	return ""
}

func (version *Service) CheckSupportedVersion(ctx csmContext.CsmContext, istioVersion, meshType string) (bool, error) {
	err := version.CheckVersionType(ctx, meshType)
	if err != nil {
		return false, err
	}
	ctx.CsmLogger().Infof("CheckSupportedVersion istio version %s", istioVersion)
	versions, _ := version.GetMeshVersionList(ctx, meshType)
	for _, v := range versions {
		if strings.EqualFold(v, istioVersion) {
			return true, nil
		}
	}
	return false, nil
}

func (version *Service) CheckK8sVersion(ctx csmContext.CsmContext, istioVersion, k8sVersion string) (bool, error) {
	switch istioVersion {
	case constants.IstioVersion13:
		return strings.Contains(k8sVersion, constants.K8sVersion20) || strings.Contains(k8sVersion, constants.K8sVersion22), nil
	case constants.IstioVersion14:
		return strings.Contains(k8sVersion, constants.K8sVersion22) || strings.Contains(k8sVersion, constants.K8sVersion24), nil
	case constants.IstioVersion16:
		return strings.Contains(k8sVersion, constants.K8sVersion22) || strings.Contains(k8sVersion, constants.K8sVersion24), nil
	case constants.IstioHostingVersion14:
		return strings.Contains(k8sVersion, constants.K8sVersion22) || strings.Contains(k8sVersion, constants.K8sVersion24), nil
	default:
		return false, nil
	}
}

// CheckNodeOS 校验集群 node 节点操作系统
func (version *Service) CheckNodeOS(ctx csmContext.CsmContext, cceClusterInstances *v2.Instance) bool {
	// centos 7.x, ubuntu 16/18/20/22 true
	// baidulinux3.0, centos8.0, rockyLinux8.6 提醒用户是否加载内核特性
	osName := cceClusterInstances.Spec.InstanceOS.OSName
	osVersion := cceClusterInstances.Spec.InstanceOS.OSVersion
	if versions, ok := version.opt.NodeOS[strings.ToLower(string(osName))]; ok {
		for _, version := range versions {
			if osVersion == version {
				return true
			}
		}
	}
	ctx.CsmLogger().Warnf("%v The nodeOS does not meet expectations，osName %v, osVersion %v", cceClusterInstances.Spec.ClusterID, osName, osVersion)
	return false
}
