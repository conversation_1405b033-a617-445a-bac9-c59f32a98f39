package version

import (
	v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetMeshVersionList(ctx csmContext.CsmContext, meshType string) ([]string, error)
	CheckVersionType(ctx csmContext.CsmContext, meshType string) error
	CheckSupportedVersion(ctx csmContext.CsmContext, istioVersion, meshType string) (bool, error)
	GetHubWithVersionType(ctx csmContext.CsmContext, meshType string) string
	CheckK8sVersion(ctx csmContext.CsmContext, istioVersion, k8sVersion string) (bool, error)
	CheckNodeOS(ctx csmContext.CsmContext, cceClusterInstances *v2.Instance) bool
}
