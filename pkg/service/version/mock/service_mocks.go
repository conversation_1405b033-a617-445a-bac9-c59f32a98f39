// Code generated by MockGen. DO NOT EDIT.
// Source: ./interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// CheckK8sVersion mocks base method.
func (m *MockServiceInterface) CheckK8sVersion(ctx context.CsmContext, istioVersion, k8sVersion string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckK8sVersion", ctx, istioVersion, k8sVersion)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckK8sVersion indicates an expected call of CheckK8sVersion.
func (mr *MockServiceInterfaceMockRecorder) CheckK8sVersion(ctx, istioVersion, k8sVersion interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckK8sVersion", reflect.TypeOf((*MockServiceInterface)(nil).CheckK8sVersion), ctx, istioVersion, k8sVersion)
}

// CheckNodeOS mocks base method.
func (m *MockServiceInterface) CheckNodeOS(ctx context.CsmContext, cceClusterInstances *v2.Instance) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckNodeOS", ctx, cceClusterInstances)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckNodeOS indicates an expected call of CheckNodeOS.
func (mr *MockServiceInterfaceMockRecorder) CheckNodeOS(ctx, cceClusterInstances interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckNodeOS", reflect.TypeOf((*MockServiceInterface)(nil).CheckNodeOS), ctx, cceClusterInstances)
}

// CheckSupportedVersion mocks base method.
func (m *MockServiceInterface) CheckSupportedVersion(ctx context.CsmContext, istioVersion, meshType string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckSupportedVersion", ctx, istioVersion, meshType)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckSupportedVersion indicates an expected call of CheckSupportedVersion.
func (mr *MockServiceInterfaceMockRecorder) CheckSupportedVersion(ctx, istioVersion, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckSupportedVersion", reflect.TypeOf((*MockServiceInterface)(nil).CheckSupportedVersion), ctx, istioVersion, meshType)
}

// CheckVersionType mocks base method.
func (m *MockServiceInterface) CheckVersionType(ctx context.CsmContext, meshType string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckVersionType", ctx, meshType)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckVersionType indicates an expected call of CheckVersionType.
func (mr *MockServiceInterfaceMockRecorder) CheckVersionType(ctx, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckVersionType", reflect.TypeOf((*MockServiceInterface)(nil).CheckVersionType), ctx, meshType)
}

// GetHubWithVersionType mocks base method.
func (m *MockServiceInterface) GetHubWithVersionType(ctx context.CsmContext, meshType string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHubWithVersionType", ctx, meshType)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetHubWithVersionType indicates an expected call of GetHubWithVersionType.
func (mr *MockServiceInterfaceMockRecorder) GetHubWithVersionType(ctx, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHubWithVersionType", reflect.TypeOf((*MockServiceInterface)(nil).GetHubWithVersionType), ctx, meshType)
}

// GetMeshVersionList mocks base method.
func (m *MockServiceInterface) GetMeshVersionList(ctx context.CsmContext, meshType string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMeshVersionList", ctx, meshType)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMeshVersionList indicates an expected call of GetMeshVersionList.
func (mr *MockServiceInterfaceMockRecorder) GetMeshVersionList(ctx, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMeshVersionList", reflect.TypeOf((*MockServiceInterface)(nil).GetMeshVersionList), ctx, meshType)
}
