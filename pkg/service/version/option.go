package version

import (
	"github.com/spf13/viper"
)

const (
	IstioStandaloneVersion = "istio.standalone.version"
	IopStandaloneHub       = "istio.standalone.iop.hub"

	IstioHostingVersion = "istio.hosting.version"
	IopHostingHub       = "istio.hosting.iop.hub"

	NodeOS = "nodeOS"
)

type istioVersionType string

const (
	HostingVersionType    istioVersionType = "hosting"
	StandaloneVersionType istioVersionType = "standalone"
)

// Option 支持的服务网格版本及 node 类型与版本
type Option struct {
	Standalone *Version
	Hosting    *Version
	NodeOS     map[string][]string
}

type Version struct {
	IstioVersion []string
	// istio 相关组件的镜像地址
	IopHub string
}

// NewOption 初始化服务网格版本及 node 类型与版本列表
func NewOption() *Option {
	return &Option{
		Standalone: &Version{
			IstioVersion: viper.GetStringSlice(IstioStandaloneVersion),
			IopHub:       viper.GetString(IopStandaloneHub),
		},
		Hosting: &Version{
			IstioVersion: viper.GetStringSlice(IstioHostingVersion),
			IopHub:       viper.GetString(IopHostingHub),
		},
		NodeOS: viper.GetStringMapStringSlice(NodeOS),
	}
}
