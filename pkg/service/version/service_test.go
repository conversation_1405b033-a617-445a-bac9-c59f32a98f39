package version

import (
	"fmt"
	"testing"

	v2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	versions = []string{"1.13.2", "1.14.5", "1.16.5"}
)

func TestGetMeshVersionList(t *testing.T) {
	testInfos := []struct {
		name        string
		versionType string
		params      []string
		values      []string
		versions    []string
		meshType    string
		expectErr   error
	}{
		{
			name:        "standalone-test",
			versions:    versions,
			versionType: IstioStandaloneVersion,
			params:      []string{"type"},
			meshType:    "standalone",
			values:      []string{"standalone"},
			expectErr:   nil,
		},
		{
			name:        "hosting-test",
			versions:    versions,
			versionType: IstioHostingVersion,
			params:      []string{"type"},
			values:      []string{"hosting"},
			meshType:    "hosting",
			expectErr:   nil,
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			viper.Set(testInfo.versionType, testInfo.versions)
			ctx, _ := csmContext.NewCsmContextMock()
			ctx.SetParamNames(testInfo.params...)
			ctx.SetParamValues(testInfo.values...)
			option := NewOption()
			versionService := NewVersionService(option)
			res, err := versionService.GetMeshVersionList(ctx, testInfo.meshType)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
				assert.Equal(t, res, versions)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestCheckSupportedVersion(t *testing.T) {
	testInfos := []struct {
		name         string
		versionType  string
		params       []string
		values       []string
		versions     []string
		testVersion  string
		testMeshType string
		res          bool
		expectErr    error
	}{
		{
			name:         "standalone-test",
			versions:     versions,
			versionType:  IstioStandaloneVersion,
			params:       []string{"type"},
			values:       []string{"standalone"},
			testVersion:  "1.13.2",
			testMeshType: "standalone",
			res:          true,
			expectErr:    nil,
		},
		{
			name:         "standalone-1.x.2-test",
			versions:     versions,
			versionType:  IstioStandaloneVersion,
			testVersion:  "1.x.2",
			testMeshType: "standalone",
			params:       []string{"type"},
			values:       []string{"standalone"},
			res:          false,
			expectErr:    nil,
		},
		{
			name:         "error-test",
			versions:     versions,
			versionType:  "error",
			testVersion:  "1.13.2",
			testMeshType: "xxx",
			params:       []string{"xxx"},
			values:       []string{"standalone"},
			res:          false,
			expectErr:    fmt.Errorf("not support version type"),
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			viper.Set(testInfo.versionType, testInfo.versions)
			ctx, _ := csmContext.NewCsmContextMock()
			ctx.SetParamNames(testInfo.params...)
			ctx.SetParamValues(testInfo.values...)
			option := NewOption()
			versionService := NewVersionService(option)
			res, err := versionService.CheckSupportedVersion(ctx, testInfo.testVersion, testInfo.testMeshType)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
				assert.Equal(t, res, testInfo.res)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestService_CheckK8sVersion(t *testing.T) {
	tests := []struct {
		name         string
		istioVersion string
		k8sVersion   string
		want         bool
		wantErr      bool
	}{
		{
			name:         "checkK8sVersion-1.13",
			istioVersion: "1.13.2",
			k8sVersion:   "1.20.1",
			want:         true,
			wantErr:      false,
		},
		{
			name:         "checkK8sVersion-1.14",
			istioVersion: "1.14.6-baidu",
			k8sVersion:   "1.22.1",
			want:         true,
			wantErr:      false,
		},
		{
			name:         "checkK8sVersion-1.14",
			istioVersion: "1.14.6",
			k8sVersion:   "1.20.1",
			want:         false,
			wantErr:      false,
		},
		{
			name:         "checkK8sVersion-1.16",
			istioVersion: "1.16.5",
			k8sVersion:   "1.20.1",
			want:         false,
			wantErr:      false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, _ := csmContext.NewCsmContextMock()
			option := NewOption()
			versionService := NewVersionService(option)
			got, err := versionService.CheckK8sVersion(ctx, tt.istioVersion, tt.k8sVersion)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckK8sVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckK8sVersion() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_CheckNodeOS(t *testing.T) {
	tests := []struct {
		name                string
		cceClusterInstances *v2.Instance
		want                bool
	}{
		{
			name: "CheckNodeOS-test",
			cceClusterInstances: &v2.Instance{
				Spec: &types.InstanceSpec{
					InstanceOS: types.InstanceOS{
						OSName:    "CentOS",
						OSVersion: "8.0",
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			viper.Set("nodeOS", "  CentOS:\n    - 7.\n  Ubuntu:\n    - 16\n    - 18\n    - 20\n    - 22")
			ctx, _ := csmContext.NewCsmContextMock()
			option := NewOption()
			versionService := NewVersionService(option)

			got := versionService.CheckNodeOS(ctx, tt.cceClusterInstances)
			if got != tt.want {
				t.Errorf("CheckNodeOS() got = %v, want %v", got, tt.want)
			}
		})
	}
}
