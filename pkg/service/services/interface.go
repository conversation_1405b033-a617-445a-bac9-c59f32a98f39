package services

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetMeshInstanceServices(ctx context.CsmContext, mrp *meta.LaneServiceParams) (*meta.MeshServiceListResponse, error)
	GetServiceDetails(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.MeshServiceDetail, error)
}
