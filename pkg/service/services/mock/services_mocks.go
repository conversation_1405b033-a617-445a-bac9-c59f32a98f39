// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// GetMeshInstanceServices mocks base method.
func (m *MockServiceInterface) GetMeshInstanceServices(ctx context.CsmContext, mrp *meta.LaneServiceParams) (*meta.MeshServiceListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMeshInstanceServices", ctx, mrp)
	ret0, _ := ret[0].(*meta.MeshServiceListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMeshInstanceServices indicates an expected call of GetMeshInstanceServices.
func (mr *MockServiceInterfaceMockRecorder) GetMeshInstanceServices(ctx, mrp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMeshInstanceServices", reflect.TypeOf((*MockServiceInterface)(nil).GetMeshInstanceServices), ctx, mrp)
}

// GetServiceDetails mocks base method.
func (m *MockServiceInterface) GetServiceDetails(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*meta.MeshServiceDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceDetails", ctx, mrp)
	ret0, _ := ret[0].(*meta.MeshServiceDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceDetails indicates an expected call of GetServiceDetails.
func (mr *MockServiceInterfaceMockRecorder) GetServiceDetails(ctx, mrp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceDetails", reflect.TypeOf((*MockServiceInterface)(nil).GetServiceDetails), ctx, mrp)
}
