package services

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	clusterMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	cceServiceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

func TestGetMeshInstanceServices(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name        string
		clusterList *[]meta.Cluster
		testMrp     *meta.LaneServiceParams
		expectErr   error
	}{
		{
			name: "correct-GetMeshInstanceServices",
			clusterList: &[]meta.Cluster{
				{
					ClusterType: string(meta.ClusterTypeExternal),
				},
				{
					ClusterType: string(meta.ClusterTypeRemote),
					Region:      "bj",
					ClusterUUID: "abc",
				},
			},
			testMrp: &meta.LaneServiceParams{
				CsmMeshRequestParams: meta.CsmMeshRequestParams{
					PageNo:      1,
					ServiceHost: "a.b.c",
				},
			},
			expectErr: nil,
		},
		{
			name: "with clusterParams",
			clusterList: &[]meta.Cluster{
				{
					ClusterType: string(meta.ClusterTypeExternal),
				},
				{
					ClusterType: string(meta.ClusterTypeRemote),
					Region:      "bj",
					ClusterUUID: "abc",
				},
			},
			testMrp: &meta.LaneServiceParams{
				CsmMeshRequestParams: meta.CsmMeshRequestParams{
					PageNo:      1,
					ServiceHost: "a.b.c",
				},
				Namespace:     "default",
				ClusterID:     "abc",
				ClusterRegion: "gz",
			},
			expectErr: nil,
		},
		{
			name: "with namespaceParams",
			clusterList: &[]meta.Cluster{
				{
					ClusterType: string(meta.ClusterTypeExternal),
				},
				{
					ClusterType: string(meta.ClusterTypeRemote),
					Region:      "bj",
					ClusterUUID: "abc",
				},
			},
			testMrp: &meta.LaneServiceParams{
				CsmMeshRequestParams: meta.CsmMeshRequestParams{
					PageNo:      1,
					ServiceHost: "a.b.c",
				},
				Namespace:     "default",
				ClusterID:     "abc",
				ClusterRegion: "bj",
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockClusterModel := clusterMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		service := &Service{
			clusterModel: mockClusterModel,
			cceService:   mockCceService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			fakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()
			mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(mockCtx, gomock.Any()).Return(testInfo.clusterList, nil)
			_, err := service.GetMeshInstanceServices(mockCtx, testInfo.testMrp)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestGetServiceDetails(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name        string
		clusterList *[]meta.Cluster
		expectErr   error
	}{
		{
			name: "correct-GetServiceDetails",
			clusterList: &[]meta.Cluster{
				{
					ClusterType: string(meta.ClusterTypeExternal),
				},
				{
					ClusterType: string(meta.ClusterTypeRemote),
					Region:      "bj",
					ClusterUUID: "abc",
				},
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockClusterModel := clusterMock.NewMockServiceInterface(ctrl)
		mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
		service := &Service{
			clusterModel: mockClusterModel,
			cceService:   mockCceService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			fakeClient := kube.NewFakeClient()
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(mockCtx, gomock.Any()).Return(testInfo.clusterList, nil)
			testMrp := &meta.CsmMeshRequestParams{
				ServiceHost: "a.b.c",
			}
			_, err := service.GetServiceDetails(mockCtx, testMrp)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}
