package services

import (
	"context"
	"errors"
	"math"
	"sort"
	"strings"
	"sync"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type Service struct {
	opt          *Option
	clusterModel cluster.ServiceInterface
	cceService   cce.ClientInterface
}

func NewServicesService(option *Option) *Service {
	gormDB := option.DB.DB
	return &Service{
		opt:          option,
		clusterModel: cluster.NewClusterService(cluster.NewOption(gormDB)),
		cceService:   cce.NewClientService(),
	}
}

type serviceCount struct {
	// k8s service count
	ksCount int64
	// service entry count
	seCount int64
}

type safeMap struct {
	ksMtx sync.Mutex
	seMtx sync.Mutex
	mp    map[string]*serviceCount
}

func (smp *safeMap) safeInsert(key string, serviceType string) {
	if serviceType == constants.KsService {
		smp.ksMtx.Lock()
		defer smp.ksMtx.Unlock()
		_, found := smp.mp[key]
		if found {
			smp.mp[key].ksCount++
		} else {
			smp.mp[key] = &serviceCount{ksCount: 1, seCount: 0}
		}
	} else {
		smp.seMtx.Lock()
		defer smp.seMtx.Unlock()
		_, found := smp.mp[key]
		if found {
			smp.mp[key].seCount++
		} else {
			smp.mp[key] = &serviceCount{ksCount: 0, seCount: 1}
		}
	}
}

// pageSlice 按照分页信息返回一个服务列表切片
func pageSlice(svcList []meta.MeshService, mrp *meta.LaneServiceParams) ([]meta.MeshService, int64) {
	// select with keyword "host"
	if mrp.Keyword != "" {
		l := 0
		for _, svc := range svcList {
			if strings.Contains(svc.Host, mrp.Keyword) {
				svcList[l] = svc
				l++
			}
		}
		svcList = svcList[:l]
	}

	// sort the svcList by its host first with desc
	sort.Slice(svcList, func(i, j int) bool {
		return svcList[i].Host > svcList[j].Host
	})
	if strings.ToUpper(mrp.Order) == constants.DBOrderASC {
		// reverse the svcList
		for i, j := 0, len(svcList)-1; i < j; i, j = i+1, j-1 {
			svcList[i], svcList[j] = svcList[j], svcList[i]
		}
	}

	totalCount := int64(len(svcList))
	realPageSize := int64(math.Min(
		math.Max(float64(mrp.PageSize), float64(constants.MinPageSize)), float64(constants.MaxPageSize)))

	maxPageNo := (totalCount + realPageSize - 1) / realPageSize
	realPageNo := int64(math.Min(float64(mrp.PageNo), math.Max(float64(maxPageNo), float64(constants.MinPageNo))))

	startItem := (realPageNo - 1) * realPageSize

	return svcList[startItem:int64(math.Min(float64(startItem+realPageSize), float64(totalCount)))], totalCount
}

// GetMeshInstanceServices 获取网格实例服务列表
func (s *Service) GetMeshInstanceServices(ctx csmContext.CsmContext, mrp *meta.LaneServiceParams) (
	msr *meta.MeshServiceListResponse, err error) {
	meshClusterList, err := s.clusterModel.GetAllClusterByInstanceUUID(ctx, mrp.InstanceUUID)
	if err != nil {
		return nil, err
	}

	serviceCountMap := safeMap{mp: make(map[string]*serviceCount)}
	var wg sync.WaitGroup
	for _, mc := range *meshClusterList {
		if mc.ClusterType == string(meta.ClusterTypeExternal) {
			continue
		}
		// 支持特定集群筛选
		if mrp.ClusterRegion != "" && mrp.ClusterID != "" {
			if !strings.EqualFold(mc.Region, mrp.ClusterRegion) || !strings.EqualFold(mc.ClusterUUID, mrp.ClusterID) {
				continue
			}
		}
		wg.Add(1)
		go func(mc meta.Cluster) {
			cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Errorf("goroutine exited abnormally because: ", e)
				}
				cancel()
				wg.Done()
			}()

			client, err := s.cceService.NewClient(ctx, mc.Region, mc.ClusterUUID, meta.StandaloneMeshType)
			if err != nil {
				return
			}
			// 默认全量获取
			namespace := ""
			// 支持namespace筛选
			if mrp.Namespace != "" {
				namespace = mrp.Namespace
			}
			// get k8s services
			k8sServices, err := client.Kube().CoreV1().Services(namespace).List(cxt, metav1.ListOptions{})
			if err != nil {
				return
			}
			for _, ks := range k8sServices.Items {
				serviceCountMap.safeInsert(ks.Name+"."+ks.Namespace+"."+constants.ClusterDomain, constants.KsService)
			}
			// get service entries
			serviceEntries, err := client.Istio().NetworkingV1beta1().
				ServiceEntries("").List(cxt, metav1.ListOptions{})
			if err != nil {
				return
			}
			for _, se := range serviceEntries.Items {
				for _, host := range se.Spec.Hosts {
					serviceCountMap.safeInsert(host, constants.SeService)
				}
			}
		}(mc)
	}
	wg.Wait()

	var svcList []meta.MeshService
	for host, cnt := range serviceCountMap.mp {
		svc := meta.MeshService{
			Host:              host,
			K8sServiceCount:   cnt.ksCount,
			ServiceEntryCount: cnt.seCount,
		}
		svcList = append(svcList, svc)
	}

	response := &meta.MeshServiceListResponse{
		PageSize: mrp.PageSize,
		PageNo:   mrp.PageNo,
		Order:    mrp.Order,
		OrderBy:  mrp.OrderBy,
	}
	response.Result, response.TotalCount = pageSlice(svcList, mrp)

	return response, nil
}

func extractNamespace(host string) (serviceName string, namespace string, err error) {
	i := strings.Index(host, ".") + 1
	if i == 0 {
		return "", "", errors.New("empty serviceName")
	}
	j := strings.Index(host[i:], ".")
	if j == -1 {
		return "", "", errors.New("empty namespace")
	}
	return host[:(i - 1)], host[i:(i + j)], nil
}

// GetServiceDetails 获取某实例下某服务的详细信息
func (s *Service) GetServiceDetails(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (
	msd *meta.MeshServiceDetail, err error) {
	mcList, err := s.clusterModel.GetAllClusterByInstanceUUID(ctx, mrp.InstanceUUID)
	if err != nil {
		return nil, err
	}

	response := &meta.MeshServiceDetail{}
	var wg sync.WaitGroup
	for _, mc := range *mcList {
		if mc.ClusterType == string(meta.ClusterTypeExternal) {
			continue
		}
		wg.Add(1)
		go func(mc meta.Cluster) {
			cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
			defer func() {
				if e := recover(); e != nil {
					ctx.CsmLogger().Errorf("goroutine exited abnormally because: ", e)
				}
				cancel()
				wg.Done()
			}()
			client, mcErr := s.cceService.NewClient(ctx, mc.Region, mc.ClusterUUID, meta.StandaloneMeshType)
			if mcErr != nil {
				ctx.CsmLogger().Errorf("fail to new cce client when getting service details")
				return
			}
			// get k8s service details
			serviceName, namespace, mcErr := extractNamespace(mrp.ServiceHost)
			if mcErr == nil {
				k8sService, kbErr := client.Kube().CoreV1().Services(namespace).Get(cxt, serviceName, metav1.GetOptions{})
				if kbErr == nil {
					ksd := meta.K8sServiceDetail{
						Region:      mc.Region,
						ClusterId:   mc.ClusterUUID,
						ClusterName: mc.ClusterName,
						ServiceName: k8sService.Name,
						Namespace:   k8sService.Namespace,
					}
					response.KSD = append(response.KSD, ksd)
				}
			}
			// get service entry details
			serviceEntries, mcErr := client.Istio().NetworkingV1beta1().ServiceEntries("").List(cxt, metav1.ListOptions{})
			if mcErr == nil {
				foundSE := false
				for _, serviceEntry := range serviceEntries.Items {
					for _, host := range serviceEntry.Spec.Hosts {
						if mrp.ServiceHost == host {
							foundSE = true
							break
						}
					}
					if foundSE {
						exportTo := ""
						if len(serviceEntry.Spec.ExportTo) == 0 {
							exportTo = constants.ExportToAllNameSpace
						} else {
							for _, et := range serviceEntry.Spec.ExportTo {
								if et == "*" {
									exportTo = constants.ExportToAllNameSpace
									break
								} else if et == "." {
									exportTo = serviceEntry.Namespace
									break
								} else {
									exportTo += et
									exportTo += ","
								}
							}
						}
						sed := meta.ServiceEntryDetail{
							Name:      serviceEntry.Name,
							Namespace: serviceEntry.Namespace,
							ExportTo:  exportTo,
						}
						response.SED = append(response.SED, sed)
						break
					}
				}
			}
		}(mc)
	}
	wg.Wait()
	return response, nil
}
