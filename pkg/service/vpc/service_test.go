package vpc

import (
	"reflect"
	"testing"

	bccApi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/baidubce/bce-sdk-go/services/endpoint"
	"github.com/baidubce/bce-sdk-go/services/vpc"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	modelMeta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	modelVpcMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/vpc/mock"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

var (
	mockCtx, _ = ctxCsm.NewCsmContextMock()
	region     = "bj"
	VpcId      = "aaa"
	Name       = "bbb"
	Cidr       = "ccc"

	subnetId   = "aaa"
	subnetName = "bbb"
	subnetCidr = "ccc"

	securityGroupId   = "xxxx"
	securityGroupName = "ffff"
	securityDesc      = "hahaha"
)

func buildListVPCData() *vpc.ListVPCResult {
	return &vpc.ListVPCResult{
		VPCs: []vpc.VPC{
			{
				VPCID: VpcId,
				Name:  Name,
				Cidr:  Cidr,
			},
		},
	}
}

func buildListVPCExpectData() *meta.VPCResult {
	return &meta.VPCResult{
		VPCs: []meta.VPC{
			{
				VpcID: VpcId,
				Name:  Name,
				Cidr:  Cidr,
			},
		},
	}
}

func TestListVPC(t *testing.T) {
	ctrl := gomock.NewController(t)
	modelVpcMockService := modelVpcMock.NewMockServiceInterface(ctrl)
	service := NewVPCService()
	service.modelVpc = modelVpcMockService
	modelVpcMockService.EXPECT().ListVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildListVPCData(), nil)
	res, err := service.ListVPC(mockCtx, nil, region)
	if err != nil {
		t.Errorf("ListVPC failed. %v", err)
	}
	expect := buildListVPCExpectData()
	if !reflect.DeepEqual(res, expect) {
		t.Errorf("get %v but get %v", res, expect)
	}
}

func buildGetVPCDetailResult() *vpc.GetVPCDetailResult {
	return &vpc.GetVPCDetailResult{VPC: vpc.ShowVPCModel{
		VPCId: VpcId,
		Name:  Name,
		Cidr:  Cidr,
		Subnets: []vpc.Subnet{
			{
				SubnetId: subnetId,
				Name:     subnetName,
				Cidr:     subnetCidr,
				VPCId:    VpcId,
			},
		},
	}}
}

func buildInstanceNetworkTypeData() *modelMeta.InstanceNetworkType {
	return &modelMeta.InstanceNetworkType{
		VpcNetworkId:   VpcId,
		VpcNetworkName: Name,
		VpcNetworkCidr: Cidr,
		SubnetId:       subnetId,
		SubnetName:     subnetName,
		SubnetCidr:     subnetCidr,
	}
}

func TestGetVPCDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	modelVpcMockService := modelVpcMock.NewMockServiceInterface(ctrl)
	service := NewVPCService()
	service.modelVpc = modelVpcMockService
	modelVpcMockService.EXPECT().GetVPCDetail(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildGetVPCDetailResult(), nil)
	res, err := service.GetVPCAndSubnetDetail(mockCtx, VpcId, subnetId, region)
	if err != nil {
		t.Errorf("ListVPC failed. %v", err)
	}
	expect := buildInstanceNetworkTypeData()
	if !reflect.DeepEqual(res, expect) {
		t.Errorf("get %v but get %v", res, expect)
	}
}

func buildListSubnetData() *vpc.ListSubnetResult {
	return &vpc.ListSubnetResult{
		Subnets: []vpc.Subnet{
			{
				SubnetId: subnetId,
				Name:     subnetName,
				Cidr:     subnetCidr,
			},
		},
	}
}

func buildListSubnetExpectData() *meta.SubnetResult {
	return &meta.SubnetResult{
		Subnets: []meta.Subnet{
			{
				SubnetID: subnetId,
				Name:     subnetName,
				Cidr:     subnetCidr,
			},
		},
	}
}

func TestListSubnet(t *testing.T) {
	ctrl := gomock.NewController(t)
	modelVpcMockService := modelVpcMock.NewMockServiceInterface(ctrl)
	service := NewVPCService()
	service.modelVpc = modelVpcMockService
	modelVpcMockService.EXPECT().ListSubnet(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildListSubnetData(), nil)
	res, err := service.ListSubnet(mockCtx, &meta.SubnetArgs{VpcID: VpcId}, region)
	if err != nil {
		t.Errorf("ListSubnet failed. %v", err)
	}
	expect := buildListSubnetExpectData()
	if !reflect.DeepEqual(res, expect) {
		t.Errorf("get %v but get %v", res, expect)
	}
}

func buildListSecurityGroupData() *bccApi.ListSecurityGroupResult {
	return &bccApi.ListSecurityGroupResult{
		SecurityGroups: []bccApi.SecurityGroupModel{
			{
				Id:   securityGroupId,
				Name: securityGroupName,
				Desc: securityDesc,
			},
		},
	}
}

func buildListSecurityGroupExpectData() *meta.SecurityGroupResult {
	return &meta.SecurityGroupResult{
		SecurityGroups: []meta.SecurityGroup{
			{
				Name: securityGroupName,
				ID:   securityGroupId,
				Desc: securityDesc,
			},
		},
	}
}

func TestListSecurityGroup(t *testing.T) {
	ctrl := gomock.NewController(t)
	modelVpcMockService := modelVpcMock.NewMockServiceInterface(ctrl)
	service := NewVPCService()
	service.modelVpc = modelVpcMockService
	modelVpcMockService.EXPECT().ListSecurityGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(buildListSecurityGroupData(), nil)
	res, err := service.ListSecurityGroup(mockCtx, &meta.SecurityGroupArgs{VpcID: VpcId}, region)
	if err != nil {
		t.Errorf("ListSecurityGroup failed. %v", err)
	}
	expect := buildListSecurityGroupExpectData()
	if !reflect.DeepEqual(res, expect) {
		t.Errorf("get %v but get %v", res, expect)
	}
}

func buildMockCreateEndpointResult() *endpoint.CreateEndpointResult {
	return &endpoint.CreateEndpointResult{
		Id:        "xxx",
		IpAddress: "*******",
	}
}

func TestCreateEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	modelVpcMockService := modelVpcMock.NewMockServiceInterface(ctrl)
	service := Service{
		modelVpc: modelVpcMockService,
	}
	modelVpcMockService.EXPECT().CreateEndpoint(gomock.Any(), gomock.Any(),
		gomock.Any()).Return(buildMockCreateEndpointResult(), nil)
	expect := buildMockCreateEndpointResult()
	res, err := service.CreateEndpoint(mockCtx, nil, region)
	assert.Nil(t, err)
	if !reflect.DeepEqual(res, expect) {
		t.Errorf("get %v but get %v", res, expect)
	}
}

func TestDeleteEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	modelVpcMockService := modelVpcMock.NewMockServiceInterface(ctrl)
	service := Service{
		modelVpc: modelVpcMockService,
	}
	modelVpcMockService.EXPECT().DeleteEndpoint(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	err := service.DeleteEndpoint(mockCtx, "xxx", region)
	assert.Nil(t, err)
}

func buildMockEndpoint() *endpoint.Endpoint {
	return &endpoint.Endpoint{
		EndpointId: "xxx",
		Name:       "xxx",
		IpAddress:  "*******",
	}
}

func TestGetEndpointDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	modelVpcMockService := modelVpcMock.NewMockServiceInterface(ctrl)
	service := Service{
		modelVpc: modelVpcMockService,
	}
	modelVpcMockService.EXPECT().GetEndpointDetail(gomock.Any(), gomock.Any(),
		gomock.Any()).Return(buildMockEndpoint(), nil)
	res, err := service.GetEndpointDetail(mockCtx, "xxx", region)
	assert.Nil(t, err)
	expect := buildMockEndpoint()
	if !reflect.DeepEqual(res, expect) {
		t.Errorf("get %v but get %v", res, expect)
	}
}

func TestListEndpoints(t *testing.T) {
	ctrl := gomock.NewController(t)
	modelVpcMockService := modelVpcMock.NewMockServiceInterface(ctrl)
	service := Service{
		modelVpc: modelVpcMockService,
	}
	modelVpcMockService.EXPECT().ListEndpoints(gomock.Any(), gomock.Any(),
		gomock.Any()).Return(nil, nil)
	res, err := service.ListEndpoints(mockCtx, nil, region)
	assert.Nil(t, err)
	assert.Nil(t, res)
}
