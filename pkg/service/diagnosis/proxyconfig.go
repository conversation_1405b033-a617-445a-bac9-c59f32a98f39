package diagnosis

import (
	"encoding/json"
	"errors"
	"fmt"

	diagnosis_cluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis/clusters"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis/configdump"
	service_meata "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

const (
	defaultProxyAdminPort = 15000
	configDump            = "configDump"
)

func getPath(path string) string {
	var res string
	pc := configdump.ConvertToProxyConfig(path)
	switch pc {
	case configdump.Cluster, configdump.Secret, configdump.Listener, configdump.Route, configdump.Bootstrap, configdump.ConfigDump:
		res = "config_dump"
	case configdump.Endpoint:
		res = "clusters?format=json"
	}
	return res
}

func analyzeProxyConfig(bt []byte, filter configdump.Filter) ([]byte, int64, error) {
	var count int64
	wrapper := &configdump.Wrapper{}
	clusterWrapper := &diagnosis_cluster.Wrapper{}
	if filter.ProxyConfig == configdump.Endpoint {
		err := json.Unmarshal(bt, clusterWrapper)
		if err != nil {
			return nil, count, fmt.Errorf("error unmarshalling config dump response from Envoy: %v", err)
		}
	} else {
		err := json.Unmarshal(bt, wrapper)
		if err != nil {
			return nil, count, fmt.Errorf("error unmarshalling config dump response from Envoy: %v", err)
		}
	}

	switch filter.ProxyConfig {
	case configdump.Cluster:
		cluster, err := configdump.GetClusters(wrapper, filter.ClusterFilter)
		if err != nil {
			return nil, count, err
		}
		return pageResource(filter.Page, cluster)
	case configdump.Listener:
		listener, err := configdump.GetListener(wrapper, filter.ListenerFilter)
		if err != nil {
			return nil, count, err
		}
		return pageResource(filter.Page, listener)
	case configdump.Route:
		route, err := configdump.GetRoute(wrapper, filter.RouteFilter)
		if err != nil {
			return nil, count, err
		}
		return pageResource(filter.Page, route)
	case configdump.Endpoint:
		clusters, err := configdump.GetEndpoint(clusterWrapper, filter.EndpointFilter)
		if err != nil {
			return nil, count, err
		}
		return pageResource(filter.Page, clusters)
	case configdump.Secret:
		secrets, err := configdump.GetSecret(wrapper)
		if err != nil {
			return nil, count, err
		}
		return pageResource(filter.Page, secrets)
	case configdump.Bootstrap:
		boostrap, err := configdump.GetBootstrap(wrapper)
		if err != nil {
			return nil, count, err
		}
		return pageResource(filter.Page, boostrap)
	default:
		return nil, count, errors.New("type not support")
	}
}

func pageResource(page *vo.Page, resource interface{}) ([]byte, int64, error) {
	// TODO 是否有更好的实现方式
	var res interface{}
	start := (page.PageNo - 1) * page.PageSize
	total := int64(0)
	switch v := resource.(type) {
	case []*service_meata.PCCluster:
		total = int64(len(v))
		end := util.MinInt64(page.PageNo*page.PageSize, total)
		if start >= end {
			res = v
		} else {
			res = v[start:end]
		}
	case []*service_meata.PCBootstrap:
		total = int64(len(v))
		end := util.MinInt64(page.PageNo*page.PageSize, total)
		if start >= end {
			res = v
		} else {
			res = v[start:end]
		}
	case []*service_meata.PCListener:
		total = int64(len(v))
		end := util.MinInt64(page.PageNo*page.PageSize, total)
		if start >= end {
			res = v
		} else {
			res = v[start:end]
		}
	case []*service_meata.PCRoute:
		total = int64(len(v))
		end := util.MinInt64(page.PageNo*page.PageSize, total)
		if start >= end {
			res = v
		} else {
			res = v[start:end]
		}
	case []*service_meata.PCEndpoint:
		total = int64(len(v))
		end := util.MinInt64(page.PageNo*page.PageSize, total)
		if start >= end {
			res = v
		} else {
			res = v[start:end]
		}
	case []*service_meata.PCSecret:
		total = int64(len(v))
		end := util.MinInt64(page.PageNo*page.PageSize, total)
		if start >= end {
			res = v
		} else {
			res = v[start:end]
		}
	default:
		return nil, total, errors.New("type not support")
	}
	bt, err := json.Marshal(res)
	if err != nil {
		return nil, total, err
	}
	return bt, total, nil
}
