package configdump

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	adminapi "github.com/envoyproxy/go-control-plane/envoy/admin/v3"
	"github.com/golang/protobuf/jsonpb"
	legacyproto "github.com/golang/protobuf/proto" // nolint: staticcheck
	exprpb "google.golang.org/genproto/googleapis/api/expr/v1alpha1"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/emptypb"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

type configTypeURL string

// See https://www.envoyproxy.io/docs/envoy/latest/api-v3/admin/v3/config_dump.proto
const (
	bootstrap configTypeURL = "type.googleapis.com/envoy.admin.v3.BootstrapConfigDump"
	//endpoints configTypeURL = "type.googleapis.com/envoy.admin.v3.EndpointsConfigDump"
	secrets   configTypeURL = "type.googleapis.com/envoy.admin.v3.SecretsConfigDump"
	clusters  configTypeURL = "type.googleapis.com/envoy.admin.v3.ClustersConfigDump"
	listeners configTypeURL = "type.googleapis.com/envoy.admin.v3.ListenersConfigDump"
	routes    configTypeURL = "type.googleapis.com/envoy.admin.v3.RoutesConfigDump"
)

// nonstrictResolver is an AnyResolver that ignores unknown proto messages
type nonstrictResolver struct{}

var envoyResolver nonstrictResolver

func (m *nonstrictResolver) Resolve(typeURL string) (legacyproto.Message, error) {
	// See https://github.com/golang/protobuf/issues/747#issuecomment-437463120
	mname := typeURL
	if slash := strings.LastIndex(typeURL, "/"); slash >= 0 {
		mname = mname[slash+1:]
	}
	// nolint: staticcheck
	mt := legacyproto.MessageType(mname)
	if mt == nil {
		// istioctl should keep going if it encounters new Envoy versions; ignore unknown types
		return &exprpb.Type{TypeKind: &exprpb.Type_Dyn{Dyn: &emptypb.Empty{}}}, nil
	}
	return reflect.New(mt.Elem()).Interface().(legacyproto.Message), nil
}

// Wrapper is a wrapper around the Envoy ConfigDump
// It has extra helper functions for handling any/struct/marshal protobuf pain
type Wrapper struct {
	*adminapi.ConfigDump
}

// MarshalJSON is a custom marshaller to handle protobuf pain
func (w *Wrapper) MarshalJSON() ([]byte, error) {
	buffer := &bytes.Buffer{}
	err := (&jsonpb.Marshaler{}).Marshal(buffer, w)
	if err != nil {
		return nil, err
	}
	return buffer.Bytes(), nil
}

// UnmarshalJSON is a custom unmarshaller to handle protobuf pain
func (w *Wrapper) UnmarshalJSON(b []byte) error {
	cd := &adminapi.ConfigDump{}
	err := (&jsonpb.Unmarshaler{
		AllowUnknownFields: true,
		AnyResolver:        &envoyResolver,
	}).Unmarshal(bytes.NewReader(b), cd)
	*w = Wrapper{cd}
	return err
}

// GetClusterConfigDump retrieves the cluster config dump from the ConfigDump
func (w *Wrapper) GetClusterConfigDump() (*adminapi.ClustersConfigDump, error) {
	clusterDumpAny, err := w.getSection(clusters)
	if err != nil {
		return nil, err
	}
	clusterDump := &adminapi.ClustersConfigDump{}
	err = clusterDumpAny.UnmarshalTo(clusterDump)
	if err != nil {
		return nil, err
	}
	return clusterDump, nil
}

// GetListenerConfigDump retrieves the listener config dump from the ConfigDump
func (w *Wrapper) GetListenerConfigDump() (*adminapi.ListenersConfigDump, error) {
	listenerDumpAny, err := w.getSection(listeners)
	if err != nil {
		return nil, err
	}
	listenerDump := &adminapi.ListenersConfigDump{}
	err = listenerDumpAny.UnmarshalTo(listenerDump)
	if err != nil {
		return nil, err
	}
	return listenerDump, nil
}

// GetRouteConfigDump retrieves the route config dump from the ConfigDump
func (w *Wrapper) GetRouteConfigDump() (*adminapi.RoutesConfigDump, error) {
	routeDumpAny, err := w.getSection(routes)
	if err != nil {
		return nil, err
	}
	routeDump := &adminapi.RoutesConfigDump{}
	err = routeDumpAny.UnmarshalTo(routeDump)
	if err != nil {
		return nil, err
	}
	return routeDump, nil
}

// GetSecretConfigDump retrieves a secret dump from a config dump wrapper
func (w *Wrapper) GetSecretConfigDump() (*adminapi.SecretsConfigDump, error) {
	secretDumpAny, err := w.getSection(secrets)
	if err != nil {
		return nil, err
	}
	secretDump := &adminapi.SecretsConfigDump{}
	err = secretDumpAny.UnmarshalTo(secretDump)
	if err != nil {
		return nil, err
	}
	return secretDump, nil
}

// GetBootstrapConfigDump retrieves the bootstrap config dump from the ConfigDump
func (w *Wrapper) GetBootstrapConfigDump() (*adminapi.BootstrapConfigDump, error) {
	bootstrapDumpAny, err := w.getSection(bootstrap)
	if err != nil {
		return nil, err
	}
	bootstrapDump := &adminapi.BootstrapConfigDump{}
	err = bootstrapDumpAny.UnmarshalTo(bootstrapDump)
	if err != nil {
		return nil, err
	}
	return bootstrapDump, nil
}

// getSection takes a TypeURL and returns the types.Any from the config dump corresponding to that URL
func (w *Wrapper) getSection(sectionTypeURL configTypeURL) (*anypb.Any, error) {
	var dumpAny *anypb.Any
	for _, conf := range w.Configs {
		if conf.TypeUrl == string(sectionTypeURL) {
			dumpAny = conf
		}
	}
	if dumpAny == nil {
		return nil, fmt.Errorf("config dump has no configuration type %s", sectionTypeURL)
	}

	return dumpAny, nil
}

type ProxyConfig string

const (
	Secret     ProxyConfig = "secret"
	Bootstrap  ProxyConfig = "bootstrap"
	Cluster    ProxyConfig = "cluster"
	Listener   ProxyConfig = "listener"
	Route      ProxyConfig = "route"
	Endpoint   ProxyConfig = "endpoint"
	ConfigDump ProxyConfig = "configDump"
)

func ConvertToProxyConfig(category string) ProxyConfig {
	return ProxyConfig(category)
}

func IsValidProxyConfig(pc ProxyConfig) bool {
	if pc == Secret || pc == Bootstrap || pc == Cluster ||
		pc == Listener || pc == Route || pc == Endpoint || pc == ConfigDump {
		return true
	}
	return false
}

// Filter is used to pass filter information into cluster based config writer print functions
type Filter struct {
	Page *vo.Page
	ProxyConfig
	ClusterFilter  *ClusterFilter
	ListenerFilter *ListenerFilter
	RouteFilter    *RouteFilter
	EndpointFilter *EndpointFilter
}
