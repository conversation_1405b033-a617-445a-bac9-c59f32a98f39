package configdump

import (
	"fmt"
	"sort"
	"strconv"
	"strings"

	adminapi "github.com/envoyproxy/go-control-plane/envoy/admin/v3"
	core "github.com/envoyproxy/go-control-plane/envoy/config/core/v3"

	diagnosis_cluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis/clusters"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

// EndpointFilter is used to pass filter information into route based config writer print functions
type EndpointFilter struct {
	Address string
	Port    uint32
	Cluster string
	Status  string
}

// EndpointCluster is used to store the endpoint and cluster
type EndpointCluster struct {
	address            string
	port               int
	cluster            string
	status             core.HealthStatus
	failedOutlierCheck bool
}

// Verify returns true if the passed host matches the filter fields
func (e *EndpointFilter) Verify(host *adminapi.HostStatus, cluster string) bool {
	if e.Address == "" && e.Port == 0 && e.Cluster == "" && e.Status == "" {
		return true
	}
	if e.Address != "" && !strings.EqualFold(retrieveEndpointAddress(host), e.Address) {
		return false
	}
	if e.Port != 0 && retrieveEndpointPort(host) != e.Port {
		return false
	}
	if e.Cluster != "" && !strings.EqualFold(cluster, e.Cluster) {
		return false
	}
	status := retrieveEndpointStatus(host)
	if e.Status != "" && !strings.EqualFold(core.HealthStatus_name[int32(status)], e.Status) {
		return false
	}
	return true
}

func retrieveEndpointAddress(host *adminapi.HostStatus) string {
	addr := host.Address.GetSocketAddress()
	if addr != nil {
		return addr.Address
	}
	if pipe := host.Address.GetPipe(); pipe != nil {
		return "unix://" + pipe.Path
	}
	if internal := host.Address.GetEnvoyInternalAddress(); internal != nil {
		switch an := internal.GetAddressNameSpecifier().(type) {
		case *core.EnvoyInternalAddress_ServerListenerName:
			return fmt.Sprintf("envoy://%s/%s", an.ServerListenerName, internal.String())
		}
	}
	return "unknown"
}

func retrieveEndpointPort(l *adminapi.HostStatus) uint32 {
	addr := l.Address.GetSocketAddress()
	if addr != nil {
		return addr.GetPortValue()
	}
	return 0
}

func retrieveEndpointStatus(l *adminapi.HostStatus) core.HealthStatus {
	return l.HealthStatus.GetEdsHealthStatus()
}

func GetEndpoint(wrapper *diagnosis_cluster.Wrapper, filter *EndpointFilter) ([]*meta.PCEndpoint, error) {
	metaEndpoint := make([]*meta.PCEndpoint, 0)
	clusterEndpoint := make([]EndpointCluster, 0)
	for _, cluster := range wrapper.ClusterStatuses {
		for _, host := range cluster.HostStatuses {
			if filter.Verify(host, cluster.Name) {
				addr := retrieveEndpointAddress(host)
				port := retrieveEndpointPort(host)
				status := retrieveEndpointStatus(host)
				outlierCheck := retrieveFailedOutlierCheck(host)
				clusterEndpoint = append(clusterEndpoint, EndpointCluster{
					address:            addr,
					port:               int(port),
					cluster:            cluster.Name,
					status:             status,
					failedOutlierCheck: outlierCheck,
				})
			}
		}
	}

	sort.Slice(clusterEndpoint, func(i, j int) bool {
		if clusterEndpoint[i].address == clusterEndpoint[j].address {
			if clusterEndpoint[i].port == clusterEndpoint[j].port {
				return clusterEndpoint[i].cluster < clusterEndpoint[j].cluster
			}
			return clusterEndpoint[i].port < clusterEndpoint[j].port
		}
		return clusterEndpoint[i].address < clusterEndpoint[j].address
	})

	for _, ce := range clusterEndpoint {
		var endpoint string
		if ce.port != 0 {
			endpoint = ce.address + ":" + strconv.Itoa(ce.port)
		} else {
			endpoint = ce.address
		}
		metaEndpoint = append(metaEndpoint, &meta.PCEndpoint{
			Endpoint:     endpoint,
			Status:       core.HealthStatus_name[int32(ce.status)],
			OutLierCheck: printFailedOutlierCheck(ce.failedOutlierCheck),
			Cluster:      ce.cluster,
		})
	}
	return metaEndpoint, nil
}

func retrieveFailedOutlierCheck(l *adminapi.HostStatus) bool {
	return l.HealthStatus.GetFailedOutlierCheck()
}

func printFailedOutlierCheck(b bool) string {
	if b {
		return "FAILED"
	}
	return "OK"
}
