package configdump

import (
	"fmt"
	"sort"
	"strconv"
	"strings"

	route "github.com/envoyproxy/go-control-plane/envoy/config/route/v3"
	"github.com/envoyproxy/go-control-plane/pkg/resource/v3"
	"k8s.io/apimachinery/pkg/util/sets"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

// RouteFilter is used to pass filter information into route based config writer print functions
type RouteFilter struct {
	Name    string
	Verbose bool
}

// Verify returns true if the passed route matches the filter fields
func (r *RouteFilter) Verify(route *route.RouteConfiguration) bool {
	if r.Name != "" && r.Name != route.Name {
		return false
	}
	return true
}

func GetRoute(wrapper *Wrapper, filter *RouteFilter) ([]*meta.PCRoute, error) {
	metaRoute := make([]*meta.PCRoute, 0)
	allRoutes, err := retrieveSortedRouteSlice(wrapper)
	if err != nil {
		return metaRoute, err
	}
	for _, route := range allRoutes {
		if filter.Verify(route) {
			for _, vhosts := range route.GetVirtualHosts() {
				if len(vhosts.Routes) == 0 {
					metaRoute = append(metaRoute, &meta.PCRoute{
						Name:        route.Name,
						RouteDomain: describeRouteDomains(vhosts.GetDomains()),
						MatchRule:   "/*",
						VhostName:   "404",
					})
				} else {
					for _, r := range vhosts.Routes {
						if !isPassthrough(r.GetAction()) {
							metaRoute = append(metaRoute, &meta.PCRoute{
								Name:        route.Name,
								RouteDomain: describeRouteDomains(vhosts.GetDomains()),
								MatchRule:   describeMatch(r.GetMatch()),
								VhostName:   describeManagement(r.GetMetadata()),
							})
						}
					}
				}
			}
		}
	}
	return metaRoute, nil
}

func describeRouteDomains(domains []string) string {
	if len(domains) == 0 {
		return ""
	}
	if len(domains) == 1 {
		return domains[0]
	}

	// Return the shortest non-numeric domain.  Count of domains seems uninteresting.
	max := 2
	withoutPort := make([]string, 0, len(domains))
	for _, d := range domains {
		if !strings.Contains(d, ":") {
			withoutPort = append(withoutPort, d)
			// if the domain contains IPv6, such as [fd00:10:96::7fc7] and [fd00:10:96::7fc7]:8090
		} else if strings.Count(d, ":") > 2 {
			// if the domain is only a IPv6 address, such as [fd00:10:96::7fc7], append it
			if strings.HasSuffix(d, "]") {
				withoutPort = append(withoutPort, d)
			}
		}
	}
	withoutPort = unexpandDomains(withoutPort)
	if len(withoutPort) > max {
		ret := strings.Join(withoutPort[:max], ", ")
		return fmt.Sprintf("%s + %d more...", ret, len(withoutPort)-max)
	}
	return strings.Join(withoutPort, ", ")
}

func unexpandDomains(domains []string) []string {
	unique := sets.New(domains...)
	shouldDelete := sets.New[string]()
	for _, h := range domains {
		stripFull := strings.TrimSuffix(h, ".svc.cluster.local")
		if _, f := unique[stripFull]; f && stripFull != h {
			shouldDelete.Insert(h)
		}
		stripPartial := strings.TrimSuffix(h, ".svc")
		if _, f := unique[stripPartial]; f && stripPartial != h {
			shouldDelete.Insert(h)
		}
	}
	// Filter from original list to keep original order
	ret := make([]string, 0, len(domains))
	for _, h := range domains {
		if _, f := shouldDelete[h]; !f {
			ret = append(ret, h)
		}
	}
	return ret
}

func isPassthrough(action any) bool {
	a, ok := action.(*route.Route_Route)
	if !ok {
		return false
	}
	cl, ok := a.Route.ClusterSpecifier.(*route.RouteAction_Cluster)
	if !ok {
		return false
	}
	return cl.Cluster == "PassthroughCluster"
}

func retrieveSortedRouteSlice(wp *Wrapper) ([]*route.RouteConfiguration, error) {
	if wp == nil {
		return nil, fmt.Errorf("configdump writer has not been inited")
	}
	routeDump, err := wp.GetRouteConfigDump()
	if err != nil {
		return nil, err
	}
	allRoutes := make([]*route.RouteConfiguration, 0)
	for _, r := range routeDump.DynamicRouteConfigs {
		if r.RouteConfig != nil {
			routeTyped := &route.RouteConfiguration{}
			// Support v2 or v3 in config dump. See ads.go:RequestedTypes for more info.
			r.RouteConfig.TypeUrl = resource.RouteType
			err = r.RouteConfig.UnmarshalTo(routeTyped)
			if err != nil {
				return nil, err
			}
			allRoutes = append(allRoutes, routeTyped)
		}
	}
	for _, r := range routeDump.StaticRouteConfigs {
		if r.RouteConfig != nil {
			routeTyped := &route.RouteConfiguration{}
			// Support v2 or v3 in config dump. See ads.go:RequestedTypes for more info.
			r.RouteConfig.TypeUrl = resource.RouteType
			err = r.RouteConfig.UnmarshalTo(routeTyped)
			if err != nil {
				return nil, err
			}
			allRoutes = append(allRoutes, routeTyped)
		}
	}
	sort.Slice(allRoutes, func(i, j int) bool {
		iName, err := strconv.Atoi(allRoutes[i].Name)
		if err != nil {
			return false
		}
		jName, err := strconv.Atoi(allRoutes[j].Name)
		if err != nil {
			return false
		}
		return iName < jName
	})
	return allRoutes, nil
}
