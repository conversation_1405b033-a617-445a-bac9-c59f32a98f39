package configdump

import (
	"fmt"
	"sort"
	"strconv"
	"strings"

	cluster "github.com/envoyproxy/go-control-plane/envoy/config/cluster/v3"
	envoy_config_core_v3 "github.com/envoyproxy/go-control-plane/envoy/config/core/v3"
	"github.com/envoyproxy/go-control-plane/pkg/resource/v3"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/diagnosis/host"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/meta"
)

const (
	// IstioMetadataKey is the key under which metadata is added to a route or cluster
	// regarding the virtual service or destination rule used for each
	IstioMetadataKey = "istio"
)

// TrafficDirection defines whether traffic exists a service instance or enters a service instance
type TrafficDirection string

const (
	// TrafficDirectionInbound indicates inbound traffic
	TrafficDirectionInbound TrafficDirection = "inbound"
	// TrafficDirectionOutbound indicates outbound traffic
	TrafficDirectionOutbound TrafficDirection = "outbound"

	// trafficDirectionOutboundSrvPrefix the prefix for a DNS SRV type subset key
	trafficDirectionOutboundSrvPrefix = string(TrafficDirectionOutbound) + "_"
	// trafficDirectionInboundSrvPrefix the prefix for a DNS SRV type subset key
	trafficDirectionInboundSrvPrefix = string(TrafficDirectionInbound) + "_"
)

// ClusterFilter is used to pass filter information into cluster based config writer print functions
type ClusterFilter struct {
	FQDN      host.Name
	Direction TrafficDirection
	Port      int
	Subset    string
}

// Verify returns true if the passed cluster matches the filter fields
func (c *ClusterFilter) Verify(cluster *cluster.Cluster) bool {
	name := cluster.Name
	if c.FQDN == "" && c.Port == 0 && c.Subset == "" && c.Direction == "" {
		return true
	}
	if c.FQDN != "" && !strings.Contains(name, string(c.FQDN)) {
		return false
	}
	if c.Direction != "" && !strings.Contains(name, string(c.Direction)) {
		return false
	}
	if c.Subset != "" && !strings.Contains(name, c.Subset) {
		return false
	}
	if c.Port != 0 {
		p := fmt.Sprintf("|%v|", c.Port)
		if !strings.Contains(name, p) {
			return false
		}
	}
	return true
}

func GetClusters(wrapper *Wrapper, filter *ClusterFilter) ([]*meta.PCCluster, error) {
	metaCluster := make([]*meta.PCCluster, 0)
	allClusters, err := retrieveSortedClusterSlice(wrapper)
	if err != nil {
		return metaCluster, err
	}

	for _, c := range allClusters {
		if filter.Verify(c) {
			if len(strings.Split(c.Name, "|")) > 3 {
				direction, subset, fqdn, port := ParseSubsetKey(c.Name)
				if subset == "" {
					subset = "-"
				}
				metaCluster = append(metaCluster, &meta.PCCluster{
					Subset:          subset,
					ServiceFqdn:     fqdn.String(),
					Port:            strconv.Itoa(port),
					Direction:       string(direction),
					Type:            c.GetType().String(),
					DestinationRule: describeManagement(c.GetMetadata()),
				})
			} else {
				metaCluster = append(metaCluster, &meta.PCCluster{
					ServiceFqdn:     c.Name,
					Port:            "-",
					Subset:          "-",
					Direction:       "-",
					Type:            c.GetType().String(),
					DestinationRule: describeManagement(c.GetMetadata()),
				})
			}
		}
	}
	return metaCluster, nil
}

func describeManagement(metadata *envoy_config_core_v3.Metadata) string {
	if metadata == nil {
		return ""
	}
	istioMetadata, ok := metadata.FilterMetadata[IstioMetadataKey]
	if !ok {
		return ""
	}
	config, ok := istioMetadata.Fields["config"]
	if !ok {
		return ""
	}
	return renderConfig(config.GetStringValue())
}

func renderConfig(configPath string) string {
	if strings.HasPrefix(configPath, "/apis/networking.istio.io/v1alpha3/namespaces/") {
		pieces := strings.Split(configPath, "/")
		if len(pieces) != 8 {
			return ""
		}
		return fmt.Sprintf("%s.%s", pieces[7], pieces[5])
	}
	return "<unknown>"
}

func retrieveSortedClusterSlice(wp *Wrapper) ([]*cluster.Cluster, error) {
	if wp == nil {
		return nil, fmt.Errorf("configdump writer has not been inited")
	}
	clusterDump, err := wp.GetClusterConfigDump()
	if err != nil {
		return nil, err
	}
	allClusters := make([]*cluster.Cluster, 0)
	for _, c := range clusterDump.DynamicActiveClusters {
		if c.Cluster != nil {
			clusterTyped := &cluster.Cluster{}
			// Support v2 or v3 in config dump. See ads.go:RequestedTypes for more info.
			c.Cluster.TypeUrl = resource.ClusterType
			err = c.Cluster.UnmarshalTo(clusterTyped)
			if err != nil {
				return nil, err
			}
			allClusters = append(allClusters, clusterTyped)
		}
	}
	for _, c := range clusterDump.StaticClusters {
		if c.Cluster != nil {
			clusterTyped := &cluster.Cluster{}
			// Support v2 or v3 in config dump. See ads.go:RequestedTypes for more info.
			c.Cluster.TypeUrl = resource.ClusterType
			err = c.Cluster.UnmarshalTo(clusterTyped)
			if err != nil {
				return nil, err
			}
			allClusters = append(allClusters, clusterTyped)
		}
	}
	sort.Slice(allClusters, func(i, j int) bool {
		iDirection, iSubset, iName, iPort := safelyParseSubsetKey(allClusters[i].Name)
		jDirection, jSubset, jName, jPort := safelyParseSubsetKey(allClusters[j].Name)
		if iName == jName {
			if iSubset == jSubset {
				if iPort == jPort {
					return iDirection < jDirection
				}
				return iPort < jPort
			}
			return iSubset < jSubset
		}
		return iName < jName
	})
	return allClusters, nil
}

func safelyParseSubsetKey(key string) (TrafficDirection, string, host.Name, int) {
	if len(strings.Split(key, "|")) > 3 {
		return ParseSubsetKey(key)
	}
	name := host.Name(key)
	return "", "", name, 0
}

// ParseSubsetKey is the inverse of the BuildSubsetKey method
func ParseSubsetKey(s string) (direction TrafficDirection, subsetName string, hostname host.Name, port int) {
	var parts []string
	dnsSrvMode := false
	// This could be the DNS srv form of the cluster that uses outbound_.port_.subset_.hostname
	// Since we do not want every callsite to implement the logic to differentiate between the two forms
	// we add an alternate parser here.
	if strings.HasPrefix(s, trafficDirectionOutboundSrvPrefix) ||
		strings.HasPrefix(s, trafficDirectionInboundSrvPrefix) {
		parts = strings.SplitN(s, ".", 4)
		dnsSrvMode = true
	} else {
		parts = strings.Split(s, "|")
	}

	if len(parts) < 4 {
		return
	}

	direction = TrafficDirection(strings.TrimSuffix(parts[0], "_"))
	port, _ = strconv.Atoi(strings.TrimSuffix(parts[1], "_"))
	subsetName = parts[2]

	if dnsSrvMode {
		subsetName = strings.TrimSuffix(parts[2], "_")
	}

	hostname = host.Name(parts[3])
	return
}
