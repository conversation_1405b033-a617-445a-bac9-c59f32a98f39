package diagnosis

import (
	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

const (
	cloudHostingRegion            = "cloud.hostingRegion"
	cloudHostingRegionClusterId   = "clusterid"
	cloudHostingRegionClusterName = "clustername"
)

type Option struct {
	DB            *dbutil.DB
	HostingRegion interface{}
}

func NewOption(d *gorm.DB) *Option {
	hostingRegion := viper.Get(cloudHostingRegion)
	return &Option{
		DB:            dbutil.NewDB(d),
		HostingRegion: hostingRegion,
	}
}

func (opt *Option) GetHostingCluster(region string) (string, string) {
	if opt.HostingRegion == nil {
		return "", ""
	}
	allRegions := opt.HostingRegion.(map[string]interface{})
	if allRegions == nil {
		return "", ""
	}
	clusters := allRegions[region]
	if clusters == nil {
		return "", ""
	}
	cluster := clusters.(map[string]interface{})
	if cluster == nil {
		return "", ""
	}
	return cluster[cloudHostingRegionClusterId].(string), cluster[cloudHostingRegionClusterName].(string)
}
