package monitor

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	mockCluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	mockInstance "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	mockMonitor "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/monitor/mock"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	cceServiceMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

var (
	mockDB, _  = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx, _ = ctxCsm.NewCsmContextMock()

	retentionPeriod       = "15d"
	monitorGrafanaId      = "grafana-w3tivadu2"
	monitorGrafanaName    = "cprom"
	monitorInstanceId     = "cprom-instance-id"
	monitorInstanceName   = "cprom-instance-name"
	monitorInstanceRegion = "gz"
	monitorEnabled        = true
	agentId               = "agent-id-test"
	istioJobId            = "istio-job-id"
	envoyJobId            = "envoy-job-id"

	instanceUUID               = "test"
	instanceName               = "test"
	instanceType               = "standalone"
	istioVersion               = "1.13.2"
	accountId                  = "1"
	discoverySelectorEnabled   = true
	discoverySelectorLabels    = "{\"user\": \"test\"}"
	discoverySelectorLabelsMap = map[string]string{"user": "test"}
	istioInstallNamespace      = "istio-system"
	instanceManageScope        = "cluster"
	clusterUUID                = "test-1"
	meshInstanceId             = "test01"

	namespace          = "istio-system"
	name               = "test"
	clusterUuid        = "test-123456"
	clusterName        = "istio-test01"
	clusterType        = "standalone"
	version            = "1.13.2"
	vpcId              = "aaaaaaaa"
	vpcName            = "test-sss"
	status             = "running"
	region             = "bj"
	testInstanceName   = "test-instance"
	testInstanceUUID   = "csm-123456"
	testInstanceType   = "standalone"
	testIstioVersion   = "1.13.2"
	testInstanceStatus = "running"

	testClusterName           = "test-cluster"
	testClusterUUID           = "cce-123456"
	testRegion                = "bj"
	testIstioInstallNamespace = "istio-system"

	clusterUUID1 = "cce-0k355plq"
	clusterUUID2 = "cce-0k355plq"

	clusterName1 = "istio-test01"
	clusterName2 = "istio-test01-remote"
)

func TestGetMonitorInstances(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockMonitorModel := mockMonitor.NewMockServiceInterface(ctrl)
	// mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
	// mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)

	s := &Service{
		MonitorModel: mockMonitorModel,
		// InstanceModel: mockInstanceModel,
		// ClusterModel:  mockClusterModel,
	}
	cpis := make([]meta.CPromInstance, 0)
	cpi := meta.CPromInstance{
		InstanceName: monitorInstanceName,
		InstanceId:   monitorInstanceId,
		Region:       monitorInstanceRegion,
	}
	cpis = append(cpis, cpi)

	mockMonitorModel.EXPECT().GetCPromInstancesByRegion(gomock.Any(), gomock.Any()).Return(cpis, nil)
	mockMonitorModel.EXPECT().GetCpromAgentList(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(buildCPromAgentsResult(), nil).AnyTimes()

	mockCtx.Request().Header.Set(constants.RegionHeaderKey, "gz")
	viper.Set("cloud.regions", []string{"gz", "bd", "bj", "su"})
	mis, err := s.GetMonitorInstances(mockCtx, clusterUUID1, "")
	if err != nil {
		t.Error()
	}

	assert.Equal(t, len(mis.Instances), 1)
	assert.Equal(t, mis.Instances[0].ID, monitorInstanceId)
	assert.Equal(t, mis.Instances[0].Name, monitorInstanceName)
	assert.Equal(t, mis.Instances[0].Region, monitorInstanceRegion)
}

func TestGetMonitorInstancesByHosting(t *testing.T) {
	testInfos := []struct {
		name           string
		clusterID      string
		instanceID     string
		instanceType   string
		success        bool
		expectErrorMsg string
	}{
		{
			name:         "success with clusterID and instanceID",
			clusterID:    testClusterUUID,
			instanceID:   testInstanceUUID,
			instanceType: string(meta.HostingMeshType),
			success:      true,
		},
		{
			name:         "success with instanceID",
			clusterID:    "",
			instanceID:   testInstanceUUID,
			instanceType: string(meta.HostingMeshType),
			success:      true,
		},
		{
			name:           "not hosting",
			clusterID:      "",
			instanceID:     testInstanceUUID,
			instanceType:   string(meta.StandaloneMeshType),
			success:        false,
			expectErrorMsg: "instance is not hosting",
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockMonitorModel := mockMonitor.NewMockServiceInterface(ctrl)
			mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
			mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)

			s := &Service{
				MonitorModel:  mockMonitorModel,
				InstanceModel: mockInstanceModel,
				ClusterModel:  mockClusterModel,
			}
			cpis := make([]meta.CPromInstance, 0)
			cpi := meta.CPromInstance{
				InstanceName: monitorInstanceName,
				InstanceId:   monitorInstanceId,
				Region:       monitorInstanceRegion,
			}
			cpis = append(cpis, cpi)

			mockMonitorModel.EXPECT().GetCPromInstancesByRegion(gomock.Any(), gomock.Any()).Return(cpis, nil).AnyTimes()
			mockMonitorModel.EXPECT().GetCpromAgentList(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(buildCPromAgentsResult(), nil).AnyTimes()
			csmInstance := buildMetaInstance()

			csmInstance.InstanceType = testInfo.instanceType
			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(csmInstance, nil)

			mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(gomock.Any(), gomock.Any()).Return(
				buildAllRemoteCluster(testInfo.clusterID), nil).AnyTimes()

			mockCtx.Request().Header.Set(constants.RegionHeaderKey, "gz")
			viper.Set("cloud.regions", []string{"gz", "bd", "bj", "su"})
			mis, err := s.GetMonitorInstances(mockCtx, testInfo.clusterID, testInfo.instanceID)
			if testInfo.success {
				assert.Nil(t, err)
				assert.Equal(t, len(mis.Instances), 1)
				assert.Equal(t, mis.Instances[0].ID, monitorInstanceId)
				assert.Equal(t, mis.Instances[0].Name, monitorInstanceName)
				assert.Equal(t, mis.Instances[0].Region, monitorInstanceRegion)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErrorMsg)
			}
		})
	}
}

func buildCPromItem() *meta.CPromItem {
	return &meta.CPromItem{
		Spec: &meta.CPromItemSpec{
			InstanceID:   monitorInstanceId,
			InstanceName: monitorInstanceName,
			Region:       monitorInstanceRegion,
			VmClusterConfig: &meta.CPromSpecVmClusterConfig{
				RetentionPeriod: retentionPeriod,
			},
		},
		Status: &meta.CPromItemStatus{
			Phase:   "running",
			Message: "test",
		},
		Metadata: &meta.CPromItemMetadata{
			Name:              "test",
			CreationTimestamp: "2024-02-02T11:39:03+08:00",
		},
		MonitorGrafanaId:   monitorGrafanaId,
		MonitorGrafanaName: monitorGrafanaName,
	}
}

func buildCPromScrapeJobItemResult() *meta.CPromScrapeJobItemResult {
	return &meta.CPromScrapeJobItemResult{
		Items: []meta.CPromScrapeJobItem{
			{
				Spec: &meta.ScrapeJobMetadata{
					AgentID:       agentId,
					ScrapeJobName: EnvoyJobName,
					ScrapeJobID:   envoyJobId,
				},
			},
			{
				Spec: &meta.ScrapeJobMetadata{
					AgentID:       agentId,
					ScrapeJobName: "external-istiod",
					ScrapeJobID:   istioJobId,
				},
			},
		},
	}
}

func buildCPromAgentsResult() *meta.CPromAgentsResult {
	return &meta.CPromAgentsResult{
		Items: []meta.CPromAgent{
			{
				AgentID:     agentId,
				AgentStatus: status,
				Cluster: &meta.CPromBindingCluster{
					Spec: &meta.CPromBindingClusterSpec{
						ClusterID: testClusterUUID,
					},
				},
			},
		},
	}
}

func TestGetMonitorInstanceDetail(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockMonitorModel := mockMonitor.NewMockServiceInterface(ctrl)
	mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
	mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)

	s := &Service{
		InstanceModel: mockInstanceModel,
		MonitorModel:  mockMonitorModel,
		ClusterModel:  mockClusterModel,
	}

	item := buildCPromItem()

	mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(buildMetaInstance(), nil)
	mockInstanceModel.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).Return(buildMetaCluster(), meta.StandaloneMeshType, nil)
	mockMonitorModel.EXPECT().GetCPromInstanceDetail(gomock.Any(), gomock.Any(), gomock.Any()).Return(item, nil)
	mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(gomock.Any(), gomock.Any()).Return(nil, nil)
	mockMonitorModel.EXPECT().GetCpromAgentList(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(buildCPromAgentsResult(), nil).AnyTimes()
	mockMonitorModel.EXPECT().GetAllScrapeJobs(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(buildCPromScrapeJobItemResult(), nil)

	mid, err := s.GetMonitorInstanceDetail(mockCtx, instanceUUID)
	if err != nil {
		t.Error()
	}

	assert.Equal(t, item.Spec.InstanceID, mid.Spec.InstanceID)
}

func TestGetMonitorInstanceDetailByHosting(t *testing.T) {
	testInfos := []struct {
		name    string
		success bool
	}{
		{
			name:    "success",
			success: true,
		},
	}
	for _, tt := range testInfos {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			mockMonitorModel := mockMonitor.NewMockServiceInterface(ctrl)
			mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
			mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)

			s := &Service{
				InstanceModel: mockInstanceModel,
				MonitorModel:  mockMonitorModel,
				ClusterModel:  mockClusterModel,
			}

			item := buildCPromItem()

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(), gomock.Any()).Return(buildHostingInstance(), nil)
			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).Return(
				buildMockCluster(string(meta.HostingMeshType)), meta.HostingMeshType, nil)
			mockMonitorModel.EXPECT().GetCPromInstanceDetail(gomock.Any(), gomock.Any(), gomock.Any()).Return(item, nil).AnyTimes()
			mockMonitorModel.EXPECT().GetCpromAgentList(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(buildCPromAgentsResult(), nil).AnyTimes()
			mockMonitorModel.EXPECT().GetAllScrapeJobs(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(buildCPromScrapeJobItemResult(), nil).AnyTimes()

			mid, err := s.GetMonitorInstanceDetail(mockCtx, instanceUUID)
			if tt.success {
				assert.Nil(t, err)
				assert.Equal(t, item.Spec.InstanceID, mid.Spec.InstanceID)
			}

		})
	}

}

func TestUpdateMonitor(t *testing.T) {
	testInfos := []struct {
		name           string
		oldInstance    *meta.Instances
		updateInstance *vo.MonitorInstances
	}{
		{
			name:        "success",
			oldInstance: buildMetaInstance(),
			updateInstance: &vo.MonitorInstances{
				Enabled: monitorEnabled,
				Instances: []*vo.MonitorInstance{
					{
						Region: region,
						ID:     monitorInstanceId,
						Name:   namespace,
					},
				},
			},
		},
		{
			name:        "open to close",
			oldInstance: buildMetaInstance(),
			updateInstance: &vo.MonitorInstances{
				Enabled: false,
				Instances: []*vo.MonitorInstance{
					{
						Region: region,
						ID:     monitorInstanceId,
						Name:   namespace,
					},
				},
			},
		},
		{
			name: "close to open",
			oldInstance: &meta.Instances{
				InstanceUUID:             testInstanceUUID,
				InstanceName:             testInstanceName,
				InstanceType:             testInstanceType,
				IstioVersion:             testIstioVersion,
				IstioInstallNamespace:    istioInstallNamespace,
				Region:                   region,
				DiscoverySelectorLabels:  discoverySelectorLabels,
				DiscoverySelectorEnabled: csm.Bool(discoverySelectorEnabled),
				MonitorEnabled:           csm.Bool(false),
			},
			updateInstance: &vo.MonitorInstances{
				Enabled: true,
				Instances: []*vo.MonitorInstance{
					{
						Region: region,
						ID:     monitorInstanceId,
						Name:   namespace,
					},
				},
			},
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockMonitorModel := mockMonitor.NewMockServiceInterface(ctrl)
			mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
			mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)

			mockInstanceModel.EXPECT().WithTx(gomock.Any()).AnyTimes().Return(mockInstanceModel)
			mockClusterModel.EXPECT().WithTx(gomock.Any()).AnyTimes().Return(mockClusterModel)

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(),
				gomock.Any()).Return(testInfo.oldInstance, nil).AnyTimes()
			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).Return(buildMetaCluster(),
				meta.StandaloneMeshType, nil).AnyTimes()
			mockMonitorModel.EXPECT().GetCPromAgent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(buildMetaCPromAgent(), nil).AnyTimes()
			mockMonitorModel.EXPECT().DeleteScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil).AnyTimes()
			mockMonitorModel.EXPECT().CreateIstioScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(istioJobId, nil).AnyTimes()
			mockMonitorModel.EXPECT().CreateEnvoyScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(envoyJobId, nil).AnyTimes()
			mockClusterModel.EXPECT().UpdateCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil).AnyTimes()

			mockInstanceModel.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil, nil)

			service := &Service{
				opt:           NewOption(mockDB),
				MonitorModel:  mockMonitorModel,
				InstanceModel: mockInstanceModel,
				ClusterModel:  mockClusterModel,
			}

			mis, err := service.UpdateMonitor(mockCtx, monitorInstanceId, testInfo.updateInstance)
			if err != nil {
				t.Error()
			}
			assert.Equal(t, mis.Instances[0].ID, monitorInstanceId)
		})
	}
}

func TestUpdateMonitorByHosting(t *testing.T) {
	testInfos := []struct {
		name                  string
		oldInstance           *meta.Instances
		istioInstallNamespace string
		istioSvc              *v1.Service
		updateInstance        *vo.MonitorInstances
		success               bool
	}{
		{
			name:                  "isntance is nil",
			oldInstance:           nil,
			istioInstallNamespace: istioInstallNamespace,
			istioSvc: &v1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      map[string]string{"app": "istio"},
					Annotations: map[string]string{constants.CceLoadBalancerID: "lb-123"},
					Name:        constants.IstiodServiceName,
					Namespace:   istioInstallNamespace,
				},
			},
			updateInstance: &vo.MonitorInstances{
				Enabled: false,
				Instances: []*vo.MonitorInstance{
					{
						Region: region,
						ID:     monitorInstanceId,
						Name:   namespace,
					},
				},
			},
			success: false,
		},
		{
			name:                  "success open to close",
			oldInstance:           buildHostingInstance(),
			istioInstallNamespace: istioInstallNamespace,
			istioSvc: &v1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      map[string]string{"app": "istio"},
					Annotations: map[string]string{constants.CceLoadBalancerID: "lb-123"},
					Name:        constants.IstiodServiceName,
					Namespace:   istioInstallNamespace,
				},
			},
			updateInstance: &vo.MonitorInstances{
				Enabled: false,
				Instances: []*vo.MonitorInstance{
					{
						Region: region,
						ID:     monitorInstanceId,
						Name:   namespace,
					},
				},
			},
			success: true,
		},
		{
			name: "success close to open",
			oldInstance: &meta.Instances{
				InstanceUUID:             testInstanceUUID,
				InstanceName:             testInstanceName,
				InstanceType:             string(meta.HostingMeshType),
				IstioVersion:             testIstioVersion,
				IstioInstallNamespace:    istioInstallNamespace,
				Region:                   region,
				DiscoverySelectorLabels:  discoverySelectorLabels,
				DiscoverySelectorEnabled: csm.Bool(false),
				MonitorEnabled:           csm.Bool(false),
			},
			istioInstallNamespace: istioInstallNamespace,
			istioSvc: &v1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      map[string]string{"app": "istio"},
					Annotations: map[string]string{constants.CceLoadBalancerID: "lb-123"},
					Name:        constants.IstiodServiceName,
					Namespace:   istioInstallNamespace,
				},
			},
			updateInstance: &vo.MonitorInstances{
				Enabled: true,
				Instances: []*vo.MonitorInstance{
					{
						Region: region,
						ID:     monitorInstanceId,
						Name:   namespace,
					},
				},
			},
			success: true,
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockMonitorModel := mockMonitor.NewMockServiceInterface(ctrl)
			mockInstanceModel := mockInstance.NewMockServiceInterface(ctrl)
			mockCceService := cceServiceMock.NewMockClientInterface(ctrl)
			mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)

			mockInstanceModel.EXPECT().WithTx(gomock.Any()).AnyTimes().Return(mockInstanceModel)
			mockClusterModel.EXPECT().WithTx(gomock.Any()).AnyTimes().Return(mockClusterModel)

			mockInstanceModel.EXPECT().GetInstanceByInstanceUUID(gomock.Any(),
				gomock.Any()).Return(testInfo.oldInstance, nil).AnyTimes()
			mockInstanceModel.EXPECT().GetInstanceIstiodCluster(gomock.Any(), gomock.Any()).Return(buildMetaCluster(),
				meta.StandaloneMeshType, nil).AnyTimes()

			mockClusterModel.EXPECT().GetIstiodCluster(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(
				buildMockCluster(string(meta.ClusterTypeExternal)), nil)
			mockClusterModel.EXPECT().GetAllRemoteClusterByInstanceUUID(gomock.Any(), gomock.Any()).AnyTimes().Return(
				buildAllRemoteCluster(testClusterUUID), nil)

			mockMonitorModel.EXPECT().GetCpromAgentList(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(
				buildCPromAgentList(), nil)

			fakeClient := kube.NewFakeClient()
			if testInfo.istioSvc != nil && testInfo.oldInstance != nil {
				_, _ = fakeClient.Kube().CoreV1().Services(testInfo.oldInstance.IstioInstallNamespace).Create(
					context.TODO(), testInfo.istioSvc, metav1.CreateOptions{})
			}

			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil).AnyTimes()
			mockMonitorModel.EXPECT().DeleteScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil).AnyTimes()
			mockMonitorModel.EXPECT().CreateHostingIstioScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return("jobID", nil).AnyTimes()
			mockClusterModel.EXPECT().UpdateCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil).AnyTimes()

			mockInstanceModel.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil, nil)

			service := &Service{
				opt:           NewOption(mockDB),
				MonitorModel:  mockMonitorModel,
				cceService:    mockCceService,
				InstanceModel: mockInstanceModel,
				ClusterModel:  mockClusterModel,
			}

			mis, err := service.UpdateMonitor(mockCtx, monitorInstanceId, testInfo.updateInstance)
			if testInfo.success {
				assert.Nil(t, err)
				assert.Equal(t, mis.Instances[0].ID, monitorInstanceId)
			} else {
				assert.Error(t, err)
			}

		})
	}
}

func TestCreateGatewayMonitor(t *testing.T) {
	testInfos := []struct {
		name      string
		jobID     string
		cprom     *meta.CPromInstance
		success   bool
		expectErr string
	}{
		{
			name:    "success",
			jobID:   "test",
			cprom:   buildCPromInstance(),
			success: true,
		},
		{
			name:      "job is empty",
			jobID:     "",
			cprom:     buildCPromInstance(),
			success:   false,
			expectErr: "vpcID is empty",
		},
		{
			name:      "cprom is empty",
			jobID:     "test",
			success:   false,
			expectErr: "InstanceId and Region are required",
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockMonitorModel := mockMonitor.NewMockServiceInterface(ctrl)

			cpis := make([]meta.CPromInstance, 0)
			cpi := meta.CPromInstance{
				InstanceName: monitorInstanceName,
				InstanceId:   monitorInstanceId,
				Region:       monitorInstanceRegion,
			}
			cpis = append(cpis, cpi)

			mockMonitorModel.EXPECT().GetCPromAgentByVpcID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(buildMetaCPromAgent(), nil)
			mockMonitorModel.EXPECT().CreateGatewayScrapeJob(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return("", nil)

			s := &Service{
				MonitorModel: mockMonitorModel,
			}

			viper.Set("cloud.regions", []string{"gz"})
			_, err := s.CreateGatewayMonitor(mockCtx, "ids", testInfo.jobID, testInfo.cprom)
			if testInfo.success {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr)
			}
		})
	}
}

func buildCPromInstance() *meta.CPromInstance {
	return &meta.CPromInstance{
		InstanceId: "instance-id",
		Region:     "region",
	}
}

func buildMetaInstance() *meta.Instances {
	instanceInfo := &meta.Instances{
		InstanceUUID:             testInstanceUUID,
		InstanceName:             testInstanceName,
		InstanceType:             testInstanceType,
		IstioVersion:             testIstioVersion,
		IstioInstallNamespace:    istioInstallNamespace,
		Region:                   region,
		DiscoverySelectorLabels:  discoverySelectorLabels,
		DiscoverySelectorEnabled: csm.Bool(discoverySelectorEnabled),
		MonitorEnabled:           csm.Bool(monitorEnabled),
	}
	return instanceInfo
}

func buildHostingInstance() *meta.Instances {
	instanceInfo := buildMetaInstance()
	instanceInfo.InstanceType = string(meta.HostingMeshType)
	return instanceInfo
}

func buildAllRemoteCluster(clusterID string) *[]meta.Cluster {
	result := make([]meta.Cluster, 0)
	temp := buildMetaCluster()
	temp.ClusterUUID = clusterID
	result = append(result, *temp)
	return &result
}

func buildMockCluster(clusterType string) *meta.Cluster {
	clusterInfo := buildMetaCluster()
	clusterInfo.ClusterType = clusterType
	clusterInfo.MonitorAgentID = agentId
	clusterInfo.MonitorJobIds = istioJobId
	return clusterInfo
}

func buildMetaCluster() *meta.Cluster {
	cluster := &meta.Cluster{
		InstanceUUID:          testInstanceUUID,
		ClusterUUID:           testClusterUUID,
		ClusterName:           testClusterName,
		Region:                testRegion,
		IstioInstallNamespace: testIstioInstallNamespace,
		MonitorInstanceId:     monitorInstanceId,
		MonitorRegion:         monitorInstanceRegion,
		MonitorJobIds:         strings.Join([]string{istioJobId, envoyJobId}, ","),
	}
	return cluster
}

func buildCPromAgentList() *meta.CPromAgentsResult {
	agentList := &meta.CPromAgentsResult{
		Items: []meta.CPromAgent{*buildMetaCPromAgent()},
	}
	return agentList
}

func buildMetaCPromAgent() *meta.CPromAgent {
	agent := &meta.CPromAgent{
		AgentID: agentId,
		Cluster: &meta.CPromBindingCluster{
			Spec: &meta.CPromBindingClusterSpec{
				ClusterID: clusterUUID,
			},
		},
	}
	return agent
}
