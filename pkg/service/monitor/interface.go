package monitor

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

type ServiceInterface interface {
	// 获取监控实例列表
	GetMonitorInstances(ctx csmContext.CsmContext, clusterID, instanceId string) (*vo.MonitorInstances, error)

	UpdateMonitor(ctx csmContext.CsmContext, instanceId string, update *vo.MonitorInstances) (*vo.MonitorInstances, error)

	UpdateRemoteMonitor(ctx csmContext.CsmContext, instanceId, clusterID, jobId string, update *vo.MonitorInstances) error

	// 网格实例对应的监控实例详细信息
	GetMonitorInstanceDetail(ctx csmContext.CsmContext, instanceId string) (*vo.MonitorInstanceDetail, error)

	// for gateway
	CreateGatewayMonitor(ctx csmContext.CsmContext, csmInstanceID, vpcID string, create *meta.CPromInstance) (*meta.CPromGatewayInfo, error)

	ClusterCPromAgentCheck(ctx csmContext.CsmContext, region, cpromInstanceID, clusterID string) *vo.CPromAgentCheckResult
}
