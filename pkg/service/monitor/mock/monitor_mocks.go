// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	vo "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/vo"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// ClusterCPromAgentCheck mocks base method.
func (m *MockServiceInterface) ClusterCPromAgentCheck(ctx context.CsmContext, region, cpromInstanceID, clusterID string) *vo.CPromAgentCheckResult {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterCPromAgentCheck", ctx, region, cpromInstanceID, clusterID)
	ret0, _ := ret[0].(*vo.CPromAgentCheckResult)
	return ret0
}

// ClusterCPromAgentCheck indicates an expected call of ClusterCPromAgentCheck.
func (mr *MockServiceInterfaceMockRecorder) ClusterCPromAgentCheck(ctx, region, cpromInstanceID, clusterID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterCPromAgentCheck", reflect.TypeOf((*MockServiceInterface)(nil).ClusterCPromAgentCheck), ctx, region, cpromInstanceID, clusterID)
}

// CreateGatewayMonitor mocks base method.
func (m *MockServiceInterface) CreateGatewayMonitor(ctx context.CsmContext, csmInstanceID, vpcID string, create *meta.CPromInstance) (*meta.CPromGatewayInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGatewayMonitor", ctx, csmInstanceID, vpcID, create)
	ret0, _ := ret[0].(*meta.CPromGatewayInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGatewayMonitor indicates an expected call of CreateGatewayMonitor.
func (mr *MockServiceInterfaceMockRecorder) CreateGatewayMonitor(ctx, csmInstanceID, vpcID, create interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGatewayMonitor", reflect.TypeOf((*MockServiceInterface)(nil).CreateGatewayMonitor), ctx, csmInstanceID, vpcID, create)
}

// GetMonitorInstanceDetail mocks base method.
func (m *MockServiceInterface) GetMonitorInstanceDetail(ctx context.CsmContext, instanceId string) (*vo.MonitorInstanceDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMonitorInstanceDetail", ctx, instanceId)
	ret0, _ := ret[0].(*vo.MonitorInstanceDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMonitorInstanceDetail indicates an expected call of GetMonitorInstanceDetail.
func (mr *MockServiceInterfaceMockRecorder) GetMonitorInstanceDetail(ctx, instanceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMonitorInstanceDetail", reflect.TypeOf((*MockServiceInterface)(nil).GetMonitorInstanceDetail), ctx, instanceId)
}

// GetMonitorInstances mocks base method.
func (m *MockServiceInterface) GetMonitorInstances(ctx context.CsmContext, clusterID, instanceId string) (*vo.MonitorInstances, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMonitorInstances", ctx, clusterID, instanceId)
	ret0, _ := ret[0].(*vo.MonitorInstances)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMonitorInstances indicates an expected call of GetMonitorInstances.
func (mr *MockServiceInterfaceMockRecorder) GetMonitorInstances(ctx, clusterID, instanceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMonitorInstances", reflect.TypeOf((*MockServiceInterface)(nil).GetMonitorInstances), ctx, clusterID, instanceId)
}

// UpdateMonitor mocks base method.
func (m *MockServiceInterface) UpdateMonitor(ctx context.CsmContext, instanceId string, update *vo.MonitorInstances) (*vo.MonitorInstances, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMonitor", ctx, instanceId, update)
	ret0, _ := ret[0].(*vo.MonitorInstances)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMonitor indicates an expected call of UpdateMonitor.
func (mr *MockServiceInterfaceMockRecorder) UpdateMonitor(ctx, instanceId, update interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMonitor", reflect.TypeOf((*MockServiceInterface)(nil).UpdateMonitor), ctx, instanceId, update)
}

// UpdateRemoteMonitor mocks base method.
func (m *MockServiceInterface) UpdateRemoteMonitor(ctx context.CsmContext, instanceId, clusterID, jobId string, update *vo.MonitorInstances) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRemoteMonitor", ctx, instanceId, clusterID, jobId, update)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRemoteMonitor indicates an expected call of UpdateRemoteMonitor.
func (mr *MockServiceInterfaceMockRecorder) UpdateRemoteMonitor(ctx, instanceId, clusterID, jobId, update interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRemoteMonitor", reflect.TypeOf((*MockServiceInterface)(nil).UpdateRemoteMonitor), ctx, instanceId, clusterID, jobId, update)
}
