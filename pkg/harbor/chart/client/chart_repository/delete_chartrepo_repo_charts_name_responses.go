// Code generated by go-swagger; DO NOT EDIT.

package chart_repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// DeleteChartrepoRepoChartsNameReader is a Reader for the DeleteChartrepoRepoChartsName structure.
type DeleteChartrepoRepoChartsNameReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *DeleteChartrepoRepoChartsNameReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewDeleteChartrepoRepoChartsNameOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 401:
		result := NewDeleteChartrepoRepoChartsNameUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 403:
		result := NewDeleteChartrepoRepoChartsNameForbidden()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewDeleteChartrepoRepoChartsNameInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewDeleteChartrepoRepoChartsNameOK creates a DeleteChartrepoRepoChartsNameOK with default headers values
func NewDeleteChartrepoRepoChartsNameOK() *DeleteChartrepoRepoChartsNameOK {
	return &DeleteChartrepoRepoChartsNameOK{}
}

/* DeleteChartrepoRepoChartsNameOK describes a response with status code 200, with default header values.

The specified chart entry is successfully deleted.
*/
type DeleteChartrepoRepoChartsNameOK struct {
}

func (o *DeleteChartrepoRepoChartsNameOK) Error() string {
	return fmt.Sprintf("[DELETE /chartrepo/{repo}/charts/{name}][%d] deleteChartrepoRepoChartsNameOK ", 200)
}

func (o *DeleteChartrepoRepoChartsNameOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewDeleteChartrepoRepoChartsNameUnauthorized creates a DeleteChartrepoRepoChartsNameUnauthorized with default headers values
func NewDeleteChartrepoRepoChartsNameUnauthorized() *DeleteChartrepoRepoChartsNameUnauthorized {
	return &DeleteChartrepoRepoChartsNameUnauthorized{}
}

/* DeleteChartrepoRepoChartsNameUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type DeleteChartrepoRepoChartsNameUnauthorized struct {
}

func (o *DeleteChartrepoRepoChartsNameUnauthorized) Error() string {
	return fmt.Sprintf("[DELETE /chartrepo/{repo}/charts/{name}][%d] deleteChartrepoRepoChartsNameUnauthorized ", 401)
}

func (o *DeleteChartrepoRepoChartsNameUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewDeleteChartrepoRepoChartsNameForbidden creates a DeleteChartrepoRepoChartsNameForbidden with default headers values
func NewDeleteChartrepoRepoChartsNameForbidden() *DeleteChartrepoRepoChartsNameForbidden {
	return &DeleteChartrepoRepoChartsNameForbidden{}
}

/* DeleteChartrepoRepoChartsNameForbidden describes a response with status code 403, with default header values.

Operation is forbidden or quota exceeded
*/
type DeleteChartrepoRepoChartsNameForbidden struct {
}

func (o *DeleteChartrepoRepoChartsNameForbidden) Error() string {
	return fmt.Sprintf("[DELETE /chartrepo/{repo}/charts/{name}][%d] deleteChartrepoRepoChartsNameForbidden ", 403)
}

func (o *DeleteChartrepoRepoChartsNameForbidden) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewDeleteChartrepoRepoChartsNameInternalServerError creates a DeleteChartrepoRepoChartsNameInternalServerError with default headers values
func NewDeleteChartrepoRepoChartsNameInternalServerError() *DeleteChartrepoRepoChartsNameInternalServerError {
	return &DeleteChartrepoRepoChartsNameInternalServerError{}
}

/* DeleteChartrepoRepoChartsNameInternalServerError describes a response with status code 500, with default header values.

Internal server error occurred
*/
type DeleteChartrepoRepoChartsNameInternalServerError struct {
}

func (o *DeleteChartrepoRepoChartsNameInternalServerError) Error() string {
	return fmt.Sprintf("[DELETE /chartrepo/{repo}/charts/{name}][%d] deleteChartrepoRepoChartsNameInternalServerError ", 500)
}

func (o *DeleteChartrepoRepoChartsNameInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}
