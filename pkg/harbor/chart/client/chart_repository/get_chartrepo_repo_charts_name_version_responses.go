// Code generated by go-swagger; DO NOT EDIT.

package chart_repository

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// GetChartrepoRepoChartsNameVersionReader is a Reader for the GetChartrepoRepoChartsNameVersion structure.
type GetChartrepoRepoChartsNameVersionReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *GetChartrepoRepoChartsNameVersionReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewGetChartrepoRepoChartsNameVersionOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 401:
		result := NewGetChartrepoRepoChartsNameVersionUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 403:
		result := NewGetChartrepoRepoChartsNameVersionForbidden()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewGetChartrepoRepoChartsNameVersionNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewGetChartrepoRepoChartsNameVersionInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewGetChartrepoRepoChartsNameVersionOK creates a GetChartrepoRepoChartsNameVersionOK with default headers values
func NewGetChartrepoRepoChartsNameVersionOK() *GetChartrepoRepoChartsNameVersionOK {
	return &GetChartrepoRepoChartsNameVersionOK{}
}

/* GetChartrepoRepoChartsNameVersionOK describes a response with status code 200, with default header values.

Successfully retrieved the chart version
*/
type GetChartrepoRepoChartsNameVersionOK struct {
	Payload *model.ChartVersionDetails
}

func (o *GetChartrepoRepoChartsNameVersionOK) Error() string {
	return fmt.Sprintf("[GET /chartrepo/{repo}/charts/{name}/{version}][%d] getChartrepoRepoChartsNameVersionOK  %+v", 200, o.Payload)
}
func (o *GetChartrepoRepoChartsNameVersionOK) GetPayload() *model.ChartVersionDetails {
	return o.Payload
}

func (o *GetChartrepoRepoChartsNameVersionOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(model.ChartVersionDetails)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetChartrepoRepoChartsNameVersionUnauthorized creates a GetChartrepoRepoChartsNameVersionUnauthorized with default headers values
func NewGetChartrepoRepoChartsNameVersionUnauthorized() *GetChartrepoRepoChartsNameVersionUnauthorized {
	return &GetChartrepoRepoChartsNameVersionUnauthorized{}
}

/* GetChartrepoRepoChartsNameVersionUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type GetChartrepoRepoChartsNameVersionUnauthorized struct {
}

func (o *GetChartrepoRepoChartsNameVersionUnauthorized) Error() string {
	return fmt.Sprintf("[GET /chartrepo/{repo}/charts/{name}/{version}][%d] getChartrepoRepoChartsNameVersionUnauthorized ", 401)
}

func (o *GetChartrepoRepoChartsNameVersionUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewGetChartrepoRepoChartsNameVersionForbidden creates a GetChartrepoRepoChartsNameVersionForbidden with default headers values
func NewGetChartrepoRepoChartsNameVersionForbidden() *GetChartrepoRepoChartsNameVersionForbidden {
	return &GetChartrepoRepoChartsNameVersionForbidden{}
}

/* GetChartrepoRepoChartsNameVersionForbidden describes a response with status code 403, with default header values.

Operation is forbidden or quota exceeded
*/
type GetChartrepoRepoChartsNameVersionForbidden struct {
}

func (o *GetChartrepoRepoChartsNameVersionForbidden) Error() string {
	return fmt.Sprintf("[GET /chartrepo/{repo}/charts/{name}/{version}][%d] getChartrepoRepoChartsNameVersionForbidden ", 403)
}

func (o *GetChartrepoRepoChartsNameVersionForbidden) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewGetChartrepoRepoChartsNameVersionNotFound creates a GetChartrepoRepoChartsNameVersionNotFound with default headers values
func NewGetChartrepoRepoChartsNameVersionNotFound() *GetChartrepoRepoChartsNameVersionNotFound {
	return &GetChartrepoRepoChartsNameVersionNotFound{}
}

/* GetChartrepoRepoChartsNameVersionNotFound describes a response with status code 404, with default header values.

Not found
*/
type GetChartrepoRepoChartsNameVersionNotFound struct {
}

func (o *GetChartrepoRepoChartsNameVersionNotFound) Error() string {
	return fmt.Sprintf("[GET /chartrepo/{repo}/charts/{name}/{version}][%d] getChartrepoRepoChartsNameVersionNotFound ", 404)
}

func (o *GetChartrepoRepoChartsNameVersionNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewGetChartrepoRepoChartsNameVersionInternalServerError creates a GetChartrepoRepoChartsNameVersionInternalServerError with default headers values
func NewGetChartrepoRepoChartsNameVersionInternalServerError() *GetChartrepoRepoChartsNameVersionInternalServerError {
	return &GetChartrepoRepoChartsNameVersionInternalServerError{}
}

/* GetChartrepoRepoChartsNameVersionInternalServerError describes a response with status code 500, with default header values.

Internal server error occurred
*/
type GetChartrepoRepoChartsNameVersionInternalServerError struct {
}

func (o *GetChartrepoRepoChartsNameVersionInternalServerError) Error() string {
	return fmt.Sprintf("[GET /chartrepo/{repo}/charts/{name}/{version}][%d] getChartrepoRepoChartsNameVersionInternalServerError ", 500)
}

func (o *GetChartrepoRepoChartsNameVersionInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}
