package response_modifier

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/goharbor/harbor/src/server/v2.0/models"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

func TestMiddleware(t *testing.T) {
	assert := assert.New(t)

	next := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		sysInfo := models.GeneralInfo{
			WithChartmuseum: utils.BoolPtr(false),
		}

		info, err := json.Marshal(sysInfo)
		assert.NoError(err)

		w.WriteHeader(http.StatusOK)
		w.Write(info)
	})

	req1 := httptest.NewRequest(http.MethodDelete, "/resource", nil)
	rec1 := httptest.NewRecorder()
	Middleware()(next).ServeHTTP(rec1, req1)
	assert.Equal(http.StatusOK, rec1.Code)
	assert.Equal(`{"with_chartmuseum":false}`, rec1.Body.String())

	req2 := httptest.NewRequest(http.MethodGet, "/api/v2.0/systeminfo", nil)
	rec2 := httptest.NewRecorder()
	Middleware()(next).ServeHTTP(rec2, req2)
	assert.Equal(http.StatusOK, rec2.Code)
	assert.Equal(`{"with_chartmuseum":true}`, rec2.Body.String())
}
