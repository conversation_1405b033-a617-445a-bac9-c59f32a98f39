package topic

import (
	"github.com/goharbor/harbor/src/lib/log"
	"github.com/goharbor/harbor/src/pkg/notifier"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/handler"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/hook"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/model"
)

// Subscribe topics
func init() {

	handlersMap := map[string][]notifier.NotificationHandler{
		model.TriggerTopic: {&handler.TriggerHandler{DefaultManager: hook.NewHookManager()}},
	}

	for t, handlers := range handlersMap {
		for _, handler := range handlers {
			if err := notifier.Subscribe(t, handler); err != nil {
				log.Errorf("failed to subscribe topic %s: %v", t, err)
				continue
			}
			log.Infof("topic %s is subscribed", t)
		}
	}
}
