package policy

import (
	"context"

	"github.com/goharbor/harbor/src/lib/q"
	"github.com/stretchr/testify/mock"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/dao/policy"
)

// mockExecutionDAO is an autogenerated mock type for the ExecutionDAO type
type mockPolicyDAO struct {
	mock.Mock
}

func (_m *mockPolicyDAO) Count(ctx context.Context, query *q.Query) (count int64, err error) {
	ret := _m.Called(ctx, query)

	var r0 int64
	if rf, ok := ret.Get(0).(func(context.Context, *q.Query) int64); ok {
		r0 = rf(ctx, query)
	} else {
		r0 = ret.Get(0).(int64)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, *q.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

func (_m *mockPolicyDAO) Get(ctx context.Context, id int64) (policy *policy.TriggerPolicy, err error) {
	panic("implement me")
}

func (_m *mockPolicyDAO) GetTriggerPolicyByName(ctx context.Context, name string) (*policy.TriggerPolicy, error) {
	panic("implement me")
}

func (_m *mockPolicyDAO) Create(ctx context.Context, policy *policy.TriggerPolicy) (id int64, err error) {
	panic("implement me")
}

func (_m *mockPolicyDAO) Update(ctx context.Context, policy *policy.TriggerPolicy, props ...string) (err error) {
	panic("implement me")
}

func (_m *mockPolicyDAO) Delete(ctx context.Context, id int64) (err error) {
	panic("implement me")
}

func (_m *mockPolicyDAO) BatchDelete(ctx context.Context, ids []int64) (err error) {
	panic("implement me")
}

// List provides a mock function with given fields: ctx, query
func (_m *mockPolicyDAO) List(ctx context.Context, query *q.Query) ([]*policy.TriggerPolicy, error) {
	ret := _m.Called(ctx, query)

	var r0 []*policy.TriggerPolicy
	if rf, ok := ret.Get(0).(func(context.Context, *q.Query) []*policy.TriggerPolicy); ok {
		r0 = rf(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*policy.TriggerPolicy)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, *q.Query) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}
