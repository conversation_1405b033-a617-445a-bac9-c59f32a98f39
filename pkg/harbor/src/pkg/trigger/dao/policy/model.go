package policy

import (
	"encoding/json"
	"time"

	"github.com/goharbor/harbor/src/lib/orm"
)

func init() {
	orm.RegisterModel(new(TriggerPolicy))
}

// FilterType represents the type info of the filter.
type FilterType string

const (
	FilterTypeProject    FilterType = "project_name"
	FilterTypeRepository FilterType = "repository_name"
	FilterTypeTag        FilterType = "tag_name"
)

const (
	// TriggerPolicyTable is table name for trigger policies
	TriggerPolicyTable = "trigger_policy"
)

// TriggerPolicy is the model for a trigger policy.
type TriggerPolicy struct {
	ID               int64            `orm:"pk;auto;column(id)" json:"id"`
	Name             string           `orm:"column(name)" json:"name"`
	Description      string           `orm:"column(description)" json:"description"`
	TargetsDB        string           `orm:"column(targets)" json:"-"`
	Targets          []TriggerTarget  `orm:"-" json:"targets"`
	EventTypesDB     string           `orm:"column(event_types)" json:"-"`
	EventTypes       []string         `orm:"-" json:"event_types"`
	Filters          []*TriggerFilter `orm:"-" json:"filters"`
	FiltersDB        string           `orm:"column(filters)" json:"-"`
	Creator          string           `orm:"column(creator)" json:"creator"`
	CreationTime     time.Time        `orm:"column(creation_time);auto_now_add" json:"creation_time"`
	UpdateTime       time.Time        `orm:"column(update_time);auto_now_add" json:"update_time"`
	Enabled          bool             `orm:"column(enabled)" json:"enabled"`
	JobStatusHookURL string           `orm:"column(job_status_hook_url)" json:"-"`
}

// TableName set table name for ORM.
func (w *TriggerPolicy) TableName() string {
	return TriggerPolicyTable
}

// ConvertToDBModel convert struct data in notification policy to DB model data
func (w *TriggerPolicy) ConvertToDBModel() error {
	if len(w.Targets) != 0 {
		targets, err := json.Marshal(w.Targets)
		if err != nil {
			return err
		}
		w.TargetsDB = string(targets)
	}
	if len(w.EventTypes) != 0 {
		eventTypes, err := json.Marshal(w.EventTypes)
		if err != nil {
			return err
		}
		w.EventTypesDB = string(eventTypes)
	}

	if len(w.Filters) != 0 {
		filters, err := json.Marshal(w.Filters)
		if err != nil {
			return err
		}
		w.FiltersDB = string(filters)
	}

	return nil
}

// ConvertFromDBModel convert from DB model data to struct data
func (w *TriggerPolicy) ConvertFromDBModel() error {
	targets := []TriggerTarget{}
	if len(w.TargetsDB) != 0 {
		err := json.Unmarshal([]byte(w.TargetsDB), &targets)
		if err != nil {
			return err
		}
	}
	w.Targets = targets

	types := []string{}
	if len(w.EventTypesDB) != 0 {
		err := json.Unmarshal([]byte(w.EventTypesDB), &types)
		if err != nil {
			return err
		}
	}
	w.EventTypes = types

	filters := make([]*TriggerFilter, 0)
	if len(w.FiltersDB) != 0 {
		err := json.Unmarshal([]byte(w.FiltersDB), &filters)
		if err != nil {
			return err
		}
	}
	w.Filters = filters

	return nil
}

type TriggerFilter struct {
	// The trigger policy filter type.
	Type FilterType `json:"type"`

	// The value of trigger policy filter.
	Value interface{} `json:"value"`
}

// TriggerTarget defines the structure of target a trigger send to
type TriggerTarget struct {
	Type           string            `json:"type"`
	Address        string            `json:"address"`
	Headers        map[string]string `json:"headers,omitempty"`
	SkipCertVerify bool              `json:"skip_cert_verify"`
}
