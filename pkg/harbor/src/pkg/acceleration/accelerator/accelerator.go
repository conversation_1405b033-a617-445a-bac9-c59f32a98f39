package accelerator

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/goharbor/harbor/src/lib"
	"github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/lib/log"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/dao/registration"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/rest"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/rest/auth"
)

const (
	statusUnhealthy = "unhealthy"
	statusHealthy   = "healthy"

	Basic = "Basic"
)

// DefaultController is a singleton api controller for plug accelerators
var DefaultController = New()

// New a basic controller
func New() Controller {
	return &basicController{
		dao:        registration.NewDAO(),
		clientPool: rest.DefaultClientPool,
	}
}

// Controller provides the related operations of accelerator for the upper API.
// All the capabilities of the accelerator are defined here.
type Controller interface {
	GetDefaultAccelerator(ctx context.Context) (*Accelerator, error)
	SetDefaultAccelerator(ctx context.Context, accelerator *Accelerator) error
}

// basicController is default implementation of api.Controller interface
type basicController struct {
	sync.Once
	// Managers for managing the accelerator registrations
	dao registration.DAO
	// Client pool for talking to adapters
	clientPool rest.ClientPool
}

func (b *basicController) GetDefaultAccelerator(ctx context.Context) (*Accelerator, error) {

	reg, err := b.dao.GetDefaultAr(ctx)
	if err != nil {
		return nil, err
	}
	ar, err := b.generate(reg)
	if err != nil {
		return nil, err
	}
	err = b.Ping(ctx, ar)
	if err != nil {
		log.Error(errors.Wrap(err, "api controller: get accelerator"))
		ar.Health = statusUnhealthy
	} else {
		ar.Health = statusHealthy
	}
	return ar, nil
}

func (b *basicController) generate(ar *registration.AccelerationRegistration) (*Accelerator, error) {
	r := &Accelerator{
		ID:               ar.ID,
		UUID:             ar.UUID,
		Name:             ar.Name,
		Description:      ar.Description,
		URL:              ar.URL,
		Disabled:         ar.Disabled,
		Auth:             ar.Auth,
		AccessCredential: ar.AccessCredential,

		SkipCertVerify:  ar.SkipCertVerify,
		UseInternalAddr: ar.UseInternalAddr,
	}
	r.Capabilities = []*Capability{{
		ConsumesMimeTypes: []string{
			rest.MimeTypeOCIArtifact,
			rest.MimeTypeDockerArtifact,
		},
	}}

	return r, nil
}

func (b *basicController) SetDefaultAccelerator(ctx context.Context, ar *Accelerator) error {

	if err := ar.Validate(true); err != nil {
		return errors.Wrap(err, "create registration")
	}

	reg, err := toDaoModel(ar)
	if err != nil {
		return err
	}

	_, err = b.dao.Create(ctx, reg)
	if err != nil {
		return errors.Wrap(err, "create accelerator registration")
	}

	err = b.dao.SetDefaultAr(ctx, ar.UUID)
	if err != nil {
		return errors.Wrap(err, "set default accelerator registration")
	}
	return nil
}

// Ping ...
func (b *basicController) Ping(ctx context.Context, ar *Accelerator) error {
	if ar == nil {
		return errors.New("nil ar to ping")
	}

	client, err := ar.Client(b.clientPool)
	if err != nil {
		return err
	}
	status, err := client.CheckHealth()
	if err != nil {
		return err
	}

	if status != "ok" {
		return fmt.Errorf("accelerator status is not ok")
	}

	return nil
}

type Accelerator struct {
	ID          int64  `json:"id"`
	UUID        string `json:"uuid"`
	Name        string `json:"name"`
	Description string `json:"description"`

	URL      string `json:"url"`
	Disabled bool   `json:"disabled"`
	Health   string `json:"health,omitempty"`

	// Authentication settings
	// "","Basic" can be supported
	Auth             string `json:"auth"`
	AccessCredential string `json:"access_credential,omitempty"`

	// Http connection settings
	SkipCertVerify bool `json:"skip_certVerify"`

	UseInternalAddr bool `json:"use_internal_addr"`

	Capabilities []*Capability `json:"capabilities"`
}

// Capability consists of the set of recognized artifact MIME types and the set of accelerator
// report MIME types. For example, a accelerator capable of analyzing Docker images and producing
// a vulnerabilities report recognizable by Harbor web console might be represented with the
// following capability:
// - consumes MIME types:
//   -- application/vnd.oci.image.manifest.v1+json
//   -- application/vnd.docker.distribution.manifest.v2+json
type Capability struct {
	ConsumesMimeTypes []string `json:"consumes_mime_types"`
}

// HasCapability returns true when mime type of the artifact support by the accelerator
func (r *Accelerator) HasCapability(manifestMimeType string) bool {
	if len(r.Capabilities) == 0 {
		return false
	}

	for _, capability := range r.Capabilities {
		for _, mt := range capability.ConsumesMimeTypes {
			if mt == manifestMimeType {
				return true
			}
		}
	}

	return false
}

// GetAuthType returns the registry authorization type of the accelerator
func (r *Accelerator) GetAuthType() string {
	return "Basic"
}

// FromJSON parses registration from json data
func (r *Accelerator) FromJSON(jsonData string) error {
	if len(jsonData) == 0 {
		return errors.New("empty json data to parse")
	}

	return json.Unmarshal([]byte(jsonData), r)
}

// ToJSON marshals registration to JSON data
func (r *Accelerator) ToJSON() (string, error) {
	data, err := json.Marshal(r)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

// Validate registration
func (r *Accelerator) Validate(checkUUID bool) error {
	if checkUUID && len(r.UUID) == 0 {
		return errors.New("malformed endpoint")
	}

	if len(r.Name) == 0 {
		return errors.New("missing registration name")
	}

	url, err := lib.ValidateHTTPURL(r.URL)
	if err != nil {
		return errors.Wrap(err, "accelerator registration validate")
	}
	r.URL = url

	if len(r.Auth) > 0 && r.Auth != auth.Basic {
		return errors.Errorf("auth type %s is not supported", r.Auth)
	}

	if len(r.Auth) > 0 && len(r.AccessCredential) == 0 {
		return errors.Errorf("access_credential is required for auth type %s", r.Auth)
	}

	return nil
}

// Client returns client of registration
func (r *Accelerator) Client(pool rest.ClientPool) (rest.Client, error) {
	if err := r.Validate(true); err != nil {
		return nil, err
	}

	return pool.Get(r.URL, r.Auth, r.AccessCredential, r.SkipCertVerify)
}

// toDaoModel ...
func toDaoModel(ar *Accelerator) (*registration.AccelerationRegistration, error) {
	m := &registration.AccelerationRegistration{
		ID:               ar.ID,
		UUID:             ar.UUID,
		Name:             ar.Name,
		Description:      ar.Description,
		URL:              ar.URL,
		Disabled:         ar.Disabled,
		Auth:             ar.Auth,
		AccessCredential: ar.AccessCredential,
		SkipCertVerify:   ar.SkipCertVerify,

		UseInternalAddr: ar.UseInternalAddr,
	}

	return m, nil
}
