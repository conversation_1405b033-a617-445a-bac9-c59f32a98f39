package policy

import (
	"context"
	"fmt"
	"time"

	"github.com/goharbor/harbor/src/lib/q"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/dao/policy"
)

// Manager manages the trigger policies
type Manager interface {
	// Create new policy
	Create(context.Context, *policy.AcceleratorPolicy) (int64, error)
	// List the policies, returns the policy list and error
	List(context.Context, *q.Query) (int64, []*policy.AcceleratorPolicy, error)
	// Get policy with specified ID
	Get(context.Context, int64) (*policy.AcceleratorPolicy, error)
	// GetByName get policy by the name
	GetByName(context.Context, string) (*policy.AcceleratorPolicy, error)
	// Update the specified policy
	Update(context.Context, *policy.AcceleratorPolicy) error
	// Delete the specified policy
	Delete(context.Context, int64) error
	// BatchDelete the specified policy
	BatchDelete(context.Context, []int64) error
	// GetRelatedPolices get related policies
	GetRelatedPolices(context.Context) ([]*policy.AcceleratorPolicy, error)
}

// policyManager ...
type policyManager struct {
	policyDAO policy.DAO
}

// NewPolicyManager ...
func NewPolicyManager() Manager {
	return &policyManager{
		policyDAO: policy.NewPolicyDAO(),
	}
}

func (m *policyManager) Create(ctx context.Context, policy *policy.AcceleratorPolicy) (int64, error) {
	t := time.Now()
	policy.CreationTime = t
	policy.UpdateTime = t

	err := policy.ConvertToDBModel()
	if err != nil {
		return 0, err
	}
	return m.policyDAO.Create(ctx, policy)
}

func (m *policyManager) List(ctx context.Context, query *q.Query) (int64, []*policy.AcceleratorPolicy, error) {

	total, err := m.policyDAO.Count(ctx, query)
	if err != nil {
		return 0, nil, err
	}

	var policies []*policy.AcceleratorPolicy
	persisPolicies, err := m.policyDAO.List(ctx, query)
	if err != nil {
		return 0, nil, err
	}

	for _, policy := range persisPolicies {
		err := policy.ConvertFromDBModel()
		if err != nil {
			return 0, nil, err
		}
		policies = append(policies, policy)
	}

	return total, policies, nil
}

// Get trigger policy with specified ID
func (m *policyManager) Get(ctx context.Context, id int64) (*policy.AcceleratorPolicy, error) {
	policy, err := m.policyDAO.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	err = policy.ConvertFromDBModel()
	return policy, err
}

// GetByName trigger policy by the name
func (m *policyManager) GetByName(ctx context.Context, name string) (*policy.AcceleratorPolicy, error) {
	policy, err := m.policyDAO.GetAcceleratorPolicyByName(ctx, name)
	if err != nil {
		return nil, err
	}
	err = policy.ConvertFromDBModel()
	return policy, err
}

// Update the specified trigger policy
func (m *policyManager) Update(ctx context.Context, policy *policy.AcceleratorPolicy) error {
	policy.UpdateTime = time.Now()
	err := policy.ConvertToDBModel()
	if err != nil {
		return err
	}

	return m.policyDAO.Update(ctx, policy)
}

// Delete the specified trigger policy
func (m *policyManager) Delete(ctx context.Context, policyID int64) error {
	return m.policyDAO.Delete(ctx, policyID)
}

// BatchDelete the specified trigger policy
func (m *policyManager) BatchDelete(ctx context.Context, policyIDs []int64) error {
	return m.policyDAO.BatchDelete(ctx, policyIDs)
}

// GetRelatedPolices get policies including event type
func (m *policyManager) GetRelatedPolices(ctx context.Context) ([]*policy.AcceleratorPolicy, error) {
	_, policies, err := m.List(ctx, &q.Query{})
	if err != nil {
		return nil, fmt.Errorf("failed to get trigger policies: %v", err)
	}

	var result []*policy.AcceleratorPolicy

	for _, ply := range policies {
		if !ply.Enabled {
			continue
		}
		result = append(result, ply)
	}
	return result, nil
}
