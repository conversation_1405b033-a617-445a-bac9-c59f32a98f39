package tag_execution

import (
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/dao/tag_execution"
)

type tagExecutionManagerTestSuite struct {
	suite.Suite
	tagExecutionDAO *mockTagExecutionDAO
	executionDAO    *mockExecutionDAO
	tagExecutionMgr *tagExecutionManager
}

func (e *tagExecutionManagerTestSuite) SetupTest() {

	e.tagExecutionDAO = &mockTagExecutionDAO{}
	e.executionDAO = &mockExecutionDAO{}
	e.tagExecutionMgr = &tagExecutionManager{
		teDAO:        e.tagExecutionDAO,
		executionDAO: e.executionDAO,
	}
}

func (e *tagExecutionManagerTestSuite) TestCreate() {
	SetExecutionSweeperCount("vendor", 50)
	e.tagExecutionDAO.On("Create", mock.Anything, mock.Anything).Return(int64(1), nil)

	e.executionDAO.On("List", mock.Anything, mock.Anything).Return(nil, nil)
	e.tagExecutionDAO.On("List", mock.Anything, mock.Anything).Return(nil, nil)

	id, err := e.tagExecutionMgr.Create(nil, "vendor", 1, &tag_execution.TagExecution{})

	e.Require().Nil(err)
	e.Equal(int64(1), id)
}

func (e *tagExecutionManagerTestSuite) TestList() {

	e.tagExecutionDAO.On("Count", mock.Anything, mock.Anything).Return(int64(1), nil)
	e.tagExecutionDAO.On("List", mock.Anything, mock.Anything).Return([]*tag_execution.TagExecution{
		{
			ID:          1,
			TagID:       1,
			ExecutionID: 1,
		},
	}, nil)

	total, execs, err := e.tagExecutionMgr.List(nil, nil)
	e.Require().Nil(err)
	e.Require().Len(execs, 1)
	e.Equal(int64(1), total)
	e.Equal(int64(1), execs[0].ID)
	e.Equal(int64(1), execs[0].ExecutionID)

	e.tagExecutionDAO.AssertExpectations(e.T())

}

func (e *tagExecutionManagerTestSuite) TestGet() {
	e.tagExecutionDAO.On("Get", mock.Anything, mock.Anything).Return(&tag_execution.TagExecution{
		ID:          1,
		TagID:       1,
		ExecutionID: 1,
	}, nil)
	te, err := e.tagExecutionMgr.Get(nil, 1)
	e.Require().Nil(err)
	e.Equal(int64(1), te.ID)
	e.Equal(int64(1), te.ExecutionID)

	e.tagExecutionDAO.AssertExpectations(e.T())
}

func (e *tagExecutionManagerTestSuite) TestDelete() {
	e.tagExecutionDAO.On("Delete", mock.Anything, mock.Anything).Return(nil)
	err := e.tagExecutionMgr.Delete(nil, 1)
	e.Require().Nil(err)

	e.tagExecutionDAO.AssertExpectations(e.T())
}

func TestTagExecutionManagerSuite(t *testing.T) {
	suite.Run(t, &tagExecutionManagerTestSuite{})
}
