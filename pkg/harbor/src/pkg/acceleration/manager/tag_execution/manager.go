package tag_execution

import (
	"context"
	"fmt"
	"time"

	"github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/lib/log"
	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/task/dao"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/dao/tag_execution"
)

var executionSweeperCount = map[string]uint8{}

// Manager manages the trigger tes
type Manager interface {
	// Create new policy
	Create(context.Context, string, int64, *tag_execution.TagExecution) (int64, error)
	// List the tes, returns the policy list and error
	List(context.Context, *q.Query) (int64, []*tag_execution.TagExecution, error)
	// Get policy with specified ID
	Get(context.Context, int64) (*tag_execution.TagExecution, error)
	// Delete the specified policy
	Delete(context.Context, int64) error
}

// tagExecutionManager ...
type tagExecutionManager struct {
	teDAO        tag_execution.DAO
	executionDAO dao.ExecutionDAO
}

// NewTagExecutionManager ...
func NewTagExecutionManager() Manager {
	return &tagExecutionManager{
		teDAO:        tag_execution.NewTagExecutionDAO(),
		executionDAO: dao.NewExecutionDAO(),
	}
}

func (m *tagExecutionManager) Create(ctx context.Context, vendorType string, vendorID int64, te *tag_execution.TagExecution) (int64, error) {
	t := time.Now()
	te.CreationTime = t
	te.UpdateTime = t

	id, err := m.teDAO.Create(ctx, te)
	if err != nil {
		return 0, err
	}

	if err := m.sweep(ctx, vendorType, vendorID); err != nil {
		log.Errorf("failed to sweep the executions of %s: %v", vendorType, err)
		return 0, err
	}

	return id, nil
}

func (m *tagExecutionManager) sweep(ctx context.Context, vendorType string, vendorID int64) error {
	size := int64(executionSweeperCount[vendorType])
	if size == 0 {
		log.Debugf("the execution sweeper size doesn't set for %s, skip sweep", vendorType)
		return nil
	}

	// get the #size execution record
	query, err := q.Build("", "", 1, size)
	query.Keywords["VendorType"] = vendorType
	query.Keywords["VendorID"] = vendorID

	executions, err := m.executionDAO.List(ctx, query)
	if err != nil {
		return err
	}

	executionIds := make(map[int64]string)
	if len(executions) > 0 {
		for _, execution := range executions {
			executionIds[execution.ID] = ""
		}
	}

	// get the #size execution record
	teQuery, err := q.Build("", "", 1, size)
	if err != nil {
		return fmt.Errorf("build query string failed: %s", err)
	}
	tes, err := m.teDAO.List(ctx, teQuery)
	if err != nil {
		return err
	}

	for _, te := range tes {
		_, ok := executionIds[te.ExecutionID]
		if !ok {
			if err := m.teDAO.Delete(ctx, te.ID); err != nil {
				// the execution may be deleted by the other sweep operation, ignore the not found error
				if errors.IsNotFoundErr(err) {
					continue
				}
				log.Errorf("failed to delete the tag_execution %d: %v", te.ID, err)
			}
		}
	}

	return nil
}

func (m *tagExecutionManager) List(ctx context.Context, query *q.Query) (int64, []*tag_execution.TagExecution, error) {

	total, err := m.teDAO.Count(ctx, query)
	if err != nil {
		return 0, nil, err
	}

	tes, err := m.teDAO.List(ctx, query)
	if err != nil {
		return 0, nil, err
	}

	return total, tes, nil
}

// Get trigger tag executions with specified ID
func (m *tagExecutionManager) Get(ctx context.Context, id int64) (*tag_execution.TagExecution, error) {
	te, err := m.teDAO.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return te, nil
}

// Delete the specified trigger policy
func (m *tagExecutionManager) Delete(ctx context.Context, teID int64) error {
	return m.teDAO.Delete(ctx, teID)
}

// SetExecutionSweeperCount sets the count of execution records retained by the sweeper
// If no count is set for the specified vendor, the default value will be used
// The sweeper retains the latest created #count execution records for the specified vendor
func SetExecutionSweeperCount(vendorType string, count uint8) {
	executionSweeperCount[vendorType] = count
}
