package rest

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"

	"github.com/goharbor/harbor/src/jobservice/logger"
	"github.com/goharbor/harbor/src/lib/errors"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/rest/auth"
)

const (
	// defaultRefreshInterval is the default interval with seconds of refreshing state
	defaultRefreshInterval = 5
	// refreshAfterHeader provides the refresh interval value
	refreshAfterHeader = "Refresh-After"
)

// Client defines the methods to access the adapter services that
// implement the REST API specs
type Client interface {
	// CheckHealth gets the metadata of the given accelerator
	//
	//   Returns:
	//     string : health status
	//     error                   : non nil error if any errors occurred
	CheckHealth() (string, error)

	// SubmitAccelerate initiates a accelerating of the given artifact.
	// Returns `nil` if the request was accepted, a non `nil` error otherwise.
	//
	//   Arguments:
	//     req *AccelerateRequest : request including the registry and artifact data
	//
	//   Returns:
	//     *AccelerateResponse : response with UUID for tracking the accelerate results
	//     error         : non nil error if any errors occurred
	SubmitAccelerate(req *AccelerateRequest) (*AccelerateResponse, error)

	// GetAccelerateState gets the accelerate result for the corresponding AccelerateRequest identifier.
	// Note that this is a blocking method which either returns a non `nil` accelerate state or error.
	// A caller is supposed to cast the returned interface{} to a structure that corresponds
	//
	//   Arguments:
	//     accelerateRequestID string  : the ID of the accelerate submitted before
	//   Returns:
	//     string : the accelerate of the given artifact
	//     error  : non nil error if any errors occurred
	GetAccelerateState(accelerateRequestID string) (string, error)
}

// basicClient is default implementation of the Client interface
type basicClient struct {
	httpClient *http.Client
	spec       *Spec
	authorizer auth.Authorizer
}

// responseHandlerFunc is a handler func template for handling the http response data,
// especially the error part.
type responseHandler func(code int, resp *http.Response) ([]byte, error)

// generalResponseHandler create a general response handler to cover the common cases.
func generalResponseHandler(expectedCode int, MIME string) responseHandler {
	return func(code int, resp *http.Response) ([]byte, error) {
		return generalRespHandlerFunc(expectedCode, code, resp, MIME)
	}
}

// stateResponseHandler creates response handler for get state special case.
func stateResponseHandler() responseHandler {
	return func(code int, resp *http.Response) ([]byte, error) {
		if code == http.StatusFound {
			// Set default
			retryAfter := defaultRefreshInterval // seconds
			// Read `retry after` info from header
			v := resp.Header.Get(refreshAfterHeader)
			if len(v) > 0 {
				if i, err := strconv.ParseInt(v, 10, 8); err == nil {
					retryAfter = int(i)
				} else {
					// log error
					logger.Errorf("Parse `%s` error: %s", refreshAfterHeader, err)
				}
			}

			return nil, &StateNotReadyError{RetryAfter: retryAfter}
		}

		return generalRespHandlerFunc(http.StatusOK, code, resp, "application/json;charset=UTF-8")
	}
}

// generalRespHandlerFunc is a handler to cover the general cases
func generalRespHandlerFunc(expectedCode, code int, resp *http.Response, MIME string) ([]byte, error) {
	buf, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if code != expectedCode {
		if len(buf) > 0 {
			// Try to read error response
			eResp := &ErrorResponse{}
			if MIME == "application/json;charset=UTF-8" {

				err := json.Unmarshal(buf, eResp)
				if err != nil {
					return nil, errors.Wrap(err, "general response handler")
				}

			}

			// Append more contexts
			eResp.Message = fmt.Sprintf(
				"%s: general response handler: unexpected status code: %d, expected: %d",
				eResp.Message,
				code,
				expectedCode,
			)

			return nil, eResp
		}

		return nil, errors.Errorf("general response handler: unexpected status code: %d, expected: %d", code, expectedCode)
	}

	return buf, nil
}

// NewClient news a basic client
func NewClient(url, authType, accessCredential string, skipCertVerify bool) (Client, error) {
	transport := &http.Transport{
		Proxy:        http.ProxyFromEnvironment,
		MaxIdleConns: 100,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: skipCertVerify,
		},
	}

	authorizer, err := auth.GetAuthorizer(authType, accessCredential)
	if err != nil {
		return nil, errors.Wrap(err, "new v1 client")
	}

	return &basicClient{
		httpClient: &http.Client{
			Timeout:   time.Second * 5,
			Transport: transport,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				return http.ErrUseLastResponse
			},
		},
		spec:       NewSpec(url),
		authorizer: authorizer,
	}, nil
}

// CheckHealth ...
func (c *basicClient) CheckHealth() (string, error) {
	def := c.spec.CheckHealth()
	request, err := http.NewRequest(http.MethodGet, def.URL, nil)
	if err != nil {
		return "", errors.Wrap(err, "v1 client: submit accelerate")
	}

	// Resolve header
	def.Resolver(request)

	respData, err := c.send(request, generalResponseHandler(http.StatusOK, "text/plain;charset=UTF-8"))
	if err != nil {
		return "", errors.Wrap(err, "v1 client: check health")
	}

	return string(respData), nil
}

// SubmitAccelerate ...
func (c *basicClient) SubmitAccelerate(req *AccelerateRequest) (*AccelerateResponse, error) {
	if req == nil {
		return nil, errors.New("nil request")
	}

	data, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "v1 client: submit accelerate")
	}

	def := c.spec.SubmitAccelerate()
	request, err := http.NewRequest(http.MethodPost, def.URL, bytes.NewReader(data))
	if err != nil {
		return nil, errors.Wrap(err, "v1 client: submit accelerate")
	}

	// Resolve header
	def.Resolver(request)

	respData, err := c.send(request, generalResponseHandler(http.StatusAccepted, "application/json;charset=UTF-8"))
	if err != nil {
		return nil, errors.Wrap(err, "v1 client: submit accelerate")
	}

	resp := &AccelerateResponse{}
	if err := json.Unmarshal(respData, resp); err != nil {
		return nil, errors.Wrap(err, "v1 client: submit accelerate")
	}

	return resp, nil
}

// GetAccelerateState ...
func (c *basicClient) GetAccelerateState(accelerateRequestID string) (string, error) {
	if len(accelerateRequestID) == 0 {
		return "", errors.New("empty accelerate request ID")
	}

	def := c.spec.GetAccelerateState(accelerateRequestID)

	request, err := http.NewRequest(http.MethodGet, def.URL, nil)
	if err != nil {
		return "", errors.Wrap(err, "v1 client: get accelerate state")
	}

	// Resolve header
	def.Resolver(request)

	respData, err := c.send(request, stateResponseHandler())
	if err != nil {
		// This error should not be wrapped
		return "", err
	}

	resp := &AccelerateStateResponse{}
	err = json.Unmarshal(respData, resp)
	if err != nil {
		// This error should not be wrapped
		return "", err
	}
	return resp.State, nil
}

func (c *basicClient) send(req *http.Request, h responseHandler) ([]byte, error) {
	if c.authorizer != nil {
		if err := c.authorizer.Authorize(req); err != nil {
			return nil, errors.Wrap(err, "send: authorization")
		}
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			// Just logged
			logger.Errorf("close response body error: %s", err)
		}
	}()

	return h(resp.StatusCode, resp)
}
