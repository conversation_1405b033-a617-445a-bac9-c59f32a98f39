package rest

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// ClientTestSuite tests the v1 client
type ClientTestSuite struct {
	suite.Suite

	testServer *httptest.Server
	client     Client
}

// TestClient is the entry of ClientTestSuite
func TestClient(t *testing.T) {
	suite.Run(t, new(ClientTestSuite))
}

// SetupSuite prepares the test suite env
func (suite *ClientTestSuite) SetupSuite() {
	suite.testServer = httptest.NewServer(&mockHandler{})

	c, err := NewClient(suite.testServer.URL, "", "", true)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), c)

	suite.client = c
}

// TestClientMetadata tests the metadata of the client
func (suite *ClientTestSuite) TestClientCheckHealth() {
	m, err := suite.client.CheckHealth()
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), m)

	assert.Equal(suite.T(), m, "ok")
}

// TestClientSubmitaccelerate tests the accelerate submission of client
func (suite *ClientTestSuite) TestClientSubmitAccelerate() {
	res, err := suite.client.SubmitAccelerate(&AccelerateRequest{})
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), res)

	assert.Equal(suite.T(), res.ID, "123456789")
}

// TestClientGetAccelerateStateError tests getting report failed
func (suite *ClientTestSuite) TestClientGetAccelerateStateError() {
	_, err := suite.client.GetAccelerateState("id1")
	require.Error(suite.T(), err)
	assert.Condition(suite.T(), func() (success bool) {
		success = strings.Index(err.Error(), "message") != -1
		return
	})
}

// TestClientGetAccelerateState tests getting report
func (suite *ClientTestSuite) TestClientGetAccelerateState() {
	res, err := suite.client.GetAccelerateState("id2")
	require.NoError(suite.T(), err)
	require.Empty(suite.T(), res)
}

// TestClientGetAccelerateStateNotReady tests the case that the report is not ready
func (suite *ClientTestSuite) TestClientGetAccelerateStateNotReady() {
	_, err := suite.client.GetAccelerateState("id3")
	require.Error(suite.T(), err)
	require.Condition(suite.T(), func() (success bool) {
		_, success = err.(*StateNotReadyError)
		return
	})
	assert.Equal(suite.T(), 10, err.(*StateNotReadyError).RetryAfter)
}

// TearDownSuite clears the test suite env
func (suite *ClientTestSuite) TearDownSuite() {
	suite.testServer.Close()
}

type mockHandler struct{}

// ServeHTTP ...
func (mh *mockHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	switch r.RequestURI {
	case "/api/v1/health":
		if r.Method != http.MethodGet {
			w.WriteHeader(http.StatusForbidden)
			return
		}

		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte("ok"))
		break
	case "/api/v1/accelerate":
		if r.Method != http.MethodPost {
			w.WriteHeader(http.StatusForbidden)
			return
		}

		res := &AccelerateResponse{}
		res.ID = "123456789"

		data, _ := json.Marshal(res)

		w.WriteHeader(http.StatusAccepted)
		_, _ = w.Write(data)
		break
	case "/api/v1/accelerate/id1/state":
		if r.Method != http.MethodGet {
			w.WriteHeader(http.StatusForbidden)
			return
		}

		e := &ErrorResponse{
			Message: "message",
		}

		data, _ := json.Marshal(e)

		w.WriteHeader(http.StatusNotFound)
		_, _ = w.Write(data)
		break
	case "/api/v1/accelerate/id2/state":
		if r.Method != http.MethodGet {
			w.WriteHeader(http.StatusForbidden)
			return
		}
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte("{}"))
		break
	case "/api/v1/accelerate/id3/state":
		if r.Method != http.MethodGet {
			w.WriteHeader(http.StatusForbidden)
			return
		}

		w.Header().Add(refreshAfterHeader, fmt.Sprintf("%d", 10))
		w.Header().Add("Location", "/accelerate/id3/state")
		w.WriteHeader(http.StatusFound)
		break
	}
}
