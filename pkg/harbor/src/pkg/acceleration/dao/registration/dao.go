package registration

import (
	"context"
	"time"

	"github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/lib/orm"
	"github.com/goharbor/harbor/src/lib/q"
)

// DAO defines the DAO operations of registry
type DAO interface {
	// Create the registry
	Create(ctx context.Context, as *AccelerationRegistration) (id int64, err error)
	// Count returns the count of registries according to the query
	Count(ctx context.Context, query *q.Query) (count int64, err error)
	// List the registries according to the query
	List(ctx context.Context, query *q.Query) (ases []*AccelerationRegistration, err error)
	// Get the registry specified by ID
	Get(ctx context.Context, id int64) (as *AccelerationRegistration, err error)
	// GetByName return acceleration registration by name
	GetByName(ctx context.Context, name string) (as *AccelerationRegistration, err error)
	// GetDefaultAr return acceleration registration
	GetDefaultAr(ctx context.Context) (as *AccelerationRegistration, err error)
	// SetDefaultAr sets the specified registration as default one
	SetDefaultAr(ctx context.Context, UUID string) error
	// Update the specified registry
	Update(ctx context.Context, as *AccelerationRegistration, props ...string) (err error)
	// Delete the registry specified by ID
	Delete(ctx context.Context, id int64) (err error)
}

// NewDAO creates an instance of DAO
func NewDAO() DAO {
	return &dao{}
}

type dao struct{}

func (d *dao) Create(ctx context.Context, as *AccelerationRegistration) (int64, error) {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return 0, err
	}
	id, err := ormer.Insert(as)
	if e := orm.AsConflictError(err, "AccelerationRegistration name: %s already exists", as.Name); e != nil {
		err = e
	}
	return id, err
}

func (d *dao) Count(ctx context.Context, query *q.Query) (int64, error) {
	qs, err := orm.QuerySetterForCount(ctx, &AccelerationRegistration{}, query)
	if err != nil {
		return 0, err
	}
	return qs.Count()
}

func (d *dao) List(ctx context.Context, query *q.Query) ([]*AccelerationRegistration, error) {
	registries := []*AccelerationRegistration{}
	qs, err := orm.QuerySetter(ctx, &AccelerationRegistration{}, query)
	if err != nil {
		return nil, err
	}
	if _, err = qs.All(&registries); err != nil {
		return nil, err
	}
	return registries, nil
}

func (d *dao) Get(ctx context.Context, id int64) (*AccelerationRegistration, error) {
	as := &AccelerationRegistration{
		ID: id,
	}
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return nil, err
	}
	if err := ormer.Read(as); err != nil {
		if e := orm.AsNotFoundError(err, "AccelerationRegistration %d not found", id); e != nil {
			err = e
		}
		return nil, err
	}
	return as, nil
}

func (d *dao) GetDefaultAr(ctx context.Context) (*AccelerationRegistration, error) {
	o, err := orm.FromContext(ctx)
	if err != nil {
		return nil, err
	}

	as := &AccelerationRegistration{}
	qt := o.QueryTable(new(AccelerationRegistration))
	if err := qt.Filter("is_default", true).One(as); err != nil {
		if e := orm.AsNotFoundError(err, "default acceleration registration not found"); e != nil {
			return nil, e
		}
		return nil, err
	}
	return as, nil
}

// SetDefaultAr sets the specified registration as default one
func (d *dao) SetDefaultAr(ctx context.Context, UUID string) error {
	f := func(ctx context.Context) error {
		o, err := orm.FromContext(ctx)
		if err != nil {
			return err
		}

		var count int64
		qt := o.QueryTable(new(AccelerationRegistration))
		count, err = qt.Filter("uuid", UUID).
			Filter("disabled", false).
			Update(orm.Params{
				"is_default": true,
			})
		if err != nil {
			return err
		}
		if count == 0 {
			return errors.NotFoundError(nil).WithMessage("registration %s not found", UUID)
		}

		qt2 := o.QueryTable(new(AccelerationRegistration))
		_, err = qt2.Exclude("uuid__exact", UUID).
			Filter("is_default", true).
			Update(orm.Params{
				"is_default": false,
			})

		return err
	}

	return orm.WithTransaction(f)(ctx)
}

func (d *dao) GetByName(ctx context.Context, name string) (*AccelerationRegistration, error) {
	o, err := orm.FromContext(ctx)
	if err != nil {
		return nil, err
	}

	as := &AccelerationRegistration{
		Name: name,
	}
	if err := o.Read(as); err != nil {
		if e := orm.AsNotFoundError(err, "acceleration registration %s not found", name); e != nil {
			err = e
		}
		return nil, err
	}
	return as, nil
}

func (d *dao) Update(ctx context.Context, as *AccelerationRegistration, props ...string) error {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return err
	}
	as.UpdatedAt = time.Now()
	n, err := ormer.Update(as, props...)
	if err != nil {
		if e := orm.AsConflictError(err, "AccelerationRegistration name: %s already exists", as.Name); e != nil {
			err = e
		}
		return err
	}
	if n == 0 {
		return errors.NotFoundError(nil).WithMessage("as %d not found", as.ID)
	}
	return nil
}

func (d *dao) Delete(ctx context.Context, id int64) error {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return err
	}
	n, err := ormer.Delete(&AccelerationRegistration{
		ID: id,
	})
	if err != nil {
		return err
	}
	if n == 0 {
		return errors.NotFoundError(nil).WithMessage("AccelerationRegistration %d not found", id)
	}
	return nil
}
