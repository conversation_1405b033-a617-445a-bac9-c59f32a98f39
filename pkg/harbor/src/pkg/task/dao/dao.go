package dao

import (
	"context"

	"github.com/goharbor/harbor/src/lib/orm"
)

type Dao interface {
	ListNonFinalErrorTask(ctx context.Context, executionID int64, limitRunCount int64) (int64, error)
}

var TaskDao Dao = &taskDao{}

type taskDao struct {
}

func (t *taskDao) ListNonFinalErrorTask(ctx context.Context, executionID int64, limitRunCount int64) (int64, error) {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return 0, err
	}

	var count int64

	err = ormer.Raw("select count(*) as count from task where status='Error' and execution_id=? and run_count<?", executionID, limitRunCount).
		QueryRow(&count)
	if err != nil {
		return 0, err
	}
	return count, nil
}
