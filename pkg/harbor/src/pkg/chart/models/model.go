package models

import (
	"time"

	"github.com/astaxie/beego/orm"
)

const (
	ChartTable = "ccr_chart"
)

func init() {
	orm.RegisterModel(
		new(Chart),
	)
}

type Chart struct {
	ID           int64     `orm:"pk;auto;column(id)" json:"id,omitempty"`
	ProjectName  string    `orm:"column(project_name)" json:"projectName,omitempty"`
	Name         string    `orm:"column(name)" json:"name,omitempty"`
	Version      string    `orm:"column(version)" json:"version,omitempty"`
	Size         int64     `orm:"column(size)" json:"size,omitempty"`
	Digest       string    `orm:"column(digest)" json:"digest,omitempty"`
	Metadata     string    `orm:"column(metadata)" json:"metadata,omitempty"`
	DeleteTime   time.Time `orm:"column(delete_time)" json:"deleteTime,omitempty"`
	CreateTime   time.Time `orm:"column(create_time);auto_now_add" json:"createTime,omitempty"`
	LastModified time.Time `orm:"column(last_modified)" json:"lastModified,omitempty"`
}

func (Chart) TableName() string {
	return ChartTable
}
