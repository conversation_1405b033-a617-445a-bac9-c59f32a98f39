package dao

import (
	"context"
	"encoding/json"
	"time"

	"github.com/goharbor/harbor/src/lib/orm"
	"github.com/goharbor/harbor/src/lib/q"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/models"
)

type Dao interface {
	CreateOrUpdate(ctx context.Context, chrt *models.Chart) (int64, error)
	Create(ctx context.Context, chrt *models.Chart) (int64, error)
	UpdateLastModified(ctx context.Context, id int64, t time.Time) error
	PreDeleteChartVersion(ctx context.Context, prj, name, version string) error
	DeleteChartVersion(ctx context.Context, prj, name, version string) error
	GetChartVersions(ctx context.Context, proj, name string, offset, limit int) ([]*models.Chart, error)
	ChartVersionCount(ctx context.Context, proj, name string) (int64, error)
	GetChartVersion(ctx context.Context, proj, name, version string) (*models.Chart, error)
	PreDeleteChart(ctx context.Context, prj, name string) error
	DeleteChart(ctx context.Context, prj, name string) error
	GetUnreadyChartVersion(ctx context.Context, withTotal bool) (int64, []*models.Chart, error)
	ResetChartVersion(ctx context.Context, prj, name, version string) error
	GetChartSummaryByProject(ctx context.Context, proj string) ([]*ChartSummary, error)
	CountByProjects(ctx context.Context, projs []string) (int64, error)
	GetChartVersionsByProjects(ctx context.Context, projs []string, offset, limit int) ([]*models.Chart, error)
	ChartRepoCount(ctx context.Context, proj string) (int64, error)
	ChartSize(ctx context.Context, proj string) (int64, error)
}

var _ Dao = &dao{}

// New returns an instance of the default DAO
func New() Dao {
	return &dao{}
}

type dao struct{}

func (d *dao) CreateOrUpdate(ctx context.Context, chrt *models.Chart) (int64, error) {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return 0, err
	}

	id, err := ormer.InsertOrUpdate(chrt, "project_name, name, version", "delete_time=null", "last_modified=null")
	if err != nil {
		return 0, err
	}

	return id, nil
}

func (d *dao) Create(ctx context.Context, chrt *models.Chart) (int64, error) {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return 0, err
	}

	id, err := ormer.Insert(chrt)
	if err != nil {
		if e := orm.AsConflictError(err, "chart %s-%s already exists under the repository %s",
			chrt.Name, chrt.Version, chrt.ProjectName); e != nil {
			err = e
		}
		return 0, err
	}

	return id, nil
}

func (d *dao) UpdateLastModified(ctx context.Context, id int64, t time.Time) error {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return err
	}

	_, err = ormer.Update(&models.Chart{
		ID:           id,
		LastModified: t,
	}, "last_modified")

	return err
}

func (d *dao) ResetChartVersion(ctx context.Context, prj, name, version string) error {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return err
	}

	_, err = ormer.Raw("UPDATE ccr_chart SET delete_time=null WHERE project_name=? AND name=? AND version=? AND delete_time is not null",
		prj, name, version).Exec()
	return err
}

func (d *dao) PreDeleteChartVersion(ctx context.Context, prj, name, version string) error {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return err
	}

	_, err = ormer.Raw("UPDATE ccr_chart SET delete_time=? WHERE project_name=? AND name=? AND version=? AND delete_time is null",
		time.Now(), prj, name, version).Exec()
	return err
}

func (d *dao) DeleteChartVersion(ctx context.Context, prj, name, version string) error {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return err
	}

	_, err = ormer.Raw("DELETE FROM ccr_chart WHERE project_name=? AND name=? AND version=? AND delete_time is not null",
		prj, name, version).Exec()

	return err
}

func (d *dao) PreDeleteChart(ctx context.Context, prj, name string) error {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return err
	}

	_, err = ormer.Raw("UPDATE ccr_chart SET delete_time=? WHERE project_name=? AND name=? AND delete_time is null",
		time.Now(), prj, name).Exec()
	return err
}

func (d *dao) DeleteChart(ctx context.Context, prj, name string) error {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return err
	}

	_, err = ormer.Raw("DELETE FROM ccr_chart WHERE project_name=? AND name=? AND delete_time is not null",
		prj, name).Exec()

	return err
}

func (d *dao) GetChartVersions(ctx context.Context, proj, name string, offset, limit int) ([]*models.Chart, error) {
	chrts := []*models.Chart{}
	qs, err := orm.QuerySetter(ctx, &models.Chart{}, &q.Query{
		Keywords: q.KeyWords{
			"project_name":          proj,
			"name":                  name,
			"last_modified__isnull": false,
			"delete_time__isnull":   true,
		},
	})
	if err != nil {
		return nil, err
	}

	if offset > 0 {
		qs = qs.Offset(offset)
	}

	qs = qs.Limit(limit)

	_, err = qs.All(&chrts)
	if err != nil {
		return nil, err
	}

	return chrts, nil
}

func (d *dao) CountByProjects(ctx context.Context, projs []string) (int64, error) {
	qs, err := orm.QuerySetter(ctx, &models.Chart{}, &q.Query{
		Keywords: q.KeyWords{
			"project_name__in":      projs,
			"last_modified__isnull": false,
			"delete_time__isnull":   true,
		},
	})
	if err != nil {
		return 0, err
	}

	return qs.Count()
}

// GetChartVersionsByProjects 根据项目名称列表获取版本信息，支持分页查询，返回值为 Chart 类型的切片和错误信息
func (d *dao) GetChartVersionsByProjects(ctx context.Context, projs []string, offset, limit int) ([]*models.Chart, error) {
	chrts := []*models.Chart{}

	qs, err := orm.QuerySetter(ctx, &models.Chart{}, &q.Query{
		Keywords: q.KeyWords{
			"project_name__in":      projs,
			"last_modified__isnull": false,
			"delete_time__isnull":   true,
		},
		Sorts: []*q.Sort{
			{
				Key: "project_name",
			},
			{
				Key: "name",
			},
			{
				Key: "version",
			},
		},
	})
	if err != nil {
		return nil, err
	}

	if offset > 0 {
		qs = qs.Offset(offset)
	}

	qs = qs.Limit(limit)

	_, err = qs.All(&chrts)
	if err != nil {
		return nil, err
	}

	return chrts, nil
}

func (d *dao) DistinctChartByProject(ctx context.Context, proj []string) ([]*models.Chart, error) {
	qs, err := orm.QuerySetter(ctx, &models.Chart{}, &q.Query{
		Keywords: q.KeyWords{
			"project_name__in": proj,
		},
	})
	if err != nil {
		return nil, err
	}

	var chrts []*models.Chart

	_, err = qs.Distinct().All(&chrts, "project_name", "name")
	return chrts, nil
}

func (d *dao) ChartVersionCount(ctx context.Context, proj, name string) (int64, error) {
	qs, err := orm.QuerySetter(ctx, &models.Chart{}, &q.Query{
		Keywords: q.KeyWords{
			"project_name":          proj,
			"name":                  name,
			"last_modified__isnull": false,
		},
	})
	if err != nil {
		return 0, err
	}

	return qs.Count()
}

func (d *dao) GetChartVersion(ctx context.Context, proj, name, version string) (*models.Chart, error) {
	qs, err := orm.QuerySetter(ctx, &models.Chart{}, &q.Query{
		Keywords: q.KeyWords{
			"project_name":          proj,
			"name":                  name,
			"version":               version,
			"last_modified__isnull": false,
		},
	})
	if err != nil {
		return nil, err
	}

	chrt := models.Chart{}
	err = qs.One(&chrt)

	if err == orm.ErrNoRows {
		return nil, nil
	}

	return &chrt, nil
}

func (d *dao) GetUnreadyChartVersion(ctx context.Context, withTotal bool) (int64, []*models.Chart, error) {
	qs, err := orm.QuerySetter(ctx, &models.Chart{}, &q.Query{
		Keywords: q.KeyWords{
			"last_modified__isnull": true,
		},
	})
	if err != nil {
		return 0, nil, err
	}

	qs = qs.SetCond(orm.NewCondition().Or("delete_time__isnull", false))

	var total int64
	if withTotal {
		total, err = qs.Count()
		if err != nil {
			return total, nil, err
		}
	}

	var chrts []*models.Chart
	_, err = qs.All(&chrts)
	return total, chrts, err
}

func (d *dao) GetChartSummaryByProject(ctx context.Context, proj string) ([]*ChartSummary, error) {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return nil, err
	}

	sql := "select distinct name, " +
		"min(create_time) over w as createTime, " +
		"max(last_modified) over w as lastModified, " +
		"count(1) over w as total, " +
		"last_value(metadata) over w as metadata " +
		"from ccr_chart where project_name=? and last_modified is not null and delete_time is null " +
		"window w as (partition by name)"

	values := []orm.Params{}
	_, err = ormer.Raw(sql, proj).Values(&values)

	if err != nil {
		return nil, err
	}

	summaryBytes, err := json.Marshal(values)
	if err != nil {
		return nil, err
	}

	var sum []*ChartSummary
	err = json.Unmarshal(summaryBytes, &sum)

	return sum, err
}

func (d *dao) ChartRepoCount(ctx context.Context, proj string) (int64, error) {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return 0, err
	}

	var cnt int64
	if proj != "" {
		err = ormer.Raw(`SELECT COUNT(DISTINCT(project_name, name)) FROM ccr_chart WHERE project_name=?`, proj).QueryRow(&cnt)
	} else {
		err = ormer.Raw(`SELECT COUNT(DISTINCT(project_name, name)) FROM ccr_chart`).QueryRow(&cnt)
	}

	return cnt, err
}

func (d *dao) ChartSize(ctx context.Context, proj string) (int64, error) {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return 0, err
	}

	var totalSize int64

	if proj != "" {
		err = ormer.Raw(`SELECT SUM(size) FROM ccr_chart WHERE project_name=?`, proj).QueryRow(&totalSize)
	} else {
		err = ormer.Raw(`SELECT SUM(size) FROM ccr_chart`).QueryRow(&totalSize)
	}

	return totalSize, err
}

type ChartSummary struct {
	Name         string    `json:"name,omitempty"`
	CreateTime   time.Time `json:"createTime,omitempty"`
	LastModified time.Time `json:"lastModified,omitempty"`
	Total        string    `json:"total,omitempty"`
	Metadata     string    `json:"metadata,omitempty"`
}
