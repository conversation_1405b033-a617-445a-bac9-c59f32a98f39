package baiduccr

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_getRegionFromHost(t *testing.T) {
	region := getRegionFromHost("ccr-xxxx-xxx.cnc.gz.baidubce.com")
	assert.Equal(t, "gz", region)

	region = getRegionFromHost("ccr-xxxx-xxx-cnc.gz.baidubce.com")
	assert.Equal(t, "gz", region)

	region = getRegionFromHost("ccr-xxx-inter-sync-gz")
	assert.Equal(t, "gz", region)

	region = getRegionFromHost("ccr-xxxx.cnc.gz")
	assert.Equal(t, "", region)

	region = getRegionFromHost("ccr-xxx-xxx")
	assert.Equal(t, "", region)
}
