package baiduccr

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"time"

	"github.com/goharbor/harbor/src/lib/log"
)

//go:generate mockgen -destination=./fake_client_factory.go -package=baiduccr icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/reg/adapter/baiduccr ClientFactory
type ClientFactory interface {
	GetClient() (ClientInterface, error)
}

var _ ClientFactory = &clientCache{}

type clientCache struct {
	region       string
	instanceID   string
	username     string
	expiredAt    time.Time
	userInfoPath string

	client ClientInterface
}

var (
	errUserInfoNotExist = fmt.Errorf("user info not exist when get temporary token")
)

func newClientCache(instanceID, username, region string) *clientCache {
	return &clientCache{
		instanceID:   instanceID,
		username:     username,
		region:       region,
		expiredAt:    time.Now(),
		userInfoPath: credentialPath + "/" + instanceID,
	}
}

func (c *clientCache) GetClient() (ClientInterface, error) {

	if c.expiredAt.UTC().Add(-15 * time.Minute).After(time.Now().UTC()) {
		return c.client, nil
	}

	// Change the token 15 minutes in advance
	log.Debugf("client expired, start refresh it, username is :%s, expiredAt: %s", c.username, c.expiredAt)
	content, err := ioutil.ReadFile(c.userInfoPath)
	if err != nil {
		if os.IsNotExist(err) {
			log.Warningf("instance %s file is not exist", c.instanceID)
			return nil, errUserInfoNotExist
		}
		return nil, err
	}

	var usernamesMap map[string]*struct {
		AK           string    `json:"ak,omitempty"`
		SK           string    `json:"sk,omitempty"`
		SessionToken string    `json:"sessionToken,omitempty"`
		ExpiredAt    time.Time `json:"expiredAt,omitempty"`
	}
	err = json.Unmarshal(content, &usernamesMap)
	if err != nil {
		return nil, err
	}

	cred := usernamesMap[c.username]
	if cred == nil {
		return nil, errUserInfoNotExist
	}

	c.expiredAt = cred.ExpiredAt
	c.client = newClient(cred.AK, cred.SK, cred.SessionToken, c.region)

	return c.client, nil
}
