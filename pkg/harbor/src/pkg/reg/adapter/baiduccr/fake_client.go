// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/reg/adapter/baiduccr (interfaces: ClientInterface)

// Package baiduccr is a generated GoMock package.
package baiduccr

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockClientInterface is a mock of ClientInterface interface.
type MockClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClientInterfaceMockRecorder
}

// MockClientInterfaceMockRecorder is the mock recorder for MockClientInterface.
type MockClientInterfaceMockRecorder struct {
	mock *MockClientInterface
}

// NewMockClientInterface creates a new mock instance.
func NewMockClientInterface(ctrl *gomock.Controller) *MockClientInterface {
	mock := &MockClientInterface{ctrl: ctrl}
	mock.recorder = &MockClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientInterface) EXPECT() *MockClientInterfaceMockRecorder {
	return m.recorder
}

// CreateNamespace mocks base method.
func (m *MockClientInterface) CreateNamespace(arg0 string, arg1 *CreateProjectRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNamespace", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateNamespace indicates an expected call of CreateNamespace.
func (mr *MockClientInterfaceMockRecorder) CreateNamespace(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNamespace", reflect.TypeOf((*MockClientInterface)(nil).CreateNamespace), arg0, arg1)
}

// CreateTemporaryToken mocks base method.
func (m *MockClientInterface) CreateTemporaryToken(arg0 string, arg1 int) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTemporaryToken", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTemporaryToken indicates an expected call of CreateTemporaryToken.
func (mr *MockClientInterfaceMockRecorder) CreateTemporaryToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTemporaryToken", reflect.TypeOf((*MockClientInterface)(nil).CreateTemporaryToken), arg0, arg1)
}

// DeleteImage mocks base method.
func (m *MockClientInterface) DeleteImage(arg0, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteImage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteImage indicates an expected call of DeleteImage.
func (mr *MockClientInterfaceMockRecorder) DeleteImage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteImage", reflect.TypeOf((*MockClientInterface)(nil).DeleteImage), arg0, arg1, arg2, arg3)
}

// GetImage mocks base method.
func (m *MockClientInterface) GetImage(arg0, arg1, arg2, arg3 string, arg4, arg5 int) (*ListTagResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImage", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*ListTagResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImage indicates an expected call of GetImage.
func (mr *MockClientInterfaceMockRecorder) GetImage(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImage", reflect.TypeOf((*MockClientInterface)(nil).GetImage), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetNamespace mocks base method.
func (m *MockClientInterface) GetNamespace(arg0, arg1 string) (*ProjectResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNamespace", arg0, arg1)
	ret0, _ := ret[0].(*ProjectResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamespace indicates an expected call of GetNamespace.
func (mr *MockClientInterfaceMockRecorder) GetNamespace(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamespace", reflect.TypeOf((*MockClientInterface)(nil).GetNamespace), arg0, arg1)
}

// ListNamespaces mocks base method.
func (m *MockClientInterface) ListNamespaces(arg0 string, arg1, arg2 int) (*ListProjectResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListNamespaces", arg0, arg1, arg2)
	ret0, _ := ret[0].(*ListProjectResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListNamespaces indicates an expected call of ListNamespaces.
func (mr *MockClientInterfaceMockRecorder) ListNamespaces(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListNamespaces", reflect.TypeOf((*MockClientInterface)(nil).ListNamespaces), arg0, arg1, arg2)
}

// ListReposByNamespace mocks base method.
func (m *MockClientInterface) ListReposByNamespace(arg0, arg1 string, arg2, arg3 int) (*ListRepositoryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListReposByNamespace", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*ListRepositoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListReposByNamespace indicates an expected call of ListReposByNamespace.
func (mr *MockClientInterfaceMockRecorder) ListReposByNamespace(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListReposByNamespace", reflect.TypeOf((*MockClientInterface)(nil).ListReposByNamespace), arg0, arg1, arg2, arg3)
}
