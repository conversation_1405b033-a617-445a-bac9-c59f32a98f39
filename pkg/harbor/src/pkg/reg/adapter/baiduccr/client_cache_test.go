package baiduccr

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func Test_ClientCache_GetClient(t *testing.T) {
	cc := newClientCache("normal-id", "username", "testregion")

	ci, err := cc.GetClient()
	assert.Equal(t, errUserInfoNotExist, err)
	assert.Nil(t, ci)

	fileName := fmt.Sprintf("/tmp/%d", time.Now().Unix())
	fs, err := os.OpenFile(fileName, os.O_CREATE|os.O_RDWR, fs.ModePerm)
	defer fs.Close()
	assert.NoError(t, err)
	assert.NotNil(t, fs)

	cc.userInfoPath = fileName
	ci, err = cc.GetClient()
	assert.Error(t, err)
	assert.Nil(t, ci)

	userInfo := map[string]*struct {
		AK           string    `json:"ak,omitempty"`
		SK           string    `json:"sk,omitempty"`
		SessionToken string    `json:"sessionToken,omitempty"`
		ExpiredAt    time.Time `json:"expiredAt,omitempty"`
	}{
		"username": {
			AK:           "ak",
			SK:           "sk",
			SessionToken: "sessionToken",
			ExpiredAt:    time.Now().Add(time.Minute),
		},
	}

	userBytes, err := json.Marshal(userInfo)
	assert.NoError(t, err)

	_, err = fs.Write(userBytes)
	assert.NoError(t, err)

	ci, err = cc.GetClient()
	assert.NoError(t, err)
	assert.NotNil(t, ci)
}
