package baiduccr

import (
	"net/http"
	"sync"
	"time"

	"github.com/goharbor/harbor/src/common/http/modifier"

	//"github.com/goharbor/harbor/src/common/http/modifier"
	"github.com/goharbor/harbor/src/lib/log"
)

// Credential ...
type Credential modifier.Modifier

var _ Credential = &baiduAuthCredential{}

var (
	credentialSet = sync.Map{}
)

func NewCredential(username, password, instanceID string, clientFactory ClientFactory) modifier.Modifier {
	key := instanceID + username

	cred, loaded := credentialSet.LoadOrStore(key, &baiduAuthCredential{
		instanceID:        instanceID,
		clientCache:       clientFactory,
		username:          username,
		password:          password,
		passwordExpiredAt: time.Now(),
	})

	log.Debugf("[baidu-ccr.NewCredential] credential exists: %v", loaded)

	return cred.(modifier.Modifier)
}

// Implements interface Credential
type baiduAuthCredential struct {
	instanceID  string
	clientCache ClientFactory

	username          string
	password          string
	passwordExpiredAt time.Time

	sync.Mutex
}

// NewAuth ...
func NewAuth(username, password string, instanceID string, clientFactory ClientFactory) *baiduAuthCredential {
	return &baiduAuthCredential{
		instanceID:        instanceID,
		clientCache:       clientFactory,
		username:          username,
		password:          password,
		passwordExpiredAt: time.Now().Add(time.Second),
	}
}

func (q *baiduAuthCredential) Modify(r *http.Request) error {
	q.Lock()
	defer q.Unlock()

	if !q.isCacheTokenValid() {
		if err := q.getTempInstanceToken(); err != nil {
			log.Errorf("baidu-ccr.Modify.isCacheTokenValid.updateToken=%s, err=%v", q.passwordExpiredAt, err)
			return err
		}
	}
	r.SetBasicAuth(q.username, q.password)
	log.Infof("[baidu-ccr.Modify after]Host: %v, header: %#v", r.Host, r.Header)
	return nil
}

func (q *baiduAuthCredential) isCacheTokenValid() bool {
	log.Infof("[]baidu-ccr.isCacheTokenValid: username: %s, expiredAt: %v", q.username, q.passwordExpiredAt)
	if q.passwordExpiredAt.IsZero() {
		return false
	}
	if q.password == "" {
		return false
	}
	// refresh token in advanced
	if time.Now().After(q.passwordExpiredAt.Add(-1 * time.Minute)) {
		return false
	}
	return true
}

func (q *baiduAuthCredential) getTempInstanceToken() error {
	cli, err := q.clientCache.GetClient()
	if err != nil {
		if err == errUserInfoNotExist {
			log.Warningf("[]baidu-ccr.getTempInstanceToken: username %s not exist: %s, use default one", q.username, err)
			return nil
		}
		log.Errorf("[]baidu-ccr.getTempInstanceToken: get client failed, username: %s, err: %s", q.username, err)
		return err
	}

	password, err := cli.CreateTemporaryToken(q.instanceID, 2)
	if err != nil {
		return err
	}

	q.password = password
	q.passwordExpiredAt = time.Now().Add(2 * time.Hour)

	return nil
}
