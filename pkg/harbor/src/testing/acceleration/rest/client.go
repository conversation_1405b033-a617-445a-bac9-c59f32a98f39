// Code generated by mockery v2.1.0. DO NOT EDIT.

package rest

import (
	mock "github.com/stretchr/testify/mock"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/rest"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

// GetMetadata provides a mock function with given fields:
func (_m *Client) CheckHealth() (string, error) {
	ret := _m.Called()

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(string)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetScanReport provides a mock function with given fields: scanRequestID, reportMIMEType
func (_m *Client) SubmitAccelerate(req *rest.AccelerateRequest) (*rest.AccelerateResponse, error) {
	ret := _m.Called(req)

	var r0 *rest.AccelerateResponse
	if rf, ok := ret.Get(0).(func(*rest.AccelerateRequest) *rest.AccelerateResponse); ok {
		r0 = rf(req)
	} else {
		r0 = ret.Get(0).(*rest.AccelerateResponse)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*rest.AccelerateRequest) error); ok {
		r1 = rf(req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SubmitScan provides a mock function with given fields: req
func (_m *Client) GetAccelerateState(req string) (string, error) {
	ret := _m.Called(req)

	var r0 string
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(string)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}
