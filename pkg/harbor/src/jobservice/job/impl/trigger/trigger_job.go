package trigger

import (
	"bytes"
	"fmt"
	"net/http"
	"os"
	"strconv"

	"github.com/goharbor/harbor/src/jobservice/job"
	"github.com/goharbor/harbor/src/jobservice/logger"
)

// Max retry has the same meaning as max fails.
const triggerMaxFails = "JOBSERVICE_TRIGGER_JOB_MAX_RETRY"

// Job implements the job interface, which send trigger by http or https.
type Job struct {
	client *http.Client
	logger logger.Interface
	ctx    job.Context
}

// MaxFails returns that how many times this job can fail, get this value from ctx.
func (j *Job) MaxFails() (result uint) {
	// Default max fails count is 10, and its max retry interval is around 3h
	// Large enough to ensure most situations can notify successfully
	result = 10
	if maxFails, exist := os.LookupEnv(triggerMaxFails); exist {
		mf, err := strconv.ParseUint(maxFails, 10, 32)
		if err != nil {
			logger.Warningf("Fetch trigger job maxFails error: %s", err.Error())
			return result
		}
		result = uint(mf)
	}
	return result
}

// MaxCurrency is implementation of same method in Interface.
func (j *Job) MaxCurrency() uint {
	return 0
}

// ShouldRetry ...
func (j *Job) ShouldRetry() bool {
	return true
}

// Validate implements the interface in job/Interface
func (j *Job) Validate(params job.Parameters) error {
	return nil
}

// Run implements the interface in job/Interface
func (j *Job) Run(ctx job.Context, params job.Parameters) error {
	if err := j.init(ctx, params); err != nil {
		return err
	}

	if err := j.execute(ctx, params); err != nil {
		j.logger.Error(err)
		return err
	}

	return nil
}

// init trigger job
func (j *Job) init(ctx job.Context, params map[string]interface{}) error {
	j.logger = ctx.GetLogger()
	j.ctx = ctx

	// default use secure transport
	j.client = httpHelper.clients[secure]
	if v, ok := params["skip_cert_verify"]; ok {
		if skipCertVerify, ok := v.(bool); ok && skipCertVerify {
			// if skip cert verify is true, it means not verify remote cert, use insecure client
			j.client = httpHelper.clients[insecure]
		}
	}
	return nil
}

// execute trigger job
func (j *Job) execute(ctx job.Context, params map[string]interface{}) error {
	payload := params["payload"].(string)
	address := params["address"].(string)

	req, err := http.NewRequest(http.MethodPost, address, bytes.NewReader([]byte(payload)))
	if err != nil {
		return err
	}
	if v, ok := params["headers"]; ok {
		headers := v.(map[string]interface{})
		for key, value := range headers {
			req.Header.Set(key, value.(string))
		}
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := j.client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("trigger job(target: %s) response code is %d", address, resp.StatusCode)
	}

	return nil
}
