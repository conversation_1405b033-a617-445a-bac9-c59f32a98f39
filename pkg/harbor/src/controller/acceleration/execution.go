package acceleration

import (
	"context"
	"fmt"
	"os"

	"github.com/goharbor/harbor/src/common/rbac"
	"github.com/goharbor/harbor/src/controller/robot"
	"github.com/goharbor/harbor/src/jobservice/job"
	"github.com/goharbor/harbor/src/lib"
	"github.com/goharbor/harbor/src/lib/config"
	"github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/lib/log"
	"github.com/goharbor/harbor/src/lib/orm"
	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/permission/types"
	"github.com/goharbor/harbor/src/pkg/robot/model"
	"github.com/goharbor/harbor/src/pkg/scheduler"
	"github.com/goharbor/harbor/src/pkg/tag"
	"github.com/goharbor/harbor/src/pkg/task"
	"github.com/google/uuid"

	ccrjob "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/jobservice/job"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/accelerator"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/dao/tag_execution"
	manager_tag_execution "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/manager/tag_execution"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/rest"
)

const (
	configRegistryEndpoint = "registryEndpoint"
	configCoreInternalAddr = "coreInternalAddr"

	artifactKey     = "artifact"
	registrationKey = "registration"
	tagKey          = "tag"

	artifactIDKey  = "artifact_id"
	artifactTagKey = "artifact_tag"
	robotIDKey     = "robot_id"
)

func init() {
	// keep only the latest created 50 acceleration execution records
	task.SetExecutionSweeperCount(ccrjob.AccelerationJob, 50)
	manager_tag_execution.SetExecutionSweeperCount(ccrjob.AccelerationJob, 50)
}

// Ctl is a global acceleration controller instance
var Ctl = NewController()

// Controller defines the operations related with acceleration
type Controller interface {
	// Acceleration the artifact
	Acceleration(ctx context.Context, e *Event, trigger string) (err error)
}

// NewController creates a new instance of the acceleration controller
func NewController() Controller {
	return &controller{
		execMgr:    task.ExecMgr,
		taskMgr:    task.Mgr,
		tagMgr:     tag.Mgr,
		teMgr:      manager_tag_execution.NewTagExecutionManager(),
		scheduler:  scheduler.Sched,
		ormCreator: orm.Crt,
		wp:         lib.NewWorkerPool(10),
		// Refer to the default robot account controller
		rc: robot.Ctl,

		// Get the required configuration options
		config: func(cfg string) (string, error) {
			switch cfg {
			case configRegistryEndpoint:
				return config.ExtEndpoint()
			case configCoreInternalAddr:
				return config.InternalCoreURL(), nil
			default:
				return "", errors.Errorf("configuration option %s not defined", cfg)
			}
		},
		// Generate UUID with uuid lib
		uuid: func() (string, error) {
			aUUID, err := uuid.NewUUID()
			if err != nil {
				return "", err
			}

			return aUUID.String(), nil
		},
	}
}

type controller struct {
	execMgr task.ExecutionManager
	taskMgr task.Manager

	tagMgr tag.Manager
	teMgr  manager_tag_execution.Manager

	scheduler  scheduler.Scheduler
	ormCreator orm.Creator
	wp         *lib.WorkerPool

	// UUID generator
	uuid uuidGenerator

	// Robot account controller
	rc robot.Controller

	// Configuration getter func
	config configGetter
}

func (c controller) Acceleration(ctx context.Context, e *Event, trigger string) error {

	logger := log.GetLogger(ctx)

	if e == nil {
		return errors.New("nil event to acceleration")
	}

	ar, err := accelerator.DefaultController.GetDefaultAccelerator(ctx)
	if err != nil {
		return errors.Wrap(err, "acceleration controller: accelerator")
	}

	// In case it does not exist
	if ar == nil {
		return errors.PreconditionFailedError(nil).WithMessage("no available accelerator")
	}

	// Check if it is disabled
	if ar.Disabled {
		return errors.PreconditionFailedError(nil).WithMessage("accelerator %s is disabled", ar.Name)
	}

	if ar.Health != "healthy" {
		return errors.PreconditionFailedError(nil).WithMessage("accelerator %s is unhealthy", ar.Name)
	}

	supported := hasCapability(ar, e.Artifact)
	if !supported {
		// image index not supported by the accelerator, so continue to walk its children
		return errors.BadRequestError(nil).WithMessage("the configured accelerator %s does "+
			"not support accelerating artifact with mime type %s", ar.Name, e.Artifact.ManifestMediaType)
	}

	tags, err := c.tagMgr.List(ctx, &q.Query{
		Keywords: map[string]interface{}{
			"ArtifactID": e.Artifact.ID,
			"Name":       e.Repository.Reference,
		},
	})
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("list tags failed: %s", err))
	}
	if len(tags) == 0 {
		return errors.Wrap(err, fmt.Sprintf("tag %s not found", e.Repository.Reference))
	}
	modelTag := tags[0]

	logger.Infof("model tag %v", modelTag)

	extraAttrs := map[string]interface{}{
		artifactKey: map[string]interface{}{
			"id":              e.Artifact.ID,
			"project_id":      e.Artifact.ProjectID,
			"repository_name": e.Artifact.RepositoryName,
			"digest":          e.Artifact.Digest,
		},
		registrationKey: map[string]interface{}{
			"id":   ar.ID,
			"name": ar.Name,
		},
		tagKey: map[string]interface{}{
			"id":   modelTag.ID,
			"name": e.Repository.Reference,
		},
	}

	// create an execution record
	executionID, err := c.execMgr.Create(ctx, ccrjob.AccelerationJob, ar.ID, trigger, extraAttrs)
	if err != nil {
		logger.Errorf("execution create err: %s", err)
		return err
	}

	_, err = c.teMgr.Create(ctx, ccrjob.AccelerationJob, ar.ID,
		&tag_execution.TagExecution{ExecutionID: executionID, TagID: modelTag.ID})
	if err != nil {
		logger.Errorf("tag execution relation create err: %s", err)
		return err
	}

	var ck string
	if ar.UseInternalAddr {
		ck = configCoreInternalAddr
	} else {
		ck = configRegistryEndpoint
	}

	registryAddr, err := c.config(ck)
	if err != nil {
		return errors.Wrap(err, "accelerate controller: launch acceleration job")
	}

	robotAccount, err := c.makeRobotAccount(ctx, e.Artifact.ProjectID, e.Repository.Namespace, ar)
	if err != nil {
		return errors.Wrap(err, "acceleration controller: launch acceleration job")
	}

	// Set job parameters
	accelerationReq := &rest.AccelerateRequest{
		Registry: &rest.Registry{
			URL:       registryAddr,
			Namespace: os.Getenv("MY_POD_NAMESPACE"),
		},
		Repository: &rest.Repository{
			Namespace:    e.Repository.Namespace,
			RepoFullName: e.Repository.RepoFullName,
			Digest:       e.Artifact.Digest,
			Reference:    e.Repository.Reference,
			MimeType:     e.Artifact.ManifestMediaType,
			Name:         e.Repository.Name,
		},
	}

	arJSON, err := ar.ToJSON()
	if err != nil {
		return errors.Wrap(err, "acceleration controller: launch acceleration job")
	}

	sJSON, err := accelerationReq.ToJSON()
	if err != nil {
		return errors.Wrap(err, "launch acceleration job")
	}

	robotJSON, err := robotAccount.ToJSON()
	if err != nil {
		return errors.Wrap(err, "launch acceleration job")
	}

	params := make(map[string]interface{})
	params[acceleration.JobParamRegistration] = arJSON
	params[acceleration.JobParameterAuthType] = ar.GetAuthType()
	params[acceleration.JobParameterRequest] = sJSON
	params[acceleration.JobParameterRobot] = robotJSON

	j := &task.Job{
		Name: ccrjob.AccelerationJob,
		Metadata: &job.Metadata{
			JobKind: job.KindGeneric,
		},
		Parameters: params,
	}

	// keep the report uuids in array so that when ?| operator support by the FilterRaw method of beego's orm
	// we can list the tasks of the acceleration reports by one SQL
	extraAttrsTask := map[string]interface{}{
		artifactIDKey:  e.Artifact.ID,
		artifactTagKey: e.Repository.Reference,
		robotIDKey:     robotAccount.ID,
	}

	_, err = c.taskMgr.Create(ctx, executionID, j, extraAttrsTask)

	return err
}

// makeRobotAccount creates a robot account based on the arguments for accelerating.
func (c *controller) makeRobotAccount(ctx context.Context, projectID int64, projectName string, acc *accelerator.Accelerator) (*robot.Robot, error) {
	// Use uuid as name to avoid duplicated entries.
	UUID, err := c.uuid()
	if err != nil {
		return nil, errors.Wrap(err, "acceleration controller: make robot account")
	}

	robotReq := &robot.Robot{
		Robot: model.Robot{
			Name:        fmt.Sprintf("%s-%s", acc.Name, UUID),
			Description: "for acceleration",
			ProjectID:   projectID,
		},
		Level: robot.LEVELSYSTEM,
		Permissions: []*robot.Permission{
			{
				Kind:      "project",
				Namespace: projectName,
				Access: []*types.Policy{
					{
						Resource: rbac.ResourceRepository,
						Action:   rbac.ActionPull,
					},
					{
						Resource: rbac.ResourceRepository,
						Action:   rbac.ActionPush,
					},
				},
			},
		},
	}

	rb, pwd, err := c.rc.Create(ctx, robotReq)
	if err != nil {
		return nil, errors.Wrap(err, "acceleration controller: make robot account")
	}

	r, err := c.rc.Get(ctx, rb, &robot.Option{WithPermission: false})
	if err != nil {
		return nil, errors.Wrap(err, "acceleration controller: make robot account")
	}
	r.Secret = pwd
	return r, nil
}

// configGetter is a func template which is used to wrap the config management
// utility methods.
type configGetter func(cfg string) (string, error)

// uuidGenerator is a func template which is for generating UUID.
type uuidGenerator func() (string, error)
