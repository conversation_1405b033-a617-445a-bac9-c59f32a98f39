package acceleration

import (
	"context"
	"testing"

	"github.com/goharbor/harbor/src/controller/artifact"
	pkgart "github.com/goharbor/harbor/src/pkg/artifact"
	"github.com/goharbor/harbor/src/pkg/task"
	testingart "github.com/goharbor/harbor/src/testing/controller/artifact"
	"github.com/goharbor/harbor/src/testing/mock"
	testingtask "github.com/goharbor/harbor/src/testing/pkg/task"
)

func Test_accelerateTaskStatusChange(t *testing.T) {
	type args struct {
		ctx    context.Context
		taskID int64
		status string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "not final",
			args: args{
				ctx:    context.Background(),
				taskID: 1,
				status: "pending",
			},
			wantErr: false,
		},
		{
			name: "success",
			args: func() args {
				testingMgr := &testingtask.Manager{}
				testingMgr.On("Get", mock.Anything, mock.Anything).Return(&task.Task{
					ExtraAttrs: map[string]interface{}{
						robotIDKey:     -1.0,
						artifactIDKey:  1.0,
						artifactTagKey: "1.1",
					},
				}, nil)
				taskMgr = testingMgr

				testingartMgr := &testingart.Controller{}
				testingartMgr.On("Get", mock.Anything, mock.Anything, mock.Anything).Return(&artifact.Artifact{
					Artifact: pkgart.Artifact{
						ProjectID:         1,
						RepositoryName:    "test",
						Digest:            "sha256",
						ManifestMediaType: "application/json",
					},
				}, nil)
				artifactCtl = testingartMgr
				return args{
					ctx:    context.Background(),
					taskID: 1,
					status: "Success",
				}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := accelerateTaskStatusChange(tt.args.ctx, tt.args.taskID, tt.args.status); (err != nil) != tt.wantErr {
				t.Errorf("accelerateTaskStatusChange() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
