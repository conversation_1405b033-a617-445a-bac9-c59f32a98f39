package metadata

import (
	"time"

	"github.com/goharbor/harbor/src/pkg/notifier/event"
	v1 "github.com/goharbor/harbor/src/pkg/scan/rest/v1"

	event2 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/controller/event"
)

type PushArtifactFailedMetadata struct {
	Repo        string
	OccurAt     time.Time
	TagOrDigest string
	Operator    string
	Project     string
	RespCode    int
}

func (p *PushArtifactFailedMetadata) Resolve(event *event.Event) error {
	event.Topic = event2.TopicPushArtifactFailed
	event.Data = p
	return nil
}

type AccelerationMetadata struct {
	Artifact *v1.Artifact
	Status   string
}

func (si *AccelerationMetadata) Resolve(evt *event.Event) error {
	data := &event2.AccelerationEvent{
		EventType: event2.TopicAcceleration,
		Artifact:  si.Artifact,
		OccurAt:   time.Now(),
		Operator:  "auto",
		Status:    si.Status,
	}

	evt.Topic = event2.TopicAcceleration
	evt.Data = data
	return nil
}

type ReplicationExecutionEvent struct {
	Status     string
	PolicyName string
}

func (re *ReplicationExecutionEvent) Resolve(evt *event.Event) error {
	evt.Topic = event2.TopicRelicationExecution
	evt.Data = re
	return nil
}
