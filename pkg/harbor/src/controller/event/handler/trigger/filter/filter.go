package filter

import (
	"context"

	"github.com/goharbor/harbor/src/lib/log"
	"github.com/goharbor/harbor/src/pkg/reg/util"
	daopolicy "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/dao/policy"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/manager/policy"
)

type Filter struct {
	// PolicyMgr is a global notification policy manager
	policyMgr policy.Manager
}

func NewFilter() *Filter {
	return &Filter{
		policyMgr: policy.NewPolicyManager(""),
	}
}

func (f *Filter) MatchTriggerPolicy(ctx context.Context, eventType string, project string, repository string, tags []string) ([]*daopolicy.TriggerPolicy, error) {
	policies, err := f.policyMgr.GetRelatedPolices(ctx, eventType)
	if err != nil {
		log.Errorf("failed to find policy for %s event: %v", eventType, err)
		return nil, err
	}

	var result []*daopolicy.TriggerPolicy
	for _, policy := range policies {
		matched, err := doFilterPolicies(policy, project, repository, tags)
		if err != nil {
			log.Errorf("failed to filter policy for %s event: %v", eventType, err)
			return nil, err
		}

		if matched {
			result = append(result, policy)
		}
	}
	return result, nil
}

// DoFilterPolicies filter policies according to the filters
func doFilterPolicies(policy *daopolicy.TriggerPolicy, project string, repository string, tags []string) (bool, error) {

	for _, filter := range policy.Filters {

		switch filter.Type {
		case daopolicy.FilterTypeProject:
			pattern := filter.Value.(string)
			if len(pattern) > 0 {
				matched, err := util.Match(pattern, project)
				if err != nil {
					return false, err
				}
				if !matched {
					return matched, nil
				}
			}
			break
		case daopolicy.FilterTypeRepository:
			pattern := filter.Value.(string)
			if len(pattern) > 0 {
				matched, err := util.Match(pattern, repository)
				if err != nil {
					return false, err
				}
				if !matched {
					return matched, nil
				}
			}
			break
		case daopolicy.FilterTypeTag:
			pattern := filter.Value.(string)
			if len(pattern) > 0 {
				matched, err := doFilterTag(pattern, tags)
				if err != nil {
					return false, err
				}
				if !matched {
					return matched, nil
				}
			}
			break
		}
	}

	return true, nil
}

func doFilterTag(pattern string, tags []string) (matched bool, err error) {
	for _, tag := range tags {
		matched, err = util.Match(pattern, tag)
		if err != nil {
			return matched, err
		}
		if matched {
			break
		}
	}
	return
}
