package filter

import (
	"testing"

	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/stretchr/testify/suite"
	daopolicy "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/dao/policy"
)

type filterTestSuite struct {
	suite.Suite
	policyMgr *mockPolicyManager
	filter    *Filter
}

func (f *filterTestSuite) SetupTest() {
	f.policyMgr = &mockPolicyManager{}
	f.filter = &Filter{
		policyMgr: f.policyMgr,
	}
}

func (f *filterTestSuite) TestMatchTriggerPolicy() {
	f.policyMgr.On("GetRelatedPolices", mock.Anything, mock.Anything).Return([]*daopolicy.TriggerPolicy{
		{
			ID:   1,
			Name: "policy1",
			Filters: []*daopolicy.TriggerFilter{
				{Type: "project_name", Value: "library"},
				{"repository_name", "cdi-http-import-server"},
				{"tag_name", "v0.30.2-kenc.1"},
			},
		},
		{
			ID:   2,
			Name: "policy2",
			Filters: []*daopolicy.TriggerFilter{
				{Type: "project_name", Value: "library"},
			},
		},
		{
			ID:   3,
			Name: "policy3",
			Filters: []*daopolicy.TriggerFilter{
				{"repository_name", "cdi-http-import-server"},
			},
		},
		{
			ID:   4,
			Name: "policy4",
			Filters: []*daopolicy.TriggerFilter{
				{"tag_name", "v0.30.2-kenc.1"},
			},
		},
		{
			ID:   5,
			Name: "policy5",
			Filters: []*daopolicy.TriggerFilter{
				{Type: "project_name", Value: "library"},
				{"repository_name", "cdi-http-*"},
			},
		},
		{
			ID:   6,
			Name: "policy6",
			Filters: []*daopolicy.TriggerFilter{
				{Type: "project_name", Value: "library"},
				{"repository_name", "receive-*"},
			},
		},
		{
			ID:   7,
			Name: "policy7",
			Filters: []*daopolicy.TriggerFilter{
				{Type: "project_name", Value: "library"},
				{"repository_name", "cdi-http-*"},
				{"tag_name", "v0.30.2-*"},
			},
		},
		{
			ID:   8,
			Name: "policy8",
			Filters: []*daopolicy.TriggerFilter{
				{Type: "project_name", Value: "library"},
				{"repository_name", "cdi-http-*"},
				{"tag_name", "v0.31.2-*"},
			},
		},
	}, nil)

	// project repository tag all matched
	policies, err := f.filter.MatchTriggerPolicy(nil, "", "library", "cdi-http-import-server", []string{"v0.30.2-kenc.1", "v0.31.2-kenc.1"})

	f.Require().Nil(err)
	f.Require().Len(policies, 7)
	f.policyMgr.AssertExpectations(f.T())

	policies, err = f.filter.MatchTriggerPolicy(nil, "", "library", "receive", []string{"v0.30.2-kenc.1"})

	f.Require().Nil(err)
	f.Require().Len(policies, 2)
	f.policyMgr.AssertExpectations(f.T())

	policies, err = f.filter.MatchTriggerPolicy(nil, "", "library", "cdi-http-import-server", []string{"v0.31.2-kenc.1"})

	f.Require().Nil(err)
	f.Require().Len(policies, 4)
	f.policyMgr.AssertExpectations(f.T())
}

func TestExecutionManagerSuite(t *testing.T) {
	suite.Run(t, &filterTestSuite{})
}
