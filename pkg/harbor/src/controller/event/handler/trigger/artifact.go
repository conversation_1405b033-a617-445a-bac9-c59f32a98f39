package trigger

import (
	"context"
	"fmt"
	"strings"

	beegorm "github.com/astaxie/beego/orm"
	"github.com/goharbor/harbor/src/controller/event"
	"github.com/goharbor/harbor/src/controller/event/handler/util"
	"github.com/goharbor/harbor/src/controller/project"
	"github.com/goharbor/harbor/src/lib/log"
	"github.com/goharbor/harbor/src/lib/orm"
	"github.com/goharbor/harbor/src/pkg/project/models"
	"github.com/goharbor/harbor/src/pkg/repository"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/controller/event/handler/trigger/filter"
	triggerutil "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/controller/event/handler/util"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/model"
)

// ArtifactHandler preprocess artifact event data
type ArtifactHandler struct {
	filter *filter.Filter
}

func NewArtifactHandler() *ArtifactHandler {
	return &ArtifactHandler{
		filter: filter.NewFilter(),
	}
}

// Name ...
func (a *ArtifactHandler) Name() string {
	return "ArtifactTrigger"
}

// Handle preprocess artifact event data and then publish hook event
func (a *ArtifactHandler) Handle(ctx context.Context, value interface{}) error {
	switch v := value.(type) {
	case *event.PushArtifactEvent:
		return a.handle(ctx, v.ArtifactEvent)
	case *event.PullArtifactEvent:
		return a.handle(ctx, v.ArtifactEvent)
	case *event.DeleteArtifactEvent:
		return a.handle(ctx, v.ArtifactEvent)
	default:
		log.Errorf("Can not handler this event type! %#v", v)
	}
	return nil
}

// IsStateful ...
func (a *ArtifactHandler) IsStateful() bool {
	return false
}

func (a *ArtifactHandler) handle(ctx context.Context, evt *event.ArtifactEvent) error {

	if len(evt.Tags) == 0 {
		log.Infof("When event type is %s, repository is %s, event tags len is zero ,Skip.", evt.EventType, evt.Repository)
		return nil
	}

	repo := strings.SplitN(evt.Repository, "/", 2)
	if len(repo) < 2 {
		return fmt.Errorf("unable to parse repo from full repository: %s", evt.Repository)
	}
	projectName, repositoryName := repo[0], repo[1]

	policies, err := a.filter.MatchTriggerPolicy(ctx, evt.EventType, projectName, repositoryName, evt.Tags)
	if err != nil {
		log.Errorf("failed to matched trigger policy: %s, %s, %s, %v, error: %v", evt.EventType, projectName, repositoryName, evt.Tags, err)
		return err
	}
	if len(policies) == 0 {
		log.Debugf("cannot find policy for %s evt: %v", evt.EventType, evt)
		return nil
	}

	prj, err := project.Ctl.Get(ctx, evt.Artifact.ProjectID, project.Metadata(true))
	if err != nil {
		log.Errorf("failed to get project: %d, error: %v", evt.Artifact.ProjectID, err)
		return err
	}

	payload, err := a.constructArtifactPayload(evt, prj)
	if err != nil {
		return err
	}

	err = triggerutil.SendTriggerWithPolicies(policies, payload, evt.EventType)
	if err != nil {
		return err
	}

	return nil
}

func (a *ArtifactHandler) constructArtifactPayload(event *event.ArtifactEvent, project *models.Project) (*model.Payload, error) {
	repoName := event.Repository
	if repoName == "" {
		return nil, fmt.Errorf("invalid %s event with empty repo name", event.EventType)
	}

	repoType := models.ProjectPrivate
	if project.IsPublic() {
		repoType = models.ProjectPublic
	}

	imageName := util.GetNameFromImgRepoFullName(repoName)

	payload := &model.Payload{
		Type:    event.EventType,
		OccurAt: event.OccurAt.Unix(),
		EventData: &model.EventData{
			Repository: &model.Repository{
				Name:         imageName,
				Namespace:    project.Name,
				RepoFullName: repoName,
				RepoType:     repoType,
			},
		},
		Operator: event.Operator,
	}

	ctx := orm.NewContext(context.Background(), beegorm.NewOrm())
	repoRecord, err := repository.Mgr.GetByName(ctx, repoName)
	if err != nil {
		log.Errorf("failed to get repository with name %s: %v", repoName, err)
	} else {
		// for the delete repository event, it cannot get the repo info here, just let the creation time be empty.
		payload.EventData.Repository.DateCreated = repoRecord.CreationTime.Unix()
	}

	tags := event.Tags
	for _, tag := range tags {
		reference := tag
		resURL := fmt.Sprintf("%s:%s", repoName, reference)

		resource := &model.Resource{
			Tag:         reference,
			Digest:      event.Artifact.Digest,
			ResourceURL: resURL,
		}
		payload.EventData.Resources = append(payload.EventData.Resources, resource)
	}

	return payload, nil
}
