package acceleration

import (
	"testing"

	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/stretchr/testify/suite"

	daopolicy "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/dao/policy"
)

type filterTestSuite struct {
	suite.Suite
	policyMgr *mockPolicyManager
	filter    *Filter
}

func (f *filterTestSuite) SetupTest() {
	f.policyMgr = &mockPolicyManager{}
	f.filter = &Filter{
		policyMgr: f.policyMgr,
	}
}

func (f *filterTestSuite) TestMatchAcceleratorPolicy() {
	f.policyMgr.On("GetRelatedPolices", mock.Anything).Return([]*daopolicy.AcceleratorPolicy{
		{
			ID:   1,
			Name: "policy1",
			Filters: []*daopolicy.AcceleratorFilter{
				{Type: "project_name", Value: "library"},
				{"repository_name", "cdi-http-import-server"},
				{"tag_name", "v0.30.2-kenc.1"},
			},
		},
		{
			ID:   2,
			Name: "policy2",
			Filters: []*daopolicy.AcceleratorFilter{
				{Type: "project_name", Value: "library"},
			},
		},
		{
			ID:   3,
			Name: "policy3",
			Filters: []*daopolicy.AcceleratorFilter{
				{"repository_name", "cdi-http-import-server"},
			},
		},
		{
			ID:   4,
			Name: "policy4",
			Filters: []*daopolicy.AcceleratorFilter{
				{"tag_name", "v0.30.2-kenc.1"},
			},
		},
		{
			ID:   5,
			Name: "policy5",
			Filters: []*daopolicy.AcceleratorFilter{
				{Type: "project_name", Value: "library"},
				{"repository_name", "cdi-http-*"},
			},
		},
		{
			ID:   6,
			Name: "policy6",
			Filters: []*daopolicy.AcceleratorFilter{
				{Type: "project_name", Value: "library"},
				{"repository_name", "receive-*"},
			},
		},
		{
			ID:   7,
			Name: "policy7",
			Filters: []*daopolicy.AcceleratorFilter{
				{Type: "project_name", Value: "library"},
				{"repository_name", "cdi-http-*"},
				{"tag_name", "v0.30.2-*"},
			},
		},
		{
			ID:   8,
			Name: "policy8",
			Filters: []*daopolicy.AcceleratorFilter{
				{Type: "project_name", Value: "library"},
				{"repository_name", "cdi-http-*"},
				{"tag_name", "v0.31.2-*"},
			},
		},
	}, nil)

	// project repository tag all matched
	matched, err := f.filter.MatchAcceleratorPolicy(nil, "library", "cdi-http-import-server", "v0.31.2-kenc.1")

	f.Require().Nil(err)
	f.Require().True(matched)
	f.policyMgr.AssertExpectations(f.T())

	matched, err = f.filter.MatchAcceleratorPolicy(nil, "library", "receive", "v0.30.2-kenc.1")

	f.Require().Nil(err)
	f.Require().True(matched)
	f.policyMgr.AssertExpectations(f.T())

	matched, err = f.filter.MatchAcceleratorPolicy(nil, "libraryxxxx", "cdi-http-import-serverxxxx", "v0.31.2-kenc.1xxxx")

	f.Require().Nil(err)
	f.Require().False(matched)
	f.policyMgr.AssertExpectations(f.T())
}

func TestExecutionManagerSuite(t *testing.T) {
	suite.Run(t, &filterTestSuite{})
}
