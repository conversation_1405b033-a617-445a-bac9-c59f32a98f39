package acceleration

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/goharbor/harbor/src/controller/event"
	"github.com/goharbor/harbor/src/controller/event/handler/util"
	"github.com/goharbor/harbor/src/controller/project"
	"github.com/goharbor/harbor/src/lib/log"
	"github.com/goharbor/harbor/src/pkg/project/models"
	"github.com/goharbor/harbor/src/pkg/task"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/controller/acceleration"
)

// Handler preprocess artifact event data
type Handler struct {
	filter *Filter
}

func NewHandler() *Handler {
	return &Handler{
		filter: newFilter(),
	}
}

// Name ...
func (a *Handler) Name() string {
	return "ArtifactAcceleration"
}

// IsStateful ...
func (a *Handler) IsStateful() bool {
	return false
}

// Handle preprocess artifact event data and then publish hook event
func (a *<PERSON><PERSON>) Handle(ctx context.Context, value interface{}) error {
	pushArtiEvt, ok := value.(*event.PushArtifactEvent)
	if !ok {
		return errors.New("invalid push artifact event type")
	}

	if len(pushArtiEvt.Tags) == 0 {
		log.Infof("When event type is %s, repository is %s, event tags len is zero ,Skip.", pushArtiEvt.EventType, pushArtiEvt.Repository)
		return nil
	}

	repoName := pushArtiEvt.Repository
	if repoName == "" {
		return fmt.Errorf("invalid %s event with empty repo name", pushArtiEvt.EventType)
	}

	repo := strings.SplitN(repoName, "/", 2)
	if len(repo) < 2 {
		return fmt.Errorf("unable to parse repo from full repository: %s", pushArtiEvt.Repository)
	}

	projectName, repositoryName := repo[0], repo[1]
	reference := pushArtiEvt.Tags[0]

	// TODO 判断annotation内容更为合理
	if strings.HasSuffix(reference, "_accelerate") {
		log.Infof("When event type is %s, repository is %s, event tag suffix has '_accelerate' ,Skip.", pushArtiEvt.EventType, pushArtiEvt.Repository)
		return nil
	}

	matched, err := a.filter.MatchAcceleratorPolicy(ctx, projectName, repositoryName, reference)
	if err != nil {
		log.Errorf("failed to matched accelerator policy: %s, %s, %v, error: %v", projectName, repositoryName, reference, err)
		return err
	}
	if !matched {
		log.Infof("When repository: %s, tag: %s matched: %t ,Skip.", repoName, reference, matched)
		return nil
	}

	prj, err := project.Ctl.Get(ctx, pushArtiEvt.Artifact.ProjectID, project.Metadata(true))
	if err != nil {
		log.Errorf("failed to get project: %d, error: %v", pushArtiEvt.Artifact.ProjectID, err)
		return err
	}

	repoType := models.ProjectPrivate
	if prj.IsPublic() {
		repoType = models.ProjectPublic
	}

	imageName := util.GetNameFromImgRepoFullName(repoName)
	e := &acceleration.Event{
		EventType: pushArtiEvt.EventType,
		Artifact:  pushArtiEvt.Artifact,

		Repository: &acceleration.Repository{
			Name:         imageName,
			Namespace:    prj.Name,
			RepoFullName: repoName,
			RepoType:     repoType,
			Reference:    reference,
		},
	}

	if err := acceleration.Ctl.Acceleration(ctx, e, task.ExecutionTriggerEvent); err != nil {
		log.Errorf("failed acceleration: %v, error: %v", pushArtiEvt, err)
		return err
	}

	return nil
}
