package bcm

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"reflect"
	"strings"
	"time"

	"github.com/goharbor/harbor/src/common/job/models"
	"github.com/goharbor/harbor/src/controller/event"
	"github.com/goharbor/harbor/src/jobservice/job"
	"github.com/goharbor/harbor/src/lib/log"
	"github.com/goharbor/harbor/src/pkg/notification"
	policy_model "github.com/goharbor/harbor/src/pkg/notification/policy/model"
	"github.com/goharbor/harbor/src/pkg/notifier"
	"github.com/goharbor/harbor/src/pkg/notifier/model"

	event2 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/controller/event"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/controller/event/metadata"
)

var (
	_ notifier.NotificationHandler = &Handler{}

	bcmHookAddress string
	instanceID     string
)

func init() {
	bcmEnd, instanceId := os.Getenv("BCM_ENDPOINT"), os.Getenv("CCR_INSTANCE_ID")
	if bcmEnd == "" || instanceId == "" {
		log.Warning("bcm endpoint or ccr instance id is unknown, disable bcm hook")
	}

	bcmURL, err := url.Parse(bcmEnd)
	if err != nil {
		log.Errorf("bcm endpoint is invalid, disable bcm hook")
		return
	}

	if bcmURL.Scheme == "" {
		bcmURL.Scheme = "http"
	}

	bcmURL.Query().Add("instance_id", instanceId)

	bcmHookAddress = bcmURL.String()
	instanceID = instanceId
}

type Handler struct {
}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) Name() string {
	return "bcm"
}

func (h *Handler) IsStateful() bool {
	return false
}

func (a *Handler) Handle(ctx context.Context, value interface{}) error {
	if bcmHookAddress == "" {
		return nil
	}

	hookEvent := &model.HookEvent{
		Target: &policy_model.EventTarget{
			Type:           "http",
			Address:        bcmHookAddress,
			SkipCertVerify: true,
		},
	}

	j := &models.JobData{
		Name: job.WebhookJob,
		Metadata: &models.JobMetadata{
			JobKind: job.KindGeneric,
		},
	}

	jobPayload := &Request{
		ResourceID: instanceID,
		InstanceID: instanceID,
	}

	switch value.(type) {
	case *metadata.PushArtifactFailedMetadata:
		pfm := value.(*metadata.PushArtifactFailedMetadata)
		hookEvent.EventType = event2.TopicPushArtifactFailed
		hookEvent.Payload = &model.Payload{
			Type:     event2.TopicPushArtifactFailed,
			OccurAt:  pfm.OccurAt.Unix(),
			Operator: pfm.Operator,
			EventData: &model.EventData{
				Repository: &model.Repository{
					Name:         pfm.Repo,
					Namespace:    pfm.Project,
					RepoFullName: pfm.TagOrDigest,
				},
				Custom: map[string]string{
					"respCode": fmt.Sprintf("%d", pfm.RespCode),
				},
			},
		}
		jobPayload.EventType = "ImagePushFailed"
		jobPayload.Timestamp = pfm.OccurAt.Format(time.RFC3339)
		jobPayload.ProjectName = pfm.Project
		jobPayload.RepositoryName = pfm.Repo
		jobPayload.RetCode = fmt.Sprintf("%d", pfm.RespCode)
	case *event.PushArtifactEvent:
		pae := value.(*event.PushArtifactEvent)
		hookEvent.EventType = pae.EventType
		hookEvent.Payload = &model.Payload{
			Type:     pae.EventType,
			OccurAt:  pae.OccurAt.Unix(),
			Operator: pae.Operator,
			EventData: &model.EventData{
				Repository: &model.Repository{
					Name:         pae.Repository,
					Namespace:    pae.Repository,
					RepoFullName: pae.Repository,
				},
			},
		}

		jobPayload.EventType = "ImagePushSucceeded"
		jobPayload.Timestamp = pae.OccurAt.Format(time.RFC3339)
		jobPayload.ProjectName = strings.Split(pae.Repository, "/")[0]
		jobPayload.RepositoryName = pae.Repository
		jobPayload.Sha256 = pae.Artifact.Digest
		if len(pae.Tags) != 0 {
			jobPayload.TagName = pae.Tags[0]
		}
	case *event.ScanImageEvent:
		si := value.(*event.ScanImageEvent)
		switch si.EventType {
		case event.TopicScanningCompleted:
			jobPayload.EventType = "ImageScanSucceeded"
		case event.TopicScanningFailed:
			jobPayload.EventType = "ImageScanFailed"
		default:
			return fmt.Errorf("not supported scan hook status %s", si.EventType)
		}

		hookEvent.Payload = &model.Payload{
			Type:     si.EventType,
			OccurAt:  time.Now().Unix(),
			Operator: "auto",
			EventData: &model.EventData{
				Repository: &model.Repository{
					Name:         si.Artifact.Repository,
					Namespace:    si.Artifact.Repository,
					RepoFullName: si.Artifact.Repository,
				},
			},
		}

		jobPayload.Timestamp = time.Now().Format(time.RFC3339)
		jobPayload.ProjectName = strings.Split(si.Artifact.Repository, "/")[0]
		jobPayload.RepositoryName = si.Artifact.Repository
		jobPayload.TagName = si.Artifact.Tag
		jobPayload.Sha256 = si.Artifact.Digest
	case *metadata.ReplicationExecutionEvent:
		rm := value.(*metadata.ReplicationExecutionEvent)
		if !strings.HasPrefix(rm.PolicyName, "CCR-SYNC-") {
			return nil
		}

		switch job.Status(rm.Status) {
		case job.SuccessStatus:
			hookEvent.EventType = event2.TopicReplicationSuccess
			jobPayload.EventType = "ImageSynchronizationSucceeded"
		case job.ErrorStatus, job.StoppedStatus:
			hookEvent.EventType = event2.TopicReplicationFailed
			jobPayload.EventType = "ImageSynchronizationFailed"
		default:
			log.Errorf("unsupported replication job status %s", rm.Status)
			return fmt.Errorf("not supported scan hook status %s", rm.Status)
		}

		hookEvent.Payload = &model.Payload{
			Type:      hookEvent.EventType,
			OccurAt:   time.Now().Unix(),
			Operator:  "auto",
			EventData: &model.EventData{},
		}

		jobPayload.Timestamp = time.Now().Format(time.RFC3339)
		jobPayload.PolicyName = strings.TrimPrefix(rm.PolicyName, "CCR-SYNC-")
	case *event2.AccelerationEvent:
		am := value.(*event2.AccelerationEvent)
		switch job.Status(am.Status) {
		case job.SuccessStatus:
			hookEvent.EventType = event2.TopicAccelerationSuccess
			jobPayload.EventType = "ImageTransitionSucceeded"
		case job.ErrorStatus, job.StoppedStatus:
			hookEvent.EventType = event2.TopicAccelerationFailed
			jobPayload.EventType = "ImageTransitionFailed"
		default:
			return fmt.Errorf("not supported scan hook status %s", am.Status)
		}

		hookEvent.Payload = &model.Payload{
			Type:     hookEvent.EventType,
			OccurAt:  time.Now().Unix(),
			Operator: "auto",
			EventData: &model.EventData{
				Repository: &model.Repository{
					Name:         am.Artifact.Repository,
					Namespace:    am.Artifact.Repository,
					RepoFullName: am.Artifact.Repository,
				},
			},
		}
		jobPayload.Timestamp = time.Now().Format(time.RFC3339)
		jobPayload.ProjectName = strings.Split(am.Artifact.Repository, "/")[0]
		jobPayload.RepositoryName = am.Artifact.Repository
		jobPayload.TagName = am.Artifact.Tag
		jobPayload.Sha256 = am.Artifact.Digest
	default:
		log.Warningf("unsupported event type %s in bcm", reflect.TypeOf(value))
		return nil
	}

	payload, err := json.Marshal(jobPayload)
	if err != nil {
		log.Errorf("marshal payload failed: %v", err)
		return err
	}

	j.Parameters = map[string]interface{}{
		"payload": string(payload),
		"address": hookEvent.Target.Address,
		// Users can define a auth header in http statement in notification(webhook) policy.
		// So it will be sent in header in http request.
		"auth_header":      hookEvent.Target.AuthHeader,
		"skip_cert_verify": hookEvent.Target.SkipCertVerify,
	}

	return notification.HookManager.StartHook(ctx, hookEvent, j)
}
