package api

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/astaxie/beego"
	beegocontext "github.com/astaxie/beego/context"
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/chartmuseum/storage"
	commonapi "github.com/goharbor/harbor/src/common/api"
	harborapi "github.com/goharbor/harbor/src/core/api"
	"github.com/goharbor/harbor/src/lib/errors"
	projectmodels "github.com/goharbor/harbor/src/pkg/project/models"
	testingsecurity "github.com/goharbor/harbor/src/testing/common/security"
	projectcontroller "github.com/goharbor/harbor/src/testing/controller/project"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/dao"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/models"
	testingapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/harbor/src/core/api"
	testingdao "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/harbor/src/pkg/chart/dao"
)

func chartContent() []byte {
	c := "H4sIFAAAAAAA/ykAK2FIUjBjSE02THk5NWIzVjBkUzVpWlM5Nk9WVjZNV2xq" +
		"YW5keVRRbz1IZWxtAOxZbW8bN/LPa36K+cv/ookvola27BYL3AvD6d0Flwcj" +
		"SlIURRFQ3FktYy654YNs1cl99gPJ1dNaOrto4qCB+ULaJYfDmeHMb4Zch9YN" +
		"TitmHJ2zWj74Ei3Lsux4NIr/WZZ1/7Ph8Q8PhqPDw+HwcBies+HB8Gj0ALIv" +
		"Ik2neeuYeZD96bW6yv1FGmvEWzRWaJXD7ICwplm+DunwmGakQMuNaFzsO4F/" +
		"oayBB4eBUhv4t5+gUejQEsVqzCE4FHHzBnNgTSMFZ2EmmS24ZnRIM/K11b5v" +
		"bYvxP2PSo/1iAHBD/B8eHWXd+B8Nh/fxfxdtD55gybx0kJwgBnVwCkr24HUl" +
		"LAgLDH45ef6sX2pTM+ewgFJIDARPkEtmEGbMCDaRaMFpmCA0zFosQCinYa59" +
		"4Fg3kjm0lBCDERZOtVcuhyEhomZTzAmAwUZb4bSZ56CmQl0SgMZLeaal4PMc" +
		"npYvtDszaFE5ArAHL2dojCjCuhVC5AOOTeGi0hahaDUTaTiB1grhKIFAnEOv" +
		"18pw5qUcIzfobA6//hYRbbFEJCu9lNc6iUUzExxPOI8qRcnGDXJRCrRwUaGr" +
		"0ACDlg5YIgRbaS+LYC9ukDksCLRPOTjjMTI6UUq7iKHRuKwowl/Qp8OOALAV" +
		"bQ5Xn+L81xVCEBl0uW1WYOYt0kj7tASlHVh0wFTRyhLNZzw+BpYYCQtTVGiC" +
		"xOCtUNPIeGGb5V4TgJQSgo0aXZx0pAt9Y+TeCDc/1crhpVtKXdp/Gu2bHA6y" +
		"LAsW3k7GWcMmQgon0Ca7AxRGN4vnPpw8exafDbLipZLzV1q7fwiJdm4d1mt2" +
		"Nl6d2BdaBYJu9xuLJodhK0q0X1ghZblT6a1D8/QsOKs2LocfM0KEmhq0UShU" +
		"ITSKHEomLW7dprDS+TKTUqEH7XzKJbN2FQ3XCZ20fcajmYPQPQJQaeviysEA" +
		"4SVPvt/HS1Y3EqnUnMk4DtAwV7XEgTy85jBo3wEmjJ+jKvJlByw86EXc292M" +
		"10jPFmYBcDKFVtyfPtgYbVtY9Z207Sau9NlbSLltVWLQam84rnz/ZwRvPZNy" +
		"Dga5rmtURfRwp8HGAJ0vQWI5O7q+0yCRzRBcgEAWIJBrZbnQPonFKx2iKKJl" +
		"FZZBQxNeMmk1CBWCx6INoiqe/o2zwZ9AK0A1E0arGpWzcCFcBVI4J1ufW4jy" +
		"GKznVVj+uVAi7DsNMTrXHgoNF0xtaLI2zaukrUuRqaXUF0JNI3cpVCBhxXtv" +
		"43gdFlDI0Vpm5o+j/gZrHbVH4N7IOUwMi7YpHRr4fmXq72nLtBarPeKNj+FS" +
		"t+811hHThwc/Phetih882tvOIMw7bTmTQk23RlQt1KuUVWzIKAA1u1zryKLn" +
		"MTNFd3r25o0TUvwe4+8MDUflQvpJ7rnXkj2P6++mJEoXOEaJ3GmT0MxpGTAx" +
		"RfWvvxHCylIo4eZx+Gvn+V0t1n/L7Dx4V6Fs0Fjqms9XCt5U/x0eduu/0dHo" +
		"4L7+u4t2dTXYJz9dNhH0OqVChCxK9gefPpGrq36ASqEQerE8DIQ96K+GIorS" +
		"dJUQAB3o23SsWC+Y4GNIrYrD8WF8FPXYl6W4hF6/By2vANKfPhESRTtNNQhb" +
		"rhDKjDl88EyG8qoI9VwUmpKfMfGO9C6sEBSwMEHOvEWwusa182pStRQoCwuh" +
		"iI0Yhm15JSw8nMyjGZ68GAfaUOcEqH1EydMSDMqA74kJ18oxoVqQb6skBxdC" +
		"ylDd+VALxxwShG+l3W7VRRG1sqwol4bsVp8Lg+0cv42xpV3y+f8oeP732+/m" +
		"SsalCRIT+irZJ81dyLnR+QeFa4xQroTed7b/ne11eKVFb+9b2x83PG5tK0Ns" +
		"tPcXYRvjdra+kagkm6DctaWRZLWfXT3WTZye2+MJfAznIck4Qu9vPei96/3x" +
		"4NF1rVUSz+4QLw0m+SqUNbXVIIqcw9VVqGOkLzqq0LRYZ9C22fBZy5CueUer" +
		"2cny7BXGWNPQzVp2eUl0dbVlykf44LXDjqbXudRMsSkW/ck8MVp4yrg9+Wyz" +
		"1CKV/29bdVXsb10/HXqu2SeFNd0us1DWhTpxU+L14Nnmo13A3n6226nM+qn1" +
		"xS7Q2SSj7ZmwA/sPN1VdoRh9tIuRWtdtLc4XLHvtQ+9WDK7F8tfOq3+V1qn/" +
		"Wht/3qvAm+7/j0Zb6r/7+/87aRv3/0NyLlSRQ4uUpEbHCuZYvrzKuYZqa6Ee" +
		"wg5aAE33BRFKNsjlMjl8BCVUEY6oozAxVFarO5UAgptRT8NAWqHRZnW9kW5c" +
		"ttCHgUQfWjrVpXuIyrlmcflhtNNcyxxen561fUnRlmiB+Lv0uZ70Onp97f29" +
		"qW2P/zaBfCYYuCn+Rz8cdOL/eHhwdB//d9Fule1TnbMbKFry2+DFtqrjzyFH" +
		"Io53aDu0WLtvTRPWL2CXyzn9C6vlLv7faoUR45+Gwl9MlTb4Jda4Mf8fHnW/" +
		"/x0eZ/fxfxdtD86Yc2jS16XkA3BRoYKJF7IQagoN4+dsinb5RdD6JuZgsBVK" +
		"CVOpJ1Azxyuhpo/BoGROzDB+SljrZ6oge6BwGgMPHjYGS3GJRQrd/3tE4aWS" +
		"c9AqzgwiQYMmXldTQp+M342dNkj2oD3Vvj0dQyGMJXQq3CD+JvEJnfxuBvF3" +
		"0VFNB+Fn8WpnarBiNGH83Dfxm6Yl+9ReNGSfTtg52aeuDs/aiCnZ/w/Zg7fM" +
		"CO0tPH3ykyW0Mfo9ckeoKJANErnR7wmdWa4LHHwjEHHf7tt9+0bbfwMAAP//" +
		"jvpHFgAoAAA="

	content, err := base64.StdEncoding.DecodeString(c)
	if err != nil {
		panic("invalid chart metadata")
	}

	return content
}

func provContent() []byte {
	c :=
		"-----BEGIN PGP SIGNED MESSAGE-----\n" +
			"wsBcBAEBCgAQBQJdy0ReCRCEO7+YH8GHYgAAfhUIADx3pHHLLINv0MFkiEYpX/Kd\n" +
			"nvHFBNps7hXqSocsg0a9Fi1LRAc3OpVh3knjPfHNGOy8+xOdhbqpdnB+5ty8YopI\n" +
			"mYMWp6cP/Mwpkt7/gP1ecWFMevicbaFH5AmJCBihBaKJE4R1IX49/wTIaLKiWkv2\n" +
			"cR64bmZruQPSW83UTNULtdD7kuTZXeAdTMjAK0NECsCz9/eK5AFggP4CDf7r2zNi\n" +
			"hZsNrzloIlBZlGGns6mUOTO42J/+JojnOLIhI3Psd0HBD2bTlsm/rSfty4yZUs7D\n" +
			"qtgooNdohoyGSzR5oapd7fEvauRQswJxOA0m0V+u9/eyLR0+JcYB8Udi1prnWf8=\n" +
			"=aHfz\n" +
			"-----END PGP SIGNATURE-----\n" +
			"name: mychart\n" +
			"type: application\n" +
			"version: 0.1.0\n"

	return []byte(c)
}

func newTestChartRepositoryAPI() *chartRepositoryAPI {
	ctx := beegocontext.NewContext()
	req, _ := http.NewRequest(http.MethodGet, "http://test.com", nil)
	ctx.Reset(httptest.NewRecorder(), req)
	return &chartRepositoryAPI{
		BaseController: harborapi.BaseController{
			BaseAPI: commonapi.BaseAPI{
				Controller: beego.Controller{
					Ctx: ctx,
				},
			},
		},
	}
}

func Test_chartRepositoryAPI_Prepare(t *testing.T) {
	req, _ := http.NewRequest(http.MethodGet, "http://localhost/chartrepo/index.yaml", nil)
	ctx := beegocontext.NewContext()
	ctx.Reset(httptest.NewRecorder(), req)

	cra := &chartRepositoryAPI{
		BaseController: harborapi.BaseController{
			BaseAPI: commonapi.BaseAPI{
				Controller: beego.Controller{
					Ctx: ctx,
				},
			},
		},
	}

	cra.Prepare()
	assert.NotNil(t, cra.storageBackend)

	prjCtl := &projectcontroller.Controller{}
	prjCtl.On("Exists", mock.Anything, "test").Return(true, nil)
	prjCtl.On("Exists", mock.Anything, "error").Return(false, fmt.Errorf("error"))
	cra.ProjectCtl = prjCtl

	cra.storageBackend = nil
	req, _ = http.NewRequest(http.MethodGet, "http://localhost/api/chartrepo/test/charts", nil)
	ctx.Reset(httptest.NewRecorder(), req)
	ctx.Input.SetParam(namespaceParam, "test")
	cra.Prepare()
	assert.NotNil(t, cra.storageBackend)

	cra.storageBackend = nil
	req, _ = http.NewRequest(http.MethodGet, "http://localhost/api/chartrepo/test/charts", nil)
	ctx.Reset(httptest.NewRecorder(), req)
	ctx.Input.SetParam(namespaceParam, "error")
	cra.Prepare()
	assert.Nil(t, cra.storageBackend)
	assert.Equal(t, 500, ctx.ResponseWriter.Status)
}

func Test_chartRepositoryAPI_requireNamespace(t *testing.T) {
	ctx := beegocontext.NewContext()
	ctx.Reset(httptest.NewRecorder(), &http.Request{})

	cra := &chartRepositoryAPI{
		BaseController: harborapi.BaseController{
			BaseAPI: commonapi.BaseAPI{
				Controller: beego.Controller{
					Ctx: ctx,
				},
			},
		},
	}

	existed := cra.requireNamespace("")
	assert.False(t, existed)
	assert.Equal(t, 400, ctx.ResponseWriter.Status)

	prjCtl := &projectcontroller.Controller{}
	prjCtl.On("Exists", mock.Anything, "test").Return(true, nil)
	prjCtl.On("Exists", mock.Anything, "error").Return(false, fmt.Errorf("error"))
	prjCtl.On("Exists", mock.Anything, "notfound").Return(false, nil)
	cra.ProjectCtl = prjCtl

	ctx.Reset(httptest.NewRecorder(), &http.Request{})
	ctx.Input.SetParam(namespaceParam, "test")
	existed = cra.requireNamespace("test")
	assert.True(t, existed)

	securityCtx := &testingsecurity.Context{}
	securityCtx.On("IsSysAdmin").Return(true)
	ctx.Reset(httptest.NewRecorder(), &http.Request{})
	cra.SecurityCtx = securityCtx
	existed = cra.requireNamespace("notfound")
	assert.False(t, existed)
	assert.Equal(t, 404, ctx.ResponseWriter.Status)

	securityCtx = &testingsecurity.Context{}
	securityCtx.On("IsSysAdmin").Return(false)
	securityCtx.On("IsAuthenticated").Return(false)
	ctx.Reset(httptest.NewRecorder(), &http.Request{})
	cra.SecurityCtx = securityCtx
	existed = cra.requireNamespace("notfound")
	assert.False(t, existed)
	assert.Equal(t, 401, ctx.ResponseWriter.Status)

	ctx.Reset(httptest.NewRecorder(), &http.Request{})
	existed = cra.requireNamespace("error")
	assert.False(t, existed)
	assert.Equal(t, 500, ctx.ResponseWriter.Status)
}

func Test_chartRepositoryAPI_GetHealthStatus(t *testing.T) {
	ctx := beegocontext.NewContext()
	cra := &chartRepositoryAPI{
		BaseController: harborapi.BaseController{
			BaseAPI: commonapi.BaseAPI{
				Controller: beego.Controller{
					Ctx: ctx,
				},
			},
		},
	}

	securityCtx := &testingsecurity.Context{}
	securityCtx.On("IsAuthenticated").Return(false)
	ctx.Reset(httptest.NewRecorder(), &http.Request{})
	cra.SecurityCtx = securityCtx
	cra.GetHealthStatus()
	assert.Equal(t, 401, ctx.ResponseWriter.Status)

	securityCtx = &testingsecurity.Context{}
	securityCtx.On("IsAuthenticated").Return(true)
	securityCtx.On("IsSysAdmin").Return(false)
	securityCtx.On("GetUsername").Return("test")
	ctx.Reset(httptest.NewRecorder(), &http.Request{})
	cra.SecurityCtx = securityCtx
	cra.GetHealthStatus()
	assert.Equal(t, 403, ctx.ResponseWriter.Status)

	securityCtx = &testingsecurity.Context{}
	securityCtx.On("IsAuthenticated").Return(true)
	securityCtx.On("IsSysAdmin").Return(true)
	securityCtx.On("GetUsername").Return("test")
	ctx.Reset(httptest.NewRecorder(), &http.Request{})
	cra.SecurityCtx = securityCtx
	cra.Data = make(map[interface{}]interface{})
	cra.GetHealthStatus()
	assert.True(t, ctx.ResponseWriter.Started)
}

func Test_chartRepositoryAPI_GetIndexByRepo(t *testing.T) {
	ctx := beegocontext.NewContext()
	req, _ := http.NewRequest(http.MethodGet, "http://test.com", nil)
	ctx.Reset(httptest.NewRecorder(), req)
	cra := &chartRepositoryAPI{
		BaseController: harborapi.BaseController{
			BaseAPI: commonapi.BaseAPI{
				Controller: beego.Controller{
					Ctx: ctx,
				},
			},
		},
		largeThreshold: 100,
	}

	chatDao := testingdao.NewMockDao(gomock.NewController(t))
	chatDao.EXPECT().CountByProjects(gomock.Any(), gomock.Any()).Return(int64(1), nil)
	chatDao.EXPECT().GetChartVersionsByProjects(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.Chart{
		{
			ProjectName: "test",
			Name:        "chart-1",
			Version:     "v1",
			Size:        10,
			Digest:      "simple",
			Metadata: `{"name":"mychart-1005","version":"0.1.0",` +
				`"description":"A Helm chart for Kubernetes","apiVersion":"v2","appVersion":"1.16.0","type":"application"}`,
		}}, nil)

	prjCtl := &projectcontroller.Controller{}
	prjCtl.On("Get", mock.Anything, mock.Anything).Times(2).Return(&projectmodels.Project{ProjectID: 1}, nil)

	securityCtx := &testingsecurity.Context{}
	securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Times(2).Return(true)

	cra.SecurityCtx = securityCtx
	cra.ProjectCtl = prjCtl
	cra.chartDao = chatDao
	cra.Data = make(map[interface{}]interface{})

	cra.GetIndexByRepo()

	assert.Equal(t, 200, ctx.ResponseWriter.Status)

	ctx = beegocontext.NewContext()
	req, _ = http.NewRequest(http.MethodGet, "http://test.com", nil)
	resp := httptest.NewRecorder()
	ctx.Reset(resp, req)
	cra = &chartRepositoryAPI{
		BaseController: harborapi.BaseController{
			BaseAPI: commonapi.BaseAPI{
				Controller: beego.Controller{
					Ctx: ctx,
				},
			},
		},
		largeThreshold: 1,
	}

	chatDao = testingdao.NewMockDao(gomock.NewController(t))
	chatDao.EXPECT().CountByProjects(gomock.Any(), gomock.Any()).Return(int64(2), nil)
	chatDao.EXPECT().GetChartVersionsByProjects(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return([]*models.Chart{
		{
			ProjectName: "test",
			Name:        "chart-1",
			Version:     "v1",
			Size:        10,
			Digest:      "simple",
			Metadata: `{"name":"mychart-1005","version":"0.1.0",` +
				`"description":"A Helm chart for Kubernetes","apiVersion":"v2","appVersion":"1.16.0","type":"application"}`,
		}}, nil)
	cra.chartDao = chatDao
	cra.SecurityCtx = securityCtx
	cra.ProjectCtl = prjCtl

	cra.GetIndexByRepo()
	assert.Equal(t, 200, ctx.ResponseWriter.Status)
}

func Test_chartRepositoryAPI_GetIndex(t *testing.T) {
	ctx := beegocontext.NewContext()
	req, _ := http.NewRequest(http.MethodGet, "http://test.com", nil)
	ctx.Reset(httptest.NewRecorder(), req)
	cra := &chartRepositoryAPI{
		BaseController: harborapi.BaseController{
			BaseAPI: commonapi.BaseAPI{
				Controller: beego.Controller{
					Ctx: ctx,
				},
			},
		},
		largeThreshold: 100,
	}

	chatDao := testingdao.NewMockDao(gomock.NewController(t))
	chatDao.EXPECT().CountByProjects(gomock.Any(), gomock.Any()).Return(int64(1), nil)
	chatDao.EXPECT().GetChartVersionsByProjects(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.Chart{
		{
			ProjectName: "test",
			Name:        "chart-1",
			Version:     "v1",
			Size:        10,
			Digest:      "simple",
			Metadata: `{"name":"mychart-1005","version":"0.1.0",` +
				`"description":"A Helm chart for Kubernetes","apiVersion":"v2","appVersion":"1.16.0","type":"application"}`,
		}}, nil)

	prjCtl := &projectcontroller.Controller{}
	prjCtl.On("List", mock.Anything, mock.Anything, mock.Anything).Return([]*projectmodels.Project{{Name: "test"}}, nil)

	securityCtx := &testingsecurity.Context{}
	securityCtx.On("IsAuthenticated").Return(true)
	securityCtx.On("IsSysAdmin").Return(true)

	cra.SecurityCtx = securityCtx
	cra.ProjectCtl = prjCtl
	cra.chartDao = chatDao
	cra.Data = make(map[interface{}]interface{})

	cra.GetIndex()

	assert.Equal(t, 200, ctx.ResponseWriter.Status)
}

func Test_chartRepositoryAPI_DownloadChart(t *testing.T) {
	type field struct {
		cra *chartRepositoryAPI
	}

	tests := []struct {
		name       string
		field      field
		statusCode int
	}{
		{
			name: "正常返回",
			field: func() field {
				cra := newTestChartRepositoryAPI()
				prjCtl := &projectcontroller.Controller{}
				prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

				securityCtx := &testingsecurity.Context{}
				securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
				securityCtx.On("GetUsername").Return("test")

				storageBackend := testingapi.NewMockStorage(gomock.NewController(t))
				storageBackend.EXPECT().GetObject("test/test-v1.tgz").Return(storage.Object{
					Path:         "test/test-v1.tgz",
					Content:      []byte("test"),
					LastModified: time.Now(),
				}, nil)

				cra.SecurityCtx = securityCtx
				cra.ProjectCtl = prjCtl
				cra.storageBackend = storageBackend
				cra.Data = make(map[interface{}]interface{})

				cra.namespace = "test"
				cra.Ctx.Input.SetParam(namespaceParam, "test")
				cra.Ctx.Input.SetParam(filenameParam, "test-v1.tgz")
				return field{
					cra: cra,
				}
			}(),
			statusCode: 200,
		},
		{
			name: "返回404",
			field: func() field {
				cra := newTestChartRepositoryAPI()
				prjCtl := &projectcontroller.Controller{}
				prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

				securityCtx := &testingsecurity.Context{}
				securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
				securityCtx.On("GetUsername").Return("test")

				storageBackend := testingapi.NewMockStorage(gomock.NewController(t))
				storageBackend.EXPECT().GetObject("test/test-v1.tgz").Return(storage.Object{
					Path:         "test/test-v1.tgz",
					Content:      []byte("test"),
					LastModified: time.Now(),
				}, &bce.BceServiceError{
					StatusCode: 404,
				})

				cra.SecurityCtx = securityCtx
				cra.ProjectCtl = prjCtl
				cra.storageBackend = storageBackend
				cra.Data = make(map[interface{}]interface{})

				cra.namespace = "test"
				cra.Ctx.Input.SetParam(namespaceParam, "test")
				cra.Ctx.Input.SetParam(filenameParam, "test-v1.tgz")
				return field{
					cra: cra,
				}
			}(),
			statusCode: 404,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.field.cra.DownloadChart()
			if tt.field.cra.Ctx.ResponseWriter.Status != tt.statusCode {
				t.Errorf("Download failed, expected status %v, but actual %v", tt.statusCode, tt.field.cra.Ctx.ResponseWriter.Status)
			}
		})
	}
}

func Test_chartRepositoryAPI_ListCharts(t *testing.T) {
	ctx := beegocontext.NewContext()
	req, _ := http.NewRequest(http.MethodGet, "http://test.com", nil)
	ctx.Reset(httptest.NewRecorder(), req)
	cra := &chartRepositoryAPI{
		BaseController: harborapi.BaseController{
			BaseAPI: commonapi.BaseAPI{
				Controller: beego.Controller{
					Ctx: ctx,
				},
			},
		},
	}

	prjCtl := &projectcontroller.Controller{}
	prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

	securityCtx := &testingsecurity.Context{}
	securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
	//securityCtx.On("GetUsername").Return("test")

	chartDao := testingdao.NewMockDao(gomock.NewController(t))
	chartDao.EXPECT().GetChartSummaryByProject(gomock.Any(), gomock.Any()).Return([]*dao.ChartSummary{
		{
			Name:         "test",
			CreateTime:   time.Now(),
			LastModified: time.Now(),
			Total:        "10",
			Metadata: `{"name":"mychart-1005","version":"0.1.0",` +
				`"description":"A Helm chart for Kubernetes","apiVersion":"v2","appVersion":"1.16.0","type":"application"}`,
		},
	}, nil)

	cra.SecurityCtx = securityCtx
	cra.ProjectCtl = prjCtl
	cra.chartDao = chartDao
	cra.Data = make(map[interface{}]interface{})

	cra.namespace = "test"
	ctx.Input.SetParam(namespaceParam, "test")
	ctx.Input.SetParam(filenameParam, "test-v1.tgz")

	cra.ListCharts()

	assert.True(t, ctx.ResponseWriter.Started)
}

func Test_chartRepositoryAPI_ListChartVersions(t *testing.T) {
	cra := newTestChartRepositoryAPI()

	prjCtl := &projectcontroller.Controller{}
	prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

	securityCtx := &testingsecurity.Context{}
	securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
	//securityCtx.On("GetUsername").Return("test")

	chartDao := testingdao.NewMockDao(gomock.NewController(t))
	chartDao.EXPECT().ChartVersionCount(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), nil)
	chartDao.EXPECT().GetChartVersions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.Chart{
		{
			ProjectName: "test",
			Name:        "chart-1",
			Version:     "v1",
			Size:        10,
			Digest:      "simple",
			Metadata: `{"name":"mychart-1005","version":"0.1.0",` +
				`"description":"A Helm chart for Kubernetes","apiVersion":"v2","appVersion":"1.16.0","type":"application"}`,
		}}, nil)

	cra.SecurityCtx = securityCtx
	cra.ProjectCtl = prjCtl
	cra.chartDao = chartDao
	cra.Data = make(map[interface{}]interface{})

	cra.namespace = "test"
	cra.Ctx.Input.SetParam(namespaceParam, "test")
	cra.Ctx.Input.SetParam(nameParam, "test")

	cra.ListChartVersions()

	assert.True(t, cra.Ctx.ResponseWriter.Started)
}

func Test_chartRepositoryAPI_GetChartVersion(t *testing.T) {
	cra := newTestChartRepositoryAPI()

	prjCtl := &projectcontroller.Controller{}
	prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

	securityCtx := &testingsecurity.Context{}
	securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
	//securityCtx.On("GetUsername").Return("test")

	chartDao := testingdao.NewMockDao(gomock.NewController(t))
	chartDao.EXPECT().GetChartVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Chart{
		ProjectName: "test",
		Name:        "test",
		Version:     "0.1.0",
		Size:        10,
		Digest:      "simple",
		Metadata: `{"name":"test","version":"0.1.0",` +
			`"description":"A Helm chart for Kubernetes","apiVersion":"v2","appVersion":"1.16.0","type":"application"}`,
	}, nil)

	storageBackend := testingapi.NewMockStorage(gomock.NewController(t))
	storageBackend.EXPECT().GetObject("test/test-0.1.0.tgz").Return(storage.Object{
		Path:         "test/test-0.1.0.tgz",
		Content:      chartContent(),
		LastModified: time.Now(),
	}, nil)
	storageBackend.EXPECT().GetObject("test/test-0.1.0.tgz.prov").Return(storage.Object{
		Path:         "test/test-0.1.0.tgz",
		Content:      []byte("test"),
		LastModified: time.Now(),
	}, nil)

	cra.SecurityCtx = securityCtx
	cra.ProjectCtl = prjCtl
	cra.chartDao = chartDao
	cra.storageBackend = storageBackend
	cra.Data = make(map[interface{}]interface{})

	cra.namespace = "test"
	cra.Ctx.Input.SetParam(versionParam, "0.1.0")
	cra.Ctx.Input.SetParam(nameParam, "test")

	cra.GetChartVersion()

	assert.True(t, cra.Ctx.ResponseWriter.Started)
}

func Test_chartRepositoryAPI_DeleteChartVersion(t *testing.T) {
	type field struct {
		cra *chartRepositoryAPI
	}

	tests := []struct {
		name       string
		field      field
		statusCode int
	}{
		{
			name: "正常返回",
			field: func() field {
				cra := newTestChartRepositoryAPI()
				prjCtl := &projectcontroller.Controller{}
				prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

				securityCtx := &testingsecurity.Context{}
				securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
				securityCtx.On("GetUsername").Return("test")

				chartDao := testingdao.NewMockDao(gomock.NewController(t))
				chartDao.EXPECT().PreDeleteChartVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				chartDao.EXPECT().DeleteChartVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				storageBackend := testingapi.NewMockStorage(gomock.NewController(t))
				storageBackend.EXPECT().DeleteObject("test/test-0.1.0.tgz").Return(nil)
				storageBackend.EXPECT().DeleteObject("test/test-0.1.0.tgz.prov").Return(nil)

				cra.SecurityCtx = securityCtx
				cra.ProjectCtl = prjCtl
				cra.chartDao = chartDao
				cra.storageBackend = storageBackend
				cra.Data = make(map[interface{}]interface{})

				cra.namespace = "test"
				cra.Ctx.Input.SetParam(versionParam, "0.1.0")
				cra.Ctx.Input.SetParam(nameParam, "test")
				return field{
					cra: cra,
				}
			}(),
			statusCode: 0,
		},
		{
			name: "bos文件不存在",
			field: func() field {
				cra := newTestChartRepositoryAPI()
				prjCtl := &projectcontroller.Controller{}
				prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

				securityCtx := &testingsecurity.Context{}
				securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
				securityCtx.On("GetUsername").Return("test")

				chartDao := testingdao.NewMockDao(gomock.NewController(t))
				chartDao.EXPECT().PreDeleteChartVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				chartDao.EXPECT().DeleteChartVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				storageBackend := testingapi.NewMockStorage(gomock.NewController(t))
				storageBackend.EXPECT().DeleteObject("test/test-0.1.0.tgz").Return(&bce.BceServiceError{StatusCode: 404})
				storageBackend.EXPECT().DeleteObject("test/test-0.1.0.tgz.prov").Return(nil)

				cra.SecurityCtx = securityCtx
				cra.ProjectCtl = prjCtl
				cra.chartDao = chartDao
				cra.storageBackend = storageBackend
				cra.Data = make(map[interface{}]interface{})

				cra.namespace = "test"
				cra.Ctx.Input.SetParam(versionParam, "0.1.0")
				cra.Ctx.Input.SetParam(nameParam, "test")
				return field{
					cra: cra,
				}
			}(),
			statusCode: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.field.cra.DeleteChartVersion()
			if tt.field.cra.Ctx.ResponseWriter.Status != tt.statusCode {
				t.Errorf("Download failed, expected status %v, but actual %v", tt.statusCode, tt.field.cra.Ctx.ResponseWriter.Status)
			}
		})
	}
}

func Test_chartRepositoryAPI_UploadChartProvFile(t *testing.T) {
	cra := newTestChartRepositoryAPI()

	prjCtl := &projectcontroller.Controller{}
	prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

	securityCtx := &testingsecurity.Context{}
	securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
	securityCtx.On("GetUsername").Return("test")

	chartDao := testingdao.NewMockDao(gomock.NewController(t))
	chartDao.EXPECT().GetChartVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Chart{
		ProjectName: "test",
		Name:        "test",
		Version:     "0.1.0",
		Size:        10,
		Digest:      "simple",
		Metadata: `{"name":"test","version":"0.1.0",` +
			`"description":"A Helm chart for Kubernetes","apiVersion":"v2","appVersion":"1.16.0","type":"application"}`,
	}, nil)

	storageBackend := testingapi.NewMockStorage(gomock.NewController(t))
	storageBackend.EXPECT().PutObject(gomock.Any(), gomock.Any()).Return(nil)

	cra.SecurityCtx = securityCtx
	cra.ProjectCtl = prjCtl
	cra.chartDao = chartDao
	cra.storageBackend = storageBackend
	cra.Data = make(map[interface{}]interface{})

	cra.namespace = "test"
	cra.Ctx.Input.SetParam(versionParam, "0.1.0")
	cra.Ctx.Input.SetParam(nameParam, "test")
	cra.Ctx.Request.Body = io.NopCloser(bytes.NewReader(provContent()))

	cra.UploadChartProvFile()
	assert.Equal(t, 201, cra.Ctx.ResponseWriter.Status)
	assert.True(t, cra.Ctx.ResponseWriter.Started)
}

func Test_chartRepositoryAPI_DeleteChart(t *testing.T) {
	type field struct {
		cra *chartRepositoryAPI
	}

	tests := []struct {
		name       string
		field      field
		statusCode int
	}{
		{
			name: "正常返回",
			field: func() field {
				cra := newTestChartRepositoryAPI()

				prjCtl := &projectcontroller.Controller{}
				prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

				securityCtx := &testingsecurity.Context{}
				securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
				securityCtx.On("GetUsername").Return("test")

				chartDao := testingdao.NewMockDao(gomock.NewController(t))
				chartDao.EXPECT().ChartVersionCount(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), nil)
				chartDao.EXPECT().GetChartVersions(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.Chart{
					{
						ProjectName: "test",
						Name:        "chart-1",
						Version:     "v1",
						Size:        10,
						Digest:      "simple",
						Metadata: `{"name":"mychart-1005","version":"0.1.0",` +
							`"description":"A Helm chart for Kubernetes","apiVersion":"v2","appVersion":"1.16.0","type":"application"}`,
					}}, nil)
				chartDao.EXPECT().PreDeleteChart(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				chartDao.EXPECT().DeleteChart(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				storageBackend := testingapi.NewMockStorage(gomock.NewController(t))
				storageBackend.EXPECT().DeleteMultiObject(gomock.Any()).Return(nil)

				cra.SecurityCtx = securityCtx
				cra.ProjectCtl = prjCtl
				cra.chartDao = chartDao
				cra.storageBackend = storageBackend
				cra.Data = make(map[interface{}]interface{})

				cra.namespace = "test"
				cra.Ctx.Input.SetParam(versionParam, "0.1.0")
				cra.Ctx.Input.SetParam(nameParam, "test")
				cra.Ctx.Request.Body = io.NopCloser(bytes.NewReader(provContent()))
				return field{
					cra: cra,
				}
			}(),
			statusCode: 0,
		},
		{
			name: "chart不存在",
			field: func() field {
				cra := newTestChartRepositoryAPI()

				prjCtl := &projectcontroller.Controller{}
				prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

				securityCtx := &testingsecurity.Context{}
				securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
				securityCtx.On("GetUsername").Return("test")

				chartDao := testingdao.NewMockDao(gomock.NewController(t))
				chartDao.EXPECT().ChartVersionCount(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), nil)

				cra.SecurityCtx = securityCtx
				cra.ProjectCtl = prjCtl
				cra.chartDao = chartDao
				cra.Data = make(map[interface{}]interface{})

				cra.namespace = "test"
				cra.Ctx.Input.SetParam(versionParam, "0.1.0")
				cra.Ctx.Input.SetParam(nameParam, "test")
				cra.Ctx.Request.Body = io.NopCloser(bytes.NewReader(provContent()))
				return field{
					cra: cra,
				}
			}(),
			statusCode: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.field.cra.DeleteChart()
			if tt.field.cra.Ctx.ResponseWriter.Status != tt.statusCode {
				t.Errorf("Download failed, expected status %v, but actual %v", tt.statusCode, tt.field.cra.Ctx.ResponseWriter.Status)
			}
		})
	}
}

func Test_chartRepositoryAPI_UploadChartVersion(t *testing.T) {
	type fields struct {
		cra *chartRepositoryAPI
	}
	tests := []struct {
		name       string
		fields     fields
		expectCode int
	}{
		// TODO: Add test cases.
		{
			name: "allow overwrite",
			fields: func() fields {
				cra := newTestChartRepositoryAPI()
				cra.allowOverwrite = true
				prjCtl := &projectcontroller.Controller{}
				prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

				securityCtx := &testingsecurity.Context{}
				securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
				securityCtx.On("GetUsername").Return("test")

				chartDao := testingdao.NewMockDao(gomock.NewController(t))
				chartDao.EXPECT().CreateOrUpdate(gomock.Any(), gomock.Any()).Return(int64(1), nil)
				chartDao.EXPECT().UpdateLastModified(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				storageBackend := testingapi.NewMockStorage(gomock.NewController(t))
				storageBackend.EXPECT().PutObject(gomock.Any(), gomock.Any()).Return(nil)

				cra.SecurityCtx = securityCtx
				cra.ProjectCtl = prjCtl
				cra.chartDao = chartDao
				cra.storageBackend = storageBackend
				cra.Data = make(map[interface{}]interface{})

				cra.namespace = "test"
				cra.Ctx.Input.SetParam(versionParam, "0.1.0")
				cra.Ctx.Input.SetParam(nameParam, "test")
				cra.Ctx.Request.Body = io.NopCloser(bytes.NewReader(chartContent()))
				return fields{
					cra: cra,
				}
			}(),
			expectCode: 201,
		},
		{
			name: "disallow overwrite and success",
			fields: func() fields {
				cra := newTestChartRepositoryAPI()
				cra.allowOverwrite = false
				prjCtl := &projectcontroller.Controller{}
				prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

				securityCtx := &testingsecurity.Context{}
				securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
				securityCtx.On("GetUsername").Return("test")

				chartDao := testingdao.NewMockDao(gomock.NewController(t))
				chartDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(int64(1), nil)
				chartDao.EXPECT().UpdateLastModified(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				storageBackend := testingapi.NewMockStorage(gomock.NewController(t))
				storageBackend.EXPECT().PutObject(gomock.Any(), gomock.Any()).Return(nil)

				cra.SecurityCtx = securityCtx
				cra.ProjectCtl = prjCtl
				cra.chartDao = chartDao
				cra.storageBackend = storageBackend
				cra.Data = make(map[interface{}]interface{})

				cra.namespace = "test"
				cra.Ctx.Input.SetParam(versionParam, "0.1.0")
				cra.Ctx.Input.SetParam(nameParam, "test")
				cra.Ctx.Request.Body = io.NopCloser(bytes.NewReader(chartContent()))
				return fields{
					cra: cra,
				}
			}(),
			expectCode: 201,
		},
		{
			name: "disallow overwrite but force",
			fields: func() fields {
				ctx := beegocontext.NewContext()
				req, _ := http.NewRequest(http.MethodGet, "http://test.com?force=true", nil)
				ctx.Reset(httptest.NewRecorder(), req)
				cra := &chartRepositoryAPI{
					BaseController: harborapi.BaseController{
						BaseAPI: commonapi.BaseAPI{
							Controller: beego.Controller{
								Ctx: ctx,
							},
						},
					},
				}
				cra.allowOverwrite = false
				prjCtl := &projectcontroller.Controller{}
				prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

				securityCtx := &testingsecurity.Context{}
				securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
				securityCtx.On("GetUsername").Return("test")

				chartDao := testingdao.NewMockDao(gomock.NewController(t))
				chartDao.EXPECT().CreateOrUpdate(gomock.Any(), gomock.Any()).Return(int64(1), nil)
				chartDao.EXPECT().UpdateLastModified(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				storageBackend := testingapi.NewMockStorage(gomock.NewController(t))
				storageBackend.EXPECT().PutObject(gomock.Any(), gomock.Any()).Return(nil)

				cra.SecurityCtx = securityCtx
				cra.ProjectCtl = prjCtl
				cra.chartDao = chartDao
				cra.storageBackend = storageBackend
				cra.Data = make(map[interface{}]interface{})

				cra.namespace = "test"
				cra.Ctx.Input.SetParam(versionParam, "0.1.0")
				cra.Ctx.Input.SetParam(nameParam, "test")
				cra.Ctx.Request.Body = io.NopCloser(bytes.NewReader(chartContent()))
				return fields{
					cra: cra,
				}
			}(),
			expectCode: 201,
		},
		{
			name: "disallow overwrite and has 409 error",
			fields: func() fields {
				cra := newTestChartRepositoryAPI()
				cra.allowOverwrite = false
				prjCtl := &projectcontroller.Controller{}
				prjCtl.On("Get", mock.Anything, mock.Anything).Return(&projectmodels.Project{ProjectID: 1}, nil)

				securityCtx := &testingsecurity.Context{}
				securityCtx.On("Can", mock.Anything, mock.Anything, mock.Anything).Return(true)
				securityCtx.On("GetUsername").Return("test")

				chartDao := testingdao.NewMockDao(gomock.NewController(t))
				chartDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(int64(1), errors.ConflictError(fmt.Errorf("error")))

				cra.SecurityCtx = securityCtx
				cra.ProjectCtl = prjCtl
				cra.chartDao = chartDao
				cra.Data = make(map[interface{}]interface{})

				cra.namespace = "test"
				cra.Ctx.Input.SetParam(versionParam, "0.1.0")
				cra.Ctx.Input.SetParam(nameParam, "test")
				cra.Ctx.Request.Body = io.NopCloser(bytes.NewReader(chartContent()))
				return fields{
					cra: cra,
				}
			}(),
			expectCode: 409,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.fields.cra.UploadChartVersion()
			if tt.fields.cra.Ctx.ResponseWriter.Status != tt.expectCode {
				t.Errorf("expect code is: %d but actual code is: %d", tt.expectCode, tt.fields.cra.Ctx.ResponseWriter.Status)
			}
		})
	}
}
