package api

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/astaxie/beego"
	beegocontext "github.com/astaxie/beego/context"
	commonapi "github.com/goharbor/harbor/src/common/api"
	"github.com/goharbor/harbor/src/common/security"
	harborapi "github.com/goharbor/harbor/src/core/api"
	testingsecurity "github.com/goharbor/harbor/src/testing/common/security"
	"github.com/stretchr/testify/assert"
)

func Test_chartLabelAPI_Prepare(t *testing.T) {
	ctx := beegocontext.NewContext()
	ctx.Reset(httptest.NewRecorder(), &http.Request{})
	securityCtx := &testingsecurity.Context{}
	ctx.Request = ctx.Request.WithContext(security.NewContext(context.Background(), securityCtx))

	cla := &chartLabelAPI{
		BaseController: harborapi.BaseController{
			BaseAPI: commonapi.BaseAPI{
				Controller: beego.Controller{
					Ctx: ctx,
				},
			},
		},
	}

	securityCtx.On("IsAuthenticated").Return(false)
	cla.Prepare()
	assert.Equal(t, 401, cla.Ctx.ResponseWriter.Status)

	ctx.Reset(httptest.NewRecorder(), &http.Request{})
	ctx.Input.SetParam(namespaceParam, "test")
	ctx.Input.SetParam(nameParam, "test")
	ctx.Input.SetParam(versionParam, "v1")
	securityCtx = &testingsecurity.Context{}
	securityCtx.On("IsAuthenticated").Return(true)
	ctx.Request = ctx.Request.WithContext(security.NewContext(context.Background(), securityCtx))
	cla.Prepare()
	assert.Equal(t, "test/test:v1", cla.chartFullName)
}
