package api

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_NewStorage(t *testing.T) {
	recovered := false
	func() {
		defer func() {
			if x := recover(); x != nil {
				recovered = true
			}
		}()
		baiduAccessID = "ttt"
		NewBosStorage()
	}()

	assert.True(t, recovered)

	baiduAccessID = ""
	s := NewBosStorage()
	assert.NotNil(t, s)
}

func Test_NewStorageWithConfig(t *testing.T) {
	recovered := false
	func() {
		defer func() {
			if x := recover(); x != nil {
				recovered = true
			}
		}()
		NewStorageWithConfig("", "sk", "end", "bk", "prefix")
	}()

	assert.True(t, recovered)

	s := NewStorageWithConfig("ak", "sk", "end", "bk", "prefix")
	assert.NotNil(t, s)
}

func Test_Storage_ListObjects(t *testing.T) {
	s := NewBosStorage()

	_, err := s.ListObjects("test")
	assert.Error(t, err)
}

func Test_Storage_DeleteMultiObject(t *testing.T) {
	s := NewBosStorage()

	err := s.DeleteMultiObject([]string{"test"})
	assert.Error(t, err)
}
