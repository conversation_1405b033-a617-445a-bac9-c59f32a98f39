package api

import (
	"context"
	"fmt"
	"os"
	"strings"

	projectcontroller "github.com/goharbor/harbor/src/controller/project"
	"github.com/goharbor/harbor/src/lib"
	"github.com/goharbor/harbor/src/lib/config"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/dao"
)

var (
	storageBucket     string
	storagePrefix     string
	storageEndpoint   string
	baiduAccessID     string
	baiduAccessSecret string

	allowChartOverwrite bool
)

func Init(ctx context.Context) {
	if config.WithChartMuseum() {
		return
	}
	// bos ak, sk 注入
	if os.Getenv("BAIDU_CLOUD_ACCESS_KEY_ID") == "" ||
		os.Getenv("STORAGE_BAIDU_ENDPOINT") == "" ||
		os.Getenv("STORAGE_BAIDU_BUCKET") == "" ||
		os.Getenv("BAIDU_CLOUD_ACCESS_KEY_SECRET") == "" {
		panic("Storage Parameter is wrong")
	}

	storageBucket = os.Getenv("STORAGE_BAIDU_BUCKET")
	storagePrefix = os.Getenv("STORAGE_BAIDU_PREFIX")
	storageEndpoint = os.Getenv("STORAGE_BAIDU_ENDPOINT")
	baiduAccessID = os.Getenv("BAIDU_CLOUD_ACCESS_KEY_ID")
	baiduAccessSecret = os.Getenv("BAIDU_CLOUD_ACCESS_KEY_SECRET")

	allowChartOverwrite = lib.ToBool(os.Getenv("ALLOW_OVERWRITE"))
	// sync storage chart
	if lib.ToBool(os.Getenv("SYNC_STORAGE_CHART")) {
		err := syncChartFromStorage(ctx, dao.New(), NewBosStorage(), projectcontroller.Ctl)
		if err != nil {
			panic(fmt.Sprintf("sync storage to chart failed: %s", err))
		}
	}
}

func StorageBucket() string {
	return storageBucket
}

func StoragePrefix() string {
	return strings.TrimPrefix(storagePrefix, "/")
}

func StorageEndpoint() string {
	return storageEndpoint
}

func BaiduAccessID() string {
	return baiduAccessID
}

func BaiduAccessSecret() string {
	return baiduAccessSecret
}
