// Code generated by go-swagger; DO NOT EDIT.

package immutable

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// GetImmutableRuleReader is a Reader for the GetImmutableRule structure.
type GetImmutableRuleReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *GetImmutableRuleReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewGetImmutableRuleOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 401:
		result := NewGetImmutableRuleUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewGetImmutableRuleInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewGetImmutableRuleOK creates a GetImmutableRuleOK with default headers values
func NewGetImmutableRuleOK() *GetImmutableRuleOK {
	return &GetImmutableRuleOK{}
}

/* GetImmutableRuleOK describes a response with status code 200, with default header values.

Return matched immutable information.
*/
type GetImmutableRuleOK struct {
	Payload *model.ModelsImmutableRule
}

func (o *GetImmutableRuleOK) Error() string {
	return fmt.Sprintf("[GET /immutable/rule/{immutableId}][%d] getImmutableRuleOK  %+v", 200, o.Payload)
}
func (o *GetImmutableRuleOK) GetPayload() *model.ModelsImmutableRule {
	return o.Payload
}

func (o *GetImmutableRuleOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(model.ModelsImmutableRule)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetImmutableRuleUnauthorized creates a GetImmutableRuleUnauthorized with default headers values
func NewGetImmutableRuleUnauthorized() *GetImmutableRuleUnauthorized {
	return &GetImmutableRuleUnauthorized{}
}

/* GetImmutableRuleUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type GetImmutableRuleUnauthorized struct {
	String string

	Payload string
}

func (o *GetImmutableRuleUnauthorized) Error() string {
	return fmt.Sprintf("[GET /immutable/rule/{immutableId}][%d] getImmutableRuleUnauthorized  %+v", 401, o.Payload)
}
func (o *GetImmutableRuleUnauthorized) GetPayload() string {
	return o.Payload
}

func (o *GetImmutableRuleUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header string
	hdrString := response.GetHeader("string")

	if hdrString != "" {
		o.String = hdrString
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetImmutableRuleInternalServerError creates a GetImmutableRuleInternalServerError with default headers values
func NewGetImmutableRuleInternalServerError() *GetImmutableRuleInternalServerError {
	return &GetImmutableRuleInternalServerError{}
}

/* GetImmutableRuleInternalServerError describes a response with status code 500, with default header values.

Internal Server Error
*/
type GetImmutableRuleInternalServerError struct {
	String string

	Payload string
}

func (o *GetImmutableRuleInternalServerError) Error() string {
	return fmt.Sprintf("[GET /immutable/rule/{immutableId}][%d] getImmutableRuleInternalServerError  %+v", 500, o.Payload)
}
func (o *GetImmutableRuleInternalServerError) GetPayload() string {
	return o.Payload
}

func (o *GetImmutableRuleInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header string
	hdrString := response.GetHeader("string")

	if hdrString != "" {
		o.String = hdrString
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
