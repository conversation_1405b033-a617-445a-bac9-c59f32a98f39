// Code generated by go-swagger; DO NOT EDIT.

package immutable

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new immutable API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for immutable API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

//go:generate mockery --name ClientService --structname MockImmutableClientService

// ClientService is the interface for Client methods
type ClientService interface {
	GetImmutableRule(params *GetImmutableRuleParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetImmutableRuleOK, error)

	ListImmutableRule(params *ListImmutableRuleParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ListImmutableRuleOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
  GetImmutableRule returns specific immutable rule detail information

  This endpoint returns specific immutable rule information by immutable ID.
*/
func (a *Client) GetImmutableRule(params *GetImmutableRuleParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetImmutableRuleOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetImmutableRuleParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "GetImmutableRule",
		Method:             "GET",
		PathPattern:        "/immutable/rule/{immutableId}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http"},
		Params:             params,
		Reader:             &GetImmutableRuleReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetImmutableRuleOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for GetImmutableRule: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  ListImmutableRule returns immutable rule list

  This endpoint returns immutable rule list
*/
func (a *Client) ListImmutableRule(params *ListImmutableRuleParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ListImmutableRuleOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListImmutableRuleParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "ListImmutableRule",
		Method:             "GET",
		PathPattern:        "/immutable/rule",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http"},
		Params:             params,
		Reader:             &ListImmutableRuleReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListImmutableRuleOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for ListImmutableRule: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
