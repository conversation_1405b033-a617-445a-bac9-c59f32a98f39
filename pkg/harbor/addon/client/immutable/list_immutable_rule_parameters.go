// Code generated by go-swagger; DO NOT EDIT.

package immutable

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewListImmutableRuleParams creates a new ListImmutableRuleParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListImmutableRuleParams() *ListImmutableRuleParams {
	return &ListImmutableRuleParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListImmutableRuleParamsWithTimeout creates a new ListImmutableRuleParams object
// with the ability to set a timeout on a request.
func NewListImmutableRuleParamsWithTimeout(timeout time.Duration) *ListImmutableRuleParams {
	return &ListImmutableRuleParams{
		timeout: timeout,
	}
}

// NewListImmutableRuleParamsWithContext creates a new ListImmutableRuleParams object
// with the ability to set a context for a request.
func NewListImmutableRuleParamsWithContext(ctx context.Context) *ListImmutableRuleParams {
	return &ListImmutableRuleParams{
		Context: ctx,
	}
}

// NewListImmutableRuleParamsWithHTTPClient creates a new ListImmutableRuleParams object
// with the ability to set a custom HTTPClient for a request.
func NewListImmutableRuleParamsWithHTTPClient(client *http.Client) *ListImmutableRuleParams {
	return &ListImmutableRuleParams{
		HTTPClient: client,
	}
}

/* ListImmutableRuleParams contains all the parameters to send to the API endpoint
   for the list immutable rule operation.

   Typically these are written to a http.Request.
*/
type ListImmutableRuleParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* Page.

	   The page number

	   Default: 1
	*/
	Page *int64

	/* PageSize.

	   The size of per page

	   Default: 10
	*/
	PageSize *int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list immutable rule params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListImmutableRuleParams) WithDefaults() *ListImmutableRuleParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list immutable rule params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListImmutableRuleParams) SetDefaults() {
	var (
		pageDefault = int64(1)

		pageSizeDefault = int64(10)
	)

	val := ListImmutableRuleParams{
		Page:     &pageDefault,
		PageSize: &pageSizeDefault,
	}

	val.timeout = o.timeout
	val.Context = o.Context
	val.HTTPClient = o.HTTPClient
	*o = val
}

// WithTimeout adds the timeout to the list immutable rule params
func (o *ListImmutableRuleParams) WithTimeout(timeout time.Duration) *ListImmutableRuleParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list immutable rule params
func (o *ListImmutableRuleParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list immutable rule params
func (o *ListImmutableRuleParams) WithContext(ctx context.Context) *ListImmutableRuleParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list immutable rule params
func (o *ListImmutableRuleParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list immutable rule params
func (o *ListImmutableRuleParams) WithHTTPClient(client *http.Client) *ListImmutableRuleParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list immutable rule params
func (o *ListImmutableRuleParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the list immutable rule params
func (o *ListImmutableRuleParams) WithXRequestID(xRequestID *string) *ListImmutableRuleParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the list immutable rule params
func (o *ListImmutableRuleParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithPage adds the page to the list immutable rule params
func (o *ListImmutableRuleParams) WithPage(page *int64) *ListImmutableRuleParams {
	o.SetPage(page)
	return o
}

// SetPage adds the page to the list immutable rule params
func (o *ListImmutableRuleParams) SetPage(page *int64) {
	o.Page = page
}

// WithPageSize adds the pageSize to the list immutable rule params
func (o *ListImmutableRuleParams) WithPageSize(pageSize *int64) *ListImmutableRuleParams {
	o.SetPageSize(pageSize)
	return o
}

// SetPageSize adds the pageSize to the list immutable rule params
func (o *ListImmutableRuleParams) SetPageSize(pageSize *int64) {
	o.PageSize = pageSize
}

// WriteToRequest writes these params to a swagger request
func (o *ListImmutableRuleParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	if o.Page != nil {

		// query param page
		var qrPage int64

		if o.Page != nil {
			qrPage = *o.Page
		}
		qPage := swag.FormatInt64(qrPage)
		if qPage != "" {

			if err := r.SetQueryParam("page", qPage); err != nil {
				return err
			}
		}
	}

	if o.PageSize != nil {

		// query param page_size
		var qrPageSize int64

		if o.PageSize != nil {
			qrPageSize = *o.PageSize
		}
		qPageSize := swag.FormatInt64(qrPageSize)
		if qPageSize != "" {

			if err := r.SetQueryParam("page_size", qPageSize); err != nil {
				return err
			}
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
