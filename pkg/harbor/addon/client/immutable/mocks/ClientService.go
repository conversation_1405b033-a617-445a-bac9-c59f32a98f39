// Code generated by mockery v2.28.1. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	immutable "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/immutable"

	runtime "github.com/go-openapi/runtime"
)

// MockImmutableClientService is an autogenerated mock type for the ClientService type
type MockImmutableClientService struct {
	mock.Mock
}

// GetImmutableRule provides a mock function with given fields: params, authInfo, opts
func (_m *MockImmutableClientService) GetImmutableRule(params *immutable.GetImmutableRuleParams, authInfo runtime.ClientAuthInfoWriter, opts ...immutable.ClientOption) (*immutable.GetImmutableRuleOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *immutable.GetImmutableRuleOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*immutable.GetImmutableRuleParams, runtime.ClientAuthInfoWriter, ...immutable.ClientOption) (*immutable.GetImmutableRuleOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*immutable.GetImmutableRuleParams, runtime.ClientAuthInfoWriter, ...immutable.ClientOption) *immutable.GetImmutableRuleOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*immutable.GetImmutableRuleOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*immutable.GetImmutableRuleParams, runtime.ClientAuthInfoWriter, ...immutable.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListImmutableRule provides a mock function with given fields: params, authInfo, opts
func (_m *MockImmutableClientService) ListImmutableRule(params *immutable.ListImmutableRuleParams, authInfo runtime.ClientAuthInfoWriter, opts ...immutable.ClientOption) (*immutable.ListImmutableRuleOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *immutable.ListImmutableRuleOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*immutable.ListImmutableRuleParams, runtime.ClientAuthInfoWriter, ...immutable.ClientOption) (*immutable.ListImmutableRuleOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*immutable.ListImmutableRuleParams, runtime.ClientAuthInfoWriter, ...immutable.ClientOption) *immutable.ListImmutableRuleOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*immutable.ListImmutableRuleOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*immutable.ListImmutableRuleParams, runtime.ClientAuthInfoWriter, ...immutable.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetTransport provides a mock function with given fields: transport
func (_m *MockImmutableClientService) SetTransport(transport runtime.ClientTransport) {
	_m.Called(transport)
}

type mockConstructorTestingTNewMockImmutableClientService interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockImmutableClientService creates a new instance of MockImmutableClientService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockImmutableClientService(t mockConstructorTestingTNewMockImmutableClientService) *MockImmutableClientService {
	mock := &MockImmutableClientService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
