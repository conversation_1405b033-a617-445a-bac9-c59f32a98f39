// Code generated by go-swagger; DO NOT EDIT.

package project

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// GetProjectOverviewReader is a Reader for the GetProjectOverview structure.
type GetProjectOverviewReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *GetProjectOverviewReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewGetProjectOverviewOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 401:
		result := NewGetProjectOverviewUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewGetProjectOverviewInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewGetProjectOverviewOK creates a GetProjectOverviewOK with default headers values
func NewGetProjectOverviewOK() *GetProjectOverviewOK {
	return &GetProjectOverviewOK{}
}

/* GetProjectOverviewOK describes a response with status code 200, with default header values.

Return matched project information.
*/
type GetProjectOverviewOK struct {
	Payload *model.ModelsProjectOverview
}

func (o *GetProjectOverviewOK) Error() string {
	return fmt.Sprintf("[GET /projects/{project_name}/overview][%d] getProjectOverviewOK  %+v", 200, o.Payload)
}
func (o *GetProjectOverviewOK) GetPayload() *model.ModelsProjectOverview {
	return o.Payload
}

func (o *GetProjectOverviewOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(model.ModelsProjectOverview)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetProjectOverviewUnauthorized creates a GetProjectOverviewUnauthorized with default headers values
func NewGetProjectOverviewUnauthorized() *GetProjectOverviewUnauthorized {
	return &GetProjectOverviewUnauthorized{}
}

/* GetProjectOverviewUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type GetProjectOverviewUnauthorized struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload string
}

func (o *GetProjectOverviewUnauthorized) Error() string {
	return fmt.Sprintf("[GET /projects/{project_name}/overview][%d] getProjectOverviewUnauthorized  %+v", 401, o.Payload)
}
func (o *GetProjectOverviewUnauthorized) GetPayload() string {
	return o.Payload
}

func (o *GetProjectOverviewUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetProjectOverviewInternalServerError creates a GetProjectOverviewInternalServerError with default headers values
func NewGetProjectOverviewInternalServerError() *GetProjectOverviewInternalServerError {
	return &GetProjectOverviewInternalServerError{}
}

/* GetProjectOverviewInternalServerError describes a response with status code 500, with default header values.

Internal Server Error
*/
type GetProjectOverviewInternalServerError struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload string
}

func (o *GetProjectOverviewInternalServerError) Error() string {
	return fmt.Sprintf("[GET /projects/{project_name}/overview][%d] getProjectOverviewInternalServerError  %+v", 500, o.Payload)
}
func (o *GetProjectOverviewInternalServerError) GetPayload() string {
	return o.Payload
}

func (o *GetProjectOverviewInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
