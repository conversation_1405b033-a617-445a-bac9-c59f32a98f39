// Code generated by mockery v2.28.1. DO NOT EDIT.

package mocks

import (
	runtime "github.com/go-openapi/runtime"
	mock "github.com/stretchr/testify/mock"
	project "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/project"
)

// MockProjectClientService is an autogenerated mock type for the ClientService type
type MockProjectClientService struct {
	mock.Mock
}

// GetProject provides a mock function with given fields: params, authInfo, opts
func (_m *MockProjectClientService) GetProject(params *project.GetProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...project.ClientOption) (*project.GetProjectOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *project.GetProjectOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*project.GetProjectParams, runtime.ClientAuthInfoWriter, ...project.ClientOption) (*project.GetProjectOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*project.GetProjectParams, runtime.ClientAuthInfoWriter, ...project.ClientOption) *project.GetProjectOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*project.GetProjectOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*project.GetProjectParams, runtime.ClientAuthInfoWriter, ...project.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProjectOverview provides a mock function with given fields: params, authInfo, opts
func (_m *MockProjectClientService) GetProjectOverview(params *project.GetProjectOverviewParams, authInfo runtime.ClientAuthInfoWriter, opts ...project.ClientOption) (*project.GetProjectOverviewOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *project.GetProjectOverviewOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*project.GetProjectOverviewParams, runtime.ClientAuthInfoWriter, ...project.ClientOption) (*project.GetProjectOverviewOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*project.GetProjectOverviewParams, runtime.ClientAuthInfoWriter, ...project.ClientOption) *project.GetProjectOverviewOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*project.GetProjectOverviewOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*project.GetProjectOverviewParams, runtime.ClientAuthInfoWriter, ...project.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListProjects provides a mock function with given fields: params, authInfo, opts
func (_m *MockProjectClientService) ListProjects(params *project.ListProjectsParams, authInfo runtime.ClientAuthInfoWriter, opts ...project.ClientOption) (*project.ListProjectsOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *project.ListProjectsOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*project.ListProjectsParams, runtime.ClientAuthInfoWriter, ...project.ClientOption) (*project.ListProjectsOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*project.ListProjectsParams, runtime.ClientAuthInfoWriter, ...project.ClientOption) *project.ListProjectsOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*project.ListProjectsOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*project.ListProjectsParams, runtime.ClientAuthInfoWriter, ...project.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetTransport provides a mock function with given fields: transport
func (_m *MockProjectClientService) SetTransport(transport runtime.ClientTransport) {
	_m.Called(transport)
}

type mockConstructorTestingTNewMockProjectClientService interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockProjectClientService creates a new instance of MockProjectClientService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockProjectClientService(t mockConstructorTestingTNewMockProjectClientService) *MockProjectClientService {
	mock := &MockProjectClientService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
