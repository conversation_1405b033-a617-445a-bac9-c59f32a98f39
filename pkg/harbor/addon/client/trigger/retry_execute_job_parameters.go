// Code generated by go-swagger; DO NOT EDIT.

package trigger

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewRetryExecuteJobParams creates a new RetryExecuteJobParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewRetryExecuteJobParams() *RetryExecuteJobParams {
	return &RetryExecuteJobParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewRetryExecuteJobParamsWithTimeout creates a new RetryExecuteJobParams object
// with the ability to set a timeout on a request.
func NewRetryExecuteJobParamsWithTimeout(timeout time.Duration) *RetryExecuteJobParams {
	return &RetryExecuteJobParams{
		timeout: timeout,
	}
}

// NewRetryExecuteJobParamsWithContext creates a new RetryExecuteJobParams object
// with the ability to set a context for a request.
func NewRetryExecuteJobParamsWithContext(ctx context.Context) *RetryExecuteJobParams {
	return &RetryExecuteJobParams{
		Context: ctx,
	}
}

// NewRetryExecuteJobParamsWithHTTPClient creates a new RetryExecuteJobParams object
// with the ability to set a custom HTTPClient for a request.
func NewRetryExecuteJobParamsWithHTTPClient(client *http.Client) *RetryExecuteJobParams {
	return &RetryExecuteJobParams{
		HTTPClient: client,
	}
}

/* RetryExecuteJobParams contains all the parameters to send to the API endpoint
   for the retry execute job operation.

   Typically these are written to a http.Request.
*/
type RetryExecuteJobParams struct {

	/* XRequestID.

	   The request id
	*/
	XRequestID *string

	/* JobID.

	   The ID of the job
	*/
	JobID string

	/* PolicyID.

	   The ID of the policy
	*/
	PolicyID string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the retry execute job params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *RetryExecuteJobParams) WithDefaults() *RetryExecuteJobParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the retry execute job params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *RetryExecuteJobParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the retry execute job params
func (o *RetryExecuteJobParams) WithTimeout(timeout time.Duration) *RetryExecuteJobParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the retry execute job params
func (o *RetryExecuteJobParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the retry execute job params
func (o *RetryExecuteJobParams) WithContext(ctx context.Context) *RetryExecuteJobParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the retry execute job params
func (o *RetryExecuteJobParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the retry execute job params
func (o *RetryExecuteJobParams) WithHTTPClient(client *http.Client) *RetryExecuteJobParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the retry execute job params
func (o *RetryExecuteJobParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the retry execute job params
func (o *RetryExecuteJobParams) WithXRequestID(xRequestID *string) *RetryExecuteJobParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the retry execute job params
func (o *RetryExecuteJobParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithJobID adds the jobID to the retry execute job params
func (o *RetryExecuteJobParams) WithJobID(jobID string) *RetryExecuteJobParams {
	o.SetJobID(jobID)
	return o
}

// SetJobID adds the jobId to the retry execute job params
func (o *RetryExecuteJobParams) SetJobID(jobID string) {
	o.JobID = jobID
}

// WithPolicyID adds the policyID to the retry execute job params
func (o *RetryExecuteJobParams) WithPolicyID(policyID string) *RetryExecuteJobParams {
	o.SetPolicyID(policyID)
	return o
}

// SetPolicyID adds the policyId to the retry execute job params
func (o *RetryExecuteJobParams) SetPolicyID(policyID string) {
	o.PolicyID = policyID
}

// WriteToRequest writes these params to a swagger request
func (o *RetryExecuteJobParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param job_id
	if err := r.SetPathParam("job_id", o.JobID); err != nil {
		return err
	}

	// path param policy_id
	if err := r.SetPathParam("policy_id", o.PolicyID); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
