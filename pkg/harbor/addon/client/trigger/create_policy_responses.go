// Code generated by go-swagger; DO NOT EDIT.

package trigger

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// CreatePolicyReader is a Reader for the CreatePolicy structure.
type CreatePolicyReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *CreatePolicyReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewCreatePolicyOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewCreatePolicyBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 409:
		result := NewCreatePolicyConflict()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewCreatePolicyInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewCreatePolicyOK creates a CreatePolicyOK with default headers values
func NewCreatePolicyOK() *CreatePolicyOK {
	return &CreatePolicyOK{}
}

/* CreatePolicyOK describes a response with status code 200, with default header values.

OK
*/
type CreatePolicyOK struct {
	Payload int64
}

func (o *CreatePolicyOK) Error() string {
	return fmt.Sprintf("[POST /triggers/policies][%d] createPolicyOK  %+v", 200, o.Payload)
}
func (o *CreatePolicyOK) GetPayload() int64 {
	return o.Payload
}

func (o *CreatePolicyOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewCreatePolicyBadRequest creates a CreatePolicyBadRequest with default headers values
func NewCreatePolicyBadRequest() *CreatePolicyBadRequest {
	return &CreatePolicyBadRequest{}
}

/* CreatePolicyBadRequest describes a response with status code 400, with default header values.

Bad Request
*/
type CreatePolicyBadRequest struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload string
}

func (o *CreatePolicyBadRequest) Error() string {
	return fmt.Sprintf("[POST /triggers/policies][%d] createPolicyBadRequest  %+v", 400, o.Payload)
}
func (o *CreatePolicyBadRequest) GetPayload() string {
	return o.Payload
}

func (o *CreatePolicyBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewCreatePolicyConflict creates a CreatePolicyConflict with default headers values
func NewCreatePolicyConflict() *CreatePolicyConflict {
	return &CreatePolicyConflict{}
}

/* CreatePolicyConflict describes a response with status code 409, with default header values.

Conflict
*/
type CreatePolicyConflict struct {
	Payload string
}

func (o *CreatePolicyConflict) Error() string {
	return fmt.Sprintf("[POST /triggers/policies][%d] createPolicyConflict  %+v", 409, o.Payload)
}
func (o *CreatePolicyConflict) GetPayload() string {
	return o.Payload
}

func (o *CreatePolicyConflict) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewCreatePolicyInternalServerError creates a CreatePolicyInternalServerError with default headers values
func NewCreatePolicyInternalServerError() *CreatePolicyInternalServerError {
	return &CreatePolicyInternalServerError{}
}

/* CreatePolicyInternalServerError describes a response with status code 500, with default header values.

Internal Server Error
*/
type CreatePolicyInternalServerError struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload string
}

func (o *CreatePolicyInternalServerError) Error() string {
	return fmt.Sprintf("[POST /triggers/policies][%d] createPolicyInternalServerError  %+v", 500, o.Payload)
}
func (o *CreatePolicyInternalServerError) GetPayload() string {
	return o.Payload
}

func (o *CreatePolicyInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
