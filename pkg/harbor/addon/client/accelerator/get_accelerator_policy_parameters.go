// Code generated by go-swagger; DO NOT EDIT.

package accelerator

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewGetAcceleratorPolicyParams creates a new GetAcceleratorPolicyParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetAcceleratorPolicyParams() *GetAcceleratorPolicyParams {
	return &GetAcceleratorPolicyParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetAcceleratorPolicyParamsWithTimeout creates a new GetAcceleratorPolicyParams object
// with the ability to set a timeout on a request.
func NewGetAcceleratorPolicyParamsWithTimeout(timeout time.Duration) *GetAcceleratorPolicyParams {
	return &GetAcceleratorPolicyParams{
		timeout: timeout,
	}
}

// NewGetAcceleratorPolicyParamsWithContext creates a new GetAcceleratorPolicyParams object
// with the ability to set a context for a request.
func NewGetAcceleratorPolicyParamsWithContext(ctx context.Context) *GetAcceleratorPolicyParams {
	return &GetAcceleratorPolicyParams{
		Context: ctx,
	}
}

// NewGetAcceleratorPolicyParamsWithHTTPClient creates a new GetAcceleratorPolicyParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetAcceleratorPolicyParamsWithHTTPClient(client *http.Client) *GetAcceleratorPolicyParams {
	return &GetAcceleratorPolicyParams{
		HTTPClient: client,
	}
}

/* GetAcceleratorPolicyParams contains all the parameters to send to the API endpoint
   for the get accelerator policy operation.

   Typically these are written to a http.Request.
*/
type GetAcceleratorPolicyParams struct {

	/* XRequestID.

	   The request id
	*/
	XRequestID *string

	/* PolicyID.

	   The ID of the policy
	*/
	PolicyID string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get accelerator policy params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetAcceleratorPolicyParams) WithDefaults() *GetAcceleratorPolicyParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get accelerator policy params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetAcceleratorPolicyParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get accelerator policy params
func (o *GetAcceleratorPolicyParams) WithTimeout(timeout time.Duration) *GetAcceleratorPolicyParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get accelerator policy params
func (o *GetAcceleratorPolicyParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get accelerator policy params
func (o *GetAcceleratorPolicyParams) WithContext(ctx context.Context) *GetAcceleratorPolicyParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get accelerator policy params
func (o *GetAcceleratorPolicyParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get accelerator policy params
func (o *GetAcceleratorPolicyParams) WithHTTPClient(client *http.Client) *GetAcceleratorPolicyParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get accelerator policy params
func (o *GetAcceleratorPolicyParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the get accelerator policy params
func (o *GetAcceleratorPolicyParams) WithXRequestID(xRequestID *string) *GetAcceleratorPolicyParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the get accelerator policy params
func (o *GetAcceleratorPolicyParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithPolicyID adds the policyID to the get accelerator policy params
func (o *GetAcceleratorPolicyParams) WithPolicyID(policyID string) *GetAcceleratorPolicyParams {
	o.SetPolicyID(policyID)
	return o
}

// SetPolicyID adds the policyId to the get accelerator policy params
func (o *GetAcceleratorPolicyParams) SetPolicyID(policyID string) {
	o.PolicyID = policyID
}

// WriteToRequest writes these params to a swagger request
func (o *GetAcceleratorPolicyParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param policy_id
	if err := r.SetPathParam("policy_id", o.PolicyID); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
