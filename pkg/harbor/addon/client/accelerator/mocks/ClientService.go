// Code generated by mockery v2.28.1. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	accelerator "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/accelerator"

	runtime "github.com/go-openapi/runtime"
)

// MockAcceleratorClientService is an autogenerated mock type for the ClientService type
type MockAcceleratorClientService struct {
	mock.Mock
}

// BatchDeleteAcceleratorPolicy provides a mock function with given fields: params, authInfo, opts
func (_m *MockAcceleratorClientService) BatchDeleteAcceleratorPolicy(params *accelerator.BatchDeleteAcceleratorPolicyParams, authInfo runtime.ClientAuthInfoWriter, opts ...accelerator.ClientOption) (*accelerator.BatchDeleteAcceleratorPolicyOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *accelerator.BatchDeleteAcceleratorPolicyOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*accelerator.BatchDeleteAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) (*accelerator.BatchDeleteAcceleratorPolicyOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*accelerator.BatchDeleteAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) *accelerator.BatchDeleteAcceleratorPolicyOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*accelerator.BatchDeleteAcceleratorPolicyOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*accelerator.BatchDeleteAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateAcceleratorPolicy provides a mock function with given fields: params, authInfo, opts
func (_m *MockAcceleratorClientService) CreateAcceleratorPolicy(params *accelerator.CreateAcceleratorPolicyParams, authInfo runtime.ClientAuthInfoWriter, opts ...accelerator.ClientOption) (*accelerator.CreateAcceleratorPolicyOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *accelerator.CreateAcceleratorPolicyOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*accelerator.CreateAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) (*accelerator.CreateAcceleratorPolicyOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*accelerator.CreateAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) *accelerator.CreateAcceleratorPolicyOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*accelerator.CreateAcceleratorPolicyOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*accelerator.CreateAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteAcceleratorPolicy provides a mock function with given fields: params, authInfo, opts
func (_m *MockAcceleratorClientService) DeleteAcceleratorPolicy(params *accelerator.DeleteAcceleratorPolicyParams, authInfo runtime.ClientAuthInfoWriter, opts ...accelerator.ClientOption) (*accelerator.DeleteAcceleratorPolicyOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *accelerator.DeleteAcceleratorPolicyOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*accelerator.DeleteAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) (*accelerator.DeleteAcceleratorPolicyOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*accelerator.DeleteAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) *accelerator.DeleteAcceleratorPolicyOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*accelerator.DeleteAcceleratorPolicyOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*accelerator.DeleteAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAcceleratorPolicy provides a mock function with given fields: params, authInfo, opts
func (_m *MockAcceleratorClientService) GetAcceleratorPolicy(params *accelerator.GetAcceleratorPolicyParams, authInfo runtime.ClientAuthInfoWriter, opts ...accelerator.ClientOption) (*accelerator.GetAcceleratorPolicyOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *accelerator.GetAcceleratorPolicyOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*accelerator.GetAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) (*accelerator.GetAcceleratorPolicyOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*accelerator.GetAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) *accelerator.GetAcceleratorPolicyOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*accelerator.GetAcceleratorPolicyOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*accelerator.GetAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListAcceleratorPolicy provides a mock function with given fields: params, authInfo, opts
func (_m *MockAcceleratorClientService) ListAcceleratorPolicy(params *accelerator.ListAcceleratorPolicyParams, authInfo runtime.ClientAuthInfoWriter, opts ...accelerator.ClientOption) (*accelerator.ListAcceleratorPolicyOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *accelerator.ListAcceleratorPolicyOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*accelerator.ListAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) (*accelerator.ListAcceleratorPolicyOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*accelerator.ListAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) *accelerator.ListAcceleratorPolicyOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*accelerator.ListAcceleratorPolicyOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*accelerator.ListAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetTransport provides a mock function with given fields: transport
func (_m *MockAcceleratorClientService) SetTransport(transport runtime.ClientTransport) {
	_m.Called(transport)
}

// UpdateAcceleratorPolicy provides a mock function with given fields: params, authInfo, opts
func (_m *MockAcceleratorClientService) UpdateAcceleratorPolicy(params *accelerator.UpdateAcceleratorPolicyParams, authInfo runtime.ClientAuthInfoWriter, opts ...accelerator.ClientOption) (*accelerator.UpdateAcceleratorPolicyOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *accelerator.UpdateAcceleratorPolicyOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*accelerator.UpdateAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) (*accelerator.UpdateAcceleratorPolicyOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*accelerator.UpdateAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) *accelerator.UpdateAcceleratorPolicyOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*accelerator.UpdateAcceleratorPolicyOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*accelerator.UpdateAcceleratorPolicyParams, runtime.ClientAuthInfoWriter, ...accelerator.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockAcceleratorClientService interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockAcceleratorClientService creates a new instance of MockAcceleratorClientService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockAcceleratorClientService(t mockConstructorTestingTNewMockAcceleratorClientService) *MockAcceleratorClientService {
	mock := &MockAcceleratorClientService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
