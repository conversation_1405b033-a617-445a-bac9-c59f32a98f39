// Code generated by go-swagger; DO NOT EDIT.

package accelerator

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// GetAcceleratorPolicyReader is a Reader for the GetAcceleratorPolicy structure.
type GetAcceleratorPolicyReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *GetAcceleratorPolicyReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewGetAcceleratorPolicyOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewGetAcceleratorPolicyBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewGetAcceleratorPolicyNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewGetAcceleratorPolicyInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewGetAcceleratorPolicyOK creates a GetAcceleratorPolicyOK with default headers values
func NewGetAcceleratorPolicyOK() *GetAcceleratorPolicyOK {
	return &GetAcceleratorPolicyOK{}
}

/* GetAcceleratorPolicyOK describes a response with status code 200, with default header values.

OK
*/
type GetAcceleratorPolicyOK struct {
	Payload *model.PolicyAcceleratorPolicy
}

func (o *GetAcceleratorPolicyOK) Error() string {
	return fmt.Sprintf("[GET /accelerators/policies/{policy_id}][%d] getAcceleratorPolicyOK  %+v", 200, o.Payload)
}
func (o *GetAcceleratorPolicyOK) GetPayload() *model.PolicyAcceleratorPolicy {
	return o.Payload
}

func (o *GetAcceleratorPolicyOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(model.PolicyAcceleratorPolicy)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetAcceleratorPolicyBadRequest creates a GetAcceleratorPolicyBadRequest with default headers values
func NewGetAcceleratorPolicyBadRequest() *GetAcceleratorPolicyBadRequest {
	return &GetAcceleratorPolicyBadRequest{}
}

/* GetAcceleratorPolicyBadRequest describes a response with status code 400, with default header values.

Bad Request
*/
type GetAcceleratorPolicyBadRequest struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload string
}

func (o *GetAcceleratorPolicyBadRequest) Error() string {
	return fmt.Sprintf("[GET /accelerators/policies/{policy_id}][%d] getAcceleratorPolicyBadRequest  %+v", 400, o.Payload)
}
func (o *GetAcceleratorPolicyBadRequest) GetPayload() string {
	return o.Payload
}

func (o *GetAcceleratorPolicyBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetAcceleratorPolicyNotFound creates a GetAcceleratorPolicyNotFound with default headers values
func NewGetAcceleratorPolicyNotFound() *GetAcceleratorPolicyNotFound {
	return &GetAcceleratorPolicyNotFound{}
}

/* GetAcceleratorPolicyNotFound describes a response with status code 404, with default header values.

Not Found
*/
type GetAcceleratorPolicyNotFound struct {
	Payload string
}

func (o *GetAcceleratorPolicyNotFound) Error() string {
	return fmt.Sprintf("[GET /accelerators/policies/{policy_id}][%d] getAcceleratorPolicyNotFound  %+v", 404, o.Payload)
}
func (o *GetAcceleratorPolicyNotFound) GetPayload() string {
	return o.Payload
}

func (o *GetAcceleratorPolicyNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetAcceleratorPolicyInternalServerError creates a GetAcceleratorPolicyInternalServerError with default headers values
func NewGetAcceleratorPolicyInternalServerError() *GetAcceleratorPolicyInternalServerError {
	return &GetAcceleratorPolicyInternalServerError{}
}

/* GetAcceleratorPolicyInternalServerError describes a response with status code 500, with default header values.

Internal Server Error
*/
type GetAcceleratorPolicyInternalServerError struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload string
}

func (o *GetAcceleratorPolicyInternalServerError) Error() string {
	return fmt.Sprintf("[GET /accelerators/policies/{policy_id}][%d] getAcceleratorPolicyInternalServerError  %+v", 500, o.Payload)
}
func (o *GetAcceleratorPolicyInternalServerError) GetPayload() string {
	return o.Payload
}

func (o *GetAcceleratorPolicyInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
