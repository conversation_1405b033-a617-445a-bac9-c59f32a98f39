// Code generated by go-swagger; DO NOT EDIT.

package accelerator

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// ListAcceleratorPolicyReader is a Reader for the ListAcceleratorPolicy structure.
type ListAcceleratorPolicyReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *ListAcceleratorPolicyReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewListAcceleratorPolicyOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewListAcceleratorPolicyBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewListAcceleratorPolicyInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewListAcceleratorPolicyOK creates a ListAcceleratorPolicyOK with default headers values
func NewListAcceleratorPolicyOK() *ListAcceleratorPolicyOK {
	return &ListAcceleratorPolicyOK{}
}

/* ListAcceleratorPolicyOK describes a response with status code 200, with default header values.

OK
*/
type ListAcceleratorPolicyOK struct {

	/* The total of accelerator policy
	 */
	XTotalCount int64

	Payload []*model.PolicyAcceleratorPolicy
}

func (o *ListAcceleratorPolicyOK) Error() string {
	return fmt.Sprintf("[GET /accelerators/policies][%d] listAcceleratorPolicyOK  %+v", 200, o.Payload)
}
func (o *ListAcceleratorPolicyOK) GetPayload() []*model.PolicyAcceleratorPolicy {
	return o.Payload
}

func (o *ListAcceleratorPolicyOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Total-Count
	hdrXTotalCount := response.GetHeader("X-Total-Count")

	if hdrXTotalCount != "" {
		valxTotalCount, err := swag.ConvertInt64(hdrXTotalCount)
		if err != nil {
			return errors.InvalidType("X-Total-Count", "header", "int64", hdrXTotalCount)
		}
		o.XTotalCount = valxTotalCount
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListAcceleratorPolicyBadRequest creates a ListAcceleratorPolicyBadRequest with default headers values
func NewListAcceleratorPolicyBadRequest() *ListAcceleratorPolicyBadRequest {
	return &ListAcceleratorPolicyBadRequest{}
}

/* ListAcceleratorPolicyBadRequest describes a response with status code 400, with default header values.

Bad Request
*/
type ListAcceleratorPolicyBadRequest struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload string
}

func (o *ListAcceleratorPolicyBadRequest) Error() string {
	return fmt.Sprintf("[GET /accelerators/policies][%d] listAcceleratorPolicyBadRequest  %+v", 400, o.Payload)
}
func (o *ListAcceleratorPolicyBadRequest) GetPayload() string {
	return o.Payload
}

func (o *ListAcceleratorPolicyBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewListAcceleratorPolicyInternalServerError creates a ListAcceleratorPolicyInternalServerError with default headers values
func NewListAcceleratorPolicyInternalServerError() *ListAcceleratorPolicyInternalServerError {
	return &ListAcceleratorPolicyInternalServerError{}
}

/* ListAcceleratorPolicyInternalServerError describes a response with status code 500, with default header values.

Internal Server Error
*/
type ListAcceleratorPolicyInternalServerError struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload string
}

func (o *ListAcceleratorPolicyInternalServerError) Error() string {
	return fmt.Sprintf("[GET /accelerators/policies][%d] listAcceleratorPolicyInternalServerError  %+v", 500, o.Payload)
}
func (o *ListAcceleratorPolicyInternalServerError) GetPayload() string {
	return o.Payload
}

func (o *ListAcceleratorPolicyInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
