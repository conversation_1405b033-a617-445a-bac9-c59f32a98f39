// Code generated by go-swagger; DO NOT EDIT.

package accelerator

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// NewBatchDeleteAcceleratorPolicyParams creates a new BatchDeleteAcceleratorPolicyParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewBatchDeleteAcceleratorPolicyParams() *BatchDeleteAcceleratorPolicyParams {
	return &BatchDeleteAcceleratorPolicyParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewBatchDeleteAcceleratorPolicyParamsWithTimeout creates a new BatchDeleteAcceleratorPolicyParams object
// with the ability to set a timeout on a request.
func NewBatchDeleteAcceleratorPolicyParamsWithTimeout(timeout time.Duration) *BatchDeleteAcceleratorPolicyParams {
	return &BatchDeleteAcceleratorPolicyParams{
		timeout: timeout,
	}
}

// NewBatchDeleteAcceleratorPolicyParamsWithContext creates a new BatchDeleteAcceleratorPolicyParams object
// with the ability to set a context for a request.
func NewBatchDeleteAcceleratorPolicyParamsWithContext(ctx context.Context) *BatchDeleteAcceleratorPolicyParams {
	return &BatchDeleteAcceleratorPolicyParams{
		Context: ctx,
	}
}

// NewBatchDeleteAcceleratorPolicyParamsWithHTTPClient creates a new BatchDeleteAcceleratorPolicyParams object
// with the ability to set a custom HTTPClient for a request.
func NewBatchDeleteAcceleratorPolicyParamsWithHTTPClient(client *http.Client) *BatchDeleteAcceleratorPolicyParams {
	return &BatchDeleteAcceleratorPolicyParams{
		HTTPClient: client,
	}
}

/* BatchDeleteAcceleratorPolicyParams contains all the parameters to send to the API endpoint
   for the batch delete accelerator policy operation.

   Typically these are written to a http.Request.
*/
type BatchDeleteAcceleratorPolicyParams struct {

	/* XRequestID.

	   The request id
	*/
	XRequestID *string

	/* Request.

	   The accelerator policy ids
	*/
	Request *model.ModelsBatchDeleteRequest

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the batch delete accelerator policy params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *BatchDeleteAcceleratorPolicyParams) WithDefaults() *BatchDeleteAcceleratorPolicyParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the batch delete accelerator policy params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *BatchDeleteAcceleratorPolicyParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the batch delete accelerator policy params
func (o *BatchDeleteAcceleratorPolicyParams) WithTimeout(timeout time.Duration) *BatchDeleteAcceleratorPolicyParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the batch delete accelerator policy params
func (o *BatchDeleteAcceleratorPolicyParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the batch delete accelerator policy params
func (o *BatchDeleteAcceleratorPolicyParams) WithContext(ctx context.Context) *BatchDeleteAcceleratorPolicyParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the batch delete accelerator policy params
func (o *BatchDeleteAcceleratorPolicyParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the batch delete accelerator policy params
func (o *BatchDeleteAcceleratorPolicyParams) WithHTTPClient(client *http.Client) *BatchDeleteAcceleratorPolicyParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the batch delete accelerator policy params
func (o *BatchDeleteAcceleratorPolicyParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the batch delete accelerator policy params
func (o *BatchDeleteAcceleratorPolicyParams) WithXRequestID(xRequestID *string) *BatchDeleteAcceleratorPolicyParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the batch delete accelerator policy params
func (o *BatchDeleteAcceleratorPolicyParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithRequest adds the request to the batch delete accelerator policy params
func (o *BatchDeleteAcceleratorPolicyParams) WithRequest(request *model.ModelsBatchDeleteRequest) *BatchDeleteAcceleratorPolicyParams {
	o.SetRequest(request)
	return o
}

// SetRequest adds the request to the batch delete accelerator policy params
func (o *BatchDeleteAcceleratorPolicyParams) SetRequest(request *model.ModelsBatchDeleteRequest) {
	o.Request = request
}

// WriteToRequest writes these params to a swagger request
func (o *BatchDeleteAcceleratorPolicyParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}
	if o.Request != nil {
		if err := r.SetBodyParam(o.Request); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
