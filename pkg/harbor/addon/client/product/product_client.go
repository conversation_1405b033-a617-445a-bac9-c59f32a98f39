// Code generated by go-swagger; DO NOT EDIT.

package product

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new product API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for product API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

//go:generate mockery --name ClientService --structname MockProductClientService

// ClientService is the interface for Client methods
type ClientService interface {
	GetProductsStatistic(params *GetProductsStatisticParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetProductsStatisticOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
  GetProductsStatistic 获取统计信息s

  获取统计信息
*/
func (a *Client) GetProductsStatistic(params *GetProductsStatisticParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetProductsStatisticOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetProductsStatisticParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "GetProductsStatistic",
		Method:             "GET",
		PathPattern:        "/products/statistic",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http"},
		Params:             params,
		Reader:             &GetProductsStatisticReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetProductsStatisticOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for GetProductsStatistic: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
