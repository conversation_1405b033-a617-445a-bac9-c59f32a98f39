// Code generated by go-swagger; DO NOT EDIT.

package tag

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewListTagsParams creates a new ListTagsParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListTagsParams() *ListTagsParams {
	return &ListTagsParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListTagsParamsWithTimeout creates a new ListTagsParams object
// with the ability to set a timeout on a request.
func NewListTagsParamsWithTimeout(timeout time.Duration) *ListTagsParams {
	return &ListTagsParams{
		timeout: timeout,
	}
}

// NewListTagsParamsWithContext creates a new ListTagsParams object
// with the ability to set a context for a request.
func NewListTagsParamsWithContext(ctx context.Context) *ListTagsParams {
	return &ListTagsParams{
		Context: ctx,
	}
}

// NewListTagsParamsWithHTTPClient creates a new ListTagsParams object
// with the ability to set a custom HTTPClient for a request.
func NewListTagsParamsWithHTTPClient(client *http.Client) *ListTagsParams {
	return &ListTagsParams{
		HTTPClient: client,
	}
}

/* ListTagsParams contains all the parameters to send to the API endpoint
   for the list tags operation.

   Typically these are written to a http.Request.
*/
type ListTagsParams struct {

	/* XRequestID.

	   request id
	*/
	XRequestID *string

	/* Key.

	   搜索关键信息
	*/
	Key *string

	/* Page.

	   页码

	   Default: 1
	*/
	Page *int64

	/* PageSize.

	   页大小

	   Default: 10
	*/
	PageSize *int64

	/* ProjectName.

	   空间名字
	*/
	ProjectName string

	/* RepositoryName.

	   仓库名字
	*/
	RepositoryName string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list tags params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListTagsParams) WithDefaults() *ListTagsParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list tags params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListTagsParams) SetDefaults() {
	var (
		pageDefault = int64(1)

		pageSizeDefault = int64(10)
	)

	val := ListTagsParams{
		Page:     &pageDefault,
		PageSize: &pageSizeDefault,
	}

	val.timeout = o.timeout
	val.Context = o.Context
	val.HTTPClient = o.HTTPClient
	*o = val
}

// WithTimeout adds the timeout to the list tags params
func (o *ListTagsParams) WithTimeout(timeout time.Duration) *ListTagsParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list tags params
func (o *ListTagsParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list tags params
func (o *ListTagsParams) WithContext(ctx context.Context) *ListTagsParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list tags params
func (o *ListTagsParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list tags params
func (o *ListTagsParams) WithHTTPClient(client *http.Client) *ListTagsParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list tags params
func (o *ListTagsParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the list tags params
func (o *ListTagsParams) WithXRequestID(xRequestID *string) *ListTagsParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the list tags params
func (o *ListTagsParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithKey adds the key to the list tags params
func (o *ListTagsParams) WithKey(key *string) *ListTagsParams {
	o.SetKey(key)
	return o
}

// SetKey adds the key to the list tags params
func (o *ListTagsParams) SetKey(key *string) {
	o.Key = key
}

// WithPage adds the page to the list tags params
func (o *ListTagsParams) WithPage(page *int64) *ListTagsParams {
	o.SetPage(page)
	return o
}

// SetPage adds the page to the list tags params
func (o *ListTagsParams) SetPage(page *int64) {
	o.Page = page
}

// WithPageSize adds the pageSize to the list tags params
func (o *ListTagsParams) WithPageSize(pageSize *int64) *ListTagsParams {
	o.SetPageSize(pageSize)
	return o
}

// SetPageSize adds the pageSize to the list tags params
func (o *ListTagsParams) SetPageSize(pageSize *int64) {
	o.PageSize = pageSize
}

// WithProjectName adds the projectName to the list tags params
func (o *ListTagsParams) WithProjectName(projectName string) *ListTagsParams {
	o.SetProjectName(projectName)
	return o
}

// SetProjectName adds the projectName to the list tags params
func (o *ListTagsParams) SetProjectName(projectName string) {
	o.ProjectName = projectName
}

// WithRepositoryName adds the repositoryName to the list tags params
func (o *ListTagsParams) WithRepositoryName(repositoryName string) *ListTagsParams {
	o.SetRepositoryName(repositoryName)
	return o
}

// SetRepositoryName adds the repositoryName to the list tags params
func (o *ListTagsParams) SetRepositoryName(repositoryName string) {
	o.RepositoryName = repositoryName
}

// WriteToRequest writes these params to a swagger request
func (o *ListTagsParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	if o.Key != nil {

		// query param key
		var qrKey string

		if o.Key != nil {
			qrKey = *o.Key
		}
		qKey := qrKey
		if qKey != "" {

			if err := r.SetQueryParam("key", qKey); err != nil {
				return err
			}
		}
	}

	if o.Page != nil {

		// query param page
		var qrPage int64

		if o.Page != nil {
			qrPage = *o.Page
		}
		qPage := swag.FormatInt64(qrPage)
		if qPage != "" {

			if err := r.SetQueryParam("page", qPage); err != nil {
				return err
			}
		}
	}

	if o.PageSize != nil {

		// query param page_size
		var qrPageSize int64

		if o.PageSize != nil {
			qrPageSize = *o.PageSize
		}
		qPageSize := swag.FormatInt64(qrPageSize)
		if qPageSize != "" {

			if err := r.SetQueryParam("page_size", qPageSize); err != nil {
				return err
			}
		}
	}

	// path param project_name
	if err := r.SetPathParam("project_name", o.ProjectName); err != nil {
		return err
	}

	// path param repository_name
	if err := r.SetPathParam("repository_name", o.RepositoryName); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
