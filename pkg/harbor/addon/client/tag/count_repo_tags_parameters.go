// Code generated by go-swagger; DO NOT EDIT.

package tag

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewCountRepoTagsParams creates a new CountRepoTagsParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewCountRepoTagsParams() *CountRepoTagsParams {
	return &CountRepoTagsParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewCountRepoTagsParamsWithTimeout creates a new CountRepoTagsParams object
// with the ability to set a timeout on a request.
func NewCountRepoTagsParamsWithTimeout(timeout time.Duration) *CountRepoTagsParams {
	return &CountRepoTagsParams{
		timeout: timeout,
	}
}

// NewCountRepoTagsParamsWithContext creates a new CountRepoTagsParams object
// with the ability to set a context for a request.
func NewCountRepoTagsParamsWithContext(ctx context.Context) *CountRepoTagsParams {
	return &CountRepoTagsParams{
		Context: ctx,
	}
}

// NewCountRepoTagsParamsWithHTTPClient creates a new CountRepoTagsParams object
// with the ability to set a custom HTTPClient for a request.
func NewCountRepoTagsParamsWithHTTPClient(client *http.Client) *CountRepoTagsParams {
	return &CountRepoTagsParams{
		HTTPClient: client,
	}
}

/* CountRepoTagsParams contains all the parameters to send to the API endpoint
   for the count repo tags operation.

   Typically these are written to a http.Request.
*/
type CountRepoTagsParams struct {

	/* XRequestID.

	   request id
	*/
	XRequestID *string

	/* ProjectName.

	   空间名字
	*/
	ProjectName string

	/* RepositoryNames.

	   仓库名字数组
	*/
	RepositoryNames string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the count repo tags params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *CountRepoTagsParams) WithDefaults() *CountRepoTagsParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the count repo tags params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *CountRepoTagsParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the count repo tags params
func (o *CountRepoTagsParams) WithTimeout(timeout time.Duration) *CountRepoTagsParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the count repo tags params
func (o *CountRepoTagsParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the count repo tags params
func (o *CountRepoTagsParams) WithContext(ctx context.Context) *CountRepoTagsParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the count repo tags params
func (o *CountRepoTagsParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the count repo tags params
func (o *CountRepoTagsParams) WithHTTPClient(client *http.Client) *CountRepoTagsParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the count repo tags params
func (o *CountRepoTagsParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the count repo tags params
func (o *CountRepoTagsParams) WithXRequestID(xRequestID *string) *CountRepoTagsParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the count repo tags params
func (o *CountRepoTagsParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithProjectName adds the projectName to the count repo tags params
func (o *CountRepoTagsParams) WithProjectName(projectName string) *CountRepoTagsParams {
	o.SetProjectName(projectName)
	return o
}

// SetProjectName adds the projectName to the count repo tags params
func (o *CountRepoTagsParams) SetProjectName(projectName string) {
	o.ProjectName = projectName
}

// WithRepositoryNames adds the repositoryNames to the count repo tags params
func (o *CountRepoTagsParams) WithRepositoryNames(repositoryNames string) *CountRepoTagsParams {
	o.SetRepositoryNames(repositoryNames)
	return o
}

// SetRepositoryNames adds the repositoryNames to the count repo tags params
func (o *CountRepoTagsParams) SetRepositoryNames(repositoryNames string) {
	o.RepositoryNames = repositoryNames
}

// WriteToRequest writes these params to a swagger request
func (o *CountRepoTagsParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param project_name
	if err := r.SetPathParam("project_name", o.ProjectName); err != nil {
		return err
	}

	// query param repository_names
	qrRepositoryNames := o.RepositoryNames
	qRepositoryNames := qrRepositoryNames
	if qRepositoryNames != "" {

		if err := r.SetQueryParam("repository_names", qRepositoryNames); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
