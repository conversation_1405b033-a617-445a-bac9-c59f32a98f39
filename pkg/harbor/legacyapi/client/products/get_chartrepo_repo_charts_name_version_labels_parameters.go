// Code generated by go-swagger; DO NOT EDIT.

package products

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewGetChartrepoRepoChartsNameVersionLabelsParams creates a new GetChartrepoRepoChartsNameVersionLabelsParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetChartrepoRepoChartsNameVersionLabelsParams() *GetChartrepoRepoChartsNameVersionLabelsParams {
	return &GetChartrepoRepoChartsNameVersionLabelsParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetChartrepoRepoChartsNameVersionLabelsParamsWithTimeout creates a new GetChartrepoRepoChartsNameVersionLabelsParams object
// with the ability to set a timeout on a request.
func NewGetChartrepoRepoChartsNameVersionLabelsParamsWithTimeout(timeout time.Duration) *GetChartrepoRepoChartsNameVersionLabelsParams {
	return &GetChartrepoRepoChartsNameVersionLabelsParams{
		timeout: timeout,
	}
}

// NewGetChartrepoRepoChartsNameVersionLabelsParamsWithContext creates a new GetChartrepoRepoChartsNameVersionLabelsParams object
// with the ability to set a context for a request.
func NewGetChartrepoRepoChartsNameVersionLabelsParamsWithContext(ctx context.Context) *GetChartrepoRepoChartsNameVersionLabelsParams {
	return &GetChartrepoRepoChartsNameVersionLabelsParams{
		Context: ctx,
	}
}

// NewGetChartrepoRepoChartsNameVersionLabelsParamsWithHTTPClient creates a new GetChartrepoRepoChartsNameVersionLabelsParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetChartrepoRepoChartsNameVersionLabelsParamsWithHTTPClient(client *http.Client) *GetChartrepoRepoChartsNameVersionLabelsParams {
	return &GetChartrepoRepoChartsNameVersionLabelsParams{
		HTTPClient: client,
	}
}

/* GetChartrepoRepoChartsNameVersionLabelsParams contains all the parameters to send to the API endpoint
   for the get chartrepo repo charts name version labels operation.

   Typically these are written to a http.Request.
*/
type GetChartrepoRepoChartsNameVersionLabelsParams struct {

	/* Name.

	   The chart name
	*/
	Name string

	/* Repo.

	   The project name
	*/
	Repo string

	/* Version.

	   The chart version
	*/
	Version string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get chartrepo repo charts name version labels params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) WithDefaults() *GetChartrepoRepoChartsNameVersionLabelsParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get chartrepo repo charts name version labels params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) WithTimeout(timeout time.Duration) *GetChartrepoRepoChartsNameVersionLabelsParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) WithContext(ctx context.Context) *GetChartrepoRepoChartsNameVersionLabelsParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) WithHTTPClient(client *http.Client) *GetChartrepoRepoChartsNameVersionLabelsParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithName adds the name to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) WithName(name string) *GetChartrepoRepoChartsNameVersionLabelsParams {
	o.SetName(name)
	return o
}

// SetName adds the name to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) SetName(name string) {
	o.Name = name
}

// WithRepo adds the repo to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) WithRepo(repo string) *GetChartrepoRepoChartsNameVersionLabelsParams {
	o.SetRepo(repo)
	return o
}

// SetRepo adds the repo to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) SetRepo(repo string) {
	o.Repo = repo
}

// WithVersion adds the version to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) WithVersion(version string) *GetChartrepoRepoChartsNameVersionLabelsParams {
	o.SetVersion(version)
	return o
}

// SetVersion adds the version to the get chartrepo repo charts name version labels params
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) SetVersion(version string) {
	o.Version = version
}

// WriteToRequest writes these params to a swagger request
func (o *GetChartrepoRepoChartsNameVersionLabelsParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	// path param name
	if err := r.SetPathParam("name", o.Name); err != nil {
		return err
	}

	// path param repo
	if err := r.SetPathParam("repo", o.Repo); err != nil {
		return err
	}

	// path param version
	if err := r.SetPathParam("version", o.Version); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
