// Code generated by go-swagger; DO NOT EDIT.

package ldap

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewSearchLdapGroupParams creates a new SearchLdapGroupParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewSearchLdapGroupParams() *SearchLdapGroupParams {
	return &SearchLdapGroupParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewSearchLdapGroupParamsWithTimeout creates a new SearchLdapGroupParams object
// with the ability to set a timeout on a request.
func NewSearchLdapGroupParamsWithTimeout(timeout time.Duration) *SearchLdapGroupParams {
	return &SearchLdapGroupParams{
		timeout: timeout,
	}
}

// NewSearchLdapGroupParamsWithContext creates a new SearchLdapGroupParams object
// with the ability to set a context for a request.
func NewSearchLdapGroupParamsWithContext(ctx context.Context) *SearchLdapGroupParams {
	return &SearchLdapGroupParams{
		Context: ctx,
	}
}

// NewSearchLdapGroupParamsWithHTTPClient creates a new SearchLdapGroupParams object
// with the ability to set a custom HTTPClient for a request.
func NewSearchLdapGroupParamsWithHTTPClient(client *http.Client) *SearchLdapGroupParams {
	return &SearchLdapGroupParams{
		HTTPClient: client,
	}
}

/* SearchLdapGroupParams contains all the parameters to send to the API endpoint
   for the search ldap group operation.

   Typically these are written to a http.Request.
*/
type SearchLdapGroupParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* Groupdn.

	   The LDAP group DN
	*/
	Groupdn *string

	/* Groupname.

	   Ldap group name
	*/
	Groupname *string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the search ldap group params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *SearchLdapGroupParams) WithDefaults() *SearchLdapGroupParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the search ldap group params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *SearchLdapGroupParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the search ldap group params
func (o *SearchLdapGroupParams) WithTimeout(timeout time.Duration) *SearchLdapGroupParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the search ldap group params
func (o *SearchLdapGroupParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the search ldap group params
func (o *SearchLdapGroupParams) WithContext(ctx context.Context) *SearchLdapGroupParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the search ldap group params
func (o *SearchLdapGroupParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the search ldap group params
func (o *SearchLdapGroupParams) WithHTTPClient(client *http.Client) *SearchLdapGroupParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the search ldap group params
func (o *SearchLdapGroupParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the search ldap group params
func (o *SearchLdapGroupParams) WithXRequestID(xRequestID *string) *SearchLdapGroupParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the search ldap group params
func (o *SearchLdapGroupParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithGroupdn adds the groupdn to the search ldap group params
func (o *SearchLdapGroupParams) WithGroupdn(groupdn *string) *SearchLdapGroupParams {
	o.SetGroupdn(groupdn)
	return o
}

// SetGroupdn adds the groupdn to the search ldap group params
func (o *SearchLdapGroupParams) SetGroupdn(groupdn *string) {
	o.Groupdn = groupdn
}

// WithGroupname adds the groupname to the search ldap group params
func (o *SearchLdapGroupParams) WithGroupname(groupname *string) *SearchLdapGroupParams {
	o.SetGroupname(groupname)
	return o
}

// SetGroupname adds the groupname to the search ldap group params
func (o *SearchLdapGroupParams) SetGroupname(groupname *string) {
	o.Groupname = groupname
}

// WriteToRequest writes these params to a swagger request
func (o *SearchLdapGroupParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	if o.Groupdn != nil {

		// query param groupdn
		var qrGroupdn string

		if o.Groupdn != nil {
			qrGroupdn = *o.Groupdn
		}
		qGroupdn := qrGroupdn
		if qGroupdn != "" {

			if err := r.SetQueryParam("groupdn", qGroupdn); err != nil {
				return err
			}
		}
	}

	if o.Groupname != nil {

		// query param groupname
		var qrGroupname string

		if o.Groupname != nil {
			qrGroupname = *o.Groupname
		}
		qGroupname := qrGroupname
		if qGroupname != "" {

			if err := r.SetQueryParam("groupname", qGroupname); err != nil {
				return err
			}
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
