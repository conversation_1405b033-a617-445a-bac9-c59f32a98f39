// Code generated by go-swagger; DO NOT EDIT.

package replication

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewListReplicationExecutionsParams creates a new ListReplicationExecutionsParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListReplicationExecutionsParams() *ListReplicationExecutionsParams {
	return &ListReplicationExecutionsParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListReplicationExecutionsParamsWithTimeout creates a new ListReplicationExecutionsParams object
// with the ability to set a timeout on a request.
func NewListReplicationExecutionsParamsWithTimeout(timeout time.Duration) *ListReplicationExecutionsParams {
	return &ListReplicationExecutionsParams{
		timeout: timeout,
	}
}

// NewListReplicationExecutionsParamsWithContext creates a new ListReplicationExecutionsParams object
// with the ability to set a context for a request.
func NewListReplicationExecutionsParamsWithContext(ctx context.Context) *ListReplicationExecutionsParams {
	return &ListReplicationExecutionsParams{
		Context: ctx,
	}
}

// NewListReplicationExecutionsParamsWithHTTPClient creates a new ListReplicationExecutionsParams object
// with the ability to set a custom HTTPClient for a request.
func NewListReplicationExecutionsParamsWithHTTPClient(client *http.Client) *ListReplicationExecutionsParams {
	return &ListReplicationExecutionsParams{
		HTTPClient: client,
	}
}

/* ListReplicationExecutionsParams contains all the parameters to send to the API endpoint
   for the list replication executions operation.

   Typically these are written to a http.Request.
*/
type ListReplicationExecutionsParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* Page.

	   The page number

	   Format: int64
	   Default: 1
	*/
	Page *int64

	/* PageSize.

	   The size of per page

	   Format: int64
	   Default: 10
	*/
	PageSize *int64

	/* PolicyID.

	   The ID of the policy that the executions belong to.
	*/
	PolicyID *int64

	/* Sort.

	   Sort the resource list in ascending or descending order. e.g. sort by field1 in ascending orderr and field2 in descending order with "sort=field1,-field2"
	*/
	Sort *string

	/* Status.

	   The execution status.
	*/
	Status *string

	/* Trigger.

	   The trigger mode.
	*/
	Trigger *string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list replication executions params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListReplicationExecutionsParams) WithDefaults() *ListReplicationExecutionsParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list replication executions params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListReplicationExecutionsParams) SetDefaults() {
	var (
		pageDefault = int64(1)

		pageSizeDefault = int64(10)
	)

	val := ListReplicationExecutionsParams{
		Page:     &pageDefault,
		PageSize: &pageSizeDefault,
	}

	val.timeout = o.timeout
	val.Context = o.Context
	val.HTTPClient = o.HTTPClient
	*o = val
}

// WithTimeout adds the timeout to the list replication executions params
func (o *ListReplicationExecutionsParams) WithTimeout(timeout time.Duration) *ListReplicationExecutionsParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list replication executions params
func (o *ListReplicationExecutionsParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list replication executions params
func (o *ListReplicationExecutionsParams) WithContext(ctx context.Context) *ListReplicationExecutionsParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list replication executions params
func (o *ListReplicationExecutionsParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list replication executions params
func (o *ListReplicationExecutionsParams) WithHTTPClient(client *http.Client) *ListReplicationExecutionsParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list replication executions params
func (o *ListReplicationExecutionsParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the list replication executions params
func (o *ListReplicationExecutionsParams) WithXRequestID(xRequestID *string) *ListReplicationExecutionsParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the list replication executions params
func (o *ListReplicationExecutionsParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithPage adds the page to the list replication executions params
func (o *ListReplicationExecutionsParams) WithPage(page *int64) *ListReplicationExecutionsParams {
	o.SetPage(page)
	return o
}

// SetPage adds the page to the list replication executions params
func (o *ListReplicationExecutionsParams) SetPage(page *int64) {
	o.Page = page
}

// WithPageSize adds the pageSize to the list replication executions params
func (o *ListReplicationExecutionsParams) WithPageSize(pageSize *int64) *ListReplicationExecutionsParams {
	o.SetPageSize(pageSize)
	return o
}

// SetPageSize adds the pageSize to the list replication executions params
func (o *ListReplicationExecutionsParams) SetPageSize(pageSize *int64) {
	o.PageSize = pageSize
}

// WithPolicyID adds the policyID to the list replication executions params
func (o *ListReplicationExecutionsParams) WithPolicyID(policyID *int64) *ListReplicationExecutionsParams {
	o.SetPolicyID(policyID)
	return o
}

// SetPolicyID adds the policyId to the list replication executions params
func (o *ListReplicationExecutionsParams) SetPolicyID(policyID *int64) {
	o.PolicyID = policyID
}

// WithSort adds the sort to the list replication executions params
func (o *ListReplicationExecutionsParams) WithSort(sort *string) *ListReplicationExecutionsParams {
	o.SetSort(sort)
	return o
}

// SetSort adds the sort to the list replication executions params
func (o *ListReplicationExecutionsParams) SetSort(sort *string) {
	o.Sort = sort
}

// WithStatus adds the status to the list replication executions params
func (o *ListReplicationExecutionsParams) WithStatus(status *string) *ListReplicationExecutionsParams {
	o.SetStatus(status)
	return o
}

// SetStatus adds the status to the list replication executions params
func (o *ListReplicationExecutionsParams) SetStatus(status *string) {
	o.Status = status
}

// WithTrigger adds the trigger to the list replication executions params
func (o *ListReplicationExecutionsParams) WithTrigger(trigger *string) *ListReplicationExecutionsParams {
	o.SetTrigger(trigger)
	return o
}

// SetTrigger adds the trigger to the list replication executions params
func (o *ListReplicationExecutionsParams) SetTrigger(trigger *string) {
	o.Trigger = trigger
}

// WriteToRequest writes these params to a swagger request
func (o *ListReplicationExecutionsParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	if o.Page != nil {

		// query param page
		var qrPage int64

		if o.Page != nil {
			qrPage = *o.Page
		}
		qPage := swag.FormatInt64(qrPage)
		if qPage != "" {

			if err := r.SetQueryParam("page", qPage); err != nil {
				return err
			}
		}
	}

	if o.PageSize != nil {

		// query param page_size
		var qrPageSize int64

		if o.PageSize != nil {
			qrPageSize = *o.PageSize
		}
		qPageSize := swag.FormatInt64(qrPageSize)
		if qPageSize != "" {

			if err := r.SetQueryParam("page_size", qPageSize); err != nil {
				return err
			}
		}
	}

	if o.PolicyID != nil {

		// query param policy_id
		var qrPolicyID int64

		if o.PolicyID != nil {
			qrPolicyID = *o.PolicyID
		}
		qPolicyID := swag.FormatInt64(qrPolicyID)
		if qPolicyID != "" {

			if err := r.SetQueryParam("policy_id", qPolicyID); err != nil {
				return err
			}
		}
	}

	if o.Sort != nil {

		// query param sort
		var qrSort string

		if o.Sort != nil {
			qrSort = *o.Sort
		}
		qSort := qrSort
		if qSort != "" {

			if err := r.SetQueryParam("sort", qSort); err != nil {
				return err
			}
		}
	}

	if o.Status != nil {

		// query param status
		var qrStatus string

		if o.Status != nil {
			qrStatus = *o.Status
		}
		qStatus := qrStatus
		if qStatus != "" {

			if err := r.SetQueryParam("status", qStatus); err != nil {
				return err
			}
		}
	}

	if o.Trigger != nil {

		// query param trigger
		var qrTrigger string

		if o.Trigger != nil {
			qrTrigger = *o.Trigger
		}
		qTrigger := qrTrigger
		if qTrigger != "" {

			if err := r.SetQueryParam("trigger", qTrigger); err != nil {
				return err
			}
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
