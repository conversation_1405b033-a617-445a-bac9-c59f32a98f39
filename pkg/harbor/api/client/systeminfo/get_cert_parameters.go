// Code generated by go-swagger; DO NOT EDIT.

package systeminfo

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewGetCertParams creates a new GetCertParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetCertParams() *GetCertParams {
	return &GetCertParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetCertParamsWithTimeout creates a new GetCertParams object
// with the ability to set a timeout on a request.
func NewGetCertParamsWithTimeout(timeout time.Duration) *GetCertParams {
	return &GetCertParams{
		timeout: timeout,
	}
}

// NewGetCertParamsWithContext creates a new GetCertParams object
// with the ability to set a context for a request.
func NewGetCertParamsWithContext(ctx context.Context) *GetCertParams {
	return &GetCertParams{
		Context: ctx,
	}
}

// NewGetCertParamsWithHTTPClient creates a new GetCertParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetCertParamsWithHTTPClient(client *http.Client) *GetCertParams {
	return &GetCertParams{
		HTTPClient: client,
	}
}

/* GetCertParams contains all the parameters to send to the API endpoint
   for the get cert operation.

   Typically these are written to a http.Request.
*/
type GetCertParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get cert params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetCertParams) WithDefaults() *GetCertParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get cert params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetCertParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get cert params
func (o *GetCertParams) WithTimeout(timeout time.Duration) *GetCertParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get cert params
func (o *GetCertParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get cert params
func (o *GetCertParams) WithContext(ctx context.Context) *GetCertParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get cert params
func (o *GetCertParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get cert params
func (o *GetCertParams) WithHTTPClient(client *http.Client) *GetCertParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get cert params
func (o *GetCertParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the get cert params
func (o *GetCertParams) WithXRequestID(xRequestID *string) *GetCertParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the get cert params
func (o *GetCertParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WriteToRequest writes these params to a swagger request
func (o *GetCertParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
