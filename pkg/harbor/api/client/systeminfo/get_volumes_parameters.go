// Code generated by go-swagger; DO NOT EDIT.

package systeminfo

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewGetVolumesParams creates a new GetVolumesParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetVolumesParams() *GetVolumesParams {
	return &GetVolumesParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetVolumesParamsWithTimeout creates a new GetVolumesParams object
// with the ability to set a timeout on a request.
func NewGetVolumesParamsWithTimeout(timeout time.Duration) *GetVolumesParams {
	return &GetVolumesParams{
		timeout: timeout,
	}
}

// NewGetVolumesParamsWithContext creates a new GetVolumesParams object
// with the ability to set a context for a request.
func NewGetVolumesParamsWithContext(ctx context.Context) *GetVolumesParams {
	return &GetVolumesParams{
		Context: ctx,
	}
}

// NewGetVolumesParamsWithHTTPClient creates a new GetVolumesParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetVolumesParamsWithHTTPClient(client *http.Client) *GetVolumesParams {
	return &GetVolumesParams{
		HTTPClient: client,
	}
}

/* GetVolumesParams contains all the parameters to send to the API endpoint
   for the get volumes operation.

   Typically these are written to a http.Request.
*/
type GetVolumesParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get volumes params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetVolumesParams) WithDefaults() *GetVolumesParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get volumes params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetVolumesParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get volumes params
func (o *GetVolumesParams) WithTimeout(timeout time.Duration) *GetVolumesParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get volumes params
func (o *GetVolumesParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get volumes params
func (o *GetVolumesParams) WithContext(ctx context.Context) *GetVolumesParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get volumes params
func (o *GetVolumesParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get volumes params
func (o *GetVolumesParams) WithHTTPClient(client *http.Client) *GetVolumesParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get volumes params
func (o *GetVolumesParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the get volumes params
func (o *GetVolumesParams) WithXRequestID(xRequestID *string) *GetVolumesParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the get volumes params
func (o *GetVolumesParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WriteToRequest writes these params to a swagger request
func (o *GetVolumesParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
