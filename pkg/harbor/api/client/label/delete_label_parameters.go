// Code generated by go-swagger; DO NOT EDIT.

package label

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewDeleteLabelParams creates a new DeleteLabelParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewDeleteLabelParams() *DeleteLabelParams {
	return &DeleteLabelParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewDeleteLabelParamsWithTimeout creates a new DeleteLabelParams object
// with the ability to set a timeout on a request.
func NewDeleteLabelParamsWithTimeout(timeout time.Duration) *DeleteLabelParams {
	return &DeleteLabelParams{
		timeout: timeout,
	}
}

// NewDeleteLabelParamsWithContext creates a new DeleteLabelParams object
// with the ability to set a context for a request.
func NewDeleteLabelParamsWithContext(ctx context.Context) *DeleteLabelParams {
	return &DeleteLabelParams{
		Context: ctx,
	}
}

// NewDeleteLabelParamsWithHTTPClient creates a new DeleteLabelParams object
// with the ability to set a custom HTTPClient for a request.
func NewDeleteLabelParamsWithHTTPClient(client *http.Client) *DeleteLabelParams {
	return &DeleteLabelParams{
		HTTPClient: client,
	}
}

/* DeleteLabelParams contains all the parameters to send to the API endpoint
   for the delete label operation.

   Typically these are written to a http.Request.
*/
type DeleteLabelParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* LabelID.

	   Label ID

	   Format: int64
	*/
	LabelID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the delete label params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DeleteLabelParams) WithDefaults() *DeleteLabelParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the delete label params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DeleteLabelParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the delete label params
func (o *DeleteLabelParams) WithTimeout(timeout time.Duration) *DeleteLabelParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the delete label params
func (o *DeleteLabelParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the delete label params
func (o *DeleteLabelParams) WithContext(ctx context.Context) *DeleteLabelParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the delete label params
func (o *DeleteLabelParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the delete label params
func (o *DeleteLabelParams) WithHTTPClient(client *http.Client) *DeleteLabelParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the delete label params
func (o *DeleteLabelParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the delete label params
func (o *DeleteLabelParams) WithXRequestID(xRequestID *string) *DeleteLabelParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the delete label params
func (o *DeleteLabelParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithLabelID adds the labelID to the delete label params
func (o *DeleteLabelParams) WithLabelID(labelID int64) *DeleteLabelParams {
	o.SetLabelID(labelID)
	return o
}

// SetLabelID adds the labelId to the delete label params
func (o *DeleteLabelParams) SetLabelID(labelID int64) {
	o.LabelID = labelID
}

// WriteToRequest writes these params to a swagger request
func (o *DeleteLabelParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param label_id
	if err := r.SetPathParam("label_id", swag.FormatInt64(o.LabelID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
