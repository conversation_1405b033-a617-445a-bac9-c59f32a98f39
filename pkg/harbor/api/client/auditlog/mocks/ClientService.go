// Code generated by mockery v2.28.1. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	auditlog "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/auditlog"

	runtime "github.com/go-openapi/runtime"
)

// MockAuditlogClientService is an autogenerated mock type for the ClientService type
type MockAuditlogClientService struct {
	mock.Mock
}

// ListAuditLogs provides a mock function with given fields: params, authInfo, opts
func (_m *MockAuditlogClientService) ListAuditLogs(params *auditlog.ListAuditLogsParams, authInfo runtime.ClientAuthInfoWriter, opts ...auditlog.ClientOption) (*auditlog.ListAuditLogsOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *auditlog.ListAuditLogsOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*auditlog.ListAuditLogsParams, runtime.ClientAuthInfoWriter, ...auditlog.ClientOption) (*auditlog.ListAuditLogsOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*auditlog.ListAuditLogsParams, runtime.ClientAuthInfoWriter, ...auditlog.ClientOption) *auditlog.ListAuditLogsOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*auditlog.ListAuditLogsOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*auditlog.ListAuditLogsParams, runtime.ClientAuthInfoWriter, ...auditlog.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetTransport provides a mock function with given fields: transport
func (_m *MockAuditlogClientService) SetTransport(transport runtime.ClientTransport) {
	_m.Called(transport)
}

type mockConstructorTestingTNewMockAuditlogClientService interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockAuditlogClientService creates a new instance of MockAuditlogClientService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockAuditlogClientService(t mockConstructorTestingTNewMockAuditlogClientService) *MockAuditlogClientService {
	mock := &MockAuditlogClientService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
