// Code generated by go-swagger; DO NOT EDIT.

package registry

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetRegistryParams creates a new GetRegistryParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetRegistryParams() *GetRegistryParams {
	return &GetRegistryParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetRegistryParamsWithTimeout creates a new GetRegistryParams object
// with the ability to set a timeout on a request.
func NewGetRegistryParamsWithTimeout(timeout time.Duration) *GetRegistryParams {
	return &GetRegistryParams{
		timeout: timeout,
	}
}

// NewGetRegistryParamsWithContext creates a new GetRegistryParams object
// with the ability to set a context for a request.
func NewGetRegistryParamsWithContext(ctx context.Context) *GetRegistryParams {
	return &GetRegistryParams{
		Context: ctx,
	}
}

// NewGetRegistryParamsWithHTTPClient creates a new GetRegistryParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetRegistryParamsWithHTTPClient(client *http.Client) *GetRegistryParams {
	return &GetRegistryParams{
		HTTPClient: client,
	}
}

/* GetRegistryParams contains all the parameters to send to the API endpoint
   for the get registry operation.

   Typically these are written to a http.Request.
*/
type GetRegistryParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* ID.

	   Registry ID

	   Format: int64
	*/
	ID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get registry params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetRegistryParams) WithDefaults() *GetRegistryParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get registry params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetRegistryParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get registry params
func (o *GetRegistryParams) WithTimeout(timeout time.Duration) *GetRegistryParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get registry params
func (o *GetRegistryParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get registry params
func (o *GetRegistryParams) WithContext(ctx context.Context) *GetRegistryParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get registry params
func (o *GetRegistryParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get registry params
func (o *GetRegistryParams) WithHTTPClient(client *http.Client) *GetRegistryParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get registry params
func (o *GetRegistryParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the get registry params
func (o *GetRegistryParams) WithXRequestID(xRequestID *string) *GetRegistryParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the get registry params
func (o *GetRegistryParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithID adds the id to the get registry params
func (o *GetRegistryParams) WithID(id int64) *GetRegistryParams {
	o.SetID(id)
	return o
}

// SetID adds the id to the get registry params
func (o *GetRegistryParams) SetID(id int64) {
	o.ID = id
}

// WriteToRequest writes these params to a swagger request
func (o *GetRegistryParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param id
	if err := r.SetPathParam("id", swag.FormatInt64(o.ID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
