// Code generated by go-swagger; DO NOT EDIT.

package registry

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// PingRegistryReader is a Reader for the PingRegistry structure.
type PingRegistryReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *PingRegistryReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewPingRegistryOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewPingRegistryBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewPingRegistryUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 403:
		result := NewPingRegistryForbidden()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 404:
		result := NewPingRegistryNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewPingRegistryInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewPingRegistryOK creates a PingRegistryOK with default headers values
func NewPingRegistryOK() *PingRegistryOK {
	return &PingRegistryOK{}
}

/* PingRegistryOK describes a response with status code 200, with default header values.

Success
*/
type PingRegistryOK struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string
}

func (o *PingRegistryOK) Error() string {
	return fmt.Sprintf("[POST /registries/ping][%d] pingRegistryOK ", 200)
}

func (o *PingRegistryOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	return nil
}

// NewPingRegistryBadRequest creates a PingRegistryBadRequest with default headers values
func NewPingRegistryBadRequest() *PingRegistryBadRequest {
	return &PingRegistryBadRequest{}
}

/* PingRegistryBadRequest describes a response with status code 400, with default header values.

Bad request
*/
type PingRegistryBadRequest struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingRegistryBadRequest) Error() string {
	return fmt.Sprintf("[POST /registries/ping][%d] pingRegistryBadRequest  %+v", 400, o.Payload)
}
func (o *PingRegistryBadRequest) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingRegistryBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPingRegistryUnauthorized creates a PingRegistryUnauthorized with default headers values
func NewPingRegistryUnauthorized() *PingRegistryUnauthorized {
	return &PingRegistryUnauthorized{}
}

/* PingRegistryUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type PingRegistryUnauthorized struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingRegistryUnauthorized) Error() string {
	return fmt.Sprintf("[POST /registries/ping][%d] pingRegistryUnauthorized  %+v", 401, o.Payload)
}
func (o *PingRegistryUnauthorized) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingRegistryUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPingRegistryForbidden creates a PingRegistryForbidden with default headers values
func NewPingRegistryForbidden() *PingRegistryForbidden {
	return &PingRegistryForbidden{}
}

/* PingRegistryForbidden describes a response with status code 403, with default header values.

Forbidden
*/
type PingRegistryForbidden struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingRegistryForbidden) Error() string {
	return fmt.Sprintf("[POST /registries/ping][%d] pingRegistryForbidden  %+v", 403, o.Payload)
}
func (o *PingRegistryForbidden) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingRegistryForbidden) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPingRegistryNotFound creates a PingRegistryNotFound with default headers values
func NewPingRegistryNotFound() *PingRegistryNotFound {
	return &PingRegistryNotFound{}
}

/* PingRegistryNotFound describes a response with status code 404, with default header values.

Not found
*/
type PingRegistryNotFound struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingRegistryNotFound) Error() string {
	return fmt.Sprintf("[POST /registries/ping][%d] pingRegistryNotFound  %+v", 404, o.Payload)
}
func (o *PingRegistryNotFound) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingRegistryNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPingRegistryInternalServerError creates a PingRegistryInternalServerError with default headers values
func NewPingRegistryInternalServerError() *PingRegistryInternalServerError {
	return &PingRegistryInternalServerError{}
}

/* PingRegistryInternalServerError describes a response with status code 500, with default header values.

Internal server error
*/
type PingRegistryInternalServerError struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingRegistryInternalServerError) Error() string {
	return fmt.Sprintf("[POST /registries/ping][%d] pingRegistryInternalServerError  %+v", 500, o.Payload)
}
func (o *PingRegistryInternalServerError) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingRegistryInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
