// Code generated by go-swagger; DO NOT EDIT.

package oidc

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

// PingOIDCReader is a Reader for the PingOIDC structure.
type PingOIDCReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *PingOIDCReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewPingOIDCOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewPingOIDCBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 401:
		result := NewPingOIDCUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 403:
		result := NewPingOIDCForbidden()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewPingOIDCOK creates a PingOIDCOK with default headers values
func NewPingOIDCOK() *PingOIDCOK {
	return &PingOIDCOK{}
}

/* PingOIDCOK describes a response with status code 200, with default header values.

Success
*/
type PingOIDCOK struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string
}

func (o *PingOIDCOK) Error() string {
	return fmt.Sprintf("[POST /system/oidc/ping][%d] pingOIdCOK ", 200)
}

func (o *PingOIDCOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	return nil
}

// NewPingOIDCBadRequest creates a PingOIDCBadRequest with default headers values
func NewPingOIDCBadRequest() *PingOIDCBadRequest {
	return &PingOIDCBadRequest{}
}

/* PingOIDCBadRequest describes a response with status code 400, with default header values.

Bad request
*/
type PingOIDCBadRequest struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingOIDCBadRequest) Error() string {
	return fmt.Sprintf("[POST /system/oidc/ping][%d] pingOIdCBadRequest  %+v", 400, o.Payload)
}
func (o *PingOIDCBadRequest) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingOIDCBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPingOIDCUnauthorized creates a PingOIDCUnauthorized with default headers values
func NewPingOIDCUnauthorized() *PingOIDCUnauthorized {
	return &PingOIDCUnauthorized{}
}

/* PingOIDCUnauthorized describes a response with status code 401, with default header values.

Unauthorized
*/
type PingOIDCUnauthorized struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingOIDCUnauthorized) Error() string {
	return fmt.Sprintf("[POST /system/oidc/ping][%d] pingOIdCUnauthorized  %+v", 401, o.Payload)
}
func (o *PingOIDCUnauthorized) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingOIDCUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPingOIDCForbidden creates a PingOIDCForbidden with default headers values
func NewPingOIDCForbidden() *PingOIDCForbidden {
	return &PingOIDCForbidden{}
}

/* PingOIDCForbidden describes a response with status code 403, with default header values.

Forbidden
*/
type PingOIDCForbidden struct {

	/* The ID of the corresponding request for the response
	 */
	XRequestID string

	Payload *model.Errors
}

func (o *PingOIDCForbidden) Error() string {
	return fmt.Sprintf("[POST /system/oidc/ping][%d] pingOIdCForbidden  %+v", 403, o.Payload)
}
func (o *PingOIDCForbidden) GetPayload() *model.Errors {
	return o.Payload
}

func (o *PingOIDCForbidden) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// hydrates response header X-Request-Id
	hdrXRequestID := response.GetHeader("X-Request-Id")

	if hdrXRequestID != "" {
		o.XRequestID = hdrXRequestID
	}

	o.Payload = new(model.Errors)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

/*PingOIDCBody ping o ID c body
swagger:model PingOIDCBody
*/
type PingOIDCBody struct {

	// The URL of OIDC endpoint to be tested.
	URL string `json:"url,omitempty"`

	// Whether the certificate should be verified
	VerifyCert bool `json:"verify_cert,omitempty"`
}

// Validate validates this ping o ID c body
func (o *PingOIDCBody) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this ping o ID c body based on context it is used
func (o *PingOIDCBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (o *PingOIDCBody) MarshalBinary() ([]byte, error) {
	if o == nil {
		return nil, nil
	}
	return swag.WriteJSON(o)
}

// UnmarshalBinary interface implementation
func (o *PingOIDCBody) UnmarshalBinary(b []byte) error {
	var res PingOIDCBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*o = res
	return nil
}
