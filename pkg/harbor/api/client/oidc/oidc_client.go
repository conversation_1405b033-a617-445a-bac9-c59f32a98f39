// Code generated by go-swagger; DO NOT EDIT.

package oidc

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new oidc API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for oidc API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

//go:generate mockery --name ClientService --structname MockOidcClientService

// ClientService is the interface for Client methods
type ClientService interface {
	PingOIDC(params *PingOIDCParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*PingOIDCOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
  PingOIDC tests the o ID c endpoint

  Test the OIDC endpoint, the setting of the endpoint is provided in the request.  This API can only be called by system admin.

*/
func (a *Client) PingOIDC(params *PingOIDCParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*PingOIDCOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewPingOIDCParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "pingOIDC",
		Method:             "POST",
		PathPattern:        "/system/oidc/ping",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &PingOIDCReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*PingOIDCOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for pingOIDC: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
