// Code generated by go-swagger; DO NOT EDIT.

package preheat

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetExecutionParams creates a new GetExecutionParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetExecutionParams() *GetExecutionParams {
	return &GetExecutionParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetExecutionParamsWithTimeout creates a new GetExecutionParams object
// with the ability to set a timeout on a request.
func NewGetExecutionParamsWithTimeout(timeout time.Duration) *GetExecutionParams {
	return &GetExecutionParams{
		timeout: timeout,
	}
}

// NewGetExecutionParamsWithContext creates a new GetExecutionParams object
// with the ability to set a context for a request.
func NewGetExecutionParamsWithContext(ctx context.Context) *GetExecutionParams {
	return &GetExecutionParams{
		Context: ctx,
	}
}

// NewGetExecutionParamsWithHTTPClient creates a new GetExecutionParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetExecutionParamsWithHTTPClient(client *http.Client) *GetExecutionParams {
	return &GetExecutionParams{
		HTTPClient: client,
	}
}

/* GetExecutionParams contains all the parameters to send to the API endpoint
   for the get execution operation.

   Typically these are written to a http.Request.
*/
type GetExecutionParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* ExecutionID.

	   Execution ID
	*/
	ExecutionID int64

	/* PreheatPolicyName.

	   Preheat Policy Name
	*/
	PreheatPolicyName string

	/* ProjectName.

	   The name of the project
	*/
	ProjectName string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get execution params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetExecutionParams) WithDefaults() *GetExecutionParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get execution params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetExecutionParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get execution params
func (o *GetExecutionParams) WithTimeout(timeout time.Duration) *GetExecutionParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get execution params
func (o *GetExecutionParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get execution params
func (o *GetExecutionParams) WithContext(ctx context.Context) *GetExecutionParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get execution params
func (o *GetExecutionParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get execution params
func (o *GetExecutionParams) WithHTTPClient(client *http.Client) *GetExecutionParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get execution params
func (o *GetExecutionParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the get execution params
func (o *GetExecutionParams) WithXRequestID(xRequestID *string) *GetExecutionParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the get execution params
func (o *GetExecutionParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithExecutionID adds the executionID to the get execution params
func (o *GetExecutionParams) WithExecutionID(executionID int64) *GetExecutionParams {
	o.SetExecutionID(executionID)
	return o
}

// SetExecutionID adds the executionId to the get execution params
func (o *GetExecutionParams) SetExecutionID(executionID int64) {
	o.ExecutionID = executionID
}

// WithPreheatPolicyName adds the preheatPolicyName to the get execution params
func (o *GetExecutionParams) WithPreheatPolicyName(preheatPolicyName string) *GetExecutionParams {
	o.SetPreheatPolicyName(preheatPolicyName)
	return o
}

// SetPreheatPolicyName adds the preheatPolicyName to the get execution params
func (o *GetExecutionParams) SetPreheatPolicyName(preheatPolicyName string) {
	o.PreheatPolicyName = preheatPolicyName
}

// WithProjectName adds the projectName to the get execution params
func (o *GetExecutionParams) WithProjectName(projectName string) *GetExecutionParams {
	o.SetProjectName(projectName)
	return o
}

// SetProjectName adds the projectName to the get execution params
func (o *GetExecutionParams) SetProjectName(projectName string) {
	o.ProjectName = projectName
}

// WriteToRequest writes these params to a swagger request
func (o *GetExecutionParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param execution_id
	if err := r.SetPathParam("execution_id", swag.FormatInt64(o.ExecutionID)); err != nil {
		return err
	}

	// path param preheat_policy_name
	if err := r.SetPathParam("preheat_policy_name", o.PreheatPolicyName); err != nil {
		return err
	}

	// path param project_name
	if err := r.SetPathParam("project_name", o.ProjectName); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
