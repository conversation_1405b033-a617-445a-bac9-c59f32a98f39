// Code generated by go-swagger; DO NOT EDIT.

package preheat

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewDeleteInstanceParams creates a new DeleteInstanceParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewDeleteInstanceParams() *DeleteInstanceParams {
	return &DeleteInstanceParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewDeleteInstanceParamsWithTimeout creates a new DeleteInstanceParams object
// with the ability to set a timeout on a request.
func NewDeleteInstanceParamsWithTimeout(timeout time.Duration) *DeleteInstanceParams {
	return &DeleteInstanceParams{
		timeout: timeout,
	}
}

// NewDeleteInstanceParamsWithContext creates a new DeleteInstanceParams object
// with the ability to set a context for a request.
func NewDeleteInstanceParamsWithContext(ctx context.Context) *DeleteInstanceParams {
	return &DeleteInstanceParams{
		Context: ctx,
	}
}

// NewDeleteInstanceParamsWithHTTPClient creates a new DeleteInstanceParams object
// with the ability to set a custom HTTPClient for a request.
func NewDeleteInstanceParamsWithHTTPClient(client *http.Client) *DeleteInstanceParams {
	return &DeleteInstanceParams{
		HTTPClient: client,
	}
}

/* DeleteInstanceParams contains all the parameters to send to the API endpoint
   for the delete instance operation.

   Typically these are written to a http.Request.
*/
type DeleteInstanceParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* PreheatInstanceName.

	   Instance Name
	*/
	PreheatInstanceName string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the delete instance params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DeleteInstanceParams) WithDefaults() *DeleteInstanceParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the delete instance params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DeleteInstanceParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the delete instance params
func (o *DeleteInstanceParams) WithTimeout(timeout time.Duration) *DeleteInstanceParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the delete instance params
func (o *DeleteInstanceParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the delete instance params
func (o *DeleteInstanceParams) WithContext(ctx context.Context) *DeleteInstanceParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the delete instance params
func (o *DeleteInstanceParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the delete instance params
func (o *DeleteInstanceParams) WithHTTPClient(client *http.Client) *DeleteInstanceParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the delete instance params
func (o *DeleteInstanceParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the delete instance params
func (o *DeleteInstanceParams) WithXRequestID(xRequestID *string) *DeleteInstanceParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the delete instance params
func (o *DeleteInstanceParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithPreheatInstanceName adds the preheatInstanceName to the delete instance params
func (o *DeleteInstanceParams) WithPreheatInstanceName(preheatInstanceName string) *DeleteInstanceParams {
	o.SetPreheatInstanceName(preheatInstanceName)
	return o
}

// SetPreheatInstanceName adds the preheatInstanceName to the delete instance params
func (o *DeleteInstanceParams) SetPreheatInstanceName(preheatInstanceName string) {
	o.PreheatInstanceName = preheatInstanceName
}

// WriteToRequest writes these params to a swagger request
func (o *DeleteInstanceParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param preheat_instance_name
	if err := r.SetPathParam("preheat_instance_name", o.PreheatInstanceName); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
