// Code generated by go-swagger; DO NOT EDIT.

package preheat

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewListProvidersUnderProjectParams creates a new ListProvidersUnderProjectParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListProvidersUnderProjectParams() *ListProvidersUnderProjectParams {
	return &ListProvidersUnderProjectParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListProvidersUnderProjectParamsWithTimeout creates a new ListProvidersUnderProjectParams object
// with the ability to set a timeout on a request.
func NewListProvidersUnderProjectParamsWithTimeout(timeout time.Duration) *ListProvidersUnderProjectParams {
	return &ListProvidersUnderProjectParams{
		timeout: timeout,
	}
}

// NewListProvidersUnderProjectParamsWithContext creates a new ListProvidersUnderProjectParams object
// with the ability to set a context for a request.
func NewListProvidersUnderProjectParamsWithContext(ctx context.Context) *ListProvidersUnderProjectParams {
	return &ListProvidersUnderProjectParams{
		Context: ctx,
	}
}

// NewListProvidersUnderProjectParamsWithHTTPClient creates a new ListProvidersUnderProjectParams object
// with the ability to set a custom HTTPClient for a request.
func NewListProvidersUnderProjectParamsWithHTTPClient(client *http.Client) *ListProvidersUnderProjectParams {
	return &ListProvidersUnderProjectParams{
		HTTPClient: client,
	}
}

/* ListProvidersUnderProjectParams contains all the parameters to send to the API endpoint
   for the list providers under project operation.

   Typically these are written to a http.Request.
*/
type ListProvidersUnderProjectParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* ProjectName.

	   The name of the project
	*/
	ProjectName string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list providers under project params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListProvidersUnderProjectParams) WithDefaults() *ListProvidersUnderProjectParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list providers under project params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListProvidersUnderProjectParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the list providers under project params
func (o *ListProvidersUnderProjectParams) WithTimeout(timeout time.Duration) *ListProvidersUnderProjectParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list providers under project params
func (o *ListProvidersUnderProjectParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list providers under project params
func (o *ListProvidersUnderProjectParams) WithContext(ctx context.Context) *ListProvidersUnderProjectParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list providers under project params
func (o *ListProvidersUnderProjectParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list providers under project params
func (o *ListProvidersUnderProjectParams) WithHTTPClient(client *http.Client) *ListProvidersUnderProjectParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list providers under project params
func (o *ListProvidersUnderProjectParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the list providers under project params
func (o *ListProvidersUnderProjectParams) WithXRequestID(xRequestID *string) *ListProvidersUnderProjectParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the list providers under project params
func (o *ListProvidersUnderProjectParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithProjectName adds the projectName to the list providers under project params
func (o *ListProvidersUnderProjectParams) WithProjectName(projectName string) *ListProvidersUnderProjectParams {
	o.SetProjectName(projectName)
	return o
}

// SetProjectName adds the projectName to the list providers under project params
func (o *ListProvidersUnderProjectParams) SetProjectName(projectName string) {
	o.ProjectName = projectName
}

// WriteToRequest writes these params to a swagger request
func (o *ListProvidersUnderProjectParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param project_name
	if err := r.SetPathParam("project_name", o.ProjectName); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
