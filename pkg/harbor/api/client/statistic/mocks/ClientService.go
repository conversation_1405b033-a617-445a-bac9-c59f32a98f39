// Code generated by mockery v2.28.1. DO NOT EDIT.

package mocks

import (
	runtime "github.com/go-openapi/runtime"
	mock "github.com/stretchr/testify/mock"

	statistic "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/statistic"
)

// MockStatisticClientService is an autogenerated mock type for the ClientService type
type MockStatisticClientService struct {
	mock.Mock
}

// GetStatistic provides a mock function with given fields: params, authInfo, opts
func (_m *MockStatisticClientService) GetStatistic(params *statistic.GetStatisticParams, authInfo runtime.ClientAuthInfoWriter, opts ...statistic.ClientOption) (*statistic.GetStatisticOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *statistic.GetStatisticOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*statistic.GetStatisticParams, runtime.ClientAuthInfoWriter, ...statistic.ClientOption) (*statistic.GetStatisticOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*statistic.GetStatisticParams, runtime.ClientAuthInfoWriter, ...statistic.ClientOption) *statistic.GetStatisticOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*statistic.GetStatisticOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*statistic.GetStatisticParams, runtime.ClientAuthInfoWriter, ...statistic.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetTransport provides a mock function with given fields: transport
func (_m *MockStatisticClientService) SetTransport(transport runtime.ClientTransport) {
	_m.Called(transport)
}

type mockConstructorTestingTNewMockStatisticClientService interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockStatisticClientService creates a new instance of MockStatisticClientService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockStatisticClientService(t mockConstructorTestingTNewMockStatisticClientService) *MockStatisticClientService {
	mock := &MockStatisticClientService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
