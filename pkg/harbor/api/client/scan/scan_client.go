// Code generated by go-swagger; DO NOT EDIT.

package scan

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new scan API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for scan API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

//go:generate mockery --name ClientService --structname MockScanClientService

// ClientService is the interface for Client methods
type ClientService interface {
	GetReportLog(params *GetReportLogParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetReportLogOK, error)

	ScanArtifact(params *ScanArtifactParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ScanArtifactAccepted, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
  GetReportLog gets the log of the scan report

  Get the log of the scan report
*/
func (a *Client) GetReportLog(params *GetReportLogParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetReportLogOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetReportLogParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "getReportLog",
		Method:             "GET",
		PathPattern:        "/projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/scan/{report_id}/log",
		ProducesMediaTypes: []string{"text/plain"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &GetReportLogReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetReportLogOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for getReportLog: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  ScanArtifact scans the artifact

  Scan the specified artifact
*/
func (a *Client) ScanArtifact(params *ScanArtifactParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ScanArtifactAccepted, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewScanArtifactParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "scanArtifact",
		Method:             "POST",
		PathPattern:        "/projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/scan",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ScanArtifactReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ScanArtifactAccepted)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for scanArtifact: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
