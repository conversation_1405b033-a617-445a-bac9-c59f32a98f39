// Code generated by go-swagger; DO NOT EDIT.

package webhook

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new webhook API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for webhook API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

//go:generate mockery --name ClientService --structname MockWebhookClientService

// ClientService is the interface for Client methods
type ClientService interface {
	CreateWebhookPolicyOfProject(params *CreateWebhookPolicyOfProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*CreateWebhookPolicyOfProjectCreated, error)

	DeleteWebhookPolicyOfProject(params *DeleteWebhookPolicyOfProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*DeleteWebhookPolicyOfProjectOK, error)

	GetSupportedEventTypes(params *GetSupportedEventTypesParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetSupportedEventTypesOK, error)

	GetWebhookPolicyOfProject(params *GetWebhookPolicyOfProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetWebhookPolicyOfProjectOK, error)

	LastTrigger(params *LastTriggerParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*LastTriggerOK, error)

	ListWebhookPoliciesOfProject(params *ListWebhookPoliciesOfProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ListWebhookPoliciesOfProjectOK, error)

	UpdateWebhookPolicyOfProject(params *UpdateWebhookPolicyOfProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*UpdateWebhookPolicyOfProjectOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
  CreateWebhookPolicyOfProject creates project webhook policy

  This endpoint create a webhook policy if the project does not have one.

*/
func (a *Client) CreateWebhookPolicyOfProject(params *CreateWebhookPolicyOfProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*CreateWebhookPolicyOfProjectCreated, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewCreateWebhookPolicyOfProjectParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "CreateWebhookPolicyOfProject",
		Method:             "POST",
		PathPattern:        "/projects/{project_name_or_id}/webhook/policies",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &CreateWebhookPolicyOfProjectReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*CreateWebhookPolicyOfProjectCreated)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for CreateWebhookPolicyOfProject: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  DeleteWebhookPolicyOfProject deletes webhook policy of a project

  This endpoint is aimed to delete webhookpolicy of a project.

*/
func (a *Client) DeleteWebhookPolicyOfProject(params *DeleteWebhookPolicyOfProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*DeleteWebhookPolicyOfProjectOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewDeleteWebhookPolicyOfProjectParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "DeleteWebhookPolicyOfProject",
		Method:             "DELETE",
		PathPattern:        "/projects/{project_name_or_id}/webhook/policies/{webhook_policy_id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &DeleteWebhookPolicyOfProjectReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*DeleteWebhookPolicyOfProjectOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for DeleteWebhookPolicyOfProject: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  GetSupportedEventTypes gets supported event types and notify types

  Get supportted event types and notify types.
*/
func (a *Client) GetSupportedEventTypes(params *GetSupportedEventTypesParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetSupportedEventTypesOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetSupportedEventTypesParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "GetSupportedEventTypes",
		Method:             "GET",
		PathPattern:        "/projects/{project_name_or_id}/webhook/events",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &GetSupportedEventTypesReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetSupportedEventTypesOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for GetSupportedEventTypes: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  GetWebhookPolicyOfProject gets project webhook policy

  This endpoint returns specified webhook policy of a project.

*/
func (a *Client) GetWebhookPolicyOfProject(params *GetWebhookPolicyOfProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetWebhookPolicyOfProjectOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetWebhookPolicyOfProjectParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "GetWebhookPolicyOfProject",
		Method:             "GET",
		PathPattern:        "/projects/{project_name_or_id}/webhook/policies/{webhook_policy_id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &GetWebhookPolicyOfProjectReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetWebhookPolicyOfProjectOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for GetWebhookPolicyOfProject: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  LastTrigger gets project webhook policy last trigger info

  This endpoint returns last trigger information of project webhook policy.

*/
func (a *Client) LastTrigger(params *LastTriggerParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*LastTriggerOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewLastTriggerParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "LastTrigger",
		Method:             "GET",
		PathPattern:        "/projects/{project_name_or_id}/webhook/lasttrigger",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &LastTriggerReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*LastTriggerOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for LastTrigger: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  ListWebhookPoliciesOfProject lists project webhook policies

  This endpoint returns webhook policies of a project.

*/
func (a *Client) ListWebhookPoliciesOfProject(params *ListWebhookPoliciesOfProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ListWebhookPoliciesOfProjectOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListWebhookPoliciesOfProjectParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "ListWebhookPoliciesOfProject",
		Method:             "GET",
		PathPattern:        "/projects/{project_name_or_id}/webhook/policies",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ListWebhookPoliciesOfProjectReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListWebhookPoliciesOfProjectOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for ListWebhookPoliciesOfProject: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  UpdateWebhookPolicyOfProject updates webhook policy of a project

  This endpoint is aimed to update the webhook policy of a project.

*/
func (a *Client) UpdateWebhookPolicyOfProject(params *UpdateWebhookPolicyOfProjectParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*UpdateWebhookPolicyOfProjectOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewUpdateWebhookPolicyOfProjectParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "UpdateWebhookPolicyOfProject",
		Method:             "PUT",
		PathPattern:        "/projects/{project_name_or_id}/webhook/policies/{webhook_policy_id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &UpdateWebhookPolicyOfProjectReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*UpdateWebhookPolicyOfProjectOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for UpdateWebhookPolicyOfProject: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
