// Code generated by go-swagger; DO NOT EDIT.

package webhook

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetSupportedEventTypesParams creates a new GetSupportedEventTypesParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetSupportedEventTypesParams() *GetSupportedEventTypesParams {
	return &GetSupportedEventTypesParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetSupportedEventTypesParamsWithTimeout creates a new GetSupportedEventTypesParams object
// with the ability to set a timeout on a request.
func NewGetSupportedEventTypesParamsWithTimeout(timeout time.Duration) *GetSupportedEventTypesParams {
	return &GetSupportedEventTypesParams{
		timeout: timeout,
	}
}

// NewGetSupportedEventTypesParamsWithContext creates a new GetSupportedEventTypesParams object
// with the ability to set a context for a request.
func NewGetSupportedEventTypesParamsWithContext(ctx context.Context) *GetSupportedEventTypesParams {
	return &GetSupportedEventTypesParams{
		Context: ctx,
	}
}

// NewGetSupportedEventTypesParamsWithHTTPClient creates a new GetSupportedEventTypesParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetSupportedEventTypesParamsWithHTTPClient(client *http.Client) *GetSupportedEventTypesParams {
	return &GetSupportedEventTypesParams{
		HTTPClient: client,
	}
}

/* GetSupportedEventTypesParams contains all the parameters to send to the API endpoint
   for the get supported event types operation.

   Typically these are written to a http.Request.
*/
type GetSupportedEventTypesParams struct {

	/* XIsResourceName.

	   The flag to indicate whether the parameter which supports both name and id in the path is the name of the resource. When the X-Is-Resource-Name is false and the parameter can be converted to an integer, the parameter will be as an id, otherwise, it will be as a name.
	*/
	XIsResourceName *bool

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* ProjectNameOrID.

	   The name or id of the project
	*/
	ProjectNameOrID string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get supported event types params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetSupportedEventTypesParams) WithDefaults() *GetSupportedEventTypesParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get supported event types params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetSupportedEventTypesParams) SetDefaults() {
	var (
		xIsResourceNameDefault = bool(false)
	)

	val := GetSupportedEventTypesParams{
		XIsResourceName: &xIsResourceNameDefault,
	}

	val.timeout = o.timeout
	val.Context = o.Context
	val.HTTPClient = o.HTTPClient
	*o = val
}

// WithTimeout adds the timeout to the get supported event types params
func (o *GetSupportedEventTypesParams) WithTimeout(timeout time.Duration) *GetSupportedEventTypesParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get supported event types params
func (o *GetSupportedEventTypesParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get supported event types params
func (o *GetSupportedEventTypesParams) WithContext(ctx context.Context) *GetSupportedEventTypesParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get supported event types params
func (o *GetSupportedEventTypesParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get supported event types params
func (o *GetSupportedEventTypesParams) WithHTTPClient(client *http.Client) *GetSupportedEventTypesParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get supported event types params
func (o *GetSupportedEventTypesParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXIsResourceName adds the xIsResourceName to the get supported event types params
func (o *GetSupportedEventTypesParams) WithXIsResourceName(xIsResourceName *bool) *GetSupportedEventTypesParams {
	o.SetXIsResourceName(xIsResourceName)
	return o
}

// SetXIsResourceName adds the xIsResourceName to the get supported event types params
func (o *GetSupportedEventTypesParams) SetXIsResourceName(xIsResourceName *bool) {
	o.XIsResourceName = xIsResourceName
}

// WithXRequestID adds the xRequestID to the get supported event types params
func (o *GetSupportedEventTypesParams) WithXRequestID(xRequestID *string) *GetSupportedEventTypesParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the get supported event types params
func (o *GetSupportedEventTypesParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithProjectNameOrID adds the projectNameOrID to the get supported event types params
func (o *GetSupportedEventTypesParams) WithProjectNameOrID(projectNameOrID string) *GetSupportedEventTypesParams {
	o.SetProjectNameOrID(projectNameOrID)
	return o
}

// SetProjectNameOrID adds the projectNameOrId to the get supported event types params
func (o *GetSupportedEventTypesParams) SetProjectNameOrID(projectNameOrID string) {
	o.ProjectNameOrID = projectNameOrID
}

// WriteToRequest writes these params to a swagger request
func (o *GetSupportedEventTypesParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XIsResourceName != nil {

		// header param X-Is-Resource-Name
		if err := r.SetHeaderParam("X-Is-Resource-Name", swag.FormatBool(*o.XIsResourceName)); err != nil {
			return err
		}
	}

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param project_name_or_id
	if err := r.SetPathParam("project_name_or_id", o.ProjectNameOrID); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
