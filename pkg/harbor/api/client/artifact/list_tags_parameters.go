// Code generated by go-swagger; DO NOT EDIT.

package artifact

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewListTagsParams creates a new ListTagsParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListTagsParams() *ListTagsParams {
	return &ListTagsParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListTagsParamsWithTimeout creates a new ListTagsParams object
// with the ability to set a timeout on a request.
func NewListTagsParamsWithTimeout(timeout time.Duration) *ListTagsParams {
	return &ListTagsParams{
		timeout: timeout,
	}
}

// NewListTagsParamsWithContext creates a new ListTagsParams object
// with the ability to set a context for a request.
func NewListTagsParamsWithContext(ctx context.Context) *ListTagsParams {
	return &ListTagsParams{
		Context: ctx,
	}
}

// NewListTagsParamsWithHTTPClient creates a new ListTagsParams object
// with the ability to set a custom HTTPClient for a request.
func NewListTagsParamsWithHTTPClient(client *http.Client) *ListTagsParams {
	return &ListTagsParams{
		HTTPClient: client,
	}
}

/* ListTagsParams contains all the parameters to send to the API endpoint
   for the list tags operation.

   Typically these are written to a http.Request.
*/
type ListTagsParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* Page.

	   The page number

	   Format: int64
	   Default: 1
	*/
	Page *int64

	/* PageSize.

	   The size of per page

	   Format: int64
	   Default: 10
	*/
	PageSize *int64

	/* ProjectName.

	   The name of the project
	*/
	ProjectName string

	/* Q.

	   Query string to query resources. Supported query patterns are "exact match(k=v)", "fuzzy match(k=~v)", "range(k=[min~max])", "list with union releationship(k={v1 v2 v3})" and "list with intersetion relationship(k=(v1 v2 v3))". The value of range and list can be string(enclosed by " or '), integer or time(in format "2020-04-09 02:36:00"). All of these query patterns should be put in the query string "q=xxx" and splitted by ",". e.g. q=k1=v1,k2=~v2,k3=[min~max]
	*/
	Q *string

	/* Reference.

	   The reference of the artifact, can be digest or tag
	*/
	Reference string

	/* RepositoryName.

	   The name of the repository. If it contains slash, encode it with URL encoding. e.g. a/b -> a%252Fb
	*/
	RepositoryName string

	/* Sort.

	   Sort the resource list in ascending or descending order. e.g. sort by field1 in ascending orderr and field2 in descending order with "sort=field1,-field2"
	*/
	Sort *string

	/* WithImmutableStatus.

	   Specify whether the immutable status is included inside the returning tags
	*/
	WithImmutableStatus *bool

	/* WithSignature.

	   Specify whether the signature is included inside the returning tags
	*/
	WithSignature *bool

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list tags params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListTagsParams) WithDefaults() *ListTagsParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list tags params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListTagsParams) SetDefaults() {
	var (
		pageDefault = int64(1)

		pageSizeDefault = int64(10)

		withImmutableStatusDefault = bool(false)

		withSignatureDefault = bool(false)
	)

	val := ListTagsParams{
		Page:                &pageDefault,
		PageSize:            &pageSizeDefault,
		WithImmutableStatus: &withImmutableStatusDefault,
		WithSignature:       &withSignatureDefault,
	}

	val.timeout = o.timeout
	val.Context = o.Context
	val.HTTPClient = o.HTTPClient
	*o = val
}

// WithTimeout adds the timeout to the list tags params
func (o *ListTagsParams) WithTimeout(timeout time.Duration) *ListTagsParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list tags params
func (o *ListTagsParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list tags params
func (o *ListTagsParams) WithContext(ctx context.Context) *ListTagsParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list tags params
func (o *ListTagsParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list tags params
func (o *ListTagsParams) WithHTTPClient(client *http.Client) *ListTagsParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list tags params
func (o *ListTagsParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the list tags params
func (o *ListTagsParams) WithXRequestID(xRequestID *string) *ListTagsParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the list tags params
func (o *ListTagsParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithPage adds the page to the list tags params
func (o *ListTagsParams) WithPage(page *int64) *ListTagsParams {
	o.SetPage(page)
	return o
}

// SetPage adds the page to the list tags params
func (o *ListTagsParams) SetPage(page *int64) {
	o.Page = page
}

// WithPageSize adds the pageSize to the list tags params
func (o *ListTagsParams) WithPageSize(pageSize *int64) *ListTagsParams {
	o.SetPageSize(pageSize)
	return o
}

// SetPageSize adds the pageSize to the list tags params
func (o *ListTagsParams) SetPageSize(pageSize *int64) {
	o.PageSize = pageSize
}

// WithProjectName adds the projectName to the list tags params
func (o *ListTagsParams) WithProjectName(projectName string) *ListTagsParams {
	o.SetProjectName(projectName)
	return o
}

// SetProjectName adds the projectName to the list tags params
func (o *ListTagsParams) SetProjectName(projectName string) {
	o.ProjectName = projectName
}

// WithQ adds the q to the list tags params
func (o *ListTagsParams) WithQ(q *string) *ListTagsParams {
	o.SetQ(q)
	return o
}

// SetQ adds the q to the list tags params
func (o *ListTagsParams) SetQ(q *string) {
	o.Q = q
}

// WithReference adds the reference to the list tags params
func (o *ListTagsParams) WithReference(reference string) *ListTagsParams {
	o.SetReference(reference)
	return o
}

// SetReference adds the reference to the list tags params
func (o *ListTagsParams) SetReference(reference string) {
	o.Reference = reference
}

// WithRepositoryName adds the repositoryName to the list tags params
func (o *ListTagsParams) WithRepositoryName(repositoryName string) *ListTagsParams {
	o.SetRepositoryName(repositoryName)
	return o
}

// SetRepositoryName adds the repositoryName to the list tags params
func (o *ListTagsParams) SetRepositoryName(repositoryName string) {
	o.RepositoryName = repositoryName
}

// WithSort adds the sort to the list tags params
func (o *ListTagsParams) WithSort(sort *string) *ListTagsParams {
	o.SetSort(sort)
	return o
}

// SetSort adds the sort to the list tags params
func (o *ListTagsParams) SetSort(sort *string) {
	o.Sort = sort
}

// WithWithImmutableStatus adds the withImmutableStatus to the list tags params
func (o *ListTagsParams) WithWithImmutableStatus(withImmutableStatus *bool) *ListTagsParams {
	o.SetWithImmutableStatus(withImmutableStatus)
	return o
}

// SetWithImmutableStatus adds the withImmutableStatus to the list tags params
func (o *ListTagsParams) SetWithImmutableStatus(withImmutableStatus *bool) {
	o.WithImmutableStatus = withImmutableStatus
}

// WithWithSignature adds the withSignature to the list tags params
func (o *ListTagsParams) WithWithSignature(withSignature *bool) *ListTagsParams {
	o.SetWithSignature(withSignature)
	return o
}

// SetWithSignature adds the withSignature to the list tags params
func (o *ListTagsParams) SetWithSignature(withSignature *bool) {
	o.WithSignature = withSignature
}

// WriteToRequest writes these params to a swagger request
func (o *ListTagsParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	if o.Page != nil {

		// query param page
		var qrPage int64

		if o.Page != nil {
			qrPage = *o.Page
		}
		qPage := swag.FormatInt64(qrPage)
		if qPage != "" {

			if err := r.SetQueryParam("page", qPage); err != nil {
				return err
			}
		}
	}

	if o.PageSize != nil {

		// query param page_size
		var qrPageSize int64

		if o.PageSize != nil {
			qrPageSize = *o.PageSize
		}
		qPageSize := swag.FormatInt64(qrPageSize)
		if qPageSize != "" {

			if err := r.SetQueryParam("page_size", qPageSize); err != nil {
				return err
			}
		}
	}

	// path param project_name
	if err := r.SetPathParam("project_name", o.ProjectName); err != nil {
		return err
	}

	if o.Q != nil {

		// query param q
		var qrQ string

		if o.Q != nil {
			qrQ = *o.Q
		}
		qQ := qrQ
		if qQ != "" {

			if err := r.SetQueryParam("q", qQ); err != nil {
				return err
			}
		}
	}

	// path param reference
	if err := r.SetPathParam("reference", o.Reference); err != nil {
		return err
	}

	// path param repository_name
	if err := r.SetPathParam("repository_name", o.RepositoryName); err != nil {
		return err
	}

	if o.Sort != nil {

		// query param sort
		var qrSort string

		if o.Sort != nil {
			qrSort = *o.Sort
		}
		qSort := qrSort
		if qSort != "" {

			if err := r.SetQueryParam("sort", qSort); err != nil {
				return err
			}
		}
	}

	if o.WithImmutableStatus != nil {

		// query param with_immutable_status
		var qrWithImmutableStatus bool

		if o.WithImmutableStatus != nil {
			qrWithImmutableStatus = *o.WithImmutableStatus
		}
		qWithImmutableStatus := swag.FormatBool(qrWithImmutableStatus)
		if qWithImmutableStatus != "" {

			if err := r.SetQueryParam("with_immutable_status", qWithImmutableStatus); err != nil {
				return err
			}
		}
	}

	if o.WithSignature != nil {

		// query param with_signature
		var qrWithSignature bool

		if o.WithSignature != nil {
			qrWithSignature = *o.WithSignature
		}
		qWithSignature := swag.FormatBool(qrWithSignature)
		if qWithSignature != "" {

			if err := r.SetQueryParam("with_signature", qWithSignature); err != nil {
				return err
			}
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
