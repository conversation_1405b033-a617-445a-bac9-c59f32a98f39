// Code generated by go-swagger; DO NOT EDIT.

package artifact

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewDeleteTagParams creates a new DeleteTagParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewDeleteTagParams() *DeleteTagParams {
	return &DeleteTagParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewDeleteTagParamsWithTimeout creates a new DeleteTagParams object
// with the ability to set a timeout on a request.
func NewDeleteTagParamsWithTimeout(timeout time.Duration) *DeleteTagParams {
	return &DeleteTagParams{
		timeout: timeout,
	}
}

// NewDeleteTagParamsWithContext creates a new DeleteTagParams object
// with the ability to set a context for a request.
func NewDeleteTagParamsWithContext(ctx context.Context) *DeleteTagParams {
	return &DeleteTagParams{
		Context: ctx,
	}
}

// NewDeleteTagParamsWithHTTPClient creates a new DeleteTagParams object
// with the ability to set a custom HTTPClient for a request.
func NewDeleteTagParamsWithHTTPClient(client *http.Client) *DeleteTagParams {
	return &DeleteTagParams{
		HTTPClient: client,
	}
}

/* DeleteTagParams contains all the parameters to send to the API endpoint
   for the delete tag operation.

   Typically these are written to a http.Request.
*/
type DeleteTagParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* ProjectName.

	   The name of the project
	*/
	ProjectName string

	/* Reference.

	   The reference of the artifact, can be digest or tag
	*/
	Reference string

	/* RepositoryName.

	   The name of the repository. If it contains slash, encode it with URL encoding. e.g. a/b -> a%252Fb
	*/
	RepositoryName string

	/* TagName.

	   The name of the tag
	*/
	TagName string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the delete tag params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DeleteTagParams) WithDefaults() *DeleteTagParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the delete tag params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DeleteTagParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the delete tag params
func (o *DeleteTagParams) WithTimeout(timeout time.Duration) *DeleteTagParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the delete tag params
func (o *DeleteTagParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the delete tag params
func (o *DeleteTagParams) WithContext(ctx context.Context) *DeleteTagParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the delete tag params
func (o *DeleteTagParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the delete tag params
func (o *DeleteTagParams) WithHTTPClient(client *http.Client) *DeleteTagParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the delete tag params
func (o *DeleteTagParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the delete tag params
func (o *DeleteTagParams) WithXRequestID(xRequestID *string) *DeleteTagParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the delete tag params
func (o *DeleteTagParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithProjectName adds the projectName to the delete tag params
func (o *DeleteTagParams) WithProjectName(projectName string) *DeleteTagParams {
	o.SetProjectName(projectName)
	return o
}

// SetProjectName adds the projectName to the delete tag params
func (o *DeleteTagParams) SetProjectName(projectName string) {
	o.ProjectName = projectName
}

// WithReference adds the reference to the delete tag params
func (o *DeleteTagParams) WithReference(reference string) *DeleteTagParams {
	o.SetReference(reference)
	return o
}

// SetReference adds the reference to the delete tag params
func (o *DeleteTagParams) SetReference(reference string) {
	o.Reference = reference
}

// WithRepositoryName adds the repositoryName to the delete tag params
func (o *DeleteTagParams) WithRepositoryName(repositoryName string) *DeleteTagParams {
	o.SetRepositoryName(repositoryName)
	return o
}

// SetRepositoryName adds the repositoryName to the delete tag params
func (o *DeleteTagParams) SetRepositoryName(repositoryName string) {
	o.RepositoryName = repositoryName
}

// WithTagName adds the tagName to the delete tag params
func (o *DeleteTagParams) WithTagName(tagName string) *DeleteTagParams {
	o.SetTagName(tagName)
	return o
}

// SetTagName adds the tagName to the delete tag params
func (o *DeleteTagParams) SetTagName(tagName string) {
	o.TagName = tagName
}

// WriteToRequest writes these params to a swagger request
func (o *DeleteTagParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param project_name
	if err := r.SetPathParam("project_name", o.ProjectName); err != nil {
		return err
	}

	// path param reference
	if err := r.SetPathParam("reference", o.Reference); err != nil {
		return err
	}

	// path param repository_name
	if err := r.SetPathParam("repository_name", o.RepositoryName); err != nil {
		return err
	}

	// path param tag_name
	if err := r.SetPathParam("tag_name", o.TagName); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
