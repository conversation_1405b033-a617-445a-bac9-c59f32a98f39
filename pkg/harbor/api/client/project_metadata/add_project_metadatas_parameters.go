// Code generated by go-swagger; DO NOT EDIT.

package project_metadata

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewAddProjectMetadatasParams creates a new AddProjectMetadatasParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewAddProjectMetadatasParams() *AddProjectMetadatasParams {
	return &AddProjectMetadatasParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewAddProjectMetadatasParamsWithTimeout creates a new AddProjectMetadatasParams object
// with the ability to set a timeout on a request.
func NewAddProjectMetadatasParamsWithTimeout(timeout time.Duration) *AddProjectMetadatasParams {
	return &AddProjectMetadatasParams{
		timeout: timeout,
	}
}

// NewAddProjectMetadatasParamsWithContext creates a new AddProjectMetadatasParams object
// with the ability to set a context for a request.
func NewAddProjectMetadatasParamsWithContext(ctx context.Context) *AddProjectMetadatasParams {
	return &AddProjectMetadatasParams{
		Context: ctx,
	}
}

// NewAddProjectMetadatasParamsWithHTTPClient creates a new AddProjectMetadatasParams object
// with the ability to set a custom HTTPClient for a request.
func NewAddProjectMetadatasParamsWithHTTPClient(client *http.Client) *AddProjectMetadatasParams {
	return &AddProjectMetadatasParams{
		HTTPClient: client,
	}
}

/* AddProjectMetadatasParams contains all the parameters to send to the API endpoint
   for the add project metadatas operation.

   Typically these are written to a http.Request.
*/
type AddProjectMetadatasParams struct {

	/* XIsResourceName.

	   The flag to indicate whether the parameter which supports both name and id in the path is the name of the resource. When the X-Is-Resource-Name is false and the parameter can be converted to an integer, the parameter will be as an id, otherwise, it will be as a name.
	*/
	XIsResourceName *bool

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	// Metadata.
	Metadata map[string]string

	/* ProjectNameOrID.

	   The name or id of the project
	*/
	ProjectNameOrID string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the add project metadatas params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *AddProjectMetadatasParams) WithDefaults() *AddProjectMetadatasParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the add project metadatas params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *AddProjectMetadatasParams) SetDefaults() {
	var (
		xIsResourceNameDefault = bool(false)
	)

	val := AddProjectMetadatasParams{
		XIsResourceName: &xIsResourceNameDefault,
	}

	val.timeout = o.timeout
	val.Context = o.Context
	val.HTTPClient = o.HTTPClient
	*o = val
}

// WithTimeout adds the timeout to the add project metadatas params
func (o *AddProjectMetadatasParams) WithTimeout(timeout time.Duration) *AddProjectMetadatasParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the add project metadatas params
func (o *AddProjectMetadatasParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the add project metadatas params
func (o *AddProjectMetadatasParams) WithContext(ctx context.Context) *AddProjectMetadatasParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the add project metadatas params
func (o *AddProjectMetadatasParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the add project metadatas params
func (o *AddProjectMetadatasParams) WithHTTPClient(client *http.Client) *AddProjectMetadatasParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the add project metadatas params
func (o *AddProjectMetadatasParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXIsResourceName adds the xIsResourceName to the add project metadatas params
func (o *AddProjectMetadatasParams) WithXIsResourceName(xIsResourceName *bool) *AddProjectMetadatasParams {
	o.SetXIsResourceName(xIsResourceName)
	return o
}

// SetXIsResourceName adds the xIsResourceName to the add project metadatas params
func (o *AddProjectMetadatasParams) SetXIsResourceName(xIsResourceName *bool) {
	o.XIsResourceName = xIsResourceName
}

// WithXRequestID adds the xRequestID to the add project metadatas params
func (o *AddProjectMetadatasParams) WithXRequestID(xRequestID *string) *AddProjectMetadatasParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the add project metadatas params
func (o *AddProjectMetadatasParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithMetadata adds the metadata to the add project metadatas params
func (o *AddProjectMetadatasParams) WithMetadata(metadata map[string]string) *AddProjectMetadatasParams {
	o.SetMetadata(metadata)
	return o
}

// SetMetadata adds the metadata to the add project metadatas params
func (o *AddProjectMetadatasParams) SetMetadata(metadata map[string]string) {
	o.Metadata = metadata
}

// WithProjectNameOrID adds the projectNameOrID to the add project metadatas params
func (o *AddProjectMetadatasParams) WithProjectNameOrID(projectNameOrID string) *AddProjectMetadatasParams {
	o.SetProjectNameOrID(projectNameOrID)
	return o
}

// SetProjectNameOrID adds the projectNameOrId to the add project metadatas params
func (o *AddProjectMetadatasParams) SetProjectNameOrID(projectNameOrID string) {
	o.ProjectNameOrID = projectNameOrID
}

// WriteToRequest writes these params to a swagger request
func (o *AddProjectMetadatasParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XIsResourceName != nil {

		// header param X-Is-Resource-Name
		if err := r.SetHeaderParam("X-Is-Resource-Name", swag.FormatBool(*o.XIsResourceName)); err != nil {
			return err
		}
	}

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}
	if o.Metadata != nil {
		if err := r.SetBodyParam(o.Metadata); err != nil {
			return err
		}
	}

	// path param project_name_or_id
	if err := r.SetPathParam("project_name_or_id", o.ProjectNameOrID); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
