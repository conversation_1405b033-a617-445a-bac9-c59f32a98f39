// Code generated by go-swagger; DO NOT EDIT.

package project_metadata

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new project metadata API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for project metadata API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

//go:generate mockery --name ClientService --structname MockProjectMetadataClientService

// ClientService is the interface for Client methods
type ClientService interface {
	AddProjectMetadatas(params *AddProjectMetadatasParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*AddProjectMetadatasOK, error)

	DeleteProjectMetadata(params *DeleteProjectMetadataParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*DeleteProjectMetadataOK, error)

	GetProjectMetadata(params *GetProjectMetadataParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetProjectMetadataOK, error)

	ListProjectMetadatas(params *ListProjectMetadatasParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ListProjectMetadatasOK, error)

	UpdateProjectMetadata(params *UpdateProjectMetadataParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*UpdateProjectMetadataOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
  AddProjectMetadatas adds metadata for the specific project

  Add metadata for the specific project
*/
func (a *Client) AddProjectMetadatas(params *AddProjectMetadatasParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*AddProjectMetadatasOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewAddProjectMetadatasParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "addProjectMetadatas",
		Method:             "POST",
		PathPattern:        "/projects/{project_name_or_id}/metadatas/",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &AddProjectMetadatasReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*AddProjectMetadatasOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for addProjectMetadatas: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  DeleteProjectMetadata deletes the specific metadata for the specific project

  Delete the specific metadata for the specific project
*/
func (a *Client) DeleteProjectMetadata(params *DeleteProjectMetadataParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*DeleteProjectMetadataOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewDeleteProjectMetadataParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "deleteProjectMetadata",
		Method:             "DELETE",
		PathPattern:        "/projects/{project_name_or_id}/metadatas/{meta_name}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &DeleteProjectMetadataReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*DeleteProjectMetadataOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for deleteProjectMetadata: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  GetProjectMetadata gets the specific metadata of the specific project

  Get the specific metadata of the specific project
*/
func (a *Client) GetProjectMetadata(params *GetProjectMetadataParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetProjectMetadataOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetProjectMetadataParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "getProjectMetadata",
		Method:             "GET",
		PathPattern:        "/projects/{project_name_or_id}/metadatas/{meta_name}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &GetProjectMetadataReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetProjectMetadataOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for getProjectMetadata: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  ListProjectMetadatas gets the metadata of the specific project

  Get the metadata of the specific project
*/
func (a *Client) ListProjectMetadatas(params *ListProjectMetadatasParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ListProjectMetadatasOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListProjectMetadatasParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "listProjectMetadatas",
		Method:             "GET",
		PathPattern:        "/projects/{project_name_or_id}/metadatas/",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ListProjectMetadatasReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListProjectMetadatasOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for listProjectMetadatas: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  UpdateProjectMetadata updates the specific metadata for the specific project

  Update the specific metadata for the specific project
*/
func (a *Client) UpdateProjectMetadata(params *UpdateProjectMetadataParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*UpdateProjectMetadataOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewUpdateProjectMetadataParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "updateProjectMetadata",
		Method:             "PUT",
		PathPattern:        "/projects/{project_name_or_id}/metadatas/{meta_name}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &UpdateProjectMetadataReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*UpdateProjectMetadataOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for updateProjectMetadata: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
