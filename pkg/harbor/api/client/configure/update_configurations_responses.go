// Code generated by go-swagger; DO NOT EDIT.

package configure

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// UpdateConfigurationsReader is a Reader for the UpdateConfigurations structure.
type UpdateConfigurationsReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *UpdateConfigurationsReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewUpdateConfigurationsOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 401:
		result := NewUpdateConfigurationsUnauthorized()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 403:
		result := NewUpdateConfigurationsForbidden()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	case 500:
		result := NewUpdateConfigurationsInternalServerError()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		return nil, runtime.NewAPIError("response status code does not match any response statuses defined for this endpoint in the swagger spec", response, response.Code())
	}
}

// NewUpdateConfigurationsOK creates a UpdateConfigurationsOK with default headers values
func NewUpdateConfigurationsOK() *UpdateConfigurationsOK {
	return &UpdateConfigurationsOK{}
}

/* UpdateConfigurationsOK describes a response with status code 200, with default header values.

Modify system configurations successfully.
*/
type UpdateConfigurationsOK struct {
}

func (o *UpdateConfigurationsOK) Error() string {
	return fmt.Sprintf("[PUT /configurations][%d] updateConfigurationsOK ", 200)
}

func (o *UpdateConfigurationsOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewUpdateConfigurationsUnauthorized creates a UpdateConfigurationsUnauthorized with default headers values
func NewUpdateConfigurationsUnauthorized() *UpdateConfigurationsUnauthorized {
	return &UpdateConfigurationsUnauthorized{}
}

/* UpdateConfigurationsUnauthorized describes a response with status code 401, with default header values.

User need to log in first.
*/
type UpdateConfigurationsUnauthorized struct {
}

func (o *UpdateConfigurationsUnauthorized) Error() string {
	return fmt.Sprintf("[PUT /configurations][%d] updateConfigurationsUnauthorized ", 401)
}

func (o *UpdateConfigurationsUnauthorized) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewUpdateConfigurationsForbidden creates a UpdateConfigurationsForbidden with default headers values
func NewUpdateConfigurationsForbidden() *UpdateConfigurationsForbidden {
	return &UpdateConfigurationsForbidden{}
}

/* UpdateConfigurationsForbidden describes a response with status code 403, with default header values.

User does not have permission of admin role.
*/
type UpdateConfigurationsForbidden struct {
}

func (o *UpdateConfigurationsForbidden) Error() string {
	return fmt.Sprintf("[PUT /configurations][%d] updateConfigurationsForbidden ", 403)
}

func (o *UpdateConfigurationsForbidden) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewUpdateConfigurationsInternalServerError creates a UpdateConfigurationsInternalServerError with default headers values
func NewUpdateConfigurationsInternalServerError() *UpdateConfigurationsInternalServerError {
	return &UpdateConfigurationsInternalServerError{}
}

/* UpdateConfigurationsInternalServerError describes a response with status code 500, with default header values.

Unexpected internal errors.
*/
type UpdateConfigurationsInternalServerError struct {
}

func (o *UpdateConfigurationsInternalServerError) Error() string {
	return fmt.Sprintf("[PUT /configurations][%d] updateConfigurationsInternalServerError ", 500)
}

func (o *UpdateConfigurationsInternalServerError) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}
