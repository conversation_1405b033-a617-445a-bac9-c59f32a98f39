// Code generated by go-swagger; DO NOT EDIT.

package usergroup

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"
)

// New creates a new usergroup API client.
func New(transport runtime.ClientTransport, formats strfmt.Registry) ClientService {
	return &Client{transport: transport, formats: formats}
}

/*
Client for usergroup API
*/
type Client struct {
	transport runtime.ClientTransport
	formats   strfmt.Registry
}

// ClientOption is the option for Client methods
type ClientOption func(*runtime.ClientOperation)

//go:generate mockery --name ClientService --structname MockUsergroupClientService

// ClientService is the interface for Client methods
type ClientService interface {
	CreateUserGroup(params *CreateUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*CreateUserGroupCreated, error)

	DeleteUserGroup(params *DeleteUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*DeleteUserGroupOK, error)

	GetUserGroup(params *GetUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetUserGroupOK, error)

	ListUserGroups(params *ListUserGroupsParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ListUserGroupsOK, error)

	UpdateUserGroup(params *UpdateUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*UpdateUserGroupOK, error)

	SetTransport(transport runtime.ClientTransport)
}

/*
  CreateUserGroup creates user group

  Create user group information
*/
func (a *Client) CreateUserGroup(params *CreateUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*CreateUserGroupCreated, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewCreateUserGroupParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "createUserGroup",
		Method:             "POST",
		PathPattern:        "/usergroups",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &CreateUserGroupReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*CreateUserGroupCreated)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for createUserGroup: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  DeleteUserGroup deletes user group

  Delete user group
*/
func (a *Client) DeleteUserGroup(params *DeleteUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*DeleteUserGroupOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewDeleteUserGroupParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "deleteUserGroup",
		Method:             "DELETE",
		PathPattern:        "/usergroups/{group_id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &DeleteUserGroupReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*DeleteUserGroupOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for deleteUserGroup: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  GetUserGroup gets user group information

  Get user group information
*/
func (a *Client) GetUserGroup(params *GetUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*GetUserGroupOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewGetUserGroupParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "getUserGroup",
		Method:             "GET",
		PathPattern:        "/usergroups/{group_id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &GetUserGroupReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*GetUserGroupOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for getUserGroup: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  ListUserGroups gets all user groups information

  Get all user groups information
*/
func (a *Client) ListUserGroups(params *ListUserGroupsParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*ListUserGroupsOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewListUserGroupsParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "listUserGroups",
		Method:             "GET",
		PathPattern:        "/usergroups",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &ListUserGroupsReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*ListUserGroupsOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for listUserGroups: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

/*
  UpdateUserGroup updates group information

  Update user group information
*/
func (a *Client) UpdateUserGroup(params *UpdateUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...ClientOption) (*UpdateUserGroupOK, error) {
	// TODO: Validate the params before sending
	if params == nil {
		params = NewUpdateUserGroupParams()
	}
	op := &runtime.ClientOperation{
		ID:                 "updateUserGroup",
		Method:             "PUT",
		PathPattern:        "/usergroups/{group_id}",
		ProducesMediaTypes: []string{"application/json"},
		ConsumesMediaTypes: []string{"application/json"},
		Schemes:            []string{"http", "https"},
		Params:             params,
		Reader:             &UpdateUserGroupReader{formats: a.formats},
		AuthInfo:           authInfo,
		Context:            params.Context,
		Client:             params.HTTPClient,
	}
	for _, opt := range opts {
		opt(op)
	}

	result, err := a.transport.Submit(op)
	if err != nil {
		return nil, err
	}
	success, ok := result.(*UpdateUserGroupOK)
	if ok {
		return success, nil
	}
	// unexpected success response
	// safeguard: normally, absent a default response, unknown success responses return an error above: so this is a codegen issue
	msg := fmt.Sprintf("unexpected success response for updateUserGroup: API contract not enforced by server. Client expected to get an error, but got: %T", result)
	panic(msg)
}

// SetTransport changes the transport on the client
func (a *Client) SetTransport(transport runtime.ClientTransport) {
	a.transport = transport
}
