// Code generated by mockery v2.28.1. DO NOT EDIT.

package mocks

import (
	runtime "github.com/go-openapi/runtime"
	mock "github.com/stretchr/testify/mock"

	usergroup "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/usergroup"
)

// MockUsergroupClientService is an autogenerated mock type for the ClientService type
type MockUsergroupClientService struct {
	mock.Mock
}

// CreateUserGroup provides a mock function with given fields: params, authInfo, opts
func (_m *MockUsergroupClientService) CreateUserGroup(params *usergroup.CreateUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...usergroup.ClientOption) (*usergroup.CreateUserGroupCreated, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *usergroup.CreateUserGroupCreated
	var r1 error
	if rf, ok := ret.Get(0).(func(*usergroup.CreateUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) (*usergroup.CreateUserGroupCreated, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*usergroup.CreateUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) *usergroup.CreateUserGroupCreated); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*usergroup.CreateUserGroupCreated)
		}
	}

	if rf, ok := ret.Get(1).(func(*usergroup.CreateUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteUserGroup provides a mock function with given fields: params, authInfo, opts
func (_m *MockUsergroupClientService) DeleteUserGroup(params *usergroup.DeleteUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...usergroup.ClientOption) (*usergroup.DeleteUserGroupOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *usergroup.DeleteUserGroupOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*usergroup.DeleteUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) (*usergroup.DeleteUserGroupOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*usergroup.DeleteUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) *usergroup.DeleteUserGroupOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*usergroup.DeleteUserGroupOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*usergroup.DeleteUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserGroup provides a mock function with given fields: params, authInfo, opts
func (_m *MockUsergroupClientService) GetUserGroup(params *usergroup.GetUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...usergroup.ClientOption) (*usergroup.GetUserGroupOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *usergroup.GetUserGroupOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*usergroup.GetUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) (*usergroup.GetUserGroupOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*usergroup.GetUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) *usergroup.GetUserGroupOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*usergroup.GetUserGroupOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*usergroup.GetUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListUserGroups provides a mock function with given fields: params, authInfo, opts
func (_m *MockUsergroupClientService) ListUserGroups(params *usergroup.ListUserGroupsParams, authInfo runtime.ClientAuthInfoWriter, opts ...usergroup.ClientOption) (*usergroup.ListUserGroupsOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *usergroup.ListUserGroupsOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*usergroup.ListUserGroupsParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) (*usergroup.ListUserGroupsOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*usergroup.ListUserGroupsParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) *usergroup.ListUserGroupsOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*usergroup.ListUserGroupsOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*usergroup.ListUserGroupsParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetTransport provides a mock function with given fields: transport
func (_m *MockUsergroupClientService) SetTransport(transport runtime.ClientTransport) {
	_m.Called(transport)
}

// UpdateUserGroup provides a mock function with given fields: params, authInfo, opts
func (_m *MockUsergroupClientService) UpdateUserGroup(params *usergroup.UpdateUserGroupParams, authInfo runtime.ClientAuthInfoWriter, opts ...usergroup.ClientOption) (*usergroup.UpdateUserGroupOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *usergroup.UpdateUserGroupOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*usergroup.UpdateUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) (*usergroup.UpdateUserGroupOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*usergroup.UpdateUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) *usergroup.UpdateUserGroupOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*usergroup.UpdateUserGroupOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*usergroup.UpdateUserGroupParams, runtime.ClientAuthInfoWriter, ...usergroup.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockUsergroupClientService interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockUsergroupClientService creates a new instance of MockUsergroupClientService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockUsergroupClientService(t mockConstructorTestingTNewMockUsergroupClientService) *MockUsergroupClientService {
	mock := &MockUsergroupClientService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
