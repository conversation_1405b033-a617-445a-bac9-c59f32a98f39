// Code generated by go-swagger; DO NOT EDIT.

package usergroup

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewListUserGroupsParams creates a new ListUserGroupsParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewListUserGroupsParams() *ListUserGroupsParams {
	return &ListUserGroupsParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewListUserGroupsParamsWithTimeout creates a new ListUserGroupsParams object
// with the ability to set a timeout on a request.
func NewListUserGroupsParamsWithTimeout(timeout time.Duration) *ListUserGroupsParams {
	return &ListUserGroupsParams{
		timeout: timeout,
	}
}

// NewListUserGroupsParamsWithContext creates a new ListUserGroupsParams object
// with the ability to set a context for a request.
func NewListUserGroupsParamsWithContext(ctx context.Context) *ListUserGroupsParams {
	return &ListUserGroupsParams{
		Context: ctx,
	}
}

// NewListUserGroupsParamsWithHTTPClient creates a new ListUserGroupsParams object
// with the ability to set a custom HTTPClient for a request.
func NewListUserGroupsParamsWithHTTPClient(client *http.Client) *ListUserGroupsParams {
	return &ListUserGroupsParams{
		HTTPClient: client,
	}
}

/* ListUserGroupsParams contains all the parameters to send to the API endpoint
   for the list user groups operation.

   Typically these are written to a http.Request.
*/
type ListUserGroupsParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the list user groups params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListUserGroupsParams) WithDefaults() *ListUserGroupsParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the list user groups params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *ListUserGroupsParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the list user groups params
func (o *ListUserGroupsParams) WithTimeout(timeout time.Duration) *ListUserGroupsParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the list user groups params
func (o *ListUserGroupsParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the list user groups params
func (o *ListUserGroupsParams) WithContext(ctx context.Context) *ListUserGroupsParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the list user groups params
func (o *ListUserGroupsParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the list user groups params
func (o *ListUserGroupsParams) WithHTTPClient(client *http.Client) *ListUserGroupsParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the list user groups params
func (o *ListUserGroupsParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the list user groups params
func (o *ListUserGroupsParams) WithXRequestID(xRequestID *string) *ListUserGroupsParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the list user groups params
func (o *ListUserGroupsParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WriteToRequest writes these params to a swagger request
func (o *ListUserGroupsParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
