// Code generated by go-swagger; DO NOT EDIT.

package scan_all

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewGetScanAllScheduleParams creates a new GetScanAllScheduleParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetScanAllScheduleParams() *GetScanAllScheduleParams {
	return &GetScanAllScheduleParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetScanAllScheduleParamsWithTimeout creates a new GetScanAllScheduleParams object
// with the ability to set a timeout on a request.
func NewGetScanAllScheduleParamsWithTimeout(timeout time.Duration) *GetScanAllScheduleParams {
	return &GetScanAllScheduleParams{
		timeout: timeout,
	}
}

// NewGetScanAllScheduleParamsWithContext creates a new GetScanAllScheduleParams object
// with the ability to set a context for a request.
func NewGetScanAllScheduleParamsWithContext(ctx context.Context) *GetScanAllScheduleParams {
	return &GetScanAllScheduleParams{
		Context: ctx,
	}
}

// NewGetScanAllScheduleParamsWithHTTPClient creates a new GetScanAllScheduleParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetScanAllScheduleParamsWithHTTPClient(client *http.Client) *GetScanAllScheduleParams {
	return &GetScanAllScheduleParams{
		HTTPClient: client,
	}
}

/* GetScanAllScheduleParams contains all the parameters to send to the API endpoint
   for the get scan all schedule operation.

   Typically these are written to a http.Request.
*/
type GetScanAllScheduleParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get scan all schedule params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetScanAllScheduleParams) WithDefaults() *GetScanAllScheduleParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get scan all schedule params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetScanAllScheduleParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get scan all schedule params
func (o *GetScanAllScheduleParams) WithTimeout(timeout time.Duration) *GetScanAllScheduleParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get scan all schedule params
func (o *GetScanAllScheduleParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get scan all schedule params
func (o *GetScanAllScheduleParams) WithContext(ctx context.Context) *GetScanAllScheduleParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get scan all schedule params
func (o *GetScanAllScheduleParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get scan all schedule params
func (o *GetScanAllScheduleParams) WithHTTPClient(client *http.Client) *GetScanAllScheduleParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get scan all schedule params
func (o *GetScanAllScheduleParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the get scan all schedule params
func (o *GetScanAllScheduleParams) WithXRequestID(xRequestID *string) *GetScanAllScheduleParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the get scan all schedule params
func (o *GetScanAllScheduleParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WriteToRequest writes these params to a swagger request
func (o *GetScanAllScheduleParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
