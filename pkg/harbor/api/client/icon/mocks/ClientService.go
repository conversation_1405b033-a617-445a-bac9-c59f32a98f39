// Code generated by mockery v2.28.1. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	icon "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/icon"

	runtime "github.com/go-openapi/runtime"
)

// MockIconClientService is an autogenerated mock type for the ClientService type
type MockIconClientService struct {
	mock.Mock
}

// GetIcon provides a mock function with given fields: params, authInfo, opts
func (_m *MockIconClientService) GetIcon(params *icon.GetIconParams, authInfo runtime.ClientAuthInfoWriter, opts ...icon.ClientOption) (*icon.GetIconOK, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, params, authInfo)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *icon.GetIconOK
	var r1 error
	if rf, ok := ret.Get(0).(func(*icon.GetIconParams, runtime.ClientAuthInfoWriter, ...icon.ClientOption) (*icon.GetIconOK, error)); ok {
		return rf(params, authInfo, opts...)
	}
	if rf, ok := ret.Get(0).(func(*icon.GetIconParams, runtime.ClientAuthInfoWriter, ...icon.ClientOption) *icon.GetIconOK); ok {
		r0 = rf(params, authInfo, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*icon.GetIconOK)
		}
	}

	if rf, ok := ret.Get(1).(func(*icon.GetIconParams, runtime.ClientAuthInfoWriter, ...icon.ClientOption) error); ok {
		r1 = rf(params, authInfo, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetTransport provides a mock function with given fields: transport
func (_m *MockIconClientService) SetTransport(transport runtime.ClientTransport) {
	_m.Called(transport)
}

type mockConstructorTestingTNewMockIconClientService interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockIconClientService creates a new instance of MockIconClientService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockIconClientService(t mockConstructorTestingTNewMockIconClientService) *MockIconClientService {
	mock := &MockIconClientService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
