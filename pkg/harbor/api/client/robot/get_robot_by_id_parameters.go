// Code generated by go-swagger; DO NOT EDIT.

package robot

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetRobotByIDParams creates a new GetRobotByIDParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetRobotByIDParams() *GetRobotByIDParams {
	return &GetRobotByIDParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetRobotByIDParamsWithTimeout creates a new GetRobotByIDParams object
// with the ability to set a timeout on a request.
func NewGetRobotByIDParamsWithTimeout(timeout time.Duration) *GetRobotByIDParams {
	return &GetRobotByIDParams{
		timeout: timeout,
	}
}

// NewGetRobotByIDParamsWithContext creates a new GetRobotByIDParams object
// with the ability to set a context for a request.
func NewGetRobotByIDParamsWithContext(ctx context.Context) *GetRobotByIDParams {
	return &GetRobotByIDParams{
		Context: ctx,
	}
}

// NewGetRobotByIDParamsWithHTTPClient creates a new GetRobotByIDParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetRobotByIDParamsWithHTTPClient(client *http.Client) *GetRobotByIDParams {
	return &GetRobotByIDParams{
		HTTPClient: client,
	}
}

/* GetRobotByIDParams contains all the parameters to send to the API endpoint
   for the get robot by ID operation.

   Typically these are written to a http.Request.
*/
type GetRobotByIDParams struct {

	/* XRequestID.

	   An unique ID for the request
	*/
	XRequestID *string

	/* RobotID.

	   Robot ID
	*/
	RobotID int64

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get robot by ID params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetRobotByIDParams) WithDefaults() *GetRobotByIDParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get robot by ID params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetRobotByIDParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get robot by ID params
func (o *GetRobotByIDParams) WithTimeout(timeout time.Duration) *GetRobotByIDParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get robot by ID params
func (o *GetRobotByIDParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get robot by ID params
func (o *GetRobotByIDParams) WithContext(ctx context.Context) *GetRobotByIDParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get robot by ID params
func (o *GetRobotByIDParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get robot by ID params
func (o *GetRobotByIDParams) WithHTTPClient(client *http.Client) *GetRobotByIDParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get robot by ID params
func (o *GetRobotByIDParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithXRequestID adds the xRequestID to the get robot by ID params
func (o *GetRobotByIDParams) WithXRequestID(xRequestID *string) *GetRobotByIDParams {
	o.SetXRequestID(xRequestID)
	return o
}

// SetXRequestID adds the xRequestId to the get robot by ID params
func (o *GetRobotByIDParams) SetXRequestID(xRequestID *string) {
	o.XRequestID = xRequestID
}

// WithRobotID adds the robotID to the get robot by ID params
func (o *GetRobotByIDParams) WithRobotID(robotID int64) *GetRobotByIDParams {
	o.SetRobotID(robotID)
	return o
}

// SetRobotID adds the robotId to the get robot by ID params
func (o *GetRobotByIDParams) SetRobotID(robotID int64) {
	o.RobotID = robotID
}

// WriteToRequest writes these params to a swagger request
func (o *GetRobotByIDParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if o.XRequestID != nil {

		// header param X-Request-Id
		if err := r.SetHeaderParam("X-Request-Id", *o.XRequestID); err != nil {
			return err
		}
	}

	// path param robot_id
	if err := r.SetPathParam("robot_id", swag.FormatInt64(o.RobotID)); err != nil {
		return err
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
