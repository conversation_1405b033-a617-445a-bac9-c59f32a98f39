// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ChartVersion A specified chart entry
//
// swagger:model ChartVersion
type ChartVersion struct {
	ChartMetadata

	// The created time of the chart entry
	Created string `json:"created,omitempty"`

	// The digest value of the chart entry
	Digest string `json:"digest,omitempty"`

	// A flag to indicate if the chart entry is removed
	Removed bool `json:"removed,omitempty"`

	// The urls of the chart entry
	Urls []string `json:"urls"`

	// labels
	Labels Labels `json:"labels,omitempty"`
}

// UnmarshalJSON unmarshals this object from a JSON structure
func (m *ChartVersion) UnmarshalJSON(raw []byte) error {
	// AO0
	var aO0 ChartMetadata
	if err := swag.ReadJSON(raw, &aO0); err != nil {
		return err
	}
	m.ChartMetadata = aO0

	// AO1
	var dataAO1 struct {
		Created string `json:"created,omitempty"`

		Digest string `json:"digest,omitempty"`

		Removed bool `json:"removed,omitempty"`

		Urls []string `json:"urls"`
	}
	if err := swag.ReadJSON(raw, &dataAO1); err != nil {
		return err
	}

	m.Created = dataAO1.Created

	m.Digest = dataAO1.Digest

	m.Removed = dataAO1.Removed

	m.Urls = dataAO1.Urls

	// now for regular properties
	var propsChartVersion struct {
		Labels Labels `json:"labels,omitempty"`
	}
	if err := swag.ReadJSON(raw, &propsChartVersion); err != nil {
		return err
	}
	m.Labels = propsChartVersion.Labels

	return nil
}

// MarshalJSON marshals this object to a JSON structure
func (m ChartVersion) MarshalJSON() ([]byte, error) {
	_parts := make([][]byte, 0, 2)

	aO0, err := swag.WriteJSON(m.ChartMetadata)
	if err != nil {
		return nil, err
	}
	_parts = append(_parts, aO0)
	var dataAO1 struct {
		Created string `json:"created,omitempty"`

		Digest string `json:"digest,omitempty"`

		Removed bool `json:"removed,omitempty"`

		Urls []string `json:"urls"`
	}

	dataAO1.Created = m.Created

	dataAO1.Digest = m.Digest

	dataAO1.Removed = m.Removed

	dataAO1.Urls = m.Urls

	jsonDataAO1, errAO1 := swag.WriteJSON(dataAO1)
	if errAO1 != nil {
		return nil, errAO1
	}
	_parts = append(_parts, jsonDataAO1)

	// now for regular properties
	var propsChartVersion struct {
		Labels Labels `json:"labels,omitempty"`
	}
	propsChartVersion.Labels = m.Labels

	jsonDataPropsChartVersion, errChartVersion := swag.WriteJSON(propsChartVersion)
	if errChartVersion != nil {
		return nil, errChartVersion
	}
	_parts = append(_parts, jsonDataPropsChartVersion)
	return swag.ConcatJSON(_parts...), nil
}

// Validate validates this chart version
func (m *ChartVersion) Validate(formats strfmt.Registry) error {
	var res []error

	// validation for a type composition with ChartMetadata
	if err := m.ChartMetadata.Validate(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateLabels(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ChartVersion) validateLabels(formats strfmt.Registry) error {
	if swag.IsZero(m.Labels) { // not required
		return nil
	}

	if err := m.Labels.Validate(formats); err != nil {
		if ve, ok := err.(*errors.Validation); ok {
			return ve.ValidateName("labels")
		}
		return err
	}

	return nil
}

// ContextValidate validate this chart version based on the context it is used
func (m *ChartVersion) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	// validation for a type composition with ChartMetadata
	if err := m.ChartMetadata.ContextValidate(ctx, formats); err != nil {
		res = append(res, err)
	}

	if err := m.contextValidateLabels(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ChartVersion) contextValidateLabels(ctx context.Context, formats strfmt.Registry) error {

	if err := m.Labels.ContextValidate(ctx, formats); err != nil {
		if ve, ok := err.(*errors.Validation); ok {
			return ve.ValidateName("labels")
		}
		return err
	}

	return nil
}

// MarshalBinary interface implementation
func (m *ChartVersion) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ChartVersion) UnmarshalBinary(b []byte) error {
	var res ChartVersion
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
