// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ModelsArtifactTag models artifact tag
//
// swagger:model models.ArtifactTag
type ModelsArtifactTag struct {

	// art
	Art *ModelArtifact `json:"art,omitempty"`

	// tag
	Tag *ModelsTag `json:"tag,omitempty"`
}

// Validate validates this models artifact tag
func (m *ModelsArtifactTag) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateArt(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateTag(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ModelsArtifactTag) validateArt(formats strfmt.Registry) error {
	if swag.IsZero(m.Art) { // not required
		return nil
	}

	if m.Art != nil {
		if err := m.Art.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("art")
			}
			return err
		}
	}

	return nil
}

func (m *ModelsArtifactTag) validateTag(formats strfmt.Registry) error {
	if swag.IsZero(m.Tag) { // not required
		return nil
	}

	if m.Tag != nil {
		if err := m.Tag.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("tag")
			}
			return err
		}
	}

	return nil
}

// ContextValidate validate this models artifact tag based on the context it is used
func (m *ModelsArtifactTag) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateArt(ctx, formats); err != nil {
		res = append(res, err)
	}

	if err := m.contextValidateTag(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ModelsArtifactTag) contextValidateArt(ctx context.Context, formats strfmt.Registry) error {

	if m.Art != nil {
		if err := m.Art.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("art")
			}
			return err
		}
	}

	return nil
}

func (m *ModelsArtifactTag) contextValidateTag(ctx context.Context, formats strfmt.Registry) error {

	if m.Tag != nil {
		if err := m.Tag.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("tag")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (m *ModelsArtifactTag) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ModelsArtifactTag) UnmarshalBinary(b []byte) error {
	var res ModelsArtifactTag
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
