// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ModelTag model tag
//
// swagger:model model.Tag
type ModelTag struct {

	// The ID of the artifact that the tag attached to
	ArtifactID int64 `json:"artifact_id,omitempty"`

	// The ID of the tag
	ID int64 `json:"id,omitempty"`

	// The immutable status of the tag
	Immutable bool `json:"immutable,omitempty"`

	// The name of the tag
	Name string `json:"name,omitempty"`

	// The latest pull time of the tag
	// Format: date-time
	PullTime string `json:"pull_time,omitempty"`

	// The push time of the tag
	// Format: date-time
	PushTime string `json:"push_time,omitempty"`

	// The ID of the repository that the tag belongs to
	RepositoryID int64 `json:"repository_id,omitempty"`

	// The attribute indicates whether the tag is signed or not
	Signed bool `json:"signed,omitempty"`
}

// Validate validates this model tag
func (m *ModelTag) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this model tag based on context it is used
func (m *ModelTag) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ModelTag) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ModelTag) UnmarshalBinary(b []byte) error {
	var res ModelTag
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
