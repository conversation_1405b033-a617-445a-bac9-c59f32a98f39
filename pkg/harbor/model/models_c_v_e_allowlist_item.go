// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ModelsCVEAllowlistItem models c v e allowlist item
//
// swagger:model models.CVEAllowlistItem
type ModelsCVEAllowlistItem struct {

	// The ID of the CVE, such as "CVE-2019-10164"
	CveID string `json:"cve_id,omitempty"`
}

// Validate validates this models c v e allowlist item
func (m *ModelsCVEAllowlistItem) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this models c v e allowlist item based on context it is used
func (m *ModelsCVEAllowlistItem) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ModelsCVEAllowlistItem) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ModelsCVEAllowlistItem) UnmarshalBinary(b []byte) error {
	var res ModelsCVEAllowlistItem
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
