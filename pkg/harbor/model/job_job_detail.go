// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// JobJobDetail job job detail
//
// swagger:model job.JobDetail
type JobJobDetail struct {

	// event data
	EventData *JobEventData `json:"event_data,omitempty"`

	// occur at
	OccurAt int64 `json:"occur_at,omitempty"`

	// operator
	Operator string `json:"operator,omitempty"`

	// type
	Type string `json:"type,omitempty"`
}

// Validate validates this job job detail
func (m *JobJobDetail) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateEventData(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *JobJobDetail) validateEventData(formats strfmt.Registry) error {
	if swag.IsZero(m.EventData) { // not required
		return nil
	}

	if m.EventData != nil {
		if err := m.EventData.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("event_data")
			}
			return err
		}
	}

	return nil
}

// ContextValidate validate this job job detail based on the context it is used
func (m *JobJobDetail) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateEventData(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *JobJobDetail) contextValidateEventData(ctx context.Context, formats strfmt.Registry) error {

	if m.EventData != nil {
		if err := m.EventData.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("event_data")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (m *JobJobDetail) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *JobJobDetail) UnmarshalBinary(b []byte) error {
	var res JobJobDetail
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
