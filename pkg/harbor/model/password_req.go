// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// PasswordReq password req
//
// swagger:model PasswordReq
type PasswordReq struct {

	// New password for marking as to be updated.
	NewPassword string `json:"new_password,omitempty"`

	// The user's existing password.
	OldPassword string `json:"old_password,omitempty"`
}

// Validate validates this password req
func (m *PasswordReq) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this password req based on context it is used
func (m *PasswordReq) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *PasswordReq) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *PasswordReq) UnmarshalBinary(b []byte) error {
	var res PasswordReq
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
