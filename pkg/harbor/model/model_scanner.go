// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ModelScanner model scanner
//
// swagger:model model.Scanner
type ModelScanner struct {

	// Name of the scanner
	// Example: Trivy
	Name string `json:"name,omitempty"`

	// Name of the scanner provider
	// Example: Aqua Security
	Vendor string `json:"vendor,omitempty"`

	// Version of the scanner adapter
	// Example: v0.9.1
	Version string `json:"version,omitempty"`
}

// Validate validates this model scanner
func (m *ModelScanner) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this model scanner based on context it is used
func (m *ModelScanner) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ModelScanner) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ModelScanner) UnmarshalBinary(b []byte) error {
	var res ModelScanner
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
