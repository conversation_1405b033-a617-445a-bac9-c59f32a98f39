// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// PolicyAcceleratorFilter policy accelerator filter
//
// swagger:model policy.AcceleratorFilter
type PolicyAcceleratorFilter struct {

	// The accelerator policy filter type.
	Type string `json:"type,omitempty"`

	// The value of accelerator policy filter.
	Value interface{} `json:"value,omitempty"`
}

// Validate validates this policy accelerator filter
func (m *PolicyAcceleratorFilter) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this policy accelerator filter based on context it is used
func (m *PolicyAcceleratorFilter) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *PolicyAcceleratorFilter) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *PolicyAcceleratorFilter) UnmarshalBinary(b []byte) error {
	var res PolicyAcceleratorFilter
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
