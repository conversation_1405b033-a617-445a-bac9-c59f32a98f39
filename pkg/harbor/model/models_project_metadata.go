// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ModelsProjectMetadata models project metadata
//
// swagger:model models.ProjectMetadata
type ModelsProjectMetadata struct {

	// Whether scan images automatically when pushing. The valid values are "true", "false".
	AutoScan string `json:"auto_scan,omitempty"`

	// Whether content trust is enabled or not. If it is enabled, user can't pull unsigned images from this project. The valid values are "true", "false".
	EnableContentTrust string `json:"enable_content_trust,omitempty"`

	// Whether prevent the vulnerable images from running. The valid values are "true", "false".
	PreventVul string `json:"prevent_vul,omitempty"`

	// The public status of the project. The valid values are "true", "false".
	Public string `json:"public,omitempty"`

	// The ID of the tag retention policy for the project
	RetentionID string `json:"retention_id,omitempty"`

	// Whether this project reuse the system level CVE allowlist as the allowlist of its own.  The valid values are "true", "false". If it is set to "true" the actual allowlist associate with this project, if any, will be ignored.
	ReuseSysCveAllowlist string `json:"reuse_sys_cve_allowlist,omitempty"`

	// If the vulnerability is high than severity defined here, the images can't be pulled. The valid values are "none", "low", "medium", "high", "critical".
	Severity string `json:"severity,omitempty"`
}

// Validate validates this models project metadata
func (m *ModelsProjectMetadata) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this models project metadata based on context it is used
func (m *ModelsProjectMetadata) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ModelsProjectMetadata) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ModelsProjectMetadata) UnmarshalBinary(b []byte) error {
	var res ModelsProjectMetadata
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
