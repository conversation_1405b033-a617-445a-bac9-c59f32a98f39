// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// RegistryUpdate registry update
//
// swagger:model RegistryUpdate
type RegistryUpdate struct {

	// The registry access key.
	AccessKey *string `json:"access_key,omitempty"`

	// The registry access secret.
	AccessSecret *string `json:"access_secret,omitempty"`

	// Credential type of the registry, e.g. 'basic'.
	CredentialType *string `json:"credential_type,omitempty"`

	// Description of the registry.
	Description *string `json:"description,omitempty"`

	// Whether or not the certificate will be verified when <PERSON> tries to access the server.
	Insecure *bool `json:"insecure,omitempty"`

	// The registry name.
	Name *string `json:"name,omitempty"`

	// The registry URL.
	URL *string `json:"url,omitempty"`
}

// Validate validates this registry update
func (m *RegistryUpdate) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this registry update based on context it is used
func (m *RegistryUpdate) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *RegistryUpdate) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *RegistryUpdate) UnmarshalBinary(b []byte) error {
	var res RegistryUpdate
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
