// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// UserSysAdminFlag user sys admin flag
//
// swagger:model UserSysAdminFlag
type UserSysAdminFlag struct {

	// true-admin, false-not admin.
	SysadminFlag bool `json:"sysadmin_flag,omitempty"`
}

// Validate validates this user sys admin flag
func (m *UserSysAdminFlag) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this user sys admin flag based on context it is used
func (m *UserSysAdminFlag) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *UserSysAdminFlag) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *UserSysAdminFlag) UnmarshalBinary(b []byte) error {
	var res UserSysAdminFlag
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
