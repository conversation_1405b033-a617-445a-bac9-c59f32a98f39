// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ModelsTemporaryTokenResponse models temporary token response
//
// swagger:model models.TemporaryTokenResponse
type ModelsTemporaryTokenResponse struct {

	// token
	Token string `json:"token,omitempty"`
}

// Validate validates this models temporary token response
func (m *ModelsTemporaryTokenResponse) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this models temporary token response based on context it is used
func (m *ModelsTemporaryTokenResponse) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ModelsTemporaryTokenResponse) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ModelsTemporaryTokenResponse) UnmarshalBinary(b []byte) error {
	var res ModelsTemporaryTokenResponse
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
