// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
)

// ResourceList resource list
//
// swagger:model ResourceList
type ResourceList map[string]int64

// Validate validates this resource list
func (m ResourceList) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this resource list based on context it is used
func (m ResourceList) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}
