// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// RegistryEndpoint The style of the resource filter
//
// swagger:model RegistryEndpoint
type RegistryEndpoint struct {

	// The endpoint key
	Key string `json:"key,omitempty"`

	// The endpoint value
	Value string `json:"value,omitempty"`
}

// Validate validates this registry endpoint
func (m *RegistryEndpoint) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this registry endpoint based on context it is used
func (m *RegistryEndpoint) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *RegistryEndpoint) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *RegistryEndpoint) UnmarshalBinary(b []byte) error {
	var res RegistryEndpoint
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
