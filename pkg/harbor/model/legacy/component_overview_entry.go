// Code generated by go-swagger; DO NOT EDIT.

package legacy

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ComponentOverviewEntry component overview entry
//
// swagger:model ComponentOverviewEntry
type ComponentOverviewEntry struct {

	// number of the components with certain severity.
	Count int64 `json:"count,omitempty"`

	// 1-None/Negligible, 2-Unknown, 3-Low, 4-Medium, 5-High
	Severity int64 `json:"severity,omitempty"`
}

// Validate validates this component overview entry
func (m *ComponentOverviewEntry) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this component overview entry based on context it is used
func (m *ComponentOverviewEntry) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ComponentOverviewEntry) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ComponentOverviewEntry) UnmarshalBinary(b []byte) error {
	var res ComponentOverviewEntry
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
