// Code generated by go-swagger; DO NOT EDIT.

package legacy

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// Permission The permission
//
// swagger:model Permission
type Permission struct {

	// The permission action
	Action string `json:"action,omitempty"`

	// The permission resoruce
	Resource string `json:"resource,omitempty"`
}

// Validate validates this permission
func (m *Permission) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this permission based on context it is used
func (m *Permission) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *Permission) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *Permission) UnmarshalBinary(b []byte) error {
	var res Permission
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
