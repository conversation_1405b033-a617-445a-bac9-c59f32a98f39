// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// RegistryCredential registry credential
//
// swagger:model RegistryCredential
type RegistryCredential struct {

	// Access key, e.g. user name when credential type is 'basic'.
	AccessKey string `json:"access_key,omitempty"`

	// Access secret, e.g. password when credential type is 'basic'.
	AccessSecret string `json:"access_secret,omitempty"`

	// Credential type, such as 'basic', 'oauth'.
	Type string `json:"type,omitempty"`
}

// Validate validates this registry credential
func (m *RegistryCredential) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this registry credential based on context it is used
func (m *RegistryCredential) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *RegistryCredential) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *RegistryCredential) UnmarshalBinary(b []byte) error {
	var res RegistryCredential
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
