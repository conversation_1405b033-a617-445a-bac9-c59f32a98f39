// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ModelsTag models tag
//
// swagger:model models.Tag
type ModelsTag struct {

	// accelerator status
	AcceleratorStatus string `json:"accelerator_status,omitempty"`

	// artifact id
	ArtifactID int64 `json:"artifact_id,omitempty"`

	// digest
	Digest string `json:"digest,omitempty"`

	// id
	ID int64 `json:"id,omitempty"`

	// the media type of manifest/index
	ManifestMediaType string `json:"manifest_media_type,omitempty"`

	// the media type of artifact. Mostly, it's the value of `manifest.config.mediatype`
	MediaType string `json:"media_type,omitempty"`

	// name
	Name string `json:"name,omitempty"`

	// project id
	ProjectID int64 `json:"project_id,omitempty"`

	// pull time
	PullTime string `json:"pull_time,omitempty"`

	// push time
	PushTime string `json:"push_time,omitempty"`

	// repository id
	RepositoryID int64 `json:"repository_id,omitempty"`

	// repository name
	RepositoryName string `json:"repository_name,omitempty"`

	// size
	Size int64 `json:"size,omitempty"`

	// image, chart, etc
	Type string `json:"type,omitempty"`
}

// Validate validates this models tag
func (m *ModelsTag) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this models tag based on context it is used
func (m *ModelsTag) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ModelsTag) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ModelsTag) UnmarshalBinary(b []byte) error {
	var res ModelsTag
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
