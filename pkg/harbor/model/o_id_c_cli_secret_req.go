// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// OIDCCliSecretReq o ID c cli secret req
//
// swagger:model OIDCCliSecretReq
type OIDCCliSecretReq struct {

	// The new secret
	Secret string `json:"secret,omitempty"`
}

// Validate validates this o ID c cli secret req
func (m *OIDCCliSecretReq) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this o ID c cli secret req based on context it is used
func (m *OIDCCliSecretReq) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *OIDCCliSecretReq) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *OIDCCliSecretReq) UnmarshalBinary(b []byte) error {
	var res OIDCCliSecretReq
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
