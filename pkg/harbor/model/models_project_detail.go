// Code generated by go-swagger; DO NOT EDIT.

package model

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// ModelsProjectDetail models project detail
//
// swagger:model models.ProjectDetail
type ModelsProjectDetail struct {

	// The total number of charts under this project.
	ChartCount int64 `json:"chart_count,omitempty"`

	// The creation time of the project.
	// Format: date-time
	// Format: date-time
	CreationTime strfmt.DateTime `json:"creation_time,omitempty"`

	// The role ID with highest permission of the current user who triggered the API (for UI).  This attribute is deprecated and will be removed in future versions.
	CurrentUserRoleID int64 `json:"current_user_role_id,omitempty"`

	// The list of role ID of the current user who triggered the API (for UI)
	CurrentUserRoleIds []int64 `json:"current_user_role_ids"`

	// The CVE allowlist of this project.
	CveAllowlist *ModelsCVEAllowlist `json:"cve_allowlist,omitempty"`

	// A deletion mark of the project.
	Deleted bool `json:"deleted,omitempty"`

	// The metadata of the project.
	Metadata *ModelsProjectMetadata `json:"metadata,omitempty"`

	// The name of the project.
	Name string `json:"name,omitempty"`

	// The owner ID of the project always means the creator of the project.
	OwnerID int64 `json:"owner_id,omitempty"`

	// The owner name of the project.
	OwnerName string `json:"owner_name,omitempty"`

	// Project ID
	ProjectID int32 `json:"project_id,omitempty"`

	// The ID of referenced registry when the project is a proxy cache project.
	RegistryID int64 `json:"registry_id,omitempty"`

	// The number of the repositories under this project.
	RepoCount int64 `json:"repo_count,omitempty"`

	// Correspond to the UI about whether the project's publicity is  updatable (for UI)
	Togglable bool `json:"togglable,omitempty"`

	// The update time of the project.
	// Format: date-time
	// Format: date-time
	UpdateTime strfmt.DateTime `json:"update_time,omitempty"`
}

// Validate validates this models project detail
func (m *ModelsProjectDetail) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateCreationTime(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateCveAllowlist(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateMetadata(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateUpdateTime(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ModelsProjectDetail) validateCreationTime(formats strfmt.Registry) error {
	if swag.IsZero(m.CreationTime) { // not required
		return nil
	}

	if err := validate.FormatOf("creation_time", "body", "date-time", m.CreationTime.String(), formats); err != nil {
		return err
	}

	return nil
}

func (m *ModelsProjectDetail) validateCveAllowlist(formats strfmt.Registry) error {
	if swag.IsZero(m.CveAllowlist) { // not required
		return nil
	}

	if m.CveAllowlist != nil {
		if err := m.CveAllowlist.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("cve_allowlist")
			}
			return err
		}
	}

	return nil
}

func (m *ModelsProjectDetail) validateMetadata(formats strfmt.Registry) error {
	if swag.IsZero(m.Metadata) { // not required
		return nil
	}

	if m.Metadata != nil {
		if err := m.Metadata.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("metadata")
			}
			return err
		}
	}

	return nil
}

func (m *ModelsProjectDetail) validateUpdateTime(formats strfmt.Registry) error {
	if swag.IsZero(m.UpdateTime) { // not required
		return nil
	}

	if err := validate.FormatOf("update_time", "body", "date-time", m.UpdateTime.String(), formats); err != nil {
		return err
	}

	return nil
}

// ContextValidate validate this models project detail based on the context it is used
func (m *ModelsProjectDetail) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateCveAllowlist(ctx, formats); err != nil {
		res = append(res, err)
	}

	if err := m.contextValidateMetadata(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ModelsProjectDetail) contextValidateCveAllowlist(ctx context.Context, formats strfmt.Registry) error {

	if m.CveAllowlist != nil {
		if err := m.CveAllowlist.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("cve_allowlist")
			}
			return err
		}
	}

	return nil
}

func (m *ModelsProjectDetail) contextValidateMetadata(ctx context.Context, formats strfmt.Registry) error {

	if m.Metadata != nil {
		if err := m.Metadata.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("metadata")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (m *ModelsProjectDetail) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ModelsProjectDetail) UnmarshalBinary(b []byte) error {
	var res ModelsProjectDetail
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
