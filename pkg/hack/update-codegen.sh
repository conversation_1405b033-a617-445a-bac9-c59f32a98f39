#!/usr/bin/env bash

set -o errexit
set -o nounset
set -o pipefail

# corresponding to go mod init <module>
MODULE=icode.baidu.com/baidu/jpaas-caas/ccr-stack
# api package
APIS_PKG=pkg/crd/apis
# generated output package
OUTPUT_PKG=pkg/crd/client/ccr/v1alpha1

GROUP=ccr
VERSION=v1alpha1
GROUP_VERSION=${GROUP}:${VERSION}

CODEGEN_PKG=vendor/k8s.io/code-generator

rm -rf ${OUTPUT_PKG}/{clientset,informers,listers}

#bash "${CODEGEN_PKG}"/generate-groups.sh "client,informer,lister" \
bash "${CODEGEN_PKG}"/generate-groups.sh all \
  ${MODULE}/${OUTPUT_PKG} ${MODULE}/${APIS_PKG} \
  ${GROUP_VERSION} \
  --go-header-file pkg/hack/boilerplate.go.txt \
  --output-base "${GOPATH}/src"