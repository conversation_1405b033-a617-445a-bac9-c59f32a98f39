package promsdk

import (
	"bytes"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/golang/snappy"
	"github.com/prometheus/client_golang/prometheus"
	dto "github.com/prometheus/client_model/go"
	"github.com/prometheus/prometheus/prompb"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
	"net/http"
	"regexp"
	"strconv"
	"time"
)

type client struct {
	http.Client
	remoteWriteEndpoint string
	token               string
	promID              string
	serverID            string
}

func newClient(promID, endpoint, token string, serverID string) (*client, error) {
	if promID == "" || endpoint == "" || token == "" || serverID == "" {
		return nil, fmt.Errorf("promID: %s, endpoint: %s, token: %s, serverId: %s", promID, endpoint, token, serverID)
	}
	return &client{
		promID:              promID,
		remoteWriteEndpoint: endpoint,
		token:               token,
		serverID:            serverID,
	}, nil
}

func (c *client) writeTimeSeries(series []prompb.TimeSeries) error {
	if len(series) == 0 {
		return nil
	}
	pbBytes, err := proto.Marshal(&prompb.WriteRequest{
		Timeseries: series,
	})
	if err != nil {
		csmlog.Infof("marshal prompb.WriteRequest failed: %v", err)
		return err
	}
	compressedBytes := snappy.Encode(nil, pbBytes)

	req, err := http.NewRequest(http.MethodPost, c.remoteWriteEndpoint, bytes.NewBuffer(compressedBytes))
	if err != nil {
		csmlog.Infof("new request failed: %v", err)
		return err
	}

	// 设置请求头
	req.Header.Set("Content-Encoding", "snappy")
	req.Header.Set("InstanceId", c.promID)
	req.Header.Set("Authorization", c.token)
	req.Header.Set("Content-Type", "application/x-protobuf")

	resp, err := c.Do(req)
	if err != nil {
		csmlog.Infof("send request failed: %v", err)
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent {
		err = fmt.Errorf("unexpected status code: %d", resp.StatusCode)
		csmlog.Infof("send request failed: %v", err)
		return err
	}
	return nil
}

func (c *client) getLabels(metric *dto.Metric) []prompb.Label {
	labels := make([]prompb.Label, 0)
	for _, label := range metric.GetLabel() {
		labels = append(labels, prompb.Label{
			Name:  label.GetName(),
			Value: label.GetValue(),
		})
	}
	return labels
}

func (c *client) parseMetricName(desc *prometheus.Desc) (string, error) {
	pattern, err := regexp.Compile(`fqName: "([^"]+)"`)
	if err != nil {
		return "", err
	}
	match := pattern.FindStringSubmatch(desc.String())

	if len(match) > 1 {
		return match[1], nil
	}
	return "", fmt.Errorf("invalid metric desc: %s", desc.String())
}

func (c *client) getTimeSeries(metricName string, dst *dto.Metric) ([]prompb.TimeSeries, error) {
	var timeSeriesList []prompb.TimeSeries

	timeStamp := time.Now().UnixMilli()
	switch {
	case dst.Counter != nil:
		counter := dst.GetCounter()
		series := prompb.TimeSeries{
			Labels: c.getLabels(dst),
			Samples: []prompb.Sample{{
				Timestamp: timeStamp,
				Value:     counter.GetValue(),
			}},
		}
		series.Labels = append([]prompb.Label{{
			Name:  "__name__",
			Value: metricName,
		}, {
			Name:  "__source__",
			Value: c.serverID,
		}}, series.Labels...)
		timeSeriesList = append(timeSeriesList, series)
	case dst.Histogram != nil:
		histogram := dst.GetHistogram()
		for _, bucket := range histogram.Bucket {
			series := prompb.TimeSeries{
				Labels: c.getLabels(dst),
				Samples: []prompb.Sample{{
					Timestamp: timeStamp,
					Value:     float64(bucket.GetCumulativeCount()),
				}},
			}
			series.Labels = append([]prompb.Label{
				{
					Name:  "__name__",
					Value: metricName + "_bucket",
				},
				{
					Name:  "__source__",
					Value: c.serverID,
				},
				{
					Name:  "le",
					Value: strconv.FormatFloat(bucket.GetUpperBound(), 'f', -1, 64),
				},
			}, series.Labels...)
			timeSeriesList = append(timeSeriesList, series)
		}
		sumSeries := prompb.TimeSeries{
			Labels: c.getLabels(dst),
			Samples: []prompb.Sample{{
				Timestamp: timeStamp,
				Value:     histogram.GetSampleSum(),
			}},
		}
		sumSeries.Labels = append([]prompb.Label{{
			Name:  "__name__",
			Value: metricName + "_sum",
		}, {
			Name:  "__source__",
			Value: c.serverID,
		}}, sumSeries.Labels...)
		timeSeriesList = append(timeSeriesList, sumSeries)
		cntSeries := prompb.TimeSeries{
			Labels: c.getLabels(dst),
			Samples: []prompb.Sample{{
				Timestamp: timeStamp,
				Value:     float64(histogram.GetSampleCount()),
			}},
		}
		cntSeries.Labels = append([]prompb.Label{{
			Name:  "__name__",
			Value: metricName + "_count",
		}, {
			Name:  "__source__",
			Value: c.serverID,
		}}, cntSeries.Labels...)
		timeSeriesList = append(timeSeriesList, cntSeries)
	}
	return timeSeriesList, nil
}
