package promsdk

import (
	"os"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/prometheus/prompb"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

var (
	httpStatusCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "http_status_counter",
		Help: "Status of HTTP responses",
	}, []string{"api_name", "http_code"})

	httpDurationSeconds = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "http_duration_seconds",
			Help:    "HTTP request duration in seconds",
			Buckets: []float64{0.01, 0.025, 0.05, 0.1, 0.2, 0.5, 1, 2, 5, 10},
		},
		[]string{"api_name"},
	)
)

func InitMetrics() {
	hostname, err := os.Hostname()
	if err != nil {
		csmlog.Warnf("get hostname failed: %v", err)
		hostname = "unknown"
	}

	c, err := newClient(viper.GetString("promId"), viper.GetString("remoteWriteEndpoint"), viper.GetString("promToken"), hostname)
	if err != nil {
		csmlog.Warnf("not enable metrics")
		return
	}
	prometheus.MustRegister(httpStatusCounter, httpDurationSeconds)

	go func() {
		for range time.Tick(5 * time.Second) {
			mfs, err := prometheus.DefaultGatherer.Gather()
			if err != nil {
				csmlog.Warnf("gather metrics failed: %v", err)
				continue
			}
			series := make([]prompb.TimeSeries, 0)
			for _, mf := range mfs {
				for _, metric := range mf.GetMetric() {
					s, err := c.getTimeSeries(mf.GetName(), metric)
					if err != nil {
						csmlog.Warnf("get time series failed: %v", err)
						continue
					}
					series = append(series, s...)
				}
			}
			if err = c.writeTimeSeries(series); err != nil {
				csmlog.Warnf("write time series failed: %v", err)
			}
		}
	}()
}

func GetMetricHTTPStatusCounter() *prometheus.CounterVec {
	return httpStatusCounter
}

func GetMetricHTTPDurationSeconds() *prometheus.HistogramVec {
	return httpDurationSeconds
}
