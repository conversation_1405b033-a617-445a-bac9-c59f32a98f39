package kafka

import (
	"io/ioutil"

	"crypto/tls"
	"crypto/x509"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

// kafka配置
type Options struct {
	Brokers                 []string
	Topics                  []string
	EnableTLS               bool
	ClientPemPath           string
	ClientKeyPath           string
	CaPemPath               string
	ConsumerGroupID         string
	ConsumerGroupAutoCommit bool // 自动commit
	ConsumerOffsetsInitial  int64
}

func NewKafkaOptions(caPemPath, clientKeyPath, clientPemPath, consumerGroupID, brokersEndpoint string) *Options {
	return &Options{
		Brokers:                 []string{brokersEndpoint},
		EnableTLS:               true,
		ClientPemPath:           clientPemPath,
		ClientKeyPath:           clientKeyPath,
		CaPemPath:               caPemPath,
		ConsumerGroupID:         consumerGroupID,
		ConsumerGroupAutoCommit: true,
		ConsumerOffsetsInitial:  -1,
	}
}

func configTLS(o *Options) (t *tls.Config, err error) {
	// 读取AES CFB 加密 + Base64 编码的证书和私钥文件
	encodedClientPemBlock, err := ioutil.ReadFile(o.ClientPemPath)
	if err != nil {
		return nil, err
	}
	certPEMBlockString, err := utils.AESCFBDecrypt(string(encodedClientPemBlock))
	if err != nil {
		return nil, err
	}
	certPEMBlock := []byte(certPEMBlockString)

	encodedKeyPemBlock, err := ioutil.ReadFile(o.ClientKeyPath)
	if err != nil {
		return nil, err
	}
	certKeyPemBlockString, err := utils.AESCFBDecrypt(string(encodedKeyPemBlock))
	if err != nil {
		return nil, err
	}
	keyPEMBlock := []byte(certKeyPemBlockString)

	clientPem, err := tls.X509KeyPair(certPEMBlock, keyPEMBlock)
	if err != nil {
		return nil, err
	}

	// 读取AES CFB 加密 + Base64 编码编码的证书文件
	encodedCaPEMBlock, err := ioutil.ReadFile(o.CaPemPath)
	if err != nil {
		return nil, err
	}
	caPemString, err := utils.AESCFBDecrypt(string(encodedCaPEMBlock))
	if err != nil {
		return nil, err
	}
	caPem := []byte(caPemString)

	certPool := x509.NewCertPool()
	certPool.AppendCertsFromPEM(caPem)
	t = &tls.Config{
		Certificates:       []tls.Certificate{clientPem},
		RootCAs:            certPool,
		InsecureSkipVerify: true,
	}
	return
}
