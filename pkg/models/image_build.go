package models

import (
	"time"

	"gorm.io/gorm/clause"
)

const (
	ImageBuildTableName = "t_ccr_image_build"
)

type ImageBuild struct {
	ID         int64     `json:"id,omitempty" gorm:"<-:false"`
	CreatedAt  time.Time `json:"createdAt,omitempty"`
	AccountID  string    `json:"accountID,omitempty" gorm:"column:account_id;index;size:128"`
	UserID     string    `json:"userID,omitempty" gorm:"column:user_id;size:128"`
	Userame    string    `json:"userame,omitempty" gorm:"column:userame;size:128"`
	InstanceID string    `json:"instanceID,omitempty" gorm:"column:instance_id;not null;size:128;index"`
	FinishedAt time.Time `json:"finishedAt,omitempty" gorm:"column:finished_at;not null"`
	Status     string    `json:"status,omitempty" gorm:"column:status;size:128;index"`
	Project    string    `json:"project,omitempty" gorm:"column:project;size:128;index"`
	Repository string    `json:"repository,omitempty" gorm:"column:repository;size:128"`
	TagName    string    `json:"tagName,omitempty" gorm:"column:tag_name;size:128"`
	IsLatest   bool      `json:"isLatest,omitempty" gorm:"column:is_latest"`
	FromType   string    `json:"fromType,omitempty" gorm:"column:from_type;size:32"`
	Dockerfile string    `json:"dockerfile,omitempty" gorm:"column:dockerfile;text"`
	Log        string    `json:"log,omitempty" gorm:"column:log;text"`
}

func (*ImageBuild) TableName() string {
	return ImageBuildTableName
}

func (c *Client) InsertImageBuild(ib *ImageBuild) error {
	ib.FinishedAt = time.Unix(0, 0)

	return c.db.Table(ImageBuildTableName).Create(ib).Error
}

func (c *Client) UpdateImageBuildStatusByID(id int64, status string) error {
	return c.db.Table(ImageBuildTableName).Where("id =?", id).UpdateColumn("status", status).Error
}

func (c *Client) GetImageBuild(id int64) (*ImageBuild, error) {
	var ib ImageBuild

	err := c.db.Table(ImageBuildTableName).Where("id =?", id).First(&ib).Error

	return &ib, err
}

func (c *Client) ListImageBuildPaged(tag string, filter map[string]interface{}, page, size int) ([]*ImageBuild, int, error) {
	var (
		ib    []*ImageBuild
		total int64
	)

	whereClause := []interface{}{filter}

	if tag != "" {
		whereClause = append(whereClause, clause.Like{Column: "tag_name", Value: "%" + tag + "%"})
	}

	if err := c.db.Table(ImageBuildTableName).
		Where(whereClause[0], whereClause[1:]...).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := c.db.Table(ImageBuildTableName).
		Where(whereClause[0], whereClause[1:]...).
		Order("created_at DESC").
		Offset((page - 1) * size).
		Limit(int(size)).
		Find(&ib).Error

	return ib, int(total), err
}

func (c *Client) GetImageBuildLog(id int64) (string, error) {
	var ib ImageBuild

	err := c.db.Table(ImageBuildTableName).Where("id =?", id).First(&ib).Error

	return ib.Log, err
}

func (c *Client) Delete(ids []int64) error {
	return c.db.Table(ImageBuildTableName).Where(map[string]interface{}{
		"id": ids,
	}).Delete(&ImageBuild{}).Error
}

func (c *Client) SetImageBuildStatusAndLog(id int64, status, log string) error {
	return c.db.Table(ImageBuildTableName).Where("id=? and log=''", id).UpdateColumns(
		map[string]interface{}{
			"status":      status,
			"finished_at": time.Now(),
			"log":         log,
		}).Error
}
