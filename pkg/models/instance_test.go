package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
)

func Test_Instance(t *testing.T) {
	obj := &ccrv1alpha1.CCR{
		ObjectMeta: v1.ObjectMeta{
			Name: "test",
		},
	}

	ins := InstanceFromCCR(obj)
	assert.Equal(t, "test", ins.InstanceID)

	obj1 := &ccrv1alpha1.CCR{
		ObjectMeta: v1.ObjectMeta{
			Name: "test1",
		},
	}

	ins1 := InstanceFromCCR(obj1)
	assert.Equal(t, "test1", ins1.InstanceID)

	assert.False(t, ins1.Equal(ins))

	now := v1.Now()
	obj.DeletionTimestamp = &now
	obj.Status.StartTime = &now
	ins = InstanceFromCCR(obj)
	assert.Equal(t, now.Time, ins.DeletedAt)
	assert.Equal(t, now.Time, ins.StartAt)
}
