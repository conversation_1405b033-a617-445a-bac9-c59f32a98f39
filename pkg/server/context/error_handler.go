package context

import (
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	csmError "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"
)

// CsmHTTPErrorHandler is the HTTP error handler for CSM app.
// It sends a JSON response with request id and error message.
func CsmHTTPErrorHandler(err error, c CsmContext) {
	code := http.StatusInternalServerError
	respError := csm.GenericError{
		RequestID: c.RequestID(),
		Code:      http.StatusText(code),
	}
	debug := c.Echo().Debug
	var internal error

	switch he := err.(type) {
	case *echo.HTTPError:
		code = he.Code
		switch msg := he.Message.(type) {
		case *csmError.Error:
		case csmError.Error:
			respError.Code = string(msg.Code)
			respError.Message = msg.Message
		case string:
			respError.Code = http.StatusText(code)
			respError.Message = msg
		default:
			respError.Code = string(csmError.DefaultError.Code)
			respError.Message = csmError.DefaultError.Message
		}
		// there may be root causes from internal
		if he.Internal != nil {
			internal = he.Internal
			if debug {
				respError.Message = fmt.Sprintf("%s. INTERNAL FOR DEBUGGING: %s", respError.Message, internal.Error())
			}
		}
	case *csm.ResponseError:
		code = he.StatusCode
		respError.Message = he.Message
		respError.Code = he.Code
	default:
		// 修复internal server error 不清楚问题
		respError.Message = err.Error()
	}

	var internalMsg string
	if internal != nil {
		internalMsg = internal.Error()
	}
	logFields := csmlog.Fields{
		"status_code": code,
		"code":        respError.Code,
		"errmsg":      respError.Message,
		"internal":    internalMsg,
	}
	if internal != nil {
		//如果err是通过csm/pkg/errors包New*生成的错误，都拥有internal err
		if aggregateErr, ok := internal.(utilerrors.Aggregate); ok {
			//+v打出堆栈
			errs := aggregateErr.Errors()
			c.CsmLogger().WithFields(logFields).Warnf("CSM_ERROR message: %s internal: %+v", respError.Message, errs)
		} else {
			c.CsmLogger().WithFields(logFields).Warnf("CSM_ERROR message: %s internal: %s", respError.Message, internal.Error())
		}
	} else {
		c.CsmLogger().WithFields(logFields).Warnf("CSM_ERROR message: %v", respError)
	}

	// Send response
	if !c.Response().Committed {
		if c.Request().Method == http.MethodHead {
			err = c.NoContent(code)
		} else {
			err = c.JSON(code, respError)
		}
		if err != nil {
			c.CsmLogger().Error(err.Error())
		}
	}
}
