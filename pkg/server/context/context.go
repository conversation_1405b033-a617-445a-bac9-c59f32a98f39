package context

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

const HeaderXRequestID = "X-BCE-Request-ID"

type CsmContext interface {
	// Context 中提供的方法在 CsmContext 中都可以使用哦
	echo.Context

	// CsmLogger 获取了这个 Context 专属的 logger
	CsmLogger() *csmlog.Logger

	// WithLoggerField 会生成一个新的 CsmContext，附带了刚加上去的 log field
	WithLoggerField(key string, value interface{}) CsmContext

	// WithLoggerFields 也会生成一个新的 CsmContext，附带了刚加上去的 log field，这个方法可以同时设置多个字段
	WithLoggerFields(fields csmlog.Fields) CsmContext

	// RequestID 用来方便地快速获取 request id，少写两行代码而已
	RequestID() string

	// SetResource resource 用来发送给 IAM 进行权限验证
	SetResource(resource string)

	GetResource() string

	ResetResourceWith(resource string)
}

type csmContext struct {
	echo.Context
	csmLogger *csmlog.Logger
	resource  string
}

func (c *csmContext) CsmLogger() *csmlog.Logger {
	return c.csmLogger
}

func NewCsmContext(c echo.Context) CsmContext {
	cc := &csmContext{
		Context:   c,
		csmLogger: csmlog.NewLogger(),
	}
	return cc
}

func (c *csmContext) WithLoggerField(key string, value interface{}) CsmContext {
	return &csmContext{
		Context:   c.Context,
		csmLogger: c.csmLogger.WithField(key, value),
	}
}

func (c *csmContext) WithLoggerFields(fields csmlog.Fields) CsmContext {
	return &csmContext{
		Context:   c.Context,
		csmLogger: c.csmLogger.WithFields(fields),
	}
}

func (c *csmContext) RequestID() string {
	return c.Context.Response().Header().Get(HeaderXRequestID)
}

func (c *csmContext) Logger() echo.Logger {
	panic("Use c.CsmLogger() instead")
}

func (c *csmContext) Error(err error) {
	CsmHTTPErrorHandler(err, c)
}

func (c *csmContext) SetResource(r string) {
	if c.resource == "" {
		c.resource = r
		return
	}
	c.resource = c.resource + "/" + r
	return
}

// GetResource 返回 API 对应的资源名称，可以使用*的对资源名称进行通配
func (c *csmContext) GetResource() string {
	if c.resource == "" {
		return "*"
	}
	return c.resource
}

// ResetResourceWith 用提供的资源重置资源
func (c *csmContext) ResetResourceWith(r string) {
	if r != "" {
		c.resource = r
	}
	return
}
