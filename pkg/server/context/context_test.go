package context

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"go.uber.org/zap/zaptest/observer"
)

func TestCsmContext_WithLoggerField(t *testing.T) {
	ctx, logs := NewCsmContextMock()
	ent := zapcore.Entry{Level: zap.InfoLevel, Message: "hello"}
	ctx.WithLoggerField("field-1", 42).CsmLogger().Info(ent.Message)
	ctx.WithLoggerField("field-2", "fourty-two").CsmLogger().Info(ent.Message)
	ctx.WithLoggerField("field-3", false).CsmLogger().Info(ent.Message)

	want := []observer.LoggedEntry{
		{
			Entry: ent,
			Context: []zapcore.Field{
				zap.Int("field-1", 42),
			},
		},
		{
			Entry: ent,
			Context: []zapcore.Field{
				zap.String("field-2", "fourty-two"),
			},
		},
		{
			Entry: ent,
			Context: []zapcore.Field{
				zap.Bool("field-3", false),
			},
		},
	}
	assert.Equal(t, want, logs.AllUntimed(), "failed")
}

func TestCsmContext_WithLoggerFields(t *testing.T) {
	ass := assert.New(t)
	ctx, logs := NewCsmContextMock()
	want := map[string]interface{}{
		"field-1": "foobar",
		"field-2": "fourty-two",
		"field-3": "not true",
	}
	ctx.WithLoggerFields(want).CsmLogger().Info("hello")
	actual := logs.AllUntimed()[0].Context
	ass.Equal(len(want), len(actual), "diff len")
	for _, field := range actual {
		v, ok := want[field.Key]
		ass.True(ok, "extra field")
		ass.Equalf(v, field.String, "field mismatch: %s", field.Key)
	}
}

func TestCsmContext_RequestID(t *testing.T) {
	ctx, _ := NewCsmContextMock()
	ctx.Response().Header().Set(HeaderXRequestID, mockRequestID)
	assert.Equal(t, mockRequestID, ctx.RequestID(), "request id mismatch")
}

func TestCsmContext_SetAndGetResource(t *testing.T) {
	tests := []struct {
		name           string
		ctx            *csmContext
		levelAResource string
		levelBResource string
	}{
		{
			name:           "normal set resource",
			ctx:            &csmContext{},
			levelAResource: "levelA_ResourceA",
			levelBResource: "levelB_ResourceB",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.ctx.SetResource(tt.levelAResource)
			tt.ctx.SetResource(tt.levelBResource)
			assert.Equal(t, "levelA_ResourceA/levelB_ResourceB", tt.ctx.GetResource())
		})
	}
}

func TestCsmContext_ResetResourceWith(t *testing.T) {
	tests := []struct {
		name           string
		ctx            *csmContext
		levelAResource string
	}{
		{
			name:           "normal set resource",
			ctx:            &csmContext{},
			levelAResource: "levelA_ResourceA",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.ctx.ResetResourceWith(tt.levelAResource)
			assert.Equal(t, "levelA_ResourceA", tt.ctx.GetResource())
		})
	}
}
