// Code generated by MockGen. DO NOT EDIT.
// Source: context.go

// Package mock is a generated GoMock package.
package mock

import (
	io "io"
	multipart "mime/multipart"
	http "net/http"
	url "net/url"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	echo "github.com/labstack/echo/v4"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	csmlog "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

// MockCsmContext is a mock of CsmContext interface.
type MockCsmContext struct {
	ctrl     *gomock.Controller
	recorder *MockCsmContextMockRecorder
}

// MockCsmContextMockRecorder is the mock recorder for MockCsmContext.
type MockCsmContextMockRecorder struct {
	mock *MockCsmContext
}

// NewMockCsmContext creates a new mock instance.
func NewMockCsmContext(ctrl *gomock.Controller) *MockCsmContext {
	mock := &MockCsmContext{ctrl: ctrl}
	mock.recorder = &MockCsmContextMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCsmContext) EXPECT() *MockCsmContextMockRecorder {
	return m.recorder
}

// Attachment mocks base method.
func (m *MockCsmContext) Attachment(file, name string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Attachment", file, name)
	ret0, _ := ret[0].(error)
	return ret0
}

// Attachment indicates an expected call of Attachment.
func (mr *MockCsmContextMockRecorder) Attachment(file, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Attachment", reflect.TypeOf((*MockCsmContext)(nil).Attachment), file, name)
}

// Bind mocks base method.
func (m *MockCsmContext) Bind(i interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Bind", i)
	ret0, _ := ret[0].(error)
	return ret0
}

// Bind indicates an expected call of Bind.
func (mr *MockCsmContextMockRecorder) Bind(i interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Bind", reflect.TypeOf((*MockCsmContext)(nil).Bind), i)
}

// Blob mocks base method.
func (m *MockCsmContext) Blob(code int, contentType string, b []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Blob", code, contentType, b)
	ret0, _ := ret[0].(error)
	return ret0
}

// Blob indicates an expected call of Blob.
func (mr *MockCsmContextMockRecorder) Blob(code, contentType, b interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Blob", reflect.TypeOf((*MockCsmContext)(nil).Blob), code, contentType, b)
}

// Cookie mocks base method.
func (m *MockCsmContext) Cookie(name string) (*http.Cookie, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cookie", name)
	ret0, _ := ret[0].(*http.Cookie)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Cookie indicates an expected call of Cookie.
func (mr *MockCsmContextMockRecorder) Cookie(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cookie", reflect.TypeOf((*MockCsmContext)(nil).Cookie), name)
}

// Cookies mocks base method.
func (m *MockCsmContext) Cookies() []*http.Cookie {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cookies")
	ret0, _ := ret[0].([]*http.Cookie)
	return ret0
}

// Cookies indicates an expected call of Cookies.
func (mr *MockCsmContextMockRecorder) Cookies() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cookies", reflect.TypeOf((*MockCsmContext)(nil).Cookies))
}

// CsmLogger mocks base method.
func (m *MockCsmContext) CsmLogger() *csmlog.Logger {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CsmLogger")
	ret0, _ := ret[0].(*csmlog.Logger)
	return ret0
}

// CsmLogger indicates an expected call of CsmLogger.
func (mr *MockCsmContextMockRecorder) CsmLogger() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CsmLogger", reflect.TypeOf((*MockCsmContext)(nil).CsmLogger))
}

// Echo mocks base method.
func (m *MockCsmContext) Echo() *echo.Echo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Echo")
	ret0, _ := ret[0].(*echo.Echo)
	return ret0
}

// Echo indicates an expected call of Echo.
func (mr *MockCsmContextMockRecorder) Echo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Echo", reflect.TypeOf((*MockCsmContext)(nil).Echo))
}

// Error mocks base method.
func (m *MockCsmContext) Error(err error) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Error", err)
}

// Error indicates an expected call of Error.
func (mr *MockCsmContextMockRecorder) Error(err interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Error", reflect.TypeOf((*MockCsmContext)(nil).Error), err)
}

// File mocks base method.
func (m *MockCsmContext) File(file string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "File", file)
	ret0, _ := ret[0].(error)
	return ret0
}

// File indicates an expected call of File.
func (mr *MockCsmContextMockRecorder) File(file interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "File", reflect.TypeOf((*MockCsmContext)(nil).File), file)
}

// FormFile mocks base method.
func (m *MockCsmContext) FormFile(name string) (*multipart.FileHeader, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FormFile", name)
	ret0, _ := ret[0].(*multipart.FileHeader)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FormFile indicates an expected call of FormFile.
func (mr *MockCsmContextMockRecorder) FormFile(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FormFile", reflect.TypeOf((*MockCsmContext)(nil).FormFile), name)
}

// FormParams mocks base method.
func (m *MockCsmContext) FormParams() (url.Values, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FormParams")
	ret0, _ := ret[0].(url.Values)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FormParams indicates an expected call of FormParams.
func (mr *MockCsmContextMockRecorder) FormParams() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FormParams", reflect.TypeOf((*MockCsmContext)(nil).FormParams))
}

// FormValue mocks base method.
func (m *MockCsmContext) FormValue(name string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FormValue", name)
	ret0, _ := ret[0].(string)
	return ret0
}

// FormValue indicates an expected call of FormValue.
func (mr *MockCsmContextMockRecorder) FormValue(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FormValue", reflect.TypeOf((*MockCsmContext)(nil).FormValue), name)
}

// Get mocks base method.
func (m *MockCsmContext) Get(key string) interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", key)
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockCsmContextMockRecorder) Get(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockCsmContext)(nil).Get), key)
}

// GetResource mocks base method.
func (m *MockCsmContext) GetResource() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResource")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetResource indicates an expected call of GetResource.
func (mr *MockCsmContextMockRecorder) GetResource() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResource", reflect.TypeOf((*MockCsmContext)(nil).GetResource))
}

// HTML mocks base method.
func (m *MockCsmContext) HTML(code int, html string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HTML", code, html)
	ret0, _ := ret[0].(error)
	return ret0
}

// HTML indicates an expected call of HTML.
func (mr *MockCsmContextMockRecorder) HTML(code, html interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HTML", reflect.TypeOf((*MockCsmContext)(nil).HTML), code, html)
}

// HTMLBlob mocks base method.
func (m *MockCsmContext) HTMLBlob(code int, b []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HTMLBlob", code, b)
	ret0, _ := ret[0].(error)
	return ret0
}

// HTMLBlob indicates an expected call of HTMLBlob.
func (mr *MockCsmContextMockRecorder) HTMLBlob(code, b interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HTMLBlob", reflect.TypeOf((*MockCsmContext)(nil).HTMLBlob), code, b)
}

// Handler mocks base method.
func (m *MockCsmContext) Handler() echo.HandlerFunc {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Handler")
	ret0, _ := ret[0].(echo.HandlerFunc)
	return ret0
}

// Handler indicates an expected call of Handler.
func (mr *MockCsmContextMockRecorder) Handler() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Handler", reflect.TypeOf((*MockCsmContext)(nil).Handler))
}

// Inline mocks base method.
func (m *MockCsmContext) Inline(file, name string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Inline", file, name)
	ret0, _ := ret[0].(error)
	return ret0
}

// Inline indicates an expected call of Inline.
func (mr *MockCsmContextMockRecorder) Inline(file, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Inline", reflect.TypeOf((*MockCsmContext)(nil).Inline), file, name)
}

// IsTLS mocks base method.
func (m *MockCsmContext) IsTLS() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsTLS")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsTLS indicates an expected call of IsTLS.
func (mr *MockCsmContextMockRecorder) IsTLS() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTLS", reflect.TypeOf((*MockCsmContext)(nil).IsTLS))
}

// IsWebSocket mocks base method.
func (m *MockCsmContext) IsWebSocket() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsWebSocket")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsWebSocket indicates an expected call of IsWebSocket.
func (mr *MockCsmContextMockRecorder) IsWebSocket() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsWebSocket", reflect.TypeOf((*MockCsmContext)(nil).IsWebSocket))
}

// JSON mocks base method.
func (m *MockCsmContext) JSON(code int, i interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSON", code, i)
	ret0, _ := ret[0].(error)
	return ret0
}

// JSON indicates an expected call of JSON.
func (mr *MockCsmContextMockRecorder) JSON(code, i interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSON", reflect.TypeOf((*MockCsmContext)(nil).JSON), code, i)
}

// JSONBlob mocks base method.
func (m *MockCsmContext) JSONBlob(code int, b []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONBlob", code, b)
	ret0, _ := ret[0].(error)
	return ret0
}

// JSONBlob indicates an expected call of JSONBlob.
func (mr *MockCsmContextMockRecorder) JSONBlob(code, b interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONBlob", reflect.TypeOf((*MockCsmContext)(nil).JSONBlob), code, b)
}

// JSONP mocks base method.
func (m *MockCsmContext) JSONP(code int, callback string, i interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONP", code, callback, i)
	ret0, _ := ret[0].(error)
	return ret0
}

// JSONP indicates an expected call of JSONP.
func (mr *MockCsmContextMockRecorder) JSONP(code, callback, i interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONP", reflect.TypeOf((*MockCsmContext)(nil).JSONP), code, callback, i)
}

// JSONPBlob mocks base method.
func (m *MockCsmContext) JSONPBlob(code int, callback string, b []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONPBlob", code, callback, b)
	ret0, _ := ret[0].(error)
	return ret0
}

// JSONPBlob indicates an expected call of JSONPBlob.
func (mr *MockCsmContextMockRecorder) JSONPBlob(code, callback, b interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONPBlob", reflect.TypeOf((*MockCsmContext)(nil).JSONPBlob), code, callback, b)
}

// JSONPretty mocks base method.
func (m *MockCsmContext) JSONPretty(code int, i interface{}, indent string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONPretty", code, i, indent)
	ret0, _ := ret[0].(error)
	return ret0
}

// JSONPretty indicates an expected call of JSONPretty.
func (mr *MockCsmContextMockRecorder) JSONPretty(code, i, indent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONPretty", reflect.TypeOf((*MockCsmContext)(nil).JSONPretty), code, i, indent)
}

// Logger mocks base method.
func (m *MockCsmContext) Logger() echo.Logger {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Logger")
	ret0, _ := ret[0].(echo.Logger)
	return ret0
}

// Logger indicates an expected call of Logger.
func (mr *MockCsmContextMockRecorder) Logger() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Logger", reflect.TypeOf((*MockCsmContext)(nil).Logger))
}

// MultipartForm mocks base method.
func (m *MockCsmContext) MultipartForm() (*multipart.Form, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultipartForm")
	ret0, _ := ret[0].(*multipart.Form)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultipartForm indicates an expected call of MultipartForm.
func (mr *MockCsmContextMockRecorder) MultipartForm() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultipartForm", reflect.TypeOf((*MockCsmContext)(nil).MultipartForm))
}

// NoContent mocks base method.
func (m *MockCsmContext) NoContent(code int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NoContent", code)
	ret0, _ := ret[0].(error)
	return ret0
}

// NoContent indicates an expected call of NoContent.
func (mr *MockCsmContextMockRecorder) NoContent(code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NoContent", reflect.TypeOf((*MockCsmContext)(nil).NoContent), code)
}

// Param mocks base method.
func (m *MockCsmContext) Param(name string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Param", name)
	ret0, _ := ret[0].(string)
	return ret0
}

// Param indicates an expected call of Param.
func (mr *MockCsmContextMockRecorder) Param(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Param", reflect.TypeOf((*MockCsmContext)(nil).Param), name)
}

// ParamNames mocks base method.
func (m *MockCsmContext) ParamNames() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParamNames")
	ret0, _ := ret[0].([]string)
	return ret0
}

// ParamNames indicates an expected call of ParamNames.
func (mr *MockCsmContextMockRecorder) ParamNames() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParamNames", reflect.TypeOf((*MockCsmContext)(nil).ParamNames))
}

// ParamValues mocks base method.
func (m *MockCsmContext) ParamValues() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParamValues")
	ret0, _ := ret[0].([]string)
	return ret0
}

// ParamValues indicates an expected call of ParamValues.
func (mr *MockCsmContextMockRecorder) ParamValues() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParamValues", reflect.TypeOf((*MockCsmContext)(nil).ParamValues))
}

// Path mocks base method.
func (m *MockCsmContext) Path() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Path")
	ret0, _ := ret[0].(string)
	return ret0
}

// Path indicates an expected call of Path.
func (mr *MockCsmContextMockRecorder) Path() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Path", reflect.TypeOf((*MockCsmContext)(nil).Path))
}

// QueryParam mocks base method.
func (m *MockCsmContext) QueryParam(name string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryParam", name)
	ret0, _ := ret[0].(string)
	return ret0
}

// QueryParam indicates an expected call of QueryParam.
func (mr *MockCsmContextMockRecorder) QueryParam(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryParam", reflect.TypeOf((*MockCsmContext)(nil).QueryParam), name)
}

// QueryParams mocks base method.
func (m *MockCsmContext) QueryParams() url.Values {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryParams")
	ret0, _ := ret[0].(url.Values)
	return ret0
}

// QueryParams indicates an expected call of QueryParams.
func (mr *MockCsmContextMockRecorder) QueryParams() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryParams", reflect.TypeOf((*MockCsmContext)(nil).QueryParams))
}

// QueryString mocks base method.
func (m *MockCsmContext) QueryString() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryString")
	ret0, _ := ret[0].(string)
	return ret0
}

// QueryString indicates an expected call of QueryString.
func (mr *MockCsmContextMockRecorder) QueryString() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryString", reflect.TypeOf((*MockCsmContext)(nil).QueryString))
}

// RealIP mocks base method.
func (m *MockCsmContext) RealIP() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RealIP")
	ret0, _ := ret[0].(string)
	return ret0
}

// RealIP indicates an expected call of RealIP.
func (mr *MockCsmContextMockRecorder) RealIP() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RealIP", reflect.TypeOf((*MockCsmContext)(nil).RealIP))
}

// Redirect mocks base method.
func (m *MockCsmContext) Redirect(code int, url string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Redirect", code, url)
	ret0, _ := ret[0].(error)
	return ret0
}

// Redirect indicates an expected call of Redirect.
func (mr *MockCsmContextMockRecorder) Redirect(code, url interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Redirect", reflect.TypeOf((*MockCsmContext)(nil).Redirect), code, url)
}

// Render mocks base method.
func (m *MockCsmContext) Render(code int, name string, data interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Render", code, name, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// Render indicates an expected call of Render.
func (mr *MockCsmContextMockRecorder) Render(code, name, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Render", reflect.TypeOf((*MockCsmContext)(nil).Render), code, name, data)
}

// Request mocks base method.
func (m *MockCsmContext) Request() *http.Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Request")
	ret0, _ := ret[0].(*http.Request)
	return ret0
}

// Request indicates an expected call of Request.
func (mr *MockCsmContextMockRecorder) Request() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Request", reflect.TypeOf((*MockCsmContext)(nil).Request))
}

// RequestID mocks base method.
func (m *MockCsmContext) RequestID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestID")
	ret0, _ := ret[0].(string)
	return ret0
}

// RequestID indicates an expected call of RequestID.
func (mr *MockCsmContextMockRecorder) RequestID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestID", reflect.TypeOf((*MockCsmContext)(nil).RequestID))
}

// Reset mocks base method.
func (m *MockCsmContext) Reset(r *http.Request, w http.ResponseWriter) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Reset", r, w)
}

// Reset indicates an expected call of Reset.
func (mr *MockCsmContextMockRecorder) Reset(r, w interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reset", reflect.TypeOf((*MockCsmContext)(nil).Reset), r, w)
}

// ResetResourceWith mocks base method.
func (m *MockCsmContext) ResetResourceWith(resource string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ResetResourceWith", resource)
}

// ResetResourceWith indicates an expected call of ResetResourceWith.
func (mr *MockCsmContextMockRecorder) ResetResourceWith(resource interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetResourceWith", reflect.TypeOf((*MockCsmContext)(nil).ResetResourceWith), resource)
}

// Response mocks base method.
func (m *MockCsmContext) Response() *echo.Response {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Response")
	ret0, _ := ret[0].(*echo.Response)
	return ret0
}

// Response indicates an expected call of Response.
func (mr *MockCsmContextMockRecorder) Response() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Response", reflect.TypeOf((*MockCsmContext)(nil).Response))
}

// Scheme mocks base method.
func (m *MockCsmContext) Scheme() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Scheme")
	ret0, _ := ret[0].(string)
	return ret0
}

// Scheme indicates an expected call of Scheme.
func (mr *MockCsmContextMockRecorder) Scheme() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scheme", reflect.TypeOf((*MockCsmContext)(nil).Scheme))
}

// Set mocks base method.
func (m *MockCsmContext) Set(key string, val interface{}) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Set", key, val)
}

// Set indicates an expected call of Set.
func (mr *MockCsmContextMockRecorder) Set(key, val interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockCsmContext)(nil).Set), key, val)
}

// SetCookie mocks base method.
func (m *MockCsmContext) SetCookie(cookie *http.Cookie) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetCookie", cookie)
}

// SetCookie indicates an expected call of SetCookie.
func (mr *MockCsmContextMockRecorder) SetCookie(cookie interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCookie", reflect.TypeOf((*MockCsmContext)(nil).SetCookie), cookie)
}

// SetHandler mocks base method.
func (m *MockCsmContext) SetHandler(h echo.HandlerFunc) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetHandler", h)
}

// SetHandler indicates an expected call of SetHandler.
func (mr *MockCsmContextMockRecorder) SetHandler(h interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHandler", reflect.TypeOf((*MockCsmContext)(nil).SetHandler), h)
}

// SetLogger mocks base method.
func (m *MockCsmContext) SetLogger(l echo.Logger) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetLogger", l)
}

// SetLogger indicates an expected call of SetLogger.
func (mr *MockCsmContextMockRecorder) SetLogger(l interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLogger", reflect.TypeOf((*MockCsmContext)(nil).SetLogger), l)
}

// SetParamNames mocks base method.
func (m *MockCsmContext) SetParamNames(names ...string) {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range names {
		varargs = append(varargs, a)
	}
	m.ctrl.Call(m, "SetParamNames", varargs...)
}

// SetParamNames indicates an expected call of SetParamNames.
func (mr *MockCsmContextMockRecorder) SetParamNames(names ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetParamNames", reflect.TypeOf((*MockCsmContext)(nil).SetParamNames), names...)
}

// SetParamValues mocks base method.
func (m *MockCsmContext) SetParamValues(values ...string) {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range values {
		varargs = append(varargs, a)
	}
	m.ctrl.Call(m, "SetParamValues", varargs...)
}

// SetParamValues indicates an expected call of SetParamValues.
func (mr *MockCsmContextMockRecorder) SetParamValues(values ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetParamValues", reflect.TypeOf((*MockCsmContext)(nil).SetParamValues), values...)
}

// SetPath mocks base method.
func (m *MockCsmContext) SetPath(p string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetPath", p)
}

// SetPath indicates an expected call of SetPath.
func (mr *MockCsmContextMockRecorder) SetPath(p interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPath", reflect.TypeOf((*MockCsmContext)(nil).SetPath), p)
}

// SetRequest mocks base method.
func (m *MockCsmContext) SetRequest(r *http.Request) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetRequest", r)
}

// SetRequest indicates an expected call of SetRequest.
func (mr *MockCsmContextMockRecorder) SetRequest(r interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRequest", reflect.TypeOf((*MockCsmContext)(nil).SetRequest), r)
}

// SetResource mocks base method.
func (m *MockCsmContext) SetResource(resource string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetResource", resource)
}

// SetResource indicates an expected call of SetResource.
func (mr *MockCsmContextMockRecorder) SetResource(resource interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetResource", reflect.TypeOf((*MockCsmContext)(nil).SetResource), resource)
}

// SetResponse mocks base method.
func (m *MockCsmContext) SetResponse(r *echo.Response) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetResponse", r)
}

// SetResponse indicates an expected call of SetResponse.
func (mr *MockCsmContextMockRecorder) SetResponse(r interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetResponse", reflect.TypeOf((*MockCsmContext)(nil).SetResponse), r)
}

// Stream mocks base method.
func (m *MockCsmContext) Stream(code int, contentType string, r io.Reader) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stream", code, contentType, r)
	ret0, _ := ret[0].(error)
	return ret0
}

// Stream indicates an expected call of Stream.
func (mr *MockCsmContextMockRecorder) Stream(code, contentType, r interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stream", reflect.TypeOf((*MockCsmContext)(nil).Stream), code, contentType, r)
}

// String mocks base method.
func (m *MockCsmContext) String(code int, s string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String", code, s)
	ret0, _ := ret[0].(error)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockCsmContextMockRecorder) String(code, s interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockCsmContext)(nil).String), code, s)
}

// Validate mocks base method.
func (m *MockCsmContext) Validate(i interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Validate", i)
	ret0, _ := ret[0].(error)
	return ret0
}

// Validate indicates an expected call of Validate.
func (mr *MockCsmContextMockRecorder) Validate(i interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Validate", reflect.TypeOf((*MockCsmContext)(nil).Validate), i)
}

// WithLoggerField mocks base method.
func (m *MockCsmContext) WithLoggerField(key string, value interface{}) context.CsmContext {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithLoggerField", key, value)
	ret0, _ := ret[0].(context.CsmContext)
	return ret0
}

// WithLoggerField indicates an expected call of WithLoggerField.
func (mr *MockCsmContextMockRecorder) WithLoggerField(key, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithLoggerField", reflect.TypeOf((*MockCsmContext)(nil).WithLoggerField), key, value)
}

// WithLoggerFields mocks base method.
func (m *MockCsmContext) WithLoggerFields(fields csmlog.Fields) context.CsmContext {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithLoggerFields", fields)
	ret0, _ := ret[0].(context.CsmContext)
	return ret0
}

// WithLoggerFields indicates an expected call of WithLoggerFields.
func (mr *MockCsmContextMockRecorder) WithLoggerFields(fields interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithLoggerFields", reflect.TypeOf((*MockCsmContext)(nil).WithLoggerFields), fields)
}

// XML mocks base method.
func (m *MockCsmContext) XML(code int, i interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XML", code, i)
	ret0, _ := ret[0].(error)
	return ret0
}

// XML indicates an expected call of XML.
func (mr *MockCsmContextMockRecorder) XML(code, i interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XML", reflect.TypeOf((*MockCsmContext)(nil).XML), code, i)
}

// XMLBlob mocks base method.
func (m *MockCsmContext) XMLBlob(code int, b []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XMLBlob", code, b)
	ret0, _ := ret[0].(error)
	return ret0
}

// XMLBlob indicates an expected call of XMLBlob.
func (mr *MockCsmContextMockRecorder) XMLBlob(code, b interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XMLBlob", reflect.TypeOf((*MockCsmContext)(nil).XMLBlob), code, b)
}

// XMLPretty mocks base method.
func (m *MockCsmContext) XMLPretty(code int, i interface{}, indent string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XMLPretty", code, i, indent)
	ret0, _ := ret[0].(error)
	return ret0
}

// XMLPretty indicates an expected call of XMLPretty.
func (mr *MockCsmContextMockRecorder) XMLPretty(code, i, indent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XMLPretty", reflect.TypeOf((*MockCsmContext)(nil).XMLPretty), code, i, indent)
}
