package mock

import (
	"io"
	"mime/multipart"
	"net/http"
	"net/url"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/mock"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

// CsmContext is an autogenerated mock type for the CsmContext type
type CsmContext struct {
	mock.Mock
}

func (c CsmContext) CsmLogger() *csmlog.Logger {
	return nil
}

func (c CsmContext) WithLoggerField(key string, value interface{}) context.CsmContext {
	return nil
}

func (c CsmContext) WithLoggerFields(fields csmlog.Fields) context.CsmContext {
	return nil
}

func (c CsmContext) RequestID() string {
	return ""
}

func (c CsmContext) GetResource() string {
	ret := c.Called()

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

func (c CsmContext) SetResource(resource string) {
	c.Called(resource)
}

func (c CsmContext) ResetResourceWith(resource string) {
	c.Called(resource)
}

func (c CsmContext) Request() *http.Request {
	ret := c.Called()

	var request *http.Request
	if rf, ok := ret.Get(0).(func() *http.Request); ok {
		request = rf()
	} else {
		request = ret.Get(0).(*http.Request)
	}

	return request
}

func (c CsmContext) SetRequest(r *http.Request) {
	return
}

func (c CsmContext) SetResponse(r *echo.Response) {
	return
}

func (c CsmContext) Response() *echo.Response {
	return nil
}

func (c CsmContext) IsTLS() bool {
	return false
}

func (c CsmContext) IsWebSocket() bool {
	return false
}

func (c CsmContext) Scheme() string {
	return ""
}

func (c CsmContext) RealIP() string {
	return ""
}

func (c CsmContext) Path() string {
	return ""
}

func (c CsmContext) SetPath(p string) {
	return
}

func (c CsmContext) Param(name string) string {
	ret := c.Called(name)

	var r0 string
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

func (c CsmContext) ParamNames() []string {
	return nil
}

func (c CsmContext) SetParamNames(names ...string) {
	return
}

func (c CsmContext) ParamValues() []string {
	return nil
}

func (c CsmContext) SetParamValues(values ...string) {
	return
}

func (c CsmContext) QueryParam(name string) string {
	return ""
}

func (c CsmContext) QueryParams() url.Values {
	return nil
}

func (c CsmContext) QueryString() string {
	return ""
}

func (c CsmContext) FormValue(name string) string {
	return ""
}

func (c CsmContext) FormParams() (url.Values, error) {
	return nil, nil
}

func (c CsmContext) FormFile(name string) (*multipart.FileHeader, error) {
	return nil, nil
}

func (c CsmContext) MultipartForm() (*multipart.Form, error) {
	return nil, nil
}

func (c CsmContext) Cookie(name string) (*http.Cookie, error) {
	return nil, nil
}

func (c CsmContext) SetCookie(cookie *http.Cookie) {
	return
}

func (c CsmContext) Cookies() []*http.Cookie {
	return nil
}

func (c CsmContext) Get(key string) interface{} {
	ret := c.Called(key)

	var r0 interface{}
	if rf, ok := ret.Get(0).(func(string) interface{}); ok {
		r0 = rf(key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	return r0
}

func (c CsmContext) Set(key string, val interface{}) {
	return
}

func (c CsmContext) Bind(i interface{}) error {
	return nil
}

func (c CsmContext) Validate(i interface{}) error {
	return nil
}

func (c CsmContext) Render(code int, name string, data interface{}) error {
	return nil
}

func (c CsmContext) HTML(code int, html string) error {
	return nil
}

func (c CsmContext) HTMLBlob(code int, b []byte) error {
	return nil
}

func (c CsmContext) String(code int, s string) error {
	return nil
}

func (c CsmContext) JSON(code int, i interface{}) error {
	return nil
}

func (c CsmContext) JSONPretty(code int, i interface{}, indent string) error {
	return nil
}

func (c CsmContext) JSONBlob(code int, b []byte) error {
	return nil
}

func (c CsmContext) JSONP(code int, callback string, i interface{}) error {
	return nil
}

func (c CsmContext) JSONPBlob(code int, callback string, b []byte) error {
	return nil
}

func (c CsmContext) XML(code int, i interface{}) error {
	return nil
}

func (c CsmContext) XMLPretty(code int, i interface{}, indent string) error {
	return nil
}

func (c CsmContext) XMLBlob(code int, b []byte) error {
	return nil
}

func (c CsmContext) Blob(code int, contentType string, b []byte) error {
	return nil
}

func (c CsmContext) Stream(code int, contentType string, r io.Reader) error {
	return nil
}

func (c CsmContext) File(file string) error {
	return nil
}

func (c CsmContext) Attachment(file string, name string) error {
	return nil
}

func (c CsmContext) Inline(file string, name string) error {
	return nil
}

func (c CsmContext) NoContent(code int) error {
	return nil
}

func (c CsmContext) Redirect(code int, url string) error {
	return nil
}

func (c CsmContext) Error(err error) {
	return
}

func (c CsmContext) Handler() echo.HandlerFunc {
	return nil
}

func (c CsmContext) SetHandler(h echo.HandlerFunc) {
	return
}

func (c CsmContext) Logger() echo.Logger {
	return nil
}

func (c CsmContext) SetLogger(l echo.Logger) {
	return
}

func (c CsmContext) Echo() *echo.Echo {
	return nil
}

func (c CsmContext) Reset(r *http.Request, w http.ResponseWriter) {
	return
}
