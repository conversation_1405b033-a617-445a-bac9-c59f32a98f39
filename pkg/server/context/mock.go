package context

import (
	"net/http/httptest"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"go.uber.org/zap/zaptest/observer"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

const (
	mockRequestID = "request-id"
)

func NewCsmContextMock() (CsmContext, *observer.ObservedLogs) {
	obs, logs := observer.New(zapcore.DebugLevel)
	csmLogger := csmlog.Logger(*zap.New(obs))

	e := echo.New()
	e.Debug = true

	ctx := &csmContext{
		Context: e.NewContext(
			httptest.NewRequest("GET", "/", nil),
			httptest.NewRecorder(),
		),
		csmLogger: &csmLogger,
	}
	return ctx, logs
}

func MockNewCsmContext() CsmContext {
	obs, _ := observer.New(zapcore.DebugLevel)
	csmLogger := csmlog.Logger(*zap.New(obs))

	e := echo.New()
	e.Debug = true

	ctx := &csmContext{
		Context: e.NewContext(
			httptest.NewRequest("PUT", "/api/logic/csm/v1/instance/csm-nKdiXdmg/sidecarQuota", nil),
			httptest.NewRecorder(),
		),
		csmLogger: &csmLogger,
	}
	return ctx
}

func NewSimpleCsmContext() CsmContext {
	e := echo.New()

	ctx := &csmContext{
		Context: e.NewContext(
			httptest.NewRequest("GET", "/", nil),
			httptest.NewRecorder(),
		),
		csmLogger: csmlog.NewLogger(),
	}
	return ctx
}
