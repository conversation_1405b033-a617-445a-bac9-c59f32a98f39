package context

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
)

func TestCsmHTTPErrorHandler(t *testing.T) {
	ass := assert.New(t)

	tests := []struct {
		name    string
		err     error
		code    int
		rspJson *csm.GenericError
	}{
		{
			name: "*echo.HTTPError csmError.Error",
			err:  csmErr.NewServiceException("mock csm error"),
			code: http.StatusInternalServerError,
			rspJson: &csm.GenericError{
				Code:    "InternalError",
				Message: "We encountered an internal error Please try again. INTERNAL FOR DEBUGGING: mock csm error",
			},
		},
		{
			name: "*echo.HTTPError string",
			err: &echo.HTTPError{
				Code:     http.StatusNotFound,
				Message:  "mock string error",
				Internal: errors.New("internal error 1"),
			},
			code: http.StatusNotFound,
			rspJson: &csm.GenericError{
				Code:    "Not Found",
				Message: "mock string error. INTERNAL FOR DEBUGGING: internal error 1",
			},
		},
		{
			name: "*echo.HTTPError default",
			err: &echo.HTTPError{
				Code:     http.StatusNotFound,
				Message:  errors.New("error string"),
				Internal: errors.New("internal error 2"),
			},
			code: http.StatusNotFound,
			rspJson: &csm.GenericError{
				Code:    "InternalError",
				Message: "We encountered an internal error Please try again. INTERNAL FOR DEBUGGING: internal error 2",
			},
		},
		{
			name: "*bce.ResponseError",
			err: &csm.ResponseError{
				StatusCode: http.StatusNotFound,
				GenericError: csm.GenericError{
					Code:    "error code",
					Message: "mock bce error",
				},
			},
			code: http.StatusNotFound,
			rspJson: &csm.GenericError{
				Code:    "error code",
				Message: "mock bce error",
			},
		},
		{
			name: "default",
			err:  errors.New("default error"),
			code: http.StatusInternalServerError,
			rspJson: &csm.GenericError{
				Code:    "Internal Server Error",
				Message: "default error",
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ctx, _ := NewCsmContextMock()
			CsmHTTPErrorHandler(test.err, ctx)
			rsp := ctx.Response().Writer.(*httptest.ResponseRecorder)
			rspJson := &csm.GenericError{}
			json.Unmarshal(rsp.Body.Bytes(), rspJson)
			ass.Equal(test.code, rsp.Code, fmt.Sprintf("%s response status code mismatch", test.name))
			ass.Equal(test.rspJson, rspJson, fmt.Sprintf("%s response json mismatch", test.name))
		})
	}
}
