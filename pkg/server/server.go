package server

import (
	"context"
	"net"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
)

// ServingInfo xxx
type ServingInfo struct {
	// Listener is the server network listener.
	Listener net.Listener
	// ReadTimeout is the maximum duration before timing out read of the request header
	ReadTimeout time.Duration
	// WriteTimeout is the maximum duration before timing out write of the response
	WriteTimeout time.Duration
}

// GenericServer contains state for a csm server.
type GenericServer struct {
	// E is the instance of echo
	E *echo.Echo

	ServingInfo *ServingInfo

	// ShutdownTimeout is the timeout used for server shutdown. This specifies the timeout before server
	// gracefully shutdown returns.
	ShutdownTimeout time.Duration
}

func (s *GenericServer) Run(stopCh <-chan struct{}) {
	server := &http.Server{
		Addr:              s.ServingInfo.Listener.Addr().String(),
		ReadHeaderTimeout: s.ServingInfo.ReadTimeout,
		WriteTimeout:      s.ServingInfo.WriteTimeout,
		MaxHeaderBytes:    1 << 20,
	}

	var listener net.Listener
	switch s.ServingInfo.Listener.(type) {
	case *net.TCPListener:
		listener = tcpKeepAliveListener{s.ServingInfo.Listener.(*net.TCPListener)}
	default:
		listener = s.ServingInfo.Listener
	}

	s.E.Listener = listener

	go func() {
		//debug.SetGCPercent(1000)
		if err := s.E.StartServer(server); err != nil {
			// TODO 似乎不会跑到这里？
			s.E.Logger.Info("shutting down the server")
		}
	}()

	<-stopCh
	ctx, cancel := context.WithTimeout(context.Background(), s.ShutdownTimeout)
	defer cancel()
	if err := s.E.Shutdown(ctx); err != nil {
		s.E.Logger.Fatal(err)
	}
}

// tcpKeepAliveListener sets TCP keep-alive timeouts on accepted
// connections. It's used by ListenAndServe and ListenAndServeTLS so
// dead TCP connections (e.g. closing laptop mid-download) eventually
// go away.
type tcpKeepAliveListener struct {
	*net.TCPListener
}

func (ln tcpKeepAliveListener) Accept() (net.Conn, error) {
	tc, err := ln.AcceptTCP()
	if err != nil {
		return nil, err
	}
	tc.SetKeepAlive(true)
	tc.SetKeepAlivePeriod(3 * time.Minute)
	return tc, nil
}
