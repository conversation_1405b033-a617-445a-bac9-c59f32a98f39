package server

import (
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"

	csmError "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type HandlerFunc func(context.CsmContext) error

// CsmHandler 将 echo.Context 包装成 CsmContext
func CsmHandler(h HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		cc := c.(context.CsmContext)
		return h(cc)
	}
}

// CsmException 判断err的格式，如果已经是一个*echo.HTTPError，就追加msg并返回该错误，否则使用fallback返回该exception
func CsmException(fallback csmError.ExceptionGenerator, msg string, errs ...error) *echo.HTTPError {
	if len(errs) == 0 || len(errs) > 1 {
		return fallback(msg, errs...)
	}
	// only have one error
	switch e := errs[0].(type) {
	case *echo.HTTPError:
		newInternal := errors.WithMessage(errs[0], msg)
		e.Internal = newInternal
		return e
	default:
		return fallback(msg, errs[0])
	}
}
