package middleware

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

func TestCheckIAMPermission(t *testing.T) {
	tests := []struct {
		name             string
		resource         string
		owner            string
		service          string
		region           string
		permission       []string
		effect           string
		allowDefaultDeny bool
		handler          echo.HandlerFunc
		expectErr        string
		result           interface{}
		config           *util.TestServerConfig
	}{
		{
			name:    "normal",
			handler: func(c echo.Context) error { return nil },
			effect:  iam.PermissionAllow,
			result:  &sdkIAM.User{ID: "1", Domain: sdkIAM.UserDomain{ID: "1"}},
			config: &util.TestServerConfig{
				ResponseBody:    []byte(`{"verify_result":{"id":"","effect":"ALLOW","eid":""},"token":{"id":"","expires_at":"0001-01-01T00:00:00Z","issued_at":"0001-01-01T00:00:00Z","methods":null,"domain":null,"project":null,"user":{"id":"1","name":"","domain":{"id":"1"}},"roles":null,"catalog":null}}`),
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
			},
			allowDefaultDeny: true,
		},
		{
			name:    "not allow default_deny",
			handler: func(c echo.Context) error { return nil },
			result:  nil,
			effect:  iam.PermissionDefaultDeny,
			config: &util.TestServerConfig{
				ResponseBody:    []byte(`{"verify_result":{"id":"","effect":"DEFAULT_DENY","eid":""},"token":{"id":"","expires_at":"0001-01-01T00:00:00Z","issued_at":"0001-01-01T00:00:00Z","methods":null,"domain":null,"project":null,"user":{"id":"1","name":"abc","domain":{"id":"1"}},"roles":null,"catalog":null}}`),
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
			},
			allowDefaultDeny: false,
			expectErr:        "code=403, message=authenticate user  failed for permission ",
		},
		{
			name:    "allow default_deny",
			effect:  iam.PermissionDefaultDeny,
			handler: func(c echo.Context) error { return nil },
			result:  &sdkIAM.User{ID: "1", Domain: sdkIAM.UserDomain{ID: "1"}},
			config: &util.TestServerConfig{
				ResponseBody:    []byte(`{"verify_result":{"id":"","effect":"DEFAULT_DENY","eid":""},"token":{"id":"","expires_at":"0001-01-01T00:00:00Z","issued_at":"0001-01-01T00:00:00Z","methods":null,"domain":null,"project":null,"user":{"id":"1","name":"","domain":{"id":"1"}},"roles":null,"catalog":null}}`),
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
			},
			allowDefaultDeny: true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			oldIamDefaultClient := iam.DefaultClient
			svr := util.NewTestServer(t, test.config)
			iam.DefaultClient = iam.NewMockClient(svr.URL)
			count := 0
			mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				if count == 0 {
					// 第一次请求都返回Token
					token := &sdkIAM.TokenWrapper{
						Token: sdkIAM.Token{
							ID: "default-token-id",
						},
					}
					// 序列化响应为JSON格式
					jsonResponse, err := json.Marshal(token)
					if err != nil {
						http.Error(w, err.Error(), http.StatusInternalServerError)
						return
					}
					// 设置响应头并返回JSON数据
					w.Header().Set("Content-Type", "application/json")
					// sdk中使用这个 tokenId := resp.Header("X-Subject-Token")
					w.Header().Set("X-Subject-Token", "default-token-id")
					_, writerErr := w.Write(jsonResponse)
					if writerErr != nil {
						http.Error(w, writerErr.Error(), http.StatusInternalServerError)
					}
				} else if count == 1 {
					// 第一次请求都返回Token
					token := &sdkIAM.TokenAndVerifyResult{
						Token: sdkIAM.Token{
							ID: "default-token-id",
						},
						VerifyResult: sdkIAM.VerifyResult{
							Effect: test.effect,
						},
					}
					// 序列化响应为JSON格式
					jsonResponse, err := json.Marshal(token)
					if err != nil {
						http.Error(w, err.Error(), http.StatusInternalServerError)
						return
					}
					// 设置响应头并返回JSON数据
					w.Header().Set("Content-Type", "application/json")
					// sdk中使用这个 tokenId := resp.Header("X-Subject-Token")
					w.Header().Set("X-Subject-Token", "default-token-id")
					_, writerErr := w.Write(jsonResponse)
					if writerErr != nil {
						http.Error(w, writerErr.Error(), http.StatusInternalServerError)
					}
				}
				count++
			}))
			defer mockServer.Close()

			iam.DefaultClient.IamCleint = &sdkIAM.BceClient{
				Config: &sdkIAM.BceClientConfiguration{
					Endpoint: mockServer.URL,
					UserName: "user",
					Password: "pass",
				},
				AgentConfiguration: &sdkIAM.AgentConfiguration{
					Endpoint: mockServer.URL,
				},
			}

			ctx, _ := context.NewCsmContextMock()
			h := checkIAMPermission(test.service, test.permission, test.allowDefaultDeny)(test.handler)
			err := h(ctx)
			if err != nil || test.expectErr != "" {
				if assert.NotNil(t, err) {
					assert.Equal(t, test.expectErr, err.Error())
				}
			}
			//assert.Equal(t, test.result, ctx.Get(iam.ContextIAMUser), "user mismatch")

			svr.Close()
			iam.DefaultClient = oldIamDefaultClient
			if !test.allowDefaultDeny {
				oldMock := viper.GetBool("local.mock")
				oldDev := viper.GetBool("local.dev")
				viper.Set("local.mock", true)
				viper.Set("local.dev", true)
				h := checkIAMPermission(test.service, test.permission, test.allowDefaultDeny)(test.handler)
				err := h(ctx)
				assert.Equal(t, err, nil)
				viper.Set("local.mock", oldMock)
				viper.Set("local.dev", oldDev)
			}
		})
	}
}

func TestCheckIAMPermission2(t *testing.T) {
	ass := assert.New(t)
	ctx, _ := context.NewCsmContextMock()
	response := &iam.VerifyResultWithToken{
		Result: &iam.SinglePermissionVerifyResult{
			Effect: iam.PermissionDeny,
		},
	}
	responseBytes, _ := json.Marshal(response)

	svr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer r.Body.Close()
		w.Header().Set("Content-Type", "application/json")
		w.Write(responseBytes)
	}))
	defer svr.Close()

	oldIamDefaultClient := iam.DefaultClient
	iam.DefaultClient = iam.NewMockClient(svr.URL)
	defer func() { iam.DefaultClient = oldIamDefaultClient }()

	count := 0
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if count == 0 {
			// 第一次请求都返回Token
			token := &sdkIAM.TokenWrapper{
				Token: sdkIAM.Token{
					ID: "default-token-id",
				},
			}
			// 序列化响应为JSON格式
			jsonResponse, err := json.Marshal(token)
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			// 设置响应头并返回JSON数据
			w.Header().Set("Content-Type", "application/json")
			// sdk中使用这个 tokenId := resp.Header("X-Subject-Token")
			w.Header().Set("X-Subject-Token", "default-token-id")
			w.Write(jsonResponse)
		} else if count == 1 {
			// 第一次请求都返回Token
			token := &sdkIAM.TokenAndVerifyResult{
				Token: sdkIAM.Token{
					ID: "default-token-id",
				},
				VerifyResult: sdkIAM.VerifyResult{
					Effect: iam.PermissionDeny,
				},
			}
			// 序列化响应为JSON格式
			jsonResponse, err := json.Marshal(token)
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			// 设置响应头并返回JSON数据
			w.Header().Set("Content-Type", "application/json")
			// sdk中使用这个 tokenId := resp.Header("X-Subject-Token")
			w.Header().Set("X-Subject-Token", "default-token-id")
			w.Write(jsonResponse)
		}
		count++
	}))
	defer mockServer.Close()

	iam.DefaultClient.IamCleint = &sdkIAM.BceClient{
		Config: &sdkIAM.BceClientConfiguration{
			Endpoint: mockServer.URL,
			UserName: "user",
			Password: "pass",
		},
		AgentConfiguration: &sdkIAM.AgentConfiguration{
			Endpoint: mockServer.URL,
		},
	}

	h := checkIAMPermission("appService", []string{}, true)(func(c echo.Context) error { return nil })
	err := h(ctx)
	httpErr := err.(*echo.HTTPError)
	ass.Equal(http.StatusForbidden, httpErr.Code, "unexpected status code")
}

func TestCheckIAMAuthorization(t *testing.T) {

	tests := []struct {
		local      bool
		name       string
		resource   string
		owner      string
		service    string
		region     string
		permission []string
		handler    echo.HandlerFunc
		result     *iam.User
		config     *util.TestServerConfig
	}{
		{
			local:   false,
			name:    "normal",
			handler: func(c echo.Context) error { return nil },
			result:  &iam.User{ID: "1"},
			config: &util.TestServerConfig{
				ResponseBody:    []byte(`{"verify_result":{"id":"","effect":"ALLOW","eid":""},"token":{"id":"","expires_at":"0001-01-01T00:00:00Z","issued_at":"0001-01-01T00:00:00Z","methods":null,"domain":null,"project":null,"user":{"id":"1","name":""},"roles":null,"catalog":null}}`),
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
			},
		},
		{
			local:   true,
			name:    "normal",
			handler: func(c echo.Context) error { return nil },
			result: &iam.User{
				ID: "1",
				Domain: &iam.Domain{
					ID: "1",
				}},
			config: &util.TestServerConfig{
				ResponseBody: []byte(`{"verify_result":{"id":"","effect":"ALLOW","eid":""},
"token":{"id":"","expires_at":"0001-01-01T00:00:00Z","issued_at":"0001-01-01T00:00:00Z",
"methods":null,"domain":null,"project":null,"user":{"id":"1","name":"","domain":{"name":"","id":"1"}},
"roles":null,"catalog":null}}`),
				ResponseHeaders: map[string]string{"Content-Type": "application/json"},
			},
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			oldIamDefaultClient := iam.DefaultClient
			svr := util.NewTestServer(t, test.config)
			iam.DefaultClient = iam.NewMockClient(svr.URL)

			count := 0
			mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				if count == 0 {
					// 第一次请求都返回Token
					token := &sdkIAM.TokenWrapper{
						Token: sdkIAM.Token{
							ID: "default-token-id",
						},
					}
					// 序列化响应为JSON格式
					jsonResponse, err := json.Marshal(token)
					if err != nil {
						http.Error(w, err.Error(), http.StatusInternalServerError)
						return
					}
					// 设置响应头并返回JSON数据
					w.Header().Set("Content-Type", "application/json")
					// sdk中使用这个 tokenId := resp.Header("X-Subject-Token")
					w.Header().Set("X-Subject-Token", "default-token-id")
					w.Write(jsonResponse)
				} else if count == 1 {
					// 第一次请求都返回Token
					token := &sdkIAM.TokenAndVerifyResult{
						Token: sdkIAM.Token{
							ID: "default-token-id",
						},
						VerifyResult: sdkIAM.VerifyResult{
							Effect: iam.PermissionAllow,
						},
					}
					// 序列化响应为JSON格式
					jsonResponse, err := json.Marshal(token)
					if err != nil {
						http.Error(w, err.Error(), http.StatusInternalServerError)
						return
					}
					// 设置响应头并返回JSON数据
					w.Header().Set("Content-Type", "application/json")
					// sdk中使用这个 tokenId := resp.Header("X-Subject-Token")
					w.Header().Set("X-Subject-Token", "default-token-id")
					w.Write(jsonResponse)
				}
				count++
			}))
			defer mockServer.Close()

			iam.DefaultClient.IamCleint = &sdkIAM.BceClient{
				Config: &sdkIAM.BceClientConfiguration{
					Endpoint: mockServer.URL,
					UserName: "user",
					Password: "pass",
				},
				AgentConfiguration: &sdkIAM.AgentConfiguration{
					Endpoint: mockServer.URL,
				},
			}
			if test.local {
				viper.Set(localDev, true)
				viper.Set(localMock, true)
				viper.Set(localMockUserID, "1")
				viper.Set(localMockDomainID, "1")
			}
			ctx, _ := context.NewCsmContextMock()
			h := CheckIAMSignature()(test.handler)
			h(ctx)

			//ass.Equal(test.result, ctx.Get(iam.ContextIAMUser), "user mismatch")

			svr.Close()
			iam.DefaultClient = oldIamDefaultClient
		})
	}
}
