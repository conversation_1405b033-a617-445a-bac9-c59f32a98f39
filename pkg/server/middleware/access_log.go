package middleware

import (
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	echoMiddleware "github.com/labstack/echo/v4/middleware"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type AccessLoggConfig struct {
	// Skipper defines a function to skip middleware.
	Skipper echoMiddleware.Skipper

	// Fields defines what a piece of access log contains
	Fields []string

	// Only print error log.
	OnlyErrLog bool
}

const (
	fieldRequestId    = "request_id"
	fieldRemoteIp     = "remote_ip"
	fieldHost         = "host"
	fieldMethod       = "method"
	fieldUri          = "uri"
	fieldPath         = "path"
	fieldProtocol     = "protocol"
	fieldReferer      = "referer"
	fieldUserAgent    = "user_agent"
	fieldStatus       = "status"
	fieldError        = "error"
	fieldLatency      = "latency"
	fieldLatencyHuman = "latency_human"
	fieldBytesIn      = "bytes_in"
	fieldBytesOut     = "bytes_out"
)

var DefaultAccessLogConfig = AccessLoggConfig{
	Skipper: echoMiddleware.DefaultSkipper,
	Fields: []string{
		fieldRemoteIp,
		fieldHost,
		fieldMethod,
		fieldUri,
		fieldUserAgent,
		fieldStatus,
		fieldError,
		fieldLatency,
		fieldLatencyHuman,
		fieldBytesIn,
		fieldBytesOut,
	},
	OnlyErrLog: false,
}

// AccessLog returns a middleware that logs HTTP requests.
func AccessLog() echo.MiddlewareFunc {
	return AccessLogWithConfig(DefaultAccessLogConfig)
}

// AccessLogWithConfig returns a Logger middleware with config.See: `AccessLog()`.
func AccessLogWithConfig(config AccessLoggConfig) echo.MiddlewareFunc {
	// Defaults
	if config.Skipper == nil {
		config.Skipper = DefaultAccessLogConfig.Skipper
	}
	if len(config.Fields) == 0 {
		config.Fields = DefaultAccessLogConfig.Fields
	}

	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) (err error) {
			if config.Skipper(c) {
				return next(c)
			}

			cc, ok := c.(context.CsmContext)
			if !ok {
				// should no error here
				err = errors.New("cannot type assert context with access log")
				return
			}

			req := cc.Request()
			res := cc.Response()
			start := time.Now()
			// catch all errors below and use error handler for csm context
			if err = next(cc); err != nil {
				cc.Error(err)
			}
			stop := time.Now()

			if config.OnlyErrLog {
				return
			}

			fields := make(map[string]interface{}, len(config.Fields))

			for _, f := range config.Fields {
				switch f {
				case fieldRequestId:
					id := req.Header.Get(context.HeaderXRequestID)
					if id == "" {
						id = res.Header().Get(context.HeaderXRequestID)
					}
					fields[fieldRequestId] = id

				case fieldRemoteIp:
					fields[fieldRemoteIp] = c.RealIP()

				case fieldHost:
					fields[fieldHost] = req.Host

				case fieldMethod:
					fields[fieldMethod] = req.Method

				case fieldUri:
					fields[fieldUri] = req.RequestURI

				case fieldPath:
					p := req.URL.Path
					if p == "" {
						p = "/"
					}
					fields[fieldPath] = p

				case fieldProtocol:
					fields[fieldProtocol] = req.Proto

				case fieldReferer:
					fields[fieldReferer] = req.Referer()

				case fieldUserAgent:
					fields[fieldUserAgent] = req.UserAgent()

				case fieldStatus:
					fields[fieldStatus] = res.Status

				case fieldError:
					if err != nil {
						fields[fieldError] = err.Error()
					} else {
						fields[fieldError] = ""
					}

				case fieldLatency:
					l := stop.Sub(start)
					fields[fieldLatency] = strconv.FormatInt(int64(l), 10)

				case fieldLatencyHuman:
					fields[fieldLatencyHuman] = stop.Sub(start).String()

				case fieldBytesIn:
					cl := req.Header.Get(echo.HeaderContentLength)
					if cl == "" {
						cl = "0"
					}
					fields[fieldBytesIn] = cl

				case fieldBytesOut:
					fields[fieldBytesOut] = strconv.FormatInt(res.Size, 10)

				default:
					switch {
					case strings.HasPrefix(f, "header:"):
						fields[f] = c.Request().Header.Get(f[7:])
					case strings.HasPrefix(f, "query:"):
						fields[f] = c.QueryParam(f[6:])
					case strings.HasPrefix(f, "form:"):
						fields[f] = c.FormValue(f[5:])
					case strings.HasPrefix(f, "cookie:"):
						cookie, err := c.Cookie(f[7:])
						if err == nil {
							fields[f] = cookie.Value
						}
					}
				}
			}

			cc.CsmLogger().WithFields(fields).Info("REQUEST")
			return
		}
	}
}
