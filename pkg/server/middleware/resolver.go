package middleware

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	instancesModel "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/promsdk"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/registercenter"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"strconv"
	"time"
)

const (
	instanceResourcePtn   = "instance/%s"
	fuzzyMatchingResource = "instance/*"
)

type loadMethod func(c context.CsmContext, resourceID string) (string, error)

// ResolveInstancesResource 解析instance资源
func ResolveInstancesResource(service instancesModel.ServiceInterface) echo.MiddlewareFunc {
	return resolveInstance(service, instanceResourcePtn)
}

// resolveInstance 解析instance资源
func resolveInstance(service instancesModel.ServiceInterface, resourcePtn string) echo.MiddlewareFunc {
	instanceLoadMethod := func(c context.CsmContext, resourceID string) (string, error) {
		// 跳过数据库验证来降低接口访问延迟
		//_, err := service.GetInstanceByInstanceUUID(c, resourceID)
		//if err != nil {
		//	return "", err
		//}
		return fmt.Sprintf(resourcePtn, resourceID), nil
	}
	return resourceVerify(constants.InstanceIDPathParam, instanceLoadMethod)
}

// resourceVerify 校验instance资源
func resourceVerify(resourceParam string, load loadMethod) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			ctx := c.(context.CsmContext)
			resourceID := ctx.Param(resourceParam)
			if resourceID != "" {
				// 查询数据库验证resourceID和userID，将resource设置到context
				resource, err := load(ctx, resourceID)
				if err != nil {
					return err
				}
				ctx.SetResource(resource)
			} else {
				ctx.SetResource(fuzzyMatchingResource)
			}
			return next(c)
		}
	}
}

// VerifyCseResourceOwnership 校验用户id和资源所属用户id是否一致
func VerifyCseResourceOwnership(r registercenter.Interface) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			ctx := c.(context.CsmContext)
			accountId, err := iam.GetAccountId(ctx)
			if err != nil {
				return csmErr.NewUnknownError(err)
			}
			resourceID := ctx.Param("instanceId")
			if resourceID != "" {
				ownerId, err := r.GetInstanceAccountId(ctx, resourceID)
				if err != nil {
					return csmErr.NewUnknownError(err)
				}
				if ownerId != accountId {
					return csmErr.NewResourceNotFoundException(fmt.Sprintf("%s not found", resourceID))
				}
			}
			return next(c)
		}
	}
}

// CheckActivate 检查用户是否开通产品，如果没有禁止调用API
func CheckActivate(r registercenter.Interface) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			//ctx := c.(context.CsmContext)
			//if err := r.GetActivate(ctx); err != nil {
			//	return err
			//}
			return next(c)
		}
	}
}

func MetricMiddleware(apiName string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			start := time.Now()
			err := next(c)
			elapsed := time.Since(start).Seconds()

			promsdk.GetMetricHTTPDurationSeconds().WithLabelValues(apiName).Observe(elapsed)
			promsdk.GetMetricHTTPStatusCounter().WithLabelValues(apiName, strconv.Itoa(c.Response().Status)).Inc()

			return err
		}
	}
}
