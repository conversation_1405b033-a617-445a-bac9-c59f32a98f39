package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/spf13/viper"

	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmError "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const (
	localDev          = "local.dev"
	localMock         = "local.mock"
	localMockUserID   = "local.mockUserID"
	localMockDomainID = "local.mockDomainID"
)

var serviceAccountPermissions = map[string][]string{
	"order_renew": {"GetBillingWorkspaceName"},
}

// CheckIAMDefaultDenyForUser Export函数用于在业务代码中对单个网格实例的访问验证IAM权限
func CheckIAMDefaultDenyForUser(ctx context.CsmContext, service string, permissions []string) error {
	if err := checkIAMPermissionForUser(ctx, service, permissions, false); err != nil {
		return err
	}
	return nil
}

// CheckIAMDefaultDeny 函数对DEFAULT_DENY不允许验证通过，即默认失败
func CheckIAMDefaultDeny(service string, permissions []string) echo.MiddlewareFunc {
	return checkIAMPermission(service, permissions, false)
}

// CheckIAMDefaultDenyIncludeServiceAccount 函数对DEFAULT_DENY不允许验证通过，即默认失败
func CheckIAMDefaultDenyIncludeServiceAccount(service string, permissions []string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			cc := c.(context.CsmContext)
			user, err := iam.GetUser(cc)
			if err != nil {
				return csmError.NewUnauthorizedException("user is nil", err)
			}
			// user.Domain.ID为default时，代表为服务号
			if user.Domain.ID == "default" {
				if err := checkIAMPermissionForServiceAccount(user.Name, permissions); err == nil {
					return next(cc)
				}
			} else {
				if err := checkIAMPermissionForUser(cc, service, permissions, false); err == nil {
					return next(cc)
				}
			}
			return newPermissionError(user.Name, permissions...)
		}
	}
}

func checkIAMPermissionForServiceAccount(userName string, permissions []string) error {
	if servicePermissions, ok := serviceAccountPermissions[userName]; ok {
		for _, p := range permissions {
			for _, sp := range servicePermissions {
				if sp == p {
					return nil
				}
			}
		}
	}
	return newPermissionError(userName, permissions...)
}

// checkIAMPermission从IAM验证用户是否有权限执行操作指定的Resource
// service参数: 默认为bce:csm
// permissions参数: 接口要求的权限列表，用户权限满足其一即可
// allowDefaultDeny参数：如为true，那么收到验证结果后，允许DEFAULT_DENY通过验证；否则不允许。将来iam如果支持可下掉此参数
func checkIAMPermission(service string, permissions []string, allowDefaultDeny bool) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			cc := c.(context.CsmContext)
			if err := checkIAMPermissionForUser(cc, service, permissions, allowDefaultDeny); err != nil {
				return err
			}
			return next(cc)
		}
	}
}

func checkIAMPermissionForUser(cc context.CsmContext, service string, permissions []string, allowDefaultDeny bool) error {
	if viper.GetBool(localDev) && viper.GetBool(localMock) {
		cc.CsmLogger().Infof("*** local develop mode for CheckIAMDefaultDeny ***")
		return nil
	}
	authReq := iam.NewAuthRequestByHttp(cc.Request())
	verifyReq := &iam.SinglePermissionVerifyRequest{}
	region := ""
	if cc.Get(reg.ContextRegion) != nil {
		region = cc.Get(reg.ContextRegion).(string)
	}
	verifyReq.WithAuth(authReq).
		WithService(service).
		WithResource(cc.GetResource()).
		WithRegion(region).WithOwner("USE_REQUESTER_ACCOUNTID") // owner的作用是跨账号共享资源鉴权，目前对CNAP来说不需要，设为占位符
	for _, p := range permissions {
		verifyReq.AddPermission(p)
	}
	verifyReq.RequestId = cc.RequestID()

	// TODO 旧逻辑，待IAM前置机接入，测试完上线后删掉
	//iamClient, err := iam.GetClient()
	//if err != nil {
	//	return csmError.NewServiceException("", err)
	//}
	//token, verifyRes, err := iamClient.PermissionVerify("", verifyReq)
	//if err != nil {
	//	return newInvalidUserError(err)
	//}

	// IAM前置机接入
	iamClient, err := iam.GetClient()
	if err != nil {
		return csmError.NewServiceException("", err)
	}
	tokenID, err := iamClient.IamCleint.GetConsoleTokenID()
	if err != nil {
		fmt.Println("Failed to AuthAndVerifyWithToken:", err)
		return err
	}
	RequestContext := sdkIAM.RequestContext{}
	if verifyReq.Context != nil {
		RequestContext = sdkIAM.RequestContext{
			IPAddress:  verifyReq.Context.IPAddr,
			Referer:    verifyReq.Context.Refer,
			Conditions: map[string]interface{}{},
		}
	}

	verifyReqNew := sdkIAM.PermissionRequest{
		Service:        verifyReq.Service,
		Region:         verifyReq.Region,
		Resource:       verifyReq.Resource,
		ResourceOwner:  verifyReq.Owner,
		Permission:     verifyReq.Permission,
		RequestContext: RequestContext,
	}

	tokenAndVerifyResult, err := iamClient.IamCleint.AuthAndVerifyWithToken(cc.Request(), verifyReqNew, tokenID)
	if err != nil {
		return newInvalidUserError(err)
	}
	token, verifyRes := tokenAndVerifyResult.Token, tokenAndVerifyResult.VerifyResult
	switch verifyRes.Effect {
	case iam.PermissionDefaultDeny:
		if !allowDefaultDeny {
			return newPermissionError(token.User.Name, permissions...)
		}
		fallthrough
	case iam.PermissionAllow:
		user := token.User
		cc.Set(iam.ContextIAMUser, &user)
	default:
		// TODO 旧逻辑，待IAM前置机接入，测试完上线后删掉
		//if token != nil {
		//	newPermissionError(token.User.Name, permissions...)
		//}
		return newPermissionError("", permissions...)
	}
	return nil
}

// CheckIAMSignature 从IAM验证客户身份，在验证Resource的middleware执行之前，必须先执行CheckIAMSignature,将Domain ID设置在context中
func CheckIAMSignature() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			cc := c.(context.CsmContext)
			if viper.GetBool(localDev) && viper.GetBool(localMock) {
				cc.CsmLogger().Infof("*** local develop mode for CheckIAMSignature ***")
				mockUser := &sdkIAM.User{
					ID: viper.GetString(localMockUserID),
					Domain: sdkIAM.UserDomain{
						ID: viper.GetString(localMockDomainID),
					},
				}
				cc.Set(iam.ContextIAMUser, mockUser)
				return next(cc)
			}
			// TODO 旧逻辑，待IAM前置机接入，测试完上线后删掉
			//iamClient, err := iam.GetClient()
			//if err != nil {
			//	return csmError.NewServiceException("", err)
			//}
			//token, err := iamClient.AuthenticateAKSKSignature(cc)
			//if err != nil {
			//	return newInvalidUserError(err)
			//}

			// IAM前置机接入改造
			iamClient, err := iam.GetClient()
			if err != nil {
				return csmError.NewServiceException("", err)
			}
			token, err := iamClient.IamCleint.ValidatorRequest(cc.Request())
			if err != nil {
				return newInvalidUserError(err)
			}
			user := token.User
			cc.Set(iam.ContextIAMUser, &user)
			return next(cc)
		}
	}
}

// (子)用户缺少某API的权限
func newPermissionError(user string, permissions ...string) *echo.HTTPError {
	msg := fmt.Sprintf("authenticate user %s failed for permission %s", user, strings.Join(permissions, "/"))
	return echo.NewHTTPError(http.StatusForbidden, msg)
}

// (子)用户签名校验错误
func newInvalidUserError(err error) *echo.HTTPError {
	msg := fmt.Sprintf("authenticate user invalid [%s]", err.Error())
	return echo.NewHTTPError(http.StatusUnauthorized, msg)
}
