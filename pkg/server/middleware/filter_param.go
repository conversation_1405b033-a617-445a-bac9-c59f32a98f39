package middleware

import (
	"net/url"
	"reflect"
	"unsafe"

	"github.com/labstack/echo/v4"
)

// FilterEmptyQueryParam 过滤请求中的空字符串参数, 例如: www.example.com?a=&b=&c=&d=some 过滤掉a, b, c三个参数
func FilterEmptyQueryParam() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			req := c.Request()
			params := c.QueryParams()
			// build a new query string
			query := make(url.Values)
			for key := range params {
				// filter query param
				origin := params[key]
				var nonEmpty []string
				for _, v := range origin {
					if len(v) > 0 {
						nonEmpty = append(nonEmpty, v)
					}
				}
				if len(nonEmpty) > 0 {
					query[key] = nonEmpty
				}
			}
			// get demo.Context query field
			ctxVal := reflect.ValueOf(c).Elem().FieldByName("Context").Elem().Elem()
			queryField := ctxVal.FieldByName("query")
			queryField = reflect.NewAt(queryField.Type(), unsafe.Pointer(queryField.UnsafeAddr())).Elem()
			// reset query
			req.URL.RawQuery = query.Encode()
			queryField.Set(reflect.ValueOf(query))
			return next(c)
		}
	}
}
