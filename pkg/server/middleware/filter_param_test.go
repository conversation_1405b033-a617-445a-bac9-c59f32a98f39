package middleware

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/magiconair/properties/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

func TestFilterEmptyQueryParam(t *testing.T) {
	q := make(url.Values)
	q.Set("a", "AAA")
	q.Set("b", "BBB")
	q.Set("c", "")
	req := httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
	e := echo.New()
	c := e.NewContext(req, nil)
	h := FilterEmptyQueryParam()(func(c echo.Context) error { return nil })
	bc := context.NewCsmContext(c)
	h(bc)
	// QueryParam
	expect := make(url.Values)
	expect.Set("a", "AAA")
	expect.Set("b", "BBB")
	assert.Equal(t, bc.QueryParams(), expect)
}

func TestFilterEmptyQueryParam2(t *testing.T) {
	q := make(url.Values)
	q.Set("a", "AAA")
	q["b"] = []string{"bbb", "bb", ""}
	q["c"] = []string{""}
	req := httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
	e := echo.New()
	c := e.NewContext(req, nil)
	h := FilterEmptyQueryParam()(func(c echo.Context) error { return nil })
	bc := context.NewCsmContext(c)
	h(bc)
	// QueryParam
	expect := make(url.Values)
	expect["a"] = []string{"AAA"}
	expect["b"] = []string{"bbb", "bb"}
	assert.Equal(t, bc.QueryParams(), expect)
}
