package middleware

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"testing"
)

func TestRequestIDWithConfig(t *testing.T) {
	want := "request_id"
	config := RequestIDConfig{
		Skipper:   middleware.DefaultSkipper,
		Generator: func() string { return want },
	}

	ctx, _ := context.NewCsmContextMock()
	h := RequestIDWithConfig(config)(func(c echo.Context) error { return nil })
	h(ctx)
	rec := ctx.Response()
	actual := rec.Header().Get(context.HeaderXRequestID)
	assert.Equal(t, want, actual, "request-id mismatch")
}
