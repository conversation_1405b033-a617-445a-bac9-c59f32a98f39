package middleware

import (
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"testing"
)

func TestRecover(t *testing.T) {
	ctx, logs := context.NewCsmContextMock()
	h := Recover()(echo.HandlerFunc(func(c echo.Context) error {
		panic("test")
	}))
	h(ctx)
	assert.Contains(t, logs.All()[0].Message, "PANIC RECOVER", "panic recover failed")
}
