package middleware

import (
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestContext(t *testing.T) {
	e := echo.New()
	e.Use(Context())
	e.GET("/", func(c echo.Context) error {
		_, ok := c.(context.CsmContext)
		assert.True(t, ok, "expected CsmContext")
		return nil
	})
	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
}
