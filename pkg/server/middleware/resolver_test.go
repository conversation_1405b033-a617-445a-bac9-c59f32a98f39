package middleware

import (
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	modelInstancesMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	contextMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context/mock"
)

func TestResolveInstancesResource(t *testing.T) {
	ctrl := gomock.NewController(t)
	testInstanceName := "instance-1"
	tests := []struct {
		name         string
		handler      echo.HandlerFunc
		service      *modelInstancesMock.MockServiceInterface
		instanceUUID string
		userID       string
		loadInstance *meta.Instances
		loadErr      error
		wantResource string
		wantErr      bool
		expectErr    string
	}{
		{
			name: "normal verify instanceUUID",
			handler: func(c echo.Context) error {
				return nil
			},
			service:      modelInstancesMock.NewMockServiceInterface(ctrl),
			instanceUUID: "inst-1",
			userID:       "csm-coder",
			loadInstance: &meta.Instances{
				InstanceName: testInstanceName,
			},
			loadErr:      nil,
			wantResource: fmt.Sprintf(instanceResourcePtn, "inst-1"),
			wantErr:      false,
		},
		{
			name: "in case of no instanceId",
			handler: func(c echo.Context) error {
				return nil
			},
			service:      modelInstancesMock.NewMockServiceInterface(ctrl),
			userID:       "csm-coder",
			wantErr:      false,
			wantResource: fmt.Sprintf(fuzzyMatchingResource),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := ResolveInstancesResource(tt.service)(tt.handler)
			ctx := new(contextMock.CsmContext)
			ctx.On("Get", iam.ContextIAMUser).Return(&iam.User{
				Domain: &iam.Domain{
					ID: tt.userID,
				},
			}).Once()
			ctx.On("Param", "instanceUUID").Return(tt.instanceUUID)
			ctx.On("SetResource", tt.wantResource).Return().Once()
			err := f(ctx)
			if tt.wantErr != (err != nil) {
				t.Errorf("want error: %t, got error: %v", tt.wantErr, err)
			}
			if tt.wantErr {
				assert.Equal(t, tt.expectErr, err.Error())
			}
		})
	}
}
