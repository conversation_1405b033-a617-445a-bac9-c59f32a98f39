package middleware

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestAccessLog(t *testing.T) {
	ass := assert.New(t)
	ctx, logs := context.NewCsmContextMock()

	want := map[string]string{
		fieldRequestId:  "request-id",
		fieldRemoteIp:   "*******",
		fieldHost:       "mockhost",
		fieldMethod:     http.MethodGet,
		fieldUri:        "test.access.log/requestURI",
		fieldPath:       "test.access.log/urlPath",
		fieldProtocol:   "TCP",
		fieldReferer:    "test.access.log",
		fieldUserAgent:  "chrome",
		fieldBytesIn:    "1234",
		"header:custom": "customized header",
		//fieldStatus:       "status",
		//fieldError:        "error",
		//fieldLatency:      "latency",
		//fieldLatencyHuman: "latency_human",
		//fieldBytesOut:     "bytes_out",
	}

	req := httptest.NewRequest(http.MethodGet, "/", nil)
	req.Header.Set(context.HeaderXRequestID, want[fieldRequestId])
	req.Header.Set(echo.HeaderXRealIP, want[fieldRemoteIp])
	req.Host = want[fieldHost]
	req.Method = want[fieldMethod]
	req.RequestURI = want[fieldUri]
	req.URL.Path = want[fieldPath]
	req.Proto = want[fieldProtocol]
	req.Header.Set("Referer", want[fieldReferer])
	req.Header.Set("User-Agent", want[fieldUserAgent])
	req.Header.Set(echo.HeaderContentLength, want[fieldBytesIn])
	req.Header.Set("custom", want["header:custom"])

	ctx.SetRequest(req)
	h := AccessLogWithConfig(AccessLoggConfig{
		Skipper: middleware.DefaultSkipper,
		Fields: []string{
			fieldRequestId,
			fieldRemoteIp,
			fieldHost,
			fieldMethod,
			fieldUri,
			fieldPath,
			fieldProtocol,
			fieldReferer,
			fieldUserAgent,
			fieldError,
			fieldBytesIn,
			"header:custom",
		},
	})(func(c echo.Context) error {
		c.String(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
		return nil
	})
	h(ctx)

	actual := make(map[string]string)
	logEntries := logs.AllUntimed()[0].Context
	for _, entry := range logEntries {
		actual[entry.Key] = entry.String
	}
	for k, v := range want {
		ov, ok := actual[k]
		ass.Truef(ok, "expected %s", k)
		ass.Equal(v, ov, "expected %s as '%s' rather '%s'", k, v, ov)
	}
}
