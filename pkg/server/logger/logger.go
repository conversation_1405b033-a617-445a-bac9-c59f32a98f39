package logger

import (
	"fmt"
	"io"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"

	"github.com/labstack/echo/v4"
	"github.com/labstack/gommon/log"
)

type csmEchoLogger struct {
	csmLogger *csmlog.Logger
}

func NewCsmEchoLogger(csmLogger *csmlog.Logger) echo.Logger {
	l := &csmEchoLogger{
		csmLogger: csmLogger.AddCallerSkip(1),
	}

	return l
}

func (l *csmEchoLogger) Output() io.Writer {
	return l.csmLogger
}

func (l *csmEchoLogger) SetOutput(w io.Writer) {
	// csmEchoLogger doesn't need a io.Writer since we use zap
	panic("cannot set output on csmEchoLogger")
}

func (l *csmEchoLogger) Prefix() string {
	return "csm"
}

func (l *csmEchoLogger) SetPrefix(p string) {
	// csmEchoLogger doesn't need to set prefix since we use zap
	panic("cannot set prefix on csmEchoLogger")
}

func (l *csmEchoLogger) Level() log.Lvl {
	return log.OFF
}

func (l *csmEchoLogger) SetLevel(v log.Lvl) {
	return
}

func (l *csmEchoLogger) SetHeader(h string) {
	// csmEchoLogger doesn't need to set header
	panic("cannot set header on csmEchoLogger")
}

func (l *csmEchoLogger) Print(i ...interface{}) {
	l.csmLogger.Info(fmt.Sprint(i...))
}

func (l *csmEchoLogger) Printf(format string, args ...interface{}) {
	l.csmLogger.Infof(format, args)
}

func (l *csmEchoLogger) Printj(j log.JSON) {
	panic("implement me")
}

func (l *csmEchoLogger) Debug(i ...interface{}) {
	l.csmLogger.Debug(fmt.Sprint(i...))
}

func (l *csmEchoLogger) Debugf(format string, args ...interface{}) {
	l.csmLogger.Debugf(format, args)
}

func (l *csmEchoLogger) Debugj(j log.JSON) {
	panic("implement me")
}

func (l *csmEchoLogger) Info(i ...interface{}) {
	l.csmLogger.Info(fmt.Sprint(i...))
}

func (l *csmEchoLogger) Infof(format string, args ...interface{}) {
	l.csmLogger.Infof(format, args)
}

func (l *csmEchoLogger) Infoj(j log.JSON) {
	panic("implement me")
}

func (l *csmEchoLogger) Warn(i ...interface{}) {
	l.csmLogger.Warn(fmt.Sprint(i...))
}

func (l *csmEchoLogger) Warnf(format string, args ...interface{}) {
	l.csmLogger.Warnf(format, args)
}

func (l *csmEchoLogger) Warnj(j log.JSON) {
	panic("implement me")
}

func (l *csmEchoLogger) Error(i ...interface{}) {
	l.csmLogger.Error(fmt.Sprint(i...))
}

func (l *csmEchoLogger) Errorf(format string, args ...interface{}) {
	l.csmLogger.Errorf(format, args)
}

func (l *csmEchoLogger) Errorj(j log.JSON) {
	panic("implement me")
}

func (l *csmEchoLogger) Fatal(i ...interface{}) {
	l.csmLogger.Fatal(fmt.Sprint(i...))
}

func (l *csmEchoLogger) Fatalj(j log.JSON) {
	panic("implement me")
}

func (l *csmEchoLogger) Fatalf(format string, args ...interface{}) {
	l.csmLogger.Fatalf(format, args)
}

func (l *csmEchoLogger) Panic(i ...interface{}) {
	l.csmLogger.Fatal(fmt.Sprint(i...))
	panic(fmt.Sprint(i...))
}

func (l *csmEchoLogger) Panicj(j log.JSON) {
	panic("implement me")
}

func (l *csmEchoLogger) Panicf(format string, args ...interface{}) {
	l.csmLogger.Fatalf(format, args)
	panic(fmt.Sprintf(format, args...))
}
