package server

import (
	stdLog "log"
	"math/rand"
	"net/http"
	"net/http/pprof"
	"os"
	"runtime"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/logger"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

// HandlerRegister 注册HTTP处理函数
type HandlerRegister func(e *echo.Echo)

type SetupConfig struct {
	ServingInfo             *ServingInfo
	TracingReporterEndpoint string
	TracingEnabled          bool
	HandlerRegister         HandlerRegister
	PprofEnabled            bool
	MetricEnabled           bool
	AccessLog               bool
}

func NewSetupConfig(handlerRegister HandlerRegister) *SetupConfig {
	config := &SetupConfig{
		HandlerRegister: handlerRegister,
	}
	return config
}

func (c *SetupConfig) Setup() (*GenericServer, error) {
	runtime.GOMAXPROCS(runtime.NumCPU())
	// 统一随机种子随机数
	rand.Seed(time.Now().UTC().UnixNano() + int64(os.Getpid()))

	c.setupTracer()

	e := c.setupEcho()
	s := &GenericServer{
		E:               e,
		ServingInfo:     c.ServingInfo,
		ShutdownTimeout: 15 * time.Second,
	}
	return s, nil
}

func (c *SetupConfig) setupEcho() *echo.Echo {
	e := echo.New()

	e.HideBanner = true
	e.HidePort = true
	if viper.GetBool("debug") {
		e.Debug = true
	}

	e.Logger = logger.NewCsmEchoLogger(csmlog.NewLogger())
	e.StdLogger = stdLog.New(e.Logger.Output(), e.Logger.Prefix()+": ", 0)
	e.HTTPErrorHandler = func(error, echo.Context) {
		// do nothing here since we use custom context and error handler
		// see icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context
	}

	// global middlewares
	// extend default context
	e.Use(middleware.Context())
	// recover if panic
	e.Use(middleware.Recover())
	// tracing

	// alternatively use simple request id middleware
	e.Use(middleware.RequestID())

	// access log
	if c.AccessLog {
		e.Use(middleware.AccessLog())
	} else {
		e.Use(middleware.AccessLogWithConfig(middleware.AccessLoggConfig{OnlyErrLog: true}))
	}

	c.HandlerRegister(e)

	if c.PprofEnabled {
		c.setupPprof(e)
	}

	return e
}

func (c *SetupConfig) setupTracer() {

}

func (c *SetupConfig) setupPprof(e *echo.Echo) {
	pg := e.Group("/debug/pprof")

	// A sampling of all past memory allocations.
	pg.GET("/allocs", echo.WrapHandler(pprof.Handler("allocs")))

	// Stack traces that led to blocking on synchronization primitives.
	pg.GET("/block", echo.WrapHandler(pprof.Handler("block")))

	// The command line invocation of the current program, with arguments separated by NUL bytes.
	pg.GET("/cmdline", echo.WrapHandler(http.HandlerFunc(pprof.Cmdline)))

	// Stack traces of all current goroutines.
	pg.GET("/goroutine", echo.WrapHandler(pprof.Handler("goroutine")))

	// A sampling of memory allocations of live objects.
	// You can specify the gc GET parameter to run GC before taking the heap sample.
	pg.GET("/heap", echo.WrapHandler(pprof.Handler("heap")))

	// Stack traces of holders of contended mutexes.
	pg.GET("/mutex", echo.WrapHandler(pprof.Handler("mutex")))

	// CPU profile. You can specify the duration in the seconds GET parameter.
	pg.GET("/profile", echo.WrapHandler(http.HandlerFunc(pprof.Profile)))

	// Stack traces that led to the creation of new OS threads.
	pg.GET("/threadcreate", echo.WrapHandler(pprof.Handler("threadcreate")))

	// A trace of execution of the current program. You can specify the duration in the seconds GET parameter.
	pg.GET("/trace", echo.WrapHandler(http.HandlerFunc(pprof.Trace)))
}
