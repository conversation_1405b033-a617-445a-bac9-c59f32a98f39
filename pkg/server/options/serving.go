package options

import (
	"fmt"
	"net"
	"strconv"
	"time"

	"github.com/spf13/pflag"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
)

type ServingOptions struct {
	BindAddress net.IP
	// BindPort is ignored when Listener is set, will serve https even with 0.
	BindPort int
	// ReadTimeout is the maximum duration before timing out read of the request
	ReadTimeout time.Duration
	// WriteTimeout is the maximum duration before timing out write of the response
	WriteTimeout time.Duration
	// Enable pprof or not.
	PprofEnabled bool
	// Expose metrics or not.
	MetricEnabled bool
	// Enable access log.
	AccessLog bool

	// Listener is the secure server network listener.
	// either Listener or BindAddress/BindPort/BindNetwork is set,
	// if Listener is set, use it and omit BindAddress/BindPort/BindNetwork.
	Listener net.Listener
}

const (
	configListen       = "server.listen"
	configPort         = "server.port"
	configReadTimeout  = "server.read_timeout"
	configWriteTimeout = "server.write_timeout"
	configPprofEnable  = "server.pprof_enabled"
	configMetricEnable = "server.metric_enabled"
	configAccessLog    = "server.access_log"
)

func InitServingOptions() {
	viper.SetDefault(configListen, "0.0.0.0")
	viper.SetDefault(configPort, 8080)
	viper.SetDefault(configReadTimeout, 10*time.Second)
	viper.SetDefault(configWriteTimeout, 10*time.Second)
	viper.SetDefault(configPprofEnable, false)
	viper.SetDefault(configMetricEnable, false)
	pflag.Int(configPort, 8080, "the port server listening")
}

func NewServingOptions() *ServingOptions {
	s := &ServingOptions{
		BindAddress:   net.ParseIP(viper.GetString(configListen)),
		BindPort:      viper.GetInt(configPort),
		ReadTimeout:   viper.GetDuration(configReadTimeout),
		WriteTimeout:  viper.GetDuration(configWriteTimeout),
		PprofEnabled:  viper.GetBool(configPprofEnable),
		MetricEnabled: viper.GetBool(configMetricEnable),
		AccessLog:     viper.GetBool(configAccessLog),
	}

	return s
}

func (s *ServingOptions) Validate() []error {
	if s == nil {
		return nil
	}

	var errors []error

	if s.BindAddress == nil {
		errors = append(errors, fmt.Errorf("listen address %v invalid", s.BindAddress))
	}

	if s.BindPort < 0 || s.BindPort > 65535 {
		errors = append(errors, fmt.Errorf("port %v must be between 0 and 65535, inclusive. 0 for turning off secure port", s.BindPort))
	}

	if s.ReadTimeout == 0 || s.WriteTimeout == 0 {
		errors = append(errors, fmt.Errorf("read_timeout %v and write_timeout %v must be larger than zero", s.ReadTimeout, s.WriteTimeout))
	}

	return errors
}

// ApplyTo fills up serving information in the server configuration.
func (s *ServingOptions) ApplyTo(config **server.ServingInfo) error {
	if s == nil {
		return nil
	}
	if s.BindPort <= 0 && s.Listener == nil {
		return nil
	}

	if s.Listener == nil {
		var err error
		addr := net.JoinHostPort(s.BindAddress.String(), strconv.Itoa(s.BindPort))
		s.Listener, s.BindPort, err = CreateListener("tcp", addr)
		if err != nil {
			return fmt.Errorf("failed to create listener: %v", err)
		}
	} else {
		if _, ok := s.Listener.Addr().(*net.TCPAddr); !ok {
			return fmt.Errorf("failed to parse ip and port from listener")
		}
		s.BindPort = s.Listener.Addr().(*net.TCPAddr).Port
		s.BindAddress = s.Listener.Addr().(*net.TCPAddr).IP
	}

	*config = &server.ServingInfo{
		Listener:     s.Listener,
		ReadTimeout:  s.ReadTimeout,
		WriteTimeout: s.WriteTimeout,
	}

	return nil
}

func CreateListener(network, addr string) (net.Listener, int, error) {
	if len(network) == 0 {
		network = "tcp"
	}
	ln, err := net.Listen(network, addr)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to listen on %v: %v", addr, err)
	}

	// get port
	tcpAddr, ok := ln.Addr().(*net.TCPAddr)
	if !ok {
		ln.Close()
		return nil, 0, fmt.Errorf("invalid listen address: %q", ln.Addr().String())
	}

	return ln, tcpAddr.Port, nil
}
