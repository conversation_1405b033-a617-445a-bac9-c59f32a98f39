package options

import (
	"fmt"
	"net/url"

	"github.com/spf13/viper"
)

type TracingOptions struct {
	ReporterEndpoint string
	Enabled          bool
}

const reporterEndpoint = "tracing.reporter_endpoint"
const enable = "tracing.enable"

func NewTracingOptions() *TracingOptions {
	o := TracingOptions{
		ReporterEndpoint: viper.GetString(reporterEndpoint),
		Enabled:          viper.GetBool(enable),
	}
	return &o
}

func (s *TracingOptions) Validate() []error {
	if s == nil {
		return nil
	}

	var errors []error

	_, err := url.Parse(s.ReporterEndpoint)
	if err != nil {
		errors = append(errors, fmt.Errorf("tracing reporter endpoing %v invalid", s.ReporterEndpoint))
	}

	return errors
}
