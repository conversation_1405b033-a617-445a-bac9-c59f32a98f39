package options

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
)

type ServerOptions struct {
	ServingOptions *ServingOptions
	TracingOptions *TracingOptions
}

func InitServerConfig() {
	InitServingOptions()
}

func NewServerOptions() *ServerOptions {
	c := &ServerOptions{
		ServingOptions: NewServingOptions(),
		TracingOptions: NewTracingOptions(),
	}
	return c
}

func (s *ServerOptions) Validate() []error {
	var errors []error
	errors = append(errors, s.ServingOptions.Validate()...)
	errors = append(errors, s.TracingOptions.Validate()...)
	return errors
}

func (s *ServerOptions) ApplyTo(c *server.SetupConfig) error {
	if err := s.ServingOptions.ApplyTo(&c.ServingInfo); err != nil {
		return err
	}

	// todo tracing

	c.PprofEnabled = s.ServingOptions.PprofEnabled
	c.MetricEnabled = s.ServingOptions.MetricEnabled
	c.AccessLog = s.ServingOptions.AccessLog
	return nil
}
