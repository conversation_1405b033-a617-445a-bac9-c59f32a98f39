/*
Copyright 2021.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// GaiaDBSpec defines the desired state of GaiaDB
type GaiaDBSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	InstanceParam InstanceParam `json:"instanceParam"`
	Database      string        `json:"database,omitempty"`
	Username      string        `json:"username"`
	Password      string        `json:"password"`
}

type EngineVersion string

const (
	EngineVersion57 EngineVersion = "5.7"
	EngineVersion80 EngineVersion = "8.0"
)

type InstanceParam struct {
	EngineVersion EngineVersion `json:"engineVersion"`

	// AllocatedCpuInCore 取值参考 https://cloud.baidu.com/doc/GaiaDB/s/nl89lanve#%E9%9B%86%E7%BE%A4%E5%8F%AF%E9%80%89%E5%A5%97%E9%A4%90
	AllocatedCpuInCore  int    `json:"allocatedCpuInCore"`
	AllocatedMemoryInMB int    `json:"allocatedMemoryInMB"`
	VpcID               string `json:"vpcID"`
	SubnetID            string `json:"subnetID"`
}

const (
	GaiaDBStatusPhasePending = "pending"
	GaiaDBStatusCreating     = "creating"
	GaiaDBStatusFailed       = "failed"
	GaiaDBStatusRunning      = "running"
	GaiaDBStatusUpdating     = "updating"
)

// +kubebuilder:object:root=true
// GaiaDBStatus defines the observed state of GaiaDB
type GaiaDBStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file
	Phase              string        `json:"phase,omitempty"`
	ClusterID          string        `json:"clusterID,omitempty"`
	ClusterDetail      ClusterDetail `json:"clusterDetail"`
	AccountDetail      AccountDetail `json:"accountDetail"`
	Database           string        `json:"database"`
	Reason             string        `json:"reason,omitempty"`
	Message            string        `json:"message,omitempty"`
	LastTransitionTime *metav1.Time  `json:"lastTransitionTime,omitempty"`
	LastProbeTime      *metav1.Time  `json:"lastProbeTime,omitempty"`
}

type ClusterDetail struct {
	AllocatedCpuInCore  int    `json:"allocatedCpuInCore"`
	AllocatedMemoryInMB int    `json:"allocatedMemoryInMB"`
	Host                string `json:"host,omitempty"`
	Port                int    `json:"port,omitempty"`
}

type AccountDetail struct {
	Username string `json:"username,omitempty"`
	Database string `json:"database,omitempty"` // 账号具备权限的 database
}

// GetObjectKind -
// TODO: 该方法无实际用处, 为了使用 controller-gen 自动生成 DeepCopy 函数
// controller-gen object 需要实现 runtime.Object interface
func (*GaiaDBStatus) GetObjectKind() schema.ObjectKind {
	return schema.EmptyObjectKind
}

//+kubebuilder:object:root=true
//+kubebuilder:printcolumn:name="PHASE",type=string,JSONPath=`.status.phase`
//+kubebuilder:printcolumn:name="DBID",type=string,JSONPath=`.status.clusterID`
//+kubebuilder:printcolumn:name="DATABASE",type=string,JSONPath=`.spec.database`

// +kubebuilder:subresource:status
// GaiaDB is the Schema for the gaiadb API
type GaiaDB struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   GaiaDBSpec   `json:"spec,omitempty"`
	Status GaiaDBStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// GaiaDBList contains a list of GaiaDB
type GaiaDBList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []GaiaDB `json:"items"`
}

// init init 初始化函数，用于注册 GaiaDB 和 GaiaDBList 两个类型到 SchemeBuilder 中
// 参数：无
// 返回值：无
func init() {
	SchemeBuilder.Register(&GaiaDB{}, &GaiaDBList{})
}
