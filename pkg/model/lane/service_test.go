package lane

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	daoMocks "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao/mocks"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	mockDB, _  = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx, _ = ctxCsm.NewCsmContextMock()

	InstanceId = "test-123456"
	AccountId  = "1"
	groupID    = "3"
	groupName  = "test-group"
	laneID     = "4"
	laneName   = "test-lane"
)

func TestNewLaneGroup(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name    string
		success bool
	}{
		{
			name:    "success create laneGroupDao",
			success: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			service := &Service{
				opt:          NewOption(mockDB),
				laneGroupDao: mockDaoModel,
			}

			mockDaoModel.EXPECT().Save(mockCtx, gomock.Any()).Return(nil)
			err := service.NewLaneGroup(mockCtx, buildLaneGroup())
			if tt.success {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
			}
		})
	}
}

func TestNewLane(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name    string
		success bool
	}{
		{
			name:    "success create laneDao",
			success: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			service := &Service{
				opt:     NewOption(mockDB),
				laneDao: mockDaoModel,
			}

			mockDaoModel.EXPECT().Save(mockCtx, gomock.Any()).Return(nil)
			err := service.NewLane(mockCtx, buildLane())
			if tt.success {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
			}
		})
	}
}

func buildLaneGroup() *meta.LaneGroups {
	return &meta.LaneGroups{
		AccountId:    AccountId,
		GroupID:      groupID,
		InstanceUUID: InstanceId,
		GroupName:    groupName,
	}
}

func buildLane() *meta.Lanes {
	return &meta.Lanes{
		AccountId:    AccountId,
		GroupID:      groupID,
		InstanceUUID: InstanceId,
		LaneID:       laneID,
		LaneName:     laneName,
	}
}

func TestGetLaneGroupsByInstanceUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name      string
		laneGroup []meta.LaneGroups
		success   bool
	}{
		{
			name:      "success get laneGroups",
			laneGroup: []meta.LaneGroups{*buildLaneGroup()},
			success:   true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			service := &Service{
				opt:          NewOption(mockDB),
				laneGroupDao: mockDaoModel,
			}

			mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.laneGroup, nil)
			_, err := service.GetLaneGroupsByInstanceUUID(mockCtx, InstanceId, "accountID")
			if tt.success {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
			}
		})
	}
}

func TestGetLaneGroupByID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name      string
		laneGroup []meta.LaneGroups
		success   bool
	}{
		{
			name:      "success get laneGroups",
			laneGroup: []meta.LaneGroups{*buildLaneGroup()},
			success:   true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			service := &Service{
				opt:          NewOption(mockDB),
				laneGroupDao: mockDaoModel,
			}

			mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.laneGroup, nil)
			_, err := service.GetLaneGroupByID(mockCtx, InstanceId, groupID, "accountID")
			if tt.success {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
			}
		})
	}
}

func TestDeleteLaneGroup(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name      string
		laneGroup *meta.LaneGroups
		success   bool
	}{
		{
			name:      "success get laneGroups",
			laneGroup: buildLaneGroup(),
			success:   true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			service := &Service{
				opt:          NewOption(mockDB),
				laneGroupDao: mockDaoModel,
				laneDao:      mockDaoModel,
			}

			mockDaoModel.EXPECT().Delete(mockCtx, gomock.Any()).Return(nil).AnyTimes()
			mockDaoModel.EXPECT().BatchDelete(mockCtx, gomock.Any()).Return(nil).AnyTimes()
			err := service.DeleteLaneGroup(mockCtx, tt.laneGroup)
			if tt.success {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
			}
		})
	}
}

func TestDeleteAllLaneGroupByInstanceUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name      string
		laneGroup *meta.LaneGroups
		success   bool
	}{
		{
			name:      "success DeleteAllLaneGroupByInstanceUUID",
			laneGroup: buildLaneGroup(),
			success:   true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			service := &Service{
				opt:          NewOption(mockDB),
				laneGroupDao: mockDaoModel,
				laneDao:      mockDaoModel,
			}

			mockDaoModel.EXPECT().BatchDelete(mockCtx, gomock.Any()).Return(nil).AnyTimes()
			err := service.DeleteAllLaneGroupByInstanceUUID(mockCtx, InstanceId, AccountId)
			if tt.success {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
			}
		})
	}
}

func TestGetLanesByGroupID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name    string
		lanes   []meta.Lanes
		success bool
	}{
		{
			name:    "success get laneGroups",
			lanes:   []meta.Lanes{*buildLane()},
			success: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			service := &Service{
				opt:     NewOption(mockDB),
				laneDao: mockDaoModel,
			}

			mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.lanes, nil).AnyTimes()
			_, err := service.GetLanesByGroupID(mockCtx, InstanceId, groupID, AccountId)
			if tt.success {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
			}
		})
	}
}

func TestGetLaneByID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name    string
		lanes   *meta.Lanes
		success bool
	}{
		{
			name:    "success get laneGroups",
			lanes:   buildLane(),
			success: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			service := &Service{
				opt:     NewOption(mockDB),
				laneDao: mockDaoModel,
			}

			mockDaoModel.EXPECT().LoadWithWhere(mockCtx, gomock.Any()).Return(tt.lanes, nil).AnyTimes()
			_, err := service.GetLaneByID(mockCtx, InstanceId, laneID, AccountId)
			if tt.success {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
			}
		})
	}
}

func TestModifyLane(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name    string
		update  *meta.Lanes
		where   *meta.Lanes
		success bool
	}{
		{
			name:    "success modify laneGroups",
			where:   buildLane(),
			success: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			service := &Service{
				opt:     NewOption(mockDB),
				laneDao: mockDaoModel,
			}

			mockDaoModel.EXPECT().Update(mockCtx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			err := service.ModifyLane(mockCtx, tt.where, tt.update)
			if tt.success {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
			}
		})
	}
}
