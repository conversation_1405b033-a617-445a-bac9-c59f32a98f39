// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	lane "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/lane"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	dbutil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// DeleteAllLaneGroupByInstanceUUID mocks base method.
func (m *MockServiceInterface) DeleteAllLaneGroupByInstanceUUID(ctx context.CsmContext, instanceUUID, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAllLaneGroupByInstanceUUID", ctx, instanceUUID, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAllLaneGroupByInstanceUUID indicates an expected call of DeleteAllLaneGroupByInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) DeleteAllLaneGroupByInstanceUUID(ctx, instanceUUID, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllLaneGroupByInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).DeleteAllLaneGroupByInstanceUUID), ctx, instanceUUID, accountID)
}

// DeleteLane mocks base method.
func (m *MockServiceInterface) DeleteLane(ctx context.CsmContext, lane *meta.Lanes) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLane", ctx, lane)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLane indicates an expected call of DeleteLane.
func (mr *MockServiceInterfaceMockRecorder) DeleteLane(ctx, lane interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLane", reflect.TypeOf((*MockServiceInterface)(nil).DeleteLane), ctx, lane)
}

// DeleteLaneGroup mocks base method.
func (m *MockServiceInterface) DeleteLaneGroup(ctx context.CsmContext, laneGroup *meta.LaneGroups) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLaneGroup", ctx, laneGroup)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteLaneGroup indicates an expected call of DeleteLaneGroup.
func (mr *MockServiceInterfaceMockRecorder) DeleteLaneGroup(ctx, laneGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLaneGroup", reflect.TypeOf((*MockServiceInterface)(nil).DeleteLaneGroup), ctx, laneGroup)
}

// GenerateLaneGroupID mocks base method.
func (m *MockServiceInterface) GenerateLaneGroupID() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateLaneGroupID")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateLaneGroupID indicates an expected call of GenerateLaneGroupID.
func (mr *MockServiceInterfaceMockRecorder) GenerateLaneGroupID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateLaneGroupID", reflect.TypeOf((*MockServiceInterface)(nil).GenerateLaneGroupID))
}

// GenerateLaneID mocks base method.
func (m *MockServiceInterface) GenerateLaneID() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateLaneID")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateLaneID indicates an expected call of GenerateLaneID.
func (mr *MockServiceInterfaceMockRecorder) GenerateLaneID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateLaneID", reflect.TypeOf((*MockServiceInterface)(nil).GenerateLaneID))
}

// GetLaneByID mocks base method.
func (m *MockServiceInterface) GetLaneByID(ctx context.CsmContext, laneID, instanceUUID, accountID string) (*meta.Lanes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLaneByID", ctx, laneID, instanceUUID, accountID)
	ret0, _ := ret[0].(*meta.Lanes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLaneByID indicates an expected call of GetLaneByID.
func (mr *MockServiceInterfaceMockRecorder) GetLaneByID(ctx, laneID, instanceUUID, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLaneByID", reflect.TypeOf((*MockServiceInterface)(nil).GetLaneByID), ctx, laneID, instanceUUID, accountID)
}

// GetLaneGroupByID mocks base method.
func (m *MockServiceInterface) GetLaneGroupByID(ctx context.CsmContext, instanceUUID, groupID, accountID string) (*meta.LaneGroups, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLaneGroupByID", ctx, instanceUUID, groupID, accountID)
	ret0, _ := ret[0].(*meta.LaneGroups)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLaneGroupByID indicates an expected call of GetLaneGroupByID.
func (mr *MockServiceInterfaceMockRecorder) GetLaneGroupByID(ctx, instanceUUID, groupID, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLaneGroupByID", reflect.TypeOf((*MockServiceInterface)(nil).GetLaneGroupByID), ctx, instanceUUID, groupID, accountID)
}

// GetLaneGroupsByInstanceUUID mocks base method.
func (m *MockServiceInterface) GetLaneGroupsByInstanceUUID(ctx context.CsmContext, instanceUUID, accountID string) ([]meta.LaneGroups, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLaneGroupsByInstanceUUID", ctx, instanceUUID, accountID)
	ret0, _ := ret[0].([]meta.LaneGroups)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLaneGroupsByInstanceUUID indicates an expected call of GetLaneGroupsByInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) GetLaneGroupsByInstanceUUID(ctx, instanceUUID, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLaneGroupsByInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).GetLaneGroupsByInstanceUUID), ctx, instanceUUID, accountID)
}

// GetLanes mocks base method.
func (m *MockServiceInterface) GetLanes(ctx context.CsmContext, search, where *meta.Lanes) ([]meta.Lanes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLanes", ctx, search, where)
	ret0, _ := ret[0].([]meta.Lanes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLanes indicates an expected call of GetLanes.
func (mr *MockServiceInterfaceMockRecorder) GetLanes(ctx, search, where interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLanes", reflect.TypeOf((*MockServiceInterface)(nil).GetLanes), ctx, search, where)
}

// GetLanesByGroupID mocks base method.
func (m *MockServiceInterface) GetLanesByGroupID(ctx context.CsmContext, instanceUUID, groupID, accountID string) ([]meta.Lanes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLanesByGroupID", ctx, instanceUUID, groupID, accountID)
	ret0, _ := ret[0].([]meta.Lanes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLanesByGroupID indicates an expected call of GetLanesByGroupID.
func (mr *MockServiceInterfaceMockRecorder) GetLanesByGroupID(ctx, instanceUUID, groupID, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLanesByGroupID", reflect.TypeOf((*MockServiceInterface)(nil).GetLanesByGroupID), ctx, instanceUUID, groupID, accountID)
}

// ModifyLane mocks base method.
func (m *MockServiceInterface) ModifyLane(ctx context.CsmContext, where, update *meta.Lanes) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyLane", ctx, where, update)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyLane indicates an expected call of ModifyLane.
func (mr *MockServiceInterfaceMockRecorder) ModifyLane(ctx, where, update interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyLane", reflect.TypeOf((*MockServiceInterface)(nil).ModifyLane), ctx, where, update)
}

// NewLane mocks base method.
func (m *MockServiceInterface) NewLane(ctx context.CsmContext, lane *meta.Lanes) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewLane", ctx, lane)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewLane indicates an expected call of NewLane.
func (mr *MockServiceInterfaceMockRecorder) NewLane(ctx, lane interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewLane", reflect.TypeOf((*MockServiceInterface)(nil).NewLane), ctx, lane)
}

// NewLaneGroup mocks base method.
func (m *MockServiceInterface) NewLaneGroup(ctx context.CsmContext, laneGroup *meta.LaneGroups) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewLaneGroup", ctx, laneGroup)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewLaneGroup indicates an expected call of NewLaneGroup.
func (mr *MockServiceInterfaceMockRecorder) NewLaneGroup(ctx, laneGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewLaneGroup", reflect.TypeOf((*MockServiceInterface)(nil).NewLaneGroup), ctx, laneGroup)
}

// WithTx mocks base method.
func (m *MockServiceInterface) WithTx(tx *dbutil.DB) lane.ServiceInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(lane.ServiceInterface)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockServiceInterfaceMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockServiceInterface)(nil).WithTx), tx)
}
