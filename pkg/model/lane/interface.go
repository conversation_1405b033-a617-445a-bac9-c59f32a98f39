package lane

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceInterface interface {
	// laneGroup
	WithTx(tx *dbutil.DB) ServiceInterface
	GenerateLaneGroupID() (string, error)
	NewLaneGroup(ctx context.CsmContext, laneGroup *meta.LaneGroups) error
	GetLaneGroupsByInstanceUUID(ctx context.CsmContext, instanceUUID, accountID string) ([]meta.LaneGroups, error)
	GetLaneGroupByID(ctx context.CsmContext, instanceUUID, groupID, accountID string) (*meta.LaneGroups, error)
	DeleteLaneGroup(ctx context.CsmContext, laneGroup *meta.LaneGroups) error
	DeleteAllLaneGroupByInstanceUUID(ctx context.CsmContext, instanceUUID, accountID string) error

	// lane
	GenerateLaneID() (string, error)
	NewLane(ctx context.CsmContext, lane *meta.Lanes) error
	GetLanesByGroupID(ctx context.CsmContext, instanceUUID, groupID, accountID string) ([]meta.Lanes, error)
	GetLanes(ctx context.CsmContext, search, where *meta.Lanes) ([]meta.Lanes, error)
	GetLaneByID(ctx context.CsmContext, laneID, instanceUUID, accountID string) (*meta.Lanes, error)
	ModifyLane(ctx context.CsmContext, where, update *meta.Lanes) (err error)
	DeleteLane(ctx context.CsmContext, lane *meta.Lanes) error
}
