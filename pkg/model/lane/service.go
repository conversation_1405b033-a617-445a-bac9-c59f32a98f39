package lane

import (
	"fmt"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/lane/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/uuid"
)

type Service struct {
	opt          *Option
	laneGroupDao model.LaneGroupDaoInterface
	laneDao      model.LaneDaoInterface
	cceService   cce.ClientInterface
}

func NewLaneService(option *Option) *Service {
	return &Service{
		opt:          option,
		laneGroupDao: dao.NewLaneGroupsDao(option.DB),
		laneDao:      dao.NewLanesDao(option.DB),
		cceService:   cce.NewClientService(),
	}
}

// WithTx 服务网格实例 tx
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	nOpt := *s.opt
	nOpt.DB = tx
	return NewLaneService(&nOpt)
}

// NewLaneGroup 创建泳道组实例
func (s *Service) NewLaneGroup(ctx context.CsmContext, laneGroup *meta.LaneGroups) error {
	if err := s.laneGroupDao.Save(ctx, laneGroup); err != nil {
		return err
	}
	return nil
}

// NewLane 创建泳道实例
func (s *Service) NewLane(ctx context.CsmContext, lane *meta.Lanes) error {
	if err := s.laneDao.Save(ctx, lane); err != nil {
		return err
	}
	return nil
}

func (s *Service) GenerateLaneID() (string, error) {
	var result string
	for i := 0; i < 8; i++ {
		result = meta.LaneIDPrefix + "-" + uuid.GetUUID()
		notFound := s.opt.DB.Where("lane_id = ?", result).Find(&meta.Lanes{}).RecordNotFound()
		if notFound {
			return result, nil
		}
	}
	return "", csmErr.NewResourceConflictException("duplicate lane id in database")
}

func (s *Service) GenerateLaneGroupID() (string, error) {
	var result string
	for i := 0; i < 8; i++ {
		result = meta.LaneGroupIDPrefix + "-" + uuid.GetUUID()
		notFound := s.opt.DB.Where("group_id = ?", result).Find(&meta.LaneGroups{}).RecordNotFound()
		if notFound {
			return result, nil
		}
	}
	return "", csmErr.NewResourceConflictException("duplicate laneGroup id in database")
}

func (s *Service) GetLaneGroupsByInstanceUUID(ctx context.CsmContext, instanceUUID, accountID string) ([]meta.LaneGroups, error) {
	if instanceUUID == "" || accountID == "" {
		return nil, csmErr.NewInvalidParameterInputValueException("instanceUUID and accountID are required")
	}
	where := meta.LaneGroups{
		AccountId:    accountID,
		InstanceUUID: instanceUUID,
	}

	lgs, err := s.laneGroupDao.ListAll(ctx, &meta.LaneGroups{}, where, &meta.LaneGroups{})
	if err != nil {
		return nil, err
	}

	result := lgs.(*[]meta.LaneGroups)
	return *result, nil
}

func (s *Service) GetLaneGroupByID(ctx context.CsmContext, instanceUUID, groupID, accountID string) (*meta.LaneGroups, error) {
	if groupID == "" || instanceUUID == "" || accountID == "" {
		return nil, csmErr.NewInvalidParameterInputValueException("instanceUUID, groupID and accountID are required")
	}

	where := meta.LaneGroups{
		AccountId:    accountID,
		InstanceUUID: instanceUUID,
		GroupID:      groupID,
	}

	lgs, err := s.laneGroupDao.ListAll(ctx, &meta.LaneGroups{}, where, &meta.LaneGroups{})
	if err != nil {
		return nil, err
	}

	laneGroupList := lgs.(*[]meta.LaneGroups)
	if len(*laneGroupList) != 1 {
		return nil, csmErr.NewResourceConflictException(fmt.Sprintf("conflict laneGroupID is %s", groupID))
	}
	result := (*laneGroupList)[0]
	return &result, nil
}

// DeleteLaneGroup 删除泳道组及其所属泳道
func (s *Service) DeleteLaneGroup(ctx context.CsmContext, laneGroup *meta.LaneGroups) error {
	where := meta.Lanes{
		AccountId:    laneGroup.AccountId,
		InstanceUUID: laneGroup.InstanceUUID,
		GroupID:      laneGroup.GroupID,
	}

	// 批量删除泳道
	err := s.laneDao.BatchDelete(ctx, where)
	if err != nil {
		return err
	}

	return s.laneGroupDao.Delete(ctx, laneGroup)
}

// DeleteAllLaneGroupByInstanceUUID 删除网格实例下所有的泳道组及其所属泳道
func (s *Service) DeleteAllLaneGroupByInstanceUUID(ctx context.CsmContext, instanceUUID, accountID string) error {
	whereLane := meta.Lanes{
		AccountId:    accountID,
		InstanceUUID: instanceUUID,
	}

	// 批量删除泳道
	err := s.laneDao.BatchDelete(ctx, whereLane)
	if err != nil {
		return err
	}

	// 批量删除泳道组
	where := meta.LaneGroups{
		AccountId:    accountID,
		InstanceUUID: instanceUUID,
	}
	return s.laneGroupDao.BatchDelete(ctx, where)
}

func (s *Service) GetLanesByGroupID(ctx context.CsmContext, instanceUUID, groupID, accountID string) ([]meta.Lanes, error) {
	if groupID == "" || instanceUUID == "" || accountID == "" {
		return nil, csmErr.NewInvalidParameterInputValueException("instanceUUID, groupID and accountID are required")
	}

	where := meta.Lanes{
		AccountId:    accountID,
		InstanceUUID: instanceUUID,
		GroupID:      groupID,
		Deleted:      csm.Int(0),
	}

	return s.GetLanes(ctx, &meta.Lanes{}, &where)
}

func (s *Service) GetLaneByID(ctx context.CsmContext, laneID, instanceUUID, accountID string) (*meta.Lanes, error) {
	where := &meta.Lanes{
		AccountId:    accountID,
		InstanceUUID: instanceUUID,
		LaneID:       laneID,
		Deleted:      csm.Int(0),
	}

	lm, err := s.laneDao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}
	result := lm.(*meta.Lanes)
	return result, nil
}

func (s *Service) GetLanes(ctx context.CsmContext, search, where *meta.Lanes) ([]meta.Lanes, error) {
	ls, err := s.laneDao.ListAll(ctx, search, where, &meta.Lanes{})
	if err != nil {
		return nil, err
	}
	if ls == nil {
		ctx.CsmLogger().Info("getLanes is empty")
		return nil, nil
	}

	result := ls.(*[]meta.Lanes)
	return *result, nil
}

func (s *Service) ModifyLane(ctx context.CsmContext, where, update *meta.Lanes) (err error) {
	return s.laneDao.Update(ctx, where, update)
}

func (s *Service) DeleteLane(ctx context.CsmContext, lane *meta.Lanes) error {
	return s.laneDao.Delete(ctx, lane)
}
