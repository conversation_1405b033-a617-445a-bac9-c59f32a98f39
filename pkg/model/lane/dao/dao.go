package dao

import (
	"reflect"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type LaneGroupsDao struct {
	*dao.Dao
}

func NewLaneGroupsDao(db *dbutil.DB) *LaneGroupsDao {
	t := reflect.TypeOf(meta.LaneGroups{})
	return &LaneGroupsDao{
		Dao: dao.NewDao(t, db),
	}
}

type LanesDao struct {
	*dao.Dao
}

func NewLanesDao(db *dbutil.DB) *LanesDao {
	t := reflect.TypeOf(meta.Lanes{})
	return &LanesDao{
		Dao: dao.NewDao(t, db),
	}
}
