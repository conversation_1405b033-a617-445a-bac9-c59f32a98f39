package blsv3

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	bls_sdk "github.com/baidubce/bce-sdk-go/services/bls/api"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3"
	blsv3Mocks "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	daoMocks "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao/mocks"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blsv3/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

var (
	mockDB, _  = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx, _ = ctxCsm.NewCsmContextMock()

	fakeBceClient       *bce_sdk.BceClient
	InstanceId          = "test-123456"
	fakeClient          *bce_sdk.BceClient
	ClusterId           = "cce-123456"
	ClusterName         = "test-test"
	ClusterTypeRemote   = "remote"
	ClusterTypeExternal = "external"
	Region              = "bj"
	AccountId           = "1"
)

func TestNewBlsService(t *testing.T) {
	option := &Option{DB: dbutil.NewDB(mockDB)}
	type testTask struct {
		name string
		args *Option
		want *Service
	}
	task := testTask{
		name: "test-1",
		args: option,
		want: &Service{
			opt:          option,
			dao:          dao.NewBlsDao(option.DB),
			blsv3Service: &blsv3.Client{},
		},
	}
	t.Run(task.name, func(t *testing.T) {
		got := NewBlsService(task.args)
		assert.Equal(t, got, task.want)
	})
}

// TestNewBlsTask 测试函数，用于测试NewBlsTask函数
func TestNewBlsTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 定义一个测试任务结构体
	type testTask struct {
		name string
		args *meta.Bls
		want error
	}

	// 创建测试任务列表
	tasks := []testTask{
		{
			name: "test-1",
			args: &meta.Bls{
				InstanceUUID: InstanceId,
				ClusterUUID:  ClusterId,
			},
			want: nil,
		},
	}

	// 遍历测试任务列表
	for _, task := range tasks {
		// 创建一个模拟的Dao模型
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)

		// 创建一个Service实例，并设置其opt和dao属性
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}

		// 设置Dao模型的Save方法预期行为，并返回nil
		mockDaoModel.EXPECT().Save(mockCtx, gomock.Any()).
			Return(nil)

		// 运行测试任务
		t.Run(task.name, func(t *testing.T) {
			// 调用Service的NewBlsTask方法，并传入mockCtx和测试任务的参数
			err := service.NewBlsTask(mockCtx, task.args)
			// 断言返回的错误为nil
			assert.Nil(t, err)
		})
	}
}

// TestGetAllBlsTasksByClusterUUID 测试函数，用于测试GetAllBlsTasksByClusterUUID方法
func TestGetAllBlsTasksByClusterUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	type result struct {
		res *[]meta.Bls
		err error
	}
	type args struct {
		ctx         context.CsmContext
		clusterUUID string
	}
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				ctx:         mockCtx,
				clusterUUID: ClusterId,
			},
			mockResult: &result{
				res: &[]meta.Bls{
					{
						InstanceUUID: InstanceId,
						ClusterUUID:  ClusterId,
					},
				},
				err: nil,
			},
			want: &result{
				res: &[]meta.Bls{
					{
						InstanceUUID: InstanceId,
						ClusterUUID:  ClusterId,
					},
				},
				err: nil,
			},
		},
		{
			name: "test-2",
			args: &args{
				ctx:         mockCtx,
				clusterUUID: ClusterId,
			},
			mockResult: &result{
				res: &[]meta.Bls{
					{
						InstanceUUID: InstanceId,
						ClusterUUID:  ClusterId,
					},
				},
				err: nil,
			},
			want: &result{
				res: &[]meta.Bls{
					{
						InstanceUUID: InstanceId,
						ClusterUUID:  ClusterId,
					},
				},
				err: nil,
			},
		},
	}
	for _, task := range tasks {
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}
		mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).
			Return(task.mockResult.res, task.mockResult.err)
		t.Run(task.name, func(t *testing.T) {
			res, err := service.GetAllBlsTasksByClusterUUID(task.args.ctx, task.args.clusterUUID)
			assert.Equal(t, res, task.want.res)
			assert.Equal(t, err, task.want.err)
		})

	}
}

// TestGetAllBlsTasksByInstanceUUID 测试函数，用于测试Service结构体的GetAllBlsTasksByInstanceUUID方法
func TestGetAllBlsTasksByInstanceUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	type result struct {
		res *[]meta.Bls
		err error
	}
	type args struct {
		ctx          context.CsmContext
		clusterUUID  string
		InstanceUUID string
	}
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				ctx:          mockCtx,
				clusterUUID:  ClusterId,
				InstanceUUID: InstanceId,
			},
			mockResult: &result{
				res: &[]meta.Bls{
					{
						InstanceUUID: InstanceId,
						ClusterUUID:  ClusterId,
					},
				},
				err: nil,
			},
			want: &result{
				res: &[]meta.Bls{
					{
						InstanceUUID: InstanceId,
						ClusterUUID:  ClusterId,
					},
				},
				err: nil,
			},
		},
		{
			name: "test-2",
			args: &args{
				ctx:          mockCtx,
				clusterUUID:  ClusterId,
				InstanceUUID: InstanceId,
			},
			mockResult: &result{
				res: &[]meta.Bls{
					{
						InstanceUUID: InstanceId,
						ClusterUUID:  ClusterId,
					},
				},
				err: nil,
			},
			want: &result{
				res: &[]meta.Bls{
					{
						InstanceUUID: InstanceId,
						ClusterUUID:  ClusterId,
					},
				},
				err: nil,
			},
		},
	}
	for _, task := range tasks {
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}
		mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).
			Return(task.mockResult.res, task.mockResult.err)
		t.Run(task.name, func(t *testing.T) {
			res, err := service.GetAllBlsTasksByInstanceUUID(task.args.ctx, task.args.InstanceUUID)
			assert.Equal(t, res, task.want.res)
			assert.Equal(t, err, task.want.err)
		})

	}
}

// TestGetBlsTaskByTaskId 是对GetBlsTaskByTaskId函数的测试函数
func TestGetBlsTaskByTaskId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 定义结果类型
	type result struct {
		res *meta.Bls
		err error
	}

	// 定义参数类型
	type args struct {
		ctx    context.CsmContext
		TaskID string
	}

	// 定义测试任务类型
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}

	// 定义测试任务列表
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				ctx:    mockCtx,
				TaskID: ClusterId,
			},
			mockResult: &result{
				res: &meta.Bls{
					InstanceUUID: InstanceId,
					ClusterUUID:  ClusterId,
				},
				err: nil,
			},
			want: &result{
				res: &meta.Bls{
					InstanceUUID: InstanceId,
					ClusterUUID:  ClusterId,
				},
				err: nil,
			},
		},
		{
			name: "test-2",
			args: &args{
				ctx:    mockCtx,
				TaskID: ClusterId,
			},
			mockResult: &result{
				res: &meta.Bls{
					InstanceUUID: InstanceId,
					ClusterUUID:  ClusterId,
				},
				err: nil,
			},
			want: &result{
				res: &meta.Bls{
					InstanceUUID: InstanceId,
					ClusterUUID:  ClusterId,
				},
				err: nil,
			},
		},
	}

	// 遍历测试任务列表
	for _, task := range tasks {
		// 创建Mock对象
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		// 创建Service对象，并注入Mock对象
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}

		// 设置Mock对象的期望返回值
		mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).
			Return(task.mockResult.res, task.mockResult.err)

		// 运行测试任务
		t.Run(task.name, func(t *testing.T) {
			// 调用被测试方法
			res, err := service.GetBlsTaskByTaskId(task.args.ctx, task.args.TaskID)
			// 断言结果是否符合预期
			assert.Equal(t, res, task.want.res)
			assert.Equal(t, err, task.want.err)
		})
	}
}

// TestDeleteBlsTaskByClusterUUID 测试函数，用于测试DeleteBlsTaskByClusterUUID方法
func TestDeleteBlsTaskByClusterUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 定义结果类型
	type result struct {
		err error
	}

	// 定义参数类型
	type args struct {
		ctx         context.CsmContext
		clusterUUID string
	}

	// 定义测试任务类型
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}

	// 创建测试任务列表
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				// 设置测试上下文
				ctx: mockCtx,
				// 设置集群UUID
				clusterUUID: ClusterId,
			},
			// 设置模拟结果
			mockResult: &result{
				err: nil,
			},
			// 设置期望结果
			want: &result{
				err: nil,
			},
		},
	}

	// 遍历测试任务列表
	for _, task := range tasks {
		// 创建模拟Dao对象
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		// 创建服务对象
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}
		// 设置模拟Dao对象的期望行为
		mockDaoModel.EXPECT().BatchDelete(mockCtx, gomock.Any()).Return(task.mockResult.err)

		// 运行测试任务
		t.Run(task.name, func(t *testing.T) {
			// 调用被测试方法
			err := service.DeleteBlsTaskByClusterUUID(task.args.ctx, task.args.clusterUUID)
			// 断言期望结果和实际结果是否相等
			assert.Equal(t, err, task.want.err)
		})
	}
}

// TestDeleteBlsTaskByTaskID 删除指定任务ID的BLS任务，返回错误信息
func TestDeleteBlsTaskByTaskID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	// 定义结果类型
	type result struct {
		err error
	}

	// 定义参数类型
	type args struct {
		ctx    context.CsmContext
		taskId string
	}

	// 定义测试任务类型
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				ctx:    mockCtx,
				taskId: ClusterId,
			},
			mockResult: &result{
				err: nil,
			},
			want: &result{
				err: nil,
			},
		},
	}
	for _, task := range tasks {
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}
		mockDaoModel.EXPECT().BatchDelete(mockCtx, gomock.Any()).Return(task.mockResult.err)
		t.Run(task.name, func(t *testing.T) {
			err := service.DeleteBlsTaskByTaskID(task.args.ctx, task.args.taskId)
			assert.Equal(t, err, task.want.err)
		})
	}
}

// TestDeleteBlsTaskByInstanceUUID 删除指定实例UUID的BLS任务，并返回错误信息
// 参数：
//   - t *testing.T: 测试对象指针，用于断言和记录测试结果
//   - args *args: 包含测试参数的结构体指针，包含上下文ctx和实例UUIDinstanceUUID
//
// 返回值：
//   - none
func TestDeleteBlsTaskByInstanceUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 定义结果类型
	type result struct {
		err error
	}

	// 定义参数类型
	type args struct {
		ctx          context.CsmContext
		instanceUUID string
	}

	// 定义测试任务类型
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}

	// 创建测试任务列表
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				// 设置测试上下文
				ctx: mockCtx,
				// 设置集群UUID
				instanceUUID: InstanceId,
			},
			// 设置模拟结果
			mockResult: &result{
				err: nil,
			},
			// 设置期望结果
			want: &result{
				err: nil,
			},
		},
	}

	// 遍历测试任务列表
	for _, task := range tasks {
		// 创建模拟Dao对象
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		// 创建服务对象
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}
		// 设置模拟Dao对象的期望行为
		mockDaoModel.EXPECT().BatchDelete(mockCtx, gomock.Any()).Return(task.mockResult.err)

		// 运行测试任务
		t.Run(task.name, func(t *testing.T) {
			// 调用被测试方法
			err := service.DeleteBlsTaskByInstanceUUID(task.args.ctx, task.args.instanceUUID)
			// 断言期望结果和实际结果是否相等
			assert.Equal(t, err, task.want.err)
		})
	}
}

// TestWithLogOperation 测试WithLogOperation函数
// 参数t为testing.T类型，表示测试实例
func TestWithLogOperation(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type result struct {
		err error
	}
	type args struct {
		ctx      context.CsmContext
		endpoint string
	}
	// 定义结果类型
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				ctx:      mockCtx,
				endpoint: "bj",
			},
			mockResult: &result{err: nil},
			want:       &result{err: nil},
		},
		{
			name: "test-2",
			args: &args{
				ctx:      mockCtx,
				endpoint: "zzw",
			},
			mockResult: &result{
				err: fmt.Errorf("invaild endpoint %s", "zzw"),
			},
			want: &result{
				err: fmt.Errorf("invaild endpoint %s", "zzw"),
			},
		},
	}
	for _, task := range tasks {
		t.Run(task.name, func(t *testing.T) {
			mockBlsv3Model := blsv3Mocks.NewMockServiceInterface(ctrl)

			service := &Service{
				opt:          NewOption(mockDB),
				blsv3Service: mockBlsv3Model,
				// dao: mockDaoModel,
			}
			mockBlsv3Model.EXPECT().GetBlsV3Client(gomock.Any(), gomock.Any()).
				Return(nil).AnyTimes()
			err := service.WithTaskOperation(task.args.ctx, task.args.endpoint)
			if task.want != nil && task.want.err != nil {
				assert.Equal(t, task.want.err.Error(), err.Error())
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

// TestWithTaskOperation 测试WithTaskOperation函数
func TestWithTaskOperation(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type result struct {
		err error
	}
	type args struct {
		ctx      context.CsmContext
		endpoint string
	}
	// 定义结果类型
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				ctx:      mockCtx,
				endpoint: "bj",
			},
			mockResult: &result{err: nil},
			want:       &result{err: nil},
		},
		{
			name: "test-2",
			args: &args{
				ctx:      mockCtx,
				endpoint: "gz",
			},
			mockResult: &result{err: nil},
			want:       &result{err: nil},
		},
	}
	for _, task := range tasks {
		t.Run(task.name, func(t *testing.T) {
			patches := gomonkey.ApplyFunc(bce.GetDefaultClient, func(_ context.CsmContext, _ string) (
				*bce_sdk.BceClient, error) {
				return fakeClient, nil
			})
			defer patches.Reset()
			service := &Service{
				opt: NewOption(mockDB),
				// blsv3Service: mockBlsv3Model,
				// dao: mockDaoModel,
			}

			err := service.WithLogOperation(task.args.ctx, task.args.endpoint)
			if task.want != nil && task.want.err != nil {
				assert.Equal(t, task.want.err.Error(), err.Error())
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

// TestWithLogOperationWithAkSk 是一个测试函数，使用gomock对内部接口ServiceInterface进行mock，
// 测试Service的WithTaskOperationWithAkSk方法
func TestWithLogOperationWithAkSk(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type result struct {
		err error
	}
	type args struct {
		ctx      context.CsmContext
		ak       string
		sk       string
		endpoint string
	}
	// 定义结果类型
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				ctx:      mockCtx,
				endpoint: "bj",
				ak:       "test-ak",
				sk:       "test-sk",
			},
			mockResult: &result{err: nil},
			want:       &result{err: nil},
		},
		{
			name: "test-2",
			args: &args{
				ctx:      mockCtx,
				endpoint: "zzw",
				ak:       "test-ak",
				sk:       "test-sk",
			},
			mockResult: &result{
				err: fmt.Errorf("invaild endpoint %s", "zzw"),
			},
			want: &result{
				err: fmt.Errorf("invaild endpoint %s", "zzw"),
			},
		},
	}
	for _, task := range tasks {
		t.Run(task.name, func(t *testing.T) {
			mockBlsv3Model := blsv3Mocks.NewMockServiceInterface(ctrl)

			service := &Service{
				opt:          NewOption(mockDB),
				blsv3Service: mockBlsv3Model,
				// dao: mockDaoModel,
			}
			mockBlsv3Model.EXPECT().GetBlsV3ClientWithAkSk(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil).AnyTimes()
			err := service.WithTaskOperationWithAkSk(task.args.ctx, task.args.ak, task.args.sk, task.args.endpoint)
			if task.want != nil && task.want.err != nil {
				assert.Equal(t, task.want.err.Error(), err.Error())
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

// TestWithTaskOperationWithAkSk 测试函数，使用gomock和gomonkey进行mock和patch操作
// 参数t：testing.T，测试用例
func TestWithTaskOperationWithAkSk(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type result struct {
		err error
	}
	type args struct {
		ctx      context.CsmContext
		endpoint string
		ak       string
		sk       string
	}
	// 定义结果类型
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				ctx:      mockCtx,
				endpoint: "bj",
				ak:       "test-ak",
				sk:       "test-sk",
			},
			mockResult: &result{err: nil},
			want:       &result{err: nil},
		},
		{
			name: "test-2",
			args: &args{
				ctx:      mockCtx,
				endpoint: "gz",
				ak:       "test-ak",
				sk:       "test-sk",
			},
			mockResult: &result{err: nil},
			want:       &result{err: nil},
		},
	}
	for _, task := range tasks {
		t.Run(task.name, func(t *testing.T) {
			patches := gomonkey.ApplyFunc(bce.GetClientWithAkSk, func(_ context.CsmContext,
				_ string, _ string, _ string) (
				*bce_sdk.BceClient, error) {
				return fakeClient, nil
			})
			defer patches.Reset()
			service := &Service{
				opt: NewOption(mockDB),
				// blsv3Service: mockBlsv3Model,
				// dao: mockDaoModel,
			}

			err := service.WithLogOperationWithAkSk(task.args.ctx, task.args.ak, task.args.sk, task.args.endpoint)
			if task.want != nil && task.want.err != nil {
				assert.Equal(t, task.want.err.Error(), err.Error())
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

// testGetBlsLogs 测试函数，用于测试GetBlsLogs方法
func testGetBlsLogs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type result struct {
		res *bls_sdk.ListLogStoreResult
		err error
	}
	type args struct {
		ctx context.CsmContext
	}
	// 定义结果类型
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}
	mockRes := &bls_sdk.ListLogStoreResult{
		Result: []bls_sdk.LogStore{
			{
				LogStoreName: "test-name1",
				Retention:    10,
			},
		},
	}
	tasks := []testTask{
		{
			name: "test-1",
			args: &args{
				ctx: mockCtx,
			},
			mockResult: &result{
				err: nil,
				res: mockRes,
			},
			want: &result{
				err: fmt.Errorf("BlsLog is not initialized"),
				res: mockRes,
			},
		},
		{
			name: "test-2",
			args: &args{
				ctx: mockCtx,
			},
			mockResult: &result{
				err: nil,
				res: mockRes,
			},
			want: &result{
				err: fmt.Errorf("BlsLog is not initialized"),
				res: mockRes,
			},
		},
	}
	for _, task := range tasks {
		t.Run(task.name, func(t *testing.T) {
			patches := gomonkey.ApplyFunc(bls_sdk.ListLogStore, func(_ bce_sdk.Client, _ *bls_sdk.QueryConditions) (
				*bls_sdk.ListLogStoreResult, error) {
				return task.mockResult.res, nil
			})
			defer patches.Reset()
			service := &Service{
				opt:    NewOption(mockDB),
				blsLog: fakeBceClient,
				// blsv3Service: mockBlsv3Model,
				// dao: mockDaoModel,
			}

			res, err := service.GetBlsLogs(task.args.ctx)
			if task.want != nil && task.want.err != nil {
				assert.Equal(t, task.want.err.Error(), err.Error())
			} else {
				assert.Equal(t, task.want.res, res)
			}
		})
	}
}

// TestGetBlsTasksByTaskID 是测试函数，用于测试GetBlsTasksByTaskID函数
func TestGetBlsTasksByTaskID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		taskId       string
		blsv3Service bool
	}
	type result struct {
		res *blsv3.BlsTaskDetailResponse
		err error
	}
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}

	test := []testTask{
		{
			name: "test-1",
			args: &args{
				taskId:       "test-id1",
				blsv3Service: true,
			},
			mockResult: &result{
				err: nil,
				res: &blsv3.BlsTaskDetailResponse{
					Task: blsv3.BlsTaskDetail{Status: blsv3.TaskStatus{Name: "test-id1"}},
				},
			},
			want: &result{
				res: &blsv3.BlsTaskDetailResponse{
					Task: blsv3.BlsTaskDetail{Status: blsv3.TaskStatus{Name: "test-id1"}},
				},
				err: nil,
			},
		},
		{
			name: "test-2",
			args: &args{
				taskId:       "test-id2",
				blsv3Service: false,
			},
			mockResult: &result{
				err: nil,
				res: &blsv3.BlsTaskDetailResponse{
					Task: blsv3.BlsTaskDetail{Status: blsv3.TaskStatus{Name: "test-id2"}},
				},
			},
			want: &result{
				res: &blsv3.BlsTaskDetailResponse{
					Task: blsv3.BlsTaskDetail{Status: blsv3.TaskStatus{Name: "test-id2"}},
				},
				err: fmt.Errorf("Blsv3Service is not initialized"),
			},
		},
	}
	for _, task := range test {
		service := &Service{
			opt: NewOption(mockDB),
		}
		if task.args.blsv3Service {
			mockBlsv3Model := blsv3Mocks.NewMockServiceInterface(ctrl)
			mockBlsv3Model.EXPECT().GetBlsTask(gomock.Any()).
				Return(task.mockResult.res, task.mockResult.err).
				AnyTimes()
			service.blsv3Service = mockBlsv3Model
		}
		t.Run(task.name, func(t *testing.T) {
			res, err := service.GetBlsTasksByTaskID(mockCtx, task.args.taskId)
			if task.want != nil && task.want.err != nil {
				assert.Equal(t, task.want.err.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, task.want.res, res)
			}
		})
	}
}

// TestGetBlsTasksInstanceByTaskID 测试获取指定任务的实例信息
func TestGetBlsTasksInstanceByTaskID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		taskId       string
		blsv3Service bool
	}
	type result struct {
		res *blsv3.TaskInstanceResponseParameters
		err error
	}
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}

	test := []testTask{
		{
			name: "test-1",
			args: &args{
				taskId:       "test-id1",
				blsv3Service: true,
			},
			mockResult: &result{
				err: nil,
				res: &blsv3.TaskInstanceResponseParameters{
					TotalSize: 1,
				},
			},
			want: &result{
				res: &blsv3.TaskInstanceResponseParameters{
					TotalSize: 1,
				},
				err: nil,
			},
		},
		{
			name: "test-2",
			args: &args{
				taskId:       "test-id2",
				blsv3Service: false,
			},
			mockResult: &result{
				err: nil,
				res: &blsv3.TaskInstanceResponseParameters{
					TotalSize: 1,
				},
			},
			want: &result{
				res: &blsv3.TaskInstanceResponseParameters{
					TotalSize: 1,
				},
				err: fmt.Errorf("Blsv3Service is not initialized"),
			},
		},
	}
	for _, task := range test {
		service := &Service{
			opt: NewOption(mockDB),
		}
		if task.args.blsv3Service {
			mockBlsv3Model := blsv3Mocks.NewMockServiceInterface(ctrl)
			mockBlsv3Model.EXPECT().GetBlsTaskInstance(gomock.Any()).
				Return(task.mockResult.res, task.mockResult.err).
				AnyTimes()
			service.blsv3Service = mockBlsv3Model
		}
		t.Run(task.name, func(t *testing.T) {
			res, err := service.GetBlsTasksInstanceByTaskID(mockCtx, task.args.taskId)
			if task.want != nil && task.want.err != nil {
				assert.Equal(t, task.want.err.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, task.want.res, res)
			}
		})
	}
}

// TestCloseBlsTaskByTaskID 测试函数，用于测试CloseBlsTaskByTaskID函数
func TestCloseBlsTaskByTaskID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		taskId       string
		blsv3Service bool
	}
	type result struct {
		err error
	}
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}

	test := []testTask{
		{
			name: "test-1",
			args: &args{
				taskId:       "test-id1",
				blsv3Service: true,
			},
			mockResult: &result{
				err: nil,
			},
			want: &result{
				err: nil,
			},
		},
		{
			name: "test-2",
			args: &args{
				taskId:       "test-id2",
				blsv3Service: false,
			},
			mockResult: &result{
				err: nil,
			},
			want: &result{
				err: fmt.Errorf("Blsv3Service is not initialized"),
			},
		},
	}
	for _, task := range test {
		service := &Service{
			opt: NewOption(mockDB),
		}
		if task.args.blsv3Service {
			mockBlsv3Model := blsv3Mocks.NewMockServiceInterface(ctrl)
			mockBlsv3Model.EXPECT().PostBlsTaskAction(gomock.Any(), gomock.Any()).
				Return(task.mockResult.err).
				AnyTimes()
			service.blsv3Service = mockBlsv3Model
		}
		t.Run(task.name, func(t *testing.T) {
			err := service.CloseBlsTaskByTaskID(mockCtx, task.args.taskId)
			if task.want != nil && task.want.err != nil {
				assert.Equal(t, task.want.err.Error(), err.Error())
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

// TestCloseBlsTaskByTaskIDs 用于测试CloseBlsTaskByTaskIDs函数
func TestCloseBlsTaskByTaskIDs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		taskId       string
		blsv3Service bool
	}
	type result struct {
		err error
	}
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *result
	}

	test := []testTask{
		{
			name: "test-1",
			args: &args{
				taskId:       "test-id1",
				blsv3Service: true,
			},
			mockResult: &result{
				err: nil,
			},
			want: &result{
				err: nil,
			},
		},
		{
			name: "test-2",
			args: &args{
				taskId:       "test-id2",
				blsv3Service: false,
			},
			mockResult: &result{
				err: nil,
			},
			want: &result{
				err: fmt.Errorf("Blsv3Service is not initialized"),
			},
		},
	}
	for _, task := range test {
		service := &Service{
			opt: NewOption(mockDB),
		}
		if task.args.blsv3Service {
			mockBlsv3Model := blsv3Mocks.NewMockServiceInterface(ctrl)
			mockBlsv3Model.EXPECT().PostBlsTaskActionBatch(gomock.Any(), gomock.Any()).
				Return(task.mockResult.err).
				AnyTimes()
			service.blsv3Service = mockBlsv3Model
		}
		t.Run(task.name, func(t *testing.T) {
			err := service.CloseBlsTaskByTaskIDs(mockCtx, blsv3.TaskIDs{TaskIDs: []blsv3.TaskID{{TaskID: task.args.taskId}}})
			if task.want != nil && task.want.err != nil {
				assert.Equal(t, task.want.err.Error(), err.Error())
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

// TestCreateTask 是一个用于测试创建BLS任务的测试函数
func TestCreateTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		logConf   *blsv3.LogConf
		accountID string
	}
	type result struct {
		taskID *blsv3.TaskID
		err    error
	}
	type getresult struct {
		taskID string
		err    error
	}
	type testTask struct {
		name       string
		args       *args
		mockResult *result
		want       *getresult
	}

	test := []testTask{
		{
			name: "test-1",
			args: &args{
				logConf: &blsv3.LogConf{
					InstanceUUID: "test-instance-uuid",
					ClusterID:    "test-cluster-id",
					Type:         "BLS",
					LogStore:     "test-logstore",
				},
				accountID: "test-zzw-account-id",
			},
			mockResult: &result{
				taskID: &blsv3.TaskID{
					TaskID: "test-task-id1",
				},
				err: nil,
			},
			want: &getresult{
				taskID: "test-task-id1",
				err:    nil,
			},
		},
		{
			name: "test-1",
			args: &args{
				logConf: &blsv3.LogConf{
					InstanceUUID: "test-instance-uuid",
					ClusterID:    "test-cluster-id",
					Type:         "BLS",
					LogStore:     "test-logstore",
				},
				accountID: "test-zzw-account-id",
			},
			mockResult: &result{
				taskID: &blsv3.TaskID{
					TaskID: "test-task-id2",
				},
				err: nil,
			},
			want: &getresult{
				taskID: "test-task-id2",
				err:    nil,
			},
		},
	}
	for _, task := range test {
		service := &Service{
			opt: NewOption(mockDB),
		}
		patches := gomonkey.ApplyFunc(iam.GetAccountId, func(_ context.CsmContext) (
			string, error) {
			return task.args.accountID, nil
		})
		defer patches.Reset()

		mockBlsv3Model := blsv3Mocks.NewMockServiceInterface(ctrl)
		mockBlsv3Model.EXPECT().CreateTask(gomock.Any()).
			Return(task.mockResult.taskID, task.mockResult.err).
			AnyTimes()
		service.blsv3Service = mockBlsv3Model

		t.Run(task.name, func(t *testing.T) {
			taskID, err := service.CreateTask(mockCtx, task.args.logConf, "test-zzw-account-id")
			if task.want != nil && task.want.err != nil {
				assert.Equal(t, task.want.err.Error(), err.Error())
			} else {
				assert.Nil(t, err)
				assert.Equal(t, task.want.taskID, taskID)
			}
		})
	}
}

func TestGenerateTaskCreationRequestBody(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		logConf   *blsv3.LogConf
		accountID string
	}
	type testTask struct {
		name string
		args *args
		want *blsv3.TaskCreationRequestBody
	}

	test := []testTask{
		{
			name: "test-1",
			args: &args{
				logConf: &blsv3.LogConf{
					InstanceUUID: "test-instance-uuid",
					ClusterID:    "test-cluster-id",
					Type:         "BLS",
					LogStore:     "test-logstore",
				},
				accountID: "test-zzw-account-id",
			},
			want: &blsv3.TaskCreationRequestBody{
				Name: "csm_task_" + "test-instance-uuid" + "_" + "test-cluster-id",
				Config: blsv3.Config{
					SrcConfig: blsv3.SrcConfig{
						SrcType:        "container",
						LogType:        "stdout",
						SrcDir:         "/var/log",
						MatchedPattern: "^.*$",
						IgnorePattern:  "",
						LabelWhite: []blsv3.Label{
							{
								Key:   IstioProxyContainerNameKey,
								Value: IstioProxyContainerNameValue,
							},
						},
						ProcessType:  "none",
						LogTime:      "system",
						UseMultiline: false,
						TTL:          BlsTaskDefaultTTL,
					},
					DestConfig: blsv3.DestConfig{
						DestType:  "BLS",
						LogStore:  "test-logstore",
						AccountID: "test-zzw-account-id",
						RateLimit: 1,
					},
				},
			},
		},
	}
	for _, task := range test {
		t.Run(task.name, func(t *testing.T) {
			got := GenerateTaskCreationRequestBody(task.args.logConf, task.args.accountID)
			assert.Equal(t, *task.want, got)
		})
	}
}
