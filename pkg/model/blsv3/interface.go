package blsv3

import (
	bls_sdk "github.com/baidubce/bce-sdk-go/services/bls/api"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceInterface interface {
	WithTx(tx *dbutil.DB) ServiceInterface
	NewBlsTask(ctx context.CsmContext, bls *meta.Bls) error
	GetAllBlsTasksByClusterUUID(ctx context.CsmContext, clusterUUID string) (*[]meta.Bls, error)
	GetAllBlsTasksByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*[]meta.Bls, error)
	GetBlsTaskByTaskId(ctx context.CsmContext, taskId string) (*meta.Bls, error)
	DeleteBlsTaskByClusterUUID(ctx context.CsmContext, clusterUUID string) error
	DeleteBlsTaskByTaskID(ctx context.CsmContext, taskId string) error
	DeleteBlsTaskByInstanceUUID(ctx context.CsmContext, instanceUUID string) error
	WithTaskOperation(ctx context.CsmContext, endpoint string) error
	WithLogOperation(ctx context.CsmContext, endpoint string) error
	WithTaskOperationWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error
	WithLogOperationWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error
	GetBlsLogs(ctx context.CsmContext) (*bls_sdk.ListLogStoreResult, error)
	GetBlsTasksByTaskID(ctx context.CsmContext, taskId string) (
		*blsv3.BlsTaskDetailResponse, error)
	GetBlsTasksInstanceByTaskID(ctx context.CsmContext, taskId string) (
		*blsv3.TaskInstanceResponseParameters, error)
	CloseBlsTaskByTaskID(ctx context.CsmContext, taskId string) error
	CloseBlsTaskByTaskIDs(ctx context.CsmContext, tasks blsv3.TaskIDs) error
	CreateTask(ctx context.CsmContext, logConf *blsv3.LogConf, accountId string) (string, error)
	BoundTaskWithInstances(ctx context.CsmContext, taskId, clusterID string) error
}
