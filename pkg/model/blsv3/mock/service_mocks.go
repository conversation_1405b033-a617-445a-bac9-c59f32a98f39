// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	api "github.com/baidubce/bce-sdk-go/services/bls/api"
	gomock "github.com/golang/mock/gomock"
	blsv3 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blsv3"
	blsv30 "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/blsv3"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	dbutil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// BoundTaskWithInstances mocks base method.
func (m *MockServiceInterface) BoundTaskWithInstances(ctx context.CsmContext, taskId, clusterID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BoundTaskWithInstances", ctx, taskId, clusterID)
	ret0, _ := ret[0].(error)
	return ret0
}

// BoundTaskWithInstances indicates an expected call of BoundTaskWithInstances.
func (mr *MockServiceInterfaceMockRecorder) BoundTaskWithInstances(ctx, taskId, clusterID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BoundTaskWithInstances", reflect.TypeOf((*MockServiceInterface)(nil).BoundTaskWithInstances), ctx, taskId, clusterID)
}

// CloseBlsTaskByTaskID mocks base method.
func (m *MockServiceInterface) CloseBlsTaskByTaskID(ctx context.CsmContext, taskId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseBlsTaskByTaskID", ctx, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseBlsTaskByTaskID indicates an expected call of CloseBlsTaskByTaskID.
func (mr *MockServiceInterfaceMockRecorder) CloseBlsTaskByTaskID(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseBlsTaskByTaskID", reflect.TypeOf((*MockServiceInterface)(nil).CloseBlsTaskByTaskID), ctx, taskId)
}

// CloseBlsTaskByTaskIDs mocks base method.
func (m *MockServiceInterface) CloseBlsTaskByTaskIDs(ctx context.CsmContext, tasks blsv3.TaskIDs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseBlsTaskByTaskIDs", ctx, tasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseBlsTaskByTaskIDs indicates an expected call of CloseBlsTaskByTaskIDs.
func (mr *MockServiceInterfaceMockRecorder) CloseBlsTaskByTaskIDs(ctx, tasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseBlsTaskByTaskIDs", reflect.TypeOf((*MockServiceInterface)(nil).CloseBlsTaskByTaskIDs), ctx, tasks)
}

// CreateTask mocks base method.
func (m *MockServiceInterface) CreateTask(ctx context.CsmContext, logConf *blsv3.LogConf, accountId string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", ctx, logConf, accountId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockServiceInterfaceMockRecorder) CreateTask(ctx, logConf, accountId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockServiceInterface)(nil).CreateTask), ctx, logConf, accountId)
}

// DeleteBlsTaskByClusterUUID mocks base method.
func (m *MockServiceInterface) DeleteBlsTaskByClusterUUID(ctx context.CsmContext, clusterUUID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBlsTaskByClusterUUID", ctx, clusterUUID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBlsTaskByClusterUUID indicates an expected call of DeleteBlsTaskByClusterUUID.
func (mr *MockServiceInterfaceMockRecorder) DeleteBlsTaskByClusterUUID(ctx, clusterUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBlsTaskByClusterUUID", reflect.TypeOf((*MockServiceInterface)(nil).DeleteBlsTaskByClusterUUID), ctx, clusterUUID)
}

// DeleteBlsTaskByInstanceUUID mocks base method.
func (m *MockServiceInterface) DeleteBlsTaskByInstanceUUID(ctx context.CsmContext, instanceUUID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBlsTaskByInstanceUUID", ctx, instanceUUID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBlsTaskByInstanceUUID indicates an expected call of DeleteBlsTaskByInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) DeleteBlsTaskByInstanceUUID(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBlsTaskByInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).DeleteBlsTaskByInstanceUUID), ctx, instanceUUID)
}

// DeleteBlsTaskByTaskID mocks base method.
func (m *MockServiceInterface) DeleteBlsTaskByTaskID(ctx context.CsmContext, taskId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBlsTaskByTaskID", ctx, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBlsTaskByTaskID indicates an expected call of DeleteBlsTaskByTaskID.
func (mr *MockServiceInterfaceMockRecorder) DeleteBlsTaskByTaskID(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBlsTaskByTaskID", reflect.TypeOf((*MockServiceInterface)(nil).DeleteBlsTaskByTaskID), ctx, taskId)
}

// GetAllBlsTasksByClusterUUID mocks base method.
func (m *MockServiceInterface) GetAllBlsTasksByClusterUUID(ctx context.CsmContext, clusterUUID string) (*[]meta.Bls, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllBlsTasksByClusterUUID", ctx, clusterUUID)
	ret0, _ := ret[0].(*[]meta.Bls)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllBlsTasksByClusterUUID indicates an expected call of GetAllBlsTasksByClusterUUID.
func (mr *MockServiceInterfaceMockRecorder) GetAllBlsTasksByClusterUUID(ctx, clusterUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllBlsTasksByClusterUUID", reflect.TypeOf((*MockServiceInterface)(nil).GetAllBlsTasksByClusterUUID), ctx, clusterUUID)
}

// GetAllBlsTasksByInstanceUUID mocks base method.
func (m *MockServiceInterface) GetAllBlsTasksByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*[]meta.Bls, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllBlsTasksByInstanceUUID", ctx, instanceUUID)
	ret0, _ := ret[0].(*[]meta.Bls)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllBlsTasksByInstanceUUID indicates an expected call of GetAllBlsTasksByInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) GetAllBlsTasksByInstanceUUID(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllBlsTasksByInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).GetAllBlsTasksByInstanceUUID), ctx, instanceUUID)
}

// GetBlsLogs mocks base method.
func (m *MockServiceInterface) GetBlsLogs(ctx context.CsmContext) (*api.ListLogStoreResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlsLogs", ctx)
	ret0, _ := ret[0].(*api.ListLogStoreResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlsLogs indicates an expected call of GetBlsLogs.
func (mr *MockServiceInterfaceMockRecorder) GetBlsLogs(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlsLogs", reflect.TypeOf((*MockServiceInterface)(nil).GetBlsLogs), ctx)
}

// GetBlsTaskByTaskId mocks base method.
func (m *MockServiceInterface) GetBlsTaskByTaskId(ctx context.CsmContext, taskId string) (*meta.Bls, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlsTaskByTaskId", ctx, taskId)
	ret0, _ := ret[0].(*meta.Bls)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlsTaskByTaskId indicates an expected call of GetBlsTaskByTaskId.
func (mr *MockServiceInterfaceMockRecorder) GetBlsTaskByTaskId(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlsTaskByTaskId", reflect.TypeOf((*MockServiceInterface)(nil).GetBlsTaskByTaskId), ctx, taskId)
}

// GetBlsTasksByTaskID mocks base method.
func (m *MockServiceInterface) GetBlsTasksByTaskID(ctx context.CsmContext, taskId string) (*blsv3.BlsTaskDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlsTasksByTaskID", ctx, taskId)
	ret0, _ := ret[0].(*blsv3.BlsTaskDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlsTasksByTaskID indicates an expected call of GetBlsTasksByTaskID.
func (mr *MockServiceInterfaceMockRecorder) GetBlsTasksByTaskID(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlsTasksByTaskID", reflect.TypeOf((*MockServiceInterface)(nil).GetBlsTasksByTaskID), ctx, taskId)
}

// GetBlsTasksInstanceByTaskID mocks base method.
func (m *MockServiceInterface) GetBlsTasksInstanceByTaskID(ctx context.CsmContext, taskId string) (*blsv3.TaskInstanceResponseParameters, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlsTasksInstanceByTaskID", ctx, taskId)
	ret0, _ := ret[0].(*blsv3.TaskInstanceResponseParameters)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlsTasksInstanceByTaskID indicates an expected call of GetBlsTasksInstanceByTaskID.
func (mr *MockServiceInterfaceMockRecorder) GetBlsTasksInstanceByTaskID(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlsTasksInstanceByTaskID", reflect.TypeOf((*MockServiceInterface)(nil).GetBlsTasksInstanceByTaskID), ctx, taskId)
}

// NewBlsTask mocks base method.
func (m *MockServiceInterface) NewBlsTask(ctx context.CsmContext, bls *meta.Bls) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewBlsTask", ctx, bls)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewBlsTask indicates an expected call of NewBlsTask.
func (mr *MockServiceInterfaceMockRecorder) NewBlsTask(ctx, bls interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).NewBlsTask), ctx, bls)
}

// WithLogOperation mocks base method.
func (m *MockServiceInterface) WithLogOperation(ctx context.CsmContext, endpoint string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithLogOperation", ctx, endpoint)
	ret0, _ := ret[0].(error)
	return ret0
}

// WithLogOperation indicates an expected call of WithLogOperation.
func (mr *MockServiceInterfaceMockRecorder) WithLogOperation(ctx, endpoint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithLogOperation", reflect.TypeOf((*MockServiceInterface)(nil).WithLogOperation), ctx, endpoint)
}

// WithLogOperationWithAkSk mocks base method.
func (m *MockServiceInterface) WithLogOperationWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithLogOperationWithAkSk", ctx, ak, sk, endpoint)
	ret0, _ := ret[0].(error)
	return ret0
}

// WithLogOperationWithAkSk indicates an expected call of WithLogOperationWithAkSk.
func (mr *MockServiceInterfaceMockRecorder) WithLogOperationWithAkSk(ctx, ak, sk, endpoint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithLogOperationWithAkSk", reflect.TypeOf((*MockServiceInterface)(nil).WithLogOperationWithAkSk), ctx, ak, sk, endpoint)
}

// WithTaskOperation mocks base method.
func (m *MockServiceInterface) WithTaskOperation(ctx context.CsmContext, endpoint string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTaskOperation", ctx, endpoint)
	ret0, _ := ret[0].(error)
	return ret0
}

// WithTaskOperation indicates an expected call of WithTaskOperation.
func (mr *MockServiceInterfaceMockRecorder) WithTaskOperation(ctx, endpoint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTaskOperation", reflect.TypeOf((*MockServiceInterface)(nil).WithTaskOperation), ctx, endpoint)
}

// WithTaskOperationWithAkSk mocks base method.
func (m *MockServiceInterface) WithTaskOperationWithAkSk(ctx context.CsmContext, ak, sk, endpoint string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTaskOperationWithAkSk", ctx, ak, sk, endpoint)
	ret0, _ := ret[0].(error)
	return ret0
}

// WithTaskOperationWithAkSk indicates an expected call of WithTaskOperationWithAkSk.
func (mr *MockServiceInterfaceMockRecorder) WithTaskOperationWithAkSk(ctx, ak, sk, endpoint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTaskOperationWithAkSk", reflect.TypeOf((*MockServiceInterface)(nil).WithTaskOperationWithAkSk), ctx, ak, sk, endpoint)
}

// WithTx mocks base method.
func (m *MockServiceInterface) WithTx(tx *dbutil.DB) blsv30.ServiceInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(blsv30.ServiceInterface)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockServiceInterfaceMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockServiceInterface)(nil).WithTx), tx)
}
