package model

type ImmutableRuleRequest struct {

	// id，规则ID
	ID int64 `json:"id"`

	// projectID，命名空间ID
	ProjectID int64 `json:"projectID"`

	// disabled：是否禁用,默认为false
	Disabled bool `json:"disabled"`

	// action,默认取值：immutable
	Action string `json:"action"`

	// scope_selectors：镜像仓库匹配规则
	ScopeSelectors []*ImmutableSelector `json:"scope_selectors"`

	// tag_selectors：镜像版本匹配规则
	TagSelectors []*ImmutableSelector `json:"tag_selectors"`

	// template，默认取值：immutable_template
	Template string `json:"template"`

	// priority，优先级，默认0
	Priority int64 `json:"priority"`
}

// ImmutableSelector immutable selector
type ImmutableSelector struct {

	// decoration,scope_selectors value is: repoMatches、repoExcludes；tag_selectors value is: matches、excludes
	Decoration string `json:"decoration"`

	// extras，扩展字段
	Extras string `json:"extras"`

	// kind,默认值：doublestar
	Kind string `json:"kind"`

	// pattern,规则取值，正则表达式
	Pattern string `json:"pattern"`
}

type ImmutableRuleResult struct {

	// id，规则ID
	ID int64 `json:"id"`

	// action,values is: immutable
	Action string `json:"action"`

	// disabled：是否禁用
	Disabled bool `json:"disabled"`

	// priority，优先级
	Priority int64 `json:"priority"`

	// scope_selectors,镜像仓库匹配规则
	ScopeSelectors []ImmutableSelector `json:"scope_selectors"`

	// tag_selectors,版本匹配规则
	TagSelectors []*ImmutableSelector `json:"tag_selectors"`

	// template
	Template string `json:"template"`

	// projectID，命名空间ID
	ProjectID int64 `json:"projectID"`

	// ProjectName，命名空间名称
	ProjectName string `json:"projectName"`
}

// ListImmutableRuleResponse list replication response
type ListImmutableRuleResponse struct {
	PageInfo `json:",inline"`
	Items    []*ImmutableRuleResult `json:"items"`
}

type ImmutableRuleProjectResult struct {
	// projectID，命名空间ID
	ProjectID int64 `json:"projectID"`

	// projectName，命名空间名称
	ProjectName string `json:"projectName"`
}

type ImmutableRuleProjectResponse struct {
	Items []*ImmutableRuleProjectResult `json:"items"`
}
