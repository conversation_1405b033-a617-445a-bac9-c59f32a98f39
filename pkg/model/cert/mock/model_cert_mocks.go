// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	cert "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cert"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	dbutil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	kube "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// GenerateTlsSecret mocks base method.
func (m *MockServiceInterface) GenerateTlsSecret(ctx context.CsmContext, client kube.Client, namespace string, certInfo *meta.DomainCert) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateTlsSecret", ctx, client, namespace, certInfo)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateTlsSecret indicates an expected call of GenerateTlsSecret.
func (mr *MockServiceInterfaceMockRecorder) GenerateTlsSecret(ctx, client, namespace, certInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateTlsSecret", reflect.TypeOf((*MockServiceInterface)(nil).GenerateTlsSecret), ctx, client, namespace, certInfo)
}

// GetCertByClusterUUIDAndRegionAndAccountId mocks base method.
func (m *MockServiceInterface) GetCertByClusterUUIDAndRegionAndAccountId(ctx context.CsmContext, clusterId, region, accountId string) (*meta.Cert, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCertByClusterUUIDAndRegionAndAccountId", ctx, clusterId, region, accountId)
	ret0, _ := ret[0].(*meta.Cert)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCertByClusterUUIDAndRegionAndAccountId indicates an expected call of GetCertByClusterUUIDAndRegionAndAccountId.
func (mr *MockServiceInterfaceMockRecorder) GetCertByClusterUUIDAndRegionAndAccountId(ctx, clusterId, region, accountId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCertByClusterUUIDAndRegionAndAccountId", reflect.TypeOf((*MockServiceInterface)(nil).GetCertByClusterUUIDAndRegionAndAccountId), ctx, clusterId, region, accountId)
}

// NewCert mocks base method.
func (m *MockServiceInterface) NewCert(ctx context.CsmContext, cert *meta.Cert) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewCert", ctx, cert)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewCert indicates an expected call of NewCert.
func (mr *MockServiceInterfaceMockRecorder) NewCert(ctx, cert interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewCert", reflect.TypeOf((*MockServiceInterface)(nil).NewCert), ctx, cert)
}

// WithTx mocks base method.
func (m *MockServiceInterface) WithTx(tx *dbutil.DB) cert.ServiceInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(cert.ServiceInterface)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockServiceInterfaceMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockServiceInterface)(nil).WithTx), tx)
}
