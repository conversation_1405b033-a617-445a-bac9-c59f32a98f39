package cert

import (
	"github.com/jinzhu/gorm"
	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

const (
	mockCertServerData = "-----B<PERSON><PERSON> CERTIFICATE-----\n" +
		"MIIC5DCCAcwCAQEwDQYJKoZIhvcNAQELBQAwLTEVMBMGA1UECgwMZXhhbXBsZSBJ\n" +
		"bmMuMRQwEgYDVQQDDAtleGFtcGxlLmNvbTAeFw0yMjEyMTExNDMyMzNaFw0yMzEy\n" +
		"MTExNDMyMzNaMEMxHzAdBgNVBAMMFmhlbGxvd29ybGQuZXhhbXBsZS5jb20xIDAe\n" +
		"BgNVBAoMF2hlbGxvd29ybGQgb3JnYW5pemF0aW9uMIIBIjANBgkqhkiG9w0BAQEF\n" +
		"AAOCAQ8AMIIBCgKCAQEAq/hFHn/4ng7a/bcX5b/HH689sQMTiLXgBlO/ka4CHBxq\n" +
		"xD2WZo5oghLSYMYRwyGx2NUJxdnKatTukgYIYXnmCCXS49dHDWPN67dfzQsQl+5v\n" +
		"7Zbm/sFJ1JS40v4Ggcqflj3UioESeb1HzP60jIOR+57jbYejFjuMQrmyLCO88Xe1\n" +
		"VOjlS5S2DYbPJJEObKYx+iNJ0mXSx3pls228nuNtGxnvo10M1irSqaUxNt5QUHvJ\n" +
		"D7X5rdGAM/ckhSCQmDkLPZbAfbe1EXzQO7Ol2qDskBJ8NtXzouVFGZIzbpfS0Eb3\n" +
		"26HQOPReL1+mtEmR+z9zOfriQgSk2meI8SLD7DXQEwIDAQABMA0GCSqGSIb3DQEB\n" +
		"CwUAA4IBAQCToZO/3ndkzZftQrgQlZQIUGcj6JTGAT6EVVTD3BtRoVHnEyhs0gCR\n" +
		"CDoUu6M17KkBDblxVIjcV5f7xB0RBGLO6NDrkJ2yJb+7sAiS6OibkYoQN/tRVfDQ\n" +
		"QzewRNnS5K5wrDSGyfmg4GQGVQd75YqMbr0n0DXPArPqQCRJ+RqJKwEaz4Z3hxsB\n" +
		"ory2czHgLtCBXTPkjhAH6BjV9jOfyU7bjZkBuAEGVRc5vFKP7BbSDS+Oz22VK3J+\n" +
		"J1yyMb6dgiqijW/MxevW1lRKSp8HjZZqsFUjflsTB06eHhccjDvpArIOKStIcN94\n" +
		"wQv0GENYWWqNhoSGTUysk6q/MwtUrT/F\n-----END CERTIFICATE-----"
	mockCertPrivateKey = "-----BEGIN PRIVATE KEY-----\n" +
		"MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCr+EUef/ieDtr9\n" +
		"txflv8cfrz2xAxOIteAGU7+RrgIcHGrEPZZmjmiCEtJgxhHDIbHY1QnF2cpq1O6S\n" +
		"BghheeYIJdLj10cNY83rt1/NCxCX7m/tlub+wUnUlLjS/gaByp+WPdSKgRJ5vUfM\n" +
		"/rSMg5H7nuNth6MWO4xCubIsI7zxd7VU6OVLlLYNhs8kkQ5spjH6I0nSZdLHemWz\n" +
		"bbye420bGe+jXQzWKtKppTE23lBQe8kPtfmt0YAz9ySFIJCYOQs9lsB9t7URfNA7\n" +
		"s6XaoOyQEnw21fOi5UUZkjNul9LQRvfbodA49F4vX6a0SZH7P3M5+uJCBKTaZ4jx\n" +
		"IsPsNdATAgMBAAECggEAQlgcNlmRjUzW4IX38rtrc0ZUqwswSJA+FsLNh8LVbonq\n" +
		"CL5n3TX4jKH9RrdzV9ASgEAkLo/IvzFZvr0UE6MHA0KwWxUNBNXpthP/IigwJSUo\n" +
		"SkVq5YKlXJ1ptoCo+v60Va6R5kNIYO4MmsHy3rjNo4DXZcSrvuKHSKDCY7P/6zmt\n" +
		"NbsD0ILZeExzi/PG6f0zxWqZ4VGio2o92gvf6PGtzUeMTbCpoMiZYLMbWQCpYNZI\n" +
		"iJzc9d8jB5SC+0sNLrCAgBFMQ+FCI2r43Txtra2Fw2NZudvaOUCSGyGrnO9M20i6\n" +
		"La3HxjvbftpI4eJCKR+iQC3OUxTXldFDhaUtpSeUpQKBgQDQZJ/XGGcsUkRVOMRy\n" +
		"tef2Lrt5yM38Z3HDGStDUJ4dgAH/dgTJtAW3yxulnbwukwvcQ+QxlgAeodaLwHG6\n" +
		"rj1/iytCRad+FAp/xR6xybUKs20Udx62vOYPCM1IO7srufHIsRO6nwSoUlc5BEoE\n" +
		"YZRKmc9jOh/9BpaR5FMNc28xhwKBgQDTQYR1nkUvgSbtXUOI8GZW3O75yhpfJUNq\n" +
		"CQwOd3gd2y6u/m4apfzYebdJC1SALsBkH1puKKezqyvqhBLnK4Ay5f6afeQzUV9/\n" +
		"tdNpUyIMKnJkuXKO1dwFxsbTKl4mmlgQs1TY4rHQFy8elTNWgVrkgV5YRyDZHDAB\n" +
		"tS0Im+NAFQKBgHqhnEYRgDVevAOCgmgWj8gZPA2wkzbjENkEaKcWO9WUTJiuNKcV\n" +
		"nv/HMB/HsMOfmK0n7pMXeD6OchiPSN3YRyxVE0HOh3/z/zGtKCDrV/RSHfCbIKHc\n" +
		"i21xeov1SnGy3IuDWN7LUQxUWCufUIc/fOphlN8Jh2V4iQaVXCJgOaDlAoGAQIBe\n" +
		"mzLp2OlMI2k4d5U/CgKHDMmCZ86O2M9OWRRtHmCPzmps38ISg14Wcjz9Xe3oXCme\n" +
		"iMfs/FSlIgOVafPEPsYuiKkrheewiuPPWvdBDAnhx5M/wRDXDwR0LIFhIdQ6U08N\n" +
		"9vKwa+LIRkQz2pPAVsiRL8733pJWrg7NYzWFm0kCgYEAn1BS9AgV+u+HEjjsipLq\n" +
		"FjKG8RusbDZzMzBYcOLIa0j/bSSr7SHm48n1Yu22680FEf0n2HS1QmEI/dli18g/\n" +
		"ZxI5o3YvPc52O1G8Ocpm56L/lWY2ED2lIYoIvW7g+/HgbBCfz9o78EhyJXkgZllO\n" +
		"LCE1MsVEm/yV1cupJNkLFA8=\n-----END PRIVATE KEY-----"

	certEndpoint   = "cloud.cert.endpoint"
	bceIamProfile  = "cloud.iamProfile"
	bceServiceRole = "bceServiceRole"
)

type Option struct {
	DB             *dbutil.DB
	CertEndpoint   string
	BceIamProfile  string
	BceServiceRole string
}

func NewOption(d *gorm.DB) *Option {
	return &Option{
		DB:             dbutil.NewDB(d),
		CertEndpoint:   viper.GetString(certEndpoint),
		BceIamProfile:  viper.GetString(bceIamProfile),
		BceServiceRole: viper.GetString(bceServiceRole),
	}
}
