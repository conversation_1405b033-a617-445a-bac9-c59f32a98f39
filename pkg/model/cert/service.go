package cert

import (
	"context"

	"github.com/spf13/viper"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/cert"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cert/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

type Service struct {
	opt *Option
	dao model.CertDaoInterface
}

// NewCertService 初始化证书服务
func NewCertService(option *Option) *Service {
	return &Service{
		opt: option,
		dao: dao.NewCertDao(option.DB),
	}
}

func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	Opt := *s.opt
	Opt.DB = tx
	return NewCertService(&Opt)
}

// NewCert 创建证书
func (s *Service) NewCert(ctx csmContext.CsmContext, cert *meta.Cert) error {
	certData, _ := s.GetCertByClusterUUIDAndRegionAndAccountId(ctx, cert.Region, cert.ClusterUUID, cert.AccountId)
	if certData != nil {
		ctx.CsmLogger().Infof("cert %v has exists, so skipped", cert)
		return nil
	}
	return s.dao.Save(ctx, cert)
}

// GetCertByClusterUUIDAndRegionAndAccountId 根据集群 uuid、地域、账户 id 获取证书
func (s *Service) GetCertByClusterUUIDAndRegionAndAccountId(ctx csmContext.CsmContext, region, clusterUUID, accountId string) (cert *meta.Cert, err error) {
	where := &meta.Cert{
		Deleted:     csm.Int(0),
		Region:      region,
		ClusterUUID: clusterUUID,
		AccountId:   accountId,
	}
	certData, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}
	// 类型转换
	return certData.(*meta.Cert), nil
}

func (s *Service) GenerateTlsSecret(ctx csmContext.CsmContext, client kube.Client, namespace string, certInfo *meta.DomainCert) (string, error) {
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout)
	defer cancel()

	certClient, err := cert.GetClient(ctx, s.opt.BceIamProfile, s.opt.BceServiceRole, s.opt.CertEndpoint)
	if err != nil {
		return "", err
	}

	caCert := make(map[string][]byte)
	if viper.GetBool("local.dev") {
		caCert["tls.crt"] = []byte(mockCertServerData)
		caCert["tls.key"] = []byte(mockCertPrivateKey)
	} else {
		result, tmpErr := certClient.GetCertsRawData(certInfo.ID)
		if tmpErr != nil {
			return "", tmpErr
		}
		caCert["tls.crt"] = []byte(result.CertServerData)
		caCert["tls.key"] = []byte(result.CertPrivateKey)
	}

	tlsSecret := &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name: certInfo.ID + "-" + certInfo.Name,
		},
		Data: caCert,
	}
	_, err = client.Kube().CoreV1().Secrets(namespace).Create(cxt, tlsSecret, metav1.CreateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("fail to create tls secret: %v", err)
		return "", err
	}

	return tlsSecret.ObjectMeta.Name, nil
}
