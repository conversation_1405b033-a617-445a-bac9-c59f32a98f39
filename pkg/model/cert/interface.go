package cert

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
)

type ServiceInterface interface {
	WithTx(tx *dbutil.DB) ServiceInterface
	NewCert(ctx context.CsmContext, cert *meta.Cert) error
	GetCertByClusterUUIDAndRegionAndAccountId(ctx context.CsmContext, clusterId, region, accountId string) (cert *meta.Cert, err error)

	GenerateTlsSecret(ctx context.CsmContext, client kube.Client, namespace string, certInfo *meta.DomainCert) (string, error)
}
