package register

import (
	"fmt"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	registerDao "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/register/dao"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/uuid"
	"time"
)

type Service struct {
	opt         *Option
	dao         dao.BaseInterface
	activateDao dao.BaseInterface
}

func NewRegisterInstancesService(option *Option) *Service {
	return &Service{
		opt:         option,
		dao:         registerDao.NewRegisterInstanceDao(option.DB),
		activateDao: registerDao.NewActivateDao(option.DB),
	}
}

func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	nOpt := *s.opt
	nOpt.DB = tx
	return NewRegisterInstancesService(&nOpt)
}

func (s *Service) NewRegisterInstance(ctx csmContext.CsmContext, instances *meta.RegisterInstance) error {

	instances.Deleted = csm.Int(0)
	now := time.Now()
	instances.CreateTime = &now
	instances.UpdateTime = &now
	instances.Status = int(meta.InitStatus)
	instances.ServerProtocol = "eureka"
	instances.ServerPort = "8761"
	if err := s.dao.Save(ctx, instances); err != nil {
		return err
	}
	return nil
}

func (s *Service) PostActivate(ctx csmContext.CsmContext) error {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return csmErr.NewUnauthorizedException("user is nil", err)
	}
	cnt, err := s.activateDao.Count(ctx, nil, &meta.Activate{
		AccountID: accountId,
	})
	if err != nil {
		return err
	}
	if cnt > 0 {
		return nil
	}

	now := time.Now()
	activate := &meta.Activate{
		AccountID: accountId,
		Deleted:   csm.Int(0),
	}
	activate.CreateTime = &now
	activate.UpdateTime = &now
	return s.activateDao.Save(ctx, activate)
}

func (s *Service) GetActivate(ctx csmContext.CsmContext) error {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return csmErr.NewUnauthorizedException("user is nil", err)
	}
	cnt, err := s.activateDao.Count(ctx, nil, &meta.Activate{
		AccountID: accountId,
	})
	if err != nil {
		return err
	}
	if cnt == 0 {
		return csmErr.NewInactiveException("mse inactive")
	}
	return nil
}

// GenerateInstancesID 生成 register center 的实例 ID
func (s *Service) GenerateInstancesID(ctx csmContext.CsmContext) (res string, err error) {
	// 最多尝试生成八次
	var result string
	for i := 0; i < 8; i++ {
		result = uuid.GetRegisterInstanceUUID()
		var instance meta.RegisterInstance
		notFound := s.opt.DB.Where("instance_id = ?", result).Find(&instance).RecordNotFound()
		if notFound {
			return result, nil
		}
	}
	return "", fmt.Errorf("duplicate register instance id in database")
}

func (s *Service) UpdateRegisterInstance(ctx csmContext.CsmContext, update *meta.RegisterInstance) (*meta.RegisterInstance, error) {
	where := &meta.RegisterInstance{
		Deleted:    csm.Int(0),
		InstanceId: update.InstanceId,
	}
	old, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}

	if old == nil {
		return nil, csmErr.NewMissingParametersException("Register Instances where is nil.")
	}

	oldInstance := old.(*meta.RegisterInstance)
	if len(oldInstance.InstanceId) == 0 {
		return nil, csmErr.NewMissingParametersException("InstanceId required.")
	}

	if err := s.dao.Update(ctx, old, update); err != nil {
		return nil, err
	}
	return update, nil
}

func (s *Service) DeleteRegisterInstanceByInstanceId(ctx csmContext.CsmContext, id string) error {
	where := &meta.RegisterInstance{
		InstanceId: id,
	}
	return s.dao.BatchDelete(ctx, where)
}

func (s *Service) GetRegisterInstances(ctx csmContext.CsmContext, query *meta.QueryRegisterInstance) (
	*dao.BasePageListResult, *[]meta.RegisterInstance, error) {
	search := &meta.Instances{}
	if query.Keyword != "" {
		switch query.KeywordType {
		case constants.InstanceName:
			search.InstanceName = query.Keyword
		case constants.InstanceId:
			search.InstanceUUID = query.Keyword
		}
	}
	where := &meta.RegisterInstance{
		Deleted:   csm.Int(0),
		AccountId: query.AccountID,
		Region:    query.Region,
	}

	basePageInfo, registerInsList, err := s.dao.ListPage(ctx, search, where,
		query.OrderBy, dbutil.DBOrder(query.Order), query.PageSize, query.PageNo)
	if err != nil {
		return nil, nil, err
	}

	insList := registerInsList.(*[]meta.RegisterInstance)
	ctx.CsmLogger().Infof("registerInsList size: %s", len(*insList))
	return basePageInfo, insList, nil
}

func (s *Service) GetRegisterInstancesByInstanceId(ctx csmContext.CsmContext, instanceId string) (*meta.RegisterInstance, error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}
	where := &meta.RegisterInstance{
		Deleted:    csm.Int(0),
		AccountId:  accountId,
		InstanceId: instanceId,
	}
	instanceInfo, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}
	if instanceInfo == nil {
		return nil, nil
	}
	return instanceInfo.(*meta.RegisterInstance), nil
}
