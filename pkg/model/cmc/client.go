package cmc

import (
	"fmt"
	"strings"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"
)

var (
	mysqlConnMap = map[string]*gorm.DB{}
)

// Client 是基于数据库的 Interface 的实现
type Client struct {
	db *gorm.DB
}

// NewClient - 初始化 Client
func NewClient(mysqlConn string) (*Client, error) {
	if mysqlConn == "" {
		return nil, fmt.Errorf("mysqlConn is empty")
	}

	var db *gorm.DB
	var err error

	// 复用已有连接
	if existedDB, ok := mysqlConnMap[mysqlConn]; ok {
		if existedDB == nil {
			return nil, fmt.Errorf("DB in mysqlConnMap is nil")
		}
		db = existedDB
	} else {
		db, err = gorm.Open(mysql.Open(mysqlConn), &gorm.Config{
			NamingStrategy: schema.NamingStrategy{
				SingularTable: true, // 使用单数表名（默认是复数）
			},
		})
		if err != nil {
			csmlog.Errorf("Init DB failed: %v", err)
			return nil, err
		}

		mysqlConnMap[mysqlConn] = db
	}

	sqlDB, err := db.DB()
	if err != nil {
		csmlog.Errorf("Init DB failed: %v", err)
		return nil, err
	}
	// Init DB
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetConnMaxLifetime(0)

	return &Client{
		db: db,
	}, nil
}
func (c *Client) DB(ctx csmContext.CsmContext) (*gorm.DB, error) {
	return c.db, nil
}

func (c *Client) CreatePlanWithResources(ctx csmContext.CsmContext, plan Plan, planResources []PlanResource) error {
	tx := c.db.Begin()
	if err := c.createPlanWithTx(ctx, tx, plan); err != nil {
		tx.Rollback()
		ctx.CsmLogger().Errorf("CreatePlanWithResources failed: %v", err)
		return err
	}

	if err := c.createPlanResourcesWithTx(ctx, tx, planResources); err != nil {
		tx.Rollback()
		ctx.CsmLogger().Errorf("CreatePlanWithResources failed: %v", err)
		return err
	}

	if err := tx.Commit().Error; err != nil {
		ctx.CsmLogger().Errorf("CreatePlanWithResources failed: %v", err)
		return err
	}
	return nil
}

func ParseWildNameForSql(name string) string {
	if IsWildName(name) {
		name = strings.ReplaceAll(name, `_`, `\_`)
	}
	if IsPrefixWildName(name) {
		name = name[:len(name)-1] + "%"
	}
	if IsSuffixWildName(name) {
		name = "%" + name[1:]
	}
	return name
}

// IsPrefixWildName 判断名字是否为通配名字，只支持前缀索引(名字最后为*)
func IsPrefixWildName(name string) bool {
	length := len(name)
	return length >= 1 && name[length-1:length] == "*"
}

// IsSuffixWildName 判断名字是否为通配名字，只支持后缀索引(名字第一个字符为*)
func IsSuffixWildName(name string) bool {
	length := len(name)
	return length >= 1 && name[0:1] == "*"
}

// IsWildName 判断名字是否为通配名字，前缀或者后缀
func IsWildName(name string) bool {
	return IsPrefixWildName(name) || IsSuffixWildName(name)
}
