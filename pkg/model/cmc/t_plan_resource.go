package cmc

import (
	"errors"

	"gorm.io/gorm"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type PlanResource struct {
	gorm.Model

	PlanID           string `json:"planId" gorm:"column:plan_id"`
	ResourceUuid     string `json:"resourceUuid" gorm:"column:resource_uuid"`
	Status           string `json:"status" gorm:"column:status"`
	TargetResourceID string `json:"targetResourceId" gorm:"column:target_resource_id"`
	TargetRegion     string `json:"targetRegion" gorm:"column:target_region"`
	Data             string `json:"data" gorm:"column:data"`

	AccountID string `json:"accountId" gorm:"column:account_id"`
	UserID    string `json:"userId" gorm:"column:user_id"`

	Plan     *Plan     `gorm:"foreignKey:PlanID;references:PlanID"`
	Resource *Resource `gorm:"foreignKey:ResourceUuid;references:Uuid"`
}

func (PlanResource) TableName() string {
	return PlanResourceTableName
}

func (c *Client) createPlanResourcesWithTx(ctx csmContext.CsmContext, tx *gorm.DB, planResources []PlanResource) error {
	if err := tx.Create(&planResources).Error; err != nil {
		ctx.CsmLogger().Errorf("CreatePlanResourcesWithTx error: %v", err)
		return err
	}
	return nil
}

func (c *Client) GetAllMigratingPlanResources(ctx csmContext.CsmContext) ([]*PlanResource, error) {
	planResources := make([]*PlanResource, 0)
	if err := c.db.Table(PlanResourceTableName).
		Where("status in (?)", []string{PlanResourceStatusCreating, PlanResourceStatusConfiguring}).
		Find(&planResources).Error; err != nil {
		ctx.CsmLogger().Errorf("GetAllMigratingPlanResources error: %v", err)
		return nil, err
	}
	return planResources, nil
}

func (c *Client) GetPlanResources(ctx csmContext.CsmContext, accountId, planId string,
	resourceId, resourceName *string, offset, limit int) (int64, []*PlanResource, error) {
	planResources := make([]*PlanResource, 0)

	// 构建基础查询（不含预加载）
	db := c.db.Table("t_plan_resource").
		Where("plan_id = ?", planId).
		Where("account_id = ?", accountId)

	// 构建resource_id过滤条件（应用于主查询，而非预加载）
	if resourceId != nil {
		if IsWildName(*resourceId) {
			db = db.Where("t_plan_resource.resource_uuid IN (SELECT uuid FROM t_resource WHERE resource_id LIKE ?)",
				ParseWildNameForSql(*resourceId))
		} else {
			db = db.Where("t_plan_resource.resource_uuid IN (SELECT uuid FROM t_resource WHERE resource_id = ?)",
				*resourceId)
		}
	}

	if resourceName != nil {
		if IsWildName(*resourceName) {
			db = db.Where("t_plan_resource.resource_uuid IN (SELECT uuid FROM t_resource WHERE resource_name LIKE ?)",
				ParseWildNameForSql(*resourceName))
		} else {
			db = db.Where("t_plan_resource.resource_uuid IN (SELECT uuid FROM t_resource WHERE resource_name = ?)",
				*resourceName)
		}
	}

	// 统计总记录数（包含resource_id过滤条件）
	var cnt int64 = 0
	if err := db.Model(&PlanResource{}).Count(&cnt).Error; err != nil {
		ctx.CsmLogger().Errorf("count resources failed: %v", err)
		return 0, nil, err
	}

	// 构建预加载查询（仅用于加载关联数据，不影响计数）
	resourceQuery := c.db.Table("t_resource")
	if resourceId != nil {
		if IsWildName(*resourceId) {
			resourceQuery = resourceQuery.Where("resource_id LIKE ?", ParseWildNameForSql(*resourceId))
		} else {
			resourceQuery = resourceQuery.Where("resource_id = ?", *resourceId)
		}
	}

	// 执行带预加载的查询（使用已过滤的db）
	if err := db.Preload("Resource", resourceQuery).
		Order("t_plan_resource.created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&planResources).Error; err != nil {
		ctx.CsmLogger().Errorf("query resources failed: %v", err)
		return 0, nil, err
	}

	return cnt, planResources, nil
}

func (c *Client) DeletePlanResources(ctx csmContext.CsmContext, accountId, planId string, uuids []string) error {
	if len(uuids) == 0 {
		return nil
	}

	if err := c.db.Table(PlanResourceTableName).
		Where("account_id = ?", accountId).
		Where("plan_id = ?", planId).
		Where("resource_uuid IN (?)", uuids).
		Unscoped().
		Delete(&PlanResource{}).Error; err != nil {
		ctx.CsmLogger().Errorf("delete resources failed: %v", err)
		return err
	}
	return nil
}

func (c *Client) GetPlanResource(ctx csmContext.CsmContext, accountId, planId, resourceUuid string) (*PlanResource, error) {
	planResource := &PlanResource{}
	db := c.db.Table(PlanResourceTableName).
		Where("account_id = ?", accountId).
		Where("resource_uuid = ?", resourceUuid)
	if planId != "" {
		db = db.Where("plan_id = ?", planId)
	}
	if err := db.Preload("Resource").
		Preload("Plan").
		First(planResource).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		ctx.CsmLogger().Errorf("get plan resource failed: %v", err)
		return nil, err
	}
	return planResource, nil
}

func (c *Client) UpdatePlanResource(ctx csmContext.CsmContext, planResource *PlanResource) error {
	if planResource == nil {
		return nil
	}
	if err := c.db.Table(PlanResourceTableName).Save(planResource).Error; err != nil {
		ctx.CsmLogger().Errorf("update plan resource failed: %v", err)
		return err
	}
	return nil
}
