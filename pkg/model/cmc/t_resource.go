package cmc

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type Resource struct {
	gorm.Model

	Uuid             string `json:"uuid" gorm:"column:uuid"`
	ResourceID       string `json:"resourceId" gorm:"column:resource_id"`
	ResourceName     string `json:"resourceName" gorm:"column:resource_name"`
	TaskID           string `json:"taskId" gorm:"column:task_id"`
	SrcCloudPlatform string `json:"srcCloudPlatform" gorm:"column:src_cloud_platform"`
	Region           string `json:"region" gorm:"column:region"`
	TaskName         string `json:"taskName" gorm:"column:task_name"`
	Data             string `json:"data" gorm:"column:data"`

	AccountID string `json:"accountId" gorm:"column:account_id"`
	UserID    string `json:"userId" gorm:"column:user_id"`
}

func (Resource) TableName() string {
	return ResourceTableName
}

func (c *Client) CreateOrUpdateResources(ctx csmContext.CsmContext, resources []*Resource) error {
	if len(resources) == 0 {
		ctx.CsmLogger().Infof("resources is empty, skip CreateOrUpdateResources")
		return nil
	}

	var err error

	// 开始事务
	tx := c.db.Begin()

	defer func() {
		if err != nil {
			ctx.CsmLogger().Errorf("Error occur during CreateOrUpdateResources, rollback transaction")
			tx.Rollback()
		}
	}()

	for _, resource := range resources {
		var existingResource Resource
		if resource == nil {
			ctx.CsmLogger().Errorf("resource is nil")
			continue
		}
		err = tx.Table(ResourceTableName).Select("*").
			Where("resource_id = ?", resource.ResourceID).
			Where("task_id = ?", resource.TaskID).
			Where("src_cloud_platform = ?", resource.SrcCloudPlatform).
			First(&existingResource).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 记录不存在，创建新记录
				resource.Uuid = uuid.New().String()
				if err = tx.Table(ResourceTableName).Create(&resource).Error; err != nil {
					ctx.CsmLogger().Errorf("failed to create resource: %v", err)
					return err
				}
			} else {
				// 查询时发生错误
				ctx.CsmLogger().Errorf("failed to query resource: %v", err)
				return err
			}
		} else {
			existingResource.UpdatedAt = time.Now()
			existingResource.TaskID = resource.TaskID
			existingResource.SrcCloudPlatform = resource.SrcCloudPlatform
			existingResource.Region = resource.Region
			existingResource.TaskName = resource.TaskName
			existingResource.ResourceName = resource.ResourceName
			existingResource.Data = resource.Data

			if err = tx.Table(ResourceTableName).Save(&existingResource).Error; err != nil {
				ctx.CsmLogger().Errorf("failed to update resource: %v", err)
				return err
			}
		}
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		ctx.CsmLogger().Errorf("DB commit failed: %v", err)
		return err
	}

	return nil
}

func (c *Client) GetResources(ctx csmContext.CsmContext, accountId string,
	resourceId, taskId, taskName, srcCloudPlatform *string, offset, limit int) (int64, []*Resource, error) {
	resources := make([]*Resource, 0)

	db := c.db.Table(ResourceTableName).Where("account_id = ?", accountId)
	if resourceId != nil {
		if IsWildName(*resourceId) {
			db = db.Where("resource_id LIKE ?", ParseWildNameForSql(*resourceId))
		} else {
			db = db.Where("resource_id = ?", *resourceId)
		}
	}
	if taskId != nil {
		if IsWildName(*taskId) {
			db = db.Where("task_id LIKE ?", ParseWildNameForSql(*taskId))
		} else {
			db = db.Where("task_id = ?", *taskId)
		}
	}
	if taskName != nil {
		if IsWildName(*taskName) {
			db = db.Where("task_name LIKE ?", ParseWildNameForSql(*taskName))
		} else {
			db = db.Where("task_name = ?", *taskName)
		}
	}
	if srcCloudPlatform != nil {
		if IsWildName(*srcCloudPlatform) {
			db = db.Where("src_cloud_platform LIKE ?", ParseWildNameForSql(*srcCloudPlatform))
		} else {
			db = db.Where("src_cloud_platform = ?", *srcCloudPlatform)
		}
	}

	var cnt int64 = 0
	if err := db.Count(&cnt).Error; err != nil {
		ctx.CsmLogger().Errorf("count resources failed: %v", err)
		return 0, nil, err
	}

	if err := db.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&resources).Error; err != nil {
		ctx.CsmLogger().Errorf("query resources failed: %v", err)
		return 0, nil, err
	}

	return cnt, resources, nil
}

func (c *Client) GetResourcesWithUUID(ctx csmContext.CsmContext, accountID string, uuids []string) ([]*Resource, error) {
	resources := make([]*Resource, 0)
	if err := c.db.Table(ResourceTableName).
		Where("account_id = ? AND uuid IN (?)", accountID, uuids).
		Find(&resources).Error; err != nil {
		ctx.CsmLogger().Errorf("query resources failed: %v", err)
		return nil, err
	}
	return resources, nil
}

func (c *Client) GetResourceCount(ctx csmContext.CsmContext, accountId string) (int64, error) {
	var count int64

	if err := c.db.Table(ResourceTableName).
		Model(&Resource{}).
		Where("account_id = ?", accountId).
		Count(&count).Error; err != nil {
		ctx.CsmLogger().Errorf("count resources failed: %v", err)
		return count, err
	}

	return count, nil
}

func (c *Client) GetResourceByResourceID(ctx csmContext.CsmContext, accountID, resourceID string) (*Resource, error) {
	return nil, nil
	//var resource Resource
	//if err := c.db.Table(ResourceTableName).Select("*").Where("resource_id = ? AND account_id = ?", resourceID, accountID).Find(&resource).Error; err != nil {
	//	ctx.CsmLogger().Errorf("Get resource info by resourceID: %v failed: %v ", resourceID, err)
	//	return nil, err
	//}
	//return &resource, nil
}

func (c *Client) DeleteResources(ctx csmContext.CsmContext, accountID string, uuids []string) error {
	if len(uuids) == 0 {
		return nil
	}

	if err := c.db.Table(ResourceTableName).
		Where("account_id = ?", accountID).
		Where("uuid IN (?)", uuids).
		Unscoped().
		Delete(&Resource{}).Error; err != nil {
		ctx.CsmLogger().Errorf("delete resources failed: %v", err)
		return err
	}
	return nil
}
