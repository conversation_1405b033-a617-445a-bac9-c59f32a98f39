package cmc

import (
	"gorm.io/gorm"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type Plan struct {
	gorm.Model

	PlanID           string `json:"planId" gorm:"column:plan_id"`
	PlanName         string `json:"planName" gorm:"column:plan_name"`
	SrcCloudPlatform string `json:"srcCloudPlatform" gorm:"column:src_cloud_platform"`

	AccountID string `json:"accountId" gorm:"column:account_id"`
	UserID    string `json:"userId" gorm:"column:user_id"`
}

func (Plan) TableName() string {
	return PlanTableName
}

func (c *Client) createPlanWithTx(ctx csmContext.CsmContext, tx *gorm.DB, plan Plan) error {
	if err := tx.Table(PlanTableName).Create(&plan).Error; err != nil {
		ctx.CsmLogger().Errorf("CreatePlanWithTx error: %v", err)
		return err
	}
	return nil
}

func (c *Client) DeletePlan(ctx csmContext.CsmContext, accountID string, planIDs []string) error {
	if err := c.db.Table(PlanTableName).
		Where("account_id = ? and plan_id in (?)", accountID, planIDs).
		Unscoped().
		Delete(&Plan{}).Error; err != nil {
		ctx.CsmLogger().Errorf("DeletePlanWithTx error: %v", err)
		return err
	}
	return nil
}
