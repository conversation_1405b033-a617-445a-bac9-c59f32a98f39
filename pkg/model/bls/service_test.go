package bls

import (
	"errors"
	"os"
	"path/filepath"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/bls"
	blsMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/bls/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	mockDB, _  = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx, _ = context.NewCsmContextMock()
)

func TestCreateHostingBlsTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name      string
		testRes   *bls.TaskCreationResponse
		expectRes string
		expectErr error
	}{
		{
			name: "correct-CreateHostingBlsTask",
			testRes: &bls.TaskCreationResponse{
				TaskID: "task-1",
			},
			expectRes: "task-1",
			expectErr: nil,
		},
	}
	for _, tt := range tests {
		mockBlsClient := blsMock.NewMockServiceInterface(ctrl)
		service := &Service{
			option:    NewOption(),
			blsClient: mockBlsClient,
		}

		mockBlsClient.EXPECT().GetClientWithAkSk(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mockBlsClient.EXPECT().CreateHostingBlsTask(gomock.Any()).Return(tt.testRes, nil)

		t.Run(tt.name, func(t *testing.T) {
			res, _ := service.CreateHostingBlsTask(mockCtx, &meta.LogConf{}, "bj")
			assert.Equal(t, tt.expectRes, res)
		})
	}
}

func TestDeleteHostingBlsTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-DeleteHostingBlsTask",
			expectErr: nil,
		},
	}
	for _, tt := range tests {
		mockBlsClient := blsMock.NewMockServiceInterface(ctrl)
		service := &Service{
			option:    NewOption(),
			blsClient: mockBlsClient,
		}

		mockBlsClient.EXPECT().GetClientWithAkSk(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mockBlsClient.EXPECT().DeleteHostingBlsTask(gomock.Any()).Return(nil)

		t.Run(tt.name, func(t *testing.T) {
			err := service.DeleteHostingBlsTask(mockCtx, "task-1", "bj")
			assert.Nil(t, err)
		})
	}
}

func TestGetHostingBlsTaskDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name      string
		testRes   *bls.TaskDetailResponse
		expectRes *meta.Log
		expectErr error
	}{
		{
			name: "correct-CreateHostingBlsTask",
			testRes: &bls.TaskDetailResponse{
				Task: bls.TaskDetail{
					Status: bls.TaskStatus{
						Status: string(BlsTaskStatusRunning),
					},
					Config: bls.ConfigBls{
						DestConfig: bls.DestBlsConfig{
							DestType: "BLS",
							LogStore: "a",
						},
					},
				},
			},
			expectRes: &meta.Log{
				Enabled: true,
				Type:    "BLS",
				LogFile: "a",
			},
			expectErr: nil,
		},
	}
	for _, tt := range tests {
		mockBlsClient := blsMock.NewMockServiceInterface(ctrl)
		service := &Service{
			option:    NewOption(),
			blsClient: mockBlsClient,
		}

		mockBlsClient.EXPECT().GetClientWithAkSk(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mockBlsClient.EXPECT().GetHostingBlsTaskDetail(gomock.Any()).Return(tt.testRes, nil)

		t.Run(tt.name, func(t *testing.T) {
			res, _ := service.GetHostingBlsTaskDetail(mockCtx, "task-1", "bj")
			assert.Equal(t, tt.expectRes, res)
		})
	}
}

func TestUpdateHostingBlsTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name      string
		resErr    error
		expectErr error
	}{
		{
			name:      "correct-UpdateHostingBlsTask",
			resErr:    nil,
			expectErr: nil,
		},
		{
			name:      "failed-UpdateHostingBlsTask",
			resErr:    errors.New("fail to update bls task"),
			expectErr: errors.New("fail to update bls task"),
		},
	}
	for _, tt := range tests {
		mockBlsClient := blsMock.NewMockServiceInterface(ctrl)
		service := &Service{
			option:    NewOption(),
			blsClient: mockBlsClient,
		}

		mockBlsClient.EXPECT().GetClientWithAkSk(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mockBlsClient.EXPECT().UpdateHostingBlsTask(gomock.Any(), gomock.Any()).Return(tt.resErr)

		t.Run(tt.name, func(t *testing.T) {
			err := service.UpdateHostingBlsTask(mockCtx, &meta.LogConf{}, "task-1", "bj")
			if tt.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.expectErr.Error())
			}
		})
	}
}

func TestStartHostingBlsTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name      string
		resErr    error
		expectErr error
	}{
		{
			name:      "correct-StartHostingBlsTask",
			resErr:    nil,
			expectErr: nil,
		},
		{
			name:      "failed-StartHostingBlsTask",
			resErr:    errors.New("fail to start bls task"),
			expectErr: errors.New("fail to start bls task"),
		},
	}
	for _, tt := range tests {
		mockBlsClient := blsMock.NewMockServiceInterface(ctrl)
		service := &Service{
			option:    NewOption(),
			blsClient: mockBlsClient,
		}

		mockBlsClient.EXPECT().GetClientWithAkSk(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mockBlsClient.EXPECT().StartHostingBlsTask(gomock.Any()).Return(tt.resErr)

		t.Run(tt.name, func(t *testing.T) {
			err := service.StartHostingBlsTask(mockCtx, "task-1", "bj")
			if tt.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.expectErr.Error())
			}
		})
	}
}

func TestPauseHostingBlsTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name      string
		resErr    error
		expectErr error
	}{
		{
			name:      "correct-PauseHostingBlsTask",
			resErr:    nil,
			expectErr: nil,
		},
		{
			name:      "failed-PauseHostingBlsTask",
			resErr:    errors.New("fail to pause bls task"),
			expectErr: errors.New("fail to pause bls task"),
		},
	}
	for _, tt := range tests {
		mockBlsClient := blsMock.NewMockServiceInterface(ctrl)
		service := &Service{
			option:    NewOption(),
			blsClient: mockBlsClient,
		}

		mockBlsClient.EXPECT().GetClientWithAkSk(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mockBlsClient.EXPECT().PauseHostingBlsTask(gomock.Any()).Return(tt.resErr)

		t.Run(tt.name, func(t *testing.T) {
			err := service.PauseHostingBlsTask(mockCtx, "task-1", "bj")
			if tt.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.expectErr.Error())
			}
		})
	}
}
