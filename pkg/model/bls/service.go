package bls

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/bls"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// VIPs - BLS各地域VIPs
var VIPs = map[string]string{
	"bj": "http://**************:8085",
	"gz": "http://************:8085",
}

type Service struct {
	option    *Option
	blsClient bls.ServiceInterface
}

func NewService(opt *Option) *Service {
	return &Service{
		option:    opt,
		blsClient: &bls.Client{},
	}
}

func (s *Service) CreateHostingBlsTask(ctx csmContext.CsmContext, logConf *meta.LogConf, region string) (string, error) {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return "", err
	}
	result, err := s.blsClient.CreateHostingBlsTask(logConf)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to create bls task: %v", err)
		return "", err
	}
	return result.TaskID, nil
}

func (s *Service) UpdateHostingBlsTask(ctx csmContext.CsmContext, logConf *meta.LogConf, taskID, region string) error {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return err
	}
	err = s.blsClient.UpdateHostingBlsTask(logConf, taskID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to update bls task: %v", err)
		return err
	}
	return nil
}

func (s *Service) DeleteHostingBlsTask(ctx csmContext.CsmContext, taskID, region string) error {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return err
	}
	err = s.blsClient.DeleteHostingBlsTask(taskID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to delete bls task: %v", err)
		return err
	}
	return nil
}

func (s *Service) StartHostingBlsTask(ctx csmContext.CsmContext, taskID, region string) error {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return err
	}
	err = s.blsClient.StartHostingBlsTask(taskID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to start bls task: %v", err)
		return err
	}
	return nil
}

func (s *Service) PauseHostingBlsTask(ctx csmContext.CsmContext, taskID, region string) error {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return err
	}
	err = s.blsClient.PauseHostingBlsTask(taskID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to pause bls task: %v", err)
		return err
	}
	return nil
}

func (s *Service) GetHostingBlsTaskDetail(ctx csmContext.CsmContext, taskID, region string) (*meta.Log, error) {
	// endpoint := fmt.Sprintf(s.option.BlsEndpoint, region), 待BLS OpenAPI上线后
	endpoint := VIPs[region]
	err := s.blsClient.GetClientWithAkSk(ctx, s.option.HostingAccessKey, s.option.HostingSecretKey, endpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get bls client with hosting aksk: %v", err)
		return nil, err
	}
	result, err := s.blsClient.GetHostingBlsTaskDetail(taskID)
	if err != nil {
		ctx.CsmLogger().Errorf("fail to get the detail of bls task: %s, : %v", taskID, err)
		return nil, err
	}
	res := &meta.Log{
		Enabled: result.Task.Status.Status == string(BlsTaskStatusRunning),
		Type:    result.Task.Config.DestConfig.DestType,
		LogFile: result.Task.Config.DestConfig.LogStore,
	}
	return res, nil
}
