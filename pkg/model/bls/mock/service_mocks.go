// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateHostingBlsTask mocks base method.
func (m *MockServiceInterface) CreateHostingBlsTask(ctx context.CsmContext, logConf *meta.LogConf, region string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHostingBlsTask", ctx, logConf, region)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateHostingBlsTask indicates an expected call of CreateHostingBlsTask.
func (mr *MockServiceInterfaceMockRecorder) CreateHostingBlsTask(ctx, logConf, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHostingBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).CreateHostingBlsTask), ctx, logConf, region)
}

// DeleteHostingBlsTask mocks base method.
func (m *MockServiceInterface) DeleteHostingBlsTask(ctx context.CsmContext, taskID, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteHostingBlsTask", ctx, taskID, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteHostingBlsTask indicates an expected call of DeleteHostingBlsTask.
func (mr *MockServiceInterfaceMockRecorder) DeleteHostingBlsTask(ctx, taskID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteHostingBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).DeleteHostingBlsTask), ctx, taskID, region)
}

// GetHostingBlsTaskDetail mocks base method.
func (m *MockServiceInterface) GetHostingBlsTaskDetail(ctx context.CsmContext, taskID, region string) (*meta.Log, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHostingBlsTaskDetail", ctx, taskID, region)
	ret0, _ := ret[0].(*meta.Log)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHostingBlsTaskDetail indicates an expected call of GetHostingBlsTaskDetail.
func (mr *MockServiceInterfaceMockRecorder) GetHostingBlsTaskDetail(ctx, taskID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHostingBlsTaskDetail", reflect.TypeOf((*MockServiceInterface)(nil).GetHostingBlsTaskDetail), ctx, taskID, region)
}

// PauseHostingBlsTask mocks base method.
func (m *MockServiceInterface) PauseHostingBlsTask(ctx context.CsmContext, taskID, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PauseHostingBlsTask", ctx, taskID, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// PauseHostingBlsTask indicates an expected call of PauseHostingBlsTask.
func (mr *MockServiceInterfaceMockRecorder) PauseHostingBlsTask(ctx, taskID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PauseHostingBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).PauseHostingBlsTask), ctx, taskID, region)
}

// StartHostingBlsTask mocks base method.
func (m *MockServiceInterface) StartHostingBlsTask(ctx context.CsmContext, taskID, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartHostingBlsTask", ctx, taskID, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartHostingBlsTask indicates an expected call of StartHostingBlsTask.
func (mr *MockServiceInterfaceMockRecorder) StartHostingBlsTask(ctx, taskID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartHostingBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).StartHostingBlsTask), ctx, taskID, region)
}

// UpdateHostingBlsTask mocks base method.
func (m *MockServiceInterface) UpdateHostingBlsTask(ctx context.CsmContext, logConf *meta.LogConf, taskID, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHostingBlsTask", ctx, logConf, taskID, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateHostingBlsTask indicates an expected call of UpdateHostingBlsTask.
func (mr *MockServiceInterfaceMockRecorder) UpdateHostingBlsTask(ctx, logConf, taskID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHostingBlsTask", reflect.TypeOf((*MockServiceInterface)(nil).UpdateHostingBlsTask), ctx, logConf, taskID, region)
}
