package bls

import "github.com/spf13/viper"

const (
	blsEndpoint      = "cloud.bls.endpoint"
	hostingAccessKey = "cloud.hosting.access_key"
	hostingSecretKey = "cloud.hosting.secret_key"
)

type TaskStatus string

const (
	BlsTaskStatusRunning  TaskStatus = "Running"
	BlsTaskStatusPaused   TaskStatus = "Paused"
	BlsTaskStatusAbnormal TaskStatus = "Abnormal"
)

type Option struct {
	// BLS endpoint
	BlsEndpoint string
	// HostingAccessKey 托管集群创建BLS日志传输任务使用的 ak
	HostingAccessKey string
	// HostingSecretKey 托管集群创建BLS日志传输任务使用的 sk
	HostingSecretKey string
}

// NewOption 初始化 bls 配置
func NewOption() *Option {
	return &Option{
		BlsEndpoint:      viper.GetString(blsEndpoint),
		HostingAccessKey: viper.GetString(hostingAccessKey),
		HostingSecretKey: viper.GetString(hostingSecretKey),
	}
}
