// Package bls implements BLS dependent interfaces
package bls

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	CreateHostingBlsTask(ctx csmContext.CsmContext, logConf *meta.LogConf, region string) (string, error)
	UpdateHostingBlsTask(ctx csmContext.CsmContext, logConf *meta.LogConf, taskID, region string) error
	DeleteHostingBlsTask(ctx csmContext.CsmContext, taskID, region string) error
	StartHostingBlsTask(ctx csmContext.CsmContext, taskID, region string) error
	PauseHostingBlsTask(ctx csmContext.CsmContext, taskID, region string) error
	GetHostingBlsTaskDetail(ctx csmContext.CsmContext, taskID, region string) (*meta.Log, error)
}
