package model

import "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"

type InstancesDaoInterface interface {
	dao.BaseInterface
}

type ClusterDaoInterface interface {
	dao.BaseInterface
}

type CertDaoInterface interface {
	dao.BaseInterface
}

type GatewayDaoInterface interface {
	dao.BaseInterface
}

type LaneGroupDaoInterface interface {
	dao.BaseInterface
}

type LaneDaoInterface interface {
	dao.BaseInterface
}

type BlsDaoInterface interface {
	dao.BaseInterface
}
