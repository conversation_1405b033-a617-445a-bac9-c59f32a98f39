package gateway

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	"github.com/stretchr/testify/assert"

	daoMock "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao/mocks"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

var (
	mockDB, _    = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	User         = "User"
	iamAccountId = "123"
	iamUser      = &sdkIAM.User{
		ID:   "1",
		Name: "test-user",
		Domain: sdkIAM.UserDomain{
			ID:   iamAccountId,
			Name: "aaaa",
		},
	}
	testRegion = "bj"
)

func TestNewGateway(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-SaveGateway",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockDao := daoMock.NewMockBaseInterface(ctrl)

		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDao,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockDao.EXPECT().Save(mockCtx, gomock.Any()).Return(nil)
			testGateway := &meta.GatewayModel{}
			err := service.NewGateway(mockCtx, testGateway)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestUpdateGateway(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-UpdateGateway",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockDao := daoMock.NewMockBaseInterface(ctrl)

		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDao,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockDao.EXPECT().Update(mockCtx, gomock.Any(), gomock.Any()).Return(nil)
			testGateway := &meta.GatewayModel{}
			err := service.UpdateGateway(mockCtx, testGateway, testGateway)
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestDeleteGateway(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-DeleteGateway",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockDao := daoMock.NewMockBaseInterface(ctrl)

		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDao,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockDao.EXPECT().BatchDelete(mockCtx, gomock.Any()).Return(nil)
			err := service.DeleteGateway(mockCtx, "inst-1", "gw-1")
			if testInfo.expectErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Contains(t, err.Error(), testInfo.expectErr.Error())
			}
		})
	}
}

func TestGetGatewayInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-GetGatewayInfo",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockDao := daoMock.NewMockBaseInterface(ctrl)

		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDao,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()
			mockCtx.Set(User, iamUser)
			mockCtx.Set(region.ContextRegion, testRegion)

			expectRes := &meta.GatewayModel{
				InstanceUUID: "inst-1",
				GatewayUUID:  "gw-1",
			}
			mockDao.EXPECT().LoadWithWhere(mockCtx, gomock.Any()).Return(expectRes, nil)

			res, err := service.GetGatewayInfo(mockCtx, "inst-1", "gw-1")
			assert.Nil(t, err)
			assert.Equal(t, *expectRes, *res)
		})
	}
}

func TestGetGatewayList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		expectErr error
	}{
		{
			name:      "correct-GetGatewayList",
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockDao := daoMock.NewMockBaseInterface(ctrl)

		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDao,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx := context.MockNewCsmContext()

			expectRes := &[]meta.GatewayModel{
				{
					InstanceUUID: "inst-1",
					GatewayUUID:  "gw-1",
				},
				{
					InstanceUUID: "inst-2",
					GatewayUUID:  "gw-2",
				},
			}
			mockDao.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(expectRes, nil)
			testMrp := &meta.CsmMeshRequestParams{
				AccountID: iamAccountId,
				Region:    testRegion,
			}
			res, err := service.GetGatewayList(mockCtx, testMrp)
			assert.Nil(t, err)
			assert.Equal(t, len(*expectRes), len(*res))
		})
	}
}
