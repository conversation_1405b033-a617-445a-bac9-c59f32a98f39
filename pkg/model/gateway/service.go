package gateway

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/gateway/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Service struct {
	opt *Option
	dao model.GatewayDaoInterface
}

func NewGatewayService(option *Option) *Service {
	return &Service{
		opt: option,
		dao: dao.NewGatewayDao(option.DB),
	}
}

// WithTx 网关实例 tx
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	nOpt := *s.opt
	nOpt.DB = tx
	return NewGatewayService(&nOpt)
}

// NewGateway 创建网关实例
func (s *Service) NewGateway(ctx csmContext.CsmContext, gateway *meta.GatewayModel) error {
	return s.dao.Save(ctx, gateway)
}

// UpdateGateway 更新网关实例
func (s *Service) UpdateGateway(ctx csmContext.CsmContext, dstGateway, updateGateway *meta.GatewayModel) error {
	return s.dao.Update(ctx, dstGateway, updateGateway)
}

// DeleteGateway 删除网关实例
func (s *Service) DeleteGateway(ctx csmContext.CsmContext, instanceUUID, gatewayUUID string) error {
	where := &meta.GatewayModel{
		InstanceUUID: instanceUUID,
		GatewayUUID:  gatewayUUID,
	}
	return s.dao.BatchDelete(ctx, where)
}

// GetGatewayInfo 根据instanceUUID和gatewayUUID获取网关信息
func (s *Service) GetGatewayInfo(ctx csmContext.CsmContext, instanceUUID, gatewayUUID string) (*meta.GatewayModel, error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}
	where := &meta.GatewayModel{
		Deleted:      csm.Int(0),
		AccountId:    accountId,
		InstanceUUID: instanceUUID,
		GatewayUUID:  gatewayUUID,
	}
	gatewayInfo, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}
	return gatewayInfo.(*meta.GatewayModel), nil
}

// GetGatewayList 获取网关列表
func (s *Service) GetGatewayList(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (*[]meta.GatewayModel, error) {
	search := &meta.GatewayModel{}
	if mrp.Keyword != "" {
		switch mrp.KeywordType {
		case GatewayName:
			search.GatewayName = mrp.Keyword
		case GatewayUUID:
			search.GatewayUUID = mrp.Keyword
		}
	}
	where := &meta.GatewayModel{
		Deleted:      csm.Int(0),
		AccountId:    mrp.AccountID,
		Region:       mrp.Region,
		InstanceUUID: mrp.InstanceUUID,
	}
	not := &meta.GatewayModel{}

	gatewayList, err := s.dao.ListAll(ctx, search, where, not)
	if err != nil {
		return nil, err
	}

	return gatewayList.(*[]meta.GatewayModel), nil
}
