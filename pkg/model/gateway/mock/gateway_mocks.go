// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	gateway "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/gateway"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	dbutil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// DeleteGateway mocks base method.
func (m *MockServiceInterface) DeleteGateway(ctx context.CsmContext, instanceUUID, gatewayUUID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteGateway", ctx, instanceUUID, gatewayUUID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteGateway indicates an expected call of DeleteGateway.
func (mr *MockServiceInterfaceMockRecorder) DeleteGateway(ctx, instanceUUID, gatewayUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGateway", reflect.TypeOf((*MockServiceInterface)(nil).DeleteGateway), ctx, instanceUUID, gatewayUUID)
}

// GetGatewayInfo mocks base method.
func (m *MockServiceInterface) GetGatewayInfo(ctx context.CsmContext, instanceUUID, gatewayUUID string) (*meta.GatewayModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGatewayInfo", ctx, instanceUUID, gatewayUUID)
	ret0, _ := ret[0].(*meta.GatewayModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGatewayInfo indicates an expected call of GetGatewayInfo.
func (mr *MockServiceInterfaceMockRecorder) GetGatewayInfo(ctx, instanceUUID, gatewayUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGatewayInfo", reflect.TypeOf((*MockServiceInterface)(nil).GetGatewayInfo), ctx, instanceUUID, gatewayUUID)
}

// GetGatewayList mocks base method.
func (m *MockServiceInterface) GetGatewayList(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*[]meta.GatewayModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGatewayList", ctx, mrp)
	ret0, _ := ret[0].(*[]meta.GatewayModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGatewayList indicates an expected call of GetGatewayList.
func (mr *MockServiceInterfaceMockRecorder) GetGatewayList(ctx, mrp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGatewayList", reflect.TypeOf((*MockServiceInterface)(nil).GetGatewayList), ctx, mrp)
}

// NewGateway mocks base method.
func (m *MockServiceInterface) NewGateway(ctx context.CsmContext, gateway *meta.GatewayModel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewGateway", ctx, gateway)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewGateway indicates an expected call of NewGateway.
func (mr *MockServiceInterfaceMockRecorder) NewGateway(ctx, gateway interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewGateway", reflect.TypeOf((*MockServiceInterface)(nil).NewGateway), ctx, gateway)
}

// UpdateGateway mocks base method.
func (m *MockServiceInterface) UpdateGateway(ctx context.CsmContext, whereGateway, updateGateway *meta.GatewayModel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGateway", ctx, whereGateway, updateGateway)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGateway indicates an expected call of UpdateGateway.
func (mr *MockServiceInterfaceMockRecorder) UpdateGateway(ctx, whereGateway, updateGateway interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGateway", reflect.TypeOf((*MockServiceInterface)(nil).UpdateGateway), ctx, whereGateway, updateGateway)
}

// WithTx mocks base method.
func (m *MockServiceInterface) WithTx(tx *dbutil.DB) gateway.ServiceInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(gateway.ServiceInterface)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockServiceInterfaceMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockServiceInterface)(nil).WithTx), tx)
}
