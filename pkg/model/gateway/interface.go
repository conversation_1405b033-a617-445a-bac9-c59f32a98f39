package gateway

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceInterface interface {
	WithTx(tx *dbutil.DB) ServiceInterface
	NewGateway(ctx context.CsmContext, gateway *meta.GatewayModel) error
	UpdateGateway(ctx context.CsmContext, dstGateway, updateGateway *meta.GatewayModel) error
	DeleteGateway(ctx context.CsmContext, instanceUUID, gatewayUUID string) error
	GetGatewayInfo(ctx context.CsmContext, instanceUUID, gatewayUUID string) (*meta.GatewayModel, error)
	GetGatewayList(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*[]meta.GatewayModel, error)
}
