package eip

import (
	"fmt"

	eip_sdk "github.com/baidubce/bce-sdk-go/services/eip"
	"github.com/baidubce/bce-sdk-go/util"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/eip"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type Service struct {
	option *Option
}

func NewService(opt *Option) *Service {
	return &Service{
		option: opt,
	}
}

func (service *Service) CreateEip(ctx context.CsmContext, args *eip_sdk.CreateEipArgs,
	region string) (*eip_sdk.CreateEipResult, error) {
	client, err := eip.GetDefaultClient(ctx, fmt.Sprintf(service.option.EipEndpoint, region))
	if err != nil {
		return nil, err
	}

	return client.CreateEip(args)
}

func (service *Service) BindEip(ctx context.CsmContext, args *eip_sdk.BindEipArgs, ip, region string) error {
	client, err := eip.GetDefaultClient(ctx, fmt.Sprintf(service.option.EipEndpoint, region))
	if err != nil {
		return err
	}

	return client.BindEip(ip, args)
}

func (service *Service) UnbindEip(ctx context.CsmContext, ip, region string) error {
	client, err := eip.GetDefaultClient(ctx, fmt.Sprintf(service.option.EipEndpoint, region))
	if err != nil {
		return err
	}

	return client.UnBindEip(ip, util.NewUUID())
}

func (service *Service) IsEipBinded(ctx context.CsmContext, args *eip_sdk.ListEipArgs, region string) (bool, string, error) {
	client, err := eip.GetDefaultClient(ctx, fmt.Sprintf(service.option.EipEndpoint, region))
	if err != nil {
		return false, "", err
	}
	listEipRes, err := client.ListEip(args)
	if err != nil {
		return false, "", err
	}
	if listEipRes == nil || listEipRes.EipList == nil || len(listEipRes.EipList) == 0 {
		return false, "", err
	}
	return true, listEipRes.EipList[0].Eip, nil
}

func (service *Service) ReleaseEip(ctx context.CsmContext, ip, region string) error {
	client, err := eip.GetDefaultClient(ctx, fmt.Sprintf(service.option.EipEndpoint, region))
	if err != nil {
		return err
	}

	return client.DeleteEip(ip, util.NewUUID())
}

func (service *Service) ListEip(ctx context.CsmContext, args *eip_sdk.ListEipArgs, region string) (*eip_sdk.ListEipResult, error) {
	client, err := eip.GetDefaultClient(ctx, fmt.Sprintf(service.option.EipEndpoint, region))
	if err != nil {
		return nil, err
	}
	return client.ListEip(args)
}
