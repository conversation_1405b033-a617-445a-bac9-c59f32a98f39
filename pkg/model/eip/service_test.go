package eip

import (
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	eip_sdk "github.com/baidubce/bce-sdk-go/services/eip"
	"github.com/golang/mock/gomock"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/eip"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/testdata"
)

const (
	localMode = "local.dev"
)

var (
	FAKE_CLIENT *eip_sdk.Client
	mockCtx     = context.MockNewCsmContext()
)

func init() {
	FAKE_CLIENT = &eip_sdk.Client{}
}

func before() {
	viper.Set(eipEndpoint, testdata.EipEndpoint)
	testdata.MockViper()
}

func TestBindEIP(t *testing.T) {
	before()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		localMode bool
		expectErr error
	}{
		{
			name:      "failed-BindEip",
			localMode: true,
			expectErr: nil,
		},
		{
			name:      "failed-PassIAM",
			localMode: false,
			expectErr: nil,
		},
	}

	for _, testInfo := range testInfos {
		service := NewService(NewOption())
		t.Run(testInfo.name, func(t *testing.T) {
			args := &eip_sdk.BindEipArgs{
				InstanceType: "BLB",
				InstanceId:   "lb-40dd38d0",
			}

			viper.Set(localMode, testInfo.localMode)
			err := service.BindEip(mockCtx, args, "*******", "bj")
			if testInfo.localMode {
				if !strings.Contains(err.Error(), "InstanceNotFound") {
					t.Errorf("BindEip failed. %v", err)
				}
			} else {
				if !strings.Contains(err.Error(), "get credential failed") {
					t.Errorf("BindEip failed. %v", err)
				}
			}
		})
	}
}

func TestUnBindEip(t *testing.T) {
	patches := gomonkey.ApplyFunc(eip.GetDefaultClient, func(_ context.CsmContext, _ string) (
		*eip_sdk.Client, error) {
		return FAKE_CLIENT, nil
	})
	defer patches.Reset()

	var c *eip_sdk.Client
	patches.ApplyMethod(c, "UnBindEip", func(_ *eip_sdk.Client, _ string, _ string) error {
		return nil
	})

	service := &Service{
		option: &Option{
			EipEndpoint: "%s",
		},
	}
	err := service.UnbindEip(mockCtx, "eip-1", "bj")
	assert.Nil(t, err)
}

func TestIsEIPBinded(t *testing.T) {
	before()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		localMode bool
		expectErr error
	}{
		{
			name:      "failed-IsEipBinded",
			localMode: true,
			expectErr: nil,
		},
	}

	for _, testInfo := range testInfos {
		service := NewService(NewOption())
		t.Run(testInfo.name, func(t *testing.T) {
			args := &eip_sdk.ListEipArgs{
				Eip:          "*******",
				InstanceType: "BLB",
				InstanceId:   "lb-40dd38d0",
				Status:       string(meta.BindedEIPStatus),
			}

			viper.Set(localMode, testInfo.localMode)
			_, _, err := service.IsEipBinded(mockCtx, args, "bj")
			if !strings.Contains(err.Error(), "InstanceNotFound") {
				t.Errorf("Check IsEipBinded failed. %v", err)
			}
		})
	}
}

func TestListEip(t *testing.T) {
	before()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name      string
		localMode bool
		expectErr error
	}{
		{
			name:      "success",
			localMode: true,
			expectErr: nil,
		},
	}

	for _, testInfo := range testInfos {
		service := NewService(NewOption())
		t.Run(testInfo.name, func(t *testing.T) {
			args := &eip_sdk.ListEipArgs{
				Status: string(meta.BindedEIPStatus),
			}

			viper.Set(localMode, testInfo.localMode)
			_, _ = service.ListEip(mockCtx, args, "gz")

		})
	}
}

func TestReleaseEip(t *testing.T) {
	patches := gomonkey.ApplyFunc(eip.GetDefaultClient, func(_ context.CsmContext, _ string) (
		*eip_sdk.Client, error) {
		return FAKE_CLIENT, nil
	})
	defer patches.Reset()

	var c *eip_sdk.Client
	patches.ApplyMethod(c, "DeleteEip", func(_ *eip_sdk.Client, _ string, _ string) error {
		return nil
	})

	service := &Service{
		option: &Option{
			EipEndpoint: "%s",
		},
	}
	err := service.ReleaseEip(mockCtx, "eip-1", "bj")
	assert.Nil(t, err)
}
