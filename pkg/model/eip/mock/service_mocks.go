// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	eip "github.com/baidubce/bce-sdk-go/services/eip"
	gomock "github.com/golang/mock/gomock"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// BindEip mocks base method.
func (m *MockServiceInterface) BindEip(ctx context.CsmContext, args *eip.BindEipArgs, ip, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindEip", ctx, args, ip, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindEip indicates an expected call of BindEip.
func (mr *MockServiceInterfaceMockRecorder) BindEip(ctx, args, ip, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindEip", reflect.TypeOf((*MockServiceInterface)(nil).BindEip), ctx, args, ip, region)
}

// CreateEip mocks base method.
func (m *MockServiceInterface) CreateEip(ctx context.CsmContext, args *eip.CreateEipArgs, region string) (*eip.CreateEipResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEip", ctx, args, region)
	ret0, _ := ret[0].(*eip.CreateEipResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEip indicates an expected call of CreateEip.
func (mr *MockServiceInterfaceMockRecorder) CreateEip(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEip", reflect.TypeOf((*MockServiceInterface)(nil).CreateEip), ctx, args, region)
}

// IsEipBinded mocks base method.
func (m *MockServiceInterface) IsEipBinded(ctx context.CsmContext, args *eip.ListEipArgs, region string) (bool, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsEipBinded", ctx, args, region)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// IsEipBinded indicates an expected call of IsEipBinded.
func (mr *MockServiceInterfaceMockRecorder) IsEipBinded(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsEipBinded", reflect.TypeOf((*MockServiceInterface)(nil).IsEipBinded), ctx, args, region)
}

// ListEip mocks base method.
func (m *MockServiceInterface) ListEip(ctx context.CsmContext, args *eip.ListEipArgs, region string) (*eip.ListEipResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListEip", ctx, args, region)
	ret0, _ := ret[0].(*eip.ListEipResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEip indicates an expected call of ListEip.
func (mr *MockServiceInterfaceMockRecorder) ListEip(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEip", reflect.TypeOf((*MockServiceInterface)(nil).ListEip), ctx, args, region)
}

// ReleaseEip mocks base method.
func (m *MockServiceInterface) ReleaseEip(ctx context.CsmContext, ip, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseEip", ctx, ip, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReleaseEip indicates an expected call of ReleaseEip.
func (mr *MockServiceInterfaceMockRecorder) ReleaseEip(ctx, ip, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseEip", reflect.TypeOf((*MockServiceInterface)(nil).ReleaseEip), ctx, ip, region)
}

// UnbindEip mocks base method.
func (m *MockServiceInterface) UnbindEip(ctx context.CsmContext, ip, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindEip", ctx, ip, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindEip indicates an expected call of UnbindEip.
func (mr *MockServiceInterfaceMockRecorder) UnbindEip(ctx, ip, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindEip", reflect.TypeOf((*MockServiceInterface)(nil).UnbindEip), ctx, ip, region)
}
