package eip

import (
	"github.com/baidubce/bce-sdk-go/services/eip"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	CreateEip(ctx context.CsmContext, args *eip.CreateEipArgs, region string) (*eip.CreateEipResult, error)
	BindEip(ctx context.CsmContext, args *eip.BindEipArgs, ip, region string) error
	UnbindEip(ctx context.CsmContext, ip, region string) error
	ReleaseEip(ctx context.CsmContext, ip, region string) error
	IsEipBinded(ctx context.CsmContext, args *eip.ListEipArgs, region string) (bool, string, error)
	ListEip(ctx context.CsmContext, args *eip.ListEipArgs, region string) (*eip.ListEipResult, error)
}
