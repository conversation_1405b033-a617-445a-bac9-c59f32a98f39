// Code generated by MockGen. DO NOT EDIT.
// Source: ./interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	instances "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	dbutil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// CountInstancesByRegion mocks base method.
func (m *MockServiceInterface) CountInstancesByRegion(ctx context.CsmContext, region string) (map[string]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountInstancesByRegion", ctx, region)
	ret0, _ := ret[0].(map[string]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountInstancesByRegion indicates an expected call of CountInstancesByRegion.
func (mr *MockServiceInterfaceMockRecorder) CountInstancesByRegion(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountInstancesByRegion", reflect.TypeOf((*MockServiceInterface)(nil).CountInstancesByRegion), ctx, region)
}

// CountInstancesByStatus mocks base method.
func (m *MockServiceInterface) CountInstancesByStatus(ctx context.CsmContext, region string) (map[meta.InstanceStatus]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountInstancesByStatus", ctx, region)
	ret0, _ := ret[0].(map[meta.InstanceStatus]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountInstancesByStatus indicates an expected call of CountInstancesByStatus.
func (mr *MockServiceInterfaceMockRecorder) CountInstancesByStatus(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountInstancesByStatus", reflect.TypeOf((*MockServiceInterface)(nil).CountInstancesByStatus), ctx, region)
}

// DeleteInstanceByInstanceUUID mocks base method.
func (m *MockServiceInterface) DeleteInstanceByInstanceUUID(ctx context.CsmContext, instanceId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstanceByInstanceUUID", ctx, instanceId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstanceByInstanceUUID indicates an expected call of DeleteInstanceByInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) DeleteInstanceByInstanceUUID(ctx, instanceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstanceByInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).DeleteInstanceByInstanceUUID), ctx, instanceId)
}

// GetAccountID mocks base method.
func (m *MockServiceInterface) GetAccountID(ctx context.CsmContext) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountID", ctx)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountID indicates an expected call of GetAccountID.
func (mr *MockServiceInterfaceMockRecorder) GetAccountID(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountID", reflect.TypeOf((*MockServiceInterface)(nil).GetAccountID), ctx)
}

// GetAllInstances mocks base method.
func (m *MockServiceInterface) GetAllInstances(ctx context.CsmContext, region string) ([]meta.Instances, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllInstances", ctx, region)
	ret0, _ := ret[0].([]meta.Instances)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllInstances indicates an expected call of GetAllInstances.
func (mr *MockServiceInterfaceMockRecorder) GetAllInstances(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllInstances", reflect.TypeOf((*MockServiceInterface)(nil).GetAllInstances), ctx, region)
}

// GetInstanceByAccountID mocks base method.
func (m *MockServiceInterface) GetInstanceByAccountID(ctx context.CsmContext, accountID, region string) ([]meta.Instances, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByAccountID", ctx, accountID, region)
	ret0, _ := ret[0].([]meta.Instances)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByAccountID indicates an expected call of GetInstanceByAccountID.
func (mr *MockServiceInterfaceMockRecorder) GetInstanceByAccountID(ctx, accountID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByAccountID", reflect.TypeOf((*MockServiceInterface)(nil).GetInstanceByAccountID), ctx, accountID, region)
}

// GetInstanceByInstanceUUID mocks base method.
func (m *MockServiceInterface) GetInstanceByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*meta.Instances, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByInstanceUUID", ctx, instanceUUID)
	ret0, _ := ret[0].(*meta.Instances)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByInstanceUUID indicates an expected call of GetInstanceByInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) GetInstanceByInstanceUUID(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).GetInstanceByInstanceUUID), ctx, instanceUUID)
}

// GetInstanceClustersCount mocks base method.
func (m *MockServiceInterface) GetInstanceClustersCount(ctx context.CsmContext, instanceUUID string, clusterList *[]meta.Cluster) (int64, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceClustersCount", ctx, instanceUUID, clusterList)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetInstanceClustersCount indicates an expected call of GetInstanceClustersCount.
func (mr *MockServiceInterfaceMockRecorder) GetInstanceClustersCount(ctx, instanceUUID, clusterList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceClustersCount", reflect.TypeOf((*MockServiceInterface)(nil).GetInstanceClustersCount), ctx, instanceUUID, clusterList)
}

// GetInstanceIstiodCluster mocks base method.
func (m *MockServiceInterface) GetInstanceIstiodCluster(ctx context.CsmContext, instanceUUID string) (*meta.Cluster, meta.MeshType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceIstiodCluster", ctx, instanceUUID)
	ret0, _ := ret[0].(*meta.Cluster)
	ret1, _ := ret[1].(meta.MeshType)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetInstanceIstiodCluster indicates an expected call of GetInstanceIstiodCluster.
func (mr *MockServiceInterfaceMockRecorder) GetInstanceIstiodCluster(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceIstiodCluster", reflect.TypeOf((*MockServiceInterface)(nil).GetInstanceIstiodCluster), ctx, instanceUUID)
}

// GetInstanceStatus mocks base method.
func (m *MockServiceInterface) GetInstanceStatus(ctx context.CsmContext, instanceUUID string, istiodCluster *meta.Cluster, meshType meta.MeshType) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceStatus", ctx, instanceUUID, istiodCluster, meshType)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceStatus indicates an expected call of GetInstanceStatus.
func (mr *MockServiceInterfaceMockRecorder) GetInstanceStatus(ctx, instanceUUID, istiodCluster, meshType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceStatus", reflect.TypeOf((*MockServiceInterface)(nil).GetInstanceStatus), ctx, instanceUUID, istiodCluster, meshType)
}

// GetInstancesByRegion mocks base method.
func (m *MockServiceInterface) GetInstancesByRegion(ctx context.CsmContext, region string) ([]meta.Instances, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesByRegion", ctx, region)
	ret0, _ := ret[0].([]meta.Instances)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesByRegion indicates an expected call of GetInstancesByRegion.
func (mr *MockServiceInterfaceMockRecorder) GetInstancesByRegion(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesByRegion", reflect.TypeOf((*MockServiceInterface)(nil).GetInstancesByRegion), ctx, region)
}

// GetInstancesByUser mocks base method.
func (m *MockServiceInterface) GetInstancesByUser(ctx context.CsmContext) (*[]meta.Instances, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesByUser", ctx)
	ret0, _ := ret[0].(*[]meta.Instances)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesByUser indicates an expected call of GetInstancesByUser.
func (mr *MockServiceInterfaceMockRecorder) GetInstancesByUser(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesByUser", reflect.TypeOf((*MockServiceInterface)(nil).GetInstancesByUser), ctx)
}

// GetInstancesList mocks base method.
func (m *MockServiceInterface) GetInstancesList(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*[]meta.Instances, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesList", ctx, mrp)
	ret0, _ := ret[0].(*[]meta.Instances)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesList indicates an expected call of GetInstancesList.
func (mr *MockServiceInterfaceMockRecorder) GetInstancesList(ctx, mrp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesList", reflect.TypeOf((*MockServiceInterface)(nil).GetInstancesList), ctx, mrp)
}

// NewInstance mocks base method.
func (m *MockServiceInterface) NewInstance(ctx context.CsmContext, instances *meta.Instances) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewInstance", ctx, instances)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewInstance indicates an expected call of NewInstance.
func (mr *MockServiceInterfaceMockRecorder) NewInstance(ctx, instances interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewInstance", reflect.TypeOf((*MockServiceInterface)(nil).NewInstance), ctx, instances)
}

// UpdateInstance mocks base method.
func (m *MockServiceInterface) UpdateInstance(ctx context.CsmContext, old, update *meta.Instances) (*meta.Instances, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstance", ctx, old, update)
	ret0, _ := ret[0].(*meta.Instances)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstance indicates an expected call of UpdateInstance.
func (mr *MockServiceInterfaceMockRecorder) UpdateInstance(ctx, old, update interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstance", reflect.TypeOf((*MockServiceInterface)(nil).UpdateInstance), ctx, old, update)
}

// WithTx mocks base method.
func (m *MockServiceInterface) WithTx(tx *dbutil.DB) instances.ServiceInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(instances.ServiceInterface)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockServiceInterfaceMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockServiceInterface)(nil).WithTx), tx)
}
