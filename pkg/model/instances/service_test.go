package instances

import (
	"context"
	"reflect"
	"testing"

	cce_v1 "github.com/baidubce/bce-sdk-go/services/cce"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	mockDao "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao/mocks"
	mockCluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	mockCceService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce/mock"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	sdkIAM "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

var (
	mockDB     *gorm.DB
	mockCtx, _ = ctxCsm.NewCsmContextMock()
	accountId  = "123"
	user       = &sdkIAM.User{
		ID:   "1",
		Name: "test-user",
		Domain: sdkIAM.UserDomain{
			ID:   accountId,
			Name: "aaaa",
		},
	}
)

func TestGetInstancesList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInstanceIDs := []string{"inst-0"}
	testInstanceNames := []string{"inst-a"}

	tests := []struct {
		name      string
		mrp       *meta.CsmMeshRequestParams
		instList  []meta.Instances
		expectNum int64
	}{
		{
			name: "test_getAllClusterByRegion",
			mrp:  &meta.CsmMeshRequestParams{},
			instList: []meta.Instances{
				{
					InstanceUUID: testInstanceIDs[0],
					InstanceName: testInstanceNames[0],
				},
			},
			expectNum: 1,
		},
	}
	for _, tt := range tests {
		mockDaoModel := mockDao.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}

		mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.instList, nil)

		t.Run(tt.name, func(t *testing.T) {
			res, _ := service.GetInstancesList(mockCtx, tt.mrp)
			assert.Equal(t, int64(len(*res)), tt.expectNum)
		})
	}
}

func TestGetInstancesByRegion(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInstanceIDs := []string{"inst-0"}
	testInstanceNames := []string{"inst-a"}

	tests := []struct {
		name      string
		region    string
		instList  []meta.Instances
		expectNum int64
	}{
		{
			name:   "test_getAllClusterByRegion",
			region: "bj",
			instList: []meta.Instances{
				{
					InstanceUUID: testInstanceIDs[0],
					InstanceName: testInstanceNames[0],
				},
			},
			expectNum: 1,
		},
	}
	for _, tt := range tests {
		mockDaoModel := mockDao.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}

		mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.instList, nil)

		t.Run(tt.name, func(t *testing.T) {
			res, _ := service.GetInstancesByRegion(mockCtx, tt.region)
			assert.Equal(t, int64(len(res)), tt.expectNum)
		})
	}
}

func TestGetInstancesByUser(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInstanceIDs := []string{"inst-0"}
	testInstanceNames := []string{"inst-a"}

	tests := []struct {
		name      string
		region    string
		instList  []meta.Instances
		expectNum int
	}{
		{
			name: "test_getAllClusterByUser",
			instList: []meta.Instances{
				{
					InstanceUUID: testInstanceIDs[0],
					InstanceName: testInstanceNames[0],
				},
			},
			expectNum: 1,
		},
	}
	for _, tt := range tests {
		mockDaoModel := mockDao.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}

		mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.instList, nil)

		t.Run(tt.name, func(t *testing.T) {
			res, _ := service.GetInstancesByUser(mockCtx)
			assert.Equal(t, len(*res), tt.expectNum)
		})
	}
}

func TestGetInstanceClustersCount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInfos := []struct {
		name              string
		testClustersList  []meta.Cluster
		testClusterDetail cce_v1.GetClusterResult
		expectRunningCnt  int64
		expectTotalCnt    int64
		expectErr         error
	}{
		{
			name: "correct-GetInstanceClustersCount-0/1",
			testClustersList: []meta.Cluster{
				{
					ClusterType: "external",
				},
				{
					ClusterType: "remote",
					ClusterUUID: "123",
					Region:      "bj",
				},
			},
			testClusterDetail: cce_v1.GetClusterResult{
				Status: "deploying",
			},
			expectRunningCnt: 0,
			expectTotalCnt:   1,
			expectErr:        nil,
		},
		{
			name: "correct-GetInstanceClustersCount-1/1",
			testClustersList: []meta.Cluster{
				{
					ClusterType: "external",
				},
				{
					ClusterType: "remote",
					ClusterUUID: "123",
					Region:      "bj",
				},
			},
			testClusterDetail: cce_v1.GetClusterResult{
				Status: "running",
			},
			expectRunningCnt: 1,
			expectTotalCnt:   1,
			expectErr:        nil,
		},
	}
	for _, testInfo := range testInfos {
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockCceService := mockCceService.NewMockClientInterface(ctrl)

		service := &Service{
			opt:          NewOption(mockDB),
			clusterModel: mockClusterModel,
			cceService:   mockCceService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockClusterModel.EXPECT().GetAllClusterByInstanceUUID(mockCtx, gomock.Any()).Return(&testInfo.testClustersList, nil)
			mockCceService.EXPECT().GetCCECluster(mockCtx, gomock.Any(), gomock.Any()).Return(&testInfo.testClusterDetail, nil)
			runningCount, totalCount, _ := service.GetInstanceClustersCount(mockCtx, "abc", nil)
			assert.Equal(t, testInfo.expectRunningCnt, runningCount)
			assert.Equal(t, testInfo.expectTotalCnt, totalCount)
		})
	}
}

func TestGetInstanceStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name          string
		instanceInfo  *meta.Instances
		testPod       *v1.Pod
		istiodCluster *meta.Cluster
		expectErr     error
	}{
		{
			name: "empty-istiopods",
			instanceInfo: &meta.Instances{
				InstanceType: "external",
			},
			testPod: nil,
			istiodCluster: &meta.Cluster{
				ClusterUUID:           "id-1",
				IstioInstallNamespace: "ns-1",
				Region:                "bj",
			},
			expectErr: nil,
		},
		{
			name: "failed-istiopods",
			instanceInfo: &meta.Instances{
				InstanceType: "external",
			},
			testPod: &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{"app": "istiod"},
				},
				Status: v1.PodStatus{
					Phase:  v1.PodPhase("Failed"),
					Reason: "Evicted",
				},
			},
			istiodCluster: &meta.Cluster{
				ClusterUUID:           "id-1",
				IstioInstallNamespace: "ns-1",
				Region:                "bj",
			},
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		mockDaoModel := mockDao.NewMockBaseInterface(ctrl)
		mockClusterModel := mockCluster.NewMockServiceInterface(ctrl)
		mockCceService := mockCceService.NewMockClientInterface(ctrl)

		service := &Service{
			opt:          NewOption(mockDB),
			dao:          mockDaoModel,
			clusterModel: mockClusterModel,
			cceService:   mockCceService,
		}
		t.Run(testInfo.name, func(t *testing.T) {
			mockCtx.Set("User", user)
			fakeClient := kube.NewFakeClient()
			if testInfo.testPod != nil {
				_, _ = fakeClient.Kube().CoreV1().Pods(testInfo.istiodCluster.IstioInstallNamespace).Create(context.TODO(), testInfo.testPod, metav1.CreateOptions{})
			}
			mockCceService.EXPECT().NewClient(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(fakeClient, nil)
			mockDaoModel.EXPECT().LoadWithWhere(mockCtx, gomock.Any()).Return(testInfo.instanceInfo, nil)
			mockClusterModel.EXPECT().GetIstiodCluster(mockCtx, gomock.Any(), gomock.Any()).Return(testInfo.istiodCluster, nil)
			res, err := service.GetInstanceStatus(mockCtx, "abc", nil, "")
			assert.Equal(t, nil, err)
			if testInfo.testPod == nil {
				assert.Equal(t, constants.SmiDeploying, res)
			} else {
				assert.Equal(t, constants.SmiRunning, res)
			}
		})
	}
}

func TestService_GetAccountId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	tests := []struct {
		name           string
		wantStringList []string
		wantErr        bool
	}{
		{
			name:           "test-getAccountId",
			wantStringList: nil,
			wantErr:        false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := mockDao.NewMockBaseInterface(ctrl)
			service := &Service{
				opt: NewOption(mockDB),
				dao: mockDaoModel,
			}
			mockDaoModel.EXPECT().CountGroupBy(mockCtx, gomock.Any(), gomock.Any()).Return(nil, nil)
			gotStringList, err := service.GetAccountID(mockCtx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotStringList, tt.wantStringList) {
				t.Errorf("GetAccountId() gotStringList = %v, want %v", gotStringList, tt.wantStringList)
			}
		})
	}
}

func TestService_GetInstanceByAccountId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInstanceIDs := []string{"inst-0"}
	testInstanceNames := []string{"inst-a"}
	tests := []struct {
		name    string
		want    []meta.Instances
		wantErr bool
	}{
		{
			name: "test-getInstanceByAccountId",
			want: []meta.Instances{
				{
					InstanceUUID: testInstanceIDs[0],
					InstanceName: testInstanceNames[0],
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := mockDao.NewMockBaseInterface(ctrl)
			service := &Service{
				opt: NewOption(mockDB),
				dao: mockDaoModel,
			}
			mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.want, nil)

			got, err := service.GetInstanceByAccountID(mockCtx, accountId, "bj")
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInstanceByAccountId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetInstanceByAccountId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetAllInstances(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInstanceIDs := []string{"inst-0"}
	testInstanceNames := []string{"inst-a"}
	tests := []struct {
		name    string
		want    []meta.Instances
		wantErr bool
	}{
		{
			name: "test-GetAllInstances",
			want: []meta.Instances{
				{
					InstanceUUID: testInstanceIDs[0],
					InstanceName: testInstanceNames[0],
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := mockDao.NewMockBaseInterface(ctrl)
			s := &Service{
				opt: NewOption(mockDB),
				dao: mockDaoModel,
			}
			mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.want, nil)
			got, err := s.GetAllInstances(mockCtx, "bj")
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllInstances() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllInstances() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetInstanceByInstanceUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInstanceIDs := []string{"inst-0"}
	testInstanceNames := []string{"inst-a"}
	tests := []struct {
		name    string
		want    *meta.Instances
		wantErr bool
	}{
		{
			name: "test-getInstanceByAccountId",
			want: &meta.Instances{
				InstanceUUID: testInstanceIDs[0],
				InstanceName: testInstanceNames[0],
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCtx.Set("User", user)
			mockDaoModel := mockDao.NewMockBaseInterface(ctrl)
			service := &Service{
				opt: NewOption(mockDB),
				dao: mockDaoModel,
			}
			mockDaoModel.EXPECT().LoadWithWhere(mockCtx, gomock.Any()).Return(tt.want, nil)

			got, err := service.GetInstanceByInstanceUUID(mockCtx, testInstanceIDs[0])
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInstanceByInstanceUUID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetInstanceByInstanceUUID() got = %v, want %v", got, tt.want)
			}
		})
	}
}
