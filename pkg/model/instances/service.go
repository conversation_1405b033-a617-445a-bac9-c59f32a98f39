package instances

import (
	"context"
	"strings"
	"sync"
	"sync/atomic"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/instances/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/cce"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Service struct {
	opt          *Option
	dao          model.InstancesDaoInterface
	clusterModel cluster.ServiceInterface
	cceService   cce.ClientInterface
}

func NewInstancesService(option *Option) *Service {
	return &Service{
		opt:          option,
		dao:          dao.NewInstancesDao(option.DB),
		clusterModel: cluster.NewClusterService(cluster.NewOption(option.DB.DB)),
		cceService:   cce.NewClientService(),
	}
}

// WithTx 服务网格实例 tx
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	nOpt := *s.opt
	nOpt.DB = tx
	return NewInstancesService(&nOpt)
}

// NewInstance 创建服务网格实例
func (s *Service) NewInstance(ctx csmContext.CsmContext, instances *meta.Instances) error {
	if err := s.dao.Save(ctx, instances); err != nil {
		return err
	}
	return nil
}

// UpdateInstance 更新实例
func (s *Service) UpdateInstance(ctx csmContext.CsmContext, old, update *meta.Instances) (*meta.Instances, error) {
	if old == nil {
		return nil, csmErr.NewMissingParametersException("Instances where is nil.")
	}
	if len(old.InstanceUUID) == 0 {
		return nil, csmErr.NewMissingParametersException("InstanceUUID required.")
	}
	if err := s.dao.Update(ctx, old, update); err != nil {
		return nil, err
	}
	return s.GetInstanceByInstanceUUID(ctx, old.InstanceUUID)
}

// DeleteInstanceByInstanceUUID 根据 instanceUUID 删除服务网格实例数据
func (s *Service) DeleteInstanceByInstanceUUID(ctx csmContext.CsmContext, instanceUUID string) error {
	where := &meta.Instances{
		InstanceUUID: instanceUUID,
	}
	return s.dao.BatchDelete(ctx, where)
}

// GetInstancesList 获取全部网格实例列表
func (s *Service) GetInstancesList(ctx csmContext.CsmContext, mrp *meta.CsmMeshRequestParams) (
	*[]meta.Instances, error) {

	search := &meta.Instances{}
	if mrp.Keyword != "" {
		switch mrp.KeywordType {
		case constants.InstanceName:
			search.InstanceName = mrp.Keyword
		case constants.InstanceId:
			search.InstanceUUID = mrp.Keyword
		}
	}
	where := &meta.Instances{
		Deleted:   csm.Int(0),
		AccountId: mrp.AccountID,
		Region:    mrp.Region,
	}
	not := &meta.Instances{}

	// basePageInfo, meshInstList, err := s.dao.ListPage(ctx, search, where, mrp.OrderBy, order, mrp.PageSize, mrp.PageNo)
	meshInstList, err := s.dao.ListAll(ctx, search, where, not)
	if err != nil {
		return nil, err
	}

	return meshInstList.(*[]meta.Instances), nil
}

func (s *Service) GetInstancesByUser(ctx csmContext.CsmContext) (*[]meta.Instances, error) {
	accountId, _ := iam.GetAccountId(ctx)
	where := &meta.Instances{
		AccountId: accountId,
		Deleted:   csm.Int(0),
	}
	// bugfix CNAP 新建实例报错
	not := &meta.Instances{}

	ins, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	// 类型转换
	cl := ins.(*[]meta.Instances)

	return cl, nil
}

// GetInstanceStatus 根据Istio Pods获取网格实例状态
func (s *Service) GetInstanceStatus(ctx csmContext.CsmContext, instanceUUID string, istiodCluster *meta.Cluster,
	meshType meta.MeshType) (string, error) {
	cxt, cancel := context.WithTimeout(context.Background(), constants.KubeTimeout*2)
	defer cancel()

	instanceStatus := constants.SmiAbnormal
	if istiodCluster == nil {
		c, t, err := s.GetInstanceIstiodCluster(ctx, instanceUUID)
		if err != nil {
			return instanceStatus, err
		}
		istiodCluster = c
		meshType = t
	}
	client, err := s.cceService.NewClient(ctx, istiodCluster.Region, istiodCluster.ClusterUUID, meshType)
	if err != nil {
		return instanceStatus, err
	}
	istiodPods, err := client.Kube().CoreV1().Pods(istiodCluster.IstioInstallNamespace).List(
		cxt, metav1.ListOptions{LabelSelector: constants.IstioLabelSelector})
	if err != nil {
		return instanceStatus, err
	}
	if len(istiodPods.Items) == 0 {
		instanceStatus = constants.SmiDeploying
	} else {
		instanceStatus = constants.SmiRunning
		for _, pod := range istiodPods.Items {
			status := pod.Status.Phase
			if status == v1.PodRunning {
				continue
			}
			if status == v1.PodPending {
				instanceStatus = constants.SmiDeploying
			} else {
				reason := strings.ToLower(pod.Status.Reason)
				if reason == constants.SmiEvicted {
					continue
				}
				instanceStatus = constants.SmiAbnormal
			}
			break
		}
	}
	return instanceStatus, nil
}

// GetInstanceByInstanceUUID 根据 instanceUUID 获取 instance
func (s *Service) GetInstanceByInstanceUUID(ctx csmContext.CsmContext, instanceUUID string) (*meta.Instances, error) {
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return nil, csmErr.NewUnauthorizedException("user is nil", err)
	}
	where := &meta.Instances{
		Deleted:      csm.Int(0),
		AccountId:    accountId,
		InstanceUUID: instanceUUID,
	}
	instanceInfo, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}
	if instanceInfo == nil {
		return nil, nil
	}
	return instanceInfo.(*meta.Instances), nil
}

// GetInstanceClustersCount 访问CCE，获取某实例对应的集群运行信息
func (s *Service) GetInstanceClustersCount(ctx csmContext.CsmContext, instanceUUID string, clusterList *[]meta.Cluster) (int64, int64, error) {
	// 访问数据库
	if clusterList == nil {
		cl, err := s.clusterModel.GetAllClusterByInstanceUUID(ctx, instanceUUID)
		if err != nil {
			return 0, 0, err
		}
		clusterList = cl
	}
	// 访问CCE，获取网格实例下的集群运行状态
	runningCluster, clusterCount := int64(0), int64(0)
	var wg sync.WaitGroup
	for _, c := range *clusterList {
		if c.ClusterType == string(meta.ClusterTypeExternal) {
			continue
		}
		clusterCount++

		wg.Add(1)
		go func() {
			defer wg.Done()
			cceRes, err := s.cceService.GetCCECluster(ctx, c.Region, c.ClusterUUID)
			if err == nil {
				if strings.ToLower(string(cceRes.Status)) == constants.CluRunning {
					atomic.AddInt64(&runningCluster, 1)
				}
			}
		}()
	}
	wg.Wait()
	return runningCluster, clusterCount, nil
}

func (s *Service) CountInstancesByRegion(ctx csmContext.CsmContext, region string) (map[string]int64, error) {
	accountID, _ := iam.GetAccountId(ctx)
	where := &meta.Instances{
		AccountId: accountID,
		Deleted:   csm.Int(0),
	}

	if !strings.EqualFold(region, reg.GlobalRegion) {
		where.Region = region
	}
	results, err := s.dao.CountGroupBy(ctx, where, constants.InstanceRegionField)
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (s *Service) CountInstancesByStatus(ctx csmContext.CsmContext, region string) (
	map[meta.InstanceStatus]int64, error) {
	accountID, _ := iam.GetAccountId(ctx)
	where := &meta.Instances{
		AccountId: accountID,
		Deleted:   csm.Int(0),
	}

	if !strings.EqualFold(region, constants.GlobalRegion) {
		where.Region = region
	}
	results, err := s.dao.CountGroupBy(ctx, where, constants.InstanceStatusField)
	if err != nil {
		return nil, err
	}

	rs := make(map[meta.InstanceStatus]int64)
	for k, v := range results {
		rs[meta.InstanceStatus(k)] = v
	}
	return rs, nil
}

func (s *Service) GetInstancesByRegion(ctx csmContext.CsmContext, region string) ([]meta.Instances, error) {
	accountID, _ := iam.GetAccountId(ctx)
	where := &meta.Instances{
		AccountId: accountID,
		Deleted:   csm.Int(0),
	}
	if !strings.EqualFold(region, constants.GlobalRegion) {
		where.Region = region
	}
	not := &meta.Instances{}
	instances, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	is := instances.(*[]meta.Instances)
	return *is, nil
}

func (s *Service) GetInstanceIstiodCluster(ctx csmContext.CsmContext, instanceUUID string) (*meta.Cluster, meta.MeshType, error) {
	instanceInfo, err := s.GetInstanceByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return nil, "", err
	}
	cluster, err := s.clusterModel.GetIstiodCluster(ctx, instanceUUID, instanceInfo.InstanceType)
	if err != nil {
		return nil, "", err
	}
	return cluster, meta.MeshType(instanceInfo.InstanceType), nil
}

func (s *Service) GetAccountID(ctx csmContext.CsmContext) (stringList []string, err error) {
	where := &meta.Instances{
		Deleted: csm.Int(0),
	}
	results, err := s.dao.CountGroupBy(ctx, where, constants.AccountID)
	if err != nil {
		return nil, err
	}
	for k := range results {
		stringList = append(stringList, k)
	}
	return stringList, err
}

func (s *Service) GetInstanceByAccountID(ctx csmContext.CsmContext, accountID string, region string) ([]meta.Instances, error) {
	where := &meta.Instances{
		AccountId: accountID,
		Region:    region,
		Deleted:   csm.Int(0),
	}
	not := &meta.Instances{}
	ins, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	cl := ins.(*[]meta.Instances)
	return *cl, nil
}

func (s *Service) GetAllInstances(ctx csmContext.CsmContext, region string) ([]meta.Instances, error) {
	where := &meta.Instances{
		Region:  region,
		Deleted: csm.Int(0),
	}
	not := &meta.Instances{}
	ins, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	cl := ins.(*[]meta.Instances)
	return *cl, nil
}
