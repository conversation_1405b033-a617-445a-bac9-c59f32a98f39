package instances

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceInterface interface {
	WithTx(tx *dbutil.DB) ServiceInterface
	NewInstance(ctx context.CsmContext, instances *meta.Instances) error
	DeleteInstanceByInstanceUUID(ctx context.CsmContext, instanceId string) error
	GetInstanceByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*meta.Instances, error)
	UpdateInstance(ctx context.CsmContext, old, update *meta.Instances) (*meta.Instances, error)
	GetInstancesList(ctx context.CsmContext, mrp *meta.CsmMeshRequestParams) (*[]meta.Instances, error)
	GetInstancesByUser(ctx context.CsmContext) (*[]meta.Instances, error)
	GetInstanceStatus(ctx context.CsmContext, instanceUUID string, istiodCluster *meta.Cluster, meshType meta.MeshType) (string, error)
	GetInstanceClustersCount(ctx context.CsmContext, instanceUUID string, clusterList *[]meta.Cluster) (int64, int64, error)

	CountInstancesByRegion(ctx context.CsmContext, region string) (map[string]int64, error)
	CountInstancesByStatus(ctx context.CsmContext, region string) (map[meta.InstanceStatus]int64, error)
	GetInstancesByRegion(ctx context.CsmContext, region string) ([]meta.Instances, error)

	GetInstanceIstiodCluster(ctx context.CsmContext, instanceUUID string) (*meta.Cluster, meta.MeshType, error)

	GetAccountID(ctx context.CsmContext) ([]string, error)
	GetInstanceByAccountID(ctx context.CsmContext, accountID string, region string) ([]meta.Instances, error)
	GetAllInstances(ctx context.CsmContext, region string) ([]meta.Instances, error)
}
