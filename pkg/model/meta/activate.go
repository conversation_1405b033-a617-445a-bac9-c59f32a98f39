package meta

import "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"

type Activate struct {
	dbutil.BaseModel

	//nolint
	AccountID string `gorm:"column:account_id" json:"accountId" dbutil:"orderable" valid:"required"`
	//nolint
	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable"`
}

func (a *Activate) TableName() string {
	return "t_mse_activate"
}
