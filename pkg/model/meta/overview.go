package meta

import "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"

type InstanceStatus string

const (
	Running      InstanceStatus = "running"
	Changing     InstanceStatus = "changing"
	Deploying    InstanceStatus = "deploying"
	Removing     InstanceStatus = "removing"
	DeployFailed InstanceStatus = "deployFailed"
	Exception    InstanceStatus = "exception"
	Unknown      InstanceStatus = "unknown"
)

func ConvertInstanceStatus(is string) InstanceStatus {
	switch is {
	case constants.SmiRunning:
		return Running
	case constants.SmiDeploying:
		return Changing
	case constants.SmiAbnormal:
		return Exception
	default:
		return Unknown
	}
}

type InstancesOverview struct {
	GroupByRegion map[string]int64         `json:"groupByRegion"`
	GroupByStatus map[InstanceStatus]int64 `json:"groupByStatus"`
}

type SidecarsOverview struct {
	InstanceId   string `json:"instanceId"`
	InstanceName string `json:"instanceName"`
	Num          int    `json:"num"`
}

type ServicesOverview struct {
	AllOf  int `json:"allOf"`
	AnyOf  int `json:"anyOf"`
	NoneOf int `json:"noneOf"`
}

type ClustersOverview struct {
	RunningNum int `json:"runningNum"`
	Total      int `json:"total"`
}

type InstanceDetailOverview struct {
	InstanceId   string         `json:"instanceId"`
	InstanceName string         `json:"instanceName"`
	Status       InstanceStatus `json:"status"`
	Region       string         `json:"region"`
	SidecarNum   int            `json:"sidecarNum"`

	ServicesOverview ServicesOverview `json:"servicesOverview"`
	ClustersOverview ClustersOverview `json:"clustersOverview"`
}

func GetAllInstanceStatus() []InstanceStatus {
	return []InstanceStatus{Running, Changing, Exception}
}
