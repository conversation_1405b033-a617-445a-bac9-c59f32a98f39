package meta

import (
	"strings"
	"time"
)

type Kind string

const (
	// Extensions
	WasmPlugin Kind = "WasmPlugin"

	// Networking
	DestinationRule Kind = "DestinationRule"
	EnvoyFilter     Kind = "EnvoyFilter"
	Gateway         Kind = "Gateway"
	ServiceEntry    Kind = "ServiceEntry"
	Sidecar         Kind = "Sidecar"
	VirtualService  Kind = "VirtualService"
	WorkloadEntry   Kind = "WorkloadEntry"
	WorkloadGroup   Kind = "WorkloadGroup"

	// Security
	AuthorizationPolicy   Kind = "AuthorizationPolicy"
	PeerAuthentication    Kind = "PeerAuthentication"
	RequestAuthentication Kind = "RequestAuthentication"

	// Telemetry
	Telemetry Kind = "Telemetry"

	// aeraki
	MetaRouter          = "MetaRouter"
	ApplicationProtocol = "ApplicationProtocol"
	GroupMetaprotocol   = "metaprotocol.aeraki.io"

	DubboAuthorizationPolicy         = "DubboAuthorizationPolicy"
	GroupDubbo                       = "dubbo.aeraki.io"
	ResourceDubboAuthorizationPolicy = "dubboauthorizationpolicies"

	RedisService     = "RedisService"
	RedisDestination = "RedisDestination"
	GroupRedis       = "redis.aeraki.io"
)

func GetRegisteredCrdKind() []Kind {
	return []Kind{
		DestinationRule,
		EnvoyFilter,
		Gateway,
		ServiceEntry,
		Sidecar,
		VirtualService,
		WorkloadEntry,
		WorkloadGroup,

		AuthorizationPolicy,
		PeerAuthentication,
		RequestAuthentication,

		Telemetry,

		WasmPlugin,
	}
}

type Crd struct {
	InstanceUUID string
	Namespace    string
	Name         string
	Kind         string
	UpdatedAt    time.Time
	Content      string
}

type BatchCreateCrdInfo struct {
	FailedCrdMessage string `json:"failedCrdMessage,omitempty"`
	SuccessCrdList   []Crd  `json:"successCrdList,omitempty"`
}

// SplitYaml 根据 "---" 分割yaml
func SplitYaml(crdContent string) []string {
	var listYaml []string
	var b strings.Builder
	for _, s := range strings.Split(crdContent, "\n") {
		if strings.HasPrefix(s, "#") {
			continue
		}
		if strings.HasPrefix(s, "---") {
			// yaml separator
			listYaml = append(listYaml, b.String())
			b.Reset()
		} else {
			b.WriteString(s)
			b.WriteString("\n")
		}
	}
	listYaml = append(listYaml, b.String())
	return listYaml
}

func IsRegisteredCrdKindForIstio(crdKind Kind) bool {
	for _, kind := range GetRegisteredCrdKind() {
		// TODO 待优化忽略大小写
		if crdKind == kind {
			return true
		}
	}
	return false
}
