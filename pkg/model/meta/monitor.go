package meta

type CPromScrapeJobItemResult struct {
	Items []CPromScrapeJobItem `json:"items"`
}

type CPromScrapeJobItem struct {
	Spec *ScrapeJobMetadata `json:"spec"`
}

type ScrapeJobMetadata struct {
	AgentID       string `json:"agentID"`
	ScrapeJobName string `json:"scrapeJobName"`
	ScrapeJobID   string `json:"scrapeJobID"`
}

type CPromItemResult struct {
	Items []CPromItem `json:"items"`
}

type CPromItem struct {
	MonitorGrafanaId   string             `json:"monitorGrafanaId"`
	MonitorGrafanaName string             `json:"monitorGrafanaName"`
	Metadata           *CPromItemMetadata `json:"metadata"`
	Spec               *CPromItemSpec     `json:"spec"`
	Status             *CPromItemStatus   `json:"status"`
}

type CPromItemMetadata struct {
	Labels            map[string]string `json:"labels"`
	Name              string            `json:"name"`
	CreationTimestamp string            `json:"creationTimestamp"`
}

type CPromItemSpec struct {
	InstanceID      string                    `json:"instanceID"`
	InstanceName    string                    `json:"instanceName"`
	Region          string                    `json:"region"`
	VmClusterConfig *CPromSpecVmClusterConfig `json:"vmClusterConfig"`
}

type CPromSpecVmClusterConfig struct {
	RetentionPeriod string `json:"retentionPeriod"`
}

type CPromItemStatus struct {
	Message string `json:"message"`
	Phase   string `json:"phase"`
}

type CPromInstancesResult struct {
	Items []CPromInstancesItem `json:"items"`
}

type CPromInstance struct {
	InstanceName string
	InstanceId   string
	Region       string
}

type CPromResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Result  interface{} `json:"result"`
}

type CPromInstancesItem struct {
	Spec *CPromInstancesItemSpec `json:"spec"`
}

type CPromInstancesItemSpec struct {
	InstanceID   string `json:"instanceID"`
	InstanceName string `json:"instanceName"`
	Region       string `json:"region"`
}

type CPromAgentsResult struct {
	Items []CPromAgent `json:"items"`
}

type CPromAgent struct {
	AgentID     string               `json:"agentID"`
	AgentStatus string               `json:"agentStatus"`
	Cluster     *CPromBindingCluster `json:"cluster"`
}

type CPromBindingCluster struct {
	Spec *CPromBindingClusterSpec `json:"spec"`
}

type CPromBindingClusterSpec struct {
	ClusterID string `json:"clusterID"`
	VpcID     string `json:"vpcID"`
}

type CPromScrapeJobBody struct {
	Spec *CPromScrapeJobBodySpec `json:"spec"`
}

type CPromScrapeJobBodySpec struct {
	Content string `json:"content"`
}

type CPromJobResult struct {
	ScrapeJobID string `json:"scrapeJobID"`
}

type CPromGatewayInfo struct {
	ID          string `json:"id"`
	Region      string `json:"region"`
	AgentID     string `json:"agentID"`
	ScrapeJobID string `json:"scrapeJobID"`
}

type CPromHostingIstioInfo struct {
	ID          string `json:"id"`
	Region      string `json:"region"`
	AgentID     string `json:"agentID"`
	ScrapeJobID string `json:"scrapeJobID"`
}
