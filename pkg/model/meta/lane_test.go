package meta

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

var (
	instanceUUID       = "test-InstanceUUID"
	groupID            = "test-2"
	groupName          = "test-name"
	traceHeader        = "trace-id"
	routeHeader        = "version"
	clusterID          = "clusterID"
	clusterRegion      = "bj"
	clusterName        = "name"
	namespace          = "test-namespace"
	serviceName        = "test-1"
	labelSelectorKey   = "test-key"
	labelSelectorValue = "test-value"
)

func TestToLaneGroupsModel(t *testing.T) {
	lg := buildLaneGroupParams()
	_, err := lg.ToLaneGroupsModel()
	assert.NoError(t, err)
}

func TestToLanesModel(t *testing.T) {
	lg := buildLaneParams()
	_, err := lg.ToLanesModel()
	assert.NoError(t, err)
}

func buildLaneGroupParams() *LaneGroupParams {
	return &LaneGroupParams{
		InstanceUUID: instanceUUID,
		GroupName:    groupName,
		TraceHeader:  traceHeader,
		RouteHeader:  routeHeader,
		ServiceList: []ServiceListParams{
			{
				ClusterID:     clusterID,
				ClusterRegion: clusterRegion,
				ClusterName:   clusterName,
				Namespace:     namespace,
				ServiceName:   serviceName,
			},
		},
	}
}

func buildLaneParams() *LaneParams {
	return &LaneParams{
		InstanceUUID:       instanceUUID,
		GroupID:            groupID,
		LabelSelectorKey:   labelSelectorKey,
		LabelSelectorValue: labelSelectorValue,
		ServiceList: []ServiceListParams{
			{
				ClusterID:     clusterID,
				ClusterRegion: clusterRegion,
				ClusterName:   clusterName,
				Namespace:     namespace,
				ServiceName:   serviceName,
			},
		},
	}
}
