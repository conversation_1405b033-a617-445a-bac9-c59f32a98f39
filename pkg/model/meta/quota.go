package meta

import v1 "k8s.io/api/core/v1"

// SidecarQuota 查询sidecar配额返回参数
type SidecarQuota struct {
	CpuQuota    CpuQuota    `json:"cpuQuota"`
	MemoryQuota MemoryQuota `json:"memoryQuota"`
}
type CpuQuota struct {
	Request int    `json:"request"`
	Limit   int    `json:"limit"`
	Unit    string `json:"unit"`
}
type MemoryQuota struct {
	Request int    `json:"request"`
	Limit   int    `json:"limit"`
	Unit    string `json:"unit"`
}

type Proxy struct {
	Resources v1.ResourceRequirements `json:"resources"`
}

type Global struct {
	Proxy Proxy `json:"proxy"`
}

type Values struct {
	Global Global `json:"global"`
}

// QuotaParam 编辑sidecar配额参数
type QuotaParam struct {
	InstanceUUID string `param:"instanceUUID"`
	SidecarQuota
}

// InjectionParam sidecar自动注入参数
type InjectionParam struct {
	InstanceUUID string `param:"instanceUUID"`
	Namespace    string `param:"namespace"`
	ClusterUUID  string `query:"clusterUuid"`
	Enabled      string `query:"enabled"`
}

// DefaultSidecarQuota设置sidecar配额默认值
func DefaultSidecarQuota() *SidecarQuota {
	cpuQuota := &CpuQuota{
		Limit:   0,
		Request: 0,
		Unit:    "",
	}
	memoryQuota := &MemoryQuota{
		Limit:   0,
		Request: 0,
		Unit:    "",
	}
	return &SidecarQuota{
		CpuQuota:    *cpuQuota,
		MemoryQuota: *memoryQuota,
	}
}
