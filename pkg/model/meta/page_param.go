package meta

// PageParam 分页请求参数
type PageParam struct {
	PageSize    int    `query:"pageSize"`
	PageNo      int    `query:"pageNo"`
	Keyword     string `query:"keyword"`
	KeywordType string `query:"keywordType"`
	Order       string `query:"order"`
	OrderBy     string `query:"orderBy"`
}

type RequestWithPage struct {
	PageParam
}

// NewRequestParams 给分页请求参数默认值
func NewRequestParams() *PageParam {
	return &PageParam{
		PageSize:    10,
		PageNo:      1,
		Keyword:     "",
		KeywordType: "",
		Order:       "desc",
		OrderBy:     "createTime",
	}
}
