package meta

import (
	"testing"

	"github.com/magiconair/properties/assert"
)

var (
	content = "apiVersion: networking.istio.io/v1alpha3\nkind: VirtualService\nmetadata:\n  namespace: xxx\n " +
		" name: istio-system-csm-knvvl6z3-helloworld-vs\nspec:\n  hosts:\n   " +
		" - helloworld.default.svc.cluster.local\n  exportTo:\n    - default\n  http:\n  - route:\n  " +
		"  - destination:\n        host: helloworld.default.svc.cluster.local\n        subset: v1\n   " +
		"   weight: 100\n    - destination:\n        host: helloworld.default.svc.cluster.local\n   " +
		"     subset: v2\n      weight: 0\n---\napiVersion: networking.istio.io/v1alpha3\n" +
		"kind: DestinationRule\nmetadata:\n  namespace: istio-system-csm-knvvl6z3\n  " +
		"name: istio-system-csm-knvvl6z3-helloworld-dr\nspec:\n  exportTo:\n    " +
		"- default\n  host: helloworld.default.svc.cluster.local\n  subsets:\n  " +
		"- name: v1\n    labels:\n      version: v1\n  - name: v2\n    labels:\n      version: v2"
)

func TestSplitYaml(t *testing.T) {
	listYaml := SplitYaml(content)
	assert.Equal(t, len(listYaml), 2)
}

func TestIsRegisteredCrdKindForIstio(t *testing.T) {
	isValid := IsRegisteredCrdKindForIstio("test")
	assert.Equal(t, isValid, false)

	isValid = IsRegisteredCrdKindForIstio(DestinationRule)
	assert.Equal(t, isValid, true)
}
