package meta

import (
	"github.com/baidubce/bce-sdk-go/model"
	"time"
)

const (
	TypeServiceAccount       = "ServiceAccount"
	TypeRole                 = "Role"
	ServiceAccount           = "ns-admin"
	RoleName                 = "ns-role"
	RoleBindingNamePrefix    = "ns-rolebinding"
	RBACAPIGroup             = "rbac.authorization.k8s.io"
	SecretsAnnotation        = "kubernetes.io/service-account.name"
	DefaultClusterName       = "kubernetes"
	VPCKubeConfigType        = "vpc"
	HostingApiServerPort     = 6443
	HostingDefaultServerName = "kubernetes"

	RoleAPIGroupExtensions  = "extensions.istio.io"
	RoleResourceWasmPlugins = "wasmplugins"

	RoleAPIGroupNetworking       = "networking.istio.io"
	RoleResourceDestinationRules = "destinationrules"
	RoleResourceEnvoyFilters     = "envoyfilters"
	RoleResourceGateways         = "gateways"
	RoleResourceProxyConfigs     = "proxyconfigs"
	RoleResourceServiceEntries   = "serviceentries"
	RoleResourceSidecars         = "sidecars"
	RoleResourceVirtualServices  = "virtualservices"
	RoleResourceWorkloadEntries  = "workloadentries"
	RoleResourceWorkloadGroups   = "workloadgroups"

	RoleAPIGroupSecurity               = "security.istio.io"
	RoleResourceAuthorizationPolicies  = "authorizationpolicies"
	RoleResourcePeerAuthentications    = "peerauthentications"
	RoleResourceRequestAuthentications = "requestauthentications"

	RoleAPIGroupTelemetry    = "telemetry.istio.io"
	RoleResourcesTelemetries = "telemetries"

	RoleVerbGet    = "get"
	RoleVerbList   = "list"
	RoleVerbWatch  = "watch"
	RoleVerbCreate = "create"
	RoleVerbUpdate = "update"
	RoleVerbDelete = "delete"
)

type Namespaces struct {
	PageSize   int      `json:"pageSize" `
	PageNo     int      `json:"pageNo"`
	Order      string   `json:"order"`
	OrderBy    string   `json:"orderBy"`
	TotalCount int      `json:"totalCount"`
	Result     []Result `json:"result"`
}

type Result struct {
	ClusterUUID string    `json:"clusterId"`
	ClusterName string    `json:"clusterName"`
	Namespace   string    `json:"namespace"`
	Region      string    `json:"region" gorm:"column:region"`
	Status      string    `json:"status"`
	CreateTime  time.Time `json:"createTime"`
	Labels      []string  `json:"labels"`
}

// KubeConfigResult 获取KubeConfig接口返回结构
type KubeConfigResult struct {
	KubeConfigType string `json:"kubeConfigType"` // KubeConfigType 类型, 仅支持vpc，使用 BLB VPC-IP
	KubeConfig     string `json:"kubeConfig"`     // KubeConfig 文本内容
}

// CreateEndpointArgs 创建vpc请求参数，sdk中的未更新eip字段，待sdk更新后删除。
type CreateEndpointArgs struct {
	ClientToken string           `json:"-"`
	VpcId       string           `json:"vpcId"`
	Name        string           `json:"name"`
	SubnetId    string           `json:"subnetId"`
	Service     string           `json:"service"`
	Description string           `json:"description,omitempty"`
	IpAddress   string           `json:"ipAddress,omitempty"`
	Billing     *Billing         `json:"billing"`
	Tags        []model.TagModel `json:"tags,omitempty"`
	Eip         string           `json:"eip,omitempty"`
}

type Billing struct {
	PaymentTiming PaymentTimingType `json:"paymentTiming,omitempty"`
	Reservation   *Reservation      `json:"reservation,omitempty"`
}

type (
	PaymentTimingType string
)

type Reservation struct {
	ReservationLength   int    `json:"reservationLength"`
	ReservationTimeUnit string `json:"reservationTimeUnit"`
}

type ListEndpointResult struct {
	Endpoints   []Endpoint `json:"endpoints"`
	Marker      string     `json:"marker"`
	IsTruncated bool       `json:"isTruncated"`
	NextMarker  string     `json:"nextMarker"`
	MaxKeys     int        `json:"maxKeys"`
}

type Endpoint struct {
	EndpointId  string           `json:"endpointId"`
	Name        string           `json:"name"`
	IpAddress   string           `json:"ipAddress"`
	Status      string           `json:"status"`
	Service     string           `json:"service"`
	SubnetId    string           `json:"subnetId"`
	Description string           `json:"description"`
	CreateTime  string           `json:"createTime"`
	VpcId       string           `json:"vpcId"`
	ProductType string           `json:"productType"`
	Tags        []model.TagModel `json:"tags,omitempty"`
	Eip         string           `json:"eip,omitempty"`
}
