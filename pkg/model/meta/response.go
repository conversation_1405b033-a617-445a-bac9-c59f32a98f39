package meta

import (
	"time"

	"github.com/baidubce/bce-sdk-go/services/appblb"
)

// MeshInstance 网格实例结构体
type MeshInstance struct {
	CreateTime     time.Time `json:"createTime"`
	InstanceId     string    `json:"instanceId"`
	InstanceName   string    `json:"instanceName"`
	InstanceType   string    `json:"instanceType"`
	IstioVersion   string    `json:"istioVersion"`
	Status         string    `json:"instanceStatus"`
	BillingModel   string    `json:"billingModel"`
	SidecarCount   int64     `json:"sidecarCount"`
	RunningCluster int64     `json:"runningClusterCount"`
	ClusterCount   int64     `json:"clusterCount"`
	PublicEnabled  bool      `json:"publicEnabled"`
	BlbInfo        BLBInfo   `json:"blbInfo"`
	EipInfo        EIPInfo   `json:"eipInfo"`
}

type BLBInfo struct {
	BlbId string `json:"blbId"`
}

type EIPInfo struct {
	EipId string `json:"eipId"`
}

// MeshInstanceList 包含分页信息和网格实例列表的返回值结构体
type MeshInstanceListResponse struct {
	PageSize   int64          `json:"pageSize"`
	PageNo     int64          `json:"pageNo"`
	Order      string         `json:"order"`
	OrderBy    string         `json:"orderBy"`
	TotalCount int64          `json:"totalCount"`
	Result     []MeshInstance `json:"result"`
}

// MeshService 实例服务结构体
type MeshService struct {
	Host              string `json:"host"`
	K8sServiceCount   int64  `json:"k8sServiceCount"`
	ServiceEntryCount int64  `json:"serviceEntryCount"`
}

// MeshServiceListResponse 包含分页信息和实例服务列表的返回值结构体
type MeshServiceListResponse struct {
	PageSize   int64         `json:"pageSize"`
	PageNo     int64         `json:"pageNo"`
	Order      string        `json:"order"`
	OrderBy    string        `json:"orderBy"`
	TotalCount int64         `json:"totalCount"`
	Result     []MeshService `json:"result"`
}

type PageResult struct {
	PageSize   int64  `json:"pageSize"`
	PageNo     int64  `json:"pageNo"`
	Order      string `json:"order"`
	OrderBy    string `json:"orderBy"`
	TotalCount int64  `json:"totalCount"`
}

// MeshServiceDetail 服务详情结构体
type MeshServiceDetail struct {
	KSD []K8sServiceDetail   `json:"k8sServiceDetailInfoList"`
	SED []ServiceEntryDetail `json:"serviceEntryDetailInfoList"`
}

// K8sServiceDetail K8s服务详情结构体
type K8sServiceDetail struct {
	Region      string `json:"region"`
	ClusterId   string `json:"clusterId"`
	ClusterName string `json:"clusterName"`
	ServiceName string `json:"serviceName"`
	Namespace   string `json:"namespace"`
}

// ServiceEntryDetail Service Entry服务详情结构体
type ServiceEntryDetail struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	ExportTo  string `json:"exportTo"`
}

// WhiteListResult 白名单查询返回结果
type WhiteListResult struct {
	IsExist bool `json:"isExist"`
}

// GatewayDisplay 网关实例展示结构体
type GatewayDisplay struct {
	CreateTime    time.Time `json:"createTime"`
	GatewayName   string    `json:"gatewayName"`
	GatewayId     string    `json:"gatewayId"`
	Status        string    `json:"status"`
	BillingModel  string    `json:"billingModel"`
	DeployMode    string    `json:"deployMode"`
	ResourceQuota string    `json:"resourceQuota"`
	Replicas      int16     `json:"replicas"`
}

// GatewayListResponse 包含分页信息和网关列表的返回值结构体
type GatewayListResponse struct {
	PageSize   int64            `json:"pageSize"`
	PageNo     int64            `json:"pageNo"`
	Order      string           `json:"order"`
	OrderBy    string           `json:"orderBy"`
	TotalCount int64            `json:"totalCount"`
	Result     []GatewayDisplay `json:"result"`
}

type HPA struct {
	Enabled     bool  `json:"enabled"`
	MinReplicas int32 `json:"minReplicas"`
	MaxReplicas int32 `json:"maxReplicas"`
}

type Log struct {
	Enabled bool   `json:"enabled"`
	Type    string `json:"type"`
	LogFile string `json:"logFile"`
}

type TLSAcc struct {
	Enabled bool `json:"enabled"`
}

type ResourceQuota struct {
	ResourceQuota string `json:"resourceQuota"`
}

type NetworkConfig struct {
	NetworkType *NetworkType `json:"networkType"`
}

type NetworkType struct {
	VpcNetworkId   string `json:"vpcNetworkId"`
	VpcNetworkCidr string `json:"vpcNetworkCidr"`
	VpcNetworkName string `json:"vpcNetworkName"`
	SubnetId       string `json:"subnetId"`
	SubnetName     string `json:"subnetName"`
	SubnetCidr     string `json:"subnetCidr"`
}

type BasicConfig struct {
	GatewayName   string   `json:"gatewayName"`
	GatewayUUID   string   `json:"gatewayId"`
	Status        string   `json:"status"`
	BillingModel  string   `json:"billingModel"`
	DeployMode    string   `json:"deployMode"`
	GatewayType   string   `json:"gatewayType"`
	ResourceQuota string   `json:"resourceQuota"`
	Replicas      int16    `json:"replicas"`
	HPA           *HPA     `json:"hpa"`
	Log           *Log     `json:"log"`
	Monitor       *Monitor `json:"monitor"`
	TLSAcc        *TLSAcc  `json:"tlsAcc"`
}

// GatewayDetailResponse 网关实例详情结构体
type GatewayDetailResponse struct {
	BasicConfig   *BasicConfig   `json:"basicConfig"`
	NetworkConfig *NetworkConfig `json:"networkConfig"`
}

type BlbDisplay struct {
	ID              string `json:"shortId"`
	Name            string `json:"name"`
	PublicIP        string `json:"publicIp"`
	EipType         string `json:"eipType"`
	EipAllocationID string `json:"eipAllocationId"`
}

type AvailableBlbListResponse struct {
	TotalCount int64        `json:"totalCount"`
	Result     []BlbDisplay `json:"result"`
}

type BlbDescribe struct {
	ID           string                 `json:"shortId"`
	Name         string                 `json:"name"`
	PublicIP     string                 `json:"publicIp"`
	InternalIP   string                 `json:"internalIp"`
	Status       string                 `json:"status"`
	CreateTime   string                 `json:"createTime"`
	ListenerList []appblb.ListenerModel `json:"listenerList"`
}

type GatewayBlbListResponse struct {
	TotalCount int64         `json:"totalCount"`
	Result     []BlbDescribe `json:"result"`
}

type DomainPort struct {
	Number    int32  `json:"number"`
	Name      string `json:"name"`
	Protocol  string `json:"protocol"`
	PreNumber int32  `json:"preNumber"`
}

type DomainCert struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type DomainDescribe struct {
	Domains      []string   `json:"domains"`
	Port         DomainPort `json:"port"`
	Cert         DomainCert `json:"cert"`
	UpdateTime   time.Time  `json:"updateTime"`
	IsForceHTTPS bool       `json:"isForceHttps"`
}

type GatewayDomainListResponse struct {
	PageSize   int64            `json:"pageSize"`
	PageNo     int64            `json:"pageNo"`
	Order      string           `json:"order"`
	OrderBy    string           `json:"orderBy"`
	TotalCount int64            `json:"totalCount"`
	Result     []DomainDescribe `json:"result"`
}

type Series struct {
	Name string `json:"name"`
	Data []int  `json:"data"`
}

type Columns struct {
	Name string `json:"name"`
	Id   string `json:"id"`
}

type Response struct {
	Status int                    `json:"status"` // 0表示成功，非0表示失败
	Msg    string                 `json:"msg"`    // 失败时的提示 信息
	Data   map[string]interface{} `json:"data"`
}

type Item struct {
	Name    string `json:"name"`
	Colspan int    `json:"colspan"`
}

type Rows struct {
	AccountId          string `json:"account_id"`
	Name               string `json:"name"`
	StandaloneInstance int    `json:"standaloneInstance"`
	HostingInstance    int    `json:"hostingInstance"`
	InstanceSum        int    `json:"instanceSum"`
	StandaloneSidecar  int    `json:"standaloneSidecar"`
	HostingSidecar     int    `json:"hostingSidecar"`
	SidecarSum         int    `json:"sidecarSum"`
}

type User struct {
	ComponentValue    string `json:"componentValue"`
	Name              string `json:"name"`
	Code              string `json:"code"`
	AccountUuid       string `json:"accountUuid"`
	StandaloneSidecar int    `json:"standaloneSidecar"`
	HostingSidecar    int    `json:"hostingSidecar"`
}
