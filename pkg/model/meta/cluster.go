package meta

import (
	"time"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ClusterStatus string
type ClusterConnectionState string
type ClusterMasterMode string
type ClusterType string
type ClusterCode string
type ClusterAgentName string

const (
	ClusterNotFound ClusterCode = "cce.warning.ClusterNotFound"
)

const (
	// 参考 CCE https://cloud.baidu.com/doc/CCE/s/Jkgajlwaz#clusterstatus
	// 已纳管集群 && 可纳管集群
	ClusterStatusPending      ClusterStatus = "pending"
	ClusterStatusProvisioning ClusterStatus = "provisioning"
	ClusterStatusProvisioned  ClusterStatus = "provisioned"
	ClusterStatusRunning      ClusterStatus = "running"
	ClusterStatusCreateFailed ClusterStatus = "create_failed"
	ClusterStatusDeleting     ClusterStatus = "deleting"
	ClusterStatusDeleted      ClusterStatus = "deleted"
	ClusterStatusDeleteFailed ClusterStatus = "delete_failed"
	ClusterStatusUnknown      ClusterStatus = "unknown"

	// 已纳管集群
	ClusterConnectedState        ClusterConnectionState = "connected"
	ClusterNotConnectedState     ClusterConnectionState = "not_connected"
	ClusterUnknownConnectedState ClusterConnectionState = "unknown"

	// 参考 CCE https://cloud.baidu.com/doc/CCE/s/Jkgajlwaz#masterconfig
	// 可纳管集群
	// 托管模式
	ManagedClusterMode    ClusterMasterMode = "managed"
	ManagedProClusterMode ClusterMasterMode = "managedPro"
	// serverless
	ServerlessClusterMode ClusterMasterMode = "serverless"
	// 独立部署
	CustomClusterMode              ClusterMasterMode = "custom"
	ContainerizedCustomClusterMode ClusterMasterMode = "containerizedCustom"
	UnknownClusterMode             ClusterMasterMode = "unknown"

	// 已纳管集群
	ClusterTypePrimary  ClusterType = "primary"
	ClusterTypeRemote   ClusterType = "remote"
	ClusterTypeExternal ClusterType = "external"
	ClusterTypeConfig   ClusterType = "config"

	// eks profile
	EksProfileOnline = "online"
	EksProfileGrey   = "grey"
)

// CCE agent名称对应的参数
const (
	CCEGPUManger                      ClusterAgentName = "cce-gpu-manager"
	CCEAIJobScheduler                 ClusterAgentName = "cce-volcano"
	CCERDMADevicePlugin               ClusterAgentName = "cce-rdma-plugin"
	CCEPaddleFlow                     ClusterAgentName = "cce-paddleflow"
	CCEFluid                          ClusterAgentName = "cce-fluid"
	CCEDeepLearningFrameworksOperator ClusterAgentName = "cce-aibox"
	CCEImageAccelerate                ClusterAgentName = "cce-image-accelerate"
	CCEHybridManager                  ClusterAgentName = "cce-hybrid-manager"
	CCEIngressNGINXController         ClusterAgentName = "cce-ingress-nginx-controller"
	CCEIngressController              ClusterAgentName = "cce-ingress-controller"
	CCECSICDSPlugin                   ClusterAgentName = "cce-csi-cds-plugin"
	CCECSIBOSPlugin                   ClusterAgentName = "cce-csi-bos-plugin"
	CCECSIPFSPlugin                   ClusterAgentName = "cce-csi-pfs-plugin"
	CCENPUManager                     ClusterAgentName = "cce-npu-manager"
	CCELogOperator                    ClusterAgentName = "cce-log-operator"
)

func ClusterStatusToString(cs ClusterStatus) string {
	switch cs {
	case ClusterStatusPending:
		return string(ClusterStatusPending)
	case ClusterStatusProvisioning:
		return string(ClusterStatusProvisioning)
	case ClusterStatusProvisioned:
		return string(ClusterStatusProvisioned)
	case ClusterStatusRunning:
		return string(ClusterStatusRunning)
	case ClusterStatusCreateFailed:
		return string(ClusterStatusCreateFailed)
	case ClusterStatusDeleting:
		return string(ClusterStatusDeleting)
	case ClusterStatusDeleted:
		return string(ClusterStatusDeleted)
	case ClusterStatusDeleteFailed:
		return string(ClusterStatusDeleteFailed)
	default:
		return string(ClusterStatusUnknown)
	}
}

func ClusterMasterModeToString(cmm ClusterMasterMode) string {
	switch cmm {
	case ManagedClusterMode:
		return string(ManagedClusterMode)
	case ManagedProClusterMode:
		return string(ManagedProClusterMode)
	case ServerlessClusterMode:
		return string(ServerlessClusterMode)
	case CustomClusterMode:
		return string(CustomClusterMode)
	case ContainerizedCustomClusterMode:
		return string(ContainerizedCustomClusterMode)
	default:
		return string(UnknownClusterMode)
	}
}

// TODO: 升级 gorm 2.0 后修改表字段
type Cluster struct {
	dbutil.BaseModel
	//nolint
	InstanceUUID string `gorm:"column:instance_uuid" json:"instanceUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	ClusterUUID string `gorm:"column:cluster_uuid" json:"clusterUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	ClusterName string `gorm:"column:cluster_name" json:"clusterName" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	ClusterType string `gorm:"column:cluster_type" json:"clusterType" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	Region string `gorm:"column:region" json:"region" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	AccountId string `gorm:"column:account_id" json:"accountId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	ConnectionState string `gorm:"column:connection_state" json:"connectionState" dbutil:"searchable"`
	//nolint
	IstioInstallNamespace string `gorm:"column:istio_install_namespace" json:"IstioInstallNamespace" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	MonitorInstanceId string `gorm:"column:monitor_instance_id" json:"monitorInstanceId" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	MonitorRegion string `gorm:"column:monitor_region" json:"monitorRegion" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	MonitorJobIds string `gorm:"column:monitor_job_ids" json:"monitorJobIds" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	MonitorAgentID string `gorm:"column:monitor_agent_id" json:"monitorAgentId" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	IngressSyncEnabled *bool `gorm:"column:ingress_sync_enabled" json:"ingressSyncEnabled" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable"`
}

type ManagedCluster struct {
	InstanceId   string
	ClusterId    string
	ClusterName  string
	ClusterType  ClusterType
	Region       string
	AccountId    string
	Status       ClusterStatus
	Version      string
	ContainerNet string
	AddedTime    time.Time
	// 已联通状态说明控制面能给集群下发 xds 配置，sidecar 能连到控制面
	ConnectionState *ClusterConnectionState
	VpcInfo
}

type CandidateCluster struct {
	ClusterId      string
	ClusterName    string
	Status         ClusterStatus
	Version        string
	MasterMode     ClusterMasterMode
	NetworkSegment string
	Region         string
	Available      bool
	VpcInfo
}

func (cluster *Cluster) TableName() string {
	return "t_service_mesh_cluster"
}
