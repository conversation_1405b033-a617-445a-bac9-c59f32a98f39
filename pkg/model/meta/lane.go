package meta

import (
	"bytes"
	"fmt"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

const (
	LaneGroupIDPrefix       = "lg"
	LaneIDPrefix            = "l"
	LaneServiceListSeparate = ","
	LaneServiceSeparate     = "/"
	InvalidKeyApp           = "app"
)

// LaneGroupParams 泳道组前后端交互结构
type LaneGroupParams struct {
	InstanceUUID string              `param:"instanceUUID" valid:"required"`
	GroupID      string              `json:"groupID"`
	GroupName    string              `json:"groupName"`
	TraceHeader  string              `json:"traceHeader"`
	RouteHeader  string              `json:"routeHeader"`
	ServiceList  []ServiceListParams `json:"serviceList"`
	BaseLane     LaneParams          `json:"baseLane"`
}

// LaneGroupParamsResponse 包含分页信息
type LaneGroupParamsResponse struct {
	PageSize   int               `json:"pageSize"`
	PageNo     int               `json:"pageNo"`
	Order      string            `json:"order"`
	OrderBy    string            `json:"orderBy"`
	TotalCount int               `json:"totalCount"`
	Result     []LaneGroupParams `json:"result"`
}

// LaneParams 泳道前后端交互结构
type LaneParams struct {
	InstanceUUID       string              `param:"instanceUUID"`
	GroupID            string              `param:"laneGroupID"`
	LaneID             string              `json:"laneID"`
	LaneName           string              `json:"laneName"`
	LabelSelectorKey   string              `json:"labelSelectorKey"`
	LabelSelectorValue string              `json:"labelSelectorValue"`
	ServiceList        []ServiceListParams `json:"serviceList"`
	RouteList          []RouteParams       `json:"routeList"`
	IsBase             bool                `json:"isBaseLane"`
}

// LaneParamsResponse 包含分页信息
type LaneParamsResponse struct {
	PageSize   int          `json:"pageSize"`
	PageNo     int          `json:"pageNo"`
	Order      string       `json:"order"`
	OrderBy    string       `json:"orderBy"`
	TotalCount int          `json:"totalCount"`
	Result     []LaneParams `json:"result"`
}

// LaneGroupServiceListParams 创建泳道组时获取服务列表的前后端交互结构体
type LaneGroupServiceListParams struct {
	InstanceUUID string `param:"instanceUUID"`
	ServiceListParams
	PageParam
}

// LaneServiceListResponse 包含分页信息和实例服务列表的返回值结构体
type LaneServiceListResponse struct {
	PageSize   int                 `json:"pageSize"`
	PageNo     int                 `json:"pageNo"`
	Order      string              `json:"order"`
	OrderBy    string              `json:"orderBy"`
	TotalCount int                 `json:"totalCount"`
	Result     []ServiceListParams `json:"result"`
}

// ServiceListParams 泳道和泳道组中的服务列表
type ServiceListParams struct {
	ClusterName   string `json:"clusterName"`
	ClusterRegion string `query:"clusterRegion" json:"clusterRegion"`
	ClusterID     string `query:"clusterID" json:"clusterID"`
	Namespace     string `query:"namespace" json:"namespace"`
	ServiceName   string `json:"serviceName"`
	IsHost        bool   `json:"isHost"` // false 表示k8s服务名称，true 表示为host
}

// RouteParams 引流规则
type RouteParams struct {
	InstanceUUID  string      `param:"instanceUUID"`
	GroupID       string      `param:"laneGroupID"`
	LaneID        string      `param:"laneID"`
	ClusterRegion string      `query:"clusterRegion" json:"clusterRegion"`
	ClusterID     string      `query:"clusterID" json:"clusterID"`
	Namespace     string      `query:"namespace" json:"namespace"`
	ServiceName   string      `query:"serviceName" json:"serviceName"`
	IsHost        bool        `query:"serviceName" json:"isHost"`
	Rules         []RouteRule `json:"rules"`
}

type RouteRule struct {
	MatchRequest      MatchRequest           `json:"matchRequest"`
	RouteDestinations []RouteDestinationRule `json:"routeDestinations"`
}

type MatchRequest struct {
	RouteName string   `json:"routeName"`
	Headers   []Header `json:"headers"`
	Uri       Uri      `json:"uri"`
}

type Header struct {
	Name            string `json:"name"`
	MatchingMode    string `json:"matchingMode"`
	MatchingContent string `json:"matchingContent"`
}

type Uri struct {
	MatchingMode    string `json:"matchingMode"`
	MatchingContent string `json:"matchingContent"`
	Enabled         bool   `json:"enabled"`
}

type RouteDestinationRule struct {
	Destination Destination `json:"destination"`
}

type Destination struct {
	Name   string `json:"name"`
	Subset string `json:"subset"`
}

// ServiceListToModel 将serviceListParams结构变成数据库存储的text。
// 样式为："{cce集群地域}/{cce集群名称}/{cce集群ID}/{namespace}/{服务名 or host}/{isHost}"
func ServiceListToModel(serviceList []ServiceListParams) string {
	var result bytes.Buffer
	separate := ""
	for _, service := range serviceList {
		tmpService := fmt.Sprintf("%s/%s/%s/%s/%s/%t", service.ClusterRegion, service.ClusterName,
			service.ClusterID, service.Namespace, service.ServiceName, service.IsHost)
		result.WriteString(separate)
		result.WriteString(tmpService)
		separate = LaneServiceListSeparate
	}
	return result.String()
}

// ModelStringToServiceList 将数据库存储的text转换成serviceListParams结构
// 样式为："{cce集群地域}/{cce集群名称}/{cce集群ID}/{namespace}/{服务名 or host}/{isHost}"
func ModelStringToServiceList(serviceString string) []ServiceListParams {
	result := make([]ServiceListParams, 0)
	serviceStringList := strings.Split(serviceString, LaneServiceListSeparate)
	for _, service := range serviceStringList {
		tmpServiceParam := strings.Split(service, LaneServiceSeparate)
		if len(tmpServiceParam) != 6 {
			fmt.Printf("invalid serviceList %s", service)
			continue
		}
		isHost, parseErr := strconv.ParseBool(tmpServiceParam[5])
		if parseErr != nil {
			fmt.Printf("invalid serviceList %s", service)
			continue
		}
		tmp := ServiceListParams{
			ClusterRegion: tmpServiceParam[0],
			ClusterName:   tmpServiceParam[1],
			ClusterID:     tmpServiceParam[2],
			Namespace:     tmpServiceParam[3],
			ServiceName:   tmpServiceParam[4],
			IsHost:        isHost,
		}
		result = append(result, tmp)
	}
	return result
}

// ToLaneGroupsModel 将laneGroupParams转换为LanGroups结构
func (lg *LaneGroupParams) ToLaneGroupsModel() (*LaneGroups, error) {
	if lg.ServiceList == nil || len(lg.ServiceList) <= 0 {
		return nil, csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("lanGroup name %s's serviceList is empty", lg.GroupName))
	}

	result := &LaneGroups{
		InstanceUUID: lg.InstanceUUID,
		GroupName:    lg.GroupName,
		TraceHeader:  lg.TraceHeader,
		RouteHeader:  lg.RouteHeader,
		ServiceList:  ServiceListToModel(lg.ServiceList),
		Deleted:      csm.Int(0),
	}
	return result, nil
}

// ToLaneGroupParams 将LanGroups转换为laneGroupParams结构
func (laneGroups *LaneGroups) ToLaneGroupParams() *LaneGroupParams {
	result := &LaneGroupParams{
		InstanceUUID: laneGroups.InstanceUUID,
		GroupName:    laneGroups.GroupName,
		TraceHeader:  laneGroups.TraceHeader,
		RouteHeader:  laneGroups.RouteHeader,
		ServiceList:  ModelStringToServiceList(laneGroups.ServiceList),
	}
	return result
}

// ToLanesModel 将laneParams转换为Lanes结构
func (l *LaneParams) ToLanesModel() (*Lanes, error) {
	if l.GroupID == "" || l.InstanceUUID == "" {
		return nil, csmErr.NewInvalidParameterInputValueException("GroupID or InstanceUUID is empty")
	}

	if l.ServiceList == nil || len(l.ServiceList) <= 0 {
		return nil, csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("lan name %s's serviceList is empty", l.LaneName))
	}

	result := &Lanes{
		InstanceUUID:       l.InstanceUUID,
		GroupID:            l.GroupID,
		LaneName:           l.LaneName,
		LabelSelectorKey:   l.LabelSelectorKey,
		LabelSelectorValue: l.LabelSelectorValue,
		ServiceList:        ServiceListToModel(l.ServiceList),
		Deleted:            csm.Int(0),
		IsBase:             csm.Bool(l.IsBase),
	}
	return result, nil
}

func ToLaneParams(laneModelList []Lanes) []LaneParams {
	result := make([]LaneParams, 0)
	for _, temp := range laneModelList {
		serviceList := ModelStringToServiceList(temp.ServiceList)
		laneParam := LaneParams{
			LaneID:             temp.LaneID,
			GroupID:            temp.GroupID,
			InstanceUUID:       temp.InstanceUUID,
			IsBase:             *temp.IsBase,
			LabelSelectorKey:   temp.LabelSelectorKey,
			LabelSelectorValue: temp.LabelSelectorValue,
			LaneName:           temp.LaneName,
			ServiceList:        serviceList,
		}

		result = append(result, laneParam)
	}
	return result
}

// LaneGroups
// *InstanceUUID* csm实例ID
// *GroupID* 泳道组ID，UUID，格式为lg-xxxxxxxx
// *GroupName* 泳道组名称
// *AccountId* 用户公有云账户ID
// *TraceHeader* 链路透传请求头
// *TraceHeader* 链路引流请求头
// *ServiceList* 泳道组包含的服务列表，字符串为json格式，样式为："{cce集群地域}/{cce集群名称}/{cce集群ID}/{namespace}/{服务名}"
// *Deleted* 是否删除，0表示未删除
type LaneGroups struct {
	dbutil.BaseModel
	//nolint
	InstanceUUID string `gorm:"column:instance_uuid" json:"instanceUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	GroupID string `gorm:"column:group_id" json:"groupID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	GroupName string `gorm:"column:group_name" json:"groupName" dbutil:"searchable:wildcard,updatable" valid:"required,stringlength(0|255)"`
	//nolint
	AccountId string `gorm:"column:account_id" json:"accountId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	TraceHeader string `gorm:"column:trace_header" json:"traceHeader" dbutil:"searchable:wildcard,updatable" valid:"required"`
	//nolint
	RouteHeader string `gorm:"column:route_header" json:"routeHeader" dbutil:"searchable:wildcard,updatable" valid:"required"`
	//nolint
	ServiceList string `gorm:"column:service_list" json:"serviceList" dbutil:"searchable:wildcard,updatable"`
	//nolint
	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable,updatable"`
}

func (laneGroups *LaneGroups) TableName() string {
	return "t_service_mesh_lane_group"
}

// Lanes
// *InstanceUUID* csm实例ID
// *GroupID* 泳道组ID，UUID，格式为lg-xxxxxxxx
// *LaneID* 泳道ID，UUID，格式为l-xxxxxxxx
// *LaneName* 泳道名称，！！！泳道名称为引流请求头的value值。
// *AccountId* 用户公有云账户ID
// *LabelSelectorKey*   匹配泳道内服务标签的标签名称
// *LabelSelectorValue* 匹配泳道内服务标签的标签值
// *ServiceList* 泳道组包含的服务列表，字符串为json格式，样式为："{cce集群地域}/{cce集群名称}/{cce集群ID}/{namespace}/{服务名}"
// *IsBase* 是否基准泳道,true为基准泳道
// *Deleted* 是否删除，0表示未删除
type Lanes struct {
	dbutil.BaseModel
	//nolint
	InstanceUUID string `gorm:"column:instance_uuid" json:"instanceUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	GroupID string `gorm:"column:group_id" json:"groupID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	LaneID string `gorm:"column:lane_id" json:"laneID" dbutil:"searchable:wildcard,updatable" valid:"required"`
	//nolint
	LaneName string `gorm:"column:lane_name" json:"laneName" dbutil:"searchable:wildcard,updatable" valid:"required,stringlength(0|255)"`
	//nolint
	AccountId string `gorm:"column:account_id" json:"accountId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	LabelSelectorKey string `gorm:"column:label_selector_key" json:"labelSelectorKey" dbutil:"searchable:wildcard,updatable" valid:"required"`
	//nolint
	LabelSelectorValue string `gorm:"column:label_selector_value" json:"labelSelectorValue" dbutil:"searchable:wildcard,updatable" valid:"required"`
	//nolint
	ServiceList string `gorm:"column:service_list" json:"serviceList" dbutil:"searchable:wildcard,updatable"`
	//nolint
	IsBase *bool `gorm:"column:is_base" json:"isBase" dbutil:"searchable:wildcard,updatable"`
	//nolint
	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable,updatable"`
}

func (lanes *Lanes) TableName() string {
	return "t_service_mesh_lane"
}
