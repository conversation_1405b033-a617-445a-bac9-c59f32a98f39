package meta

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Bls struct {
	dbutil.BaseModel
	InstanceUUID string `gorm:"column:instance_uuid" json:"instanceUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	LogStoreName string `gorm:"column:log_store_name" json:"logStoreName" dbutil:"searchable:wildcard,orderable" valid:"required"`
	TaskID       string `gorm:"column:task_id" json:"taskId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	AccountID    string `gorm:"column:account_id;default:0" json:"accountId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	ClusterUUID  string `gorm:"column:cluster_uuid;default:0" json:"clusterUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	Deleted      *int   `gorm:"column:deleted;default:0" json:"deleted" dbutil:"searchable:wildcard,orderable" `
}

type BlsLogStoreName struct {
	BlsLogStoreName string `json:"name"`
}

func (bls *Bls) TableName() string {
	return "t_service_mesh_bls_group"
}
