package meta

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

const (
	EksAccountIds               = "eks.accountIds"
	EksProfile                  = "eks.profile"
	EksProfileGreyClusterId     = "eks.grey.clusterId"
	EksProfileGreyClusterName   = "eks.grey.clusterName"
	EksProfileOnlineClusterId   = "eks.online.clusterId"
	EksProfileOnlineClusterName = "eks.online.clusterName"
)

type PaaSType string
type MeshType string

const (
	PaaSTypeEKS PaaSType = "EKS"
	PaaSTypeCCE PaaSType = "CCE"

	HostingMeshType    MeshType = "hosting"
	StandaloneMeshType MeshType = "standalone"
	CnapMeshType       MeshType = "cnap-hosting"
)

// InstanceManageScope is the type of scope of istio
type InstanceManageScope string

const (
	// InstanceManageClusterScope is cluster type
	InstanceManageClusterScope InstanceManageScope = "cluster"

	// InstanceManageNamespaceScope is namespace type
	InstanceManageNamespaceScope InstanceManageScope = "namespace"
)

// InstanceManageScopeToString converts InstanceManageScope to string
func InstanceManageScopeToString(ims InstanceManageScope) string {
	switch ims {
	case InstanceManageNamespaceScope:
		return string(InstanceManageNamespaceScope)
	default:
		return string(InstanceManageClusterScope)
	}
}

type Instances struct {
	dbutil.BaseModel

	//nolint
	InstanceUUID string `gorm:"column:instance_uuid" json:"instanceUUID" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	InstanceName string `gorm:"column:instance_name" json:"instanceName" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	InstanceType string `gorm:"type:enum('standalone', 'hosting');column:instance_type" json:"instanceType" dbutil:"searchable:wildcard" valid:"required,stringlength(0|255)"`
	//nolint
	IstioVersion string `gorm:"column:istio_version" json:"istioVersion" dbutil:"searchable:wildcard" valid:"required"`
	//nolint
	Region string `gorm:"column:region" json:"region" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	AccountId string `gorm:"column:account_id" json:"accountId" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	VpcNetworkId string `gorm:"column:vpc_network_id" json:"vpcNetworkId" dbutil:"searchable:wildcard,orderable"`
	//nolint
	SubnetId string `gorm:"column:subnet_id" json:"subnetId" dbutil:"searchable:wildcard,orderable"`
	//nolint
	DiscoverySelectorLabels string `gorm:"column:discovery_selector_labels" json:"discoverySelectorLabels" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	IstioInstallNamespace string `gorm:"column:istio_install_namespace" json:"istioInstallNamespace" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	InstanceManageScope string `gorm:"type:enum('cluster', 'namespace');column:instance_manage_scope" json:"instanceManageScope" dbutil:"searchable:wildcard,orderable" valid:"required"`
	//nolint
	Status string `gorm:"column:status" json:"status" dbutil:"searchable:wildcard,orderable"`
	//nolint
	Metadata string `gorm:"column:metadata" json:"metadata" dbutil:"searchable:wildcard"`
	//nolint
	PublicEnabled *bool `gorm:"public_enabled" json:"publicEnabled" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	MultiProtocolEnabled *bool `gorm:"multi_protocol_enabled" json:"multiProtocolEnabled" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	DiscoverySelectorEnabled *bool `gorm:"column:discovery_selector_enabled" json:"discoverySelectorEnabled" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	MonitorEnabled *bool `gorm:"column:monitor_enabled" json:"monitorEnabled" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable"`
	//nolint
	BlsEnabled *bool `gorm:"column:bls_enabled" json:"blsEnabled" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	APIServerEip *bool `gorm:"column:api_server_eip" json:"apiServerEip" dbutil:"searchable:wildcard,orderable,updatable"`
	//nolint
	ConfigCluster string `gorm:"column:config_cluster" json:"configCluster" dbutil:"searchable:wildcard,updatable"`
	//nolint
	TraceEnabled *bool `gorm:"column:trace_enabled" json:"traceEnabled" dbutil:"searchable:wildcard,orderable,updatable"`
}

func (instances *Instances) TableName() string {
	return "t_service_mesh_instance"
}
