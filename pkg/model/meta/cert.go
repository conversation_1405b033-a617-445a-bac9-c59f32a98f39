package meta

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

func (cert *Cert) TableName() string {
	return "t_service_mesh_cert"
}

type Cert struct {
	dbutil.BaseModel
	//nolint
	Region string `gorm:"column:region" json:"region" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	ClusterUUID string `gorm:"column:cluster_uuid" json:"clusterUUID" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	AccountId string `gorm:"column:account_id" json:"accountId" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	CaCertPem string `gorm:"column:ca_cert_pem" json:"caCertPem" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	<PERSON> string `gorm:"column:ca_key_pem" json:"caKeyPem" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	CertChainPem string `gorm:"column:cert_chain_pem" json:"certChainPem" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	RootCertPem string `gorm:"column:root_cert_pem" json:"rootCertPem" dbutil:"searchable:wildcard,orderable,updatable" valid:"required"`
	//nolint
	Deleted *int `gorm:"column:deleted" json:"deleted" dbutil:"searchable:wildcard,orderable,updatable"`
}

func NewCert(region, clusterUUID, accountId, caCertPem, caKeyPem, certChainPem, rootCertPem string) *Cert {
	return &Cert{
		Region:       region,
		ClusterUUID:  clusterUUID,
		AccountId:    accountId,
		CaCertPem:    caCertPem,
		CaKeyPem:     caKeyPem,
		CertChainPem: certChainPem,
		RootCertPem:  rootCertPem,
		Deleted:      csm.Int(0),
	}
}
