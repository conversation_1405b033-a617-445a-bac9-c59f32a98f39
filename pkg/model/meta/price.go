package meta

import "time"

type Flavor struct {
	FlavorItems []FlavorItem `json:"flavorItems"`
}

type FlavorItem struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Scale int    `json:"scale"`
}

type QuoteInfo struct {
	Uuid               string               `json:"uuid"`
	Price              float64              `json:"price"`
	PriceId            int64                `json:"priceId"`
	CatalogPrice       float64              `json:"catalogPrice"`
	PriceType          string               `json:"priceType"`
	PriceName          string               `json:"priceName"`
	ErrorCode          int                  `json:"errorCode"`
	DiscountRate       float64              `json:"discountRate"`
	SkuId              string               `json:"skuId"`
	PostPayPriceDetail PostPayPriceDetail   `json:"postPayPriceDetail"`
	PastOriginPrice    PricingQueryResponse `json:"pastOriginPrice"`
}

type ResponsePayload struct {
	Response []QuoteInfo `json:"responses"`
}

type PricingQueryResponse struct {
}

type PostPayPriceDetail struct {
}

type Request struct {
	UUID           string    `json:"uuid"`
	AccountID      string    `json:"accountId"`
	ServiceType    string    `json:"serviceType"`
	Region         string    `json:"region"`
	Duration       int       `json:"duration"`
	TimeUnit       string    `json:"timeUnit"`
	OrderType      string    `json:"orderType"`
	Count          int       `json:"count"`
	Flavor         Flavor    `json:"flavor"`
	InstanceID     string    `json:"instanceId"`
	ProductType    string    `json:"productType"`
	ChargeItemName string    `json:"chargeItemName"`
	QueryTime      time.Time `json:"queryTime"`
}

type RequestPayload struct {
	Requests []Request `json:"requests"`
}

type BillingArgs struct {
	PaymentTiming string `json:"paymentTiming"`
	BillingMethod string `json:"billingMethod"`
}

type EipPriceRequest struct {
	BandwidthInMbps int         `json:"bandwidthInMbps"`
	Count           int         `json:"count"`
	PurchaseType    string      `json:"purchaseType"`
	Billing         BillingArgs `json:"billing"`
}

type BlbPriceRequest struct {
	BlbType          string      `json:"blbType"`
	Count            int         `json:"count"`
	PerformanceLevel string      `json:"performanceLevel"`
	ClientToken      string      `json:"-"`
	Billing          BillingArgs `json:"billing"`
}

type Prices struct {
	ConfigPrice   string `json:"configPrice"`
	ChargeItem    string `json:"chargeItem"`
	OriginalPrice string `json:"originalPrice"`
	DiscountPrice string `json:"discountPrice"`
	ChargeUnit    string `json:"chargeUnit"`
}

type PricesResponse struct {
	Prices []Prices `json:"prices"`
}
