package billing

import (
	"bytes"
	"github.com/agiledragon/gomonkey/v2"
	bce_sdk "github.com/baidubce/bce-sdk-go/bce"
	"testing"

	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	mockCtx, _ = ctxCsm.NewCsmContextMock()
)

func TestService_NewClient(t *testing.T) {
	opt := &Option{
		BillingAccessKey:   "accessKey",
		BillingSecretKey:   "secretKey",
		billingGetPriceUrl: "/test",
		BillingHost:        "example.com",
		BillingEndpoint:    "http://example.com",
	}
	payload := map[string]interface{}{"key": "value"}
	tests := []struct {
		name    string
		want    *bytes.Buffer
		wantErr bool
	}{
		{
			name:    "test_billing_newClient",
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				option: opt,
			}
			patches := gomonkey.ApplyFunc(bce_sdk.NewRequestBuilder, func(_ bce_sdk.Client) *bce_sdk.RequestBuilder {
				return &bce_sdk.RequestBuilder{}
			})
			defer patches.Reset()

			var rb *bce_sdk.RequestBuilder
			patches.ApplyMethod(rb, "Do", func(_ *bce_sdk.RequestBuilder) error {
				return nil
			})
			_, err := s.NewClient(mockCtx, payload)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
