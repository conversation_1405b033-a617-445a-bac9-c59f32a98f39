package billing

import (
	bcesdk "github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type Service struct {
	option *Option
}

func NewService(opt *Option) *Service {
	return &Service{
		option: opt,
	}
}

func (s *Service) NewClient(ctx context.CsmContext, requestPayload interface{}) (*meta.ResponsePayload, error) {
	client, err := bcesdk.NewBceClientWithAkSk(s.option.BillingAccessKey, s.option.BillingSecretKey, s.option.BillingEndpoint)
	if err != nil {
		ctx.CsmLogger().Errorf("Error NewBceClientWithAkSk : %v", err)
		return nil, err
	}

	quoteInfo := &meta.ResponsePayload{}
	err = bcesdk.NewRequestBuilder(client).
		WithURL(s.option.billingGetPriceUrl).
		WithMethod(http.POST).
		WithBody(requestPayload).
		WithResult(quoteInfo).
		Do()
	if err != nil {
		ctx.CsmLogger().Errorf("Error NewRequestBuilder : %v", err)
		return nil, err
	}
	return quoteInfo, nil
}
