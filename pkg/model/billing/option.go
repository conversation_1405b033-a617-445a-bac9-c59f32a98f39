package billing

import "github.com/spf13/viper"

const (
	billingEndpoint    = "billing.endpoint"
	billingHost        = "billing.host"
	billingGetPriceUrl = "billing.getPriceUrl"

	billingAccessKey = "billing.access_key"
	billingSecretKey = "billing.secret_key"
)

type Option struct {
	// Billing endpoint
	BillingEndpoint string
	// Billing host
	BillingHost string
	// Billing getPriceUrl
	billingGetPriceUrl string
	// BillingAccessKey billing询价使用的 ak
	BillingAccessKey string
	// BillingSecretKey billing询价使用的 sk
	BillingSecretKey string
}

// NewOption 初始化 billing 配置
func NewOption() *Option {
	return &Option{
		BillingEndpoint:    viper.GetString(billingEndpoint),
		BillingHost:        viper.GetString(billingHost),
		billingGetPriceUrl: viper.GetString(billingGetPriceUrl),
		BillingAccessKey:   viper.GetString(billingAccessKey),
		BillingSecretKey:   viper.GetString(billingSecretKey),
	}
}
