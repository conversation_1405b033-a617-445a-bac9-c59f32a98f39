// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// CheckScrapeJob mocks base method.
func (m *MockServiceInterface) CheckScrapeJob(cc context.CsmContext, region, cpromInstanceID, cpromAgentID, scrapeJobName string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckScrapeJob", cc, region, cpromInstanceID, cpromAgentID, scrapeJobName)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckScrapeJob indicates an expected call of CheckScrapeJob.
func (mr *MockServiceInterfaceMockRecorder) CheckScrapeJob(cc, region, cpromInstanceID, cpromAgentID, scrapeJobName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckScrapeJob", reflect.TypeOf((*MockServiceInterface)(nil).CheckScrapeJob), cc, region, cpromInstanceID, cpromAgentID, scrapeJobName)
}

// CreateEnvoyScrapeJob mocks base method.
func (m *MockServiceInterface) CreateEnvoyScrapeJob(ctx context.CsmContext, region, cpromInstanceId, cpromAgentId string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEnvoyScrapeJob", ctx, region, cpromInstanceId, cpromAgentId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEnvoyScrapeJob indicates an expected call of CreateEnvoyScrapeJob.
func (mr *MockServiceInterfaceMockRecorder) CreateEnvoyScrapeJob(ctx, region, cpromInstanceId, cpromAgentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEnvoyScrapeJob", reflect.TypeOf((*MockServiceInterface)(nil).CreateEnvoyScrapeJob), ctx, region, cpromInstanceId, cpromAgentId)
}

// CreateGatewayScrapeJob mocks base method.
func (m *MockServiceInterface) CreateGatewayScrapeJob(ctx context.CsmContext, csmInstanceID, region, cpromInstanceID, cpromAgentID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGatewayScrapeJob", ctx, csmInstanceID, region, cpromInstanceID, cpromAgentID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGatewayScrapeJob indicates an expected call of CreateGatewayScrapeJob.
func (mr *MockServiceInterfaceMockRecorder) CreateGatewayScrapeJob(ctx, csmInstanceID, region, cpromInstanceID, cpromAgentID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGatewayScrapeJob", reflect.TypeOf((*MockServiceInterface)(nil).CreateGatewayScrapeJob), ctx, csmInstanceID, region, cpromInstanceID, cpromAgentID)
}

// CreateHostingIstioScrapeJob mocks base method.
func (m *MockServiceInterface) CreateHostingIstioScrapeJob(cc context.CsmContext, csmInstanceID, region, cpromInstanceID, cpromAgentID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHostingIstioScrapeJob", cc, csmInstanceID, region, cpromInstanceID, cpromAgentID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateHostingIstioScrapeJob indicates an expected call of CreateHostingIstioScrapeJob.
func (mr *MockServiceInterfaceMockRecorder) CreateHostingIstioScrapeJob(cc, csmInstanceID, region, cpromInstanceID, cpromAgentID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHostingIstioScrapeJob", reflect.TypeOf((*MockServiceInterface)(nil).CreateHostingIstioScrapeJob), cc, csmInstanceID, region, cpromInstanceID, cpromAgentID)
}

// CreateIstioScrapeJob mocks base method.
func (m *MockServiceInterface) CreateIstioScrapeJob(ctx context.CsmContext, region, cpromInstanceId, cpromAgentId string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIstioScrapeJob", ctx, region, cpromInstanceId, cpromAgentId)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIstioScrapeJob indicates an expected call of CreateIstioScrapeJob.
func (mr *MockServiceInterfaceMockRecorder) CreateIstioScrapeJob(ctx, region, cpromInstanceId, cpromAgentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIstioScrapeJob", reflect.TypeOf((*MockServiceInterface)(nil).CreateIstioScrapeJob), ctx, region, cpromInstanceId, cpromAgentId)
}

// DeleteScrapeJob mocks base method.
func (m *MockServiceInterface) DeleteScrapeJob(ctx context.CsmContext, region, cpromJobId, cpromInstanceId, cpromAgentId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteScrapeJob", ctx, region, cpromJobId, cpromInstanceId, cpromAgentId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteScrapeJob indicates an expected call of DeleteScrapeJob.
func (mr *MockServiceInterfaceMockRecorder) DeleteScrapeJob(ctx, region, cpromJobId, cpromInstanceId, cpromAgentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteScrapeJob", reflect.TypeOf((*MockServiceInterface)(nil).DeleteScrapeJob), ctx, region, cpromJobId, cpromInstanceId, cpromAgentId)
}

// GetAllScrapeJobs mocks base method.
func (m *MockServiceInterface) GetAllScrapeJobs(ctx context.CsmContext, region, cpromInstanceId, cpromAgentId string) (*meta.CPromScrapeJobItemResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllScrapeJobs", ctx, region, cpromInstanceId, cpromAgentId)
	ret0, _ := ret[0].(*meta.CPromScrapeJobItemResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllScrapeJobs indicates an expected call of GetAllScrapeJobs.
func (mr *MockServiceInterfaceMockRecorder) GetAllScrapeJobs(ctx, region, cpromInstanceId, cpromAgentId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllScrapeJobs", reflect.TypeOf((*MockServiceInterface)(nil).GetAllScrapeJobs), ctx, region, cpromInstanceId, cpromAgentId)
}

// GetCPromAgent mocks base method.
func (m *MockServiceInterface) GetCPromAgent(ctx context.CsmContext, cpromInstanceId, region, clusterId string) (*meta.CPromAgent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCPromAgent", ctx, cpromInstanceId, region, clusterId)
	ret0, _ := ret[0].(*meta.CPromAgent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCPromAgent indicates an expected call of GetCPromAgent.
func (mr *MockServiceInterfaceMockRecorder) GetCPromAgent(ctx, cpromInstanceId, region, clusterId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCPromAgent", reflect.TypeOf((*MockServiceInterface)(nil).GetCPromAgent), ctx, cpromInstanceId, region, clusterId)
}

// GetCPromAgentByVpcID mocks base method.
func (m *MockServiceInterface) GetCPromAgentByVpcID(ctx context.CsmContext, cpromInstanceID, region, vpcID string) (*meta.CPromAgent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCPromAgentByVpcID", ctx, cpromInstanceID, region, vpcID)
	ret0, _ := ret[0].(*meta.CPromAgent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCPromAgentByVpcID indicates an expected call of GetCPromAgentByVpcID.
func (mr *MockServiceInterfaceMockRecorder) GetCPromAgentByVpcID(ctx, cpromInstanceID, region, vpcID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCPromAgentByVpcID", reflect.TypeOf((*MockServiceInterface)(nil).GetCPromAgentByVpcID), ctx, cpromInstanceID, region, vpcID)
}

// GetCPromInstanceById mocks base method.
func (m *MockServiceInterface) GetCPromInstanceById(ctx context.CsmContext, region, instanceId string) (*meta.CPromInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCPromInstanceById", ctx, region, instanceId)
	ret0, _ := ret[0].(*meta.CPromInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCPromInstanceById indicates an expected call of GetCPromInstanceById.
func (mr *MockServiceInterfaceMockRecorder) GetCPromInstanceById(ctx, region, instanceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCPromInstanceById", reflect.TypeOf((*MockServiceInterface)(nil).GetCPromInstanceById), ctx, region, instanceId)
}

// GetCPromInstanceDetail mocks base method.
func (m *MockServiceInterface) GetCPromInstanceDetail(ctx context.CsmContext, region, monitorInstacneId string) (*meta.CPromItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCPromInstanceDetail", ctx, region, monitorInstacneId)
	ret0, _ := ret[0].(*meta.CPromItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCPromInstanceDetail indicates an expected call of GetCPromInstanceDetail.
func (mr *MockServiceInterfaceMockRecorder) GetCPromInstanceDetail(ctx, region, monitorInstacneId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCPromInstanceDetail", reflect.TypeOf((*MockServiceInterface)(nil).GetCPromInstanceDetail), ctx, region, monitorInstacneId)
}

// GetCPromInstancesByRegion mocks base method.
func (m *MockServiceInterface) GetCPromInstancesByRegion(ctx context.CsmContext, region string) ([]meta.CPromInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCPromInstancesByRegion", ctx, region)
	ret0, _ := ret[0].([]meta.CPromInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCPromInstancesByRegion indicates an expected call of GetCPromInstancesByRegion.
func (mr *MockServiceInterfaceMockRecorder) GetCPromInstancesByRegion(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCPromInstancesByRegion", reflect.TypeOf((*MockServiceInterface)(nil).GetCPromInstancesByRegion), ctx, region)
}

// GetCpromAgentList mocks base method.
func (m *MockServiceInterface) GetCpromAgentList(cc context.CsmContext, cpromInstanceID, region string) (*meta.CPromAgentsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCpromAgentList", cc, cpromInstanceID, region)
	ret0, _ := ret[0].(*meta.CPromAgentsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCpromAgentList indicates an expected call of GetCpromAgentList.
func (mr *MockServiceInterfaceMockRecorder) GetCpromAgentList(cc, cpromInstanceID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCpromAgentList", reflect.TypeOf((*MockServiceInterface)(nil).GetCpromAgentList), cc, cpromInstanceID, region)
}
