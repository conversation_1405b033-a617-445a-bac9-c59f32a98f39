package monitor

import (
	"fmt"
	"os"
	"path"
	"strings"

	"github.com/spf13/viper"
)

const (
	monitorEndpoint   = "monitor.logicEndpoint"
	monitorIamProfile = "monitor.iamProfile"
	bceServiceRole    = "bceServiceRole"

	monitorAccessKey = "local.access_key"
	monitorSecretKey = "local.secret_key"

	IstioScrapeJobTemplate        = "monitor.scrape_job.template.istio"
	EnvoyScrapeJobTemplate        = "monitor.scrape_job.template.envoy"
	GatewayScrapeJobTemplate      = "monitor.scrape_job.template.gateway"
	HostingIstioScrapeJobTemplate = "monitor.scrape_job.template.hostingIstio"

	monitorSandboxHost               = "monitor.sandbox.host"
	monitorSandboxInstancesUrl       = "monitor.sandbox.instancesUrl"
	monitorSandboxAgentUrl           = "monitor.sandbox.agentUrl"
	monitorSandboxScrapeJobUrl       = "monitor.sandbox.scrapeJobUrl"
	monitorSandboxDeleteScrapeJobUrl = "monitor.sandbox.deleteScrapeJobUrl"

	monitorOnlineHost               = "monitor.online.host"
	monitorOnlineInstancesUrl       = "monitor.online.instancesUrl"
	monitorOnlineAgentUrl           = "monitor.online.agentUrl"
	monitorOnlineScrapeJobUrl       = "monitor.online.scrapeJobUrl"
	monitorOnlineDeleteScrapeJobUrl = "monitor.online.deleteScrapeJobUrl"

	monitorInternalHost               = "monitor.internal.host"
	monitorInternalInstancesUrl       = "monitor.internal.instancesUrl"
	monitorInternalAgentUrl           = "monitor.internal.agentUrl"
	monitorInternalScrapeJobUrl       = "monitor.internal.scrapeJobUrl"
	monitorInternalDeleteScrapeJobUrl = "monitor.internal.deleteScrapeJobUrl"

	localMode      = "local.dev"
	monitorProfile = "monitor.profile"
)

type Option struct {
	// monitor endpoint
	MonitorEndpoint string
	// 服务号名称
	BceServiceRole string
	// monitor 使用 iam 配置
	MonitorIamProfile string
	// dev mode
	// AccessKey ak
	AccessKey string
	// SecretKey sk
	SecretKey string

	IstioScrapeJob        string
	EnvoyScrapeJob        string
	GatewayScrapeJob      string
	HostingIstioScrapeJob string

	Host               string
	InternalHosts      map[string]string
	InstancesUrl       string
	AgentUrl           string
	ScrapeJobUrl       string
	DeleteScrapeJobUrl string
}

// NewOption 初始化 monitor 配置
func NewOption() *Option {
	var istioJobContent []byte
	var envoyJobContent []byte
	var gatewayJobContent []byte
	var hostingIstioJobContent []byte

	pwd, err := os.Getwd()
	if err != nil {
		fmt.Printf("get pwd error %v", err)
		panic(err)
	} else {
		istioFilePath := path.Join(pwd, "templates", "monitor", viper.GetString(IstioScrapeJobTemplate))
		envoyFilePath := path.Join(pwd, "templates", "monitor", viper.GetString(EnvoyScrapeJobTemplate))
		gatewayFilePath := path.Join(pwd, "templates", "monitor", viper.GetString(GatewayScrapeJobTemplate))
		hostingIstioFilePath := path.Join(pwd, "templates", "monitor", viper.GetString(HostingIstioScrapeJobTemplate))
		istioJobContent, err = os.ReadFile(istioFilePath)
		if err != nil {
			panic(err)
		}
		envoyJobContent, err = os.ReadFile(envoyFilePath)
		if err != nil {
			panic(err)
		}
		gatewayJobContent, err = os.ReadFile(gatewayFilePath)
		if err != nil {
			panic(err)
		}
		hostingIstioJobContent, err = os.ReadFile(hostingIstioFilePath)
		if err != nil {
			panic(err)
		}
	}

	var host string
	var internalHosts map[string]string
	var instancesUrl string
	var agentUrl string
	var scrapeJobUrl string
	var deleteScrapeJobUrl string

	profile := strings.TrimSpace(viper.GetString(monitorProfile))
	if strings.EqualFold(profile, "sandbox") {
		host = viper.GetString(monitorSandboxHost)
		instancesUrl = viper.GetString(monitorSandboxInstancesUrl)
		agentUrl = viper.GetString(monitorSandboxAgentUrl)
		scrapeJobUrl = viper.GetString(monitorSandboxScrapeJobUrl)
		deleteScrapeJobUrl = viper.GetString(monitorSandboxDeleteScrapeJobUrl)
	} else if strings.EqualFold(profile, "internal") {
		internalHosts = viper.GetStringMapString(monitorInternalHost)
		instancesUrl = viper.GetString(monitorInternalInstancesUrl)
		agentUrl = viper.GetString(monitorInternalAgentUrl)
		scrapeJobUrl = viper.GetString(monitorInternalScrapeJobUrl)
		deleteScrapeJobUrl = viper.GetString(monitorInternalDeleteScrapeJobUrl)
	} else {
		host = viper.GetString(monitorOnlineHost)
		instancesUrl = viper.GetString(monitorOnlineInstancesUrl)
		agentUrl = viper.GetString(monitorOnlineAgentUrl)
		scrapeJobUrl = viper.GetString(monitorOnlineScrapeJobUrl)
		deleteScrapeJobUrl = viper.GetString(monitorOnlineDeleteScrapeJobUrl)
	}

	return &Option{
		MonitorEndpoint:       viper.GetString(monitorEndpoint),
		BceServiceRole:        viper.GetString(bceServiceRole),
		MonitorIamProfile:     viper.GetString(monitorIamProfile),
		AccessKey:             viper.GetString(monitorAccessKey),
		SecretKey:             viper.GetString(monitorSecretKey),
		IstioScrapeJob:        string(istioJobContent),
		EnvoyScrapeJob:        string(envoyJobContent),
		GatewayScrapeJob:      string(gatewayJobContent),
		HostingIstioScrapeJob: string(hostingIstioJobContent),
		Host:                  host,
		InternalHosts:         internalHosts,
		InstancesUrl:          instancesUrl,
		AgentUrl:              agentUrl,
		ScrapeJobUrl:          scrapeJobUrl,
		DeleteScrapeJobUrl:    deleteScrapeJobUrl,
	}
}
