package monitor

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/spf13/viper"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam/sts_credential"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/restclient"
)

var (
	mockCtx, _      = ctxCsm.NewCsmContextMock()
	region          = "gz"
	cpromInstanceId = "cprom-instance-id-test"
	clusterId       = "cluster-id-test"
)

func buildService() *Service {
	localCache := sts_credential.NewLocalCredentialCache()
	currentDir, _ := os.Getwd()
	os.Chdir("../../../")
	viper.Set(IstioScrapeJobTemplate, "istio_scrape_job.yaml")
	viper.Set(EnvoyScrapeJobTemplate, "envoy_scrape_job.yaml")
	viper.Set(GatewayScrapeJobTemplate, "gateway_scrape_job.tmpl")
	viper.Set(HostingIstioScrapeJobTemplate, "hosting_istio_scrape_job.tmpl")
	viper.Set("local.dev", true)
	viper.Set("local.access_key", "********************************")
	viper.Set("local.secret_key", "b599d5f4f30e41bcb0e1482c9de65bd6")
	viper.Set("monitor.sandbox.host", "http://*************:8794")
	viper.Set("monitor.sandbox.instancesUrl", "/api/v1/cprom/instances")
	viper.Set("monitor.sandbox.agentUrl", "/api/v1/cprom/instances/%s/binding_cluster")
	viper.Set("monitor.sandbox.scrapeJobUrl", "/api/v1/cprom/scrape_jobs?instanceID=%s&agentID=%s")
	viper.Set("monitor.sandbox.deleteScrapeJobUrl", "/api/v1/cprom/scrape_jobs/%s?instanceID=%s&agentID=%s")
	service := &Service{
		option:          NewOption(),
		credentialCache: localCache,
		Client:          restclient.NewBCERestClient("monitor-test"),
	}
	os.Chdir(currentDir)
	return service
}

func TestGetCPromInstancesByRegion(t *testing.T) {
	s := buildService()
	s.GetCPromInstancesByRegion(mockCtx, region)
	// TODO(xiaosong) mock prometheus data
	//if err != nil {
	//	t.Errorf("get CProm instances by region failed error %v", err)
	//}
}

func TestGetCPromAgentByVpcID(t *testing.T) {
	currentDir, _ := os.Getwd()
	resp := &meta.CPromResponse{
		Success: true,
		Message: "success",
		Result: &meta.CPromAgentsResult{
			Items: []meta.CPromAgent{
				{
					AgentID: "111",
					Cluster: &meta.CPromBindingCluster{
						Spec: &meta.CPromBindingClusterSpec{
							VpcID:     "vpcID",
							ClusterID: "clusterID",
						},
					},
				},
			},
		},
	}
	responseBody, _ := json.Marshal(resp)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, writeErr := w.Write(responseBody)
		if writeErr != nil {
			return
		}
	}))
	_ = os.Chdir("../../../")
	viper.Set(IstioScrapeJobTemplate, "istio_scrape_job.yaml")
	viper.Set(EnvoyScrapeJobTemplate, "envoy_scrape_job.yaml")
	viper.Set(GatewayScrapeJobTemplate, "gateway_scrape_job.tmpl")
	viper.Set("local.dev", true)
	viper.Set("monitor.profile", "sandbox")
	viper.Set("local.access_key", "********************************")
	viper.Set("local.secret_key", "b599d5f4f30e41bcb0e1482c9de65bd6")
	viper.Set("monitor.sandbox.host", ts.URL)
	viper.Set("monitor.sandbox.instancesUrl", "/api/v1/cprom/instances")
	viper.Set("monitor.sandbox.agentUrl", "/api/v1/cprom/instances/%s/binding_cluster")
	viper.Set("monitor.sandbox.scrapeJobUrl", "/api/v1/cprom/scrape_jobs?instanceID=%s&agentID=%s")
	viper.Set("monitor.sandbox.deleteScrapeJobUrl", "/api/v1/cprom/scrape_jobs/%s?instanceID=%s&agentID=%s")

	service := &Service{
		option:          NewOption(),
		credentialCache: localCache,
		Client:          restclient.NewBCERestClient("monitor-test"),
	}
	_ = os.Chdir(currentDir)

	_, err := service.GetCPromAgentByVpcID(mockCtx, "insID", region, "vpcID")
	if err != nil {
		t.Error(err)
	}
}

func TestCreateGatewayScrapeJob(t *testing.T) {
	currentDir, _ := os.Getwd()
	resp := &meta.CPromResponse{
		Success: true,
		Message: "success",
		Result: &meta.CPromJobResult{
			ScrapeJobID: "scrape-job",
		},
	}
	responseBody, _ := json.Marshal(resp)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, writeErr := w.Write(responseBody)
		if writeErr != nil {
			return
		}
	}))
	_ = os.Chdir("../../../")
	viper.Set(IstioScrapeJobTemplate, "istio_scrape_job.yaml")
	viper.Set(EnvoyScrapeJobTemplate, "envoy_scrape_job.yaml")
	viper.Set(GatewayScrapeJobTemplate, "gateway_scrape_job.tmpl")
	viper.Set("local.dev", true)
	viper.Set("monitor.profile", "sandbox")
	viper.Set("local.access_key", "********************************")
	viper.Set("local.secret_key", "b599d5f4f30e41bcb0e1482c9de65bd6")
	viper.Set("monitor.sandbox.host", ts.URL)
	viper.Set("monitor.sandbox.instancesUrl", "/api/v1/cprom/instances")
	viper.Set("monitor.sandbox.agentUrl", "/api/v1/cprom/instances/%s/binding_cluster")
	viper.Set("monitor.sandbox.scrapeJobUrl", "/api/v1/cprom/scrape_jobs?instanceID=%s&agentID=%s")
	viper.Set("monitor.sandbox.deleteScrapeJobUrl", "/api/v1/cprom/scrape_jobs/%s?instanceID=%s&agentID=%s")

	service := &Service{
		option:          NewOption(),
		credentialCache: localCache,
		Client:          restclient.NewBCERestClient("monitor-test"),
	}
	_ = os.Chdir(currentDir)

	_, err := service.CreateGatewayScrapeJob(mockCtx, "insID", region, "vpcID", "agent")
	if err != nil {
		t.Error(err)
	}
}

func TestCreateHostingIstioScrapeJob(t *testing.T) {
	currentDir, _ := os.Getwd()
	resp := &meta.CPromResponse{
		Success: true,
		Message: "success",
		Result: &meta.CPromJobResult{
			ScrapeJobID: "scrape-job",
		},
	}
	responseBody, _ := json.Marshal(resp)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, writeErr := w.Write(responseBody)
		if writeErr != nil {
			return
		}
	}))
	_ = os.Chdir("../../../")
	viper.Set(IstioScrapeJobTemplate, "istio_scrape_job.yaml")
	viper.Set(EnvoyScrapeJobTemplate, "envoy_scrape_job.yaml")
	viper.Set(GatewayScrapeJobTemplate, "gateway_scrape_job.tmpl")
	viper.Set(HostingIstioScrapeJobTemplate, "hosting_istio_scrape_job.tmpl")
	viper.Set("local.dev", true)
	viper.Set("monitor.profile", "sandbox")
	viper.Set("local.access_key", "********************************")
	viper.Set("local.secret_key", "b599d5f4f30e41bcb0e1482c9de65bd6")
	viper.Set("monitor.sandbox.host", ts.URL)
	viper.Set("monitor.sandbox.instancesUrl", "/api/v1/cprom/instances")
	viper.Set("monitor.sandbox.agentUrl", "/api/v1/cprom/instances/%s/binding_cluster")
	viper.Set("monitor.sandbox.scrapeJobUrl", "/api/v1/cprom/scrape_jobs?instanceID=%s&agentID=%s")
	viper.Set("monitor.sandbox.deleteScrapeJobUrl", "/api/v1/cprom/scrape_jobs/%s?instanceID=%s&agentID=%s")

	service := &Service{
		option:          NewOption(),
		credentialCache: localCache,
		Client:          restclient.NewBCERestClient("monitor-test"),
	}
	_ = os.Chdir(currentDir)

	_, err := service.CreateHostingIstioScrapeJob(mockCtx, "insID", region, "vpcID", "agent")
	if err != nil {
		t.Error(err)
	}
}
