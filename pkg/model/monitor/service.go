package monitor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/spf13/viper"
	"gopkg.in/resty.v1"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/auth"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam/sts_credential"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/restclient"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/tmpl"
)

var (
	localCache sts_credential.Cache
	// ContextKeyBceRequestId request id
	ContextKeyBceRequestId = contextHeaderKey("x-bce-request-id")
	GatewayScrapeJobName   = "gateway-stats"
	EnvoyJobName           = "envoy-stats"
	IstioJobName           = "istiod"
	HostingIstioJobName    = "istio-stats"
)

type ScrapeJobParams struct {
	JobName string
}

func init() {
	localCache = sts_credential.NewLocalCredentialCache()
}

type contextHeaderKey string

func (c contextHeaderKey) String() string {
	return string(c)
}

type Service struct {
	credentialCache sts_credential.Cache
	option          *Option
	*resty.Client
}

func NewService(opt *Option) *Service {
	return &Service{
		option:          opt,
		credentialCache: localCache,
		Client:          restclient.NewBCERestClient("monitor"),
	}
}

func (service *Service) getCPromInstancesResult(cc csmContext.CsmContext, region string) ([]byte, error) {
	url := service.option.InstancesUrl
	uri, queryString := parseQueryParam(url)
	resp, err := service.execute(cc, region, uri, queryString, "get", nil)
	if err != nil {
		return nil, err
	}

	response := &meta.CPromResponse{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, err
	}

	if !response.Success {
		return nil, errors.New(response.Message)
	}

	if response.Result == nil {
		return nil, nil
	}

	return json.Marshal(response.Result)
}

func (service *Service) GetCPromInstancesByRegion(cc csmContext.CsmContext, region string) ([]meta.CPromInstance, error) {
	jsonBytes, err := service.getCPromInstancesResult(cc, region)
	if err != nil {
		return nil, err
	}

	r := &meta.CPromInstancesResult{}
	err = json.Unmarshal(jsonBytes, r)
	if err != nil || r.Items == nil {
		return nil, nil
	}

	mis := make([]meta.CPromInstance, 0)
	for _, i := range r.Items {
		if i.Spec == nil {
			continue
		}
		instance := meta.CPromInstance{
			InstanceName: i.Spec.InstanceName,
			InstanceId:   i.Spec.InstanceID,
			Region:       i.Spec.Region,
		}
		mis = append(mis, instance)
	}
	return mis, nil
}

func (service *Service) GetCPromInstanceDetail(ctx csmContext.CsmContext,
	region, monitorInstacneId string) (*meta.CPromItem, error) {
	jsonBytes, err := service.getCPromInstancesResult(ctx, region)
	if err != nil {
		return nil, err
	}

	cir := &meta.CPromItemResult{}
	res := &meta.CPromItem{}

	err = json.Unmarshal(jsonBytes, cir)
	if err != nil {
		ctx.Logger().Errorf("unmarshal cir error: %v", err)
		return res, nil
	}

	for _, i := range cir.Items {
		if i.Spec == nil {
			continue
		}
		if i.Spec.InstanceID == monitorInstacneId && i.Spec.Region == region {
			res = &i
			break
		}
	}
	return res, nil
}

func (service *Service) GetCPromInstanceById(cc csmContext.CsmContext, region, instanceId string) (*meta.CPromInstance, error) {
	uri := service.option.InstancesUrl + "/" + instanceId
	resp, err := service.execute(cc, region, uri, "", "get", nil)
	if err != nil {
		return nil, err
	}

	response := &meta.CPromResponse{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, err
	}

	if !response.Success {
		return nil, errors.New(response.Message)
	}

	if response.Result == nil {
		return nil, nil
	}

	jsonBytes, err := json.Marshal(response.Result)
	if err != nil {
		return nil, nil
	}

	r := &meta.CPromInstancesItem{}
	err = json.Unmarshal(jsonBytes, r)
	if err != nil || r.Spec == nil {
		return nil, nil
	}

	instance := &meta.CPromInstance{
		InstanceName: r.Spec.InstanceName,
		InstanceId:   r.Spec.InstanceID,
		Region:       r.Spec.Region,
	}
	return instance, nil
}

func (service *Service) GetCPromAgent(cc csmContext.CsmContext, cpromInstanceID, region, clusterID string) (*meta.CPromAgent, error) {
	r, getErr := service.GetCpromAgentList(cc, cpromInstanceID, region)
	if getErr != nil {
		return nil, getErr
	}

	for _, i := range r.Items {
		if i.Cluster == nil || i.Cluster.Spec == nil {
			continue
		}
		if i.Cluster.Spec.ClusterID == clusterID {
			return &i, nil
		}
	}
	return nil, fmt.Errorf("not found agent for CProm instance %s in cluster %s with region %s",
		cpromInstanceID, clusterID, region)
}

func (service *Service) GetCpromAgentList(cc csmContext.CsmContext, cpromInstanceID, region string) (*meta.CPromAgentsResult, error) {
	url := fmt.Sprintf(service.option.AgentUrl, cpromInstanceID)
	uri, queryString := parseQueryParam(url)
	resp, err := service.execute(cc, region, uri, queryString, "get", nil)
	if err != nil {
		return nil, err
	}

	response := &meta.CPromResponse{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, err
	}

	if !response.Success {
		return nil, errors.New(response.Message)
	}

	if response.Result == nil {
		return nil, nil
	}

	jsonBytes, err := json.Marshal(response.Result)
	if err != nil {
		return nil, nil
	}

	r := &meta.CPromAgentsResult{}
	err = json.Unmarshal(jsonBytes, r)
	if err != nil || r.Items == nil {
		return nil, nil
	}
	return r, nil
}

func (service *Service) GetCPromAgentByVpcID(cc csmContext.CsmContext, cpromInstanceID, region, vpcID string) (*meta.CPromAgent, error) {
	r, getErr := service.GetCpromAgentList(cc, cpromInstanceID, region)
	if getErr != nil {
		return nil, getErr
	}

	for _, i := range r.Items {
		if i.AgentID == "" || i.Cluster == nil || i.Cluster.Spec == nil || i.Cluster.Spec.VpcID != vpcID {
			continue
		}
		return &i, nil
	}
	return nil, fmt.Errorf("not found agent for CProm instance %s in vpcId %s with region %s",
		cpromInstanceID, vpcID, region)
}

func (service *Service) CheckScrapeJob(cc csmContext.CsmContext, region, cpromInstanceID,
	cpromAgentID, scrapeJobName string) bool {
	allScrapeJob, err := service.GetAllScrapeJobs(cc, region, cpromInstanceID, cpromAgentID)
	if err != nil {
		cc.CsmLogger().Errorf("CheckScrapeJob error: %v", err)
		return false
	}
	for _, item := range allScrapeJob.Items {
		if item.Spec == nil || item.Spec.ScrapeJobName == "" {
			continue
		}
		if item.Spec.ScrapeJobName == scrapeJobName {
			return true
		}
	}
	return false
}

func (service *Service) GetAllScrapeJobs(ctx csmContext.CsmContext, region, cpromInstanceId, cpromAgentId string) (
	*meta.CPromScrapeJobItemResult, error) {
	ctx.CsmLogger().Infof("get scrape job for CProm instance %s in region %s in CPromAgentID %s",
		cpromInstanceId, region, cpromAgentId)
	url := fmt.Sprintf(service.option.ScrapeJobUrl, cpromInstanceId, cpromAgentId)
	uri, queryString := parseQueryParam(url)
	// 一次性查询1000条数据（默认 job 条目不会超过 1000）
	queryString = queryString + "&pageNo=1&pageSize=1000"

	resp, err := service.execute(ctx, region, uri, queryString, "get", nil)
	if err != nil {
		return nil, err
	}

	response := &meta.CPromResponse{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, err
	}

	if !response.Success {
		return nil, errors.New(response.Message)
	}

	if response.Result == nil {
		return nil, nil
	}

	jsonBytes, err := json.Marshal(response.Result)
	if err != nil {
		return nil, nil
	}

	r := &meta.CPromScrapeJobItemResult{}
	err = json.Unmarshal(jsonBytes, r)
	if err != nil || len(r.Items) == 0 {
		return nil, nil
	}

	return r, nil
}

func (service *Service) CreateIstioScrapeJob(cc csmContext.CsmContext, region, cpromInstanceID, cpromAgentID string) (
	jobId string, err error) {
	body := &meta.CPromScrapeJobBody{
		Spec: &meta.CPromScrapeJobBodySpec{
			Content: service.option.IstioScrapeJob,
		},
	}

	return service.createScrapeJob(cc, region, cpromInstanceID, cpromAgentID, body)
}

func (service *Service) CreateEnvoyScrapeJob(cc csmContext.CsmContext, region, cpromInstanceID, cpromAgentID string) (
	jobId string, err error) {
	body := &meta.CPromScrapeJobBody{
		Spec: &meta.CPromScrapeJobBodySpec{
			Content: service.option.EnvoyScrapeJob,
		},
	}

	jobId, err = service.createScrapeJob(cc, region, cpromInstanceID, cpromAgentID, body)
	if err != nil {
		if strings.Contains(err.Error(), "already exists") || strings.Contains(err.Error(), "status=409") {
			cc.CsmLogger().Infof("Istio scrape job already exists, skip create it.")
			allScrapeJob, err := service.GetAllScrapeJobs(cc, region, cpromInstanceID, cpromAgentID)
			if err != nil {
				cc.CsmLogger().Errorf("GetScrapeJob error: %v", err)
				return "", err
			}
			for _, item := range allScrapeJob.Items {
				if item.Spec == nil || item.Spec.ScrapeJobName == "" {
					continue
				}
				if item.Spec.ScrapeJobName == EnvoyJobName {
					jobId = item.Spec.ScrapeJobID
					break
				}
			}
		} else {
			return "", err
		}
	}

	return jobId, nil
}

func (service *Service) CreateGatewayScrapeJob(cc csmContext.CsmContext, csmInstanceID, region,
	cpromInstanceID, cpromAgentID string) (jobID string, err error) {
	content := service.option.GatewayScrapeJob
	jobParam := &ScrapeJobParams{
		JobName: fmt.Sprintf("%s-%s", csmInstanceID, GatewayScrapeJobName),
	}
	newContent, err := tmpl.Evaluate(cc, content, jobParam)
	if err != nil {
		return "", err
	}

	body := &meta.CPromScrapeJobBody{
		Spec: &meta.CPromScrapeJobBodySpec{
			Content: newContent,
		},
	}

	return service.createScrapeJob(cc, region, cpromInstanceID, cpromAgentID, body)
}

// CreateHostingIstioScrapeJob 在用户集群的CProm实例中创建托管网格istiod的采集任务
func (service *Service) CreateHostingIstioScrapeJob(cc csmContext.CsmContext, csmInstanceID, region,
	cpromInstanceID, cpromAgentID string) (jobID string, err error) {
	content := service.option.HostingIstioScrapeJob
	jobParam := &ScrapeJobParams{
		JobName: fmt.Sprintf("%s-%s", csmInstanceID, HostingIstioJobName),
	}
	newContent, err := tmpl.Evaluate(cc, content, jobParam)
	if err != nil {
		return "", err
	}

	body := &meta.CPromScrapeJobBody{
		Spec: &meta.CPromScrapeJobBodySpec{
			Content: newContent,
		},
	}

	return service.createScrapeJob(cc, region, cpromInstanceID, cpromAgentID, body)
}

func parseQueryParam(url string) (uri, queryString string) {
	urls := strings.SplitN(url, "?", 2)
	uri = urls[0]
	if len(urls) > 1 {
		queryString = urls[1]
	}
	return uri, queryString
}

func (service *Service) createScrapeJob(cc csmContext.CsmContext, region, cpromInstanceID, cpromAgentID string,
	body *meta.CPromScrapeJobBody) (jobId string, err error) {
	cc.CsmLogger().Infof("create scrape job for CProm instance %s in region %s in CPromAgentID %s with body %s",
		cpromInstanceID, region, cpromAgentID, body.Spec.Content)
	url := fmt.Sprintf(service.option.ScrapeJobUrl, cpromInstanceID, cpromAgentID)
	uri, queryString := parseQueryParam(url)

	resp, err := service.execute(cc, region, uri, queryString, "post", body)
	if err != nil {
		return "", err
	}

	response := &meta.CPromResponse{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return "", err
	}

	if !response.Success {
		return "", errors.New(response.Message)
	}

	if response.Result == nil {
		return "", nil
	}

	jsonBytes, err := json.Marshal(response.Result)
	if err != nil {
		return "", nil
	}

	r := &meta.CPromJobResult{}
	err = json.Unmarshal(jsonBytes, r)
	if err != nil || len(r.ScrapeJobID) == 0 {
		return "", nil
	}

	return r.ScrapeJobID, nil
}

func (service *Service) DeleteScrapeJob(cc csmContext.CsmContext, region, cpromJobID, cpromInstanceID,
	cpromAgentID string) error {
	url := fmt.Sprintf(service.option.DeleteScrapeJobUrl, cpromJobID, cpromInstanceID, cpromAgentID)
	uri, queryString := parseQueryParam(url)
	resp, err := service.execute(cc, region, uri, queryString, "delete", nil)
	if err != nil {
		return err
	}

	response := &meta.CPromResponse{}
	err = json.Unmarshal(resp, &response)
	if err != nil {
		return err
	}

	if !response.Success {
		return errors.New(response.Message)
	}

	return nil
}

func (service *Service) execute(cc csmContext.CsmContext, region, url, queryString,
	method string, body interface{}) ([]byte, error) {
	var hostUrl string
	profile := strings.TrimSpace(viper.GetString(monitorProfile))
	if strings.EqualFold(profile, "sandbox") {
		hostUrl = service.option.Host
	} else if strings.EqualFold(profile, "internal") {
		hostUrl = service.option.InternalHosts[region]
	} else {
		hostUrl = fmt.Sprintf(service.option.Host, region)
	}
	service.Client.SetHostURL(hostUrl)
	cc.CsmLogger().Infof("Request url : %s", hostUrl+url)

	// TODO: 优化代码
	req, err := service.getRequest(cc)
	if err != nil {
		return nil, err
	}
	req.SetQueryString(queryString)

	var resp *resty.Response
	switch method {
	case "get":
		resp, err = req.Get(url)
	case "post":
		req.SetBody(body)
		resp, err = req.Post(url)
	case "put":
		resp, err = req.Put(url)
	case "delete":
		resp, err = req.Delete(url)
	default:
		return nil, fmt.Errorf("unsupported method %s", method)
	}
	if err != nil {
		return nil, err
	}

	err = service.checkResponse(resp)
	if err != nil {
		return nil, err
	}
	return resp.Body(), nil
}

func (service *Service) getRequest(cc csmContext.CsmContext) (*resty.Request, error) {
	ctx := context.Background()
	if viper.GetBool(localMode) {
		cc.CsmLogger().Infof("*** local develop mode to get monitor response ***")
		// todo we maybe can consider bce cce api
		authKey := auth.NewBceAuthKey(service.option.AccessKey, service.option.SecretKey)
		ctx = context.WithValue(ctx, restclient.BceAuthContextKey, *authKey)
		ctx = context.WithValue(ctx, ContextKeyBceRequestId, cc.RequestID())
	} else {
		// 通过sts的方式完成服务调用
		stsCtx, credential, err := service.requestContext(cc)
		if err != nil {
			cc.CsmLogger().Errorf("get sts credential failed. %v", err)
			return nil, err
		}
		ctx = context.WithValue(stsCtx, restclient.BceSubAccountKey, credential.UserId)
	}
	return service.R().SetContext(ctx), nil
}

func (service *Service) requestContext(cc csmContext.CsmContext) (context.Context, *sts_credential.StsCredential, error) {
	credential, err := iam.GetAssumeRoleCredential(cc, service.option.MonitorIamProfile,
		service.option.BceServiceRole, service.credentialCache)
	if err != nil {
		return nil, nil, err
	}
	authKey := auth.NewBceAuthKey(credential.AccessKeyId, credential.AccessKeySecret)
	ctx := context.WithValue(context.Background(), restclient.BceAuthContextKey, *authKey)
	ctx = context.WithValue(ctx, restclient.BceSecurityTokenKey, credential.SessionToken)
	ctx = context.WithValue(ctx, ContextKeyBceRequestId, cc.RequestID())
	return ctx, credential, nil
}

func (service *Service) checkResponse(resp *resty.Response) error {
	if resp.IsError() {
		bceError, e := restclient.BceResponseError(resp)
		if e != nil {
			return e
		}
		return bceError
	}
	return nil
}
