package monitor

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetCPromInstanceDetail(ctx csmContext.CsmContext, region, monitorInstacneId string) (*meta.CPromItem, error)
	GetCpromAgentList(cc csmContext.CsmContext, cpromInstanceID, region string) (*meta.CPromAgentsResult, error)
	GetCPromInstancesByRegion(ctx csmContext.CsmContext, region string) ([]meta.CPromInstance, error)
	GetCPromInstanceById(ctx csmContext.CsmContext, region, instanceId string) (*meta.CPromInstance, error)
	//InstallCPromAgent(ctx csmContext.CsmContext, cpromInstanceId, region, clusterId string) (*meta.CPromAgent, error)
	GetCPromAgent(ctx csmContext.CsmContext, cpromInstanceId, region, clusterId string) (*meta.CPromAgent, error)
	GetCPromAgentByVpcID(ctx csmContext.CsmContext, cpromInstanceID, region, vpcID string) (*meta.CPromAgent, error)
	CreateIstioScrapeJob(ctx csmContext.CsmContext, region, cpromInstanceId, cpromAgentId string) (
		jobId string, err error)
	CreateHostingIstioScrapeJob(cc csmContext.CsmContext, csmInstanceID, region,
		cpromInstanceID, cpromAgentID string) (jobID string, err error)
	CreateEnvoyScrapeJob(ctx csmContext.CsmContext, region, cpromInstanceId, cpromAgentId string) (
		jobId string, err error)
	GetAllScrapeJobs(ctx csmContext.CsmContext, region, cpromInstanceId, cpromAgentId string) (
		*meta.CPromScrapeJobItemResult, error)
	CheckScrapeJob(cc csmContext.CsmContext, region, cpromInstanceID, cpromAgentID, scrapeJobName string) bool
	CreateGatewayScrapeJob(ctx csmContext.CsmContext, csmInstanceID, region, cpromInstanceID, cpromAgentID string) (jobID string, err error)
	DeleteScrapeJob(ctx csmContext.CsmContext, region, cpromJobId, cpromInstanceId, cpromAgentId string) error
}
