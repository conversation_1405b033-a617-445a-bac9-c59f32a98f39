package cluster

import (
	"os"
	"path/filepath"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao"
	daoMocks "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/dao/mocks"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

var (
	mockDB, _  = gorm.Open("sqlite3", filepath.Join(os.TempDir(), "gorm.db"))
	mockCtx, _ = ctxCsm.NewCsmContextMock()

	InstanceId          = "test-123456"
	ClusterId           = "cce-123456"
	ClusterName         = "test-test"
	ClusterTypeRemote   = "remote"
	ClusterTypeExternal = "external"
	Region              = "bj"
	AccountId           = "1"
)

func TestGetAllClusterByRegion(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInstanceIDs := []string{"inst-0"}
	testClusterNames := []string{"clu-a"}
	testClusterTypes := []string{"primary"}

	tests := []struct {
		name      string
		region    string
		cluList   []meta.Cluster
		expectNum int64
	}{
		{
			name:   "test_getAllClusterByRegion",
			region: "bj",
			cluList: []meta.Cluster{
				{
					InstanceUUID: testInstanceIDs[0],
					ClusterName:  testClusterNames[0],
					ClusterType:  testClusterTypes[0],
				},
			},
			expectNum: 1,
		},
	}
	for _, tt := range tests {
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}

		mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.cluList, nil)

		t.Run(tt.name, func(t *testing.T) {
			res, _ := service.GetAllClusterByRegion(mockCtx, tt.region)
			assert.Equal(t, int64(len(*res)), tt.expectNum)
		})
	}
}

func TestGetAllClusterByInstanceUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInstanceIDs := []string{"inst-0"}
	testClusterNames := []string{"clu-a"}
	testClusterTypes := []string{"primary"}

	tests := []struct {
		name      string
		instID    string
		cluList   []meta.Cluster
		expectNum int64
	}{
		{
			name:   "test_getAllClusterByInstanceUUID",
			instID: "inst-0",
			cluList: []meta.Cluster{
				{
					InstanceUUID: testInstanceIDs[0],
					ClusterName:  testClusterNames[0],
					ClusterType:  testClusterTypes[0],
				},
			},
			expectNum: 1,
		},
	}

	for _, tt := range tests {
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}

		mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.cluList, nil)

		t.Run(tt.name, func(t *testing.T) {
			res, _ := service.GetAllClusterByInstanceUUID(mockCtx, tt.instID)
			assert.Equal(t, int64(len(*res)), tt.expectNum)
		})
	}
}

func TestGetIstiodCluster(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInstanceIDs := []string{"inst-0", "inst-1"}
	testInstanceTypes := []string{"standalone", "hosting"}
	testClusterNames := []string{"clu-a", "clu-b"}
	testClusterTypes := []string{"primary", "external"}

	tests := []struct {
		name      string
		instID    string
		instType  string
		expectClu meta.Cluster
	}{
		{
			name:     "test_getPrimaryCluster",
			instID:   testInstanceIDs[0],
			instType: testInstanceTypes[0],
			expectClu: meta.Cluster{
				ClusterName: testClusterNames[0],
				ClusterType: testClusterTypes[0],
			},
		},
		{
			name:     "test_getExternalCluster",
			instID:   testInstanceIDs[1],
			instType: testInstanceTypes[1],
			expectClu: meta.Cluster{
				ClusterName: testClusterNames[1],
				ClusterType: testClusterTypes[1],
			},
		},
	}

	for _, tt := range tests {
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}

		mockDaoModel.EXPECT().LoadWithWhere(mockCtx, gomock.Any()).Return(&tt.expectClu, nil)

		t.Run(tt.name, func(t *testing.T) {
			istiodClu, _ := service.GetIstiodCluster(mockCtx, tt.instID, tt.instType)
			assert.Equal(t, *istiodClu, tt.expectClu)
		})
	}
}

func buildManagedCluster() *meta.ManagedCluster {
	return &meta.ManagedCluster{
		InstanceId:      InstanceId,
		ClusterId:       ClusterId,
		ClusterName:     ClusterName,
		ClusterType:     meta.ClusterTypeRemote,
		Region:          Region,
		AccountId:       AccountId,
		ConnectionState: (*meta.ClusterConnectionState)(csm.String(string(meta.ClusterConnectedState))),
	}
}

func buildManagedExpectCluster() []*meta.ManagedCluster {
	var managedClusters []*meta.ManagedCluster
	managedCluster := buildManagedCluster()
	managedClusters = append(managedClusters, managedCluster)
	return managedClusters
}

func buildBasePageListResult() *dao.BasePageListResult {
	return &dao.BasePageListResult{
		PageSize:   10,
		PageNo:     1,
		TotalCount: 2,
	}
}

func buildCluster() meta.Cluster {
	cluster := meta.Cluster{
		InstanceUUID: InstanceId,
		ClusterUUID:  ClusterId,
		ClusterName:  ClusterName,
		Region:       Region,
		AccountId:    AccountId,
		ClusterType:  string(meta.ClusterTypeExternal),
		BaseModel: dbutil.BaseModel{
			CreateTime: new(time.Time),
		},
		ConnectionState: string(meta.ClusterConnectedState),
	}
	return cluster
}

func buildMockClusters() *[]meta.Cluster {
	clusters := make([]meta.Cluster, 0)
	cluster := buildCluster()
	clusters = append(clusters, cluster)

	cluster1 := buildCluster()
	cluster1.ClusterType = string(meta.ClusterTypeRemote)
	clusters = append(clusters, cluster1)
	return &clusters
}

func TestGetManagedClustersByPage(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	page := meta.GetPageParam(mockCtx, nil)
	page.PageSize = 100

	tests := []struct {
		name             string
		page             *meta.Page
		where            *meta.Cluster
		search           *meta.Cluster
		mockClusters     *[]meta.Cluster
		mockTotalCount   *dao.BasePageListResult
		mockErr          error
		expectClusters   []*meta.ManagedCluster
		expectTotalCount int64
		err              error
	}{
		{
			name:             "GetManagedClustersByPage-success",
			page:             page,
			where:            &meta.Cluster{},
			search:           &meta.Cluster{},
			mockClusters:     buildMockClusters(),
			mockTotalCount:   buildBasePageListResult(),
			mockErr:          nil,
			expectClusters:   buildManagedExpectCluster(),
			expectTotalCount: 1,
			err:              nil,
		},
	}

	for _, tt := range tests {
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}

		mockDaoModel.EXPECT().ListPage(mockCtx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.mockTotalCount, tt.mockClusters, tt.mockErr)

		t.Run(tt.name, func(t *testing.T) {
			gotCluster, gotTotalCount, err := service.GetManagedClustersByPage(mockCtx, tt.page, tt.where, tt.search)
			if err != nil {
				assert.Contains(t, tt.err, err)
			} else {
				assert.Equal(t, tt.expectClusters, gotCluster)
				assert.Equal(t, tt.expectTotalCount, gotTotalCount)
			}
		})
	}
}

func TestService_GetAllIstiodClusterByAccountId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testClusterNames := "clu-a"
	testClusterTypes := "primary"

	tests := []struct {
		name      string
		want      []meta.Cluster
		expectClu []meta.Cluster
		wantErr   bool
	}{
		{
			name:    "GetPrimaryCluster-test",
			wantErr: false,
			expectClu: []meta.Cluster{
				{
					ClusterName: testClusterNames,
					ClusterType: testClusterTypes,
				},
			},
			want: []meta.Cluster{
				{
					ClusterName: testClusterNames,
					ClusterType: testClusterTypes,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			s := &Service{
				opt: NewOption(mockDB),
				dao: mockDaoModel,
			}
			mockDaoModel.EXPECT().InWithWhere(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.expectClu, nil)

			got, err := s.GetAllIstiodClusterByAccountId(mockCtx, Region)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPrimaryCluster() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, &tt.want) {
				t.Errorf("GetPrimaryCluster() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetAllRemoteClusterByInstanceUUID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testInstanceIDs := []string{"inst-0", "inst-1"}
	testClusterNames := []string{"clu-a", "clu-b"}
	testClusterTypes := []string{"primary", "remote"}

	tests := []struct {
		name      string
		instID    string
		cluList   []meta.Cluster
		expectNum int64
	}{
		{
			name:   "test_getAllClusterByInstanceUUID",
			instID: "inst-0",
			cluList: []meta.Cluster{
				{
					InstanceUUID: testInstanceIDs[0],
					ClusterName:  testClusterNames[0],
					ClusterType:  testClusterTypes[0],
				},
				{
					InstanceUUID: testInstanceIDs[1],
					ClusterName:  testClusterNames[1],
					ClusterType:  testClusterTypes[1],
				},
			},
			expectNum: 1,
		},
	}

	for _, tt := range tests {
		mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
		service := &Service{
			opt: NewOption(mockDB),
			dao: mockDaoModel,
		}

		mockDaoModel.EXPECT().ListAll(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.cluList, nil)

		t.Run(tt.name, func(t *testing.T) {
			res, _ := service.GetAllRemoteClusterByInstanceUUID(mockCtx, tt.instID)
			assert.Equal(t, int64(len(*res)), tt.expectNum)
		})
	}
}

func TestService_GetAllIstiodCluster(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testClusterNames := "clu-a"
	testClusterTypes := "primary"

	tests := []struct {
		name      string
		want      []meta.Cluster
		expectClu []meta.Cluster
		wantErr   bool
	}{
		{
			name:    "GetPrimaryCluster-test",
			wantErr: false,
			expectClu: []meta.Cluster{
				{
					ClusterName: testClusterNames,
					ClusterType: testClusterTypes,
				},
			},
			want: []meta.Cluster{
				{
					ClusterName: testClusterNames,
					ClusterType: testClusterTypes,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDaoModel := daoMocks.NewMockBaseInterface(ctrl)
			s := &Service{
				opt: NewOption(mockDB),
				dao: mockDaoModel,
			}
			mockDaoModel.EXPECT().InWithWhere(mockCtx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&tt.expectClu, nil)

			got, err := s.GetAllIstiodCluster(mockCtx, Region)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPrimaryCluster() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, &tt.want) {
				t.Errorf("GetPrimaryCluster() got = %v, want %v", got, tt.want)
			}
		})
	}
}
