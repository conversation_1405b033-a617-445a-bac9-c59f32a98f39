package cluster

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster/dao"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type Service struct {
	opt *Option
	dao model.ClusterDaoInterface
}

func (s *Service) NewCluster(ctx context.CsmContext, cluster *meta.Cluster) error {
	if err := s.dao.Save(ctx, cluster); err != nil {
		return err
	}
	return nil
}

func NewClusterService(option *Option) *Service {
	return &Service{
		opt: option,
		dao: dao.NewClusterDao(option.DB),
	}
}

// WithTx 服务网格实例列表关联的集群列表
func (s *Service) WithTx(tx *dbutil.DB) ServiceInterface {
	Opt := *s.opt
	Opt.DB = tx
	return NewClusterService(&Opt)
}

// DeleteClusterByInstanceUUID 删除服务网格实例列表关联的集群实例
func (s *Service) DeleteClusterByInstanceUUID(ctx context.CsmContext, instanceUUID string) error {
	where := &meta.Cluster{
		InstanceUUID: instanceUUID,
	}
	return s.dao.BatchDelete(ctx, where)
}

func (s *Service) DeleteCluster(ctx context.CsmContext, instanceUUID, clusterId, region string) error {
	where := &meta.Cluster{
		InstanceUUID: instanceUUID,
		ClusterUUID:  clusterId,
		Region:       region,
	}
	// TODO: 批量删除
	// TODO: 单个删除 ByIDAndRegion
	return s.dao.BatchDelete(ctx, where)
}

func (s *Service) UpdateCluster(ctx context.CsmContext, instanceId, clusterId, region string,
	update *meta.Cluster) error {
	where := &meta.Cluster{
		InstanceUUID: instanceId,
		ClusterUUID:  clusterId,
		Region:       region,
	}

	old, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return err
	}

	return s.dao.Update(ctx, old, update)
}

func (s *Service) GetAllClusterByRegion(ctx context.CsmContext, region string) (*[]meta.Cluster, error) {
	accountId, _ := iam.GetAccountId(ctx)

	where := &meta.Cluster{
		Region:    region,
		AccountId: accountId,
		Deleted:   csm.Int(0),
	}
	not := &meta.Cluster{}

	clusters, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	return clusters.(*[]meta.Cluster), nil
}

// GetIstiodCluster 获取服务网格实例的控制面集群实例
func (s *Service) GetIstiodCluster(ctx context.CsmContext, instanceUUID string, instanceType string) (*meta.Cluster, error) {
	// TODO 查询条件中可补充accountId
	where := &meta.Cluster{
		Deleted:      csm.Int(0),
		InstanceUUID: instanceUUID,
		ClusterType: func(s string) string {
			if s == string(meta.StandaloneMeshType) || s == string(meta.CnapMeshType) {
				return string(meta.ClusterTypePrimary)
			}
			return string(meta.ClusterTypeExternal)
		}(instanceType),
	}

	istiodCluster, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}
	// 类型转换
	return istiodCluster.(*meta.Cluster), nil
}

func (s *Service) GetClusterByIdAndRegion(ctx context.CsmContext, instanceID, clusterID, region string) (*meta.Cluster, error) {
	// TODO 查询条件中可补充accountId
	where := &meta.Cluster{
		Deleted:      csm.Int(0),
		InstanceUUID: instanceID,
		ClusterUUID:  clusterID,
		Region:       region,
	}

	// TODO: 添加事务
	cluster, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return nil, err
	}
	// 类型转换
	return cluster.(*meta.Cluster), nil
}

// GetAllClusterByInstanceUUID 获取服务网格实例的全部集群实例信息
func (s *Service) GetAllClusterByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*[]meta.Cluster, error) {
	// TODO 查询条件中可补充accountId
	where := &meta.Cluster{
		Deleted:      csm.Int(0),
		InstanceUUID: instanceUUID,
	}
	not := &meta.Cluster{}
	ctx.CsmLogger().Infof("get clusters of instanceUUID: %s", instanceUUID)
	clusterList, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		ctx.CsmLogger().Infof("get clusters of instanceUUID: %s,have an error:%v", instanceUUID, err)
		return nil, err
	}
	if clusterList == nil {
		ctx.CsmLogger().Infof("instanceUUID: %s, clusterList is nil", instanceUUID)
		return &[]meta.Cluster{}, nil
	}
	// 类型转换
	return clusterList.(*[]meta.Cluster), nil
}

func (s *Service) GetAllRemoteClusterByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*[]meta.Cluster, error) {
	cl, err := s.GetAllClusterByInstanceUUID(ctx, instanceUUID)
	if err != nil {
		return nil, err
	}
	allRemoteCluster := make([]meta.Cluster, 0)
	for _, clu := range *cl {
		if clu.ClusterType == string(meta.ClusterTypeRemote) || clu.ClusterType == string(meta.ClusterTypeConfig) {
			allRemoteCluster = append(allRemoteCluster, clu)
		}
	}
	return &allRemoteCluster, nil
}

func (s *Service) GetManagedClustersByUser(ctx context.CsmContext) (
	clusters []meta.ManagedCluster, err error) {
	accountId, _ := iam.GetAccountId(ctx)
	where := &meta.Cluster{
		AccountId: accountId,
		Deleted:   csm.Int(0),
	}
	not := &meta.Cluster{}

	cs, err := s.dao.ListAll(ctx, nil, where, not)
	if err != nil {
		return nil, err
	}
	// 类型转换
	cl := cs.(*[]meta.Cluster)

	// TODO: 获取集群相关状态
	clusters = make([]meta.ManagedCluster, 0, len(*cl))
	for _, c := range *cl {
		managedCluster := meta.ManagedCluster{
			InstanceId:  c.InstanceUUID,
			ClusterId:   c.ClusterUUID,
			ClusterName: c.ClusterName,
			ClusterType: meta.ClusterType(c.ClusterType),
			Region:      c.Region,
			AccountId:   c.AccountId,
			AddedTime:   *c.CreateTime,
			ConnectionState: func() *meta.ClusterConnectionState {
				if len(c.ConnectionState) > 0 {
					ccs := meta.ClusterConnectionState(c.ConnectionState)
					return &ccs
				}
				ccs := meta.ClusterNotConnectedState
				return &ccs
			}(),
		}
		clusters = append(clusters, managedCluster)
	}

	return clusters, nil
}
func (s *Service) GetManagedClustersByPage(ctx context.CsmContext, p *meta.Page, where, search *meta.Cluster) (
	clusters []*meta.ManagedCluster, totalCount int64, err error) {
	// TODO: 待修改，where 和 search 只暴露在 model 层
	where.Deleted = csm.Int(0)
	pr, cs, err := s.dao.ListPage(ctx, search, where, p.OrderBy, dbutil.DBOrder(p.Order), p.PageSize, p.PageNo)
	if err != nil {
		// TODO: 添加定制错误
		return nil, 0, err
	}
	// 类型转换
	cl := cs.(*[]meta.Cluster)

	// TODO: 获取集群相关状态
	clusters = make([]*meta.ManagedCluster, 0, len(*cl))
	for _, c := range *cl {
		// 集群管理列表忽略托管 external 类型集群
		if c.ClusterType == string(meta.ClusterTypeExternal) {
			if pr.TotalCount > 0 {
				pr.TotalCount--
			}
			continue
		}
		managedCluster := &meta.ManagedCluster{
			InstanceId:  c.InstanceUUID,
			ClusterId:   c.ClusterUUID,
			ClusterName: c.ClusterName,
			ClusterType: meta.ClusterType(c.ClusterType),
			Region:      c.Region,
			AccountId:   c.AccountId,
			AddedTime:   *c.CreateTime,
			ConnectionState: func() *meta.ClusterConnectionState {
				if len(c.ConnectionState) > 0 {
					ccs := meta.ClusterConnectionState(c.ConnectionState)
					return &ccs
				}
				ccs := meta.ClusterNotConnectedState
				return &ccs
			}(),
		}
		clusters = append(clusters, managedCluster)
	}

	return clusters, pr.TotalCount, nil
}

// GetRegionByClusterUUID 根据clusterUUID获取集群region
func (s *Service) GetRegionByClusterUUID(ctx context.CsmContext, clusterUUID string) (string, error) {
	where := &meta.Cluster{
		Deleted:     csm.Int(0),
		ClusterUUID: clusterUUID,
	}
	clusterInfo, err := s.dao.LoadWithWhere(ctx, where)
	if err != nil {
		return "", err
	}
	region := clusterInfo.(*meta.Cluster).Region
	return region, nil
}

// GetAllIstiodClusterByAccountId 获取当前AccountId下所有实例的主集群列表
// clusterType: 仅支持 primary、external 和 ""。 其中""表示查询primary和external集群的并集
func (s *Service) GetAllIstiodClusterByAccountId(ctx context.CsmContext, clusterType string) (*[]meta.Cluster, error) {
	accountId, _ := iam.GetAccountId(ctx)
	where := &meta.Cluster{
		AccountId: accountId,
		Deleted:   csm.Int(0),
	}
	allIstiodClusters, err := s.getIstiodClusters(ctx, where, clusterType)
	if err != nil {
		return nil, err
	}
	return allIstiodClusters, nil
}

// GetAllIstiodCluster 获取所有的实例主集群列表
func (s *Service) GetAllIstiodCluster(ctx context.CsmContext, clusterType string) (*[]meta.Cluster, error) {
	where := &meta.Cluster{
		Deleted:     csm.Int(0),
		ClusterType: clusterType,
	}
	allIstiodClusters, err := s.getIstiodClusters(ctx, where, clusterType)
	if err != nil {
		return nil, err
	}
	return allIstiodClusters, nil
}

func (s *Service) getIstiodClusters(ctx context.CsmContext, where *meta.Cluster, clusterType string) (*[]meta.Cluster, error) {
	var inString []interface{}
	if clusterType != "" {
		inString = append(inString, clusterType)
	} else {
		inString = append(inString, string(meta.ClusterTypePrimary), string(meta.ClusterTypeExternal))
	}

	clusterInfo, err := s.dao.InWithWhere(ctx, constants.ClusterType, inString, where)
	if err != nil {
		return nil, err
	}

	cl := clusterInfo.(*[]meta.Cluster)
	return cl, nil
}
