package cluster

import (
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

type ServiceInterface interface {
	WithTx(tx *dbutil.DB) ServiceInterface
	NewCluster(ctx context.CsmContext, clusters *meta.Cluster) error
	DeleteClusterByInstanceUUID(ctx context.CsmContext, instanceUUID string) error
	DeleteCluster(ctx context.CsmContext, instanceUUID, clusterId, region string) error
	UpdateCluster(ctx context.CsmContext, instanceId, clusterId, region string, update *meta.Cluster) error
	GetIstiodCluster(ctx context.CsmContext, instanceUUID, instanceType string) (*meta.Cluster, error)
	GetClusterByIdAndRegion(ctx context.CsmContext, instanceID, clusterID, region string) (*meta.Cluster, error)
	GetAllClusterByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*[]meta.Cluster, error)
	GetAllClusterByRegion(ctx context.CsmContext, region string) (*[]meta.Cluster, error)
	GetAllRemoteClusterByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*[]meta.Cluster, error)

	GetManagedClustersByUser(ctx context.CsmContext) (clusters []meta.ManagedCluster, err error)
	GetManagedClustersByPage(ctx context.CsmContext, p *meta.Page, where, search *meta.Cluster) (
		clusters []*meta.ManagedCluster, totalCount int64, err error)
	GetRegionByClusterUUID(ctx context.CsmContext, clusterUUID string) (string, error)
	GetAllIstiodClusterByAccountId(ctx context.CsmContext, clusterType string) (*[]meta.Cluster, error)
	GetAllIstiodCluster(ctx context.CsmContext, clusterType string) (*[]meta.Cluster, error)
}
