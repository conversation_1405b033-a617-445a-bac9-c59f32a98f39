// Code generated by MockGen. DO NOT EDIT.
// Source: ./interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	cluster "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/cluster"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	dbutil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/dbutil"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// DeleteCluster mocks base method.
func (m *MockServiceInterface) DeleteCluster(ctx context.CsmContext, instanceUUID, clusterId, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCluster", ctx, instanceUUID, clusterId, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteCluster indicates an expected call of DeleteCluster.
func (mr *MockServiceInterfaceMockRecorder) DeleteCluster(ctx, instanceUUID, clusterId, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCluster", reflect.TypeOf((*MockServiceInterface)(nil).DeleteCluster), ctx, instanceUUID, clusterId, region)
}

// DeleteClusterByInstanceUUID mocks base method.
func (m *MockServiceInterface) DeleteClusterByInstanceUUID(ctx context.CsmContext, instanceUUID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteClusterByInstanceUUID", ctx, instanceUUID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteClusterByInstanceUUID indicates an expected call of DeleteClusterByInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) DeleteClusterByInstanceUUID(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteClusterByInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).DeleteClusterByInstanceUUID), ctx, instanceUUID)
}

// GetAllClusterByInstanceUUID mocks base method.
func (m *MockServiceInterface) GetAllClusterByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*[]meta.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllClusterByInstanceUUID", ctx, instanceUUID)
	ret0, _ := ret[0].(*[]meta.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllClusterByInstanceUUID indicates an expected call of GetAllClusterByInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) GetAllClusterByInstanceUUID(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllClusterByInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).GetAllClusterByInstanceUUID), ctx, instanceUUID)
}

// GetAllClusterByRegion mocks base method.
func (m *MockServiceInterface) GetAllClusterByRegion(ctx context.CsmContext, region string) (*[]meta.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllClusterByRegion", ctx, region)
	ret0, _ := ret[0].(*[]meta.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllClusterByRegion indicates an expected call of GetAllClusterByRegion.
func (mr *MockServiceInterfaceMockRecorder) GetAllClusterByRegion(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllClusterByRegion", reflect.TypeOf((*MockServiceInterface)(nil).GetAllClusterByRegion), ctx, region)
}

// GetAllIstiodCluster mocks base method.
func (m *MockServiceInterface) GetAllIstiodCluster(ctx context.CsmContext, clusterType string) (*[]meta.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllIstiodCluster", ctx, clusterType)
	ret0, _ := ret[0].(*[]meta.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllIstiodCluster indicates an expected call of GetAllIstiodCluster.
func (mr *MockServiceInterfaceMockRecorder) GetAllIstiodCluster(ctx, clusterType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllIstiodCluster", reflect.TypeOf((*MockServiceInterface)(nil).GetAllIstiodCluster), ctx, clusterType)
}

// GetAllIstiodClusterByAccountId mocks base method.
func (m *MockServiceInterface) GetAllIstiodClusterByAccountId(ctx context.CsmContext, clusterType string) (*[]meta.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllIstiodClusterByAccountId", ctx, clusterType)
	ret0, _ := ret[0].(*[]meta.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllIstiodClusterByAccountId indicates an expected call of GetAllIstiodClusterByAccountId.
func (mr *MockServiceInterfaceMockRecorder) GetAllIstiodClusterByAccountId(ctx, clusterType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllIstiodClusterByAccountId", reflect.TypeOf((*MockServiceInterface)(nil).GetAllIstiodClusterByAccountId), ctx, clusterType)
}

// GetAllRemoteClusterByInstanceUUID mocks base method.
func (m *MockServiceInterface) GetAllRemoteClusterByInstanceUUID(ctx context.CsmContext, instanceUUID string) (*[]meta.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllRemoteClusterByInstanceUUID", ctx, instanceUUID)
	ret0, _ := ret[0].(*[]meta.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllRemoteClusterByInstanceUUID indicates an expected call of GetAllRemoteClusterByInstanceUUID.
func (mr *MockServiceInterfaceMockRecorder) GetAllRemoteClusterByInstanceUUID(ctx, instanceUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRemoteClusterByInstanceUUID", reflect.TypeOf((*MockServiceInterface)(nil).GetAllRemoteClusterByInstanceUUID), ctx, instanceUUID)
}

// GetClusterByIdAndRegion mocks base method.
func (m *MockServiceInterface) GetClusterByIdAndRegion(ctx context.CsmContext, instanceID, clusterID, region string) (*meta.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterByIdAndRegion", ctx, instanceID, clusterID, region)
	ret0, _ := ret[0].(*meta.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterByIdAndRegion indicates an expected call of GetClusterByIdAndRegion.
func (mr *MockServiceInterfaceMockRecorder) GetClusterByIdAndRegion(ctx, instanceID, clusterID, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterByIdAndRegion", reflect.TypeOf((*MockServiceInterface)(nil).GetClusterByIdAndRegion), ctx, instanceID, clusterID, region)
}

// GetIstiodCluster mocks base method.
func (m *MockServiceInterface) GetIstiodCluster(ctx context.CsmContext, instanceUUID, instanceType string) (*meta.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIstiodCluster", ctx, instanceUUID, instanceType)
	ret0, _ := ret[0].(*meta.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIstiodCluster indicates an expected call of GetIstiodCluster.
func (mr *MockServiceInterfaceMockRecorder) GetIstiodCluster(ctx, instanceUUID, instanceType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIstiodCluster", reflect.TypeOf((*MockServiceInterface)(nil).GetIstiodCluster), ctx, instanceUUID, instanceType)
}

// GetManagedClustersByPage mocks base method.
func (m *MockServiceInterface) GetManagedClustersByPage(ctx context.CsmContext, p *meta.Page, where, search *meta.Cluster) ([]*meta.ManagedCluster, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManagedClustersByPage", ctx, p, where, search)
	ret0, _ := ret[0].([]*meta.ManagedCluster)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetManagedClustersByPage indicates an expected call of GetManagedClustersByPage.
func (mr *MockServiceInterfaceMockRecorder) GetManagedClustersByPage(ctx, p, where, search interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManagedClustersByPage", reflect.TypeOf((*MockServiceInterface)(nil).GetManagedClustersByPage), ctx, p, where, search)
}

// GetManagedClustersByUser mocks base method.
func (m *MockServiceInterface) GetManagedClustersByUser(ctx context.CsmContext) ([]meta.ManagedCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManagedClustersByUser", ctx)
	ret0, _ := ret[0].([]meta.ManagedCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManagedClustersByUser indicates an expected call of GetManagedClustersByUser.
func (mr *MockServiceInterfaceMockRecorder) GetManagedClustersByUser(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManagedClustersByUser", reflect.TypeOf((*MockServiceInterface)(nil).GetManagedClustersByUser), ctx)
}

// GetRegionByClusterUUID mocks base method.
func (m *MockServiceInterface) GetRegionByClusterUUID(ctx context.CsmContext, clusterUUID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRegionByClusterUUID", ctx, clusterUUID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRegionByClusterUUID indicates an expected call of GetRegionByClusterUUID.
func (mr *MockServiceInterfaceMockRecorder) GetRegionByClusterUUID(ctx, clusterUUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRegionByClusterUUID", reflect.TypeOf((*MockServiceInterface)(nil).GetRegionByClusterUUID), ctx, clusterUUID)
}

// NewCluster mocks base method.
func (m *MockServiceInterface) NewCluster(ctx context.CsmContext, clusters *meta.Cluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewCluster", ctx, clusters)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewCluster indicates an expected call of NewCluster.
func (mr *MockServiceInterfaceMockRecorder) NewCluster(ctx, clusters interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewCluster", reflect.TypeOf((*MockServiceInterface)(nil).NewCluster), ctx, clusters)
}

// UpdateCluster mocks base method.
func (m *MockServiceInterface) UpdateCluster(ctx context.CsmContext, instanceId, clusterId, region string, update *meta.Cluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCluster", ctx, instanceId, clusterId, region, update)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCluster indicates an expected call of UpdateCluster.
func (mr *MockServiceInterfaceMockRecorder) UpdateCluster(ctx, instanceId, clusterId, region, update interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCluster", reflect.TypeOf((*MockServiceInterface)(nil).UpdateCluster), ctx, instanceId, clusterId, region, update)
}

// WithTx mocks base method.
func (m *MockServiceInterface) WithTx(tx *dbutil.DB) cluster.ServiceInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(cluster.ServiceInterface)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockServiceInterfaceMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockServiceInterface)(nil).WithTx), tx)
}
