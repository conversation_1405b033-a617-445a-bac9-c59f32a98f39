package model

import "github.com/go-openapi/strfmt"

type CreateProjectRequest struct {
	// 命名空间名称.
	ProjectName string `json:"projectName" binding:"required"`
	// The public status of the project. The valid values are "true", "false".
	Public string `json:"public" binding:"required"`
}

type UpdateProjectRequest struct {
	// The public status of the project. The valid values are "true", "false".
	Public string `json:"public" binding:"required"`
	// Whether scan images automatically when pushing. The valid values are "true", "false".
	AutoScan *string `json:"autoScan" binding:"required"`
}

type ListProjectResponse struct {
	PageInfo `json:",inline"`
	Projects []*ProjectResult `json:"projects"`
}

// ProjectReq project req
type ProjectReq struct {

	// The name of the project.
	ProjectName string `json:"projectName"`

	// Whether scan images automatically when pushing. The valid values are "true", "false".
	AutoScan *string `json:"autoScan"`

	// The public status of the project. The valid values are "true", "false".
	Public string `json:"public"`
}

// ProjectResult project
type ProjectResult struct {

	// The total number of charts under this project.
	ChartCount int64 `json:"chartCount"`

	// The creation time of the project.
	// Format: date-time
	CreationTime strfmt.DateTime `json:"creationTime"`

	// The name of the project.
	ProjectName string `json:"projectName"`

	// Project ID
	ProjectID int32 `json:"projectId"`

	// The number of the repositories under this project.
	RepoCount int64 `json:"repoCount"`

	// The update time of the project.
	// Format: date-time
	UpdateTime strfmt.DateTime `json:"updateTime"`

	// Whether scan images automatically when pushing. The valid values are "true", "false".
	AutoScan string `json:"autoScan"`

	// The public status of the project. The valid values are "true", "false".
	Public string `json:"public"`
}

type MemberUser struct {
	UserID   int64  `json:"userID"`
	Username string `json:"username"`
}

type CreateProjectMemberRequest struct {
	RoleID     int64      `json:"roleID"`
	MemberUser MemberUser `json:"memberUser"`
}

type ProjectMemberResult struct {
	ID         int64  `json:"id"`
	ProjectID  int64  `json:"projectID"`
	EntityName string `json:"entityName"`
	RoleName   string `json:"roleName"`
	RoleID     int64  `json:"roleID"`
	EntityID   int64  `json:"entityID"`
	EntityType string `json:"entityType"`
}
type ListProjectMemberResponse struct {
	PageInfo `json:",inline"`
	Items    []*ProjectMemberResult `json:"items"`
}
