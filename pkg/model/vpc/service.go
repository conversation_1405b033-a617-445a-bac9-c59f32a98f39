package vpc

import (
	"fmt"
	"strconv"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
	"github.com/baidubce/bce-sdk-go/services/esg"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"

	bccApi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/baidubce/bce-sdk-go/services/endpoint"
	"github.com/baidubce/bce-sdk-go/services/vpc"

	bceBcc "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/bcc"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/util"
	bceVpc "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/vpc"
	bceEndpoint "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/vpc/endpoint"
	bceEsg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/vpc/esg"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

const (
	URI_PREFIX = bce.URI_PREFIX + "v1"

	REQUEST_ENDPOINT_URL = "/endpoint"
)

type Service struct {
	option *Option
}

func NewService(opt *Option) *Service {
	return &Service{
		option: opt,
	}
}

func (service *Service) ListVPC(ctx csmContext.CsmContext, args *vpc.ListVPCArgs,
	region string) (*vpc.ListVPCResult, error) {
	client, err := bceVpc.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return nil, err
	}
	return client.ListVPC(args)
}

func (service *Service) GetVPCDetail(ctx csmContext.CsmContext, vpcId,
	region string) (*vpc.GetVPCDetailResult, error) {
	client, err := bceVpc.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return nil, err
	}
	return client.GetVPCDetail(vpcId)
}

func (service *Service) ListSubnet(ctx csmContext.CsmContext, args *vpc.ListSubnetArgs,
	region string) (*vpc.ListSubnetResult, error) {
	client, err := bceVpc.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return nil, err
	}
	return client.ListSubnets(args)
}

func (service *Service) ListSecurityGroup(ctx csmContext.CsmContext, args *bccApi.ListSecurityGroupArgs,
	region string) (*bccApi.ListSecurityGroupResult, error) {
	client, err := bceBcc.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return nil, err
	}
	return client.ListSecurityGroup(args)
}

func (service *Service) CreateEndpoint(ctx csmContext.CsmContext, args *endpoint.CreateEndpointArgs,
	region string) (*endpoint.CreateEndpointResult, error) {
	client, err := bceEndpoint.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return nil, err
	}
	return client.CreateEndpoint(args)
}

func (service *Service) DeleteEndpoint(ctx csmContext.CsmContext, endpointId, region string) error {
	client, err := bceEndpoint.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return err
	}
	clientToken := util.GetClientToken()
	ctx.CsmLogger().Infof("endpointId=%s,region=%s,clientToken=%s", endpointId, region, clientToken)
	return client.DeleteEndpoint(endpointId, clientToken)
}

func (service *Service) GetEndpointDetail(ctx csmContext.CsmContext, endpointId,
	region string) (*endpoint.Endpoint, error) {
	client, err := bceEndpoint.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return nil, err
	}
	return client.GetEndpointDetail(endpointId)
}

func (service *Service) ListEndpoints(ctx csmContext.CsmContext, args *endpoint.ListEndpointArgs,
	region string) (*endpoint.ListEndpointResult, error) {
	client, err := bceEndpoint.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return nil, err
	}
	return client.ListEndpoints(args)
}

func getURLForEndpoint() string {
	return URI_PREFIX + REQUEST_ENDPOINT_URL
}

func (service *Service) CreateEndpointWithEip(ctx csmContext.CsmContext, args *meta.CreateEndpointArgs,
	region string) (*endpoint.CreateEndpointResult, error) {
	client, err := bceEndpoint.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return nil, err
	}

	result := &endpoint.CreateEndpointResult{}
	err = bce.NewRequestBuilder(client).
		WithURL(getURLForEndpoint()).
		WithMethod(http.POST).
		WithBody(args).
		WithQueryParamFilter("clientToken", args.ClientToken).
		WithResult(result).
		Do()

	return result, err
}

func (service *Service) ListEndpointsWithEip(ctx csmContext.CsmContext, args *endpoint.ListEndpointArgs,
	region string) (*meta.ListEndpointResult, error) {
	client, err := bceEndpoint.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return nil, err
	}

	if args.MaxKeys == 0 {
		args.MaxKeys = 1000
	}

	result := &meta.ListEndpointResult{}
	err = bce.NewRequestBuilder(client).
		WithURL(getURLForEndpoint()).
		WithMethod(http.GET).
		WithQueryParam("vpcId", args.VpcId).
		WithQueryParam("name", args.Name).
		WithQueryParam("ipAddress", args.IpAddress).
		WithQueryParam("status", args.Status).
		WithQueryParam("subnetId", args.SubnetId).
		WithQueryParam("service", args.Service).
		WithQueryParamFilter("marker", args.Marker).
		WithQueryParamFilter("maxKeys", strconv.Itoa(args.MaxKeys)).
		WithResult(result).
		Do()

	return result, err
}

func (service *Service) ListEsg(ctx csmContext.CsmContext, args *esg.ListEsgArgs,
	region string) (*esg.ListEsgResult, error) {
	client, err := bceEsg.GetClient(ctx, fmt.Sprintf(service.option.VpcEndpoint, region))
	if err != nil {
		return nil, err
	}
	return client.ListEsg(args)
}
