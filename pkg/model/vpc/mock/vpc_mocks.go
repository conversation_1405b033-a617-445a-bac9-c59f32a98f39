// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	api "github.com/baidubce/bce-sdk-go/services/bcc/api"
	endpoint "github.com/baidubce/bce-sdk-go/services/endpoint"
	"github.com/baidubce/bce-sdk-go/services/esg"
	vpc "github.com/baidubce/bce-sdk-go/services/vpc"
	gomock "github.com/golang/mock/gomock"
	meta "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateEndpoint mocks base method.
func (m *MockServiceInterface) CreateEndpoint(ctx context.CsmContext, args *endpoint.CreateEndpointArgs, region string) (*endpoint.CreateEndpointResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEndpoint", ctx, args, region)
	ret0, _ := ret[0].(*endpoint.CreateEndpointResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEndpoint indicates an expected call of CreateEndpoint.
func (mr *MockServiceInterfaceMockRecorder) CreateEndpoint(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEndpoint", reflect.TypeOf((*MockServiceInterface)(nil).CreateEndpoint), ctx, args, region)
}

// CreateEndpointWithEip mocks base method.
func (m *MockServiceInterface) CreateEndpointWithEip(ctx context.CsmContext, args *meta.CreateEndpointArgs, region string) (*endpoint.CreateEndpointResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEndpointWithEip", ctx, args, region)
	ret0, _ := ret[0].(*endpoint.CreateEndpointResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEndpointWithEip indicates an expected call of CreateEndpointWithEip.
func (mr *MockServiceInterfaceMockRecorder) CreateEndpointWithEip(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEndpointWithEip", reflect.TypeOf((*MockServiceInterface)(nil).CreateEndpointWithEip), ctx, args, region)
}

// DeleteEndpoint mocks base method.
func (m *MockServiceInterface) DeleteEndpoint(ctx context.CsmContext, endpointId, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEndpoint", ctx, endpointId, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteEndpoint indicates an expected call of DeleteEndpoint.
func (mr *MockServiceInterfaceMockRecorder) DeleteEndpoint(ctx, endpointId, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEndpoint", reflect.TypeOf((*MockServiceInterface)(nil).DeleteEndpoint), ctx, endpointId, region)
}

// GetEndpointDetail mocks base method.
func (m *MockServiceInterface) GetEndpointDetail(ctx context.CsmContext, endpointId, region string) (*endpoint.Endpoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEndpointDetail", ctx, endpointId, region)
	ret0, _ := ret[0].(*endpoint.Endpoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEndpointDetail indicates an expected call of GetEndpointDetail.
func (mr *MockServiceInterfaceMockRecorder) GetEndpointDetail(ctx, endpointId, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEndpointDetail", reflect.TypeOf((*MockServiceInterface)(nil).GetEndpointDetail), ctx, endpointId, region)
}

// GetVPCDetail mocks base method.
func (m *MockServiceInterface) GetVPCDetail(ctx context.CsmContext, vpcId, region string) (*vpc.GetVPCDetailResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVPCDetail", ctx, vpcId, region)
	ret0, _ := ret[0].(*vpc.GetVPCDetailResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVPCDetail indicates an expected call of GetVPCDetail.
func (mr *MockServiceInterfaceMockRecorder) GetVPCDetail(ctx, vpcId, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVPCDetail", reflect.TypeOf((*MockServiceInterface)(nil).GetVPCDetail), ctx, vpcId, region)
}

// ListEndpoints mocks base method.
func (m *MockServiceInterface) ListEndpoints(ctx context.CsmContext, args *endpoint.ListEndpointArgs, region string) (*endpoint.ListEndpointResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListEndpoints", ctx, args, region)
	ret0, _ := ret[0].(*endpoint.ListEndpointResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEndpoints indicates an expected call of ListEndpoints.
func (mr *MockServiceInterfaceMockRecorder) ListEndpoints(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEndpoints", reflect.TypeOf((*MockServiceInterface)(nil).ListEndpoints), ctx, args, region)
}

// ListEndpointsWithEip mocks base method.
func (m *MockServiceInterface) ListEndpointsWithEip(ctx context.CsmContext, args *endpoint.ListEndpointArgs, region string) (*meta.ListEndpointResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListEndpointsWithEip", ctx, args, region)
	ret0, _ := ret[0].(*meta.ListEndpointResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEndpointsWithEip indicates an expected call of ListEndpointsWithEip.
func (mr *MockServiceInterfaceMockRecorder) ListEndpointsWithEip(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEndpointsWithEip", reflect.TypeOf((*MockServiceInterface)(nil).ListEndpointsWithEip), ctx, args, region)
}

// ListSecurityGroup mocks base method.
func (m *MockServiceInterface) ListSecurityGroup(ctx context.CsmContext, args *api.ListSecurityGroupArgs, region string) (*api.ListSecurityGroupResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSecurityGroup", ctx, args, region)
	ret0, _ := ret[0].(*api.ListSecurityGroupResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSecurityGroup indicates an expected call of ListSecurityGroup.
func (mr *MockServiceInterfaceMockRecorder) ListSecurityGroup(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSecurityGroup", reflect.TypeOf((*MockServiceInterface)(nil).ListSecurityGroup), ctx, args, region)
}

// ListSubnet mocks base method.
func (m *MockServiceInterface) ListSubnet(ctx context.CsmContext, args *vpc.ListSubnetArgs, region string) (*vpc.ListSubnetResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSubnet", ctx, args, region)
	ret0, _ := ret[0].(*vpc.ListSubnetResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSubnet indicates an expected call of ListSubnet.
func (mr *MockServiceInterfaceMockRecorder) ListSubnet(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSubnet", reflect.TypeOf((*MockServiceInterface)(nil).ListSubnet), ctx, args, region)
}

// ListVPC mocks base method.
func (m *MockServiceInterface) ListVPC(ctx context.CsmContext, args *vpc.ListVPCArgs, region string) (*vpc.ListVPCResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListVPC", ctx, args, region)
	ret0, _ := ret[0].(*vpc.ListVPCResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListVPC indicates an expected call of ListVPC.
func (mr *MockServiceInterfaceMockRecorder) ListVPC(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListVPC", reflect.TypeOf((*MockServiceInterface)(nil).ListVPC), ctx, args, region)
}

// ListEsg mocks base method.
func (m *MockServiceInterface) ListEsg(ctx context.CsmContext, args *esg.ListEsgArgs,
	region string) (*esg.ListEsgResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListEsg", ctx, args, region)
	ret0, _ := ret[0].(*esg.ListEsgResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEsg indicates an expected call of ListEsg.
func (mr *MockServiceInterfaceMockRecorder) ListEsg(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEsg", reflect.TypeOf((*MockServiceInterface)(nil).ListEsg), ctx, args, region)
}
