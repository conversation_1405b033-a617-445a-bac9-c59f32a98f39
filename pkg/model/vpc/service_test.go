package vpc

import (
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/baidubce/bce-sdk-go/services/bcc"
	bccApi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/baidubce/bce-sdk-go/services/endpoint"
	"github.com/baidubce/bce-sdk-go/services/vpc"
	"github.com/stretchr/testify/assert"

	bceBcc "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/bcc"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/util"
	bceVpc "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/vpc"
	bceEndpoint "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/vpc/endpoint"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	fakeVpcClient         *vpc.Client
	fakeVpcEndpointClient *endpoint.Client
	fakeBccClient         *bcc.Client
	mockCtx, _            = ctxCsm.NewCsmContextMock()
	region                = "bj"
)

func init() {
	fakeVpcClient = &vpc.Client{}
	fakeVpcEndpointClient = &endpoint.Client{}
	fakeBccClient = &bcc.Client{}
}

func TestListVPC(t *testing.T) {
	patches := gomonkey.ApplyFunc(bceVpc.GetClient,
		func(_ ctxCsm.CsmContext, _ string) (*vpc.Client, error) {
			return fakeVpcClient, nil
		})
	defer patches.Reset()

	patches.ApplyMethod(fakeVpcClient, "ListVPC",
		func(_ *vpc.Client, _ *vpc.ListVPCArgs) (*vpc.ListVPCResult, error) {
			return nil, nil
		})
	service := &Service{
		option: &Option{
			VpcEndpoint: "%s",
		},
	}
	_, err := service.ListVPC(mockCtx, nil, region)
	assert.Nil(t, err)

}

func TestListSubnet(t *testing.T) {
	patches := gomonkey.ApplyFunc(bceVpc.GetClient,
		func(_ ctxCsm.CsmContext, _ string) (*vpc.Client, error) {
			return fakeVpcClient, nil
		})
	defer patches.Reset()

	patches.ApplyMethod(fakeVpcClient, "ListSubnets",
		func(_ *vpc.Client, _ *vpc.ListSubnetArgs) (*vpc.ListSubnetResult, error) {
			return nil, nil
		})
	service := &Service{
		option: &Option{
			VpcEndpoint: "%s",
		},
	}
	_, err := service.ListSubnet(mockCtx, nil, region)
	assert.Nil(t, err)
}

func TestListSecurityGroup(t *testing.T) {
	testInfos := []struct {
		name      string
		localMode bool
		expectErr error
	}{
		{
			name:      "ListSecurityGroup-ok",
			localMode: true,
			expectErr: nil,
		},
	}
	for _, testInfo := range testInfos {
		t.Run(testInfo.name, func(t *testing.T) {
			patches := gomonkey.ApplyFunc(bceBcc.GetClient,
				func(_ ctxCsm.CsmContext, _ string) (*bcc.Client, error) {
					return fakeBccClient, nil
				})
			defer patches.Reset()

			patches.ApplyMethod(fakeBccClient, "ListSecurityGroup",
				func(_ *bcc.Client, _ *bccApi.ListSecurityGroupArgs) (*bccApi.ListSecurityGroupResult, error) {
					return nil, nil
				})

			service := &Service{
				option: &Option{
					VpcEndpoint: "%s",
				},
			}
			_, err := service.ListSecurityGroup(mockCtx, nil, region)
			assert.Nil(t, err)
		})
	}
}

func TestCreateEndpoint(t *testing.T) {
	patches := gomonkey.ApplyFunc(bceEndpoint.GetClient,
		func(_ ctxCsm.CsmContext, _ string) (*endpoint.Client, error) {
			return fakeVpcEndpointClient, nil
		})
	defer patches.Reset()

	patches.ApplyMethod(fakeVpcEndpointClient, "CreateEndpoint",
		func(_ *endpoint.Client, _ *endpoint.CreateEndpointArgs) (*endpoint.CreateEndpointResult, error) {
			return nil, nil
		})

	service := &Service{
		option: &Option{
			VpcEndpoint: "xxx",
		},
	}
	args := &endpoint.CreateEndpointArgs{
		ClientToken: util.GetClientToken(),
		VpcId:       "xxx",
		Name:        "xxx",
		SubnetId:    "xxx",
		Service:     "xxx",
		Billing: &endpoint.Billing{
			PaymentTiming: "Postpaid",
		},
	}
	_, err := service.CreateEndpoint(mockCtx, args, region)
	assert.Nil(t, err)
}

func TestDeleteEndpoint(t *testing.T) {
	patches := gomonkey.ApplyFunc(bceEndpoint.GetClient,
		func(_ ctxCsm.CsmContext, _ string) (*endpoint.Client, error) {
			return fakeVpcEndpointClient, nil
		})
	defer patches.Reset()

	patches.ApplyMethod(fakeVpcEndpointClient, "DeleteEndpoint",
		func(_ *endpoint.Client, _ string, _ string) error {
			return nil
		})

	service := &Service{
		option: &Option{
			VpcEndpoint: "xxx",
		},
	}

	err := service.DeleteEndpoint(mockCtx, "xxx", region)
	assert.Nil(t, err)
}

func TestGetEndpointDetail(t *testing.T) {
	patches := gomonkey.ApplyFunc(bceEndpoint.GetClient,
		func(_ ctxCsm.CsmContext, _ string) (*endpoint.Client, error) {
			return fakeVpcEndpointClient, nil
		})
	defer patches.Reset()

	patches.ApplyMethod(fakeVpcEndpointClient, "GetEndpointDetail",
		func(_ *endpoint.Client, _ string) (*endpoint.Endpoint, error) {
			return nil, nil
		})

	service := &Service{
		option: &Option{
			VpcEndpoint: "xxx",
		},
	}

	_, err := service.GetEndpointDetail(mockCtx, "xxx", region)
	assert.Nil(t, err)
}

func TestListEndpoints(t *testing.T) {
	patches := gomonkey.ApplyFunc(bceEndpoint.GetClient,
		func(_ ctxCsm.CsmContext, _ string) (*endpoint.Client, error) {
			return fakeVpcEndpointClient, nil
		})
	defer patches.Reset()

	patches.ApplyMethod(fakeVpcEndpointClient, "ListEndpoints",
		func(_ *endpoint.Client, _ *endpoint.ListEndpointArgs) (*endpoint.ListEndpointResult, error) {
			return nil, nil
		})

	service := &Service{
		option: &Option{
			VpcEndpoint: "xxx",
		},
	}

	args := &endpoint.ListEndpointArgs{
		VpcId: "xxx",
	}

	_, err := service.ListEndpoints(mockCtx, args, region)
	assert.Nil(t, err)
}
