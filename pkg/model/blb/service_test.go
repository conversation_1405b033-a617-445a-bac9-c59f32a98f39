package blb

import (
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/baidubce/bce-sdk-go/model"
	"github.com/baidubce/bce-sdk-go/services/appblb"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb"
	blbService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/tag"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	ctxCsm "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

var (
	mockCtx, _ = ctxCsm.NewCsmContextMock()
	region     = "bj"
)

func TestGetBlb(t *testing.T) {
	patches := gomonkey.ApplyFunc(blb.NewClient, func(_ context.CsmContext, _ string, _ string) (
		*appblb.Client, error) {
		return &appblb.Client{}, nil
	})
	defer patches.Reset()

	var c *appblb.Client
	patches.ApplyMethod(c, "DescribeLoadBalancerDetail", func(_ *appblb.Client, _ string) (
		*appblb.DescribeLoadBalancerDetailResult, error) {
		return nil, nil
	})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	data, err := service.GetBlb(mockCtx, "blb-id-1", "bj")
	assert.Nil(t, data)
	assert.Nil(t, err)
	t.Log(data)
}

func TestReleaseBlb(t *testing.T) {
	patches := gomonkey.ApplyFunc(blb.NewClient, func(_ context.CsmContext, _ string, _ string) (
		*appblb.Client, error) {
		return &appblb.Client{}, nil
	})
	defer patches.Reset()

	var c *appblb.Client
	patches.ApplyMethod(c, "DeleteLoadBalancer", func(_ *appblb.Client, _ string) error {
		return nil
	})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	err := service.ReleaseBlb(mockCtx, "blb-id-1", "bj")
	assert.Nil(t, err)
}

func TestCreateAppIpGroup(t *testing.T) {
	patches := gomonkey.ApplyFunc(blb.NewClient, func(_ context.CsmContext, _ string, _ string) (
		*appblb.Client, error) {
		return &appblb.Client{}, nil
	})
	defer patches.Reset()

	var c *appblb.Client
	patches.ApplyMethod(c, "CreateAppIpGroup", func(_ *appblb.Client, _ string, _ *appblb.CreateAppIpGroupArgs) (
		*appblb.CreateAppIpGroupResult, error) {
		return &appblb.CreateAppIpGroupResult{
			Id: "ip-group-id-1",
		}, nil
	})
	patches.ApplyMethod(c, "CreateAppIpGroupBackendPolicy", func(_ *appblb.Client, _ string, _ *appblb.CreateAppIpGroupBackendPolicyArgs) error {
		return nil
	})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	data, err := service.CreateAppIpGroup(mockCtx, "blb-id-1", "bj", nil)
	assert.Equal(t, data.Id, "ip-group-id-1")
	assert.Nil(t, err)
	t.Log(data)
}

func TestBindTag(t *testing.T) {
	c := &tag.Client{}
	patches := gomonkey.ApplyFunc(tag.GetClient, func(_ context.CsmContext, _ string, _ string) (
		*tag.Client, error) {
		return c, nil
	})
	defer patches.Reset()

	patches.ApplyMethod(c, "BindResource", func(_ *tag.Client, _ *tag.BindResourceArgs) error {
		return nil
	})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	err := service.BindTag(mockCtx, "blb-id-1", "bj", &model.TagModel{
		TagKey:   "tag-key",
		TagValue: "tag-value",
	})
	assert.Nil(t, err)
}

func TestUnBindTag(t *testing.T) {
	c := &tag.Client{}
	patches := gomonkey.ApplyFunc(tag.GetClient, func(_ context.CsmContext, _ string, _ string) (
		*tag.Client, error) {
		return c, nil
	})
	defer patches.Reset()

	patches.ApplyMethod(c, "UnBindResource", func(_ *tag.Client, _ *tag.BindResourceArgs) error {
		return nil
	})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	err := service.UnBindTag(mockCtx, "blb-id-1", "bj", &model.TagModel{
		TagKey:   "tag-key",
		TagValue: "tag-value",
	})
	assert.Nil(t, err)
}

func TestGetAllBlb(t *testing.T) {
	patches := gomonkey.ApplyFunc(blb.NewClient, func(_ context.CsmContext, _ string, _ string) (*appblb.Client, error) {
		return &appblb.Client{}, nil
	})
	defer patches.Reset()

	var c *appblb.Client
	patches.ApplyMethod(c, "DescribeLoadBalancers", func(_ *appblb.Client, _ *appblb.DescribeLoadBalancersArgs) (
		*appblb.DescribeLoadBalancersResult, error) {
		return nil, nil
	})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	_, err := service.GetAllBlb(mockCtx, "bj", nil)
	assert.Nil(t, err)
}

func TestCreateBlbService(t *testing.T) {
	c := &blbService.Client{}
	patches := gomonkey.ApplyFunc(blbService.GetClientWithAkSk,
		func(_ context.CsmContext, _ string, _ string, _ string) (*blbService.Client, error) {
			return c, nil
		})
	defer patches.Reset()

	patches.ApplyMethod(c, "CreateBlbService",
		func(_ *blbService.Client, _ context.CsmContext, _ *blbService.CreateBlbServiceArgs) (*blbService.CreateBlbServiceResult, error) {
			return nil, nil
		})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	args := &blbService.CreateBlbServiceArgs{
		Name:        "xxx",
		Description: "xxx",
		ServiceName: "xxx",
		InstanceID:  "xxxx",
		AuthList:    blbService.NewDefaultAuth(),
	}
	_, err := service.CreateBlbService(mockCtx, args, region)
	assert.Nil(t, err)
}

func TestDeleteBlbService(t *testing.T) {
	c := &blbService.Client{}
	patches := gomonkey.ApplyFunc(blbService.GetClientWithAkSk,
		func(_ context.CsmContext, _ string, _ string, _ string) (*blbService.Client, error) {
			return c, nil
		})
	defer patches.Reset()

	patches.ApplyMethod(c, "DeleteBlbService",
		func(_ *blbService.Client, _ context.CsmContext, _ *blbService.DeleteBlbServiceArgs) error {
			return nil
		})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	args := &blbService.DeleteBlbServiceArgs{
		Service:     "xxx",
		ClientToken: util.GetClientToken(),
	}
	err := service.DeleteBlbService(mockCtx, args, region)
	assert.Nil(t, err)
}

func TestGetBlbService(t *testing.T) {
	c := &blbService.Client{}
	patches := gomonkey.ApplyFunc(blbService.GetClientWithAkSk,
		func(_ context.CsmContext, _ string, _ string, _ string) (*blbService.Client, error) {
			return c, nil
		})
	defer patches.Reset()

	patches.ApplyMethod(c, "GetBlbService",
		func(_ *blbService.Client, _ context.CsmContext, _ *blbService.GetBlbServiceArgs) (*blbService.GetBlbServiceResult, error) {
			return nil, nil
		})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	args := &blbService.GetBlbServiceArgs{
		Service: "xxx",
	}
	_, err := service.GetBlbService(mockCtx, args, region)
	assert.Nil(t, err)
}

func TestDeleteAllAppListeners(t *testing.T) {
	patches := gomonkey.ApplyFunc(blb.NewClient, func(_ context.CsmContext, _ string, _ string) (*appblb.Client, error) {
		return &appblb.Client{}, nil
	})
	defer patches.Reset()

	var c *appblb.Client
	patches.ApplyMethod(c, "DeleteAppListeners", func(_ *appblb.Client, _ string, _ *appblb.DeleteAppListenersArgs) error {
		return nil
	})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	err := service.DeleteAllAppListeners(mockCtx, "blb-id-1", "bj", nil)
	assert.Nil(t, err)
}

func TestGetAllAppIpGroups(t *testing.T) {
	patches := gomonkey.ApplyFunc(blb.NewClient, func(_ context.CsmContext, _ string, _ string) (*appblb.Client, error) {
		return &appblb.Client{}, nil
	})
	defer patches.Reset()

	var c *appblb.Client
	patches.ApplyMethod(c, "DescribeAppIpGroup", func(_ *appblb.Client, _ string, _ *appblb.DescribeAppIpGroupArgs) (
		*appblb.DescribeAppIpGroupResult, error) {
		return &appblb.DescribeAppIpGroupResult{
			AppIpGroupList: []appblb.AppIpGroup{
				{
					Id: "ip-group-123",
				},
			},
		}, nil
	})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	_, err := service.GetAllAppIpGroups(mockCtx, "blb-id-1", "bj")
	assert.Nil(t, err)
}

func TestDeleteAppIpGroup(t *testing.T) {
	patches := gomonkey.ApplyFunc(blb.NewClient, func(_ context.CsmContext, _ string, _ string) (*appblb.Client, error) {
		return &appblb.Client{}, nil
	})
	defer patches.Reset()

	var c *appblb.Client
	patches.ApplyMethod(c, "DeleteAppIpGroup", func(_ *appblb.Client, _ string, _ *appblb.DeleteAppIpGroupArgs) error {
		return nil
	})

	service := &Service{
		option: &Option{
			BlbEndpoint:    "",
			BceServiceRole: "",
			BlbIamProfile:  "",
		},
	}
	err := service.DeleteAppIpGroup(mockCtx, "blb-id-1", "bj", nil)
	assert.Nil(t, err)
}
