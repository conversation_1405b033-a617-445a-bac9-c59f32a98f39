// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	model "github.com/baidubce/bce-sdk-go/model"
	appblb "github.com/baidubce/bce-sdk-go/services/appblb"
	gomock "github.com/golang/mock/gomock"
	service "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	context "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// BindTag mocks base method.
func (m *MockServiceInterface) BindTag(ctx context.CsmContext, blbId, region string, tag *model.TagModel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindTag", ctx, blbId, region, tag)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindTag indicates an expected call of BindTag.
func (mr *MockServiceInterfaceMockRecorder) BindTag(ctx, blbId, region, tag interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindTag", reflect.TypeOf((*MockServiceInterface)(nil).BindTag), ctx, blbId, region, tag)
}

// CreateAppIpGroup mocks base method.
func (m *MockServiceInterface) CreateAppIpGroup(ctx context.CsmContext, blbId, region string, ipGroupArgs *appblb.CreateAppIpGroupArgs) (*appblb.CreateAppIpGroupResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppIpGroup", ctx, blbId, region, ipGroupArgs)
	ret0, _ := ret[0].(*appblb.CreateAppIpGroupResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAppIpGroup indicates an expected call of CreateAppIpGroup.
func (mr *MockServiceInterfaceMockRecorder) CreateAppIpGroup(ctx, blbId, region, ipGroupArgs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppIpGroup", reflect.TypeOf((*MockServiceInterface)(nil).CreateAppIpGroup), ctx, blbId, region, ipGroupArgs)
}

// CreateAppTCPListener mocks base method.
func (m *MockServiceInterface) CreateAppTCPListener(ctx context.CsmContext, blbId, region string, appTCPListenerArgs *appblb.CreateAppTCPListenerArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppTCPListener", ctx, blbId, region, appTCPListenerArgs)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppTCPListener indicates an expected call of CreateAppTCPListener.
func (mr *MockServiceInterfaceMockRecorder) CreateAppTCPListener(ctx, blbId, region, appTCPListenerArgs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppTCPListener", reflect.TypeOf((*MockServiceInterface)(nil).CreateAppTCPListener), ctx, blbId, region, appTCPListenerArgs)
}

// CreateBlbService mocks base method.
func (m *MockServiceInterface) CreateBlbService(ctx context.CsmContext, args *service.CreateBlbServiceArgs, region string) (*service.CreateBlbServiceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBlbService", ctx, args, region)
	ret0, _ := ret[0].(*service.CreateBlbServiceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBlbService indicates an expected call of CreateBlbService.
func (mr *MockServiceInterfaceMockRecorder) CreateBlbService(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBlbService", reflect.TypeOf((*MockServiceInterface)(nil).CreateBlbService), ctx, args, region)
}

// DeleteAllAppListeners mocks base method.
func (m *MockServiceInterface) DeleteAllAppListeners(ctx context.CsmContext, blbId, region string, args *appblb.DeleteAppListenersArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAllAppListeners", ctx, blbId, region, args)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAllAppListeners indicates an expected call of DeleteAllAppListeners.
func (mr *MockServiceInterfaceMockRecorder) DeleteAllAppListeners(ctx, blbId, region, args interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllAppListeners", reflect.TypeOf((*MockServiceInterface)(nil).DeleteAllAppListeners), ctx, blbId, region, args)
}

// DeleteAppIpGroup mocks base method.
func (m *MockServiceInterface) DeleteAppIpGroup(ctx context.CsmContext, blbId, region string, args *appblb.DeleteAppIpGroupArgs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAppIpGroup", ctx, blbId, region, args)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAppIpGroup indicates an expected call of DeleteAppIpGroup.
func (mr *MockServiceInterfaceMockRecorder) DeleteAppIpGroup(ctx, blbId, region, args interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAppIpGroup", reflect.TypeOf((*MockServiceInterface)(nil).DeleteAppIpGroup), ctx, blbId, region, args)
}

// DeleteBlbService mocks base method.
func (m *MockServiceInterface) DeleteBlbService(ctx context.CsmContext, args *service.DeleteBlbServiceArgs, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBlbService", ctx, args, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBlbService indicates an expected call of DeleteBlbService.
func (mr *MockServiceInterfaceMockRecorder) DeleteBlbService(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBlbService", reflect.TypeOf((*MockServiceInterface)(nil).DeleteBlbService), ctx, args, region)
}

// GetAllAppIpGroups mocks base method.
func (m *MockServiceInterface) GetAllAppIpGroups(ctx context.CsmContext, blbId, region string) (*appblb.DescribeAppIpGroupResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllAppIpGroups", ctx, blbId, region)
	ret0, _ := ret[0].(*appblb.DescribeAppIpGroupResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllAppIpGroups indicates an expected call of GetAllAppIpGroups.
func (mr *MockServiceInterfaceMockRecorder) GetAllAppIpGroups(ctx, blbId, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllAppIpGroups", reflect.TypeOf((*MockServiceInterface)(nil).GetAllAppIpGroups), ctx, blbId, region)
}

// GetAllAppListeners mocks base method.
func (m *MockServiceInterface) GetAllAppListeners(ctx context.CsmContext, blbId, region string, args *appblb.DescribeAppListenerArgs) (*appblb.DescribeAppAllListenersResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllAppListeners", ctx, blbId, region, args)
	ret0, _ := ret[0].(*appblb.DescribeAppAllListenersResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllAppListeners indicates an expected call of GetAllAppListeners.
func (mr *MockServiceInterfaceMockRecorder) GetAllAppListeners(ctx, blbId, region, args interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllAppListeners", reflect.TypeOf((*MockServiceInterface)(nil).GetAllAppListeners), ctx, blbId, region, args)
}

// GetAllBlb mocks base method.
func (m *MockServiceInterface) GetAllBlb(ctx context.CsmContext, region string, args *appblb.DescribeLoadBalancersArgs) (*appblb.DescribeLoadBalancersResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllBlb", ctx, region, args)
	ret0, _ := ret[0].(*appblb.DescribeLoadBalancersResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllBlb indicates an expected call of GetAllBlb.
func (mr *MockServiceInterfaceMockRecorder) GetAllBlb(ctx, region, args interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllBlb", reflect.TypeOf((*MockServiceInterface)(nil).GetAllBlb), ctx, region, args)
}

// GetBlb mocks base method.
func (m *MockServiceInterface) GetBlb(ctx context.CsmContext, blbId, region string) (*appblb.DescribeLoadBalancerDetailResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlb", ctx, blbId, region)
	ret0, _ := ret[0].(*appblb.DescribeLoadBalancerDetailResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlb indicates an expected call of GetBlb.
func (mr *MockServiceInterfaceMockRecorder) GetBlb(ctx, blbId, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlb", reflect.TypeOf((*MockServiceInterface)(nil).GetBlb), ctx, blbId, region)
}

// GetBlbService mocks base method.
func (m *MockServiceInterface) GetBlbService(ctx context.CsmContext, args *service.GetBlbServiceArgs, region string) (*service.GetBlbServiceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlbService", ctx, args, region)
	ret0, _ := ret[0].(*service.GetBlbServiceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlbService indicates an expected call of GetBlbService.
func (mr *MockServiceInterfaceMockRecorder) GetBlbService(ctx, args, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlbService", reflect.TypeOf((*MockServiceInterface)(nil).GetBlbService), ctx, args, region)
}

// ReleaseBlb mocks base method.
func (m *MockServiceInterface) ReleaseBlb(ctx context.CsmContext, blbId, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseBlb", ctx, blbId, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReleaseBlb indicates an expected call of ReleaseBlb.
func (mr *MockServiceInterfaceMockRecorder) ReleaseBlb(ctx, blbId, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseBlb", reflect.TypeOf((*MockServiceInterface)(nil).ReleaseBlb), ctx, blbId, region)
}

// UnBindTag mocks base method.
func (m *MockServiceInterface) UnBindTag(ctx context.CsmContext, blbId, region string, tag *model.TagModel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnBindTag", ctx, blbId, region, tag)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnBindTag indicates an expected call of UnBindTag.
func (mr *MockServiceInterfaceMockRecorder) UnBindTag(ctx, blbId, region, tag interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnBindTag", reflect.TypeOf((*MockServiceInterface)(nil).UnBindTag), ctx, blbId, region, tag)
}
