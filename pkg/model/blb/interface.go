package blb

import (
	"github.com/baidubce/bce-sdk-go/model"
	"github.com/baidubce/bce-sdk-go/services/appblb"

	blbService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type ServiceInterface interface {
	GetBlb(ctx context.CsmContext, blbId, region string) (*appblb.DescribeLoadBalancerDetailResult, error)
	CreateAppIpGroup(ctx context.CsmContext, blbId, region string,
		ipGroupArgs *appblb.CreateAppIpGroupArgs) (*appblb.CreateAppIpGroupResult, error)
	CreateAppTCPListener(ctx context.CsmContext, blbId, region string,
		appTCPListenerArgs *appblb.CreateAppTCPListenerArgs) error
	ReleaseBlb(ctx context.CsmContext, blbId, region string) error

	BindTag(ctx context.CsmContext, blbId, region string, tag *model.TagModel) error
	UnBindTag(ctx context.CsmContext, blbId, region string, tag *model.TagModel) error

	GetAllBlb(ctx context.CsmContext, region string, args *appblb.DescribeLoadBalancersArgs) (*appblb.DescribeLoadBalancersResult, error)

	// CreateBlbService BLB 创建服务发布点
	CreateBlbService(ctx context.CsmContext, args *blbService.CreateBlbServiceArgs, region string) (*blbService.CreateBlbServiceResult, error)
	// DeleteBlbService BLB 删除服务发布点
	DeleteBlbService(ctx context.CsmContext, args *blbService.DeleteBlbServiceArgs, region string) error
	// GetBlbService  BLB 查询服务发布点详情
	GetBlbService(ctx context.CsmContext, args *blbService.GetBlbServiceArgs, region string) (*blbService.GetBlbServiceResult, error)

	// GetAllAppListeners 获取BLB的所有监听器
	GetAllAppListeners(ctx context.CsmContext, blbId, region string, args *appblb.DescribeAppListenerArgs) (
		*appblb.DescribeAppAllListenersResult, error)
	// DeleteAllAppListeners 删除BLB的所有监听器
	DeleteAllAppListeners(ctx context.CsmContext, blbId, region string, args *appblb.DeleteAppListenersArgs) error
	// GetAllAppIpGroups 获取BLB的所有目标IP组
	GetAllAppIpGroups(ctx context.CsmContext, blbId, region string) (*appblb.DescribeAppIpGroupResult, error)
	// DeleteAppIpGroup 删除BLB指定目标IP组
	DeleteAppIpGroup(ctx context.CsmContext, blbId, region string, args *appblb.DeleteAppIpGroupArgs) error
}
