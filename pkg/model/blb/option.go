package blb

import "github.com/spf13/viper"

const (
	blbEndpoint = "cloud.blb.endpoint"

	// TODO 删除 iam 与服务号
	blbIamProfile  = "cloud.iamProfile"
	bceServiceRole = "bceServiceRole"

	hostingAccessKey = "cloud.hosting.access_key"
	hostingSecretKey = "cloud.hosting.secret_key"
)

type Option struct {
	// BLB endpoint
	BlbEndpoint string

	// TODO 删除 iam 与服务号
	// 服务号名称
	BceServiceRole string
	// BLB 使用 iam 配置
	BlbIamProfile string

	// HostingAccessKey 托管集群创建服务发布点使用的 ak
	HostingAccessKey string
	// HostingSecretKey 托管集群创建服务发布点使用的 sk
	HostingSecretKey string
}

// NewOption 初始化 blb 配置
func NewOption() *Option {
	return &Option{
		BlbEndpoint:      viper.GetString(blbEndpoint),
		BceServiceRole:   viper.GetString(bceServiceRole),
		BlbIamProfile:    viper.GetString(blbIamProfile),
		HostingAccessKey: viper.GetString(hostingAccessKey),
		HostingSecretKey: viper.GetString(hostingSecretKey),
	}
}
