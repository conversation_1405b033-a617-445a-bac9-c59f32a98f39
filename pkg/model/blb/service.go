package blb

import (
	"fmt"

	"github.com/baidubce/bce-sdk-go/model"
	"github.com/baidubce/bce-sdk-go/services/appblb"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb"
	blbService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/tag"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
)

type Service struct {
	option *Option
}

func NewService(opt *Option) *Service {
	return &Service{
		option: opt,
	}
}

func (service *Service) getTagClient(ctx context.CsmContext) (tagClient *tag.Client, err error) {
	return tag.GetClient(ctx, service.option.BlbIamProfile, service.option.BceServiceRole, "")
}

func (service *Service) GetBlb(ctx context.CsmContext, blbId, region string) (
	*appblb.DescribeLoadBalancerDetailResult, error) {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)

	client, err := blb.NewClient(ctx, endpoint, service.option.BlbIamProfile, service.option.BceServiceRole)
	if err != nil {
		return nil, err
	}
	return client.DescribeLoadBalancerDetail(blbId)
}

func (service *Service) ReleaseBlb(ctx context.CsmContext, blbId, region string) error {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := blb.NewClient(ctx, endpoint, service.option.BlbIamProfile, service.option.BceServiceRole)
	if err != nil {
		return err
	}
	return client.DeleteLoadBalancer(blbId)
}

func (service *Service) CreateAppIpGroup(ctx context.CsmContext, blbId, region string,
	ipGroupArgs *appblb.CreateAppIpGroupArgs) (*appblb.CreateAppIpGroupResult, error) {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := blb.NewClient(ctx, endpoint, service.option.BlbIamProfile, service.option.BceServiceRole)
	if err != nil {
		return nil, err
	}
	createAppIpGroupResult, createAppIpErr := client.CreateAppIpGroup(blbId, ipGroupArgs)
	createAppIpGroupArgs := &appblb.CreateAppIpGroupBackendPolicyArgs{
		IpGroupId:                   createAppIpGroupResult.Id,
		Type:                        "TCP",
		HealthCheckTimeoutInSecond:  3,
		HealthCheckIntervalInSecond: 3,
	}
	err = client.CreateAppIpGroupBackendPolicy(blbId, createAppIpGroupArgs)
	if err != nil {
		return nil, err
	}
	return createAppIpGroupResult, createAppIpErr
}

func (service *Service) CreateAppTCPListener(ctx context.CsmContext, blbId, region string,
	appTCPListenerArgs *appblb.CreateAppTCPListenerArgs) error {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := blb.NewClient(ctx, endpoint, service.option.BlbIamProfile, service.option.BceServiceRole)
	if err != nil {
		return err
	}
	return client.CreateAppTCPListener(blbId, appTCPListenerArgs)
}

func (service *Service) BindTag(ctx context.CsmContext, blbId, region string, t *model.TagModel) error {
	client, err := service.getTagClient(ctx)
	if err != nil {
		return err
	}

	args := &tag.BindResourceArgs{
		TagKey:      t.TagKey,
		TagValue:    t.TagValue,
		ServiceType: tag.ServiceTypeBLB,
		Resources: []tag.Resource{
			{
				ResourceId:  blbId,
				ServiceType: tag.ServiceTypeBLB,
				Region:      region,
			},
		},
	}
	return client.BindResource(args)
}

func (service *Service) UnBindTag(ctx context.CsmContext, blbId, region string, t *model.TagModel) error {
	client, err := service.getTagClient(ctx)
	if err != nil {
		return err
	}

	args := &tag.BindResourceArgs{
		TagKey:      t.TagKey,
		TagValue:    t.TagValue,
		ServiceType: tag.ServiceTypeBLB,
		Resources: []tag.Resource{
			{
				ResourceId:  blbId,
				ServiceType: tag.ServiceTypeBLB,
				Region:      region,
			},
		},
	}
	return client.UnBindResource(args)
}

func (service *Service) GetAllBlb(ctx context.CsmContext, region string, args *appblb.DescribeLoadBalancersArgs) (
	*appblb.DescribeLoadBalancersResult, error) {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := blb.NewClient(ctx, endpoint, service.option.BlbIamProfile, service.option.BceServiceRole)
	if err != nil {
		return nil, err
	}
	return client.DescribeLoadBalancers(args)
}

func (service *Service) getBlbServiceClient(ctx context.CsmContext, endpoint string) (client *blbService.Client, err error) {
	return blbService.GetClientWithAkSk(ctx, service.option.HostingAccessKey, service.option.HostingSecretKey, endpoint)
}

// CreateBlbService 创建服务发布点
func (service *Service) CreateBlbService(ctx context.CsmContext, args *blbService.CreateBlbServiceArgs,
	region string) (*blbService.CreateBlbServiceResult, error) {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := service.getBlbServiceClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	if len(args.ClientToken) == 0 {
		args.ClientToken = util.GetClientToken()
	}
	return client.CreateBlbService(ctx, args)
}

// DeleteBlbService 删除服务发布点
func (service *Service) DeleteBlbService(ctx context.CsmContext, args *blbService.DeleteBlbServiceArgs,
	region string) error {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := service.getBlbServiceClient(ctx, endpoint)
	if err != nil {
		return err
	}
	return client.DeleteBlbService(ctx, args)
}

// GetBlbService 查询服务发布点详情
func (service *Service) GetBlbService(ctx context.CsmContext, args *blbService.GetBlbServiceArgs,
	region string) (*blbService.GetBlbServiceResult, error) {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := service.getBlbServiceClient(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	return client.GetBlbService(ctx, args)
}

// GetAllAppListeners 获取BLB的所有监听器
func (service *Service) GetAllAppListeners(ctx context.CsmContext, blbId, region string, args *appblb.DescribeAppListenerArgs) (
	*appblb.DescribeAppAllListenersResult, error) {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := blb.NewClient(ctx, endpoint, service.option.BlbIamProfile, service.option.BceServiceRole)
	if err != nil {
		return nil, err
	}
	return client.DescribeAppAllListeners(blbId, args)
}

// DeleteAllAppListeners 删除BLB的所有监听器
func (service *Service) DeleteAllAppListeners(ctx context.CsmContext, blbId, region string, args *appblb.DeleteAppListenersArgs) error {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := blb.NewClient(ctx, endpoint, service.option.BlbIamProfile, service.option.BceServiceRole)
	if err != nil {
		return err
	}
	return client.DeleteAppListeners(blbId, args)
}

// GetAllAppIpGroups 获取BLB的所有IP组信息
func (service *Service) GetAllAppIpGroups(ctx context.CsmContext, blbId, region string) (*appblb.DescribeAppIpGroupResult, error) {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := blb.NewClient(ctx, endpoint, service.option.BlbIamProfile, service.option.BceServiceRole)
	if err != nil {
		return nil, err
	}
	return client.DescribeAppIpGroup(blbId, nil)
}

// DeleteAppIpGroup 删除BLB指定目标IP组
func (service *Service) DeleteAppIpGroup(ctx context.CsmContext, blbId, region string, args *appblb.DeleteAppIpGroupArgs) error {
	endpoint := fmt.Sprintf(service.option.BlbEndpoint, region)
	client, err := blb.NewClient(ctx, endpoint, service.option.BlbIamProfile, service.option.BceServiceRole)
	if err != nil {
		return err
	}
	return client.DeleteAppIpGroup(blbId, args)
}
