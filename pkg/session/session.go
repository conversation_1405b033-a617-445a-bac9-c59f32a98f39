package session

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	harbormodels "github.com/goharbor/harbor/src/common/models"
	"github.com/goharbor/harbor/src/jobservice/logger"
	"github.com/karlseguin/ccache/v2"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	harboruser "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/user"
	harborclimodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

type SessionToken struct {
	cache     *ccache.Cache
	harborCli *harbor.HarborClient
}

func NewSessionToken(harborCli *harbor.HarborClient) *SessionToken {
	return &SessionToken{
		cache:     ccache.New(ccache.Configure().MaxSize(5000).ItemsToPrune(500).GetsPerPromote(10)),
		harborCli: harborCli,
	}
}

type sessionValue struct {
	user  *harbormodels.User
	token string
}

func (s *SessionToken) GetSessionToken(c *gin.Context, user *harbormodels.User) (string, error) {
	var (
		sid string
		err error
	)

	if user == nil {
		return "", fmt.Errorf("no user provided")
	}

	item := s.cache.Get(user.Username)
	if item == nil || item.Expired() {
		sid, err = s.updateSessionToken(c, user)
		if err != nil {
			logger.Errorf("get session token failed: %s", err)
			return "", err
		}

		return sid, nil
	}

	sessionVal, ok := item.Value().(sessionValue)
	if !ok {
		return "", fmt.Errorf("get session with wrong type")
	}

	return sessionVal.token, nil
}

func (s *SessionToken) updateSessionToken(c *gin.Context, user *harbormodels.User) (string, error) {
	groups := []int64{}
	if user.GroupIDs != nil {
		for _, v := range user.GroupIDs {
			groups = append(groups, int64(v))
		}
	}

	reqID := gin_context.RequestIdFromContext(c)
	logger := gin_context.LoggerFromContext(c)

	result, err := s.harborCli.AddonClient.User.GetUserSessionToken(
		harboruser.NewGetUserSessionTokenParams().WithContext(c).WithXRequestID(&reqID).WithBody(
			&harborclimodel.ModelsSessionTokenArgs{
				Email:           user.Email,
				GroupIDs:        groups,
				PasswordVersion: user.PasswordVersion,
				Realname:        user.Realname,
				ResetUUID:       user.ResetUUID,
				Role:            int64(user.Role),
				Rolename:        user.Rolename,
				Salt:            user.Salt,
				SysAdminFlag:    user.SysAdminFlag,
				UserID:          int64(user.UserID),
				Username:        user.Username,
			},
		), s.harborCli.AuthInfo)

	if err != nil {
		logger.Errorf("get session token from addon failed: %s", err)
		return "", err
	}

	logger.Infof("session token will expired at %s", result.GetPayload().ExpiredAt)

	t, err := time.Parse(time.RFC3339, result.GetPayload().ExpiredAt)
	if err != nil {
		logger.Warningf("time %s is invalid, will use default value", result.GetPayload().ExpiredAt)
		t = time.Now().Add(25 * time.Minute)
	}

	s.cache.Set(user.Username,
		sessionValue{user: user, token: result.GetPayload().Token},
		t.UTC().Sub(time.Now().UTC().Add(5*time.Minute)),
	)

	return result.GetPayload().Token, nil
}
