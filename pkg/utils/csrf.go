package utils

import (
	"encoding/base64"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/gorilla/securecookie"
)

type HarborCsrf struct {
	Key string

	csrfToken  string
	csrfCookie string
	expiredAt  time.Time

	csrfDuration time.Duration
	mutex        sync.RWMutex
}

func NewHarborCsrf(key string) (*HarborCsrf, error) {
	// harbor encode key must be 32 length
	if len(key) != 32 {
		return nil, fmt.Errorf("length of key must be 32")
	}

	return &HarborCsrf{
		Key:          key,
		csrfToken:    "",
		csrfCookie:   "",
		expiredAt:    time.Now(),
		csrfDuration: 12 * time.Hour,
	}, nil
}

func (c *HarborCsrf) GetCsrfToken() (string, string, error) {
	var err error

	token, cookie := "", ""

	c.mutex.RLock()
	if c.expiredAt.Add(-5*time.Minute).After(time.Now()) &&
		c.csrfToken != "" &&
		c.csrfCookie != "" {
		token, cookie = c.csrfToken, c.csrfCookie
		c.mutex.RUnlock()
		return token, cookie, nil
	}

	c.mutex.RUnlock()
	c.mutex.Lock()
	defer func() {
		c.mutex.Unlock()
	}()

	c.csrfToken, c.csrfCookie, err = c.generateCsrfToken([]byte(c.Key))
	if err != nil {
		return "", "", err
	}
	c.expiredAt = time.Now().Add(c.csrfDuration)

	return c.csrfToken, c.csrfCookie, nil
}

func (c *HarborCsrf) generateCsrfToken(key []byte) (string, string, error) {
	var (
		len        = len(key)
		cookieName = "_gorilla_csrf"
	)

	sc := securecookie.New(key, nil)
	sc.SetSerializer(securecookie.JSONEncoder{})

	randToken := make([]byte, len)
	_, err := rand.Read(randToken)
	if err != nil {
		return "", "", fmt.Errorf("generate random cookie byte failed: %w", err)
	}

	csrfBytes := make([]byte, len)
	_, err = rand.Read(csrfBytes)
	if err != nil {
		return "", "", fmt.Errorf("generate random csrf byte failed: %s", err)
	}

	xor := make([]byte, len)
	for i := 0; i < len; i++ {
		xor[i] = randToken[i] ^ csrfBytes[i]
	}

	csrfKey := base64.StdEncoding.EncodeToString(append(csrfBytes, xor...))

	tk, err := sc.Encode(cookieName, randToken)
	if err != nil {
		return "", "", fmt.Errorf("encode cookie failed: %s", err)
	}

	return csrfKey, tk, nil
}
