package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// Test_Domain 函数用于测试Domain函数
// 该函数接受一个参数t，类型为*testing.T
// 函数中包含若干断言语句用于判断函数的正确性和结果
func Test_Domain(t *testing.T) {
	src := "baidubce.com"

	d, err := NewDomain(src)
	assert.NotNil(t, err)
	assert.Nil(t, d)

	src = "ccr.baidubce.com"
	d, err = NewDomain(src)
	assert.NotNil(t, err)
	assert.Nil(t, d)

	src = "ccr.cnc.gz.baidubce.com"
	d, err = NewDomain(src)
	assert.<PERSON>l(t, err)
	assert.Equal(t, "ccr.cnc", d.<PERSON>())
	assert.Equal(t, "gz.baidubce.com", d.GetZone())
	assert.Equal(t, "inet", d.<PERSON>("inet"))
	assert.Equal(t, "gz", d.<PERSON><PERSON>("pnet"))
	assert.Equal(t, src, d.String())

	src = "ccr-cnc.gz.baidubce.com"
	d, err = NewDomain(src)
	assert.Nil(t, err)
	t.Logf("domain info: %#v", d)
	assert.Equal(t, "ccr-cnc", d.GetName())
	assert.Equal(t, "gz.baidubce.com", d.GetZone())
	assert.Equal(t, "inet", d.GetView("inet"))
	assert.Equal(t, "gz", d.GetView("pnet"))
	assert.Equal(t, src, d.String())

	src = "ccr.cnc.hkg.baidubce.com"
	d, err = NewDomain(src)
	assert.Nil(t, err)
	assert.Equal(t, "ccr.cnc", d.GetName())
	assert.Equal(t, "hkg.baidubce.com", d.GetZone())
	assert.Equal(t, "inet", d.GetView("inet"))
	assert.Equal(t, "hkg", d.GetView("pnet"))
	assert.Equal(t, src, d.String())

	src = "ccr.cnc.gztest.baidubce.com"
	d, err = NewDomain(src)
	assert.NotNil(t, err)
	assert.Nil(t, d)
}

func TestIsSameRegion(t *testing.T) {
	type args struct {
		left  string
		right string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				left:  "test",
				right: "test",
			},
			want: true,
		},
		{
			name: "gztest",
			args: args{
				left:  "gz",
				right: "gztest",
			},
			want: true,
		},
		{
			name: "other",
			args: args{
				left:  "gz11",
				right: "gztest",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsSameRegion(tt.args.left, tt.args.right); got != tt.want {
				t.Errorf("IsSameRegion() = %v, want %v", got, tt.want)
			}
		})
	}
}
