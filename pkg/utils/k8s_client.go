package utils

import (
	"context"
	"fmt"
	"time"

	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/apiutil"
	"sigs.k8s.io/controller-runtime/pkg/cluster"
)

type k8sclientUtilInterface interface {
	createClient(config *rest.Config, scheme *runtime.Scheme) (client.Client, error)
	createCacheClient(cache cache.Cache, config *rest.Config, options client.Options, uncachedObjects ...client.Object) (client.Client, error)
	createRESTMapper(cfg *rest.Config, opts ...apiutil.DynamicRESTMapperOption) (meta.RESTMapper, error)
	createCache(config *rest.Config, opts cache.Options) (cache.Cache, error)
	createK8sClientset(config *rest.Config) (kubernetes.Interface, error)
}

type k8sclientUtil struct{}

func (k8sclientUtil) createClient(config *rest.Config, scheme *runtime.Scheme) (client.Client, error) {
	return client.New(config, client.Options{Scheme: scheme})
}

func (k8sclientUtil) createCacheClient(cache cache.Cache, config *rest.Config, options client.Options, uncachedObjects ...client.Object) (client.Client, error) {
	return cluster.DefaultNewClient(cache, config, options, uncachedObjects...)
}

func (k8sclientUtil) createRESTMapper(cfg *rest.Config, opts ...apiutil.DynamicRESTMapperOption) (meta.RESTMapper, error) {
	return apiutil.NewDynamicRESTMapper(cfg, opts...)
}

func (k8sclientUtil) createCache(config *rest.Config, opts cache.Options) (cache.Cache, error) {
	return cache.New(config, opts)
}

func (k8sclientUtil) createK8sClientset(config *rest.Config) (kubernetes.Interface, error) {
	return kubernetes.NewForConfig(config)
}

var kcu k8sclientUtilInterface = k8sclientUtil{}

func NewK8sClient(kubeconfig string, scheme *runtime.Scheme) (client.Client, error) {
	var config *rest.Config
	var err error

	if kubeconfig == "" {
		config, err = rest.InClusterConfig()
	} else {
		config, err = clientcmd.RESTConfigFromKubeConfig([]byte(kubeconfig))
	}

	if err != nil {
		return nil, err
	}

	return kcu.createClient(config, scheme)
}

func NewK8sClientWithCache(ctx context.Context, kubeconfig string, scheme *runtime.Scheme, namespace string, resync time.Duration) (client.Client, error) {
	var (
		config *rest.Config
		err    error
	)

	if kubeconfig == "" {
		config, err = rest.InClusterConfig()
	} else {
		config, err = clientcmd.RESTConfigFromKubeConfig([]byte(kubeconfig))
	}

	if err != nil {
		return nil, fmt.Errorf("get rest config from %s failed: %w", kubeconfig, err)
	}

	return NewK8sClientWithCacheFromRestConfig(ctx, config, scheme, namespace, resync)
}

func NewK8sClientWithCacheFromRestConfig(ctx context.Context, config *rest.Config, scheme *runtime.Scheme, namespace string, resync time.Duration) (client.Client, error) {
	mapper, err := kcu.createRESTMapper(config)
	if err != nil {
		return nil, fmt.Errorf("create rest mapper failed: %w", err)
	}

	ch, err := kcu.createCache(config, cache.Options{
		Scheme:    scheme,
		Mapper:    mapper,
		Resync:    &resync,
		Namespace: namespace,
	})
	if err != nil {
		return nil, fmt.Errorf("create cache failed: %w", err)
	}

	// start cache
	go ch.Start(ctx)

	return kcu.createCacheClient(ch, config, client.Options{
		Scheme: scheme,
		Mapper: mapper,
	})
}

func NewK8sClientSetFromKubeconfig(kubeconfig string) (kubernetes.Interface, error) {
	var (
		config *rest.Config
		err    error
	)

	if kubeconfig == "" {
		config, err = rest.InClusterConfig()
	} else {
		config, err = clientcmd.RESTConfigFromKubeConfig([]byte(kubeconfig))
	}

	if err != nil {
		return nil, fmt.Errorf("get rest config from %s failed: %w", kubeconfig, err)
	}

	return kcu.createK8sClientset(config)
}
