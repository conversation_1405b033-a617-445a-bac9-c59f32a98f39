package utils

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
)

// 密钥: 16 字节长度
var aesKey = []byte("ylbOHq7DgjWGuT2M")

// AESCFBEncrypt - AES CFB 加密 + Base64 编码
//
// PARAMS:
//  - plaintext: 明文
//
// RETURNS:
//   ciphertext: 经过 Base64 编码之后的密文
//   error: nil if succeed, error if fail
func AESCFBEncrypt(plaintext string) (string, error) {
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return "", err
	}

	// PKCS#7 填充明文
	plainBytes := []byte(plaintext)
	plainBytes = PKCS7Padding(plainBytes, aes.BlockSize)

	cipherBytes := make([]byte, aes.BlockSize+len(plainBytes))

	// 随机初始化 Initialization Vector
	iv := cipherBytes[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	// 加密
	cfb := cipher.NewCFBEncrypter(block, iv)
	cfb.XORKeyStream(cipherBytes[aes.BlockSize:], plainBytes)

	// Base64 编码
	ciphertext := base64.StdEncoding.EncodeToString(cipherBytes)

	return ciphertext, nil
}

// AESCFBDecrypt - Base64 解码 + AES CFB 解密
//
// PARAMS:
//  - ciphertext: 经过 Base64 编码之后的密文
//
// RETURNS:
//   plaintext: 明文
//   error: nil if succeed, error if fail
func AESCFBDecrypt(ciphertext string) (string, error) {
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return "", err
	}

	// Base64 解码
	cipherBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	if len(cipherBytes) < aes.BlockSize {
		return "", fmt.Errorf("insufficient message length %d", len(cipherBytes))
	}

	// 解密
	iv := cipherBytes[:aes.BlockSize]
	plainBytes := cipherBytes[aes.BlockSize:]

	cfb := cipher.NewCFBDecrypter(block, iv)
	cfb.XORKeyStream(plainBytes, plainBytes)

	// PKCS#7 去填充
	plainBytes = PKCS7UnPadding(plainBytes)

	return string(plainBytes), nil
}

// PKCS7Padding - 以 PKCS#7 模式对明文进行填充
//
// PARAMS:
//  - data: 待填充的字节数组
//  - blockSize: 加密分组大小
//
// RETURNS:
//   []byte: 经填充后的字节数组
//   error: nil if succeed, error if fail
func PKCS7Padding(data []byte, blockSize int) []byte {
	padLen := blockSize - len(data)%blockSize
	padData := bytes.Repeat([]byte{byte(padLen)}, padLen)

	return append(data, padData...)
}

// PKCS7UnPadding - 以 PKCS#7 模式对密文去填充
//
// PARAMS:
//  - data: 经填充后的字节数组
//  - blockSize: 加密分组大小
//
// RETURNS:
//   []byte: 去填充后的字节数组
//   error: nil if succeed, error if fail
func PKCS7UnPadding(data []byte) []byte {
	dataLen := len(data)
	padLen := int(data[dataLen-1])

	return data[:(dataLen - padLen)]
}
