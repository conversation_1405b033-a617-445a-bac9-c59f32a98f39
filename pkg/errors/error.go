package errors

import (
	"fmt"
	"time"
)

type CcrError struct {
	Message string
}

func (e *CcrError) Error() string {
	return e.Message
}

type ReconcileRetryError struct {
	RetryAfter time.Duration
}

func (re *ReconcileRetryError) Error() string {
	return fmt.Sprintf("need retry after %.2f seconds", re.RetryAfter.Seconds())
}

type UnauthorizedError struct {
	Effect string
}

func (ue *UnauthorizedError) Error() string {
	return fmt.Sprintf("no permission to do the request, result is: %s", ue.Effect)
}
