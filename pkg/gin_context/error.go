package gin_context

import (
	"net/http"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/gin-gonic/gin"
)

const (
	BAD_REQUEST               = "BadRequest"
	EACCESS_DENIED            = "AccessDenied"
	EINAPPROPRIATE_JSON       = "InappropriateJSON"
	EINTERNAL_ERROR           = "InternalError"
	EINVALID_ACCESS_KEY_ID    = "InvalidAccessKeyId"
	EINVALID_HTTP_AUTH_HEADER = "InvalidHTTPAuthHeader"
	EINVALID_HTTP_REQUEST     = "InvalidHTTPRequest"
	NOT_FOUND                 = "NotFound"
	STATUS_CONFLICT           = "StatusConflict"
	EINVALID_URI              = "InvalidURI"
	EMALFORMED_JSON           = "MalformedJSON"
	EINVALID_VERSION          = "InvalidVersion"
	EOPT_IN_REQUIRED          = "OptInRequired"
	EPRECONDITION_FAILED      = "PreconditionFailed"
	EREQUEST_EXPIRED          = "RequestExpired"
	ESIGNATURE_DOES_NOT_MATCH = "SignatureDoesNotMatch"
	UNAUTHORIZED              = "Unauthorized"
)

func InternalServerError(requestId string) *bce.BceServiceError {
	return &bce.BceServiceError{
		Code:       "InternalServerError",
		Message:    "internal server error",
		RequestId:  requestId,
		StatusCode: http.StatusInternalServerError,
	}
}

func ForbiddenError(requestId string) *bce.BceServiceError {
	return &bce.BceServiceError{
		Code:       "Forbidden",
		Message:    "have no permission",
		RequestId:  requestId,
		StatusCode: http.StatusForbidden,
	}
}

func UnauthorizedError(requestId string) *bce.BceServiceError {
	return &bce.BceServiceError{
		Code:       "Unauthorized",
		Message:    "have no permission",
		RequestId:  requestId,
		StatusCode: http.StatusUnauthorized,
	}
}

func UnauthorizedErrorWithMessage(requestId string, mesage string) *bce.BceServiceError {
	return &bce.BceServiceError{
		Code:       "Unauthorized",
		Message:    mesage,
		RequestId:  requestId,
		StatusCode: http.StatusUnauthorized,
	}
}

func BadRequestError(requestId string) *bce.BceServiceError {
	return &bce.BceServiceError{
		Code:       "BadRequest",
		Message:    "please check the request",
		RequestId:  requestId,
		StatusCode: http.StatusBadRequest,
	}
}

func NotFoundError(requestId, message string) *bce.BceServiceError {
	return &bce.BceServiceError{
		Code:       "NotFound",
		Message:    message,
		RequestId:  requestId,
		StatusCode: http.StatusNotFound,
	}
}

func ConflictError(requestId string, message string) *bce.BceServiceError {
	if message == "" {
		message = "conflict"
	}
	return &bce.BceServiceError{
		Code:       "Conflict",
		Message:    message,
		RequestId:  requestId,
		StatusCode: http.StatusConflict,
	}
}

func QuotaExceedError(requestId string) *bce.BceServiceError {
	return &bce.BceServiceError{
		Code:       "QuotaExceed",
		Message:    "quota exceed",
		RequestId:  requestId,
		StatusCode: http.StatusPreconditionFailed,
	}
}

func AlreadyExist(requestId string) *bce.BceServiceError {
	return &bce.BceServiceError{
		Code:       "AlreadyExist",
		Message:    "resource already exist",
		RequestId:  requestId,
		StatusCode: http.StatusConflict,
	}
}

func E(c *gin.Context, err *bce.BceServiceError) {
	c.AbortWithStatusJSON(err.StatusCode, err)
}
