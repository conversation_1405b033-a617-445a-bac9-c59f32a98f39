package error

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"
)

// ErrorType is the alias of string
type ErrorType string

// Error types is the brief introduction of error types,
// and it's the key to navigate the message below.
const (
	// External error face to user
	ClusterAccessFailException           ErrorType = "ClusterAccessFailException"
	ClusterOperationFailException        ErrorType = "ClusterOperationFailException"
	InvalidParameterValueException       ErrorType = "InvalidParameterValueException"
	MissingParameterValueException       ErrorType = "MissingParameterValueException"
	InvalidRequestContentException       ErrorType = "InvalidRequestContentException"
	InvalidSecurityGroupIDException      ErrorType = "InvalidSecurityGroupIDException"
	InvalidSubnetIDException             ErrorType = "InvalidSubnetIDException"
	PolicyLengthExceededException        ErrorType = "PolicyLengthExceededException"
	ServiceException                     ErrorType = "ServiceException"
	SubnetIPAddressLimitReachedException ErrorType = "SubnetIPAddressLimitReachedException"
	UnrecognizedClientException          ErrorType = "UnrecognizedClientException"
	UnsupportedMediaTypeException        ErrorType = "UnsupportedMediaTypeException"
	PreconditionValidationException      ErrorType = "PreconditionValidationException"
	ResourceNameConflictException        ErrorType = "ResourceNameConflictException"
	ReservedKeyException                 ErrorType = "ReservedKeyException"
	AlertLabelsTooLongException          ErrorType = "AlertLabelsTooLongException"
	PrototypeException                   ErrorType = "PrototypeException"

	NotImplementedException   ErrorType = "NotImplementedException"
	RequestTimeoutException   ErrorType = "RequestTimeoutException"
	InternalError             ErrorType = "InternalError"
	HttpNotFoundException     ErrorType = "HttpNotFoundException"
	ConflictException         ErrorType = "ConflictException"
	ResourceConflictException ErrorType = "ResourceConflictException"
	UnauthorizedException     ErrorType = "UnauthorizedException"

	// ClusterResourceNotReadyException returned to user when underlying resource is creating
	ClusterResourceNotReadyException ErrorType = "ClusterResourceNotReadyException"
	ComponentNotReadyException       ErrorType = "ComponentNotReadyException"

	DataBaseOperationException ErrorType = "DataBaseOperationException"
	DataCryptException         ErrorType = "DataCryptException"

	CPromAgentNotInstallException ErrorType = "CPromAgentNotInstallException"

	ServiceUnavailable ErrorType = "ServiceUnavailable"

	HostingInstanceAssociationGatewaysException       ErrorType = "HostingInstanceAssociationGateways"
	HostingInstanceAssociationRemoteClustersException ErrorType = "HostingInstanceAssociationRemoteClusters"

	// CProm scrape job
	CPromScrapeJobNotFoundException = "not found scrapeJob"
	CPromAgentNotFoundException     = "not found agent"
	CPromInstanceNotFoundException  = "not found instance"

	AddClusterFailException ErrorType = "AddClusterFailException"
	InactiveException       ErrorType = "InactiveException"
	OperationNotSupport     ErrorType = "OperationNotSupport"
)

// Define error message that will be returned in http body
var message = map[ErrorType]string{
	ClusterAccessFailException:           "Cluster access fail",
	InvalidParameterValueException:       "One of the parameters in the request is invalid",
	InvalidRequestContentException:       "The request body could not be parsed as JSON",
	InvalidSecurityGroupIDException:      "The Security Group ID provided in the function VPC configuration is invalid",
	InvalidSubnetIDException:             "The Subnet ID provided in the function VPC configuration is invalid",
	MissingParameterValueException:       "One of the parameters in the request is missing",
	PolicyLengthExceededException:        "Function access policy is limited to 20 KB",
	ServiceException:                     "Service encountered an internal error",
	ServiceUnavailable:                   "Service is temporarily unavailable",
	SubnetIPAddressLimitReachedException: "Unable to set up VPC access because one or more configured subnets has no available IP addresses",
	UnsupportedMediaTypeException:        "The content type of the Invoke request body is not JSON",
	PreconditionValidationException:      "Precondition Validation exception",

	InternalError:               "We encountered an internal error Please try again",
	UnrecognizedClientException: "Access denied",

	NotImplementedException:   "Function not implemented",
	HttpNotFoundException:     "404 Not found",
	ResourceConflictException: "The resource already exists",
	UnauthorizedException:     "Authentication required",

	// 修改
	ClusterResourceNotReadyException: "Underlying resource not ready",
	ResourceNameConflictException:    "Resource name conflict",

	DataBaseOperationException: "DB operation failed",
	DataCryptException:         "Data encrypt or decrypt failed",

	HostingInstanceAssociationGatewaysException:       "mesh instance associated with gateways, please delete them firstly",
	HostingInstanceAssociationRemoteClustersException: "mesh instance associated with remote cluster, please delete them firstly",

	AddClusterFailException: "wait until blb is ready to add the remote cluster",

	CPromAgentNotInstallException: "CProm Agent not installed",
	InactiveException:             "Product MSE Inactive",
	OperationNotSupport:           "Operation not support",
}

type Error struct {
	Code    ErrorType `json:"Code,omitempty"`
	Message string    `json:"Message"`
}

var DefaultError = Error{
	Code:    InternalError,
	Message: message[InternalError],
}

// NewUnknownError 未知错误
func NewUnknownError(errs ...error) *echo.HTTPError {
	e := &echo.HTTPError{
		Code:     http.StatusInternalServerError,
		Message:  DefaultError,
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

type ExceptionGenerator func(msg string, errs ...error) *echo.HTTPError

// NewServiceException 未知错误的另一种形式
func NewServiceException(msg string, errs ...error) *echo.HTTPError {
	errs = append([]error{errors.New(msg)}, errs...)
	return NewUnknownError(errs...)
}

// NewDBOperationException DB操作失败
func NewDBOperationException(errs ...error) *echo.HTTPError {
	return NewUnknownError(errs...)
}

// NewUnauthorizedException 未授权错误
func NewUnauthorizedException(msg string, errs ...error) *echo.HTTPError {
	errs = append([]error{errors.New(msg)}, errs...)
	e := &echo.HTTPError{
		Code: http.StatusUnauthorized,
		Message: Error{
			Code:    UnauthorizedException,
			Message: message[UnauthorizedException],
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

// NewInactiveException 产品未开通
func NewInactiveException(msg string, errs ...error) *echo.HTTPError {
	errs = append([]error{errors.New(msg)}, errs...)
	e := &echo.HTTPError{
		Code: http.StatusForbidden,
		Message: Error{
			Code:    InactiveException,
			Message: message[InactiveException],
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

// NewServiceUnavailableException 服务不可用
func NewServiceUnavailableException(msg string, errs ...error) *echo.HTTPError {
	errs = append([]error{errors.New(msg)}, errs...)
	e := &echo.HTTPError{
		Code: http.StatusServiceUnavailable,
		Message: Error{
			Code:    ServiceUnavailable,
			Message: message[ServiceUnavailable],
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

// NewOperationNotSupportException 操作不支持
func NewOperationNotSupportException(msg string, errs ...error) *echo.HTTPError {
	if msg == "" {
		msg = message[OperationNotSupport]
	}
	errs = append([]error{errors.New(msg)}, errs...)
	e := &echo.HTTPError{
		Code: http.StatusForbidden,
		Message: Error{
			Code:    OperationNotSupport,
			Message: msg,
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

// NewUnrecognizedClientException 无权限禁止
func NewUnrecognizedClientException(msg string, errs ...error) *echo.HTTPError {
	errs = append([]error{errors.New(msg)}, errs...)
	e := &echo.HTTPError{
		Code: http.StatusForbidden,
		Message: Error{
			Code:    UnrecognizedClientException,
			Message: message[UnrecognizedClientException],
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

func NewInvalidParameterValueExceptionWithoutMsg(errs ...error) *echo.HTTPError {
	e := &echo.HTTPError{
		Code: http.StatusBadRequest,
		Message: Error{
			Code:    InvalidParameterValueException,
			Message: message[InvalidParameterValueException],
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

func NewHostingInstanceAssociationGateways(errs ...error) *echo.HTTPError {
	e := &echo.HTTPError{
		Code: http.StatusBadRequest,
		Message: Error{
			Code:    HostingInstanceAssociationGatewaysException,
			Message: message[HostingInstanceAssociationGatewaysException],
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

func NewHostingInstanceAssociationRemoteClusters(errs ...error) *echo.HTTPError {
	e := &echo.HTTPError{
		Code: http.StatusBadRequest,
		Message: Error{
			Code:    HostingInstanceAssociationRemoteClustersException,
			Message: message[HostingInstanceAssociationRemoteClustersException],
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

func AddCluster(errs ...error) *echo.HTTPError {
	e := &echo.HTTPError{
		Code: http.StatusBadRequest,
		Message: Error{
			Code:    AddClusterFailException,
			Message: message[AddClusterFailException],
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

func NewCPromAgentNotInstallException(errs ...error) *echo.HTTPError {
	e := &echo.HTTPError{
		Code: http.StatusBadRequest,
		Message: Error{
			Code:    CPromAgentNotInstallException,
			Message: message[CPromAgentNotInstallException],
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}
