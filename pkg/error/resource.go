package error

import (
	"net/http"

	"github.com/VividCortex/mysqlerr"
	"github.com/go-sql-driver/mysql"
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"
)

// NewResourceConflictException 资源已经存在，冲突错误
func NewResourceConflictException(msg string, errs ...error) *echo.HTTPError {
	responseMessage := "The resource already exists"
	if msg != "" {
		errs = append([]error{errors.New(msg)}, errs...)
		responseMessage = msg
	}
	e := &echo.HTTPError{
		Code: http.StatusConflict,
		Message: Error{
			Code:    ConflictException,
			Message: responseMessage,
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

func ConvertMysqlErr(err error) *echo.HTTPError {
	if driverErr, ok := err.(*mysql.MySQLError); ok {
		switch driverErr.Number {
		case mysqlerr.ER_DUP_ENTRY:
			fallthrough
		case mysqlerr.ER_DUP_KEYNAME:
			fallthrough
		case mysqlerr.ER_DUP_UNIQUE:
			return NewResourceConflictException("", driverErr)
		default:
			return NewDBOperationException(driverErr)
		}
	} else {
		return NewDBOperationException(err)
	}
}

// NewResourceNotFoundException 资源找不到 如需自定义资源，请填写msg字段
func NewResourceNotFoundException(msg string, errs ...error) *echo.HTTPError {
	responseMessage := "The resource specified in the request does not exist"
	if msg != "" {
		errs = append([]error{errors.New(msg)}, errs...)
		responseMessage = msg
	}

	e := &echo.HTTPError{
		Code: http.StatusNotFound,
		Message: Error{
			Code:    "ResourceNotFoundException",
			Message: responseMessage,
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}
