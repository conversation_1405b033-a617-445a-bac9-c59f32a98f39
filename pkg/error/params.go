package error

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"
)

func NewInvalidParameterValueException(msg string, errs ...error) *echo.HTTPError {
	errs = append([]error{errors.New(msg)}, errs...)
	e := &echo.HTTPError{
		Code: http.StatusBadRequest,
		Message: Error{
			Code:    InvalidParameterValueException,
			Message: message[InvalidParameterValueException],
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

// NewInvalidParameterInputValueException 参数错误时抛出的msg需要特殊说明并在国际化那边配置时用此函数，
// 通用的参数错误用NewInvalidParameterValueException 或者用NewInvalidParameterInputValueException("", err)
func NewInvalidParameterInputValueException(msg string, errs ...error) *echo.HTTPError {
	responseMessage := message[InvalidParameterValueException]
	if msg != "" {
		errs = append([]error{errors.New(msg)}, errs...)
		responseMessage = msg
	}
	e := &echo.HTTPError{
		Code: http.StatusBadRequest,
		Message: Error{
			Code:    InvalidParameterValueException,
			Message: responseMessage,
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}

func NewMissingParametersException(msg string, errs ...error) *echo.HTTPError {
	responseMessage := message[InvalidParameterValueException]
	if msg != "" {
		errs = append([]error{errors.New(msg)}, errs...)
		responseMessage = msg
	}
	e := &echo.HTTPError{
		Code: http.StatusBadRequest,
		Message: Error{
			Code:    MissingParameterValueException,
			Message: responseMessage,
		},
		Internal: utilerrors.NewAggregate(errs),
	}
	return e
}
