package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strconv"
)

var _ Request = &ListUserRequest{}

type ListUserRequest struct {
	PageRequest
	HideOwner bool `query:"hideOwner"`
}

type ListUserResponse struct {
	PageResponse
	Users []User `json:"users"`
}

func (r *ListUserRequest) ApiName() string {
	return "ListUser"
}

func (r *ListUserRequest) FillAndValidate(release string) error {
	return r.PageRequest.FillAndValidate()
}

func (r *ListUserRequest) Method() string { return http.MethodGet }

func (r *ListUserRequest) URI() string { return "/core/v1/users" }

func (r *ListUserRequest) QueryParams() map[string]string {
	m := map[string]string{
		"offset": strconv.FormatInt(r.Offset(), 10),
		"limit":  strconv.FormatInt(r.Limit(), 10),
	}
	if r.HideOwner {
		m["hide_owner"] = "true"
	}
	return m
}

func (r *ListUserRequest) Body(release string) (io.Reader, error) {
	return nil, nil
}

func (r *ListUserRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ListUserRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchQueryResponse)
	if !ok {
		return nil, fmt.Errorf("parse list user response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ListUserResponse{
		PageResponse: PageResponse{
			PageNo:     r.pageNo,
			PageSize:   r.pageSize,
			TotalCount: rawResponse.GetAmount().GetValue(),
		},
		Users: make([]User, 0),
	}

	for _, rawUser := range rawResponse.GetUsers() {
		response.Users = append(response.Users, User{
			ID:         rawUser.GetId().GetValue(),
			Name:       rawUser.GetName().GetValue(),
			Owner:      rawUser.GetOwner().GetValue(),
			Comment:    rawUser.GetComment().GetValue(),
			CreateTime: rawUser.GetCtime().GetValue(),
			UpdateTime: rawUser.GetMtime().GetValue(),
		})
	}
	return response, nil
}
