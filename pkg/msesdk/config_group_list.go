package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strconv"
)

var _ Request = &ListConfigGroupRequest{}

type ListConfigGroupRequest struct {
	PageRequest
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
}

type ListConfigGroupResponse struct {
	PageResponse
	ConfigGroups []ConfigGroup `json:"configGroups"`
}

func (r *ListConfigGroupRequest) ApiName() string {
	return "ListConfigGroup"
}

func (r *ListConfigGroupRequest) FillAndValidate(release string) error {
	return r.PageRequest.FillAndValidate()
}

func (r *ListConfigGroupRequest) Method() string { return http.MethodGet }

func (r *ListConfigGroupRequest) URI() string { return "/config/v1/configfilegroups" }

func (r *ListConfigGroupRequest) QueryParams() map[string]string {
	return map[string]string{
		"namespace": r.Namespace,
		"name":      r.Name,
		"offset":    strconv.FormatInt(r.Offset(), 10),
		"limit":     strconv.FormatInt(r.Limit(), 10),
	}
}

func (r *ListConfigGroupRequest) Body(release string) (io.Reader, error) { return nil, nil }

func (r *ListConfigGroupRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ListConfigGroupRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigBatchQueryResponse)
	if !ok {
		return nil, fmt.Errorf("parse list config group response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ListConfigGroupResponse{
		PageResponse: PageResponse{
			PageNo:     r.pageNo,
			PageSize:   r.pageSize,
			TotalCount: rawResponse.Total.GetValue(),
		},
		ConfigGroups: make([]ConfigGroup, 0),
	}

	for _, rawGroup := range rawResponse.ConfigFileGroups {
		group := ConfigGroup{
			ConfigGroupInput: ConfigGroupInput{
				Name:      rawGroup.GetName().GetValue(),
				Namespace: rawGroup.GetNamespace().GetValue(),
				Comment:   func(s string) *string { return &s }(rawGroup.GetComment().GetValue()),
			},
			FileCount:  rawGroup.GetFileCount().GetValue(),
			CreateTime: rawGroup.GetCreateTime().GetValue(),
			UpdateTime: rawGroup.GetModifyTime().GetValue(),
			Editable:   rawGroup.GetEditable().GetValue(),
		}
		response.ConfigGroups = append(response.ConfigGroups, group)
	}
	return response, nil
}
