package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &UpdateAuthStrategyRequest{}

type UpdateAuthStrategyRequest struct {
	AuthStrategies []UpdateAuthStrategy `json:"authStrategies"`
}

type UpdateAuthStrategy struct {
	StrategyID     string    `json:"id"`
	AddResource    *Resource `json:"addResource"`
	RemoveResource *Resource `json:"removeResource"`
}

func (r *UpdateAuthStrategyRequest) ApiName() string {
	return "UpdateAuthStrategy"
}

func (r *UpdateAuthStrategyRequest) FillAndValidate(release string) error {
	return nil
}

func (r *UpdateAuthStrategyRequest) Method() string { return http.MethodPut }

func (r *UpdateAuthStrategyRequest) URI() string { return "/core/v1/auth/strategies" }

func (r *UpdateAuthStrategyRequest) QueryParams() map[string]string {
	return nil
}

func (r *UpdateAuthStrategyRequest) Body(release string) (io.Reader, error) {
	payload := make([]map[string]interface{}, 0)
	for _, authStrategy := range r.AuthStrategies {
		m := map[string]interface{}{
			"id": authStrategy.StrategyID,
		}
		if authStrategy.AddResource != nil && len(authStrategy.AddResource.Namespaces) > 0 {
			namespaces := make([]map[string]string, 0)
			for _, namespace := range authStrategy.AddResource.Namespaces {
				namespaces = append(namespaces, map[string]string{
					"id": namespace,
				})
			}
			m["add_resources"] = map[string]interface{}{
				"namespaces": namespaces,
			}
		}
		if authStrategy.RemoveResource != nil && len(authStrategy.RemoveResource.Namespaces) > 0 {
			namespaces := make([]map[string]string, 0)
			for _, namespace := range authStrategy.RemoveResource.Namespaces {
				namespaces = append(namespaces, map[string]string{
					"id": namespace,
				})
			}
			m["remove_resources"] = map[string]interface{}{
				"namespaces": namespaces,
			}
		}
		payload = append(payload, m)
	}

	body, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *UpdateAuthStrategyRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *UpdateAuthStrategyRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchWriteResponse)
	if !ok {
		return nil, fmt.Errorf("parse update auth strategy response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
