package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strconv"
)

var _ Request = &ListConfigFileRequest{}

type ListConfigFileRequest struct {
	PageRequest
	Name        string `json:"name"`
	Namespace   string `json:"namespace"`
	Group       string `json:"group"`
	Status      string `json:"status"`
	OnlyBetaing string `json:"onlyBetaing"`
}

type ListConfigFileResponse struct {
	PageResponse
	ConfigFiles []ConfigFile `json:"configFiles"`
}

func (r *ListConfigFileRequest) ApiName() string {
	return "ListConfigFile"
}

func (r *ListConfigFileRequest) FillAndValidate(release string) error {
	return r.PageRequest.FillAndValidate()
}

func (r *ListConfigFileRequest) Method() string { return http.MethodGet }

func (r *ListConfigFileRequest) URI() string { return "/config/v1/configfiles/search" }

func (r *ListConfigFileRequest) QueryParams() map[string]string {
	return map[string]string{
		"namespace":    r.Namespace,
		"name":         r.Name,
		"group":        r.Group,
		"offset":       strconv.FormatInt(r.Offset(), 10),
		"limit":        strconv.FormatInt(r.Limit(), 10),
		"status":       r.Status,
		"only_betaing": r.OnlyBetaing,
	}
}

func (r *ListConfigFileRequest) Body(release string) (io.Reader, error) { return nil, nil }

func (r *ListConfigFileRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ListConfigFileRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigBatchQueryResponse)
	if !ok {
		return nil, fmt.Errorf("parse list config file response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ListConfigFileResponse{
		PageResponse: PageResponse{
			PageNo:     r.pageNo,
			PageSize:   r.pageSize,
			TotalCount: rawResponse.Total.GetValue(),
		},
		ConfigFiles: make([]ConfigFile, 0),
	}

	for _, rawFile := range rawResponse.ConfigFiles {
		tags := make([]Tag, 0)
		for _, rawTag := range rawFile.Tags {
			tags = append(tags, Tag{
				Key:   rawTag.GetKey().GetValue(),
				Value: rawTag.GetValue().GetValue(),
			})
		}
		betaLabels := make([]Tag, 0)
		for _, label := range rawFile.GetBetaStatus().GetBetaLabels() {
			value := ""
			if label.GetValue() != nil {
				value = label.GetValue().Value.GetValue()
			}
			betaLabels = append(betaLabels, Tag{
				Key:   label.GetKey(),
				Value: value,
			})
		}
		file := ConfigFile{
			ConfigFileInput: ConfigFileInput{
				Name:      rawFile.GetName().GetValue(),
				Namespace: rawFile.GetNamespace().GetValue(),
				Group:     rawFile.GetGroup().GetValue(),
				Format:    rawFile.GetFormat().GetValue(),
				Comment:   func(s string) *string { return &s }(rawFile.GetComment().GetValue()),
				Content:   func(s string) *string { return &s }(rawFile.GetContent().GetValue()),
				Tags:      tags,
			},
			Status:             rawFile.GetStatus().GetValue(),
			CreateTime:         rawFile.GetCreateTime().GetValue(),
			UpdateTime:         rawFile.GetModifyTime().GetValue(),
			ReleaseTime:        rawFile.GetReleaseTime().GetValue(),
			CurrentReleaseName: rawFile.GetCurrentReleaseName().GetValue(),
			BetaStatus: &BetaStatus{
				Active:      rawFile.GetBetaStatus().GetActive().GetValue(),
				UpdateTime:  rawFile.GetBetaStatus().GetModifyTime().GetValue(),
				ReleaseTime: rawFile.GetBetaStatus().GetReleaseTime().GetValue(),
				BetaLabels:  betaLabels,
				ReleaseName: rawFile.GetBetaStatus().GetReleaseName().GetValue(),
			},
		}
		response.ConfigFiles = append(response.ConfigFiles, file)
	}
	return response, nil
}
