package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/common"
	"io"
	"net/http"
)

var _ Request = &CreateConfigReleaseRequest{}

type CreateConfigReleaseRequest struct {
	ConfigReleaseInput
}

func (r *CreateConfigReleaseRequest) ApiName() string {
	return "CreateConfigRelease"
}

func (r *CreateConfigReleaseRequest) FillAndValidate(release string) error {
	return r.ConfigReleaseInput.WriteFillAndValidate(release)
}

func (r *CreateConfigReleaseRequest) Method() string { return http.MethodPost }

func (r *CreateConfigReleaseRequest) URI() string { return "/config/v1/configfiles/release" }

func (r *CreateConfigReleaseRequest) QueryParams() map[string]string { return nil }

func (r *CreateConfigReleaseRequest) Body(release string) (io.Reader, error) {
	betaLabels := make([]map[string]interface{}, 0)
	for _, label := range r.BetaLabels {
		betaLabel := map[string]interface{}{
			"key": label.Key,
			"value": map[string]string{
				"value":      label.Value,
				"type":       "EXACT",
				"value_type": "TEXT",
			},
		}
		betaLabels = append(betaLabels, betaLabel)
	}
	m := map[string]interface{}{
		"name":      r.Name,
		"namespace": r.Namespace,
		"group":     r.Group,
		"file_name": r.File,
		"releaseDescription": func(s *string) string {
			if s == nil {
				return ""
			} else {
				return *s
			}
		}(r.Comment),
	}
	if ge, err := common.IsReleaseAfterOrEqual(release, "1.2.1"); err != nil {
		return nil, fmt.Errorf("invalid release %v", release)
	} else if ge {
		m["betaLabels"] = betaLabels
		m["releaseType"] = r.Type
		m["content"] = r.Content
		m["reserveGrayRelease"] = r.ReserveGrayRelease
	}
	body, err := json.Marshal(m)
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *CreateConfigReleaseRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *CreateConfigReleaseRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigResponse)
	if !ok {
		return nil, fmt.Errorf("parse create config release response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return map[string]string{"name": r.Name}, nil
}
