package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &UpdateUserRequest{}

type UpdateUserRequest struct {
	Name     string  `json:"name"`
	Owner    string  `json:"owner"`
	Password *string `json:"password"`
	Comment  *string `json:"comment"`
}

func (r *UpdateUserRequest) ApiName() string {
	return "UpdateUser"
}

func (r *UpdateUserRequest) FillAndValidate(release string) error {
	return nil
}

func (r *UpdateUserRequest) Method() string { return http.MethodPut }

func (r *UpdateUserRequest) URI() string { return "/core/v1/user" }

func (r *UpdateUserRequest) QueryParams() map[string]string { return nil }

func (r *UpdateUserRequest) Body(release string) (io.Reader, error) {
	m := map[string]string{
		"name": r.Name,
	}
	if r.Name != r.Owner {
		m["owner"] = r.Owner
	}
	if r.Password != nil && *r.Password != "" {
		m["password"] = *r.Password
	}
	if r.Comment != nil {
		m["comment"] = *r.Comment
	}
	body, err := json.Marshal(m)

	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *UpdateUserRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *UpdateUserRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.Response)
	if !ok {
		return nil, fmt.Errorf("parse create user response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
