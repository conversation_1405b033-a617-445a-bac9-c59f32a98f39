package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &UpdateServiceInstanceRequest{}

type UpdateServiceInstanceRequest struct {
	ServiceInstanceInput
	ServiceInstanceID string `json:"-"`
}

func (r *UpdateServiceInstanceRequest) ApiName() string {
	return "UpdateServiceInstance"
}

func (r *UpdateServiceInstanceRequest) FillAndValidate(release string) error {
	if r.ServiceInstanceID == "" {
		return csmErr.NewInvalidParameterInputValueException("serviceInstanceId is empty")
	}
	return nil
}

func (r *UpdateServiceInstanceRequest) Method() string { return http.MethodPut }

func (r *UpdateServiceInstanceRequest) URI() string { return "/naming/v1/instances" }

func (r *UpdateServiceInstanceRequest) QueryParams() map[string]string {
	return nil
}

func (r *UpdateServiceInstanceRequest) Body(release string) (io.Reader, error) {
	serviceInstance := ServiceInstance{
		ID:                r.ServiceInstanceID,
		Host:              r.Host,
		Port:              r.Port,
		Metadata:          r.Metadata,
		Service:           r.ServiceName,
		Namespace:         r.Namespace,
		EnableHealthCheck: r.HealthCheckEnable,
		Isolate:           r.IsolateEnable,
	}
	if serviceInstance.EnableHealthCheck {
		serviceInstance.HealthCheck = &HealthCheck{
			Type: "HEARTBEAT",
			Heartbeat: Heartbeat{
				Ttl: r.TTL,
			},
		}
	}
	if len(serviceInstance.Metadata) > 0 {
		if v, ok := serviceInstance.Metadata["version"]; ok && v != "" {
			serviceInstance.Version = v
		}
		if v, ok := serviceInstance.Metadata["protocol"]; ok && v != "" {
			serviceInstance.Protocol = v
		}
		if v, ok := serviceInstance.Metadata["region"]; ok && v != "" {
			serviceInstance.Location.Region = v
		}
		if v, ok := serviceInstance.Metadata["zone"]; ok && v != "" {
			serviceInstance.Location.Zone = v
		}
		if v, ok := serviceInstance.Metadata["campus"]; ok && v != "" {
			serviceInstance.Location.Campus = v
		}
	}
	body, err := json.Marshal([]ServiceInstance{serviceInstance})
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *UpdateServiceInstanceRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *UpdateServiceInstanceRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchWriteResponse)
	if !ok {
		return nil, fmt.Errorf("parse create service instance response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
