package msesdk

import (
	"archive/zip"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
)

var _ Request = &ExportConfigFileRequest{}

type ExportConfigFileRequest struct {
	Namespace       string
	ConfigGroupsStr string
	configGroups    []string
}

func (r *ExportConfigFileRequest) ApiName() string {
	return "ExportConfigFile"
}

type ExportConfigFileResponse []byte

func (r *ExportConfigFileRequest) FillAndValidate(release string) error {
	if r.Namespace == "" {
		return csmErr.NewInvalidParameterInputValueException("namespace is required")
	}
	if r.ConfigGroupsStr != "" {
		groups := strings.Split(r.ConfigGroupsStr, ",")
		groupMap := make(map[string]struct{})
		for _, g := range groups {
			if g == "" {
				continue
			}
			groupMap[g] = struct{}{}
		}
		for g := range groupMap {
			r.configGroups = append(r.configGroups, g)
		}
	}
	return nil
}

func (r *ExportConfigFileRequest) Method() string { return http.MethodPost }

func (r *ExportConfigFileRequest) URI() string { return "/config/v1/configfiles/export" }

func (r *ExportConfigFileRequest) QueryParams() map[string]string { return nil }

func (r *ExportConfigFileRequest) Body(release string) (io.Reader, error) {
	m := map[string]interface{}{
		"namespace": r.Namespace,
	}
	if len(r.configGroups) > 0 {
		m["groups"] = r.configGroups
	}
	body, err := json.Marshal(m)
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *ExportConfigFileRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ExportConfigFileRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigExportResponse)
	if !ok {
		return nil, fmt.Errorf("parse import config file response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	data := rawResponse.GetData().GetValue()

	// 去掉META文件
	reader, zipErr := zip.NewReader(bytes.NewReader(data), int64(len(data)))
	if zipErr != nil {
		return nil, zipErr
	}

	var buf bytes.Buffer
	writer := zip.NewWriter(&buf)

	for _, file := range reader.File {
		if file.Name == "META" {
			continue
		}

		// 保留原始 header 信息（包括目录结构、时间戳等）
		header := file.FileHeader
		w, err := writer.CreateHeader(&header)
		if err != nil {
			return nil, err
		}
		// 目录项不需要复制内容
		if file.FileInfo().IsDir() {
			continue
		}

		rc, err := file.Open()
		if err != nil {
			return nil, err
		}

		_, err = io.Copy(w, rc)
		rc.Close()
		if err != nil {
			return nil, err
		}
	}

	zipErr = writer.Close()
	if zipErr != nil {
		return nil, zipErr
	}

	return ExportConfigFileResponse(buf.Bytes()), nil
}
