package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strconv"
)

var _ Request = &ListServiceInstanceRequest{}

type ListServiceInstanceRequest struct {
	PageRequest
	ServiceName       string `json:"-"`
	ServiceInstanceID string `json:"-"`
	Host              string `json:"-"`
	HealthStatus      string `json:"-"`
	IsolateStatus     string `json:"-"`
	Namespace         string `json:"-"`
}

type ListServiceInstanceResponse struct {
	PageResponse
	Result []ServiceInstanceOutput `json:"result"`
}

func (r *ListServiceInstanceRequest) ApiName() string {
	return "ListServiceInstance"
}

func (r *ListServiceInstanceRequest) FillAndValidate(release string) error {
	if r.ServiceName == "" {
		return csmErr.NewInvalidParameterInputValueException("serviceName is empty")
	}
	return r.PageRequest.FillAndValidate()
}

func (r *ListServiceInstanceRequest) Method() string { return http.MethodGet }

func (r *ListServiceInstanceRequest) URI() string { return "/naming/v1/instances" }

func (r *ListServiceInstanceRequest) QueryParams() map[string]string {
	m := map[string]string{
		"offset":              strconv.FormatInt(r.Offset(), 10),
		"limit":               strconv.FormatInt(r.Limit(), 10),
		"service":             r.ServiceName,
		"show_last_heartbeat": "true",
	}
	if r.ServiceInstanceID != "" {
		m["id"] = r.ServiceInstanceID
	}
	if r.Host != "" {
		m["host"] = r.Host
	}
	if r.HealthStatus != "" {
		m["healthy"] = r.HealthStatus
	}
	if r.IsolateStatus != "" {
		m["isolate"] = r.IsolateStatus
	}
	if r.Namespace != "" {
		m["namespace"] = r.Namespace
	}
	return m
}

func (r *ListServiceInstanceRequest) Body(release string) (io.Reader, error) {
	return nil, nil
}

func (r *ListServiceInstanceRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ListServiceInstanceRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchQueryResponse)
	if !ok {
		return nil, fmt.Errorf("parse list service response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ListServiceInstanceResponse{
		PageResponse: PageResponse{
			PageNo:     r.pageNo,
			PageSize:   r.pageSize,
			TotalCount: rawResponse.GetAmount().GetValue(),
		},
		Result: make([]ServiceInstanceOutput, 0),
	}

	for _, rawInstance := range rawResponse.Instances {
		instance := ServiceInstanceOutput{
			ServiceName:       rawInstance.GetService().GetValue(),
			ServiceInstanceID: rawInstance.GetId().GetValue(),
			Namespace:         rawInstance.GetNamespace().GetValue(),
			Host:              rawInstance.GetHost().GetValue(),
			Port:              rawInstance.GetPort().GetValue(),
			Weight:            rawInstance.GetWeight().GetValue(),
			HealthStatus:      func(b bool) *bool { return &b }(rawInstance.GetHealthy().GetValue()),
			IsolateEnable:     rawInstance.GetIsolate().GetValue(),
			CreateTime:        rawInstance.GetCtime().GetValue(),
			UpdateTime:        rawInstance.GetMtime().GetValue(),
			HealthCheckEnable: rawInstance.GetEnableHealthCheck().GetValue(),
			Metadata:          rawInstance.GetMetadata(),
		}
		if instance.HealthCheckEnable {
			instance.TTL = rawInstance.GetHealthCheck().GetHeartbeat().GetTtl().GetValue()
		}
		if len(instance.Metadata) == 0 {
			instance.Metadata = map[string]string{}
		}
		if heartbeatTime, ok := instance.Metadata["last-heartbeat-time"]; ok {
			instance.LastHeartbeatTime = heartbeatTime
		}
		if rawInstance.GetProtocol().GetValue() != "" {
			instance.Metadata["protocol"] = rawInstance.GetProtocol().GetValue()
		}
		if rawInstance.GetVersion().GetValue() != "" {
			instance.Metadata["version"] = rawInstance.GetVersion().GetValue()
		}
		if rawInstance.GetLocation().GetZone().GetValue() != "" {
			instance.Metadata["zone"] = rawInstance.GetLocation().GetZone().GetValue()
		}
		if rawInstance.GetLocation().GetCampus().GetValue() != "" {
			instance.Metadata["campus"] = rawInstance.GetLocation().GetCampus().GetValue()
		}
		if rawInstance.GetLocation().GetRegion().GetValue() != "" {
			instance.Metadata["region"] = rawInstance.GetLocation().GetRegion().GetValue()
		}
		for k, v := range instance.Metadata {
			if v == "<nil>" {
				instance.Metadata[k] = ""
			}
		}
		response.Result = append(response.Result, instance)
	}
	return response, nil
}
