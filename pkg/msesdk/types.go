package msesdk

import (
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/BurntSushi/toml"
	"github.com/golang/protobuf/proto"
	"golang.org/x/net/html"
	"gopkg.in/yaml.v3"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/common"
)

type Request interface {
	FillAndValidate(release string) error

	Method() string
	URI() string
	QueryParams() map[string]string
	Body(release string) (io.Reader, error)
	Headers() map[string]string

	ParseResponse(proto.Message) (Response, error)

	ApiName() string
}

type Response interface{}

type PageRequest struct {
	pageNo   int64
	pageSize int64

	PageNoStr   string
	PageSizeStr string
}

type PageResponse struct {
	PageNo     int64  `json:"pageNo"`
	PageSize   int64  `json:"pageSize"`
	TotalCount uint32 `json:"totalCount"`
}

type ConfigGroup struct {
	ConfigGroupInput
	FileCount  uint64 `json:"fileCount"`
	CreateTime string `json:"createTime"`
	UpdateTime string `json:"updateTime"`
	Editable   bool   `json:"editable"`
}

type ConfigListener struct {
	IP          string `json:"ip"`
	Labels      []Tag  `json:"labels"`
	ReleaseName string `json:"releaseName"`
	MD5         string `json:"md5"`
	ReleaseType string `json:"releaseType"`
}

type ConfigGroupInput struct {
	Name      string  `json:"name"`
	Namespace string  `json:"namespace"`
	Comment   *string `json:"comment,omitempty"`
}

type ConfigFile struct {
	ConfigFileInput
	Status             string      `json:"status"`
	CreateTime         string      `json:"createTime"`
	UpdateTime         string      `json:"updateTime"`
	ReleaseTime        string      `json:"releaseTime"`
	CurrentReleaseName string      `json:"currentReleaseName"`
	BetaStatus         *BetaStatus `json:"betaStatus,omitempty"`
}

type BetaStatus struct {
	Active      bool   `json:"active"`
	UpdateTime  string `json:"updateTime,omitempty"`
	ReleaseTime string `json:"releaseTime,omitempty"`
	BetaLabels  []Tag  `json:"betaLabels,omitempty"`
	ReleaseName string `json:"releaseName,omitempty"`
}

type ConfigFileInput struct {
	Namespace string  `json:"namespace"`
	Group     string  `json:"group"`
	Name      string  `json:"name"`
	Format    string  `json:"format"`
	Comment   *string `json:"comment,omitempty"`
	Content   *string `json:"content,omitempty"`
	Tags      []Tag   `json:"tags,omitempty"`
}

type ConfigRelease struct {
	ConfigReleaseInput
	CreateBy   string `json:"createBy"`
	CreateTime string `json:"createTime"`
	Active     bool   `json:"active"`
	Format     string `json:"format"`
	Tags       []Tag  `json:"tags"`
	Content    string `json:"content"`
}

type ConfigReleaseInput struct {
	Namespace          string  `json:"namespace"`
	Group              string  `json:"group"`
	File               string  `json:"file"`
	Name               string  `json:"name"`
	Comment            *string `json:"comment,omitempty"`
	Type               string  `json:"type"`
	ReserveGrayRelease bool    `json:"reserveGrayRelease"`
	BetaLabels         []Tag   `json:"betaLabels"`
	Content            string  `json:"content,omitempty"`
	IsBeta             bool    `json:"isBeta,omitempty"`
}

type Tag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type ClientLabel struct {
	Key   string           `json:"key"`
	Value ClientLabelValue `json:"value"`
}

type ClientLabelValue struct {
	Value     string `json:"value"`
	Type      string `json:"type"`
	ValueType string `json:"value_type"`
}

type ConfigReleaseRecord struct {
	Namespace  string `json:"namespace"`
	Group      string `json:"group"`
	File       string `json:"file"`
	Release    string `json:"release"`
	Type       string `json:"type"`
	Status     string `json:"status"`
	CreateTime string `json:"createTime"`
	CreateBy   string `json:"createBy"`
	Format     string `json:"format"`
	Content    string `json:"content"`
	Comment    string `json:"comment"`
	BetaLabels []Tag  `json:"betaLabels"`
}

func (r *PageRequest) FillAndValidate() (err error) {
	r.pageNo, err = strconv.ParseInt(r.PageNoStr, 10, 64)
	if err != nil {
		r.pageNo = 1
	}
	r.pageNo = util.MaxInt64(r.pageNo, 1)

	r.pageSize, err = strconv.ParseInt(r.PageSizeStr, 10, 64)
	if err != nil {
		r.pageSize = 10
	}
	r.pageSize = util.MaxInt64(r.pageSize, 1)
	r.pageSize = util.MinInt64(r.pageSize, 1000)
	return nil
}

func (r *PageRequest) Offset() int64 {
	return (r.pageNo - 1) * r.pageSize
}

func (r *PageRequest) Limit() int64 {
	return r.pageSize
}

func (r *PageRequest) PageNo() int64 {
	return r.pageNo
}

func (r *PageRequest) PageSize() int64 {
	return r.pageSize
}

func (r *ConfigFileInput) FillAndValidate() error {
	if r.Name == "" {
		return csmErr.NewInvalidParameterInputValueException("name is required")
	}
	if r.Namespace == "" {
		return csmErr.NewInvalidParameterInputValueException("namespace is required")
	}
	if r.Group == "" {
		return csmErr.NewInvalidParameterInputValueException("group is required")
	}
	for _, tag := range r.Tags {
		if tag.Key == "" {
			return csmErr.NewInvalidParameterInputValueException("tag key is required ")
		}
		if tag.Value == "" {
			return csmErr.NewInvalidParameterInputValueException(
				fmt.Sprintf("value of tag %v is required ", tag.Key))
		}
	}
	if r.Format == "" {
		return csmErr.NewInvalidParameterInputValueException("format is required")
	}
	r.Format = strings.ToLower(r.Format)
	formats := []string{"text", "json", "xml", "yaml", "html", "properties", "toml"}
	for _, format := range formats {
		if strings.EqualFold(format, r.Format) {
			return nil
		}
	}
	return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("format should in %v", formats))
}

func (r *ConfigReleaseInput) FillAndValidate() error {
	if r.Namespace == "" {
		return csmErr.NewInvalidParameterInputValueException("namespace is required")
	}
	if r.Group == "" {
		return csmErr.NewInvalidParameterInputValueException("group is required")
	}
	if r.File == "" {
		return csmErr.NewInvalidParameterInputValueException("file is required")
	}
	return nil
}

func (r *ConfigGroupInput) FillAndValidate() error {
	if r.Name == "" {
		return csmErr.NewInvalidParameterInputValueException("name is required")
	}
	if r.Namespace == "" {
		return csmErr.NewInvalidParameterInputValueException("namespace is required")
	}
	return nil
}

func (r *ConfigGroupInput) WriteFillAndValidate() error {
	if err := r.FillAndValidate(); err != nil {
		return err
	}
	if len(r.Name) > 128 {
		return csmErr.NewInvalidParameterInputValueException("invalid name: too long")
	}
	if ok := regexp.MustCompile("^[0-9A-Za-z-._]+$").MatchString(r.Name); !ok {
		return csmErr.NewInvalidParameterInputValueException("invalid name")
	}
	if r.Comment != nil && len([]rune(*r.Comment)) > 200 {
		return csmErr.NewInvalidParameterInputValueException("invalid comment: too long")
	}
	return nil
}

func (r *ConfigFileInput) WriteFillAndValidate() error {
	if err := r.FillAndValidate(); err != nil {
		return err
	}
	if len(r.Name) > 128 {
		return csmErr.NewInvalidParameterInputValueException("invalid name: too long")
	}
	if ok := regexp.MustCompile("^[0-9A-Za-z-._]+$").MatchString(r.Name); !ok {
		return csmErr.NewInvalidParameterInputValueException("invalid name")
	}
	if r.Comment != nil && len([]rune(*r.Comment)) > 200 {
		return csmErr.NewInvalidParameterInputValueException("invalid comment: too long")
	}

	validateFormat := func(input string, format string) bool {
		format = strings.ToLower(format)
		switch format {
		case "json":
			var v interface{}
			err := json.Unmarshal([]byte(input), &v)
			return err == nil
		case "xml":
			var v interface{}
			err := xml.Unmarshal([]byte(input), &v)
			return err == nil
		case "yaml":
			var v interface{}
			err := yaml.Unmarshal([]byte(input), &v)
			return err == nil
		case "html":
			r := strings.NewReader(input)
			_, err := html.Parse(r)
			return err == nil
		case "properties":
			// properties格式没有标准库可以用，跳过格式校验
			return true
		case "toml":
			var data interface{}
			_, err := toml.Decode(input, &data)
			return err == nil
		}
		return true
	}
	if r.Format != "" && r.Content != nil && *r.Content != "" {
		if !validateFormat(*r.Content, r.Format) {
			return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("invalid %s schema", r.Format))
		}
	}

	for _, tag := range r.Tags {
		if tag.Key == "" || tag.Value == "" {
			return csmErr.NewInvalidParameterInputValueException("invalid tag")
		}
		if len([]rune(tag.Key)) > 127 {
			return csmErr.NewInvalidParameterInputValueException("invalid tag key: too long")
		}
		if len([]rune(tag.Value)) > 255 {
			return csmErr.NewInvalidParameterInputValueException("invalid tag value: too long")
		}
	}
	return nil
}

func (r *ConfigReleaseInput) WriteFillAndValidate(release string) error {
	if r.Name == "" {
		r.Name = strconv.FormatInt(time.Now().UnixMilli(), 10)
	}
	if r.Namespace == "" {
		return csmErr.NewInvalidParameterInputValueException("namespace is required")
	}
	if r.Group == "" {
		return csmErr.NewInvalidParameterInputValueException("group is required")
	}
	if r.File == "" {
		return csmErr.NewInvalidParameterInputValueException("file is required")
	}
	if len(r.Name) > 50 {
		return csmErr.NewInvalidParameterInputValueException("invalid name: too long")
	}
	if r.Comment != nil && len([]rune(*r.Comment)) > 50 {
		return csmErr.NewInvalidParameterInputValueException("invalid comment: too long")
	}
	r.Type = strings.ToLower(r.Type)
	if r.Type != "" && r.Type != "full" && r.Type != "gray" {
		return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("invalid release type: %v", r.Type))
	}
	if r.Type != "gray" {
		r.Type = ""
	} else {
		if ge, err := common.IsReleaseAfterOrEqual(release, "1.2.1"); err != nil {
			return fmt.Errorf("invalid release %v", release)
		} else if !ge {
			return csmErr.NewOperationNotSupportException("Registry version must be greater than or equal to 1.2.1.")
		}
		if len(r.BetaLabels) == 0 {
			return csmErr.NewInvalidParameterInputValueException("need beta labels for gray release")
		}
		for _, label := range r.BetaLabels {
			if label.Key == "" || label.Value == "" {
				return csmErr.NewInvalidParameterInputValueException("invalid beta label")
			}
			if label.Key == "CLIENT_IP" {
				if net.ParseIP(label.Value) == nil {
					return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("invalid ip: %v", label.Value))
				}
			}
		}
	}
	return nil
}

type Namespace struct {
	NamespaceInput
	CreateTime           string `json:"createTime"`
	UpdateTime           string `json:"updateTime"`
	ServiceCount         uint32 `json:"serviceCount"`
	InstanceCount        uint32 `json:"instanceCount"`
	HealthInstanceCount  uint32 `json:"healthInstanceCount"`
	ConfigFileGroupCount uint32 `json:"configGroupCount"`
	Editable             bool   `json:"editable"`
}

type NamespaceInput struct {
	Name    string  `json:"name"`
	Comment *string `json:"comment,omitempty"`
}

type Service struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Namespace   string `json:"namespace"`
	TotalCount  uint32 `json:"totalCount"` //总实例数
	CreateTime  string `json:"createTime"`
	UpdateTime  string `json:"updateTime"`
	HealthCount uint32 `json:"healthCount"` //健康实例数
	Editable    bool   `json:"editable"`
}

type ServiceInstanceInput struct {
	Host              string            `json:"host"`
	Port              int               `json:"port"`
	ServiceName       string            `json:"serviceName"`
	HealthCheckEnable bool              `json:"healthCheckEnable"`
	TTL               int               `json:"ttl"`
	Namespace         string            `json:"namespace"`
	Metadata          map[string]string `json:"metadata"`
	IsolateEnable     bool              `json:"isolateEnable"`
}

type ServiceInstanceOutput struct {
	ServiceName       string            `json:"serviceName"`
	Namespace         string            `json:"namespace"`
	ServiceInstanceID string            `json:"serviceInstanceId"`
	Host              string            `json:"host"`
	Port              uint32            `json:"port"`
	Weight            uint32            `json:"weight"`
	HealthStatus      *bool             `json:"healthStatus,omitempty"`
	IsolateEnable     bool              `json:"isolateEnable"`
	HealthCheckEnable bool              `json:"healthCheckEnable"`
	TTL               uint32            `json:"ttl"`
	CreateTime        string            `json:"createTime"`
	UpdateTime        string            `json:"updateTime"`
	Metadata          map[string]string `json:"metadata"`
	LastHeartbeatTime string            `json:"lastHeartbeatTime"`
}

type ServiceInstance struct {
	ID                string            `json:"id"`
	Host              string            `json:"host"`
	Port              int               `json:"port"`
	Service           string            `json:"service"`
	EnableHealthCheck bool              `json:"enableHealthCheck"`
	Namespace         string            `json:"namespace"`
	Metadata          map[string]string `json:"metadata"`
	Isolate           bool              `json:"isolate"`
	HealthCheck       *HealthCheck      `json:"healthCheck,omitempty"`
	Protocol          string            `json:"protocol"`
	Version           string            `json:"version"`
	Location          Location          `json:"location"`
}

type Location struct {
	Region string `json:"region"`
	Zone   string `json:"zone"`
	Campus string `json:"campus"`
}

type HealthCheck struct {
	Type      string    `json:"type"`
	Heartbeat Heartbeat `json:"heartbeat"`
}

type Heartbeat struct {
	Ttl int `json:"ttl"`
}

type ServiceInstanceBatchOperate struct {
	Host      string `json:"host"`
	Namespace string `json:"namespace"`
	Service   string `json:"service"`
	Id        string `json:"id"`
	Isolate   *bool  `json:"isolate,omitempty"`
}

type User struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Owner      string `json:"owner"`
	Comment    string `json:"comment"`
	CreateTime string `json:"createTime"`
	UpdateTime string `json:"updateTime"`
}

type AuthStrategy struct {
	ID       string   `json:"id"`
	Resource Resource `json:"resource"`
	Action   string   `json:"action"`
}

type Resource struct {
	Namespaces []string `json:"namespaces"`
}
