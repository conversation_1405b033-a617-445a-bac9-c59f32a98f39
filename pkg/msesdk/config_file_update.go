package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &UpdateConfigFileRequest{}

type UpdateConfigFileRequest struct {
	ConfigFileInput
}

func (r *UpdateConfigFileRequest) ApiName() string {
	return "UpdateConfigFile"
}

func (r *UpdateConfigFileRequest) FillAndValidate(release string) error {
	return r.ConfigFileInput.WriteFillAndValidate()
}

func (r *UpdateConfigFileRequest) Method() string { return http.MethodPut }

func (r *UpdateConfigFileRequest) URI() string { return "/config/v1/configfiles" }

func (r *UpdateConfigFileRequest) QueryParams() map[string]string { return nil }

func (r *UpdateConfigFileRequest) Body(release string) (io.Reader, error) {
	body, err := json.Marshal(r.ConfigFileInput)
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *UpdateConfigFileRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *UpdateConfigFileRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigResponse)
	if !ok {
		return nil, fmt.Errorf("parse update config file response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
