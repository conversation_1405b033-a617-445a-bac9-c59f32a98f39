package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &CreateUserRequest{}

type CreateUserRequest struct {
	Name     string `json:"name"`
	Password string `json:"password"`
	Comment  string `json:"comment"`
}

func (r *CreateUserRequest) ApiName() string {
	return "CreateUser"
}

func (r *CreateUserRequest) FillAndValidate(release string) error {
	return nil
}

func (r *CreateUserRequest) Method() string { return http.MethodPost }

func (r *CreateUserRequest) URI() string { return "/core/v1/users" }

func (r *CreateUserRequest) QueryParams() map[string]string { return nil }

func (r *CreateUserRequest) Body(release string) (io.Reader, error) {
	body, err := json.Marshal([]map[string]string{{
		"name":     r.Name,
		"password": r.Password,
		"comment":  r.Comment,
	}})
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *CreateUserRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *CreateUserRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchWriteResponse)
	if !ok {
		return nil, fmt.Errorf("parse create user response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
