package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strconv"
)

var _ Request = &ListConfigReleaseRequest{}

type ListConfigReleaseRequest struct {
	PageRequest
	Name        string `json:"name"`
	Namespace   string `json:"namespace"`
	Group       string `json:"group"`
	File        string `json:"file"`
	Active      *bool  `json:"active"`
	WithContent bool   `json:"withContent"`
}

type ListConfigReleaseResponse struct {
	PageResponse
	ConfigReleases []ConfigRelease `json:"configReleases"`
}

func (r *ListConfigReleaseRequest) ApiName() string {
	return "ListConfigRelease"
}

func (r *ListConfigReleaseRequest) FillAndValidate(release string) error {
	return r.PageRequest.FillAndValidate()
}

func (r *ListConfigReleaseRequest) Method() string { return http.MethodGet }

func (r *ListConfigReleaseRequest) URI() string { return "/config/v1/configfiles/releases" }

func (r *ListConfigReleaseRequest) QueryParams() map[string]string {
	params := map[string]string{
		"namespace":    r.Namespace,
		"group":        r.Group,
		"file_name":    r.File,
		"release_name": r.Name,
		"offset":       strconv.FormatInt(r.Offset(), 10),
		"limit":        strconv.FormatInt(r.Limit(), 10),
		"with_content": strconv.FormatBool(r.WithContent),
		"order_type":   "desc",
		"order_field":  "ctime",
	}
	if r.Active != nil {
		params["active"] = strconv.FormatBool(*r.Active)
	}
	return params
}

func (r *ListConfigReleaseRequest) Body(release string) (io.Reader, error) { return nil, nil }

func (r *ListConfigReleaseRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ListConfigReleaseRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigBatchQueryResponse)
	if !ok {
		return nil, fmt.Errorf("parse list config release response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ListConfigReleaseResponse{
		PageResponse: PageResponse{
			PageNo:     r.pageNo,
			PageSize:   r.pageSize,
			TotalCount: rawResponse.Total.GetValue(),
		},
		ConfigReleases: make([]ConfigRelease, 0),
	}

	for _, rawRelease := range rawResponse.ConfigFileReleases {
		tags := make([]Tag, 0)
		for _, rawTag := range rawRelease.GetTags() {
			tags = append(tags, Tag{
				Key:   rawTag.GetKey().GetValue(),
				Value: rawTag.GetValue().GetValue(),
			})
		}
		betaLabels := make([]Tag, 0)
		for _, label := range rawRelease.GetBetaLabels() {
			value := ""
			if label.GetValue() != nil {
				value = label.GetValue().Value.GetValue()
			}
			betaLabels = append(betaLabels, Tag{
				Key:   label.GetKey(),
				Value: value,
			})
		}
		response.ConfigReleases = append(response.ConfigReleases, ConfigRelease{
			ConfigReleaseInput: ConfigReleaseInput{
				Namespace:  rawRelease.GetNamespace().GetValue(),
				Group:      rawRelease.GetGroup().GetValue(),
				File:       rawRelease.GetFileName().GetValue(),
				Name:       rawRelease.GetName().GetValue(),
				Comment:    func(s string) *string { return &s }(rawRelease.GetReleaseDescription().GetValue()),
				BetaLabels: betaLabels,
			},
			CreateBy:   rawRelease.GetCreateBy().GetValue(),
			CreateTime: rawRelease.GetCreateTime().GetValue(),
			Active:     rawRelease.GetActive().GetValue(),
			Tags:       tags,
			Format:     rawRelease.GetFormat().GetValue(),
			Content:    rawRelease.GetContent().GetValue(),
		})
	}
	return response, nil
}
