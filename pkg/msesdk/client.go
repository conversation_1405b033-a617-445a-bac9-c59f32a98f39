package msesdk

import (
	"io"
	"net/http"

	"github.com/golang/protobuf/jsonpb"
	"github.com/golang/protobuf/proto"
	"github.com/golang/protobuf/ptypes/wrappers"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"

	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
)

type Client struct {
	http.Client
	endpoint string
	token    string
	release  string
}

type ClientDecorator struct {
	Client
	Request
}

func NewClient(endpoint, token, release string) *Client {
	return &Client{
		endpoint: endpoint,
		token:    token,
		release:  release,
	}
}

func NewClientDecorator(client *Client, request Request) *ClientDecorator {
	return &ClientDecorator{
		Client:  *client,
		Request: request,
	}
}

func (c *ClientDecorator) DoRequest(ctx csmContext.CsmContext, response proto.Message) (statusCode int, err error) {
	if err = c.FillAndValidate(c.release); err != nil {
		return
	}
	url := util.GetURL("http", c.endpoint, c.URI(), c.QueryParams())
	body, err := c.Body(c.release)
	if err != nil {
		return
	}
	rawRequest, err := http.NewRequest(c.Method(), url, body)
	if err != nil {
		return
	}
	for k, v := range c.Headers() {
		rawRequest.Header.Set(k, v)
	}
	rawRequest.Header.Set("X-Polaris-Token", c.token)
	rawResponse, err := c.Do(rawRequest)
	if err != nil {
		return
	}
	defer rawResponse.Body.Close()
	statusCode = rawResponse.StatusCode

	responseBody, err := io.ReadAll(rawResponse.Body)
	if err != nil {
		return
	}

	if exportResponse, ok := response.(*apiconfig.ConfigExportResponse); ok {
		contentType := rawResponse.Header.Get("Content-Type")
		if contentType == "application/zip" || http.DetectContentType(responseBody) == "application/zip" {
			exportResponse.Code = &wrappers.UInt32Value{Value: 200000}
			exportResponse.Data = &wrappers.BytesValue{Value: responseBody}
			return
		}
	}

	responseBodyStr := string(responseBody)
	ctx.CsmLogger().Infof("%s %s, response: %s", c.Method(), url, responseBodyStr)
	if err = jsonpb.UnmarshalString(responseBodyStr, response); err != nil {
		return
	}
	return
}
