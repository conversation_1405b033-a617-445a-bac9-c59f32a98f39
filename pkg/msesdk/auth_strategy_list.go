package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strconv"
)

var _ Request = &ListAuthStrategyRequest{}

type ListAuthStrategyRequest struct {
	PageRequest
	MseUserID string `json:"userId"`
}

type ListAuthStrategyResponse struct {
	PageResponse
	AuthStrategies []AuthStrategy `json:"authStrategies"`
}

func (r *ListAuthStrategyRequest) ApiName() string {
	return "ListAuthStrategy"
}

func (r *ListAuthStrategyRequest) FillAndValidate(release string) error {
	return r.PageRequest.FillAndValidate()
}

func (r *ListAuthStrategyRequest) Method() string { return http.MethodGet }

func (r *ListAuthStrategyRequest) URI() string { return "/core/v1/auth/strategies" }

func (r *ListAuthStrategyRequest) QueryParams() map[string]string {
	m := map[string]string{
		"offset":      strconv.FormatInt(r.Offset(), 10),
		"limit":       strconv.FormatInt(r.Limit(), 10),
		"show_detail": "true",
	}
	if r.MseUserID != "" {
		m["principal_id"] = r.MseUserID
		m["principal_type"] = "user"
	}
	return m
}

func (r *ListAuthStrategyRequest) Body(release string) (io.Reader, error) {
	return nil, nil
}

func (r *ListAuthStrategyRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ListAuthStrategyRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchQueryResponse)
	if !ok {
		return nil, fmt.Errorf("parse list auth strategy response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ListAuthStrategyResponse{
		PageResponse: PageResponse{
			PageNo:     r.pageNo,
			PageSize:   r.pageSize,
			TotalCount: rawResponse.GetAmount().GetValue(),
		},
		AuthStrategies: make([]AuthStrategy, 0),
	}

	for _, rawStrategy := range rawResponse.GetAuthStrategies() {
		strategy := AuthStrategy{
			Resource: Resource{
				Namespaces: make([]string, 0),
			},
		}
		strategy.ID = rawStrategy.GetId().GetValue()
		strategy.Action = rawStrategy.GetAction().String()
		for _, namespace := range rawStrategy.GetResources().GetNamespaces() {
			strategy.Resource.Namespaces = append(strategy.Resource.Namespaces, namespace.GetName().GetValue())
		}
		response.AuthStrategies = append(response.AuthStrategies, strategy)
	}
	return response, nil
}
