package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strings"
)

var _ Request = &DeleteNamespaceRequest{}

type DeleteNamespaceRequest struct {
	Namespaces string `json:"namespaces"`
}

func (r *DeleteNamespaceRequest) ApiName() string {
	return "DeleteNamespace"
}

func (r *DeleteNamespaceRequest) FillAndValidate(release string) error {
	if r.Namespaces == "" || len(strings.Split(r.Namespaces, ",")) == 0 {
		return csmErr.NewInvalidParameterInputValueException("namespaces could not be nil")
	}
	return nil
}

func (r *DeleteNamespaceRequest) Method() string { return http.MethodPost }

func (r *DeleteNamespaceRequest) URI() string { return "/naming/v1/namespaces/delete" }

func (r *DeleteNamespaceRequest) QueryParams() map[string]string {
	return nil
}

func (r *DeleteNamespaceRequest) Body(release string) (io.Reader, error) {
	m := make([]map[string]string, 0)
	for _, namespace := range strings.Split(r.Namespaces, ",") {
		m = append(m, map[string]string{
			"name": namespace,
		})
	}
	body, err := json.Marshal(m)
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *DeleteNamespaceRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *DeleteNamespaceRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchWriteResponse)
	if !ok {
		return nil, fmt.Errorf("parse create namespace response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
