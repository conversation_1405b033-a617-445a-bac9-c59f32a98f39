package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &GetConfigFileRequest{}

type GetConfigFileRequest struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	Group     string `json:"group"`
}

type GetConfigFileResponse struct {
	ConfigFile
}

func (r *GetConfigFileRequest) ApiName() string {
	return "GetConfigFile"
}

func (r *GetConfigFileRequest) FillAndValidate(release string) error {
	if r.Name == "" {
		return csmErr.NewInvalidParameterInputValueException("name is required")
	}
	if r.Namespace == "" {
		return csmErr.NewInvalidParameterInputValueException("namespace is required")
	}
	if r.Group == "" {
		return csmErr.NewInvalidParameterInputValueException("group is required")
	}
	return nil
}

func (r *GetConfigFileRequest) Method() string { return http.MethodGet }

func (r *GetConfigFileRequest) URI() string { return "/config/v1/configfiles" }

func (r *GetConfigFileRequest) QueryParams() map[string]string {
	return map[string]string{
		"namespace": r.Namespace,
		"name":      r.Name,
		"group":     r.Group,
	}
}

func (r *GetConfigFileRequest) Body(release string) (io.Reader, error) { return nil, nil }

func (r *GetConfigFileRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *GetConfigFileRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigResponse)
	if !ok {
		return nil, fmt.Errorf("parse list config file response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &GetConfigFileResponse{}
	rawFile := rawResponse.GetConfigFile()
	response.Name = rawFile.GetName().GetValue()
	response.Namespace = rawFile.GetNamespace().GetValue()
	response.Group = rawFile.GetGroup().GetValue()
	response.Status = rawFile.GetStatus().GetValue()
	response.Format = rawFile.GetFormat().GetValue()
	response.Comment = func(s string) *string { return &s }(rawFile.GetComment().GetValue())
	response.Content = func(s string) *string { return &s }(rawFile.GetContent().GetValue())
	tags := make([]Tag, 0)
	for _, rawTag := range rawFile.Tags {
		tags = append(tags, Tag{
			Key:   rawTag.GetKey().GetValue(),
			Value: rawTag.GetValue().GetValue(),
		})
	}
	response.Tags = tags
	response.CreateTime = rawFile.GetCreateTime().GetValue()
	response.UpdateTime = rawFile.GetModifyTime().GetValue()
	response.ReleaseTime = rawFile.GetReleaseTime().GetValue()
	response.CurrentReleaseName = rawFile.GetCurrentReleaseName().GetValue()
	response.BetaStatus = &BetaStatus{
		Active:      rawFile.GetBetaStatus().GetActive().GetValue(),
		UpdateTime:  rawFile.GetBetaStatus().GetModifyTime().GetValue(),
		ReleaseTime: rawFile.GetBetaStatus().GetReleaseTime().GetValue(),
		ReleaseName: rawFile.GetBetaStatus().GetReleaseName().GetValue(),
	}
	betaLabels := make([]Tag, 0)
	for _, label := range rawFile.GetBetaStatus().GetBetaLabels() {
		value := ""
		if label.GetValue() != nil {
			value = label.GetValue().Value.GetValue()
		}
		betaLabels = append(betaLabels, Tag{
			Key:   label.GetKey(),
			Value: value,
		})
	}
	response.BetaStatus.BetaLabels = betaLabels
	return response, nil
}
