package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &UpdateNamespaceRequest{}

type UpdateNamespaceRequest struct {
	Namespaces []NamespaceInput `json:"namespaces"`
}

func (r *UpdateNamespaceRequest) ApiName() string {
	return "UpdateNamespace"
}

func (r *UpdateNamespaceRequest) FillAndValidate(release string) error {
	return nil
}

func (r *UpdateNamespaceRequest) Method() string { return http.MethodPut }

func (r *UpdateNamespaceRequest) URI() string { return "/naming/v1/namespaces" }

func (r *UpdateNamespaceRequest) QueryParams() map[string]string {
	return nil
}

func (r *UpdateNamespaceRequest) Body(release string) (io.Reader, error) {
	body, err := json.Marshal(r.Namespaces)
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *UpdateNamespaceRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *UpdateNamespaceRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchWriteResponse)
	if !ok {
		return nil, fmt.Errorf("parse create namespace response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
