package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &DeleteConfigGroupRequest{}

type DeleteConfigGroupRequest struct {
	ConfigGroupInput
}

func (r *DeleteConfigGroupRequest) ApiName() string {
	return "DeleteConfigGroup"
}

func (r *DeleteConfigGroupRequest) FillAndValidate(release string) error {
	return r.ConfigGroupInput.FillAndValidate()
}

func (r *DeleteConfigGroupRequest) Method() string { return http.MethodDelete }

func (r *DeleteConfigGroupRequest) URI() string { return "/config/v1/configfilegroups" }

func (r *DeleteConfigGroupRequest) QueryParams() map[string]string {
	return map[string]string{
		"group":     r.Name,
		"namespace": r.Namespace,
	}
}

func (r *DeleteConfigGroupRequest) Body(release string) (io.Reader, error) {
	return nil, nil
}

func (r *DeleteConfigGroupRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *DeleteConfigGroupRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigResponse)
	if !ok {
		return nil, fmt.Errorf("parse delete config group response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
