package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &DeleteConfigReleaseRequest{}

type DeleteConfigReleaseRequest struct {
	ConfigReleaseInput
}

func (r *DeleteConfigReleaseRequest) ApiName() string {
	return "DeleteConfigRelease"
}

func (r *DeleteConfigReleaseRequest) FillAndValidate(release string) error {
	if r.Name == "" && !r.IsBeta {
		return csmErr.NewInvalidParameterInputValueException("name is required")
	}
	if r.Is<PERSON>eta {
		r.Name = ""
	}
	return r.ConfigReleaseInput.FillAndValidate()
}

func (r *DeleteConfigReleaseRequest) Method() string { return http.MethodPost }

func (r *DeleteConfigReleaseRequest) URI() string {
	if r.IsBeta {
		return "/config/v1/configfiles/releases/stopbeta"
	}
	return "/config/v1/configfiles/releases/delete"
}

func (r *DeleteConfigReleaseRequest) QueryParams() map[string]string { return nil }

func (r *DeleteConfigReleaseRequest) Body(release string) (io.Reader, error) {
	body, err := json.Marshal([]map[string]interface{}{{
		"name":      r.Name,
		"namespace": r.Namespace,
		"group":     r.Group,
		"file_name": r.File,
	}})
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *DeleteConfigReleaseRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *DeleteConfigReleaseRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigBatchWriteResponse)
	if !ok {
		return nil, fmt.Errorf("parse delete config release response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
