package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &DeleteServiceInstanceRequest{}

type DeleteServiceInstanceRequest struct {
	ServiceInstanceID string `json:"-"`
}

func (r *DeleteServiceInstanceRequest) ApiName() string {
	return "DeleteServiceInstance"
}

func (r *DeleteServiceInstanceRequest) FillAndValidate(release string) error {
	if r.ServiceInstanceID == "" {
		return csmErr.NewInvalidParameterInputValueException("serviceInstanceId is empty")
	}
	return nil
}

func (r *DeleteServiceInstanceRequest) Method() string { return http.MethodPost }

func (r *DeleteServiceInstanceRequest) URI() string { return "/naming/v1/instances/delete" }

func (r *DeleteServiceInstanceRequest) QueryParams() map[string]string {
	return nil
}

func (r *DeleteServiceInstanceRequest) Body(release string) (io.Reader, error) {
	body, err := json.Marshal([]map[string]string{{"id": r.ServiceInstanceID}})
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *DeleteServiceInstanceRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *DeleteServiceInstanceRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchWriteResponse)
	if !ok {
		return nil, fmt.Errorf("parse create service instance response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
