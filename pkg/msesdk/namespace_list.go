package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strconv"
)

var _ Request = &ListNamespaceRequest{}

type ListNamespaceRequest struct {
	PageRequest
	Name      string `json:"name"`
	AccountID string `json:"-"`
}

type ListNamespaceResponse struct {
	PageResponse
	Result []Namespace `json:"result"`
}

func (r *ListNamespaceRequest) ApiName() string {
	return "ListNamespace"
}

func (r *ListNamespaceRequest) FillAndValidate(release string) error {
	return r.PageRequest.FillAndValidate()
}

func (r *ListNamespaceRequest) Method() string { return http.MethodGet }

func (r *ListNamespaceRequest) URI() string { return "/naming/v1/namespaces" }

func (r *ListNamespaceRequest) QueryParams() map[string]string {
	m := map[string]string{
		"offset": strconv.FormatInt(r.Offset(), 10),
		"limit":  strconv.FormatInt(r.Limit(), 10),
		"owner":  r.AccountID,
	}
	if r.Name != "" {
		m["name"] = r.Name
	}
	return m
}

func (r *ListNamespaceRequest) Body(release string) (io.Reader, error) { return nil, nil }

func (r *ListNamespaceRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ListNamespaceRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchQueryResponse)
	if !ok {
		return nil, fmt.Errorf("parse list namespace response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ListNamespaceResponse{
		PageResponse: PageResponse{
			PageNo:     r.pageNo,
			PageSize:   r.pageSize,
			TotalCount: rawResponse.GetAmount().GetValue(),
		},
		Result: make([]Namespace, 0),
	}

	for _, rawNamespace := range rawResponse.Namespaces {
		namespace := Namespace{
			NamespaceInput: NamespaceInput{
				Name:    rawNamespace.GetName().GetValue(),
				Comment: func(s string) *string { return &s }(rawNamespace.GetComment().GetValue()),
			},
			CreateTime:           rawNamespace.GetCtime().GetValue(),
			UpdateTime:           rawNamespace.GetMtime().GetValue(),
			ServiceCount:         rawNamespace.GetTotalServiceCount().GetValue(),
			InstanceCount:        rawNamespace.GetTotalInstanceCount().GetValue(),
			HealthInstanceCount:  rawNamespace.GetTotalHealthInstanceCount().GetValue(),
			ConfigFileGroupCount: rawNamespace.GetTotalConfigGroupCount().GetValue(),
			Editable:             rawNamespace.GetEditable().GetValue(),
		}
		response.Result = append(response.Result, namespace)
	}
	return response, nil
}
