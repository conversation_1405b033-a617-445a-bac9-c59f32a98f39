package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &BatchOperateServiceInstanceRequest{}

type BatchOperateServiceInstanceRequest struct {
	Action       string                        `json:"-"`
	BatchOperate []ServiceInstanceBatchOperate `json:"serviceInstanceList"`
}

func (r *BatchOperateServiceInstanceRequest) ApiName() string {
	return "BatchOperateServiceInstance"
}

func (r *BatchOperateServiceInstanceRequest) FillAndValidate(release string) error {
	if r.Action == "" {
		return csmErr.NewInvalidParameterInputValueException("action is empty")
	}
	if r.Action != "BatchDelete" && r.Action != "BatchIsolate" {
		return csmErr.NewInvalidParameterInputValueException(fmt.Sprintf("unknown action: %v", r.Action))
	}
	return nil
}

func (r *BatchOperateServiceInstanceRequest) Method() string {
	if r.Action == "BatchDelete" {
		return http.MethodPost
	} else if r.Action == "BatchIsolate" {
		return http.MethodPut
	}
	return ""
}

func (r *BatchOperateServiceInstanceRequest) URI() string {
	if r.Action == "BatchDelete" {
		return "/naming/v1/instances/delete/host"
	} else if r.Action == "BatchIsolate" {
		return "/naming/v1/instances/isolate/host"
	}
	return ""
}

func (r *BatchOperateServiceInstanceRequest) QueryParams() map[string]string {
	return nil
}

func (r *BatchOperateServiceInstanceRequest) Body(release string) (io.Reader, error) {
	body, err := json.Marshal(r.BatchOperate)
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *BatchOperateServiceInstanceRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *BatchOperateServiceInstanceRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchWriteResponse)
	if !ok {
		return nil, fmt.Errorf("parse create service instance response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
