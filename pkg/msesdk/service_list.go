package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strconv"
)

var _ Request = &ListServiceRequest{}

type ListServiceRequest struct {
	PageRequest
	Name      string
	Namespace string
	AccountID string `json:"-"`
}

type ListServiceResponse struct {
	PageResponse
	Result []Service `json:"result"`
}

func (r *ListServiceRequest) ApiName() string {
	return "ListService"
}

func (r *ListServiceRequest) FillAndValidate(release string) error {
	return r.PageRequest.FillAndValidate()
}

func (r *ListServiceRequest) Method() string { return http.MethodGet }

func (r *ListServiceRequest) URI() string { return "/naming/v1/services" }

func (r *ListServiceRequest) QueryParams() map[string]string {
	m := map[string]string{
		"offset": strconv.FormatInt(r.Offset(), 10),
		"limit":  strconv.FormatInt(r.Limit(), 10),
		"owner":  r.AccountID,
	}
	if r.Name != "" {
		m["name"] = r.Name
	}
	if r.Namespace != "" {
		m["namespace"] = r.Namespace
	}
	return m
}

func (r *ListServiceRequest) Body(release string) (io.Reader, error) {
	return nil, nil
}

func (r *ListServiceRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ListServiceRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchQueryResponse)
	if !ok {
		return nil, fmt.Errorf("parse list service response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ListServiceResponse{
		PageResponse: PageResponse{
			PageNo:     r.pageNo,
			PageSize:   r.pageSize,
			TotalCount: rawResponse.GetAmount().GetValue(),
		},
		Result: make([]Service, 0),
	}

	for _, rawService := range rawResponse.Services {
		service := Service{
			ID:          rawService.GetId().GetValue(),
			Name:        rawService.GetName().GetValue(),
			Namespace:   rawService.GetNamespace().GetValue(),
			TotalCount:  rawService.GetTotalInstanceCount().GetValue(),
			HealthCount: rawService.GetHealthyInstanceCount().GetValue(),
			CreateTime:  rawService.GetCtime().GetValue(),
			UpdateTime:  rawService.GetMtime().GetValue(),
			Editable:    rawService.GetEditable().GetValue(),
		}
		response.Result = append(response.Result, service)
	}
	return response, nil
}
