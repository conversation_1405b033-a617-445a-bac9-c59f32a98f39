package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/common"
	"io"
	"net/http"
)

var _ Request = &ResetAdminPasswordRequest{}

type ResetAdminPasswordRequest struct {
	Password  string `json:"password"`
	AccountID string `json:"-"`
}

func (r *ResetAdminPasswordRequest) ApiName() string {
	return "ResetAdminPasswordRequest"
}

func (r *ResetAdminPasswordRequest) FillAndValidate(release string) error {
	if ge, err := common.IsReleaseAfterOrEqual(release, "1.3.0"); err != nil {
		return fmt.Errorf("invalid release %v", release)
	} else if !ge {
		return csmErr.NewOperationNotSupportException("Registry version must be greater than or equal to 1.3.0.")
	}
	if len(r.Password) < 6 || len(r.Password) > 17 {
		return csmErr.NewInvalidParameterInputValueException("invalid password length")
	}
	return nil
}

func (r *ResetAdminPasswordRequest) Method() string { return http.MethodPut }

func (r *ResetAdminPasswordRequest) URI() string { return "/core/v1/user/password" }

func (r *ResetAdminPasswordRequest) QueryParams() map[string]string { return nil }

func (r *ResetAdminPasswordRequest) Body(release string) (io.Reader, error) {
	body, err := json.Marshal(map[string]string{
		"id":           r.AccountID,
		"new_password": r.Password,
	})
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *ResetAdminPasswordRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ResetAdminPasswordRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.Response)
	if !ok {
		return nil, fmt.Errorf("parse reset admin password response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
