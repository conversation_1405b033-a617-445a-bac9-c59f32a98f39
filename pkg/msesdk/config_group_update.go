package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &UpdateConfigGroupRequest{}

type UpdateConfigGroupRequest struct {
	ConfigGroupInput
}

func (r *UpdateConfigGroupRequest) ApiName() string {
	return "UpdateConfigGroup"
}

func (r *UpdateConfigGroupRequest) FillAndValidate(release string) error {
	return r.ConfigGroupInput.WriteFillAndValidate()
}

func (r *UpdateConfigGroupRequest) Method() string { return http.MethodPut }

func (r *UpdateConfigGroupRequest) URI() string { return "/config/v1/configfilegroups" }

func (r *UpdateConfigGroupRequest) QueryParams() map[string]string { return nil }

func (r *UpdateConfigGroupRequest) Body(release string) (io.Reader, error) {
	body, err := json.Marshal(map[string]interface{}{
		"name":      r.Name,
		"namespace": r.Namespace,
		"comment":   r.Comment,
	})
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *UpdateConfigGroupRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *UpdateConfigGroupRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigResponse)
	if !ok {
		return nil, fmt.Errorf("parse update config group response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
