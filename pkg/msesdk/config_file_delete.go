package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &DeleteConfigFileRequest{}

type DeleteConfigFileRequest struct {
	ConfigFiles []ConfigFileInput `json:"configFiles"`
}

func (r *DeleteConfigFileRequest) ApiName() string {
	return "DeleteConfigFile"
}

func (r *DeleteConfigFileRequest) FillAndValidate(release string) error {
	if len(r.ConfigFiles) == 0 {
		return csmErr.NewInvalidParameterInputValueException("configFiles is required")
	}
	for _, file := range r.ConfigFiles {
		if file.Namespace == "" {
			return csmErr.NewInvalidParameterInputValueException("namespace is required")
		}
		if file.Name == "" {
			return csmErr.NewInvalidParameterInputValueException("name is required")
		}
		if file.Group == "" {
			return csmErr.NewInvalidParameterInputValueException("group is required")
		}
	}
	return nil
}

func (r *DeleteConfigFileRequest) Method() string { return http.MethodPost }

func (r *DeleteConfigFileRequest) URI() string { return "/config/v1/configfiles/batchdelete" }

func (r *DeleteConfigFileRequest) QueryParams() map[string]string { return nil }

func (r *DeleteConfigFileRequest) Body(release string) (io.Reader, error) {
	body, err := json.Marshal(r.ConfigFiles)
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *DeleteConfigFileRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *DeleteConfigFileRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigResponse)
	if !ok {
		return nil, fmt.Errorf("parse delete config file response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
