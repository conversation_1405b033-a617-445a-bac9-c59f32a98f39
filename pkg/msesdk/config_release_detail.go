package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &GetConfigReleaseRequest{}

type GetConfigReleaseRequest struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	Group     string `json:"group"`
	File      string `json:"file"`
}

type GetConfigReleaseResponse struct {
	ConfigRelease
}

func (r *GetConfigReleaseRequest) ApiName() string {
	return "GetConfigRelease"
}

func (r *GetConfigReleaseRequest) FillAndValidate(release string) error {
	if r.Name == "" {
		return csmErr.NewInvalidParameterInputValueException("name is required")
	}
	if r.Namespace == "" {
		return csmErr.NewInvalidParameterInputValueException("namespace is required")
	}
	if r.Group == "" {
		return csmErr.NewInvalidParameterInputValueException("group is required")
	}
	if r.File == "" {
		return csmErr.NewInvalidParameterInputValueException("file is required")
	}
	return nil
}

func (r *GetConfigReleaseRequest) Method() string { return http.MethodGet }

func (r *GetConfigReleaseRequest) URI() string { return "/config/v1/configfiles/release" }

func (r *GetConfigReleaseRequest) QueryParams() map[string]string {
	params := map[string]string{
		"namespace":    r.Namespace,
		"group":        r.Group,
		"file_name":    r.File,
		"release_name": r.Name,
	}
	return params
}

func (r *GetConfigReleaseRequest) Body(release string) (io.Reader, error) { return nil, nil }

func (r *GetConfigReleaseRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *GetConfigReleaseRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigResponse)
	if !ok {
		return nil, fmt.Errorf("parse list config release response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &GetConfigReleaseResponse{}
	rawRelease := rawResponse.GetConfigFileRelease()

	tags := make([]Tag, 0)
	for _, rawTag := range rawRelease.GetTags() {
		tags = append(tags, Tag{
			Key:   rawTag.GetKey().GetValue(),
			Value: rawTag.GetValue().GetValue(),
		})
	}
	response.Tags = tags
	response.Namespace = rawRelease.GetNamespace().GetValue()
	response.Group = rawRelease.GetGroup().GetValue()
	response.File = rawRelease.GetFileName().GetValue()
	response.Name = rawRelease.GetName().GetValue()
	response.Comment = func(s string) *string { return &s }(rawRelease.GetReleaseDescription().GetValue())
	response.CreateBy = rawRelease.GetCreateBy().GetValue()
	response.CreateTime = rawRelease.GetCreateTime().GetValue()
	response.Active = rawRelease.GetActive().GetValue()
	response.Format = rawRelease.GetFormat().GetValue()
	response.Content = rawRelease.GetContent().GetValue()

	betaLabels := make([]Tag, 0)
	for _, label := range rawRelease.GetBetaLabels() {
		value := ""
		if label.GetValue() != nil {
			value = label.GetValue().Value.GetValue()
		}
		betaLabels = append(betaLabels, Tag{
			Key:   label.GetKey(),
			Value: value,
		})
	}
	response.BetaLabels = betaLabels
	return response, nil
}
