package msesdk

import (
	"archive/zip"
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"strings"

	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"

	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
)

var _ Request = &ImportConfigFileRequest{}

type ImportConfigFileRequest struct {
	Namespace        string                `json:"namespace"`
	Zip              *multipart.FileHeader `json:"zip"`
	ConflictHandling string                `json:"conflictHandling"`
	Publish          string                `json:"publish"`

	contentType string
}

func (r *ImportConfigFileRequest) ApiName() string {
	return "ImportConfigFile"
}

type ImportConfigFileResponse struct {
	CreateConfigFiles    []ConfigFile `json:"createConfigFiles"`
	SkipConfigFiles      []ConfigFile `json:"skipConfigFiles"`
	OverwriteConfigFiles []ConfigFile `json:"overwriteConfigFiles"`
}

func (r *ImportConfigFileRequest) FillAndValidate(release string) error {
	if r.Namespace == "" {
		return csmErr.NewInvalidParameterInputValueException("namespace is required")
	}
	if r.Zip == nil {
		return csmErr.NewInvalidParameterInputValueException("zip is required")
	}
	if r.ConflictHandling != "skip" && r.ConflictHandling != "overwrite" {
		return csmErr.NewInvalidParameterInputValueException("conflictHandling should skip/overwrite")
	}
	return nil
}

func (r *ImportConfigFileRequest) Method() string { return http.MethodPost }

func (r *ImportConfigFileRequest) URI() string { return "/config/v1/configfiles/import" }

func (r *ImportConfigFileRequest) QueryParams() map[string]string { return nil }

func (r *ImportConfigFileRequest) Body(release string) (io.Reader, error) {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	_ = writer.WriteField("namespace", r.Namespace)
	_ = writer.WriteField("conflict_handling", r.ConflictHandling)
	_ = writer.WriteField("publish", r.Publish)

	// 1. 读取原始 zip 文件内容
	src, err := r.Zip.Open()
	if err != nil {
		return nil, err
	}
	defer src.Close()
	var originalZipBuf bytes.Buffer
	if _, err = io.Copy(&originalZipBuf, src); err != nil {
		return nil, err
	}

	// 2. 解压原始 zip 内容
	originalReader, err := zip.NewReader(bytes.NewReader(originalZipBuf.Bytes()), int64(originalZipBuf.Len()))
	if err != nil {
		return nil, err
	}

	// 3. 构造一个新的 zip，仅包含子目录文件
	var filteredZipBuf bytes.Buffer
	filteredWriter := zip.NewWriter(&filteredZipBuf)

	for _, file := range originalReader.File {
		if strings.Contains(file.Name, "/") {
			// 是子目录下的文件，保留
			srcFile, err := file.Open()
			if err != nil {
				return nil, err
			}
			defer srcFile.Close()

			w, err := filteredWriter.Create(file.Name)
			if err != nil {
				return nil, err
			}
			if _, err := io.Copy(w, srcFile); err != nil {
				return nil, err
			}
		}
	}
	if err := filteredWriter.Close(); err != nil {
		return nil, err
	}

	// 4. 构建 multipart/form-data 请求体
	part, err := writer.CreateFormFile("config", r.Zip.Filename)
	if err != nil {
		return nil, err
	}
	if _, err = part.Write(filteredZipBuf.Bytes()); err != nil {
		return nil, err
	}
	if err = writer.Close(); err != nil {
		return nil, err
	}
	r.contentType = writer.FormDataContentType()
	return body, nil
}

func (r *ImportConfigFileRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": r.contentType,
	}
}

func (r *ImportConfigFileRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigImportResponse)
	if !ok {
		return nil, fmt.Errorf("parse import config file response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ImportConfigFileResponse{}

	parse := func(rawFiles []*apiconfig.ConfigFile) []ConfigFile {
		files := make([]ConfigFile, 0)
		for _, rawFile := range rawFiles {
			tags := make([]Tag, 0)
			for _, rawTag := range rawFile.Tags {
				tags = append(tags, Tag{
					Key:   rawTag.GetKey().GetValue(),
					Value: rawTag.GetValue().GetValue(),
				})
			}
			file := ConfigFile{
				ConfigFileInput: ConfigFileInput{
					Name:      rawFile.GetName().GetValue(),
					Namespace: rawFile.GetNamespace().GetValue(),
					Group:     rawFile.GetGroup().GetValue(),
					Format:    rawFile.GetFormat().GetValue(),
					Comment:   func(s string) *string { return &s }(rawFile.GetComment().GetValue()),
					Content:   func(s string) *string { return &s }(rawFile.GetContent().GetValue()),
					Tags:      tags,
				},
				Status:             rawFile.GetStatus().GetValue(),
				CreateTime:         rawFile.GetCreateTime().GetValue(),
				UpdateTime:         rawFile.GetModifyTime().GetValue(),
				ReleaseTime:        rawFile.GetReleaseTime().GetValue(),
				CurrentReleaseName: rawFile.GetCurrentReleaseName().GetValue(),
			}
			files = append(files, file)
		}
		return files
	}

	response.CreateConfigFiles = parse(rawResponse.CreateConfigFiles)
	response.SkipConfigFiles = parse(rawResponse.SkipConfigFiles)
	response.OverwriteConfigFiles = parse(rawResponse.OverwriteConfigFiles)
	return response, nil
}
