package msesdk

import (
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strconv"
)

var _ Request = &ListConfigReleaseRecordRequest{}

type ListConfigReleaseRecordRequest struct {
	PageRequest
	Namespace string `json:"namespace"`
	Group     string `json:"group"`
	File      string `json:"file"`
}

type ListConfigReleaseRecordResponse struct {
	PageResponse
	ConfigReleaseRecords []ConfigReleaseRecord `json:"configReleaseRecords"`
}

func (r *ListConfigReleaseRecordRequest) ApiName() string {
	return "ListConfigReleaseRecord"
}

func (r *ListConfigReleaseRecordRequest) FillAndValidate(release string) error {
	return r.PageRequest.FillAndValidate()
}

func (r *ListConfigReleaseRecordRequest) Method() string { return http.MethodGet }

func (r *ListConfigReleaseRecordRequest) URI() string { return "/config/v1/configfiles/releasehistory" }

func (r *ListConfigReleaseRecordRequest) QueryParams() map[string]string {
	return map[string]string{
		"namespace": r.Namespace,
		"group":     r.Group,
		"name":      r.File,
		"offset":    strconv.FormatInt(r.Offset(), 10),
		"limit":     strconv.FormatInt(r.Limit(), 10),
	}
}

func (r *ListConfigReleaseRecordRequest) Body(release string) (io.Reader, error) { return nil, nil }

func (r *ListConfigReleaseRecordRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ListConfigReleaseRecordRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigBatchQueryResponse)
	if !ok {
		return nil, fmt.Errorf("parse list config release record response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ListConfigReleaseRecordResponse{
		PageResponse: PageResponse{
			PageNo:     r.pageNo,
			PageSize:   r.pageSize,
			TotalCount: rawResponse.Total.GetValue(),
		},
		ConfigReleaseRecords: make([]ConfigReleaseRecord, 0),
	}

	for _, rawRecord := range rawResponse.ConfigFileReleaseHistories {
		labels := make([]Tag, 0)
		if len(rawRecord.GetBetaLabels().GetValue()) > 0 {
			betaLabels := make([]ClientLabel, 0)
			if jsonErr := json.Unmarshal([]byte(rawRecord.GetBetaLabels().GetValue()), &betaLabels); jsonErr == nil {
				for _, betaLabel := range betaLabels {
					labels = append(labels, Tag{
						Key:   betaLabel.Key,
						Value: betaLabel.Value.Value,
					})
				}
			}
		}
		response.ConfigReleaseRecords = append(response.ConfigReleaseRecords, ConfigReleaseRecord{
			Namespace:  rawRecord.GetNamespace().GetValue(),
			Group:      rawRecord.GetGroup().GetValue(),
			File:       rawRecord.GetFileName().GetValue(),
			Release:    rawRecord.GetName().GetValue(),
			Type:       rawRecord.GetType().GetValue(),
			Status:     rawRecord.GetStatus().GetValue(),
			CreateBy:   rawRecord.GetCreateBy().GetValue(),
			CreateTime: rawRecord.GetCreateTime().GetValue(),
			Content:    rawRecord.GetContent().GetValue(),
			Format:     rawRecord.GetFormat().GetValue(),
			Comment:    rawRecord.GetReleaseDescription().GetValue(),
			BetaLabels: labels,
		})
	}
	return response, nil
}
