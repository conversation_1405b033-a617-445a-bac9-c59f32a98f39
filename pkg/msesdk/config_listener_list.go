package msesdk

import (
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apiconfig "github.com/polarismesh/specification/source/go/api/v1/config_manage"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
	"strconv"
	"strings"
)

var _ Request = &ListConfigListenerRequest{}

type ListConfigListenerRequest struct {
	PageRequest
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	Group     string `json:"group"`
}

type ListConfigListenerResponse struct {
	PageResponse
	ConfigListeners []ConfigListener `json:"configListeners"`
}

func (r *ListConfigListenerRequest) ApiName() string {
	return "ListConfigListener"
}

func (r *ListConfigListenerRequest) FillAndValidate(release string) error {
	return r.PageRequest.FillAndValidate()
}

func (r *ListConfigListenerRequest) Method() string { return http.MethodGet }

func (r *ListConfigListenerRequest) URI() string { return "/config/v1/watchers" }

func (r *ListConfigListenerRequest) QueryParams() map[string]string {
	return map[string]string{
		"namespace": r.Namespace,
		"name":      r.Name,
		"group":     r.Group,
		"offset":    strconv.FormatInt(r.Offset(), 10),
		"limit":     strconv.FormatInt(r.Limit(), 10),
	}
}

func (r *ListConfigListenerRequest) Body(release string) (io.Reader, error) { return nil, nil }

func (r *ListConfigListenerRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *ListConfigListenerRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiconfig.ConfigBatchQueryResponse)
	if !ok {
		return nil, fmt.Errorf("parse list config group response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	response := &ListConfigListenerResponse{
		PageResponse: PageResponse{
			PageNo:     r.pageNo,
			PageSize:   r.pageSize,
			TotalCount: rawResponse.Total.GetValue(),
		},
		ConfigListeners: make([]ConfigListener, 0),
	}

	for _, rawWatcher := range rawResponse.ConfigFileWatchers {
		labels := make([]Tag, 0)
		for _, clientLabel := range rawWatcher.GetClientLabels() {
			sp := strings.Split(clientLabel.GetValue(), ":")
			if len(sp) != 2 {
				continue
			}
			labels = append(labels, Tag{
				Key:   sp[0],
				Value: sp[1],
			})
		}
		listener := ConfigListener{
			IP:          rawWatcher.GetClientIp().GetValue(),
			Labels:      labels,
			ReleaseName: rawWatcher.GetReleaseName().GetValue(),
			MD5:         rawWatcher.GetMd5().GetValue(),
			ReleaseType: rawWatcher.GetReleaseType().GetValue(),
		}
		response.ConfigListeners = append(response.ConfigListeners, listener)
	}
	return response, nil
}
