package msesdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"github.com/labstack/echo/v4"
	apimodel "github.com/polarismesh/specification/source/go/api/v1/model"
	apiservice "github.com/polarismesh/specification/source/go/api/v1/service_manage"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"io"
	"net/http"
)

var _ Request = &CreateServiceInstanceRequest{}

type CreateServiceInstanceRequest struct {
	ServiceInstanceInput
}

func (r *CreateServiceInstanceRequest) ApiName() string {
	return "CreateServiceInstance"
}

func (r *CreateServiceInstanceRequest) FillAndValidate(release string) error {
	return nil
}

func (r *CreateServiceInstanceRequest) Method() string { return http.MethodPost }

func (r *CreateServiceInstanceRequest) URI() string { return "/naming/v1/instances" }

func (r *CreateServiceInstanceRequest) QueryParams() map[string]string {
	return nil
}

func (r *CreateServiceInstanceRequest) Body(release string) (io.Reader, error) {
	serviceInstance := ServiceInstance{
		Host:              r.Host,
		Port:              r.Port,
		Metadata:          r.Metadata,
		Service:           r.ServiceName,
		Namespace:         r.Namespace,
		EnableHealthCheck: r.HealthCheckEnable,
		Isolate:           r.IsolateEnable,
	}
	if serviceInstance.EnableHealthCheck {
		serviceInstance.HealthCheck = &HealthCheck{
			Type: "HEARTBEAT",
			Heartbeat: Heartbeat{
				Ttl: r.TTL,
			},
		}
	}
	if len(serviceInstance.Metadata) > 0 {
		if v, ok := serviceInstance.Metadata["version"]; ok && v != "" {
			serviceInstance.Version = v
		}
		if v, ok := serviceInstance.Metadata["protocol"]; ok && v != "" {
			serviceInstance.Protocol = v
		}
		if v, ok := serviceInstance.Metadata["region"]; ok && v != "" {
			serviceInstance.Location.Region = v
		}
		if v, ok := serviceInstance.Metadata["zone"]; ok && v != "" {
			serviceInstance.Location.Zone = v
		}
		if v, ok := serviceInstance.Metadata["campus"]; ok && v != "" {
			serviceInstance.Location.Campus = v
		}
	}
	body, err := json.Marshal([]ServiceInstance{serviceInstance})
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(body), nil
}

func (r *CreateServiceInstanceRequest) Headers() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func (r *CreateServiceInstanceRequest) ParseResponse(i proto.Message) (Response, error) {
	if i == nil {
		return nil, nil
	}

	rawResponse, ok := i.(*apiservice.BatchWriteResponse)
	if !ok {
		return nil, fmt.Errorf("parse create service instance response failed")
	}

	err := &echo.HTTPError{}
	if err.Code = int(rawResponse.GetCode().GetValue() / 1000); err.Code != http.StatusOK {
		err.Message = csmErr.Error{
			Code:    csmErr.ErrorType(apimodel.Code_name[int32(rawResponse.GetCode().GetValue())]),
			Message: rawResponse.GetInfo().GetValue(),
		}
		return nil, err
	}

	return true, nil
}
