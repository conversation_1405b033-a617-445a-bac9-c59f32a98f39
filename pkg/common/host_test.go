package common

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_getRegionFromHost(t *testing.T) {
	region := GetRegionFromHost("ccr-xxxx-xxx.cnc.gz.baidubce.com")
	assert.Equal(t, "gz", region)

	region = GetRegionFromHost("ccr-xxxx-xxx-cnc.gz.baidubce.com")
	assert.Equal(t, "gz", region)

	region = GetRegionFromHost("ccr-xxx-inter-sync-gz")
	assert.Equal(t, "gz", region)

	region = GetRegionFromHost("ccr-xxxx.cnc.gz")
	assert.Equal(t, "", region)

	region = GetRegionFromHost("ccr-xxx-xxx")
	assert.Equal(t, "", region)
}
