package common

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_IDGenerator(t *testing.T) {
	id := IDGenerator.Next("gztest")
	assert.True(t, strings.HasPrefix(id, "1"))

	id = IDGenerator.Next("gz")
	assert.True(t, strings.HasPrefix(id, "3"))

	id = IDGenerator.Next("bj")
	assert.True(t, strings.HasPrefix(id, "2"))

	id = IDGenerator.Next("bd")
	assert.True(t, strings.HasPrefix(id, "4"))

	id = IDGenerator.Next("su")
	assert.True(t, strings.HasPrefix(id, "5"))

	id = IDGenerator.Next("fwh")
	assert.True(t, strings.HasPrefix(id, "6"))

	id = IDGenerator.Next("hkg")
	assert.True(t, strings.HasPrefix(id, "7"))
}

func TestGetRegionFromInstanceID(t *testing.T) {
	type args struct {
		instanceID string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "gztest",
			args: args{
				instanceID: "ccr-1xxxxxxx",
			},
			want: RegionGZTEST,
		},
		{
			name: "bj",
			args: args{
				instanceID: "ccr-2xxxxxxx",
			},
			want: RegionBJ,
		},
		{
			name: "gz",
			args: args{
				instanceID: "ccr-3xxxxxxx",
			},
			want: RegionGZ,
		},
		{
			name: "bd",
			args: args{
				instanceID: "ccr-4xxxxxxx",
			},
			want: RegionBD,
		},
		{
			name: "su",
			args: args{
				instanceID: "ccr-5xxxxxxx",
			},
			want: RegionSU,
		},
		{
			name: "fwh",
			args: args{
				instanceID: "ccr-6xxxxxxx",
			},
			want: RegionFWH,
		},
		{
			name: "hkg",
			args: args{
				instanceID: "ccr-7xxxxxxx",
			},
			want: RegionHKG,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, GetRegionFromInstanceID(tt.args.instanceID), "GetRegionFromInstanceID(%v)", tt.args.instanceID)
		})
	}
}

func TestGetRealRegion(t *testing.T) {
	type args struct {
		region string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "gztest",
			args: args{
				region: RegionGZTEST,
			},
			want: RegionGZ,
		},
		{
			name: "gz",
			args: args{
				region: RegionGZ,
			},
			want: RegionGZ,
		},
		{
			name: "bj",
			args: args{
				region: RegionBJ,
			},
			want: RegionBJ,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, GetRealRegion(tt.args.region), "GetRealRegion(%v)", tt.args.region)
		})
	}
}
