package common

import "strings"

// GetRegionFromHost 根据域名来获取区域
// ccr-xxxx-xxx.cnc.gz.baidubce.com
// ccr-xxx-inter-sync-gz
func GetRegionFromHost(host string) string {
	parts := strings.Split(host, ".")
	if len(parts) == 5 {
		return parts[2]
	}
	if len(parts) == 4 {
		return parts[1]
	}

	if len(parts) == 1 {
		parts = strings.Split(host, "-")
		if len(parts) == 5 {
			return parts[4]
		}
	}

	return ""
}
