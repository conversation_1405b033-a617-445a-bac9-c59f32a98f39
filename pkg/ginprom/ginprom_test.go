package ginprom

import (
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	dto "github.com/prometheus/client_model/go"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
)

func Test_PromMiddleware(t *testing.T) {
	getOperationFunc := func(*gin.Context) string {
		return "test"
	}

	promFunc := PromMiddleware(getOperationFunc)

	ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

	promFunc(ctx)

	gather := prometheus.DefaultGatherer

	result, err := gather.Gather()
	assert.NoError(t, err)

	requestNum := 0.0
	sampleCount := 0
	for _, metric := range result {
		if *metric.Name == "ccr_http_request_count" && *metric.Type == dto.MetricType_COUNTER {
			for _, v := range metric.Metric {
				if v.Counter != nil {
					requestNum = v.Counter.GetValue()
				}
			}
		}

		if *metric.Name == "ccr_http_request_duration_seconds" && *metric.Type == dto.MetricType_HISTOGRAM {
			for _, v := range metric.Metric {
				sampleCount = int(v.Histogram.GetSampleCount())
			}
		}
	}

	assert.Equal(t, 1.0, requestNum)
	assert.Equal(t, 1, sampleCount)
}
