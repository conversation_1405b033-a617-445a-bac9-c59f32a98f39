# init project path
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output

# init command params
GO        := $(GO_1_18_BIN)/go
GOROOT    := $(GO_1_18_HOME)
GOPATH    := $(shell $(GO) env GOPATH)
GOMOD     := $(GO) mod
GOBUILD   := $(GO) build
GOTEST    := $(GO) test -gcflags="-N -l"
GOPKGS    := $$($(GO) list ./...| grep -vE "vendor")
GOVERSION := $$($(GO) version | awk -F\go '{print $$3}' | awk '{print $$1}')

GITCOMMITLOG := $(shell git log --pretty='format:%h' -n 1)

DATE := $(shell date +%Y%m%d%H%M)

GOSWAGGER_VERSION := v0.27.0
MOCKERY_VERSION = v2.7.5

# test cover files
COVPROF := $(HOMEDIR)/covprof.out  # coverage profile
COVFUNC := $(HOMEDIR)/covfunc.txt  # coverage profile information for each function
COVHTML := $(HOMEDIR)/covhtml.html # HTML representation of coverage profile

CCR_COMPONENTS := ccr-auth ccr-controller ccr-registryctl ccr-service harbor-addon ccr-iregistry acceleration-service

CCR_NAMESPACE := ccr-image

# make, make all
all: prepare compile package

# set proxy env
set-env:
	$(GO) env -w GO111MODULE=on
	$(GO) env -w GONOPROXY=\*\*.baidu.com\*\*
	$(GO) env -w GOPROXY=https://goproxy.baidu-int.com
	$(GO) env -w GONOSUMDB=\*

#make prepare, download dependencies
prepare: gomod

gomod: set-env
	$(GOMOD) download

#make compile
compile: build

build:
	for component in $(CCR_COMPONENTS) ; do \
        export CCR_COMPONENT=$$component && make build-ccr-component ; \
    done

# make test, test your code
test: prepare test-case
test-case:
	$(GOTEST) -v -cover $(GOPKGS)

# make package
package: package-bin
package-bin:
	mkdir -p $(OUTDIR)
	cp bin/harbor_core bin/harbor_jobservice  $(OUTDIR)/
	for component in $(CCR_COMPONENTS) ; do \
        mv bin/$$component  $(OUTDIR)/ ; \
    done

# make clean
clean:
	$(GO) clean
	rm -rf $(OUTDIR)

# 更新 CRD 信息
update_gene:
	controller-gen object:headerFile=./pkg/hack/boilerplate.go.txt paths=./pkg/crd/apis/ccr/... && \
	controller-gen crd:trivialVersions=true paths="./pkg/crd/apis/ccr/..." output:crd:artifacts:config=./services/ccr-controller/config/crd/bases && \
	controller-gen rbac:roleName=ccr-admin paths="./services/ccr-controller/..." output:rbac:dir=./services/ccr-controller/config/crd/bases

# 更新 CRD chart 文件
update_charts:
	controller-gen crd:trivialVersions=true paths="./pkg/crd/apis/ccr/..." output:crd:artifacts:config=./infra/charts/ccr-controller/crds

update-codegen: ## generate clientset,informers,listers
	chmod +x ./pkg/hack/update-codegen.sh
	./pkg/hack/update-codegen.sh

# Delete all auto-generated API files
swagger-cleanup:
	echo "cleanup swagger file"
	rm -rf ./pkg/harbor/api ./pkg/harbor/legacyapi ./pkg/harbor/chart ./pkg/harbor/addon ./pkg/harbor/model/

# Generate swagger harbor client code
generate-swagger-server: swagger-cleanup
	echo "generate swagger file,use goswagger version: $(GOSWAGGER_VERSION)"
	scripts/swagger-gen.sh $(GOSWAGGER_VERSION)

# Generate swagger server document
generate-swagger-docs:
	swag init --parseDependency --parseDepth 6 --dir services/$(CCR_COMPONENT) --output services/$(CCR_COMPONENT)/docs


build-ccr-component:
	bash build.sh $(CCR_COMPONENT)

docker-build-ccr-component: build-ccr-component
	cp -r infra/charts/ccr output/$(CCR_COMPONENT) && cd output/$(CCR_COMPONENT) && docker build --platform=linux/amd64 --no-cache -t registry.baidubce.com/$(CCR_NAMESPACE)/$(CCR_COMPONENT):$(GITCOMMITLOG)-$(DATE) -f Dockerfile . && cd ../..

docker-build-and-push: docker-build-ccr-component
	docker push registry.baidubce.com/$(CCR_NAMESPACE)/$(CCR_COMPONENT):$(GITCOMMITLOG)-$(DATE)
# avoid filename conflict and speed up build
.PHONY: all prepare compile test package clean build swagger-cleanup generate-swagger-server generate-swagger-docs build-ccr-component docker-build-ccr-component

