apiVersion: v1
kind: ConfigMap
metadata:
  annotations:
  name: aeraki-bootstrap-config
data:
  custom_bootstrap.json: |-
    {
       "static_resources":{
          "clusters":[
             {
                "name":"aeraki-xds",
                "type":"STRICT_DNS",
                "connect_timeout":"1s",
                "max_requests_per_connection":1,
                "circuit_breakers":{
                   "thresholds":[
                      {
                         "max_connections":100000,
                         "max_pending_requests":100000,
                         "max_requests":100000
                      },
                      {
                         "priority":"HIGH",
                         "max_connections":100000,
                         "max_pending_requests":100000,
                         "max_requests":100000
                      }
                   ]
                },
                "http2_protocol_options":{

                },
                "upstream_connection_options":{
                   "tcp_keepalive":{
                      "keepalive_time":300
                   }
                },
                "load_assignment":{
                   "cluster_name":"aeraki-xds",
                   "endpoints":[
                      {
                         "lb_endpoints":[
                            {
                               "endpoint":{
                                  "address":{
                                     "socket_address":{
                                        "address":"aeraki.istio-system",
                                        "port_value":15010
                                     }
                                  }
                               }
                            }
                         ]
                      }
                   ]
                }
             }
          ]
       }
    }
