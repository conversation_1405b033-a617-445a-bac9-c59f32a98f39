apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: dubbo-demo-gateway
  namespace: dubbo-demo
spec:
  selector:
    istio: ingressgateway # use istio default controller
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - "*"
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: dubbo-demo-vs
  namespace: dubbo-demo
spec:
  hosts:
    - "*"
  gateways:
    - dubbo-demo-gateway
  http:
    - match:
        - uri:
            prefix: /echo-dubbo
          queryParams:
            id:
              exact: "123456"
      headers:
        request:
          set:
            grayLabel: v1   # ingress-gateway 路由时，若匹配到对应的 uri 和 queryParams 时则添加对应 header
      route:
        - destination:
            host: dubbo-springboot-demo-consumer.dubbo-demo.svc.cluster.local
            port:
              number: 9999
    - match:
        - uri:
            prefix: /echo-dubbo # 默认路由，并未添加 header
      route:
        - destination:
            host: dubbo-springboot-demo-consumer.dubbo-demo.svc.cluster.local
            port:
              number: 9999