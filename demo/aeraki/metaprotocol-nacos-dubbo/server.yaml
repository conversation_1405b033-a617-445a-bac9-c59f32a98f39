---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: server-demo-deployment-v1
  namespace: dubbo-demo
  labels:
    app: server-demo
    name: server-demo
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: server-demo
      version: v1
  template:
    metadata:
      annotations:
        sidecar.istio.io/bootstrapOverride: aeraki-bootstrap-config
      labels:
        app: server-demo
        version: v1
    spec:
      restartPolicy: Always
      containers:
        - name: server-demo
          image: registry.baidubce.com/bms/dubbo-spring-boot-demo-server:dev
          imagePullPolicy: Always
          ports:
            - containerPort: 20880
          resources:
            limits:
              cpu: 260m
              memory: 600Mi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: server-demo-deployment-v2
  namespace: dubbo-demo
  labels:
    app: server-demo
    name: server-demo
    version: v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: server-demo
      version: v2
  template:
    metadata:
      annotations:
        sidecar.istio.io/bootstrapOverride: aeraki-bootstrap-config
      labels:
        app: server-demo
        version: v2
    spec:
      restartPolicy: Always
      containers:
        - name: server-demo
          image: registry.baidubce.com/bms/dubbo-spring-boot-demo-server:dev
          imagePullPolicy: Always
          ports:
            - containerPort: 20880
          resources:
            limits:
              cpu: 260m
              memory: 600Mi
---
apiVersion: v1
kind: Service
metadata:
  name: dubbo-springboot-demo-server
  namespace: dubbo-demo
spec:
  selector:
    app: server-demo
  ports:
    - name: tcp-metaprotocol-dubbo
      protocol: TCP
      port: 20880
      targetPort: 20880
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: dubbo-springboot-demo-server
  namespace: dubbo-demo
spec:
  host: dubbo-springboot-demo-server.dubbo-demo.svc.cluster.local
  subsets:
    - labels:
        version: v1
      name: v1
    - labels:
        version: v2
      name: v2
---
apiVersion: metaprotocol.aeraki.io/v1alpha1
kind: MetaRouter
metadata:
  name: test-metaprotocol-dubbo-server-route
  namespace: dubbo-demo
spec:
  hosts:
    - dubbo-springboot-demo-server.dubbo-demo.svc.cluster.local
  routes:
    - name: v1
      match:
        attributes:
          interface:
            exact: org.apache.dubbo.springboot.demo.ServerService
          method:
            exact: echo
          grayLabel:
            exact: v1
      route:
        - destination:
            host: dubbo-springboot-demo-server.dubbo-demo.svc.cluster.local
            subset: v1
    - name: v2
      route:
        - destination:
            host: dubbo-springboot-demo-server.dubbo-demo.svc.cluster.local
            subset: v2