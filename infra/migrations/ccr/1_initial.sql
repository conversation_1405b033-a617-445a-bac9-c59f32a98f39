CREATE TABLE IF NOT EXISTS `t_ccr_user_token` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'auto-increasing ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'update time',
    `expired_at` timestamp(3) NOT NULL DEFAULT "1970-01-01T00:00:01.000Z" COMMENT 'delete time',
    `username` varchar(128) NOT NULL DEFAULT '' COMMENT 'user name',
    `account_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'parent id',
    `user_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'user id',
    `instance_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'instance id',
    `password_hash` varchar(256) NOT NULL DEFAULT '' COMMENT 'password hash',
    `description` varchar(512) NOT NULL DEFAULT '' COMMENT 'description',
    PRIMARY KEY(`id`),
    UNIQUE INDEX idx_account_user(`account_id`, `user_id`, `instance_id`, `expired_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ccr user token';

CREATE TABLE IF NOT EXISTS `t_ccr_instance` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'auto-increasing ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'update time',
    `account_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'parent id',
    `user_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'user id',
    `instance_name` varchar(128) NOT NULL DEFAULT '' COMMENT 'instance name',
    `instance_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'instance id',
    `region` varchar(64) NOT NULL DEFAULT '' COMMENT 'region id',
    `public_url` varchar(128) NOT NULL DEFAULT '' COMMENT 'public url',
    `status` varchar(128) NOT NULL DEFAULT '' COMMENT 'status',
    `type` varchar(128) NOT NULL DEFAULT '' COMMENT 'type',
    PRIMARY KEY(`id`),
    UNIQUE INDEX idx_ccr_instance_instance_id(`instance_id`),
    INDEX idx_ccr_instance_region(`region`),
    INDEX idx_ccr_instance_account_id(`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ccr user token';