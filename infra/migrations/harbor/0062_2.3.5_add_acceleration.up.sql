CREATE TABLE IF NOT EXISTS  ccr_acceleration_registration (
    id SERIAL PRIMARY KEY NOT NULL,
    uuid VARCHAR(64) UNIQUE NOT NULL,
    url VARCHAR(256) NOT NULL,
    name VARCHAR(128) UNIQUE NOT NULL,
    description VARCHAR(1024) NULL,
    auth VARCHAR(16) NOT NULL,
    access_cred VARCHAR(512) NULL,
    disabled BOOLEAN NOT NULL DEFAULT FALSE,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    use_internal_addr BOOLEAN NOT NULL DEFAULT FALSE,
    skip_cert_verify BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

/*add acceleration policy table*/
CREATE TABLE IF NOT EXISTS ccr_acceleration_policy (
    id SERIAL PRIMARY KEY NOT NULL,
    name varchar(256),
    enabled boolean NOT NULL DEFAULT true,
    description text,
    filters text,
    creator varchar(256),
    creation_time timestamp default CURRENT_TIMESTAMP,
    update_time timestamp default CURRENT_TIMESTAMP,
    CONSTRAINT unique_ccr_acceleration_policy_name UNIQUE (name)
);
