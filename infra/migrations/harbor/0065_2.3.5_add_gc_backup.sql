/* create repository_backup */
CREATE TABLE IF NOT EXISTS repository_backup (LIKE repository INCLUDING ALL);

ALTER TABLE repository_backup ADD COLUMN deleted_time timestamp WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP;

CREATE OR <PERSON><PERSON>LACE FUNCTION backup_deleted_repository()
    RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO repository_backup
    SELECT OLD.*, CURRENT_TIMESTAMP AS deleted_time
    ON CONFLICT (name) DO UPDATE
        SET
            repository_id = EXCLUDED.repository_id,
            name = EXCLUDED.name,
            project_id = EXCLUDED.project_id,
            description = EXCLUDED.description,
            pull_count = EXCLUDED.pull_count,
            star_count = EXCLUDED.star_count,
            creation_time = EXCLUDED.creation_time,
            update_time = EXCLUDED.update_time,
            deleted_time = EXCLUDED.deleted_time;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;


DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'repository_delete_trigger') THEN
            -- Trigger creation statement goes here
            CREATE TRIGGER repository_delete_trigger
                BEFORE DELETE ON repository
                FOR EACH ROW EXECUTE PROCEDURE backup_deleted_repository();
        END IF;
    END
$$;

/* end create repository_backup */

/* create artifact_backup */
CREATE TABLE IF NOT EXISTS artifact_backup (LIKE artifact INCLUDING ALL);
ALTER TABLE artifact_backup ADD COLUMN deleted_time timestamp WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP;

CREATE OR REPLACE FUNCTION backup_deleted_artifact()
    RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO artifact_backup
    SELECT OLD.*, CURRENT_TIMESTAMP AS deleted_time
    ON CONFLICT (repository_id,digest) DO UPDATE
        SET
            id = EXCLUDED.id,
            project_id = EXCLUDED.project_id,
            repository_name = EXCLUDED.repository_name,
            digest = EXCLUDED.digest,
            pull_time = EXCLUDED.pull_time,
            push_time = EXCLUDED.push_time,
            repository_id = EXCLUDED.repository_id,
            media_type = EXCLUDED.media_type,
            manifest_media_type = EXCLUDED.manifest_media_type,
            size = EXCLUDED.size,
            extra_attrs = EXCLUDED.extra_attrs,
            annotations = EXCLUDED.annotations,
            icon = EXCLUDED.icon,
            deleted_time = EXCLUDED.deleted_time;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'artifact_delete_trigger') THEN
            -- Trigger creation statement goes here
            CREATE TRIGGER artifact_delete_trigger
                AFTER DELETE ON artifact
                FOR EACH ROW
            EXECUTE PROCEDURE backup_deleted_artifact();
        END IF;
    END
$$;

/* end create artifact_backup */

/* create artifact_blob_backup */
CREATE TABLE IF NOT EXISTS artifact_blob_backup (LIKE artifact_blob INCLUDING ALL);
ALTER TABLE artifact_blob_backup ADD COLUMN deleted_time timestamp WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP;

CREATE OR REPLACE FUNCTION backup_deleted_artifact_blob()
    RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO artifact_blob_backup
    SELECT OLD.*, CURRENT_TIMESTAMP AS deleted_time
    ON CONFLICT (digest_af) DO UPDATE
        SET
            id = EXCLUDED.id,
            name = EXCLUDED.name,
            digest_af = EXCLUDED.digest_af,
            digest_blob = EXCLUDED.digest_blob,
            creation_time = EXCLUDED.creation_time,
            deleted_time = EXCLUDED.deleted_time;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;


DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'artifact_blob_delete_trigger') THEN
            -- Trigger creation statement goes here
            CREATE TRIGGER artifact_blob_delete_trigger
                AFTER DELETE ON artifact_blob
                FOR EACH ROW
            EXECUTE PROCEDURE backup_deleted_artifact_blob();
        END IF;
    END
$$;
/* end create artifact_blob_backup */

/* create artifact_manifest_backup */
CREATE TABLE IF NOT EXISTS tag_backup (LIKE tag INCLUDING ALL);
ALTER TABLE tag_backup ADD COLUMN deleted_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP;


CREATE OR REPLACE FUNCTION backup_deleted_tag()
    RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO tag_backup
    SELECT OLD.*, CURRENT_TIMESTAMP AS deleted_time
    ON CONFLICT (repository_id,name) DO UPDATE
        SET
            id = EXCLUDED.id,
            repository_id = EXCLUDED.repository_id,
            artifact_id = EXCLUDED.artifact_id,
            name = EXCLUDED.name,
            push_time = EXCLUDED.push_time,
            pull_time = EXCLUDED.pull_time,
            deleted_time = EXCLUDED.deleted_time;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'tag_delete_trigger') THEN
            -- Trigger creation statement goes here
            CREATE TRIGGER tag_delete_trigger
                AFTER DELETE ON tag
                FOR EACH ROW
            EXECUTE PROCEDURE backup_deleted_tag();
        END IF;
    END
$$;
/* end create tag_backup */

/* create project_blob_backup */
CREATE TABLE IF NOT EXISTS project_blob_backup (LIKE project_blob INCLUDING ALL);
ALTER TABLE project_blob_backup ADD COLUMN IF NOT EXISTS deleted_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP;

CREATE OR REPLACE FUNCTION backup_deleted_project_blob()
    RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO project_blob_backup
    SELECT OLD.*, CURRENT_TIMESTAMP AS deleted_time
    ON CONFLICT (id) DO UPDATE
        SET
            id = EXCLUDED.id,
            project_id = EXCLUDED.project_id,
            blob_id = EXCLUDED.blob_id,
            creation_time = EXCLUDED.creation_time,
            deleted_time = EXCLUDED.deleted_time;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'project_blob_delete_trigger') THEN
            -- Trigger creation statement goes here
            CREATE TRIGGER project_blob_delete_trigger
                AFTER DELETE ON project_blob
                FOR EACH ROW
            EXECUTE PROCEDURE backup_deleted_project_blob();
        END IF;
    END
$$;

/* end create project_blob_backup */