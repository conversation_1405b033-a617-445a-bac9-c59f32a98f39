CREATE TABLE IF NOT EXISTS ccr_chart (
    id SERIAL PRIMARY KEY NOT NULL,
    project_name varchar(256) NOT NULL,
    name varchar(256) NOT NULL,
    version varchar(256) NOT NULL,
    size bigint,
    digest varchar(256),
    metadata text,
    delete_time timestamp,
    create_time timestamp default CURRENT_TIMESTAMP,
    last_modified timestamp,
    UNIQUE (project_name, name, version)
);

CREATE INDEX idx_ccr_chart_delete_time ON ccr_chart(delete_time);
CREATE INDEX idx_ccr_chart_last_modified ON ccr_chart(last_modified);