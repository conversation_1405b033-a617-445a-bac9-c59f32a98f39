/*add trigger policy table*/
CREATE TABLE IF NOT EXISTS trigger_policy (
    id SERIAL NOT NULL,
    name varchar(256),
    enabled boolean NOT NULL DEFAULT true,
    description text,
    targets text,
    event_types text,
    filters text,
    creator varchar(256),
    creation_time timestamp default CURRENT_TIMESTAMP,
    update_time timestamp default CURRENT_TIMESTAMP,
    job_status_hook_url varchar(128),
    PRIMARY KEY (id),
    CONSTRAINT unique_trigger_policy_name UNIQUE (name)
);

/*add trigger job table*/
CREATE TABLE IF NOT EXISTS trigger_job (
    id SERIAL NOT NULL,
    policy_id int NOT NULL,
    status varchar(32),
    /* event_type is the type of trigger event, eg. pushImage, pullImage, uploadChart... */
    event_type varchar(256),
    /* notify_type is the type to notify event to user, eg. HTTP, Email... */
    notify_type varchar(256),
    job_detail text,
    job_uuid varchar(64),
    creation_time timestamp default CURRENT_TIMESTAMP,
    update_time timestamp default CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);