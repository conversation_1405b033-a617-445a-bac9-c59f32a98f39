# Configuration file of Harbor Acceleration Service

# http related config
server:
  name: API
  # listened host for http
  host: 0.0.0.0
  # port for http
  port: 2077

metric:
  # export metrics on `/metrics` endpoint
  enabled: true

converter:
  # enable to add harbor specified annotations to converted image for tracking.
  harborAnnotation: true
  driver:
    # accelerator driver type: stargz`
    type: stargz
    config:
      docker2oci: true
    # add suffix to tag of source image reference as target image reference
    tagSuffix: -esgz
  # kubeconfig path
  kubeconfig: ""
  converterImage:

