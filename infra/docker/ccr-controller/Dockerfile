FROM registry.baidubce.com/ccr-image/ubuntu:18.04-tools

ENV CHART_DIR=/home/<USER>/charts
ENV CONFIG_DIR=/home/<USER>/config
ENV BIN_DIR=/home/<USER>/bin

RUN groupadd -r -g 1000 ccr \
    && useradd --no-log-init -m -g 1000 -u 1000 ccr \
    && mkdir -p $CHART_DIR $CONFIG_DIR $BIN_DIR \
    && chgrp -R ccr $CHART_DIR $CONFIG_DIR $BIN_DIR

WORKDIR /home/<USER>

USER ccr

COPY ccr $CHART_DIR
COPY ccr-controller $BIN_DIR/ccr-controller

ENTRYPOINT ["/home/<USER>/bin/ccr-controller"]


