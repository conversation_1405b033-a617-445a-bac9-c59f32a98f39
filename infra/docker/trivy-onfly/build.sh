#!/bin/bash

set -xe

TRIVY_VUL_LIB_VERSION=${TRIVY_VUL_LIB_VERSION:-v1-2023020812}

cd $(dirname $0)
cur=$PWD

# The temporary directory to clone trivy-db
TEMP=$cur/db
mkdir -p $TEMP
curl "http://trivy-db.bj.bcebos.com/trivy-db/db/${TRIVY_VUL_LIB_VERSION}" -o $TEMP/trivy-db.tgz
tar -xf $TEMP/trivy-db.tgz -C $TEMP
rm $TEMP/trivy-db.tgz

docker build --platform=linux/amd64 -t registry.baidubce.com/ccr-image/trivy-onfly:${TRIVY_VUL_LIB_VERSION} -f $cur/Dockerfile $cur

echo "Building Trivy onfly image finished successfully"
rm -rf $TEMP

docker login --username ${CCR_USERNAME} --password ${CCR_PASSWORD} registry.baidubce.com
docker push registry.baidubce.com/ccr-image/trivy-onfly:${TRIVY_VUL_LIB_VERSION}
