{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Granafa dashboard for dragonfly scheduler.", "editable": true, "gnetId": 15944, "graphTooltip": 0, "id": 48, "iteration": 1680778418532, "links": [], "panels": [{"collapsed": false, "datasource": "${DS_PROMETHEUS}", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 132, "panels": [], "title": "manager", "type": "row"}, {"datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 1}, "id": 143, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "exemplar": true, "expr": "sum(dragonfly_manager_peer_total{})", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Peer Total", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 15, "x": 8, "y": 1}, "id": 182, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "exemplar": true, "expr": "sum(dragonfly_manager_request_duration_seconds_count)by(clusterID)", "interval": "60s", "legendFormat": "{{clusterID}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Request Count", "type": "timeseries"}, {"collapsed": false, "datasource": "${DS_PROMETHEUS}", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 134, "panels": [], "title": "scheduler", "type": "row"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 10}, "id": 172, "links": [], "options": {"displayLabels": [], "legend": {"displayMode": "list", "placement": "right", "values": []}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "sum(dragonfly_scheduler_traffic{}) by (type)", "format": "time_series", "interval": "", "legendFormat": "{{type}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Traffic", "type": "piechart"}, {"datasource": "${DS_PROMETHEUS}", "description": "回源下载流量", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 6, "y": 10}, "id": 149, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "table", "placement": "right"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(dragonfly_scheduler_traffic{type=\"BACK_TO_SOURCE\"}[$interval]))", "forceInterval": false, "instant": false, "interval": "60", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Back To Source Traffic", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "p2p下载流量", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 10}, "id": 174, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(dragonfly_scheduler_traffic{type=\"REMOTE_PEER\"}[$interval]))", "forceInterval": false, "instant": false, "interval": "60", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Remote Peer Traffic", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 18}, "id": 179, "links": [], "maxDataPoints": 100, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(dragonfly_scheduler_register_task_total{}[$interval])) by (instance)", "format": "time_series", "interval": "60", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Register Task", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 18}, "id": 36, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_task_total{}[$interval])) by (instance, host_type)", "hide": false, "interval": "60", "legendFormat": "{{instance}}-{{host_type}}", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Task", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 27}, "id": 150, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "dragonfly_scheduler_download_task_duration_milliseconds_bucket", "forceInterval": false, "instant": false, "interval": "60", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Task Duration_bucket", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 27}, "id": 151, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "dragonfly_scheduler_download_task_duration_milliseconds_sum", "instant": false, "interval": "60", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Task Duration", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 36}, "id": 90, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_announce_host_total{}[$interval])) -\nsum(rate(dragonfly_scheduler_announce_host_failure_total{}[$interval])))\n/ \nsum(rate(dragonfly_scheduler_announce_host_total{}[$interval]))", "format": "time_series", "interval": "60", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Announce Host Success Rate", "type": "gauge"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 36}, "id": 91, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(dragonfly_scheduler_announce_host_total{}[$interval]))", "format": "time_series", "interval": "60", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Announce Host QPS", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}, "id": 93, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(dragonfly_scheduler_announce_task_total{}[$interval])) by (instance)", "interval": "60", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Announce Task", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}, "id": 178, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(dragonfly_scheduler_announce_task_failure_total{}[$interval])) by (instance)", "interval": "60", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Announce Task Failed", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 48}, "id": 177, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(dragonfly_scheduler_announce_host_total{}[$interval])) by (instance)", "interval": "60", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Announce Host", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 48}, "id": 92, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(dragonfly_scheduler_announce_host_failure_total{}[$interval])) by (instance)", "interval": "60", "legendFormat": "{{instance}}", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Announce Host Failed", "type": "timeseries"}, {"collapsed": false, "datasource": "${DS_PROMETHEUS}", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 56}, "id": 136, "panels": [], "title": "dfdaemon", "type": "row"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 57}, "id": 164, "links": [], "maxDataPoints": 100, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(dragonfly_dfdaemon_peer_task_cache_hit_total{}[$interval])) / sum(rate(dragonfly_dfdaemon_peer_task_total{}[$interval]))", "format": "time_series", "interval": "60", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Task Cach Hit Rate", "type": "gauge"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 6, "y": 57}, "id": 168, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "editorMode": "code", "exemplar": true, "expr": "increase(dragonfly_dfdaemon_peer_task_total{}[$interval])", "format": "time_series", "interval": "60", "intervalFactor": 2, "legendFormat": "{{instance}}-{{container}}", "metric": "go_gc_duration_seconds", "range": true, "refId": "A", "step": 4}], "timeFrom": null, "timeShift": null, "title": "peer_task", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 57}, "id": 170, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "editorMode": "code", "exemplar": true, "expr": "increase(dragonfly_dfdaemon_peer_task_cache_hit_total{}[$interval])", "format": "time_series", "interval": "60", "intervalFactor": 2, "legendFormat": "{{instance}}-{{container}}", "metric": "go_gc_duration_seconds", "range": true, "refId": "A", "step": 4}], "timeFrom": null, "timeShift": null, "title": "peer_task_cache_hit", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 65}, "id": 185, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "(sum(rate(dragonfly_dfdaemon_piece_task_total{}[$interval])) - sum(rate(dragonfly_dfdaemon_piece_task_failed_total{}[$interval]))) / sum(rate(dragonfly_dfdaemon_piece_task_total{}[$interval]))", "format": "time_series", "interval": "60", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "piece_task  Success Rate", "type": "gauge"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 6, "y": 65}, "id": 183, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "editorMode": "code", "exemplar": true, "expr": "increase(dragonfly_dfdaemon_piece_task_total{}[$interval])", "format": "time_series", "interval": "60", "intervalFactor": 2, "legendFormat": "{{instance}}-{{container}}", "metric": "go_gc_duration_seconds", "range": true, "refId": "A", "step": 4}], "timeFrom": null, "timeShift": null, "title": "piece_task", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 65}, "id": 184, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "table", "placement": "right"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "editorMode": "code", "exemplar": true, "expr": "increase(dragonfly_dfdaemon_piece_task_failed_total{}[$interval])", "format": "time_series", "interval": "60", "intervalFactor": 2, "legendFormat": "{{instance}}-{{container}}", "metric": "go_gc_duration_seconds", "range": true, "refId": "A", "step": 4}], "timeFrom": null, "timeShift": null, "title": "piece_task_failed", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 73}, "id": 166, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(dragonfly_dfdaemon_peer_task_total{}[$interval]))", "format": "time_series", "interval": "60", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download QPS", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 7, "w": 11, "x": 0, "y": 77}, "id": 196, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(dragonfly_dfdaemon_upload_request_duration_seconds_count{}[$interval]))", "format": "time_series", "interval": "60", "intervalFactor": 1, "legendFormat": "instance", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Upload QPS", "type": "stat"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "stepAfter", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 13, "x": 11, "y": 77}, "id": 194, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "rate(dragonfly_dfdaemon_upload_request_duration_seconds_sum{}[$interval]) / rate(dragonfly_dfdaemon_upload_request_duration_seconds_count{}[$interval])", "forceInterval": false, "instant": false, "interval": "60", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Upload Request Duration", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "stepBefore", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 84}, "id": 193, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(dragonfly_dfdaemon_upload_response_size_bytes_sum{}[$interval])) by (instance, container)", "forceInterval": false, "instant": false, "interval": "60", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "upload traffic", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 93}, "id": 186, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "editorMode": "code", "exemplar": true, "expr": "increase(dragonfly_dfdaemon_proxy_request_total{}[$interval])", "format": "time_series", "interval": "60", "intervalFactor": 2, "legendFormat": "{{instance}}-{{container}}", "metric": "go_gc_duration_seconds", "range": true, "refId": "A", "step": 4}], "timeFrom": null, "timeShift": null, "title": "proxy_request", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 93}, "id": 187, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "editorMode": "code", "exemplar": true, "expr": "increase(dragonfly_dfdaemon_proxy_request_running_total{}[$interval])", "format": "time_series", "interval": "60", "intervalFactor": 2, "legendFormat": "{{instance}}-{{container}}", "metric": "go_gc_duration_seconds", "range": true, "refId": "A", "step": 4}], "timeFrom": null, "timeShift": null, "title": "proxy_request_running", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 101}, "id": 190, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "(sum(rate(dragonfly_dfdaemon_seed_peer_download_total{}[$interval])) - sum(rate(dragonfly_dfdaemon_seed_peer_download_failure_total{}[$interval]))) / sum(rate(dragonfly_dfdaemon_seed_peer_download_total{}[$interval]))", "format": "time_series", "interval": "60", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "seed_peer_download  Success Rate", "type": "gauge"}, {"datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 18, "x": 6, "y": 101}, "id": 192, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(dragonfly_dfdaemon_seed_peer_download_traffic{}[$interval]))", "forceInterval": false, "instant": false, "interval": "60", "legendFormat": "{{type}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "seed_peer_download_traffic", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 107}, "id": 188, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "editorMode": "code", "exemplar": true, "expr": "increase(dragonfly_dfdaemon_seed_peer_download_total{}[$interval])", "format": "time_series", "interval": "60", "intervalFactor": 2, "legendFormat": "{{instance}}-{{container}}", "metric": "go_gc_duration_seconds", "range": true, "refId": "A", "step": 4}], "timeFrom": null, "timeShift": null, "title": "seed_peer_download", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 107}, "id": 189, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "editorMode": "code", "exemplar": true, "expr": "increase(dragonfly_dfdaemon_seed_peer_download_failure_total{}[$interval])", "format": "time_series", "interval": "60", "intervalFactor": 2, "legendFormat": "{{instance}}-{{container}}", "metric": "go_gc_duration_seconds", "range": true, "refId": "A", "step": 4}], "timeFrom": null, "timeShift": null, "title": "seed_peer_download_failure", "type": "timeseries"}, {"datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 115}, "id": 62, "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "vertical", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showUnfilled": true, "text": {}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "topk(10, sum(increase(dragonfly_scheduler_host_traffic{type=\"upload\"}[1d])) by (host_ip))", "instant": true, "interval": "60", "intervalFactor": 1, "legendFormat": "{{host_ip}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Host Upload Traffic Top 10", "type": "bargauge"}, {"datasource": "${DS_PROMETHEUS}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 125}, "id": 60, "options": {"displayMode": "gradient", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}}, "pluginVersion": "7.5.16", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "topk(10, sum(increase(dragonfly_scheduler_host_traffic{type=\"download\"}[1d])) by (host_ip))", "instant": true, "interval": "60", "legendFormat": "{{host_ip}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Host Download Traffic Top 10", "type": "bargauge"}], "refresh": "", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)", "value": "cprom-autotest-wstvy6(cprom-wew2bnwr63z3)"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "DS_PROMETHEUS", "multi": false, "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"selected": false, "text": "10m", "value": "10m"}, "description": null, "error": null, "hide": 0, "label": null, "name": "interval", "options": [{"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": true, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "1m,5m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Overview", "uid": "v4WNKEBVz", "version": 91}