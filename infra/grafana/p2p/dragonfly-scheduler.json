{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Granafa dashboard for dragonfly scheduler.", "editable": true, "gnetId": 15944, "graphTooltip": 0, "id": 391, "iteration": 1694605031300, "links": [], "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "title": "Global", "type": "row"}, {"datasource": "cce-vm-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 132, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(dragonfly_scheduler_version{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}) by (git_version, clusterID,pod_namespace,instance)", "format": "time_series", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "UP", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "p2p总流量", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 134, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_traffic{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (type,clusterID,pod_namespace)", "interval": "60", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Traffic", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "回源下载流量", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 9}, "id": 136, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "table", "placement": "right"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_traffic{clusterID=\"$cluster\",pod_namespace=\"$namespace\",type=\"BACK_TO_SOURCE\"}[$interval])) by (clusterID, pod_namespace)", "interval": "60", "legendFormat": "{{clusterID}}:{{pod_namespace}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Back To Source Traffic", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "peer本机流量", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 9}, "id": 153, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "table", "placement": "right"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_traffic{clusterID=\"$cluster\",pod_namespace=\"$namespace\",type=\"LOCAL_PEER\"}[$interval])) by (clusterID, pod_namespace)", "interval": "60", "legendFormat": "{{clusterID}}:{{pod_namespace}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Hit Local Peer Traffic", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "peer共享下载流量", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 9}, "id": 138, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_traffic{clusterID=\"$cluster\",pod_namespace=\"$namespace\",type=\"REMOTE_PEER\"}[$interval])) by (clusterID, pod_namespace)", "interval": "60", "legendFormat": "{{clusterID}}-{{pod_namespace}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Hit Remote Peer Traffic", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 140, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(rate(dragonfly_scheduler_traffic{clusterID=\"$cluster\",pod_namespace=\"$namespace\",type=\"BACK_TO_SOURCE\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "{{clusterID}}:{{pod_namespace}}", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "back source rate", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "id": 142, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"exemplar": true, "expr": "sum(rate(dragonfly_scheduler_traffic{clusterID=\"$cluster\",pod_namespace=\"$namespace\",type=\"REMOTE_PEER\"}[$interval]))", "interval": "60", "legendFormat": "{{clusterID}}:{{pod_namespace}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "remote peer rate", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 25}, "id": 56, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_concurrent_schedule_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (clusterID,pod_namespace,instance)", "interval": "60", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Concurrent Scheduling", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 25}, "id": 152, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_peer_duration_milliseconds_sum{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]) / increase(dragonfly_scheduler_download_peer_duration_milliseconds_count{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "interval": "60", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Task Duration_bucket", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 34}, "id": 74, "panels": [], "title": "GRPC", "type": "row"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 35}, "id": 12, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(grpc_server_handled_total{grpc_service=\"scheduler.Scheduler\",grpc_type=\"unary\",grpc_code=\"OK\"}[$interval])) + \nsum(rate(grpc_server_handled_total{grpc_service=\"scheduler.Scheduler\",grpc_type=\"unary\",grpc_code=\"NotFound\"}[$interval])) + \nsum(rate(grpc_server_handled_total{grpc_service=\"scheduler.Scheduler\",grpc_type=\"unary\",grpc_code=\"PermissionDenied\"}[$interval])) + \nsum(rate(grpc_server_handled_total{grpc_service=\"scheduler.Scheduler\",grpc_type=\"unary\",grpc_code=\"InvalidArgument\"}[$interval])))\n/ \nsum(rate(grpc_server_started_total{grpc_service=\"scheduler.Scheduler\",grpc_type=\"unary\"}[$interval]))", "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "title": "GRPC Global Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 35}, "id": 14, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(grpc_server_started_total{grpc_service=\"scheduler.Scheduler\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "GRPC QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 39}, "id": 26, "links": [], "options": {"graph": {}, "legend": {"calcs": ["mean", "lastNotNull", "max", "min", "sum"], "displayMode": "table", "placement": "right"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(grpc_server_handled_total{grpc_service=\"scheduler.Scheduler\",grpc_type=\"unary\"}[$interval])) by (grpc_code, grpc_method)", "hide": false, "interval": "", "legendFormat": "{{grpc_method}}/{{grpc_code}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "GRPC request code", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 48}, "id": 44, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_download_peer_finished_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))-sum(rate(dragonfly_scheduler_download_peer_finished_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/sum(rate(dragonfly_scheduler_download_peer_finished_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "60", "legendFormat": "", "refId": "A"}], "title": "Download Peer Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 48}, "id": 46, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(grpc_server_started_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\",grpc_service=\"scheduler.Scheduler\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Download Peer QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 52}, "id": 36, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_peer_finished_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (clusterID,pod_namespace, host_type)", "hide": false, "interval": "60", "legendFormat": "{{clusterID}}-{{pod_namespace}}-{{host_type}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Peer", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 52}, "id": 38, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_peer_finished_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (clusterID, pod_namespace, host_type)", "hide": false, "interval": "60", "legendFormat": "{{instance}}-{{host_type}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Peer Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 52}, "id": 112, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_peer_back_to_source_finished_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (clusterID, pod_namespace, host_type)", "hide": false, "interval": "60", "legendFormat": "{{clusterID}}-{{pod_namespace}}-{{host_type}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Peer Back To Source Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 60}, "id": 90, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_announce_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))-sum(rate(dragonfly_scheduler_announce_peer_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/sum(rate(dragonfly_scheduler_announce_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Announce Peer Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 60}, "id": 91, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(dragonfly_scheduler_announce_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Announce Peer QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 64}, "id": 93, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_announce_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Announce Peer", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 64}, "id": 92, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_announce_peer_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Announce Peer Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 72}, "id": 48, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_register_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))-sum(rate(dragonfly_scheduler_register_peer_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/sum(rate(dragonfly_scheduler_register_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Register Peer Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 72}, "id": 50, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(dragonfly_scheduler_download_peer_started_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Register Peer QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 76}, "id": 40, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_register_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Register Peer", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 76}, "id": 115, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_register_peer_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Register Peer Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 84}, "id": 117, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_download_peer_started_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))-sum(rate(dragonfly_scheduler_download_peer_started_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/sum(rate(dragonfly_scheduler_download_peer_started_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Download Peer Started Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 84}, "id": 118, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(dragonfly_scheduler_download_peer_started_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Download Peer Started QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 88}, "id": 119, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_peer_started_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "60", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Peer Started", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 88}, "id": 120, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_peer_started_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Peer Started Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 96}, "id": 121, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_download_peer_back_to_source_started_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))-sum(rate(dragonfly_scheduler_download_peer_back_to_source_started_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/sum(rate(dragonfly_scheduler_download_peer_back_to_source_started_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Download Peer Back To Source Started Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 96}, "id": 122, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(dragonfly_scheduler_download_peer_back_to_source_started_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Download Peer Back To Source Started QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 100}, "id": 123, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_peer_back_to_source_started_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Peer Back To Source Started", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 100}, "id": 124, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_peer_back_to_source_started_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Peer Back To Source Started Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 108}, "id": 125, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_download_piece_finished_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))-sum(rate(dragonfly_scheduler_download_piece_finished_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/sum(rate(dragonfly_scheduler_download_piece_finished_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Download Piece Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 108}, "id": 114, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(dragonfly_scheduler_download_piece_finished_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Download Piece QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 112}, "id": 127, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_piece_finished_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Piece", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 112}, "id": 128, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_piece_finished_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Piece Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 112}, "id": 129, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_download_piece_back_to_source_finished_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Download Piece Back To Source Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 120}, "id": 113, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_stat_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))-sum(rate(dragonfly_scheduler_stat_peer_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/sum(rate(dragonfly_scheduler_stat_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Stat Peer Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 120}, "id": 126, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(dragonfly_scheduler_stat_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Stat Peer QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 124}, "id": 42, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_stat_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Stat Peer", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 124}, "id": 116, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_stat_peer_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Stat Peer Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 132}, "id": 81, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_leave_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))-sum(rate(dragonfly_scheduler_leave_peer_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/sum(rate(dragonfly_scheduler_leave_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Leave Peer Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 132}, "id": 82, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(dragonfly_scheduler_leave_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Leave Peer QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 136}, "id": 83, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_leave_peer_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "60", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Leave Peer", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 136}, "id": 100, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_leave_peer_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Leave Peer Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 144}, "id": 86, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_stat_task_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) - sum(rate(dragonfly_scheduler_stat_task_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/ sum(rate(dragonfly_scheduler_stat_task_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Stat Task Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 144}, "id": 87, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(dragonfly_scheduler_stat_task_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Stat Task QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 148}, "id": 144, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_stat_task_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (clusterID)", "interval": "60", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Stat Task", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 148}, "id": 146, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.16", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_stat_task_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (clusterID)", "interval": "60", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Stat Task Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 157}, "id": 94, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_announce_host_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))-sum(rate(dragonfly_scheduler_announce_host_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/sum(rate(dragonfly_scheduler_announce_host_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Announce Host Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 157}, "id": 95, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_announce_host_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (clusterID,pod_namespace,instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "title": "Announce Host QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 161}, "id": 96, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_announce_host_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "60", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Announce Host", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 161}, "id": 97, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_announce_host_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (instance)", "hide": false, "interval": "60", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Announce Host Failed", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 169}, "id": 98, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_scheduler_leave_host_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))-sum(rate(dragonfly_scheduler_leave_host_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])))/sum(rate(dragonfly_scheduler_leave_host_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Leave Host Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 4, "w": 18, "x": 6, "y": 169}, "id": 99, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(dragonfly_scheduler_leave_host_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Leave Host QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 173}, "id": 88, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_leave_host_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (clusterID,pod_namespace)", "hide": false, "interval": "60", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Leave Host", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 173}, "id": 101, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_scheduler_leave_host_failure_total{clusterID=\"$cluster\",pod_namespace=\"$namespace\"}[$interval])) by (clusterID,pod_namespace)", "hide": false, "interval": "60", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Leave Host Failed", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 181}, "id": 76, "panels": [], "title": "Golang", "type": "row"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 182}, "id": 32, "links": [], "options": {"graph": {}, "legend": {"calcs": ["mean", "lastNotNull", "max", "min", "sum"], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "go_goroutines{clusterID=\"$cluster\",component=\"scheduler\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Goroutines", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 182}, "id": 30, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "go_gc_duration_seconds{clusterID=\"$cluster\",component=\"scheduler\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}-{{quantile}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "GC duration quantiles", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "resident"}, "properties": [{"id": "unit", "value": "short"}]}]}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 190}, "id": 64, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "process_resident_memory_bytes{clusterID=\"$cluster\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}-resident", "refId": "A"}, {"exemplar": true, "expr": "process_virtual_memory_bytes{component=\"scheduler\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}-virtual", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "process memory", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "resident"}, "properties": [{"id": "unit", "value": "short"}]}]}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 190}, "id": 65, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "go_memstats_alloc_bytes{clusterID=\"$cluster\",component=\"scheduler\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}-bytes_allocated", "refId": "A"}, {"exemplar": true, "expr": "rate(go_memstats_alloc_bytes_total{clusterID=\"$cluster\",component=\"scheduler\"}[30s])", "hide": false, "interval": "60", "legendFormat": "{{instance}}-alloc_rate", "refId": "B"}, {"exemplar": true, "expr": "go_memstats_stack_inuse_bytes{clusterID=\"$cluster\",component=\"scheduler\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}-stack_inuse", "refId": "C"}, {"exemplar": true, "expr": "go_memstats_heap_inuse_bytes{clusterID=\"$cluster\",component=\"scheduler\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}-heap_inuse", "refId": "D"}], "timeFrom": null, "timeShift": null, "title": "go memstats", "type": "timeseries"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"selected": false, "text": "5m", "value": "5m"}, "description": null, "error": null, "hide": 0, "label": null, "name": "interval", "options": [{"selected": false, "text": "1m", "value": "1m"}, {"selected": true, "text": "5m", "value": "5m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "1m,5m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"allValue": null, "current": {"selected": true, "text": "cce-t1jqcsca", "value": "cce-t1jqcsca"}, "description": null, "error": null, "hide": 0, "includeAll": true, "label": "cluster", "multi": false, "name": "cluster", "options": [{"selected": false, "text": "All", "value": "$__all"}, {"selected": false, "text": "cce-ptpenwpk", "value": "cce-ptpenwpk"}, {"selected": false, "text": "cce-567hbeyy", "value": "cce-567hbeyy"}, {"selected": false, "text": "cce-s3yvgr5v", "value": "cce-s3yvgr5v"}, {"selected": true, "text": "cce-t1jqcsca", "value": "cce-t1jqcsca"}, {"selected": false, "text": "cce-9wyj2x6l", "value": "cce-9wyj2x6l"}, {"selected": false, "text": "cce-0g68wl24", "value": "cce-0g68wl24"}, {"selected": false, "text": "cce-harhrfgw", "value": "cce-harhrfgw"}], "query": "cce-ptpenwpk,cce-567h<PERSON><PERSON>,cce-s3yvgr5v,cce-t1jqcsca,cce-9wyj2x6l,cce-0g68wl24,cce-harhrfgw", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"allValue": null, "current": {"selected": true, "text": "ccr-4xxbdreg", "value": "ccr-4xxbdreg"}, "datasource": "cce-vm-cluster", "definition": "label_values(dragonfly_scheduler_version{clusterID=\"$cluster\"}, pod_namespace)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(dragonfly_scheduler_version{clusterID=\"$cluster\"}, pod_namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "P2P Scheduler", "uid": "sZHAs9zIk", "version": 43}