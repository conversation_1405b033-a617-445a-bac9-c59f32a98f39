{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Granafa dashboard for dragonfly manager.", "editable": true, "gnetId": 15945, "graphTooltip": 0, "id": 390, "iteration": 1694584633545, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 50, "panels": [], "title": "Global", "type": "row"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 0, "y": 1}, "id": 54, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(dragonfly_manager_version{clusterID=\"$cluster\"}) by (git_version, clusterID, instance)", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "UP", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 15, "x": 9, "y": 1}, "id": 64, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "v1.0", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_manager_peer_total{clusterID=\"$cluster\"}))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "peer count", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 5, "w": 9, "x": 0, "y": 9}, "id": 65, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(dragonfly_manager_search_scheduler_cluster_total{clusterID=\"$cluster\"}[$interval])) - sum(rate(dragonfly_manager_search_scheduler_cluster_failure_total{clusterID=\"$cluster\"}[$interval])))/sum(rate(dragonfly_manager_search_scheduler_cluster_total{clusterID=\"$cluster\"}[$interval]))", "interval": "60", "legendFormat": "", "refId": "A"}], "title": "Search Scheduler Cluster Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 5, "w": 15, "x": 9, "y": 9}, "id": 59, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(dragonfly_manager_search_scheduler_cluster_total{clusterID=\"$cluster\"}[$interval])) by (clusterID)", "interval": "60", "legendFormat": "", "refId": "A"}], "title": "Search Scheduler Cluster QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "id": 61, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_manager_search_scheduler_cluster_total{clusterID=\"$cluster\"}[$interval])) by (clusterID)", "hide": false, "interval": "60", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Search Scheduler Cluster", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 63, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(dragonfly_manager_search_scheduler_cluster_failure_total{clusterID=\"$cluster\"}[$interval])) by (clusterID)", "interval": "60", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Search Scheduler Cluster Failed", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 22}, "id": 44, "panels": [], "title": "GRPC", "type": "row"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 95}, {"color": "#d44a3a", "value": 99}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 23}, "id": 12, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "(sum(rate(grpc_server_handled_total{clusterID=\"cce-t1jqcsca\",grpc_service=\"manager.Manager\",grpc_type=\"unary\",grpc_code=\"OK\"}[$interval])) + sum(rate(grpc_server_handled_total{clusterID=\"cce-t1jqcsca\",grpc_service=\"manager.Manager\",grpc_type=\"unary\",grpc_code=\"NotFound\"}[$interval])) + sum(rate(grpc_server_handled_total{clusterID=\"cce-t1jqcsca\",grpc_service=\"manager.Manager\",grpc_type=\"unary\",grpc_code=\"PermissionDenied\"}[$interval])) + sum(rate(grpc_server_handled_total{clusterID=\"cce-t1jqcsca\",grpc_service=\"manager.Manager\",grpc_type=\"unary\",grpc_code=\"InvalidArgument\"}[$interval])))/sum(rate(grpc_server_started_total{clusterID=\"cce-t1jqcsca\",grpc_service=\"manager.Manager\",grpc_type=\"unary\"}[$interval]))", "interval": "60", "legendFormat": "", "refId": "A"}], "title": "GRPC Global Success Rate", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 23}, "id": 14, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(irate(grpc_server_started_total{clusterID=\"$cluster\",grpc_service=\"manager.Manager\"}[$interval]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "GRPC QPS", "type": "stat"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 28}, "id": 26, "links": [], "options": {"graph": {}, "legend": {"calcs": ["mean", "lastNotNull", "max", "min", "sum"], "displayMode": "table", "placement": "right"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "sum(increase(grpc_server_handled_total{clusterID=\"$cluster\",grpc_service=\"manager.Manager\",grpc_type=\"unary\"}[$interval])) by (grpc_code, grpc_method, clusterID)", "interval": "60", "legendFormat": "{{grpc_method}}/{{grpc_code}}", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "GRPC request code", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 37}, "id": 48, "panels": [], "title": "Golang", "type": "row"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 38}, "id": 32, "links": [], "options": {"graph": {}, "legend": {"calcs": ["mean", "lastNotNull", "max", "min", "sum"], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "go_goroutines{clusterID=\"$cluster\",component=\"manager\"}", "interval": "60", "legendFormat": "{{instance}}", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Goroutines", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 38}, "id": 30, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "go_gc_duration_seconds{clusterID=\"$cluster\",component=\"manager\"}", "interval": "60", "legendFormat": "{{instance}}-{{quantile}}", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "GC duration quantiles", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 46}, "id": 28, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "process_resident_memory_bytes{clusterID=\"$cluster\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}-resident", "refId": "A"}, {"exemplar": true, "expr": "process_virtual_memory_bytes{component=\"manager\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}-virtual", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "process memory", "type": "timeseries"}, {"datasource": "cce-vm-cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "alloc rate"}, "properties": [{"id": "unit", "value": "Bps"}]}]}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 46}, "id": 34, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "7.5.17", "targets": [{"exemplar": true, "expr": "go_memstats_alloc_bytes{clusterID=\"$cluster\",component=\"manager\"}", "interval": "60", "legendFormat": "{{instance}}-bytes_allocated", "refId": "A"}, {"exemplar": true, "expr": "go_memstats_stack_inuse_bytes{clusterID=\"$cluster\",component=\"manager\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}-stack_inuse", "refId": "C"}, {"exemplar": true, "expr": "go_memstats_heap_inuse_bytes{clusterID=\"$cluster\",component=\"manager\"}", "hide": false, "interval": "60", "legendFormat": "{{instance}}-heap_inuse", "refId": "D"}, {"exemplar": true, "expr": "rate(go_memstats_alloc_bytes_total{clusterID=\"$cluster\",component=\"manager\"}[30s])", "hide": false, "interval": "60", "legendFormat": "{{instance}}-alloc_rate", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "go memstats", "type": "timeseries"}], "refresh": "", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"selected": false, "text": "5m", "value": "5m"}, "description": null, "error": null, "hide": 0, "label": null, "name": "interval", "options": [{"selected": false, "text": "1m", "value": "1m"}, {"selected": true, "text": "5m", "value": "5m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "1m,5m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"allValue": null, "current": {"selected": false, "text": "cce-ptpenwpk", "value": "cce-ptpenwpk"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "cluster", "multi": false, "name": "cluster", "options": [{"selected": false, "text": "cce-ptpenwpk", "value": "cce-ptpenwpk"}, {"selected": false, "text": "cce-567hbeyy", "value": "cce-567hbeyy"}, {"selected": false, "text": "cce-s3yvgr5v", "value": "cce-s3yvgr5v"}, {"selected": true, "text": "cce-t1jqcsca", "value": "cce-t1jqcsca"}, {"selected": false, "text": "cce-9wyj2x6l", "value": "cce-9wyj2x6l"}, {"selected": false, "text": "cce-0g68wl24", "value": "cce-0g68wl24"}, {"selected": false, "text": "cce-harhrfgw", "value": "cce-harhrfgw"}], "query": "cce-ptpenwpk,cce-567h<PERSON><PERSON>,cce-s3yvgr5v,cce-t1jqcsca,cce-9wyj2x6l,cce-0g68wl24,cce-harhrfgw", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "P2P Manager", "uid": "TL-e3c9nz", "version": 2}