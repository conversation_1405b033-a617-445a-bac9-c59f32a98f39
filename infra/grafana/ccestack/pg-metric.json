{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"datasource": "-- <PERSON><PERSON> --", "enable": true, "iconColor": "rgba(0, 211, 255, 1)", "iconSize": 0, "lineColor": "", "name": "Annotations & Alerts", "query": "", "showLine": false, "tagsField": "", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "textField": "", "type": "dashboard"}]}, "editable": false, "gnetId": null, "graphTooltip": 1, "id": 142, "iteration": 1684119301107, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 13, "panels": [], "title": "主机监控", "type": "row"}, {"datasource": null, "description": "container_cpu_usage_seconds_total", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\",pod=~\"$pod\"}[2m])", "interval": "", "legendFormat": "{{namespace}}.{{pod}}", "refId": "A"}], "title": "CPU使用率", "type": "timeseries"}, {"datasource": null, "description": "container_cpu_usage_seconds_total", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 127, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\",pod=~\"$pod\"}[2m])", "interval": "", "legendFormat": "{{namespace}}.{{pod}}", "refId": "A"}], "title": "CPU使用率", "type": "timeseries"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 119, "panels": [], "title": "部署监控", "type": "row"}, {"datasource": null, "description": "mysql_is_alived\n1：alive\n2：dead", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 125, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(pg_up{namespace=\"$namespace\"}) by(namespace)", "interval": "", "legendFormat": "{{namespace}}", "refId": "A"}], "title": "存活状态", "type": "timeseries"}, {"datasource": null, "description": "pg_stat_replication_pg_wal_lsn_diff", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 121, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "pg_stat_replication_pg_wal_lsn_diff{namespace=~\"$namespace\",pod=~\"$pod\"}", "interval": "", "legendFormat": "{{namespace}}.{{pod}}", "refId": "A"}], "title": "主库当前位置与备库回放差异", "type": "timeseries"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 56, "panels": [], "title": "引擎监控", "type": "row"}, {"datasource": null, "description": "pg_stat_activity_count", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 19}, "id": 57, "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": true, "expr": "sum(pg_stat_activity_count{namespace=\"$namespace\",pod=~\"$pod\"})by (namespace,pod,state)", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{namespace}}.{{pod}}.{{state}}", "refId": "A"}], "title": "连接数", "type": "timeseries"}, {"datasource": null, "description": "pg_stat_activity_max_tx_duration", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 19}, "id": 60, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single"}, "tooltipOptions": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "exemplar": false, "expr": "sum(pg_stat_activity_max_tx_duration{namespace=\"$namespace\",pod=\"$pod\"})by (namespace,pod,state)", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{namespace}}.{{pod}}.{{state}}", "refId": "A"}], "title": "长事务", "type": "timeseries"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["postgres", "harbor"], "templating": {"list": [{"allFormat": "", "allValue": "", "current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": null, "definition": "label_values(pg_up, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "命名空间", "multi": false, "multiFormat": "", "name": "namespace", "options": [], "query": {"query": "label_values(pg_up, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": null, "definition": "label_values(pg_up{namespace=\"$namespace\"}, pod_name)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "pod", "multi": true, "name": "pod", "options": [], "query": {"query": "label_values(pg_up{namespace=\"$namespace\"}, pod_name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "2023-05-11T08:41:19.013Z", "to": "2023-05-11T10:07:43.229Z"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "PG", "uid": "tMACkVKVk", "version": 1}