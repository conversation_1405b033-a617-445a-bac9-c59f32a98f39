apiVersion: v1
kind: Secret
metadata:
  name: {{ include "ccr-event-keeper.fullname" . }}
  labels:
    {{- include "ccr-event-keeper.labels" . | nindent 4 }}
data:
  config.yaml: {{ tpl (.Files.Get "conf/config.yaml") . | b64enc }}
---
apiVersion: v1
kind: Secret
type: kubernetes.io/dockerconfigjson
metadata:
  name: {{ include "ccr-event-keeper.fullname" . }}-registry-secret
  labels:
    {{- include "ccr-event-keeper.labels" . | nindent 4 }}
data:
  .dockerconfigjson: {{ .Values.ccrPullSecret }}