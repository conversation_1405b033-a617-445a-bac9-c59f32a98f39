# HK values for ccr-auth.

image:
  # In <PERSON><PERSON>'s pipeline, the variable will be replaced according to the component
  tag: ccr-auth.image.tag


config:
  ak: REPLACEME_ACCESSKEY
  sk: REPLACEME_SECRETKEY
  clusterId: cce-harhrfgw
  region: hkg
  endpoint:
    cce: cce.hkg.baidubce.com
    iam: http://iam.hkg.bce.baidu-int.com
    ccr: *************
    sts: http://sts.hkg.bce.baidu-int.com:8586
  servicePassword: REPLACEME_SERVICE_PASSWORD
  mysqlConnection: ccr:qAnikdKni!I_eH6p@tcp(mysql57.rdsmq588d1toqph.rds.hkg.baidubce.com:3306)/ccr?parseTime=True&loc=Asia%2fShanghai


affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution: []
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app.kubernetes.io/name
                operator: In
                values:
                  - ccr-auth
          topologyKey: kubernetes.io/hostname