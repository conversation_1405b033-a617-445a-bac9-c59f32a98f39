# GZ values for ccr-auth.

kind: DaemonSet
hostNetwork: true

image:
  # In <PERSON><PERSON>'s pipeline, the variable will be replaced according to the component
  tag: ccr-auth.image.tag


config:
  ak: REPLACEME_ACCESSKEY
  sk: REPLACEME_SECRETKEY
  clusterId: cce-567hbeyy
  region: gz
  endpoint:
    cce: cce.gz.baidubce.com
    iam: http://iam.gz.bce-internal.baidu.com
    ccr: **************
    sts: http://sts.gz.iam.sdns.baidu.com:8586
  servicePassword: REPLACEME_SERVICE_PASSWORD
  mysqlConnection: ccr:cBx4dl7DwTcPct_G2nLIeDI5Y#0dZ5NG@tcp(***********:3306)/ccr?parseTime=True&loc=Asia%2fShanghai
