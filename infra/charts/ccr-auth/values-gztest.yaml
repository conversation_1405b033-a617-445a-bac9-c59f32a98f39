# GZTEST values for ccr-auth.

replicaCount: 1

image:
  # In <PERSON><PERSON>'s pipeline, the variable will be replaced according to the component
  tag: ccr-auth.image.tag


config:
  ak: REPLACEME_ACCESSKEY
  sk: REPLACEME_SECRETKEY
  clusterId: cce-ptpenwpk
  region: gztest
  endpoint:
    cce: *************:8693
    iam: http://iam.gz.bce-internal.baidu.com
    ccr: ************
    sts: http://sts.gz.iam.sdns.baidu.com:8586
  servicePassword: REPLACEME_SERVICE_PASSWORD
  mysqlConnection: ccr:5sqdvGzj7GW9hy!!@tcp(************:3306)/ccr?parseTime=True&loc=Asia%2fShanghai

# The test environment is a single node, and affinity is not required
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution: null
    preferredDuringSchedulingIgnoredDuringExecution: []
  podAffinity:
    requiredDuringSchedulingIgnoredDuringExecution: []
    preferredDuringSchedulingIgnoredDuringExecution: []
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution: []
    preferredDuringSchedulingIgnoredDuringExecution: []