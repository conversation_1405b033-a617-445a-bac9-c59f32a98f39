# HK values for ccr-auth.

image:
  # In <PERSON><PERSON>'s pipeline, the variable will be replaced according to the component
  tag: ccr-auth.image.tag


config:
  ak: REPLACEME_ACCESSKEY
  sk: REPLACEME_SECRETKEY
  clusterId: cce-t1jqcsca
  region: bd
  endpoint:
    cce: cce.bd.baidubce.com
    iam: http://iam.bdbl.bce.baidu-int.com
    ccr: **************
    sts: http://sts.bdbl.bce.baidu-int.com:8586
  servicePassword: REPLACEME_SERVICE_PASSWORD
  mysqlConnection: ccr:ueOu^m4nGHL0iBeM@tcp(mysql57.rdsmre7wkryaffd.rds.bd.baidubce.com:3306)/ccr?parseTime=True&loc=Asia%2fShanghai