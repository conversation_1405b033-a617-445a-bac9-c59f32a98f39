annotations:
  artifacthub.io/changes: |
    - Add namespace field in the namespace scoped resource templates
apiVersion: v2
appVersion: 0.47.0
description: Ingress controller for Kubernetes using NGINX as a reverse proxy and load balancer
home: https://github.com/kubernetes/ingress-nginx
icon: https://upload.wikimedia.org/wikipedia/commons/thumb/c/c5/Nginx_logo.svg/500px-Nginx_logo.svg.png
keywords:
- ingress
- nginx
kubeVersion: '>=1.16.0-0'
maintainers:
- name: ChiefAlexander
name: ingress-nginx
sources:
- https://github.com/kubernetes/ingress-nginx
type: application
version: 3.34.1
