{{- if and .Values.rbac.create (not .Values.rbac.scope) -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
  name: {{ include "ingress-nginx.fullname" . }}
rules:
  - apiGroups:
      - ""
    resources:
      - configmaps
      - endpoints
      - nodes
      - pods
      - secrets
    verbs:
      - list
      - watch
{{- if and .Values.controller.scope.enabled .Values.controller.scope.namespace }}
  - apiGroups:
      - ""
    resources:
      - namespaces
    resourceNames:
      - "{{ .Values.controller.scope.namespace }}"
    verbs:
      - get
{{- end }}
  - apiGroups:
      - ""
    resources:
      - nodes
    verbs:
      - get
  - apiGroups:
      - ""
    resources:
      - services
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - extensions
      - "networking.k8s.io" # k8s 1.14+
    resources:
      - ingresses
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - patch
  - apiGroups:
      - extensions
      - "networking.k8s.io" # k8s 1.14+
    resources:
      - ingresses/status
    verbs:
      - update
  - apiGroups:
      - "networking.k8s.io" # k8s 1.14+
    resources:
      - ingressclasses
    verbs:
      - get
      - list
      - watch
{{- end }}
