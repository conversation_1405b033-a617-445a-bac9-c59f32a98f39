database:
  host: ************
  port: "3306"
  username: "ccr"
  password: "enterprise123"
  database: "harbor"
  sslMode: disable

uic:
  address: "http://iregistry-test.baidu-int.com"
  username: registry_server_token
  secret: Harbor12345

cas:
  endpoint: "https://itebeta.baidu.com"
  localEndpoint: "https://iregistry-test.baidu-int.com"

harbor:
  ip: *************
  domain: iregistry-test.baidu-int.com
  serviceName: harbor-registry
  adminPassword: LNs76dGKJPysSnp1
  csrfKey: G2XfutFpSSAGnnmGuCVKtkT2C6B9qnwQ

image:
  repository: registry.baidubce.com/ccr-image/ccr-iregistry
  # Overrides the image tag whose default is the chart appVersion.
  tag: "cd2e7b0-202204152235"

p2p:
  preheatServerAddr: http://************:8088
  authUser: ccr-image-seed
  authPassword: ccr-image-seed
  projectList: [eks_test,eks-dev]

resources:
  limits:
    cpu: "2"
    memory: "4Gi"
  requests:
    cpu: "1"
    memory: "2Gi"

service:
  type: LoadBalancer