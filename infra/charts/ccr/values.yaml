nameOverride: ""

harbor:
  expose:
    type: ingress
    tls:
      enabled: true
      certSource: none
    ingress:
      hosts:
        core: ccr.baidubce.com
      annotations:
        ingress.kubernetes.io/ssl-redirect: "true"
        ingress.kubernetes.io/proxy-body-size: "0"
        nginx.ingress.kubernetes.io/ssl-redirect: "true"
        nginx.ingress.kubernetes.io/proxy-body-size: "0"
        nginx.org/client-max-body-size: "0"
  externalURL: "https://ccr.baidubce.com"
  harborAdminPassword: ""
  persistence:
    enabled: false
    imageChartStorage:
      disableredirect: false
      type: bos
      bos:
        region: gz
        bucket: ccr-test
        endpoint: gz.bcebos.com
        accesskeyid: ""
        secretaccesskey: ""
        chunksize: "52428800"
        rootdirectory: /
        expiresin: 1200s
        userename: true
  
  imagePullSecrets:
  - name: ccr-registry-secret
  
  core:
    image:
      repository: goharbor/harbor-core
      tag: v2.2.3
    replicas: 2
    # private key, used for jwt token encrypt/decrypt
    # tls.crt is necessary
    secretName: ccr-cipher
    resources:
      requests:
        memory: 2Gi
        cpu: "1"
    podAnnotations:
      prometheus.io/scrape: 'true'
      prometheus.io/port: '8001'

  portal:
    image:
      repository: registry.baidubce.com/ccr-image/harbor-portal
      tag: v2.3.5
    replicas: 1
    resources:
      requests:
        memory: 100Mi
        cpu: 100m
  
  jobservice:
    image:
      repository: goharbor/harbor-jobservice
      tag: v2.2.3
    replicas: 2
    jobLoggers:
    - database
    resources:
      requests:
        memory: 2Gi
        cpu: "1"
    podAnnotations:
      prometheus.io/scrape: 'true'
      prometheus.io/port: '8001'
  
  registry:
    registry:
      image:
        repository: goharbor/registry-photon
        tag: v2.2.3
      resources:
        requests:
          memory: 2Gi
          cpu: "1"
    controller:
      image:
        repository: goharbor/harbor-registryctl
        tag: v2.2.3
      resources:
        requests:
          memory: 2Gi
          cpu: "1"
    replicas: 2
    relativeurls: true
    podAnnotations:
      prometheus.io/scrape: 'true'
      prometheus.io/port: '8001'
  
  chartmuseum:
    enabled: false
    image:
      repository: goharbor/chartmuseum-photon
      tag: v2.2.3
    replicas: 2
    resources:
      requests:
        memory: 2Gi
        cpu: "1"
    podAnnotations:
      prometheus.io/scrape: 'true'
      prometheus.io/port: '9999'
  
  trivy:
    image:
      repository: registry.baidubce.com/qce/trivy-onfly
      tag: v1-2023020812
    replicas: 2
    skipUpdate: true
    resources:
      requests:
        memory: 2Gi
        cpu: "1"
      limits:
        cpu: "4"
        memory: 8Gi
    podAnnotations:
      prometheus.io/scrape: 'true'
      prometheus.io/port: '8080'
  
  notary:
    enabled: false
  
  database:
    type: external
    external:
      host: ""
      port: "3306"
      username: "ccr"
      password: "bdccr123"
      coreDatabase: "harbor"
    
  redis:
    type: external
    external:
      addr: "redis-node-0.redis-headless:26379,redis-node-1.redis-headless:26379,redis-node-2.redis-headless:26379"
      # 与下面的redis密码保持一致
      password: "ccrpassword"
      sentinelMasterSet: "ccr"
  
  exporter:
    replicas: 1
    resources:
      requests:
        memory: 512Mi
        cpu: "500m"
    image:
      repository: goharbor/harbor-exporter
      tag: v2.2.3
    podAnnotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "8001"

  metrics:
    enabled: true
    core:
      path: /metrics
      port: 8001
    registry:
      path: /metrics
      port: 8001
    exporter:
      path: /metrics
      port: 8001
    jobservice:
      path: /metrics
      port: 8001

ingress-nginx:
  fullnameOverride: "ingress"
  imagePullSecrets:
  - name: ccr-registry-secret
  controller:
    name: controller
    image:
      registry: registry.baidubce.com
      image: k8s-mirror/ingress-nginx
      tag: "v0.47.0"
      digest: ""
    podAnnotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "10254"
      restart.version.flag: "1"

    extraVolumes:
    - name: lua-ingress
      configMap:
        name: ccr-lua-ingress

    extraVolumeMounts:
    - name: lua-ingress
      mountPath: /etc/nginx/lua/lua_ingress.lua
      subPath: lua_ingress.lua

    config:
      proxy-body-size: "0"
      whitelist-source-range: ""
      http-snippet: |
        map "$request_method:$uri" $v2uri {
          default $uri;
          "~^(HEAD|PUT|GET):/v2/(?<repo>.*)/manifests/(?<ref>.*)" /addon/v1/manifests/$repo@$ref;
          "~^HEAD:/v2/(?<repo>.*)/blobs/(?<ref>.*)" /addon/v1/blobs/$repo@$ref;
        }

    ingressClass: ccr
    ingressClassResource:
      enabled: true
    scope:
      enabled: true
    
    kind: Deployment
    replicaCount: 3
  
    resources:
      requests:
        cpu: "1"
        memory: "2Gi"
    service:
      externalTrafficPolicy: "Local"
      annotations:
        service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
        service.beta.kubernetes.io/cce-load-balancer-lb-name: ""
    admissionWebhooks:
      enabled: false
    metrics:
      enabled: true

redis:
  global:
    imagePullSecrets:
    - ccr-registry-secret
  fullnameOverride: redis
  commonConfiguration: |-
    appendonly yes
    save ""

  image:
    registry: docker.io
    repository: bitnami/redis
    tag: 6.2.4-debian-10-r14
  auth:
    sentinel: false
    password: "ccrpassword"
  replica:
    replicaCount: 3
    resources:
      limits: {}
      requests: {}
    podAntiAffinityPreset: hard
    persistence:
      enabled: false
  sentinel:
    enabled: true
    image:
      registry: docker.io
      repository: bitnami/redis-sentinel
      tag: 6.2.4-debian-10-r14
    masterSet: ccr
    downAfterMilliseconds: 30000
    failoverTimeout: 15000
    resources:
      limits: {}
      requests: {}
  serviceAccount:
    create: false
    name: ""
  metrics:
    enabled: true
    image:
      registry: docker.io
      repository: bitnami/redis-exporter
      tag: 1.24.0-debian-10-r9
    resources:
      limits: {}
      requests: {}
    sentinel:
      enabled: true
      image:
        registry: docker.io
        repository: bitnami/redis-sentinel-exporter
        tag: 1.7.1-debian-10-r161
      resources:
        limits: {}
        requests: {}

dragonfly:
  global:
    # -- Image pull secrets.
    imagePullSecrets:
      - name: ccr-registry-secret

  seedPeer:
    enable: false

  dfdaemon:
    enable: false

  manager:
    enable: false

  scheduler:
    enable: true
    replicas: 3
    generateIndex:
     - 0
     - 1
     - 2
    image: registry.baidubce.com/ccr-image/dragonfly-scheduler
    tag: v2.1.0-0.2
    initContainer:
      image: registry.baidubce.com/cce-plugin-pro/busybox
      tag: latest
    config:
      server:
        # -- Advertise ip.
        advertiseHostSuffix: vpc.cnc.xx.baidubce.com
        advertisePort: 443
      seedPeer:
        enable: false
      console: true
      verbose: true
      host:
        idc: ccr-xxxxxxxx
      manager:
        schedulerClusterID: 9999
    metrics:
      # -- Enable scheduler metrics.
      enable: true
      enableHost: true
      service:
        annotations:
          prometheus.io/scrape: "true"
    resources:
      requests:
        cpu: "2"
        memory: "4Gi"
      limits:
        cpu: "4"
        memory: "8Gi"

  externalManager:
    # -- External manager hostname.
    host: dragonfly-manager.ccr-system.svc.cluster.local
    # -- External REST service port.
    restPort: 8080
    # -- External GRPC service port.
    grpcPort: 65003

  database:
    enable: false
    type: mysql

  redis:
    enable: false

  externalRedis:
    # -- External redis server addresses
    addrs:
      - "redis-node-0.redis-headless:26379"
      - "redis-node-1.redis-headless:26379"
      - "redis-node-2.redis-headless:26379"
    username: ""
    # 与下面的redis密码保持一致
    password: "ccrpassword"
    masterName: "ccr"
    # Server cache DB name.
    db: 6
    # Server broker DB name.
    brokerDB: 7
    # Server backend DB name.
    backendDB: 8

ccr:
  imagePullSecret: "********************************************************************************************************************************************************************************************************"
  jwtTlsCrt: ""
  jwtTlsKey: ""
  region: gztest
  domainCert:
    key: ""
    cert: ""
    # common name here, may be empty
    name: ""
  privateDomain:
    name: "xxxx-vpc.cnc.xx.baidubce.com"
    annotations: {}
  publicDomain:
    name: "xxxx-pub.cnc.xx.baidubce.com"
    annotations: {}
  authDomain:
    name: "ccr-auth.xx.baidubce.com"
  harborAddon:
    replicas: 2
    containerPort: "9203"
    image:
      repository: "harbor-addon"
      tag: "0.0.1"
      pullPolicy: "IfNotPresent"
    resources:
      limits: {}
      requests: {}
    nodeSelector: {}
    tolerations: []
    podAnnotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "9203"
    # The XSRF key. Will be generated automatically if it isn't specified
    secretName: ccr-cipher