** Please be patient while the chart is being deployed **

{{- if .Values.diagnosticMode.enabled }}
The chart has been deployed in diagnostic mode. All probes have been disabled and the command has been overwritten with:

  command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 4 }}
  args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 4 }}

Get the list of pods by executing:

  kubectl get pods --namespace {{ .Release.Namespace }} -l app.kubernetes.io/instance={{ .Release.Name }}

Access the pod you want to debug by executing

  kubectl exec --namespace {{ .Release.Namespace }} -ti <NAME OF THE POD> -- bash

In order to replicate the container startup scripts execute this command:

For Redis:

    /opt/bitnami/scripts/redis/entrypoint.sh /opt/bitnami/scripts/redis/run.sh

{{- if .Values.sentinel.enabled }}

For Redis Sentinel:

    /opt/bitnami/scripts/redis-sentinel/entrypoint.sh /opt/bitnami/scripts/redis-sentinel/run.sh

{{- end }}
{{- else }}

{{- if contains .Values.master.service.type "LoadBalancer" }}
{{- if not .Values.auth.enabled }}
{{ if and (not .Values.networkPolicy.enabled) (.Values.networkPolicy.allowExternal) }}

-------------------------------------------------------------------------------
 WARNING

    By specifying "master.service.type=LoadBalancer" and "auth.enabled=false" you have
    most likely exposed the Redis(TM) service externally without any authentication
    mechanism.

    For security reasons, we strongly suggest that you switch to "ClusterIP" or
    "NodePort". As alternative, you can also switch to "auth.enabled=true"
    providing a valid password on "password" parameter.

-------------------------------------------------------------------------------
{{- end }}
{{- end }}
{{- end }}

{{- if eq .Values.architecture "replication" }}
{{- if .Values.sentinel.enabled }}

Redis(TM) can be accessed via port {{ .Values.sentinel.service.port }} on the following DNS name from within your cluster:

    {{ template "common.names.fullname" . }}.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }} for read only operations

For read/write operations, first access the Redis(TM) Sentinel cluster, which is available in port {{ .Values.sentinel.service.sentinelPort }} using the same domain name above.

{{- else }}

Redis(TM) can be accessed on the following DNS names from within your cluster:

    {{ printf "%s-master.%s.svc.%s" (include "common.names.fullname" .) .Release.Namespace .Values.clusterDomain }} for read/write operations (port {{ .Values.master.service.port }})
    {{ printf "%s-replicas.%s.svc.%s" (include "common.names.fullname" .) .Release.Namespace .Values.clusterDomain }} for read-only operations (port {{ .Values.replica.service.port }})

{{- end }}
{{- else }}

Redis(TM) can be accessed via port {{ .Values.master.service.port }} on the following DNS name from within your cluster:

    {{ template "common.names.fullname" . }}-master.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }}

{{- end }}

{{ if .Values.auth.enabled }}

To get your password run:

    export REDIS_PASSWORD=$(kubectl get secret --namespace {{ .Release.Namespace }} {{ template "redis.secretName" . }} -o jsonpath="{.data.redis-password}" | base64 --decode)

{{- end }}

To connect to your Redis(TM) server:

1. Run a Redis(TM) pod that you can use as a client:

   kubectl run --namespace {{ .Release.Namespace }} redis-client --restart='Never' {{ if .Values.auth.enabled }} --env REDIS_PASSWORD=$REDIS_PASSWORD {{ end }} --image {{ template "redis.image" . }} --command -- sleep infinity

{{- if .Values.tls.enabled }}

   Copy your TLS certificates to the pod:

   kubectl cp --namespace {{ .Release.Namespace }} /path/to/client.cert redis-client:/tmp/client.cert
   kubectl cp --namespace {{ .Release.Namespace }} /path/to/client.key redis-client:/tmp/client.key
   kubectl cp --namespace {{ .Release.Namespace }} /path/to/CA.cert redis-client:/tmp/CA.cert

{{- end }}

   Use the following command to attach to the pod:

   kubectl exec --tty -i redis-client \
   {{- if and (.Values.networkPolicy.enabled) (not .Values.networkPolicy.allowExternal) }}--labels="{{ template "common.names.fullname" . }}-client=true" \{{- end }}
   --namespace {{ .Release.Namespace }} -- bash

2. Connect using the Redis(TM) CLI:

{{- if eq .Values.architecture "replication" }}
   {{- if .Values.sentinel.enabled }}
   redis-cli -h {{ template "common.names.fullname" . }} -p {{ .Values.sentinel.service.port }}{{ if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }} # Read only operations
   redis-cli -h {{ template "common.names.fullname" . }} -p {{ .Values.sentinel.service.sentinelPort }}{{ if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }} # Sentinel access
   {{- else }}
   redis-cli -h {{ printf "%s-master" (include "common.names.fullname" .) }}{{ if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }}
   redis-cli -h {{ printf "%s-replicas" (include "common.names.fullname" .) }}{{ if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }}
   {{- end }}
{{- else }}
   redis-cli -h {{ template "common.names.fullname" . }}-master{{ if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }}
{{- end }}

{{- if and (.Values.networkPolicy.enabled) (not .Values.networkPolicy.allowExternal) }}

Note: Since NetworkPolicy is enabled, only pods with label {{ template "common.names.fullname" . }}-client=true" will be able to connect to redis.

{{- else }}

To connect to your database from outside the cluster execute the following commands:

{{- if and (eq .Values.architecture "replication") .Values.sentinel.enabled }}
{{- if contains "NodePort" .Values.sentinel.service.type }}

    export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
    export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ template "common.names.fullname" . }})
    redis-cli -h $NODE_IP -p $NODE_PORT {{- if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }}

{{- else if contains "LoadBalancer" .Values.sentinel.service.type }}

  NOTE: It may take a few minutes for the LoadBalancer IP to be available.
        Watch the status with: 'kubectl get svc --namespace {{ .Release.Namespace }} -w {{ template "common.names.fullname" . }}'

    export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ template "common.names.fullname" . }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
    redis-cli -h $SERVICE_IP -p {{ .Values.sentinel.service.port }} {{- if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }}

{{- else if contains "ClusterIP" .Values.sentinel.service.type }}

    kubectl port-forward --namespace {{ .Release.Namespace }} svc/{{ template "common.names.fullname" . }} {{ .Values.sentinel.service.port }}:{{ .Values.sentinel.service.port }} &
    redis-cli -h 127.0.0.1 -p {{ .Values.sentinel.service.port }} {{- if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }}

{{- end }}
{{- else }}
{{- if contains "NodePort" .Values.master.service.type }}

    export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
    export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ printf "%s-master" (include "common.names.fullname" .) }})
    redis-cli -h $NODE_IP -p $NODE_PORT {{- if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }}

{{- else if contains "LoadBalancer" .Values.master.service.type }}

  NOTE: It may take a few minutes for the LoadBalancer IP to be available.
        Watch the status with: 'kubectl get svc --namespace {{ .Release.Namespace }} -w {{ template "common.names.fullname" . }}'

    export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ printf "%s-master" (include "common.names.fullname" .) }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
    redis-cli -h $SERVICE_IP -p {{ .Values.master.service.port }} {{- if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }}

{{- else if contains "ClusterIP" .Values.master.service.type }}

    kubectl port-forward --namespace {{ .Release.Namespace }} svc/{{ printf "%s-master" (include "common.names.fullname" .) }} {{ .Values.master.service.port }}:{{ .Values.master.service.port }} &
    redis-cli -h 127.0.0.1 -p {{ .Values.master.service.port }} {{- if .Values.auth.enabled }} -a $REDIS_PASSWORD{{ end }}{{ if .Values.tls.enabled }} --tls --cert /tmp/client.cert --key /tmp/client.key --cacert /tmp/CA.cert{{ end }}

{{- end }}
{{- end }}

{{- end }}
{{- end }}
{{- include "redis.checkRollingTags" . }}
{{- include "redis.validateValues" . }}
{{- $requiredPassword := list -}}
{{- $secretName := include "redis.secretName" . -}}
{{- $secretPasswordKey := include "redis.secretPasswordKey" . -}}
{{- if and .Values.auth.enabled (not .Values.auth.existingSecret) -}}
  {{- $requiredRedisPassword := dict "valueKey" "auth.password" "secret" $secretName "field" $secretPasswordKey -}}
  {{- $requiredPassword = append $requiredPassword $requiredRedisPassword -}}
{{- end -}}
{{- $requiredRedisPasswordErrors := include "common.validations.values.multiple.empty" (dict "required" $requiredPassword "context" $) -}}
{{- include "common.errors.upgrade.passwords.empty" (dict "validationErrors" (list $requiredRedisPasswordErrors) "context" $) -}}
