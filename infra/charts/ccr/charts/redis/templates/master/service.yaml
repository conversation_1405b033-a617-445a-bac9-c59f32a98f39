{{- if not .Values.sentinel.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-master" (include "common.names.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: master
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if or .Values.master.service.annotations .Values.commonAnnotations }}
  annotations:
    {{- if .Values.master.service.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.master.service.annotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  type: {{ .Values.master.service.type }}
  {{ if eq .Values.master.service.type "LoadBalancer" }}
  externalTrafficPolicy: {{ .Values.master.service.externalTrafficPolicy }}
  {{- end }}
  {{- if and (eq .Values.master.service.type "LoadBalancer") .Values.master.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.master.service.loadBalancerIP }}
  {{- end }}
  {{- if and (eq .Values.master.service.type "LoadBalancer") .Values.master.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{- toYaml .Values.master.service.loadBalancerSourceRanges | nindent 4 }}
  {{- end }}
  {{- if and (eq .Values.master.service.type "ClusterIP") .Values.master.service.clusterIP }}
  clusterIP: {{ .Values.master.service.clusterIP }}
  {{- end }}
  ports:
    - name: tcp-redis
      port: {{ .Values.master.service.port }}
      targetPort: redis
      {{- if and (or (eq .Values.master.service.type "NodePort") (eq .Values.master.service.type "LoadBalancer")) .Values.master.service.nodePort }}
      nodePort: {{ .Values.master.service.nodePort }}
      {{- else if eq .Values.master.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: master
{{- end }}
