{{- if and .Values.sentinel.enabled .Values.metrics.sentinel.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-sentinel-metrics" (include "common.names.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: sentinel-metrics
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if or .Values.metrics.sentinel.service.annotations .Values.commonAnnotations }}
  annotations:
    {{- if .Values.metrics.sentinel.service.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.sentinel.service.annotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  type: {{ .Values.metrics.sentinel.service.type }}
  {{- if eq .Values.metrics.sentinel.service.type "LoadBalancer" }}
  externalTrafficPolicy: {{ .Values.metrics.sentinel.service.externalTrafficPolicy }}
  {{- end }}
  {{- if and (eq .Values.metrics.sentinel.service.type "LoadBalancer") .Values.metrics.sentinel.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.metrics.sentinel.service.loadBalancerIP }}
  {{- end }}
  {{- if and (eq .Values.metrics.sentinel.service.type "LoadBalancer") .Values.metrics.sentinel.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{- toYaml .Values.metrics.sentinel.service.loadBalancerSourceRanges | nindent 4 }}
  {{- end }}
  ports:
    - name: tcp-metrics
      port: {{ .Values.metrics.sentinel.service.port }}
      protocol: TCP
      targetPort: sentinelmetrics
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
{{- end }}
