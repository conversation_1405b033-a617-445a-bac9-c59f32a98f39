apiVersion: v2
name: dragonfly
description: Dragonfly is an intelligent P2P based image and file distribution system
icon: https://raw.githubusercontent.com/dragonflyoss/Dragonfly2/main/docs/images/logo/dragonfly.svg
type: application
version: 1.1.4
appVersion: 2.1.0
keywords:
  - dragonfly
  - d7y
  - P2P
  - image

maintainers:
  - name: jim3ma
    email: <EMAIL>
  - name: gaius-qi
    email: <EMAIL>
  - name: yxxhero
    email: <EMAIL>

home: https://d7y.io/

sources:
  - https://github.com/dragonflyoss/Dragonfly2

annotations:
  artifacthub.io/changes: |
    - Fix bug cause 'plugins."io.containerd.grpc.v1.cri".registry' duplicated in dfdaemon init script(containerd part)

  artifacthub.io/links: |
    - name: Chart Source
      url: https://github.com/dragonflyoss/helm-charts
    - name: Source
      url: https://github.com/dragonflyoss/Dragonfly2
  artifacthub.io/images: |
    - name: manager
      image: dragonflyoss/manager:v2.1.0
    - name: dfdaemon
      image: dragonflyoss/dfdaemon:v2.1.0
    - name: scheduler
      image: dragonflyoss/scheduler:v2.1.0
    - name: trainer
      image: dragonflyoss/scheduler:v2.1.0
    - name: triton
      image: nvcr.io/nvidia/tritonserver:23.06-py3

dependencies:
  - name: mysql
    version: 9.4.6
    repository: https://charts.bitnami.com/bitnami
    condition: mysql.enable
  - name: redis
    version: 17.4.3
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enable
  - name: jaeger
    version: 0.66.1
    repository: https://jaegertracing.github.io/helm-charts
    condition: jaeger.enable
