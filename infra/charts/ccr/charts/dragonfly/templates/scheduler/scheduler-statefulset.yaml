{{- if .Values.scheduler.enable }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ template "dragonfly.scheduler.fullname" . }}
  labels:
    app: {{ template "dragonfly.fullname" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.scheduler.name }}
  annotations:
    checksum/config: {{ include (print $.Template.BasePath "/scheduler/scheduler-configmap.yaml") . | sha256sum }}
  {{- if .Values.scheduler.statefulsetAnnotations }}
{{ toYaml .Values.scheduler.statefulsetAnnotations | indent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.scheduler.replicas }}
  selector:
    matchLabels:
      app: {{ template "dragonfly.fullname" . }}
      release: {{ .Release.Name }}
      component: {{ .Values.scheduler.name }}
  serviceName: scheduler
  template:
    metadata:
      labels:
        app: {{ template "dragonfly.fullname" . }}
        release: {{ .Release.Name }}
        component: {{ .Values.scheduler.name }}
        {{- if .Values.scheduler.podLabels }}
{{ toYaml .Values.scheduler.podLabels | indent 8 }}
        {{- end }}
      {{- if .Values.scheduler.podAnnotations }}
      annotations:
{{ toYaml .Values.scheduler.podAnnotations | indent 8 }}
      {{- end }}
    spec:
      {{- if .Values.scheduler.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.scheduler.nodeSelector | indent 8 }}
      {{- end }}
      {{- if .Values.scheduler.tolerations }}
      tolerations:
{{ toYaml .Values.scheduler.tolerations | indent 8 }}
      {{- end }}
      {{- if .Values.scheduler.affinity }}
      affinity:
{{ toYaml .Values.scheduler.affinity | indent 8 }}
      {{- end }}
      {{- if quote .Values.scheduler.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.scheduler.terminationGracePeriodSeconds }}
      {{- end }}
      {{- if and (.Capabilities.APIVersions.Has "scheduling.k8s.io/v1beta1") (.Values.scheduler.priorityClassName) }}
      priorityClassName: {{ .Values.scheduler.priorityClassName }}
      {{- end }}
      {{- with .Values.scheduler.pullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.scheduler.hostAliases }}
      hostAliases:
{{ toYaml .Values.scheduler.hostAliases | indent 8 }}
      {{- end }}
      initContainers:
      - name: wait-for-manager
        image: {{ .Values.scheduler.initContainer.image }}:{{ .Values.scheduler.initContainer.tag  }}
        imagePullPolicy: {{ .Values.scheduler.initContainer.pullPolicy }}
        {{- if .Values.manager.enable }}
        command: ['sh', '-c', 'until nslookup {{ template "dragonfly.manager.fullname" . }}.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }} && nc -vz {{ template "dragonfly.manager.fullname" . }}.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }} {{ .Values.manager.restPort }}; do echo waiting for manager; sleep 2; done;']
        {{- else }}
        command: ['sh', '-c', 'until nslookup {{ .Values.externalManager.host }} && nc -vz {{ .Values.externalManager.host }} {{ .Values.externalManager.restPort }}; do echo waiting for external manager; sleep 2; done;']
        {{- end }}
      containers:
      - name: scheduler
        image: "{{ .Values.scheduler.image }}:{{ .Values.scheduler.tag }}"
        imagePullPolicy: {{ .Values.scheduler.pullPolicy | quote }}
        resources:
{{ toYaml .Values.scheduler.resources | indent 12 }}
        ports:
        - containerPort: {{ .Values.scheduler.containerPort }}
          protocol: TCP
        {{- if .Values.scheduler.metrics.enable }}
        - containerPort: 8000
          protocol: TCP
        {{- end }}
        volumeMounts:
        - name: config
          mountPath: "/etc/dragonfly"
        {{- if .Values.scheduler.extraVolumeMounts }}
        {{- toYaml .Values.scheduler.extraVolumeMounts | nindent 8 }}
        {{- end }}
        readinessProbe:
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:{{ .Values.scheduler.containerPort }}"]
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:{{ .Values.scheduler.containerPort }}"]
          initialDelaySeconds: 15
          periodSeconds: 10
      volumes:
      - name: config
        configMap:
          name: {{ template "dragonfly.scheduler.fullname" . }}
          items:
          - key: scheduler.yaml
            path: scheduler.yaml
      {{- if .Values.scheduler.extraVolumes }}
      {{- toYaml .Values.scheduler.extraVolumes | nindent 6 }}
      {{- end }}
{{- end }}
