{{- if and .Values.seedPeer.metrics.enable .Values.seedPeer.metrics.serviceMonitor.enable }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "dragonfly.seedPeer.fullname" . }}
  labels:
    app: {{ template "dragonfly.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.seedPeer.name }}
    {{- if .Values.seedPeer.metrics.serviceMonitor.additionalLabels }}
{{ toYaml .Values.seedPeer.metrics.serviceMonitor.additionalLabels | indent 4 }}
    {{- end }}
spec:
  endpoints:
  - port: http-metrics
    {{- if .Values.seedPeer.metrics.serviceMonitor.interval }}
    interval: {{ .Values.seedPeer.metrics.serviceMonitor.interval }}
    {{- end }}
    {{- if .Values.seedPeer.metrics.serviceMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.seedPeer.metrics.serviceMonitor.scrapeTimeout }}
    {{- end }}
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
  selector:
    matchLabels:
      app: {{ template "dragonfly.name" . }}
      release: {{ .Release.Name }}
      component: {{ .Values.seedPeer.name }}-metrics
{{- end }}
