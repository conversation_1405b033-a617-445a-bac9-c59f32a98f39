{{- if and .Values.seedPeer.metrics.enable .Values.seedPeer.metrics.serviceMonitor.enable }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "dragonfly.seedPeer.fullname" . }}-metrics
  labels:
    app: {{ template "dragonfly.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.seedPeer.name }}-metrics
{{- if .Values.seedPeer.metrics.service.labels }}
{{ toYaml .Values.metrics.service.labels | indent 4 }}
{{- end }}
{{- if .Values.seedPeer.metrics.service.annotations }}
  annotations:
{{ toYaml .Values.seedPeer.metrics.service.annotations | indent 4 }}
{{- end }}
spec:
  type: {{ .Values.seedPeer.metrics.service.type }}
  ports:
  - port: 8000
    name: http-metrics
    targetPort: 8000
    protocol: TCP
  selector:
    app: {{ template "dragonfly.fullname" . }}
    component: {{ .Values.seedPeer.name }}
    release: {{ .Release.Name }}
{{- end }}
