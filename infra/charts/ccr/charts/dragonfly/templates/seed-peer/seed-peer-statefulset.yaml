{{- if .Values.seedPeer.enable }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: {{ template "dragonfly.fullname" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.seedPeer.name }}
  name: {{ template "dragonfly.seedPeer.fullname" . }}
  annotations:
    checksum/config: {{ include (print $.Template.BasePath "/seed-peer/seed-peer-configmap.yaml") . | sha256sum }}
  {{- if .Values.seedPeer.statefulsetAnnotations }}
{{ toYaml .Values.seedPeer.statefulsetAnnotations | indent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.seedPeer.replicas }}
  selector:
    matchLabels:
      app: {{ template "dragonfly.fullname" . }}
      component: {{ .Values.seedPeer.name }}
      release: {{ .Release.Name }}
  serviceName: seed-peer
  template:
    metadata:
      labels:
        app: {{ template "dragonfly.fullname" . }}
        component: {{ .Values.seedPeer.name }}
        release: {{ .Release.Name }}
        {{- if .Values.seedPeer.podLabels }}
{{ toYaml .Values.seedPeer.podLabels | indent 8 }}
        {{- end }}
      {{- if .Values.seedPeer.podAnnotations }}
      annotations:
{{ toYaml .Values.seedPeer.podAnnotations | indent 8 }}
      {{- end }}
    spec:
      {{- if .Values.seedPeer.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.seedPeer.nodeSelector | indent 8 }}
      {{- end }}
      {{- if .Values.seedPeer.tolerations }}
      tolerations:
{{ toYaml .Values.seedPeer.tolerations | indent 8 }}
      {{- end }}
      {{- if .Values.seedPeer.affinity }}
      affinity:
{{ toYaml .Values.seedPeer.affinity | indent 8 }}
      {{- end }}
      {{- if quote .Values.seedPeer.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.seedPeer.terminationGracePeriodSeconds }}
      {{- end }}
      {{- if and (.Capabilities.APIVersions.Has "scheduling.k8s.io/v1beta1") (.Values.seedPeer.priorityClassName) }}
      priorityClassName: {{ .Values.seedPeer.priorityClassName }}
      {{- end }}
      {{- with .Values.seedPeer.pullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.seedPeer.hostAliases }}
      hostAliases:
{{ toYaml .Values.seedPeer.hostAliases | indent 8 }}
      {{- end }}
      initContainers:
      - name: wait-for-manager
        image: {{ .Values.seedPeer.initContainer.image }}:{{ .Values.seedPeer.initContainer.tag  }}
        imagePullPolicy: {{ .Values.seedPeer.initContainer.pullPolicy }}
        {{- if .Values.manager.enable }}
        command: ['sh', '-c', 'until nslookup {{ template "dragonfly.manager.fullname" . }}.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }} && nc -vz {{ template "dragonfly.manager.fullname" . }}.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }} {{ .Values.manager.restPort }}; do echo waiting for manager; sleep 2; done;']
        {{- else }}
        command: ['sh', '-c', 'until nslookup {{ .Values.externalManager.host }} && nc -vz {{ .Values.externalManager.host }} {{ .Values.externalManager.restPort }}; do echo waiting for external manager; sleep 2; done;']
        {{- end }}
      containers:
      - name: seed-peer
        image: "{{ .Values.seedPeer.image }}:{{ .Values.seedPeer.tag }}"
        imagePullPolicy: {{ .Values.seedPeer.pullPolicy | quote }}
        resources:
{{ toYaml .Values.seedPeer.resources | indent 12 }}
        ports:
        - containerPort: {{ .Values.seedPeer.config.download.peerGRPC.tcpListen.port }}
          protocol: TCP
        - containerPort: {{ .Values.seedPeer.config.upload.tcpListen.port }}
          protocol: TCP
        {{- if .Values.seedPeer.config.objectStorage.enable }}
        - containerPort: {{ .Values.seedPeer.config.objectStorage.tcpListen.port }}
          protocol: TCP
        {{- end }}
        {{- if .Values.seedPeer.metrics.enable }}
        - containerPort: 8000
          protocol: TCP
        {{- end }}
        volumeMounts:
        - name: config
          mountPath: "/etc/dragonfly"
        - name: storage
          mountPath: {{ .Values.seedPeer.config.dataDir }}
        {{- if .Values.seedPeer.extraVolumeMounts }}
        {{- toYaml .Values.seedPeer.extraVolumeMounts | nindent 8 }}
        {{- end }}
        readinessProbe:
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:{{ .Values.seedPeer.config.download.peerGRPC.tcpListen.port }}"]
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          exec:
            command: ["/bin/grpc_health_probe", "-addr=:{{ .Values.seedPeer.config.download.peerGRPC.tcpListen.port }}"]
          initialDelaySeconds: 15
          periodSeconds: 10
      volumes:
      - name: config
        configMap:
          name: {{ template "dragonfly.seedPeer.fullname" $ }}
          items:
          - key: dfget.yaml
            path: dfget.yaml
      {{- if not (.Values.seedPeer.persistence.enable) }}
      - name: storage
        emptyDir: {}
      {{- end }}
      {{- if .Values.seedPeer.extraVolumes }}
      {{- toYaml .Values.seedPeer.extraVolumes | nindent 6 }}
      {{- end }}
  {{- if .Values.seedPeer.persistence.enable }}
  volumeClaimTemplates:
    - metadata:
        name: storage
        {{- range $key, $value := .Values.seedPeer.persistence.annotations }}
          {{ $key }}: {{ $value }}
        {{- end }}
      spec:
        accessModes:
          {{- range .Values.seedPeer.persistence.accessModes }}
          - {{ . | quote }}
          {{- end }}
        resources:
          requests:
            storage: {{ .Values.seedPeer.persistence.size | quote }}
      {{- if .Values.seedPeer.persistence.storageClass }}
      {{- if (eq "-" .Values.seedPeer.persistence.storageClass) }}
        storageClassName: ""
      {{- else }}
        storageClassName: "{{ .Values.seedPeer.persistence.storageClass }}"
      {{- end }}
      {{- end }}
  {{- end }}
{{- end }}
