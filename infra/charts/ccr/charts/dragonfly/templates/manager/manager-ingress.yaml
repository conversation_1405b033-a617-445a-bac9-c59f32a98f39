{{- if .Values.manager.ingress.enable -}}
{{- if semverCompare ">=1.19-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1
{{- else if semverCompare ">=1.14-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1beta1
{{- else -}}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ template "dragonfly.manager.fullname" . }}
  labels:
    app: {{ template "dragonfly.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.manager.name }}
{{- if .Values.manager.ingress.annotations }}
  annotations:
{{ toYaml .Values.manager.ingress.annotations | indent 4 }}
{{- end }}
spec:
  {{- if and .Values.manager.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.manager.ingress.className }}
  {{- end }}
{{- if .Values.manager.ingress.tls }}
  tls:
  {{- range .Values.manager.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . }}
      {{- end }}
      secretName: {{ .secretName }}
  {{- end }}
{{- end }}
  rules:
  {{- range .Values.manager.ingress.hosts }}
    - host: {{ . }}
      http:
        paths:
          - path: {{ $.Values.manager.ingress.path }}
            {{- if and $.Values.manager.ingress.pathType (semverCompare ">=1.18-0" $.Capabilities.KubeVersion.GitVersion) }}
            pathType: {{ default "ImplementationSpecific" $.Values.manager.ingress.pathType }}
            {{- end }}
            backend:
              {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
              service:
                name: {{ template "dragonfly.manager.fullname" $ }}
                port:
                  number: {{ $.Values.manager.restPort }}
              {{- else }}
              serviceName: {{ template "dragonfly.manager.fullname" $ }}
              servicePort: {{ $.Values.manager.restPort }}
              {{- end }}
  {{- end }}
{{- end }}
