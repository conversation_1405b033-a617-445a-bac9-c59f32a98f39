{{- if .Values.chartmuseum.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ template "harbor.chartmuseum" . }}"
  labels:
{{ include "harbor.labels" . | indent 4 }}
data:
  PORT: "{{ template "harbor.chartmuseum.containerPort" . }}"
{{- if .Values.internalTLS.enabled }}
  TLS_CERT: "/etc/harbor/ssl/chartmuseum/tls.crt"
  TLS_KEY: "/etc/harbor/ssl/chartmuseum/tls.key"
{{- end }}
  {{- if eq "redis" (include "harbor.redis.scheme" .) }}
  CACHE: "redis"
  {{- else }}
  CACHE: "redis_sentinel"
  CACHE_REDIS_MASTERNAME: "{{ template "harbor.redis.masterSet" . }}"
  {{- end }}
  CACHE_REDIS_ADDR: "{{ template "harbor.redis.addr" . }}"
  CACHE_REDIS_DB: "{{ template "harbor.redis.dbForChartmuseum" . }}"
  BASIC_AUTH_USER: "chart_controller"
{{- if .Values.chartmuseum.absoluteUrl }}
  CHART_URL: {{ .Values.externalURL }}/chartrepo
{{- end }}
  DEPTH: "1"
{{- if eq .Values.logLevel "debug" }}
  DEBUG: "true"
{{- else }}
  DEBUG: "false"
{{- end }}
  LOG_JSON: "true"
  DISABLE_METRICS: "false"
  DISABLE_API: "false"
  DISABLE_STATEFILES: "false"
  ALLOW_OVERWRITE: "true"
  AUTH_ANONYMOUS_GET: "false"
  CONTEXT_PATH: ""
  INDEX_LIMIT: "0"
  MAX_STORAGE_OBJECTS: "0"
  MAX_UPLOAD_SIZE: "********"
  CHART_POST_FORM_FIELD_NAME: "chart"
  PROV_POST_FORM_FIELD_NAME: "prov"
{{- $storage := .Values.persistence.imageChartStorage }}
{{- $storageType := $storage.type }}
{{- if eq $storageType "filesystem" }}
  STORAGE: "local"
  STORAGE_LOCAL_ROOTDIR: "/chart_storage"
{{- else if eq $storageType "azure" }}
  STORAGE: "microsoft"
  STORAGE_MICROSOFT_CONTAINER: {{ $storage.azure.container }}
  AZURE_STORAGE_ACCOUNT: {{ $storage.azure.accountname }}
  AZURE_BASE_URL: {{ $storage.azure.realm }}
  STORAGE_MICROSOFT_PREFIX: "/azure/harbor/charts"
{{- else if eq $storageType "gcs" }}
  STORAGE: "google"
  STORAGE_GOOGLE_BUCKET: {{ $storage.gcs.bucket }}
  GOOGLE_APPLICATION_CREDENTIALS: /etc/chartmuseum/gcs-key.json
  {{- if $storage.gcs.rootdirectory }}
  STORAGE_GOOGLE_PREFIX: {{ $storage.gcs.rootdirectory }}
  {{- end }}
{{- else if eq $storageType "s3" }}
  STORAGE: "amazon"
  STORAGE_AMAZON_BUCKET: {{ $storage.s3.bucket }}
  {{- if $storage.s3.rootdirectory }}
  STORAGE_AMAZON_PREFIX: {{ $storage.s3.rootdirectory }}
  {{- end }}
  STORAGE_AMAZON_REGION: {{ $storage.s3.region }}
  {{- if $storage.s3.regionendpoint }}
  STORAGE_AMAZON_ENDPOINT: {{ $storage.s3.regionendpoint }}
  {{- end }}
  {{- if $storage.s3.accesskey }}
  AWS_ACCESS_KEY_ID: {{ $storage.s3.accesskey }}
  {{- end }}
  {{- if $storage.s3.keyid }}
  STORAGE_AMAZON_SSE: aws:kms
  {{- end }}
{{- else if eq $storageType "swift" }}
  STORAGE: "openstack"
  STORAGE_OPENSTACK_CONTAINER: {{ $storage.swift.container }}
  {{- if $storage.swift.prefix }}
  STORAGE_OPENSTACK_PREFIX: {{ $storage.swift.prefix }}
  {{- end }}
  {{- if $storage.swift.region }}
  STORAGE_OPENSTACK_REGION: {{ $storage.swift.region }}
  {{- end }}
  OS_AUTH_URL: {{ $storage.swift.authurl }}
  OS_USERNAME: {{ $storage.swift.username }}
  {{- if $storage.swift.tenantid }}
  OS_PROJECT_ID: {{ $storage.swift.tenantid }}
  {{- end }}
  {{- if $storage.swift.tenant }}
  OS_PROJECT_NAME: {{ $storage.swift.tenant }}
  {{- end }}
  {{- if $storage.swift.domainid }}
  OS_DOMAIN_ID: {{ $storage.swift.domainid }}
  {{- end }}
  {{- if $storage.swift.domain }}
  OS_DOMAIN_NAME: {{ $storage.swift.domain }}
  {{- end }}
{{- else if eq $storageType "oss" }}
  STORAGE: "alibaba"
  STORAGE_ALIBABA_BUCKET: {{ $storage.oss.bucket }}
  {{- if $storage.oss.rootdirectory }}
  STORAGE_ALIBABA_PREFIX: {{ $storage.oss.rootdirectory }}
  {{- end }}
  {{- if $storage.oss.endpoint }}
  STORAGE_ALIBABA_ENDPOINT: {{ $storage.oss.endpoint }}
  {{- end }}
  ALIBABA_CLOUD_ACCESS_KEY_ID: {{ $storage.oss.accesskeyid }}
{{- else if eq $storageType "bos" }}
  STORAGE: "baidu"
  STORAGE_BAIDU_BUCKET: {{ $storage.bos.bucket }}
  {{- if $storage.bos.rootdirectory }}
  STORAGE_BAIDU_PREFIX: {{ $storage.bos.rootdirectory }}
  {{- end }}
  {{- if $storage.bos.endpoint }}
  STORAGE_BAIDU_ENDPOINT: {{ $storage.bos.endpoint }}
  {{- end }}
  BAIDU_CLOUD_ACCESS_KEY_ID: {{ $storage.bos.accesskeyid }}
{{- end }}
  STORAGE_TIMESTAMP_TOLERANCE: 1s
{{- end }}
