apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ template "harbor.registry" . }}"
  labels:
{{ include "harbor.labels" . | indent 4 }}
    component: registry
spec:
  replicas: {{ .Values.registry.replicas }}
  strategy:
    type: {{ .Values.updateStrategy.type }}
    {{- if eq .Values.updateStrategy.type "Recreate" }}
    rollingUpdate: null
    {{- end }}
  selector:
    matchLabels:
{{ include "harbor.matchLabels" . | indent 6 }}
      component: registry
  template:
    metadata:
      labels:
{{ include "harbor.labels" . | indent 8 }}
        component: registry
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/registry/registry-cm.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/registry/registry-secret.yaml") . | sha256sum }}
        checksum/secret-jobservice: {{ include (print $.Template.BasePath "/jobservice/jobservice-secrets.yaml") . | sha256sum }}
        checksum/secret-core: {{ include (print $.Template.BasePath "/core/core-secret.yaml") . | sha256sum }}
{{- if and .Values.internalTLS.enabled (eq .Values.internalTLS.certSource "auto") }}
        checksum/tls: {{ include (print $.Template.BasePath "/internal/auto-tls.yaml") . | sha256sum }}
{{- else if and .Values.internalTLS.enabled (eq .Values.internalTLS.certSource "manual") }}
        checksum/tls: {{ include (print $.Template.BasePath "/registry/registry-tls.yaml") . | sha256sum }}
{{- end }}
{{- if .Values.registry.podAnnotations }}
{{ toYaml .Values.registry.podAnnotations | indent 8 }}
{{- end }}
    spec:
      securityContext:
        runAsUser: 10000
        fsGroup: 10000
{{- if .Values.registry.serviceAccountName }}
      serviceAccountName: {{ .Values.registry.serviceAccountName }}
{{- end -}}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      automountServiceAccountToken: false
      containers:
      - name: registry
        image: {{ .Values.registry.registry.image.repository }}:{{ .Values.registry.registry.image.tag }}
        imagePullPolicy: {{ .Values.imagePullPolicy }}
        livenessProbe:
          httpGet:
            path: /
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.registry.containerPort" . }}
          initialDelaySeconds: 300
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.registry.containerPort" . }}
          initialDelaySeconds: 1
          periodSeconds: 10
{{- if .Values.registry.registry.resources }}
        resources:
{{ toYaml .Values.registry.registry.resources | indent 10 }}
{{- end }}
        args: ["serve", "/etc/registry/config.yml"]
        envFrom:
        - secretRef:
            name: "{{ template "harbor.registry" . }}"
        env:
        {{- if has "registry" .Values.proxy.components }}
        - name: HTTP_PROXY
          value: "{{ .Values.proxy.httpProxy }}"
        - name: HTTPS_PROXY
          value: "{{ .Values.proxy.httpsProxy }}"
        - name: NO_PROXY
          value: "{{ template "harbor.noProxy" . }}"
        {{- end }}
        {{- if .Values.internalTLS.enabled }}
        - name: INTERNAL_TLS_ENABLED
          value: "true"
        - name: INTERNAL_TLS_KEY_PATH
          value: /etc/harbor/ssl/registry/tls.key
        - name: INTERNAL_TLS_CERT_PATH
          value: /etc/harbor/ssl/registry/tls.crt
        - name: INTERNAL_TLS_TRUST_CA_PATH
          value: /etc/harbor/ssl/registry/ca.crt
        {{- end }}
        ports:
        - containerPort: {{ template "harbor.registry.containerPort" . }}
        - containerPort: 5001
        volumeMounts:
        - name: registry-data
          mountPath: {{ .Values.persistence.imageChartStorage.filesystem.rootdirectory }}
          subPath: {{ .Values.persistence.persistentVolumeClaim.registry.subPath }}
        - name: registry-htpasswd
          mountPath: /etc/registry/passwd
          subPath: passwd
        - name: registry-config
          mountPath: /etc/registry/config.yml
          subPath: config.yml
        {{- if .Values.internalTLS.enabled }}
        - name: registry-internal-certs
          mountPath: /etc/harbor/ssl/registry
        {{- end }}
        {{- if and .Values.persistence.enabled (eq .Values.persistence.imageChartStorage.type "gcs") }}
        - name: gcs-key
          mountPath: /etc/registry/gcs-key.json
          subPath: gcs-key.json
        {{- end }}
        {{- if .Values.persistence.imageChartStorage.caBundleSecretName }}
        - name: storage-service-ca
          mountPath: /harbor_cust_cert/custom-ca-bundle.crt
          subPath: ca.crt
        {{- end }}
        {{- if .Values.registry.middleware.enabled }}
        {{- if eq .Values.registry.middleware.type "cloudFront" }}
        - name: cloudfront-key
          mountPath: /etc/registry/pk.pem
          subPath: pk.pem
        {{- end }}
        {{- end }}
        {{- if .Values.caBundleSecretName }}
{{ include "harbor.caBundleVolumeMount" . | indent 8 }}
        {{- end }}
      - name: registryctl
        image: {{ .Values.registry.controller.image.repository }}:{{ .Values.registry.controller.image.tag }}
        imagePullPolicy: {{ .Values.imagePullPolicy }}
        livenessProbe:
          httpGet:
            path: /api/health
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.registryctl.containerPort" . }}
          initialDelaySeconds: 300
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            scheme: {{ include "harbor.component.scheme" . | upper }}
            port: {{ template "harbor.registryctl.containerPort" . }}
          initialDelaySeconds: 1
          periodSeconds: 10
{{- if .Values.registry.controller.resources }}
        resources:
{{ toYaml .Values.registry.controller.resources | indent 10 }}
{{- end }}
        envFrom:
        - secretRef:
            name: "{{ template "harbor.registry" . }}"
        env:
        - name: CORE_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ template "harbor.core" . }}
              key: secret
        - name: JOBSERVICE_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ template "harbor.jobservice" . }}
              key: JOBSERVICE_SECRET
        {{- if has "registry" .Values.proxy.components }}
        - name: HTTP_PROXY
          value: "{{ .Values.proxy.httpProxy }}"
        - name: HTTPS_PROXY
          value: "{{ .Values.proxy.httpsProxy }}"
        - name: NO_PROXY
          value: "{{ template "harbor.noProxy" . }}"
        {{- end }}
        {{- if .Values.internalTLS.enabled }}
        - name: INTERNAL_TLS_ENABLED
          value: "true"
        - name: INTERNAL_TLS_KEY_PATH
          value: /etc/harbor/ssl/registry/tls.key
        - name: INTERNAL_TLS_CERT_PATH
          value: /etc/harbor/ssl/registry/tls.crt
        - name: INTERNAL_TLS_TRUST_CA_PATH
          value: /etc/harbor/ssl/registry/ca.crt
        {{- end }}
        ports:
        - containerPort: {{ template "harbor.registryctl.containerPort" . }}
        volumeMounts:
        - name: registry-data
          mountPath: {{ .Values.persistence.imageChartStorage.filesystem.rootdirectory }}
          subPath: {{ .Values.persistence.persistentVolumeClaim.registry.subPath }}
        - name: registry-config
          mountPath: /etc/registry/config.yml
          subPath: config.yml
        - name: registry-config
          mountPath: /etc/registryctl/config.yml
          subPath: ctl-config.yml
        {{- if .Values.internalTLS.enabled }}
        - name: registry-internal-certs
          mountPath: /etc/harbor/ssl/registry
        {{- end }}
        {{- if .Values.persistence.imageChartStorage.caBundleSecretName }}
        - name: storage-service-ca
          mountPath: /harbor_cust_cert/custom-ca-bundle.crt
          subPath: ca.crt
        {{- end }}
        {{- if and .Values.persistence.enabled (eq .Values.persistence.imageChartStorage.type "gcs") }}
        - name: gcs-key
          mountPath: /etc/registry/gcs-key.json
          subPath: gcs-key.json
        {{- end }}
        {{- if .Values.caBundleSecretName }}
{{ include "harbor.caBundleVolumeMount" . | indent 8 }}
        {{- end }}
      volumes:
      - name: registry-htpasswd
        secret:
          secretName: {{ template "harbor.registry" . }}
          items:
            - key: REGISTRY_HTPASSWD
              path: passwd
      - name: registry-config
        configMap:
          name: "{{ template "harbor.registry" . }}"
      - name: registry-data
      {{- if and .Values.persistence.enabled (eq .Values.persistence.imageChartStorage.type "filesystem") }}
        persistentVolumeClaim:
          claimName: {{ .Values.persistence.persistentVolumeClaim.registry.existingClaim | default (include "harbor.registry" .) }}
      {{- else }}
        emptyDir: {}
      {{- end }}
      {{- if .Values.internalTLS.enabled }}
      - name: registry-internal-certs
        secret:
          secretName: {{ template "harbor.internalTLS.registry.secretName" . }}
      {{- end }}
      {{- if and .Values.persistence.enabled (eq .Values.persistence.imageChartStorage.type "gcs") }}
      - name: gcs-key
        secret:
          secretName: {{ template "harbor.registry" . }}
          items:
            - key: GCS_KEY_DATA
              path: gcs-key.json
      {{- end }}
      {{- if .Values.persistence.imageChartStorage.caBundleSecretName }}
      - name: storage-service-ca
        secret:
          secretName: {{ .Values.persistence.imageChartStorage.caBundleSecretName }}
      {{- end }}
      {{- if .Values.registry.middleware.enabled }}
      {{- if eq .Values.registry.middleware.type "cloudFront" }}
      - name: cloudfront-key
        secret:
          secretName: {{ .Values.registry.middleware.cloudFront.privateKeySecret }}
          items:
            - key: CLOUDFRONT_KEY_DATA
              path: pk.pem
      {{- end }}
      {{- end }}
      {{- if .Values.caBundleSecretName }}
{{ include "harbor.caBundleVolume" . | indent 6 }}
      {{- end }}
    {{- with .Values.registry.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
      affinity:
        podAntiAffinity: {{ include "harbor.affinities.pods.hard" (dict "component" "registry" "context" $) | nindent 10}}
    {{- with .Values.registry.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
