# Helm Chart for Harbor

**Notes:** The master branch is in heavy development, please use the other stable versions instead. A high available solution for Harbor based on chart can be find [here](docs/High%20Availability.md). And refer to the [guide](docs/Upgrade.md) to upgrade the existing deployment.

This repository, including the issues, focus on deploying Harbor chart via helm.  So for the functionality issues or questions of Harbor, please open issues on [goharbor/harbor](https://github.com/goharbor/harbor)

## Introduction

This [Helm](https://github.com/kubernetes/helm) chart installs [Harbor](https://github.com/goharbor/harbor) in a Kubernetes cluster. Welcome to [contribute](CONTRIBUTING.md) to Helm Chart for Harbor.

## Prerequisites

- Kubernetes cluster 1.18+
- Helm 2.10.0+

## Installation

### Add Helm repository

```bash
helm repo add harbor https://helm.goharbor.io
```

### Configure the chart

The following items can be set via `--set` flag during installation or configured by editing the `values.yaml` directly(need to download the chart first).

#### Configure the way how to expose Harbor service

- **Ingress**: The ingress controller must be installed in the Kubernetes cluster.
  **Notes:** if the TLS is disabled, the port must be included in the command when pulling/pushing images. Refer to issue [#5291](https://github.com/goharbor/harbor/issues/5291) for the detail.
- **ClusterIP**: Exposes the service on a cluster-internal IP. Choosing this value makes the service only reachable from within the cluster.
- **NodePort**: Exposes the service on each Node’s IP at a static port (the NodePort). You’ll be able to contact the NodePort service, from outside the cluster, by requesting `NodeIP:NodePort`.
- **LoadBalancer**: Exposes the service externally using a cloud provider’s load balancer.

#### Configure the external URL

The external URL for Harbor core service is used to:

1. populate the docker/helm commands showed on portal
2. populate the token service URL returned to docker/notary client

Format: `protocol://domain[:port]`. Usually:

- if expose the service via `Ingress`, the `domain` should be the value of `expose.ingress.hosts.core`
- if expose the service via `ClusterIP`, the `domain` should be the value of `expose.clusterIP.name`
- if expose the service via `NodePort`, the `domain` should be the IP address of one Kubernetes node
- if expose the service via `LoadBalancer`, set the `domain` as your own domain name and add a CNAME record to map the domain name to the one you got from the cloud provider

If Harbor is deployed behind the proxy, set it as the URL of proxy.

#### Configure the way how to persistent data

- **Disable**: The data does not survive the termination of a pod.
- **Persistent Volume Claim(default)**: A default `StorageClass` is needed in the Kubernetes cluster to dynamic provision the volumes. Specify another StorageClass in the `storageClass` or set `existingClaim` if you have already existing persistent volumes to use.
- **External Storage(only for images and charts)**: For images and charts, the external storages are supported: `azure`, `gcs`, `s3` `swift` and `oss`.

#### Configure the other items listed in [configuration](#configuration) section

### Install the chart

Install the Harbor helm chart with a release name `my-release`:

helm 2:
```bash
helm install --name my-release harbor/harbor
```
helm 3:
```bash
helm install my-release harbor/harbor
```

## Uninstallation

To uninstall/delete the `my-release` deployment:

helm 2:
```bash
helm delete --purge my-release
```
helm 3:
```
helm uninstall my-release
```

## Configuration

The following table lists the configurable parameters of the Harbor chart and the default values.

| Parameter                                                                   | Description                                                                                                                                                                                                                                                                                                                                     | Default                         |
| --------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------- |
| **Expose** |
| `expose.type` | The way how to expose the service: `ingress`, `clusterIP`, `nodePort` or `loadBalancer`, other values will be ignored and the creation of service will be skipped. | `ingress` |
| `expose.tls.enabled` | Enable the tls or not. Delete the `ssl-redirect` annotations in `expose.ingress.annotations` when TLS is disabled and `expose.type` is `ingress`. Note: if the `expose.type` is `ingress` and the tls is disabled, the port must be included in the command when pull/push images. Refer to https://github.com/goharbor/harbor/issues/5291 for the detail. | `true` |
| `expose.tls.certSource` | The source of the tls certificate. Set it as `auto`, `secret` or `none` and fill the information in the corresponding section: 1) auto: generate the tls certificate automatically 2) secret: read the tls certificate from the specified secret. The tls certificate can be generated manually or by cert manager 3) none: configure no tls certificate for the ingress. If the default tls certificate is configured in the ingress controller, choose this option | `auto` |
| `expose.tls.auto.commonName` | The common name used to generate the certificate, it's necessary when the type isn't `ingress` | |
| `expose.tls.secret.secretName` | The name of secret which contains keys named: `tls.crt` - the certificate; `tls.key` - the private key | |
| `expose.tls.secret.notarySecretName` | The name of secret which contains keys named: `tls.crt` - the certificate; `tls.key` - the private key. Only needed when the `expose.type` is `ingress` | |
| `expose.ingress.hosts.core` | The host of Harbor core service in ingress rule | `core.harbor.domain` |
| `expose.ingress.hosts.notary` | The host of Harbor Notary service in ingress rule | `notary.harbor.domain` |
| `expose.ingress.controller` | The ingress controller type. Currently supports `default`, `gce` and `ncp` | `default` |
| `expose.ingress.annotations` | The annotations used in ingress |  |
| `expose.clusterIP.name` | The name of ClusterIP service | `harbor` |
| `expose.clusterIP.ports.httpPort` | The service port Harbor listens on when serving with HTTP | `80` |
| `expose.clusterIP.ports.httpsPort` | The service port Harbor listens on when serving with HTTPS | `443` |
| `expose.clusterIP.ports.notaryPort` | The service port Notary listens on. Only needed when `notary.enabled` is set to `true` | `4443` |
| `expose.nodePort.name` | The name of NodePort service | `harbor` |
| `expose.nodePort.ports.http.port` | The service port Harbor listens on when serving with HTTP | `80` |
| `expose.nodePort.ports.http.nodePort` | The node port Harbor listens on when serving with HTTP | `30002` |
| `expose.nodePort.ports.https.port` | The service port Harbor listens on when serving with HTTPS | `443` |
| `expose.nodePort.ports.https.nodePort` | The node port Harbor listens on when serving with HTTPS | `30003` |
| `expose.nodePort.ports.notary.port` | The service port Notary listens on. Only needed when `notary.enabled` is set to `true` | `4443` |
| `expose.nodePort.ports.notary.nodePort` | The node port Notary listens on. Only needed when `notary.enabled` is set to `true` | `30004` |
| `expose.loadBalancer.name` | The name of service |`harbor`|
| `expose.loadBalancer.IP` | The IP of the loadBalancer.  It works only when loadBalancer support assigning IP |`""`|
| `expose.loadBalancer.ports.httpPort` | The service port Harbor listens on when serving with HTTP |`80`|
| `expose.loadBalancer.ports.httpsPort` | The service port Harbor listens on when serving with HTTPS |`30002`|
| `expose.loadBalancer.ports.notaryPort` | The service port Notary listens on. Only needed when `notary.enabled` is set to `true`|
| `expose.loadBalancer.annotations` | The annotations attached to the loadBalancer service | {} |
| `expose.loadBalancer.sourceRanges` | List of IP address ranges to assign to loadBalancerSourceRanges | [] |
| **Internal TLS** |
| `internalTLS.enabled` | Enable the tls for the components (chartmuseum, core, jobservice, portal, registry, trivy) | `false` |
| `internalTLS.certSource` | Method to provide tls for the components, options is `auto`, `manual`, `secret`. | `auto` |
| `internalTLS.trustCa` | The content of trust ca, only available when `certSrouce` is `manual`. **Note**: all the internal certificates of the components must be issued by this ca |  |
| `internalTLS.core.secretName` | The secret name for core component, only available when `certSource` is `secret`. The secret must contain keys named: `ca.crt` - the certificate of CA which is used to issue internal key and crt pair for components and all Harbor components must issued by the same CA , `tls.crt` - the content of the TLS cert file, `tls.key` - the content of the TLS key file. | |
| `internalTLS.core.crt` | Content of core's TLS cert file, only available when `certSource` is `manual` | |
| `internalTLS.core.key` | Content of core's TLS key file, only available when `certSource` is `manual` | |
| `internalTLS.jobservice.secretName` | The secret name for jobservice component, only available when `certSource` is `secret`. The secret must contain keys named: `ca.crt` - the certificate of CA which is used to issue internal key and crt pair for components and all Harbor components must issued by the same CA , `tls.crt` - the content of the TLS cert file, `tls.key` - the content of the TLS key file. | |
| `internalTLS.jobservice.crt` | Content of jobservice's TLS cert file, only available when `certSource` is `manual` | |
| `internalTLS.jobservice.key` | Content of jobservice's TLS key file, only available when `certSource` is `manual` | |
| `internalTLS.registry.secretName` | The secret name for registry component, only available when `certSource` is `secret`. The secret must contain keys named: `ca.crt` - the certificate of CA which is used to issue internal key and crt pair for components and all Harbor components must issued by the same CA , `tls.crt` - the content of the TLS cert file, `tls.key` - the content of the TLS key file. | |
| `internalTLS.registry.crt` | Content of registry's TLS cert file, only available when `certSource` is `manual` | |
| `internalTLS.registry.key` | Content of registry's TLS key file, only available when `certSource` is `manual` | |
| `internalTLS.portal.secretName` | The secret name for portal component, only available when `certSource` is `secret`. The secret must contain keys named: `ca.crt` - the certificate of CA which is used to issue internal key and crt pair for components and all Harbor components must issued by the same CA , `tls.crt` - the content of the TLS cert file, `tls.key` - the content of the TLS key file. | |
| `internalTLS.portal.crt` | Content of portal's TLS cert file, only available when `certSource` is `manual` | |
| `internalTLS.portal.key` | Content of portal's TLS key file, only available when `certSource` is `manual` | |
| `internalTLS.chartmuseum.secretName` | The secret name for chartmuseum component, only available when `certSource` is `secret`. The secret must contain keys named: `ca.crt` - the certificate of CA which is used to issue internal key and crt pair for components and all Harbor components must issued by the same CA , `tls.crt` - the content of the TLS cert file, `tls.key` - the content of the TLS key file. | |
| `internalTLS.chartmuseum.crt` | Content of chartmuseum's TLS cert file, only available when `certSource` is `manual` | |
| `internalTLS.chartmuseum.key` | Content of chartmuseum's TLS key file, only available when `certSource` is `manual` | |
| `internalTLS.trivy.secretName` | The secret name for trivy component, only available when `certSource` is `secret`. The secret must contain keys named: `ca.crt` - the certificate of CA which is used to issue internal key and crt pair for components and all Harbor components must issued by the same CA , `tls.crt` - the content of the TLS cert file, `tls.key` - the content of the TLS key file. | |
| `internalTLS.trivy.crt` | Content of trivy's TLS cert file, only available when `certSource` is `manual` | |
| `internalTLS.trivy.key` | Content of trivy's TLS key file, only available when `certSource` is `manual` | |
| **Persistence**                                                             |
| `persistence.enabled`                                                       | Enable the data persistence or not                                                                                                                                                                                                                                                                                                              | `true`                          |
| `persistence.resourcePolicy`                                                | Setting it to `keep` to avoid removing PVCs during a helm delete operation. Leaving it empty will delete PVCs after the chart deleted. Does not affect PVCs created for internal database and redis components.                                                                                                                                 | `keep`                          |
| `persistence.persistentVolumeClaim.registry.existingClaim`                  | Use the existing PVC which must be created manually before bound, and specify the `subPath` if the PVC is shared with other components                                                                                                                                                                                                                                                                                |                                 |
| `persistence.persistentVolumeClaim.registry.storageClass`                   | Specify the `storageClass` used to provision the volume. Or the default StorageClass will be used(the default). Set it to `-` to disable dynamic provisioning                                                                                                                                                                                   |                                 |
| `persistence.persistentVolumeClaim.registry.subPath`                        | The sub path used in the volume                                                                                                                                                                                                                                                                                                                 |                                 |
| `persistence.persistentVolumeClaim.registry.accessMode`                     | The access mode of the volume                                                                                                                                                                                                                                                                                                                   | `ReadWriteOnce`                 |
| `persistence.persistentVolumeClaim.registry.size`                           | The size of the volume                                                                                                                                                                                                                                                                                                                          | `5Gi`                           |
| `persistence.persistentVolumeClaim.chartmuseum.existingClaim`               | Use the existing PVC which must be created manually before bound, and specify the `subPath` if the PVC is shared with other components                                                                                                                                                                                                                                                                                |                                 |
| `persistence.persistentVolumeClaim.chartmuseum.storageClass`                | Specify the `storageClass` used to provision the volume. Or the default StorageClass will be used(the default). Set it to `-` to disable dynamic provisioning                                                                                                                                                                                   |                                 |
| `persistence.persistentVolumeClaim.chartmuseum.subPath`                     | The sub path used in the volume                                                                                                                                                                                                                                                                                                                 |                                 |
| `persistence.persistentVolumeClaim.chartmuseum.accessMode`                  | The access mode of the volume                                                                                                                                                                                                                                                                                                                   | `ReadWriteOnce`                 |
| `persistence.persistentVolumeClaim.chartmuseum.size`                        | The size of the volume                                                                                                                                                                                                                                                                                                                          | `5Gi`                           |
| `persistence.persistentVolumeClaim.jobservice.existingClaim`                | Use the existing PVC which must be created manually before bound, and specify the `subPath` if the PVC is shared with other components                                                                                                                                                                                                                                                                                |                                 |
| `persistence.persistentVolumeClaim.jobservice.storageClass`                 | Specify the `storageClass` used to provision the volume. Or the default StorageClass will be used(the default). Set it to `-` to disable dynamic provisioning                                                                                                                                                                                   |                                 |
| `persistence.persistentVolumeClaim.jobservice.subPath`                      | The sub path used in the volume                                                                                                                                                                                                                                                                                                                 |                                 |
| `persistence.persistentVolumeClaim.jobservice.accessMode`                   | The access mode of the volume                                                                                                                                                                                                                                                                                                                   | `ReadWriteOnce`                 |
| `persistence.persistentVolumeClaim.jobservice.size`                         | The size of the volume                                                                                                                                                                                                                                                                                                                          | `1Gi`                           |
| `persistence.persistentVolumeClaim.database.existingClaim`                  | Use the existing PVC which must be created manually before bound, and specify the `subPath` if the PVC is shared with other components. If external database is used, the setting will be ignored                                                                                                                                                                                                                     |                                 |
| `persistence.persistentVolumeClaim.database.storageClass`                   | Specify the `storageClass` used to provision the volume. Or the default StorageClass will be used(the default). Set it to `-` to disable dynamic provisioning. If external database is used, the setting will be ignored                                                                                                                        |                                 |
| `persistence.persistentVolumeClaim.database.subPath`                        | The sub path used in the volume. If external database is used, the setting will be ignored                                                                                                                                                                                                                                                      |                                 |
| `persistence.persistentVolumeClaim.database.accessMode`                     | The access mode of the volume. If external database is used, the setting will be ignored                                                                                                                                                                                                                                                        | `ReadWriteOnce`                 |
| `persistence.persistentVolumeClaim.database.size`                           | The size of the volume. If external database is used, the setting will be ignored                                                                                                                                                                                                                                                               | `1Gi`                           |
| `persistence.persistentVolumeClaim.redis.existingClaim`                     | Use the existing PVC which must be created manually before bound, and specify the `subPath` if the PVC is shared with other components. If external Redis is used, the setting will be ignored                                                                                                                                                                                                                        |                                 |
| `persistence.persistentVolumeClaim.redis.storageClass`                      | Specify the `storageClass` used to provision the volume. Or the default StorageClass will be used(the default). Set it to `-` to disable dynamic provisioning. If external Redis is used, the setting will be ignored                                                                                                                           |                                 |
| `persistence.persistentVolumeClaim.redis.subPath`                           | The sub path used in the volume. If external Redis is used, the setting will be ignored                                                                                                                                                                                                                                                         |                                 |
| `persistence.persistentVolumeClaim.redis.accessMode`                        | The access mode of the volume. If external Redis is used, the setting will be ignored                                                                                                                                                                                                                                                           | `ReadWriteOnce`                 |
| `persistence.persistentVolumeClaim.redis.size`                              | The size of the volume. If external Redis is used, the setting will be ignored                                                                                                                                                                                                                                                                  | `1Gi`                           |
| `persistence.persistentVolumeClaim.trivy.existingClaim`                     | Use the existing PVC which must be created manually before bound, and specify the `subPath` if the PVC is shared with other components. | |
| `persistence.persistentVolumeClaim.trivy.storageClass`                      | Specify the `storageClass` used to provision the volume. Or the default StorageClass will be used(the default). Set it to `-` to disable dynamic provisioning | |
| `persistence.persistentVolumeClaim.trivy.subPath`                           | The sub path used in the volume | |
| `persistence.persistentVolumeClaim.trivy.accessMode`                        | The access mode of the volume | `ReadWriteOnce` |
| `persistence.persistentVolumeClaim.trivy.size`                              | The size of the volume | `5Gi` |
| `persistence.imageChartStorage.disableredirect`                             | The configuration for managing redirects from content backends. For backends which not supported it (such as using minio for `s3` storage type), please set it to `true` to disable redirects. Refer to the [guide](https://github.com/docker/distribution/blob/master/docs/configuration.md#redirect) for more information about the detail    | `false`                         |
| `persistence.imageChartStorage.caBundleSecretName` | Specify the `caBundleSecretName` if the storage service uses a self-signed certificate. The secret must contain keys named `ca.crt` which will be injected into the trust store  of registry's and chartmuseum's containers. | |
| `persistence.imageChartStorage.type`                                        | The type of storage for images and charts: `filesystem`, `azure`, `gcs`, `s3`, `swift` or `oss`. The type must be `filesystem` if you want to use persistent volumes for registry and chartmuseum. Refer to the [guide](https://github.com/docker/distribution/blob/master/docs/configuration.md#storage) for more information about the detail | `filesystem`                    |
| **General**                                                                 |
| `externalURL`                                                               | The external URL for Harbor core service                                                                                                                                                                                                                                                                                                        | `https://core.harbor.domain`    |
| `caBundleSecretName` | The custom ca bundle secret name, the secret must contain key named "ca.crt" which will be injected into the trust store for chartmuseum, core, jobservice, registry, trivy components. | |
| `uaaSecretName` | If using external UAA auth which has a self signed cert, you can provide a pre-created secret containing it under the key `ca.crt`. | |
| `imagePullPolicy` | The image pull policy |  |
| `imagePullSecrets` | The imagePullSecrets names for all deployments |  |
| `updateStrategy.type` | The update strategy for deployments with persistent volumes(jobservice, registry and chartmuseum): `RollingUpdate` or `Recreate`. Set it as `Recreate` when `RWM` for volumes isn't supported  | `RollingUpdate` |
| `logLevel` | The log level: `debug`, `info`, `warning`, `error` or `fatal` | `info` |
| `harborAdminPassword` | The initial password of Harbor admin. Change it from portal after launching Harbor | `Harbor12345` |
| `caSecretName` | The name of the secret which contains key named `ca.crt`. Setting this enables the download link on portal to download the certificate of CA when the certificate isn't generated automatically | |
| `secretKey` | The key used for encryption. Must be a string of 16 chars | `not-a-secure-key` |
| `proxy.httpProxy` | The URL of the HTTP proxy server | |
| `proxy.httpsProxy` | The URL of the HTTPS proxy server | |
| `proxy.noProxy` | The URLs that the proxy settings not apply to | 127.0.0.1,localhost,.local,.internal |
| `proxy.components` | The component list that the proxy settings apply to | core, jobservice, trivy |
| **Nginx** (if expose the service via `ingress`, the Nginx will not be used) |
| `nginx.image.repository`                                                    | Image repository                                                                                                                                                                                                                                                                                                                                | `goharbor/nginx-photon`         |
| `nginx.image.tag`                                                           | Image tag                                                                                                                                                                                                                                                                                                                                       | `dev`                           |
| `nginx.replicas`                                                            | The replica count                                                                                                                                                                                                                                                                                                                               | `1`                             |
| `nginx.resources`                                                           | The [resources] to allocate for container                                                                                                                                                                                                                                                                                                       | undefined                       |
| `nginx.nodeSelector`                                                        | Node labels for pod assignment                                                                                                                                                                                                                                                                                                                  | `{}`                            |
| `nginx.tolerations`                                                         | Tolerations for pod assignment                                                                                                                                                                                                                                                                                                                  | `[]`                            |
| `nginx.affinity`                                                            | Node/Pod affinities                                                                                                                                                                                                                                                                                                                             | `{}`                            |
| `nginx.podAnnotations`                                                      | Annotations to add to the nginx pod                                                                                                                                                                                                                                                                                                             | `{}`                            |
| **Portal**                                                                  |
| `portal.image.repository`                                                   | Repository for portal image                                                                                                                                                                                                                                                                                                                     | `goharbor/harbor-portal`        |
| `portal.image.tag`                                                          | Tag for portal image                                                                                                                                                                                                                                                                                                                            | `dev`                           |
| `portal.replicas`                                                           | The replica count                                                                                                                                                                                                                                                                                                                               | `1`                             |
| `portal.resources`                                                          | The [resources] to allocate for container                                                                                                                                                                                                                                                                                                       | undefined                       |
| `portal.nodeSelector`                                                       | Node labels for pod assignment                                                                                                                                                                                                                                                                                                                  | `{}`                            |
| `portal.tolerations`                                                        | Tolerations for pod assignment                                                                                                                                                                                                                                                                                                                  | `[]`                            |
| `portal.affinity`                                                           | Node/Pod affinities                                                                                                                                                                                                                                                                                                                             | `{}`                            |
| `portal.podAnnotations`                                                     | Annotations to add to the portal pod                                                                                                                                                                                                                                                                                                            | `{}`                            |
| **Core** |
| `core.image.repository` | Repository for Harbor core image | `goharbor/harbor-core` |
| `core.image.tag` | Tag for Harbor core image | `dev` |
| `core.replicas` | The replica count  | `1` |
| `core.startupProbe.initialDelaySeconds` | The initial delay in seconds for the startup probe | `10` |
| `core.resources` | The [resources] to allocate for container | undefined |
| `core.nodeSelector`  | Node labels for pod assignment | `{}` |
| `core.tolerations` | Tolerations for pod assignment | `[]` |
| `core.affinity` | Node/Pod affinities | `{}` |
| `core.podAnnotations` | Annotations to add to the core pod | `{}` |
| `core.secret` | Secret is used when core server communicates with other components. If a secret key is not specified, Helm will generate one. Must be a string of 16 chars. | |
| `core.secretName` | Fill the name of a kubernetes secret if you want to use your own TLS certificate and private key for token encryption/decryption. The secret must contain keys named: `tls.crt` - the certificate and `tls.key` - the private key. The default key pair will be used if it isn't set | |
| `core.xsrfKey` | The XSRF key. Will be generated automatically if it isn't specified | |
| **Jobservice**                                                              |
| `jobservice.image.repository`                                               | Repository for jobservice image                                                                                                                                                                                                                                                                                                                 | `goharbor/harbor-jobservice`    |
| `jobservice.image.tag`                                                      | Tag for jobservice image                                                                                                                                                                                                                                                                                                                        | `dev`                           |
| `jobservice.replicas`                                                       | The replica count                                                                                                                                                                                                                                                                                                                               | `1`                             |
| `jobservice.maxJobWorkers`                                                  | The max job workers                                                                                                                                                                                                                                                                                                                             | `10`                            |
| `jobservice.jobLoggers`                                                     | The loggers for jobs: `file`, `database` or `stdout`                                                                                                                                                                                                                                                                                            | `file`                          |
| `jobservice.resources`                                                      | The [resources] to allocate for container                                                                                                                                                                                                                                                                                                       | undefined                       |
| `jobservice.nodeSelector`                                                   | Node labels for pod assignment                                                                                                                                                                                                                                                                                                                  | `{}`                            |
| `jobservice.tolerations`                                                    | Tolerations for pod assignment                                                                                                                                                                                                                                                                                                                  | `[]`                            |
| `jobservice.affinity`                                                       | Node/Pod affinities                                                                                                                                                                                                                                                                                                                             | `{}`                            |
| `jobservice.podAnnotations`                                                 | Annotations to add to the jobservice pod                                                                                                                                                                                                                                                                                                        | `{}`                            |
| `jobservice.secret`                                                         | Secret is used when job service communicates with other components. If a secret key is not specified, Helm will generate one. Must be a string of 16 chars.                                                                                                                                                                                     |                                 |
| **Registry**                                                                |
| `registry.registry.image.repository`                                        | Repository for registry image                                                                                                                                                                                                                                                                                                                   | `goharbor/registry-photon`      |
| `registry.registry.image.tag`                                               | Tag for registry image                                                                                                                                                                                                                                                                                                                          |
| `registry.registry.resources`                                               | The [resources] to allocate for container                                                                                                                                                                                                                                                                                                       | undefined                       |  | `dev` |
| `registry.controller.image.repository`                                      | Repository for registry controller image                                                                                                                                                                                                                                                                                                        | `goharbor/harbor-registryctl`   |
| `registry.controller.image.tag`                                             | Tag for registry controller image                                                                                                                                                                                                                                                                                                               |
| `registry.controller.resources`                                             | The [resources] to allocate for container                                                                                                                                                                                                                                                                                                       | undefined                       |  | `dev` |
| `registry.replicas`                                                         | The replica count                                                                                                                                                                                                                                                                                                                               | `1`                             |
| `registry.nodeSelector`                                                     | Node labels for pod assignment                                                                                                                                                                                                                                                                                                                  | `{}`                            |
| `registry.tolerations`                                                      | Tolerations for pod assignment                                                                                                                                                                                                                                                                                                                  | `[]`                            |
| `registry.affinity`                                                         | Node/Pod affinities                                                                                                                                                                                                                                                                                                                             | `{}`                            |
| `registry.middleware`                                                       | Middleware is used to add support for a CDN between backend storage and `docker pull` recipient.  See [official docs](https://github.com/docker/distribution/blob/master/docs/configuration.md#middleware).
| `registry.podAnnotations`                                                   | Annotations to add to the registry pod                                                                                                                                                                                                                                                                                                          | `{}`                            |
| `registry.secret`                                                           | Secret is used to secure the upload state from client and registry storage backend. See [official docs](https://github.com/docker/distribution/blob/master/docs/configuration.md#http). If a secret key is not specified, Helm will generate one. Must be a string of 16 chars.                                                                                 |                                 |
| `registry.credentials.username` | The username for accessing the registry instance, which is hosted by htpasswd auth mode.  More details see [official docs](https://github.com/docker/distribution/blob/master/docs/configuration.md#htpasswd). | `harbor_registry_user` |
| `registry.credentials.password` | The password for accessing the registry instance, which is hosted by htpasswd auth mode.  More details see [official docs](https://github.com/docker/distribution/blob/master/docs/configuration.md#htpasswd).  It is suggested you update this value before installation. | `harbor_registry_password` |
| `registry.credentials.htpasswd` | The content of htpasswd file based on the value of `registry.credentials.username` `registry.credentials.password`.  Currently `helm` does not support bcrypt in the template script, if the credential is updated you need to manually generated by calling [htpasswd](https://httpd.apache.org/docs/2.4/programs/htpasswd.html): `htpasswd -nbBC10 $username $password`.  More details see [official_docs](https://github.com/docker/distribution/blob/master/docs/configuration.md#htpasswd).|  `harbor_registry_user:$2y$10$9L4Tc0DJbFFMB6RdSCunrOpTHdwhid4ktBJmLD00bYgqkkGOvll3m` |
| `registry.relativeurls`                                                     | If true, the registry returns relative URLs in Location headers. The client is responsible for resolving the correct URL. Needed if harbor is behind a reverse proxy| `false` |
| **Chartmuseum**                                                             |
| `chartmuseum.enabled`                                                       | Enable chartmusuem to store chart                                                                                                                                                                                                                                                                                                               | `true`                          |
| `chartmuseum.absoluteUrl`                                                   | If true, ChartMuseum will return absolute URLs. The default behavior is to return relative URLs                                                                                                                                                                                                                                                 | `false`                         |
| `chartmuseum.image.repository`                                              | Repository for chartmuseum image                                                                                                                                                                                                                                                                                                                | `goharbor/chartmuseum-photon`   |
| `chartmuseum.image.tag`                                                     | Tag for chartmuseum image                                                                                                                                                                                                                                                                                                                       | `dev`                           |
| `chartmuseum.replicas`                                                      | The replica count                                                                                                                                                                                                                                                                                                                               | `1`                             |
| `chartmuseum.resources`                                                     | The [resources] to allocate for container                                                                                                                                                                                                                                                                                                       | undefined                       |
| `chartmuseum.nodeSelector`                                                  | Node labels for pod assignment                                                                                                                                                                                                                                                                                                                  | `{}`                            |
| `chartmuseum.tolerations`                                                   | Tolerations for pod assignment                                                                                                                                                                                                                                                                                                                  | `[]`                            |
| `chartmuseum.affinity`                                                      | Node/Pod affinities                                                                                                                                                                                                                                                                                                                             | `{}`                            |
| `chartmuseum.podAnnotations`                                                | Annotations to add to the chart museum pod                                                                                                                                                                                                                                                                                                      | `{}`                            |
| **[Trivy][trivy]** |
| `trivy.enabled`          | The flag to enable Trivy scanner                                                                           | `true` |
| `trivy.image.repository` | Repository for Trivy adapter image                                                                         | `goharbor/trivy-adapter-photon` |
| `trivy.image.tag`        | Tag for Trivy adapter image                                                                                | `dev` |
| `trivy.resources`        | The [resources] to allocate for Trivy adapter container                                                    | |
| `trivy.replicas`         | The number of Pod replicas                                                                                 | `1` |
| `trivy.debugMode`        | The flag to enable Trivy debug mode                                                                        | `false` |
| `trivy.vulnType`         | Comma-separated list of vulnerability types. Possible values `os` and `library`.                           | `os,library` |
| `trivy.severity`         | Comma-separated list of severities to be checked                                                           | `UNKNOWN,LOW,MEDIUM,HIGH,CRITICAL` |
| `trivy.ignoreUnfixed`    | The flag to display only fixed vulnerabilities                                                             | `false` |
| `trivy.insecure`         | The flag to skip verifying registry certificate                                                            | `false` |
| `trivy.skipUpdate`       | The flag to disable [Trivy DB][trivy-db] downloads from GitHub                                             | `false` |
| `trivy.gitHubToken`      | The GitHub access token to download [Trivy DB][trivy-db] (see [GitHub rate limiting][trivy-rate-limiting]) | |
| **Notary**                                                                  |
| `notary.enabled`                                                            | Enable Notary?                                                                                                                                                                                                                                                                                                                                  | `true`                          |
| `notary.server.image.repository`                                            | Repository for notary server image                                                                                                                                                                                                                                                                                                              | `goharbor/notary-server-photon` |
| `notary.server.image.tag`                                                   | Tag for notary server image                                                                                                                                                                                                                                                                                                                     | `dev`                           |
| `notary.server.replicas`                                                    | The replica count                                                                                                                                                                                                                                                                                                                               |
| `notary.server.resources`                                                   | The [resources] to allocate for container                                                                                                                                                                                                                                                                                                       | undefined                       |  | `1` |
| `notary.signer.image.repository`                                            | Repository for notary signer image                                                                                                                                                                                                                                                                                                              | `goharbor/notary-signer-photon` |
| `notary.signer.image.tag`                                                   | Tag for notary signer image                                                                                                                                                                                                                                                                                                                     | `dev`                           |
| `notary.signer.replicas`                                                    | The replica count                                                                                                                                                                                                                                                                                                                               |
| `notary.signer.resources`                                                   | The [resources] to allocate for container                                                                                                                                                                                                                                                                                                       | undefined                       |  | `1` |
| `notary.nodeSelector`                                                       | Node labels for pod assignment                                                                                                                                                                                                                                                                                                                  | `{}`                            |
| `notary.tolerations`                                                        | Tolerations for pod assignment                                                                                                                                                                                                                                                                                                                  | `[]`                            |
| `notary.affinity`                                                           | Node/Pod affinities                                                                                                                                                                                                                                                                                                                             | `{}`                            |
| `notary.podAnnotations`                                                     | Annotations to add to the notary pod                                                                                                                                                                                                                                                                                                            | `{}`                            |
| `notary.secretName`                                                         | Fill the name of a kubernetes secret if you want to use your own TLS certificate authority, certificate and private key for notary communications. The secret must contain keys named `tls.ca`, `tls.crt` and `tls.key` that contain the CA, certificate and private key. They will be generated if not set.                                    |                                 |
| **Database** |
| `database.type` | If external database is used, set it to `external` | `internal` |
| `database.internal.image.repository` | Repository for database image | `goharbor/harbor-db` |
| `database.internal.image.tag` | Tag for database image | `dev` |
| `database.internal.password`                                                | The password for database                                                                                                                                                                                                                                                                                                                       | `changeit`                      |
| `database.internal.resources`                                               | The [resources] to allocate for container                                                                                                                                                                                                                                                                                                       | undefined                       |
| `database.internal.nodeSelector`                                            | Node labels for pod assignment                                                                                                                                                                                                                                                                                                                  | `{}`                            |
| `database.internal.tolerations`                                             | Tolerations for pod assignment                                                                                                                                                                                                                                                                                                                  | `[]`                            |
| `database.internal.affinity`                                                | Node/Pod affinities                                                                                                                                                                                                                                                                                                                             | `{}`                            |
| `database.external.host`                                                    | The hostname of external database                                                                                                                                                                                                                                                                                                               | `***********`                   |
| `database.external.port`                                                    | The port of external database                                                                                                                                                                                                                                                                                                                   | `5432`                          |
| `database.external.username`                                                | The username of external database                                                                                                                                                                                                                                                                                                               | `user`                          |
| `database.external.password`                                                | The password of external database                                                                                                                                                                                                                                                                                                               | `password`                      |
| `database.external.coreDatabase`                                            | The database used by core service                                                                                                                                                                                                                                                                                                               | `registry`                      |
| `database.external.notaryServerDatabase`                                    | The database used by Notary server                                                                                                                                                                                                                                                                                                              | `notary_server`                 |
| `database.external.notarySignerDatabase`                                    | The database used by Notary signer                                                                                                                                                                                                                                                                                                              | `notary_signer`                 |
| `database.external.sslmode`                                                 | Connection method of external database (require, verify-full, verify-ca, disable)                                                                                                                                                                                                                                                               | `disable` |
| `database.maxIdleConns` | The maximum number of connections in the idle connection pool. If it <=0, no idle connections are retained. | `50` |
| `database.maxOpenConns` | The maximum number of open connections to the database. If it <= 0, then there is no limit on the number of open connections. | `100` |
| `database.podAnnotations`                                                   | Annotations to add to the database pod                                                                                                                                                                                                                                                                                                          | `{}`                            |
| **Redis** |
| `redis.type` | If external redis is used, set it to `external` | `internal` |
| `redis.internal.image.repository` | Repository for redis image | `goharbor/redis-photon` |
| `redis.internal.image.tag` | Tag for redis image | `dev` |
| `redis.internal.resources` | The [resources] to allocate for container | undefined |
| `redis.internal.nodeSelector` | Node labels for pod assignment | `{}` |
| `redis.internal.tolerations` | Tolerations for pod assignment | `[]` |
| `redis.internal.affinity` | Node/Pod affinities | `{}` |
| `redis.external.addr` | The addr of external Redis: <host_redis>:<port_redis>. When using sentinel, it should be <host_sentinel1>:<port_sentinel1>,<host_sentinel2>:<port_sentinel2>,<host_sentinel3>:<port_sentinel3> | `***********:6379` |
| `redis.external.sentinelMasterSet` | The name of the set of Redis instances to monitor | |
| `redis.external.coreDatabaseIndex` | The database index for core | `0` |
| `redis.external.jobserviceDatabaseIndex` | The database index for jobservice | `1` |
| `redis.external.registryDatabaseIndex` | The database index for registry | `2` |
| `redis.external.chartmuseumDatabaseIndex` | The database index for chartmuseum | `3` |
| `redis.external.trivyAdapterIndex` | The database index for trivy adapter | `5` |
| `redis.external.password` | The password of external Redis | |
| `redis.podAnnotations` | Annotations to add to the redis pod | `{}` |
| **Exporter** |
| `exporter.replicas` ｜ The replica count | `1` |
| `exporter.podAnnotations` | Annotations to add to the exporter pod | `{}` |
| `exporter.image.repository` | Repository for redis image | `goharbor/harbor-exporter` |
| `exporter.image.tag` | Tag for exporter image | `dev` |
| `exporter.nodeSelector` |  Node labels for pod assignment | `{}` |
| `exporter.tolerations` | Tolerations for pod assignment | `[]` |
| `exporter.affinity` | Node/Pod affinities | `{}` |
| `exporter.cacheDuration` | the cache duration for infomation that exporter collected from Harbor | `30` |
| `exporter.cacheCleanInterval` | cache clean interval for infomation that exporter collected from Harbor | `14400` |
| **Metrics** |
| `metrics.enabled`| if enable harbor metrics | `false` |
| `metrics.core.path`| the url path for core metrics | `/metrics` |
| `metrics.core.port` | the port for core metrics | `8001` |
| `metrics.registry.path` | the url path for registry metrics | `/metrics` |
| `metrics.registry.port` | the port for registry metrics | `8001` |
| `metrics.exporter.path` | the url path for exporter metrics | `/metrics` |
| `metrics.exporter.port` | the port for exporter metrics | `8001` |

[resources]: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
[trivy]: https://github.com/aquasecurity/trivy
[trivy-db]: https://github.com/aquasecurity/trivy-db
[trivy-rate-limiting]: https://github.com/aquasecurity/trivy#github-rate-limiting
