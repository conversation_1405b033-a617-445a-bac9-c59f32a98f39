{{- if .Values.dragonfly.scheduler.enable }}

{{ $advertiseHostSuffix := .Values.dragonfly.scheduler.config.server.advertiseHostSuffix }}
{{ $GitVersion := .Capabilities.KubeVersion.GitVersion }}
{{ $port := .Values.dragonfly.scheduler.config.server.port }}
{{ $serviceName := printf "%s-dragonfly-%s" .Release.Name .Values.dragonfly.scheduler.name }}
{{ $ccrInstanceId := .Release.Name }}
{{- if semverCompare "<1.14-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: extensions/v1beta1
{{- else if semverCompare "<1.19-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: networking.k8s.io/v1beta1
{{- else }}
apiVersion: networking.k8s.io/v1
{{- end }}
kind: Ingress
metadata:
  name: {{ template "ccr.dragonfly.scheduler" . }}
  labels:
{{ include "ccr.labels" . | indent 4 }}
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
spec:
  ingressClassName: {{ index .Values "ingress-nginx" "controller" "ingressClass" | quote }}
  tls:
{{- range .Values.dragonfly.scheduler.generateIndex }}
    - secretName: domain-cert
      hosts:
        - {{ $ccrInstanceId }}-dragonfly-scheduler-{{ . }}-{{ $advertiseHostSuffix }}
{{- end }}
  rules:
{{- range .Values.dragonfly.scheduler.generateIndex }}
    - http:
        paths:
{{- if semverCompare "<1.19-0" $GitVersion }}
          - path: /
            backend:
              serviceName: {{ $serviceName }}-{{ . }}
              servicePort:  {{ $port }}
{{- else }}
          - path: /
            backend:
              service:
                name: {{ $serviceName }}-{{ . }}
                port:
                  number: {{ $port }}
{{- end }}
      host: {{ $ccrInstanceId }}-dragonfly-scheduler-{{ . }}-{{ $advertiseHostSuffix }}
{{- end }}
{{- end }}
