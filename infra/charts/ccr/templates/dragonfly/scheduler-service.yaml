{{- if .Values.dragonfly.scheduler.enable }}

{{ $chart := printf "%s-%s" .Chart.Name .Chart.Version }}
{{ $release := .Release.Name }}
{{ $heritage := .Release.Service }}
{{ $component := .Values.dragonfly.scheduler.name }}
{{ $labels := .Values.dragonfly.scheduler.service.labels }}
{{ $annotations := .Values.dragonfly.scheduler.service.annotations }}
{{ $type := .Values.dragonfly.scheduler.service.type }}
{{ $port := .Values.dragonfly.scheduler.config.server.port }}
{{ $name := printf "%s-dragonfly-%s" .Release.Name .Values.dragonfly.scheduler.name }}

{{- range .Values.dragonfly.scheduler.generateIndex }}
apiVersion: v1
kind: Service
metadata:
  name: {{ $name }}-{{ . }}
  labels:
    chart: {{ $chart }}
    release: {{ $release }}
    heritage: {{ $heritage }}
    component: {{ $component }}
{{- if $labels }}
{{ toYaml $labels | indent 4 }}
{{- end }}
{{- if $annotations }}
  annotations:
{{ toYaml $annotations | indent 4 }}
{{- end }}
spec:
  type: {{ $type }}
  ports:
    - name: http-grpc
      port: {{ $port }}
      protocol: TCP
      targetPort: {{ $port }}
  selector:
    release: {{ $release }}
    component: {{ $component }}
    statefulset.kubernetes.io/pod-name: {{ $name }}-{{ . }}
---
{{- end }}

{{- end }}
