{{/* vim: set filetype=mustache: */}}
{{/*
Expand the name of the chart.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
*/}}
{{- define "ccr.name" -}}
{{- default "ccr" .Values.nameOverride | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/* Helm required labels */}}
{{- define "ccr.labels" -}}
heritage: {{ .Release.Service }}
release: {{ .Release.Name }}
chart: {{ .Chart.Name }}
app: "{{ template "ccr.name" . }}"
{{- end -}}

{{- define "ccr.backend.port" -}}
  {{- if .Values.harbor.internalTLS.enabled -}}
    {{- printf "443" -}}
  {{- else -}}
    {{- printf "80" -}}
  {{- end -}}
{{- end -}}

{{- define "ccr.harbor.core" -}}
{{ .Release.Name }}-harbor-core
{{- end }}

{{- define "ccr.harbor.addon.addr" -}}
{{ template "ccr.name" . }}-harbor-addon:{{ .Values.ccr.harborAddon.containerPort }}
{{- end }}

{{- define "ccr.harbor.ingress" -}}
{{ .Release.Name }}-harbor-ingress
{{- end }}

{{- define "ccr.intersync.domain" -}}
{{ .Release.Name }}-inter-sync-{{ .Values.ccr.region }}
{{- end }}

{{- define "ccr.dragonfly.manager" -}}
{{ .Release.Name }}-dragonfly-manager
{{- end }}

{{- define "ccr.dragonfly.scheduler" -}}
{{ .Release.Name }}-dragonfly-scheduler
{{- end }}

{{/*
Return a hard podAffinity/podAntiAffinity definition
{{ include "ccr.affinities.pods.hard" (dict "component" "FOO" "context" $) -}}
*/}}
{{- define "ccr.affinities.pods.hard" -}}
{{- $component := default "" .component -}}
requiredDuringSchedulingIgnoredDuringExecution:
  - labelSelector:
      matchLabels: {{- (include "ccr.labels" .context) | nindent 8 }}
        {{- if not (empty $component) }}
        {{ printf "component: %s" $component }}
        {{- end }}
    namespaces:
      - {{ .context.Release.Namespace | quote }}
    topologyKey: kubernetes.io/hostname
{{- end -}}