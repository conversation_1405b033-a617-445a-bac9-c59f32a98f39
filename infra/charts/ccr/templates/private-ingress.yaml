{{- if .Values.ccr.privateDomain -}}
{{- if semverCompare "<1.14-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: extensions/v1beta1
{{- else if semverCompare "<1.19-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: networking.k8s.io/v1beta1
{{- else }}
apiVersion: networking.k8s.io/v1
{{- end }}
kind: Ingress
metadata:
  name: {{ .Values.ccr.privateDomain.name }}
  labels:
{{ include "ccr.labels" . | indent 4 }}
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-request-buffering: "off"
    nginx.ingress.kubernetes.io/server-snippet: |
      if ($http_authorization !~* "^((bearer|basic) .*)?$") {
        return 401 '';
      }
      rewrite ^/v2/(.*)/manifests/(.*) $v2uri last;
      rewrite ^/v2/(.*)/blobs/(.*) $v2uri last;
{{- with .Values.ccr.privateDomain.annotations }}
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  ingressClassName: {{ index .Values "ingress-nginx" "controller" "ingressClass" | quote }}
  tls:
  - secretName: domain-cert
    hosts:
    - {{ .Values.ccr.privateDomain.name }}
  rules:
  - http:
      paths:
{{- if semverCompare "<1.19-0" .Capabilities.KubeVersion.GitVersion }}
      - path: /v2/
        backend:
          serviceName: {{ template "harbor.core" . }}
          servicePort: {{ template "ccr.backend.port" . }}
      - path: /chartrepo/
        backend:
          serviceName: {{ template "ccr.name" . }}-harbor-addon
          servicePort: {{ .Values.ccr.harborAddon.containerPort }}
      - path: /api/chartrepo/
        backend:
          serviceName: {{ template "ccr.name" . }}-harbor-addon
          servicePort: {{ .Values.ccr.harborAddon.containerPort }}
      - path: /addon/v1/manifests/
        backend:
          serviceName: {{ template "ccr.name" . }}-harbor-addon
          servicePort: {{ .Values.ccr.harborAddon.containerPort }}
      - path: /addon/v1/blobs/
        backend:
          serviceName: {{ template "ccr.name" . }}-harbor-addon
          servicePort: {{ .Values.ccr.harborAddon.containerPort }}
{{- else }}
      - path: /addon/v1/manifests/
        backend:
          service:
            name: {{ template "ccr.name" . }}-harbor-addon
            port:
              number: {{ .Values.ccr.harborAddon.containerPort }}
      - path: /v2/
        pathType: Prefix
        backend:
          service:
            name: {{ template "harbor.core" . }}
            port:
              number: {{ template "ccr.backend.port" . }}
      - path: /chartrepo/
        backend:
          service:
            name: {{ template "ccr.name" . }}-harbor-addon
            port:
              number: {{ .Values.ccr.harborAddon.containerPort }}
      - path: /api/chartrepo/
        backend:
          service:
            name: {{ template "ccr.name" . }}-harbor-addon
            port:
              number: {{ .Values.ccr.harborAddon.containerPort }}
      - path: /addon/v1/blobs/
        backend:
          service:
            name: {{ template "ccr.name" . }}-harbor-addon
            port:
              number: {{ .Values.ccr.harborAddon.containerPort }}
{{- end }}
    host: {{ .Values.ccr.privateDomain.name }}

{{- end -}}