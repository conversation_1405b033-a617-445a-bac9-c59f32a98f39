apiVersion: v1
kind: Secret
metadata:
  name: ccr-cipher
  labels:
{{ include "ccr.labels" . | indent 4 }}
data:
  {{- if or (empty .Values.ccr.jwtTlsCrt) (empty .Values.ccr.jwtTlsKey) }}
  {{- $ca := genCA .Release.Namespace 36500 }}
  {{- $cert := genSignedCert .Release.Name nil nil 36500 $ca }}
  ca.crt: {{ $ca.Cert | b64enc | quote }}
  tls.crt: {{ $cert.Cert | b64enc | quote }}
  tls.key: {{ $cert.Key | b64enc | quote }}
  {{- else }}
  tls.crt: {{ .Values.ccr.jwtTlsCrt }}
  tls.key: {{ .Values.ccr.jwtTlsKey }}
  {{- end }}