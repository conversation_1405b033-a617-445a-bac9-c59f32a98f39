apiVersion: v1
kind: Secret
metadata:
  name: domain-cert
  labels:
{{ include "ccr.labels" . | indent 4 }}
data:
  {{- with .Values.ccr.domainCert }}
  {{- if and .key .cert }}
  tls.crt: {{ .cert }}
  tls.key: {{ .key }}
  {{- else }}
  {{- $ca := genCA "ccr-ca" 36500 }}
  {{- $cert := genSignedCert .name nil nil 36500 $ca }}
  ca.crt: {{ $ca.Cert | b64enc | quote }}
  tls.crt: {{ $cert.Cert | b64enc | quote }}
  tls.key: {{ $cert.Key | b64enc | quote }}
  {{- end }}
  {{- end }}