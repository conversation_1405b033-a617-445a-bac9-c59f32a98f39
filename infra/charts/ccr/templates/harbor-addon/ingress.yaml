{{- if .Values.ccr.privateDomain -}}
{{- if semverCompare "<1.14-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: extensions/v1beta1
{{- else if semverCompare "<1.19-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: networking.k8s.io/v1beta1
{{- else }}
apiVersion: networking.k8s.io/v1
{{- end }}
kind: Ingress
metadata:
  name: {{ template "ccr.name" . }}-harbor-addon
  labels:
{{ include "ccr.labels" . | indent 4 }}
    component: harbor-addon
spec:
  tls:
  - secretName: ""
    hosts:
    - {{ .Values.harbor.expose.ingress.hosts.core }}
  rules:
  - http:
      paths:
{{- if semverCompare "<1.19-0" .Capabilities.KubeVersion.GitVersion }}
      - path: /addon/v1
        backend:
          serviceName: {{ template "ccr.name" . }}-harbor-addon
          servicePort: {{ .Values.ccr.harborAddon.containerPort }}
{{- else }}
      - path: /addon/v1/
        pathType: Prefix
        backend:
          service:
            name: {{ template "ccr.name" . }}-harbor-addon
            port:
              number: {{ .Values.ccr.harborAddon.containerPort }}
{{- end }}
    host: {{ .Values.harbor.expose.ingress.hosts.core }}
{{- end -}}