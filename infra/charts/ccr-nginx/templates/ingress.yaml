{{- if .Values.ingress.enabled }}
apiVersion: {{ template "ccr-nginx.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ include "ccr-nginx.fullname" . }}
  labels:
  {{- include "ccr-nginx.labels" . | nindent 4 }}
spec:
  {{- if .Values.ingress.tls }}
  tls:
  {{ toYaml .Values.ingress.tls | indent 4 }}
  {{- end }}
  rules:
  {{- if .Values.ingress.hostname }}
  - host: {{ .Values.ingress.hostname }}
  - http:
      paths:
    {{- if semverCompare "<1.19-0" .Capabilities.KubeVersion.GitVersion }}
      - path: /
        backend:
          serviceName: "{{ template "ccr-nginx.fullname" $ }}"
          servicePort: {{ .Values.service.port }}
    {{- else }}
      - path: /
        pathType: Prefix
        backend:
          service:
            name: "{{ template "ccr-nginx.fullname" $ }}"
            port:
              number: {{ .Values.service.port }}
    {{- end }}
  {{- end }}
{{- end }}