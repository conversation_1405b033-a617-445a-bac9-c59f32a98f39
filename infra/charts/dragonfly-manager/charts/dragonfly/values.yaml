# Dragonfly Helm Chart Values.
#

global:
  # -- Image pull secrets.
  imagePullSecrets: []

# -- Override dragonfly name.
nameOverride: ""
# -- Override dragonfly fullname.
fullnameOverride: ""
# -- Install application cluster domain.
clusterDomain: "cluster.local"

# -- [Experimental] Container runtime support.
# Choose special container runtime in Kubernetes.
# Support: Containerd, Docker, CRI-O.
containerRuntime:
  # -- [Experimental] Support docker, when use docker-shim in Kubernetes, please set containerRuntime.docker.enable to true.
  # For supporting docker, we need generate CA and update certs, then hijack registries traffic,
  # By default, it's unnecessary to restart docker daemon when pull image from private registries,
  # this feature is support explicit registries in containerRuntime.registry.domains, default domains is ghcr.io, quay.io,
  # please update your registries by `--set containerRuntime.registry.domains='{harbor.example.com,harbor.example.net}' --set containerRuntime.registry.injectHosts=true --set containerRuntime.docker.enable=true`.
  # Caution:
  #   **We did not recommend to using dragonfly with docker in Kubernetes** due to many reasons: 1. no fallback image pulling policy. 2. deprecated in Kubernetes.
  #   Because the original `daemonset` in Kubernetes did not support `Surging Rolling Update` policy.
  #   When kill current dfdaemon pod, the new pod image can not be pulled anymore.
  #   If you can not change runtime from docker to others, remind to choose a plan when upgrade dfdaemon:
  #     Option 1: pull newly dfdaemon image manually before upgrade dragonfly, or use [ImagePullJob](https://openkruise.io/docs/user-manuals/imagepulljob) to pull image automate.
  #     Option 2: keep the image registry of dragonfly is different from common registries and add host in `containerRuntime.docker.skipHosts`.
  # Caution: docker hub image is not supported without restart docker daemon.
  # When need pull image from docker hub or any other registries not in containerRuntime.registry.domains, set containerRuntime.docker.restart=true
  # this feature will inject proxy config into docker.service and restart docker daemon.
  # Caution: set restart to true only when live restore is enable.
  # Requirement: Docker Engine v1.2.0+ without Rootless.
  docker:
    # -- Enable docker support.
    # Inject ca cert into /etc/docker/certs.d/, Refer: https://docs.docker.com/engine/security/certificates/.
    enable: false
    # -- Restart docker daemon to redirect traffic to dfdaemon.
    # When containerRuntime.docker.restart=true, containerRuntime.docker.injectHosts and containerRuntime.registry.domains is ignored.
    # If did not want restart docker daemon, keep containerRuntime.docker.restart=false and containerRuntime.docker.injectHosts=true.
    restart: false
    # -- Skip hosts.
    # Some traffic did not redirect to dragonfly, like 127.0.0.1, and the image registries of dragonfly itself.
    # The format likes NO_PROXY in golang, refer: https://github.com/golang/net/blob/release-branch.go1.15/http/httpproxy/proxy.go#L39.
    # Caution: Some registries use s3 or oss for backend storage, when add registries to skipHosts,
    # don't forget add the corresponding backend storage.
    skipHosts:
      - "127.0.0.1"
      # Dragonfly use this image registry to upgrade itself, so we need skip it. Change it in real environment.
      - "docker.io"
    # -- Inject domains into /etc/hosts to force redirect traffic to dfdaemon.
    # Caution: This feature need dfdaemon to implement SNI Proxy, confirm image tag is greater than or equal to v2.0.0.
    # When use certs and inject hosts in docker, no necessary to restart docker daemon.
    injectHosts: true
    # -- Skip verify remote tls cert in dfdaemon.
    # If registry cert is private or self-signed, set to true.
    # Caution: this option is test only. When deploy in production, should not skip verify tls cert.
    insecure: false
    # -- Registry domains.
    # By default, docker pull image via https, currently, by default 443 port with https. If not standard port, update registryPorts.
    registryDomains:
      - "ghcr.io"
      - "quay.io"
    # -- Registry ports.
    registryPorts:
      - 443
    # -- CA cert info for generating.
    caCert:
      countryName: CN
      stateOrProvinceName: Hangzhou
      localityName: Hangzhou
      organizationName: Dragonfly
      commonName: "Dragonfly Authority CA"
  # -- [Experimental] Containerd support.
  containerd:
    # -- Enable containerd support.
    # Inject mirror config into ${containerRuntime.containerd.configPathDir}/config.toml,
    # if config_path is enabled in ${containerRuntime.containerd.configPathDir}/config.toml, the config take effect real time,
    # but if config_path is not enabled in ${containerRuntime.containerd.configPathDir}/config.toml, need restart containerd to take effect.
    # When the version in ${containerRuntime.containerd.configPathDir}/config.toml is "1", inject dfdaemon.config.proxy.registryMirror.url as registry mirror and restart containerd.
    # When the version in ${containerRuntime.containerd.configPathDir}/config.toml is "2":
    #   1. when config_path is enabled in ${containerRuntime.containerd.configPathDir}/config.toml, inject containerRuntime.containerd.registries into config_path,
    #   2. when containerRuntime.containerd.injectConfigPath=true, inject config_path into ${containerRuntime.containerd.configPathDir}/config.toml and inject containerRuntime.containerd.registries into config_path,
    #   3. when not config_path in ${containerRuntime.containerd.configPathDir}/config.toml and containerRuntime.containerd.injectConfigPath=false, inject dfdaemon.config.proxy.registryMirror.url as registry mirror and restart containerd.
    enable: false
    # -- Config path for multiple registries.
    # By default, init container will check ${containerRuntime.containerd.configPathDir}/config.toml, whether is config_path configured,
    # if not, init container will just add the dfdaemon.config.proxy.registryMirror.url for registry mirror.
    # When configPath is true, init container will inject config_path=${containerRuntime.containerd.configPathDir}/certs.d and configure all registries.
    injectConfigPath: false
    # -- Custom config path directory, default is /etc/containerd.
    # e.g. rke2 generator config path is /var/lib/rancher/rke2/agent/etc/containerd/config.toml, docs: https://github.com/rancher/rke2/blob/master/docs/advanced.md#configuring-containerd.
    configPathDir: /etc/containerd
    # -- Custom config file name, default is config.toml.
    # This is workaround for kops provider, see https://github.com/kubernetes/kops/pull/13090 for more details.
    configFileName: ""
    # -- Credencials for authenticating to private registries.
    # By default this is aplicable for single registry mode, for reference see docs: https://github.com/containerd/containerd/blob/v1.6.4/docs/cri/registry.md#configure-registry-credentials.
    injectRegistryCredencials:
      enable: false
      username: ""
      password: ""
      auth: ""
      identitytoken: ""
    # Registries full urls.
    # Only used when config_path is enabled in ${containerRuntime.containerd.configPathDir}/config.toml or containerRuntime.containerd.injectConfigPath=true.
    registries:
      - "https://ghcr.io"
      - "https://quay.io"
      - "https://harbor.example.com:8443"
      - "ccr-1xxxxxx5-vpc.cnc.gz.baidubce.com"
  # -- [Experimental] CRI-O support.
  crio:
    # -- Enable CRI-O support.
    # Inject drop-in mirror config into /etc/containers/registries.conf.d.
    enable: false
    # Registries full urls.
    registries:
      - "https://ghcr.io"
      - "https://quay.io"
      - "https://harbor.example.com:8443"
  # -- The image name of init container, need include openssl for ca generating.
  initContainerImage: dragonflyoss/openssl
  # -- Additional init containers.
  extraInitContainers: []
  # - name: update-containerd.
  #   image: dragonflyoss/openssl.
  #   command: ['echo', 'hey'].

scheduler:
  # -- Enable scheduler.
  enable: true
  # -- Scheduler name.
  name: scheduler
  # -- Override scheduler name.
  nameOverride: ""
  # -- Override scheduler fullname.
  fullnameOverride: ""
  # -- Number of Pods to launch.
  replicas: 3
  # -- Image repository.
  image: dragonflyoss/scheduler
  # -- Image tag.
  tag: v2.1.0
  # -- Image pull policy.
  pullPolicy: IfNotPresent
  # -- Image pull secrets.
  # @default -- `[]` (defaults to global.imagePullSecrets).
  pullSecrets: []
  # -- Host Aliases.
  hostAliases: []
  # -- Pod resource requests and limits.
  resources:
    requests:
      cpu: "0"
      memory: "0"
    limits:
      cpu: "4"
      memory: "8Gi"
  service:
    # -- Service type.
    type: ClusterIP
    # -- Service labels.
    labels: {}
    # -- Service annotations.
    annotations: {}
  # -- Pod priorityClassName.
  priorityClassName: ""
  # -- Node labels for pod assignment.
  nodeSelector: {}
  # -- Pod terminationGracePeriodSeconds.
  terminationGracePeriodSeconds:
  # -- List of node taints to tolerate.
  tolerations: []
  # -- Pod annotations.
  podAnnotations: {}
  # -- Pod labels.
  podLabels: {}
  # -- Statefulset annotations.
  statefulsetAnnotations: {}
  # -- Pod containerPort.
  containerPort: 8002
  # -- Extra volumes for scheduler.
  extraVolumes:
    - name: logs
      emptyDir: {}
  # -- Extra volumeMounts for scheduler.
  extraVolumeMounts:
    - name: logs
      mountPath: "/var/log/dragonfly/scheduler"
  initContainer:
    # -- Init container image repository.
    image: busybox
    # -- Init container image tag.
    tag: latest
    # -- Container image pull policy.
    pullPolicy: IfNotPresent
  config:
    server:
      # -- Advertise ip.
      advertiseIP: ""
      # -- Advertise port.
      advertisePort: 8002
      # -- Listen ip.
      listenIP: "0.0.0.0"
      # -- Server port.
      port: 8002
      # -- Work directory.
      workHome: ""
      # -- Log directory.
      logDir: ""
      # -- Dynconfig cache directory.
      cacheDir: ""
      # -- Plugin directory.
      pluginDir: ""
      # -- Storage directory.
      dataDir: ""
    scheduler:
      # -- Algorithm configuration to use different scheduling algorithms,
      # default configuration supports "default" and "ml".
      # "default" is the rule-based scheduling algorithm, "ml" is the machine learning scheduling algorithm.
      # It also supports user plugin extension, the algorithm value is "plugin",
      # and the compiled `d7y-scheduler-plugin-evaluator.so` file is added to
      # the dragonfly working directory plugins.
      algorithm: default
      # -- backToSourceCount is single task allows the peer to back-to-source count.
      backToSourceCount: 3
      # -- retryBackToSourceLimit reaches the limit, then the peer back-to-source.
      retryBackToSourceLimit: 5
      # -- Retry scheduling limit times.
      retryLimit: 10
      # -- Retry scheduling interval.
      retryInterval: 50ms
      gc:
        # -- pieceDownloadTimeout is the timeout of downloading piece.
        pieceDownloadTimeout: 30m
        # peerGCInterval is the interval of peer gc.
        peerGCInterval: 10s
        # -- peerTTL is the ttl of peer. If the peer has been downloaded by other peers,
        # then PeerTTL will be reset.
        peerTTL: 24h
        # -- taskGCInterval is the interval of task gc. If all the peers have been reclaimed in the task,
        # then the task will also be reclaimed.
        taskGCInterval: 30m
        # -- hostGCInterval is the interval of host gc.
        hostGCInterval: 6h
        # -- hostTTL is time to live of host. If host announces message to scheduler,
        # then HostTTl will be reset.
        hostTTL: 1h
    # -- resource configuration.
    resource:
      # -- task configuration.
      task:
        # -- downloadTiny is the configuration of downloading tiny task by scheduler.
        downloadTiny:
          # -- scheme is download tiny task scheme.
          scheme: http
          # -- timeout is http request timeout.
          timeout: 1m
          # -- tls is download tiny task TLS configuration.
          tls:
            # -- insecureSkipVerify controls whether a client verifies the
            # server's certificate chain and hostname.
            insecureSkipVerify: true
    dynconfig:
      # -- Type is deprecated and is no longer used.
      # Please remove it from your configuration.
      type: manager
      # -- Dynamic config refresh interval.
      refreshInterval: 1m
    host:
      # -- IDC is the idc of scheduler instance.
      idc: ""
      # -- Location is the location of scheduler instance.
      location: ""
    manager:
      # -- Associated scheduler cluster id.
      schedulerClusterID: 1
      keepAlive:
        # -- Manager keepalive interval.
        interval: 5s
    seedPeer:
      # -- scheduler enable seed peer as P2P peer,
      # if the value is false, P2P network will not be back-to-source through
      # seed peer but by dfdaemon and preheat feature does not work.
      enable: true
    storage:
      # -- maxSize sets the maximum size in megabytes of storage file.
      maxSize: 100
      # -- maxBackups sets the maximum number of storage files to retain.
      maxBackups: 10
      # -- bufferSize sets the size of buffer container,
      # if the buffer is full, write all the records in the buffer to the file.
      bufferSize: 100
    security:
      # -- AutoIssueCert indicates to issue client certificates for all grpc call.
      # If AutoIssueCert is false, any other option in Security will be ignored.
      autoIssueCert: false
      # -- CACert is the root CA certificate for all grpc tls handshake, it can be path or PEM format string.
      caCert: ""
      # -- TLSVerify indicates to verify certificates.
      tlsVerify: false
      # -- TLSPolicy controls the grpc shandshake behaviors:
      #   force: both ClientHandshake and ServerHandshake are only support tls.
      #   prefer: ServerHandshake supports tls and insecure (non-tls), ClientHandshake will only support tls.
      #   default: ServerHandshake supports tls and insecure (non-tls), ClientHandshake will only support insecure (non-tls).
      # Notice: If the drgaonfly service has been deployed, a two-step upgrade is required.
      # The first step is to set tlsPolicy to default, and then upgrade the dragonfly services.
      # The second step is to set tlsPolicy to prefer, and tthen completely upgrade the dragonfly services.
      tlsPolicy: "prefer"
      certSpec:
        # -- DNSNames is a list of dns names be set on the certificate.
        dnsNames:
          - "dragonfly-scheduler"
          - "dragonfly-scheduler.dragonfly-system.svc"
          - "dragonfly-scheduler.dragonfly-system.svc.cluster.local"
        # -- IPAddresses is a list of ip addresses be set on the certificate.
        ipAddresses:
        # -- ValidityPeriod is the validity period  of certificate.
        validityPeriod: 4320h
    network:
      # -- enableIPv6 enables ipv6.
      enableIPv6: false
    # -- Console shows log on console.
    console: false
    # -- Whether to enable debug level logger and enable pprof.
    verbose: false
    # -- Listen port for pprof, only valid when the verbose option is true.
    # default is -1. If it is 0, pprof will use a random port.
    pprofPort: -1
    # - Jaeger endpoint url, like: http://jaeger.dragonfly.svc:14268/api/traces.
    jaeger: ""
  metrics:
    # -- Enable scheduler metrics.
    enable: false
    # -- Enable host metrics.
    enableHost: false
    service:
      # -- Service type.
      type: ClusterIP
      # -- Service labels.
      labels: {}
      # -- Service annotations.
      annotations: {}
    serviceMonitor:
      # -- Enable prometheus service monitor.
      # ref: https://github.com/coreos/prometheus-operator.
      enable: false
      # -- Additional labels.
      additionalLabels: {}
      # -- Interval at which metrics should be scraped.
      interval: 30s
      # -- Timeout after which the scrape is ended.
      scrapeTimeout: 10s
    prometheusRule:
      # -- Enable prometheus rule
      # ref: https://github.com/coreos/prometheus-operator.
      enable: false
      # -- Additional labels.
      additionalLabels: {}
      # -- Prometheus rules.
      rules:
        - alert: SchedulerDown
          expr: sum(dragonfly_scheduler_version{}) == 0
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Scheduler instance is down
            message: Scheduler instance {{ "{{ $labels.instance }}" }} is down
        - alert: SchedulerHighNumberOfFailedDownloadPeer
          expr: sum(increase(dragonfly_scheduler_download_peer_finished_failure_total{}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Scheduler has a high number of failed download peer
            message: Scheduler has a high number of failed download peer
        - alert: SchedulerSuccessRateOfDownloadingPeer
          expr: (sum(rate(dragonfly_scheduler_download_peer_finished_total{}[1m])) - sum(rate(dragonfly_scheduler_download_peer_finished_failure_total{}[1m]))) / sum(rate(dragonfly_scheduler_download_peer_finished_total{}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Scheduler's success rate of downloading peer is low
            message: Scheduler's success rate of downloading peer is low
        - alert: SchedulerHighNumberOfFailedRegisterPeer
          expr: sum(increase(dragonfly_scheduler_register_peer_failure_total{}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Scheduler has a high number of failed register peer
            message: Scheduler has a high number of failed register peer
        - alert: SchedulerSuccessRateOfRegisterTask
          expr: (sum(rate(dragonfly_scheduler_register_peer_total{}[1m])) - sum(rate(dragonfly_scheduler_register_peer_failure_total{}[1m]))) / sum(rate(dragonfly_scheduler_register_peer_total{}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Scheduler's success rate of register peer is low
            message: Scheduler's success rate of register peer is low
        - alert: SchedulerHighNumberOfFailedLeavePeer
          expr: sum(increase(dragonfly_scheduler_leave_peer_failure_total{}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Scheduler has a high number of failed leave peer
            message: Scheduler has a high number of failed leave peer
        - alert: SchedulerSuccessRateOfLeavingPeer
          expr: (sum(rate(dragonfly_scheduler_leave_peer_total{}[1m])) - sum(rate(dragonfly_scheduler_leave_peer_failure_total{}[1m]))) / sum(rate(dragonfly_scheduler_leave_peer_total{}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Scheduler's success rate of leaving peer is low
            message: Scheduler's success rate of leaving peer is low
        - alert: SchedulerHighNumberOfFailedStatTask
          expr: sum(increase(dragonfly_scheduler_stat_task_failure_total{}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Scheduler has a high number of failed stat task
            message: Scheduler has a high number of failed stat task
        - alert: SchedulerSuccessRateOfStatTask
          expr: (sum(rate(dragonfly_scheduler_stat_task_total{}[1m])) - sum(rate(dragonfly_scheduler_stat_task_failure_total{}[1m]))) / sum(rate(dragonfly_scheduler_stat_task_total{}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Scheduler's success rate of stat task is low
            message: Scheduler's success rate of stat task is low
        - alert: SchedulerHighNumberOfFailedAnnouncePeer
          expr: sum(increase(dragonfly_scheduler_announce_peer_failure_total{}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Scheduler has a high number of failed announce peer
            message: Scheduler has a high number of failed announce peer
        - alert: SchedulerSuccessRateOfAnnouncingPeer
          expr: (sum(rate(dragonfly_scheduler_announce_peer_total{}[1m])) - sum(rate(dragonfly_scheduler_announce_peer_failure_total{}[1m]))) / sum(rate(dragonfly_scheduler_announce_peer_total{}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Scheduler's success rate of announcing peer is low
            message: Scheduler's success rate of announcing peer is low
        - alert: SchedulerHighNumberOfFailedLeaveHost
          expr: sum(increase(dragonfly_scheduler_leave_host_failure_total{}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Scheduler has a high number of failed leave host
            message: Scheduler has a high number of failed leave host
        - alert: SchedulerSuccessRateOfLeavingHost
          expr: (sum(rate(dragonfly_scheduler_leave_host_total{}[1m])) - sum(rate(dragonfly_scheduler_leave_host_failure_total{}[1m]))) / sum(rate(dragonfly_scheduler_leave_host_total{}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Scheduler's success rate of leaving host is low
            message: Scheduler's success rate of leaving host is low
        - alert: SchedulerHighNumberOfFailedAnnounceHost
          expr: sum(increase(dragonfly_scheduler_announce_host_failure_total{}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Scheduler has a high number of failed annoucne host
            message: Scheduler has a high number of failed annoucne host
        - alert: SchedulerSuccessRateOfAnnouncingHost
          expr: (sum(rate(dragonfly_scheduler_announce_host_total{}[1m])) - sum(rate(dragonfly_scheduler_announce_host_failure_total{}[1m]))) / sum(rate(dragonfly_scheduler_announce_host_total{}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Scheduler's success rate of announcing host is low
            message: Scheduler's success rate of announcing host is low
        - alert: SchedulerHighNumberOfFailedGRPCRequest
          expr: sum(rate(grpc_server_started_total{grpc_service="scheduler.Scheduler",grpc_type="unary"}[1m])) - sum(rate(grpc_server_handled_total{grpc_service="scheduler.Scheduler",grpc_type="unary",grpc_code="OK"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="scheduler.Scheduler",grpc_type="unary",grpc_code="NotFound"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="scheduler.Scheduler",grpc_type="unary",grpc_code="PermissionDenied"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="scheduler.Scheduler",grpc_type="unary",grpc_code="InvalidArgument"}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Scheduler has a high number of failed grpc request
            message: Scheduler has a high number of failed grpc request
        - alert: SchedulerSuccessRateOfGRPCRequest
          expr: (sum(rate(grpc_server_handled_total{grpc_service="scheduler.Scheduler",grpc_type="unary",grpc_code="OK"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="scheduler.Scheduler",grpc_type="unary",grpc_code="NotFound"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="scheduler.Scheduler",grpc_type="unary",grpc_code="PermissionDenied"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="scheduler.Scheduler",grpc_type="unary",grpc_code="InvalidArgument"}[1m]))) / sum(rate(grpc_server_started_total{grpc_service="scheduler.Scheduler",grpc_type="unary"}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Scheduler's success rate of grpc request is low
            message: Scheduler's success rate of grpc request is low

seedPeer:
  # -- Enable dfdaemon seed peer.
  enable: true
  # -- Seed peer name.
  name: seed-peer
  # -- Override scheduler name.
  nameOverride: ""
  # -- Override scheduler fullname.
  fullnameOverride: ""
  # -- Number of Pods to launch.
  replicas: 3
  # -- Image repository.
  image: dragonflyoss/dfdaemon
  # -- Image tag.
  tag: v2.1.0
  # -- Image pull policy.
  pullPolicy: IfNotPresent
  # -- Image pull secrets.
  # @default -- `[]` (defaults to global.imagePullSecrets).
  pullSecrets: []
  # -- Host Aliases.
  hostAliases: []
  # -- Pod resource requests and limits.
  resources:
    requests:
      cpu: "0"
      memory: "0"
    limits:
      cpu: "2"
      memory: "4Gi"
  # -- Pod priorityClassName.
  priorityClassName: ""
  # -- Node labels for pod assignment.
  nodeSelector: {}
  # -- Pod terminationGracePeriodSeconds.
  terminationGracePeriodSeconds:
  # -- List of node taints to tolerate.
  tolerations: []
  # -- Pod annotations.
  podAnnotations: {}
  # -- Pod labels.
  podLabels: {}
  # -- Statefulset annotations.
  statefulsetAnnotations: {}
  initContainer:
    # -- Init container image repository.
    image: busybox
    # -- Init container image tag.
    tag: latest
    # -- Container image pull policy.
    pullPolicy: IfNotPresent
  # -- Extra volumes for dfdaemon.
  extraVolumes:
    - name: logs
      emptyDir: {}
  # -- Extra volumeMounts for dfdaemon.
  extraVolumeMounts:
    - name: logs
      mountPath: "/var/log/dragonfly/daemon"
  persistence:
    # -- Enable persistence for seed peer.
    enable: true
    # -- Persistence annotations.
    annotations: {}
    # -- Persistence access modes.
    accessModes:
      - ReadWriteOnce
    # -- Persistence persistence size.
    size: 8Gi
    ## Seed peer data Persistent Volume Storage Class.
    ## If defined, storageClassName: <storageClass>.
    ## If set to "-", storageClassName: "", which disables dynamic provisioning.
    ## If undefined (the default) or set to null, no storageClassName spec is
    ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
    ##   GKE, AWS & OpenStack).
    ##
    # storageClass: "-".
  config:
    # -- Daemon alive time, when sets 0s, daemon will not auto exit, it is useful for longtime running.
    aliveTime: 0s
    # -- Daemon gc task running interval.
    gcInterval: 1m0s
    # -- When daemon exit, keep peer task data or not.
    # it is usefully when upgrade daemon service, all local cache will be saved.
    # default is false.
    keepStorage: false
    # -- Work directory.
    workHome: ""
    # -- Log directory.
    logDir: ""
    # -- Dynconfig cache directory.
    cacheDir: ""
    # -- Plugin directory.
    pluginDir: ""
    # -- Daemon data storage directory.
    dataDir: "/var/lib/dragonfly"
    # -- Console shows log on console.
    console: false
    # -- Whether to enable debug level logger and enable pprof.
    verbose: false
    # -- Listen port for pprof, only valid when the verbose option is true
    # default is -1. If it is 0, pprof will use a random port.
    pprofPort: -1
    # - Jaeger endpoint url, like: http://jaeger.dragonfly.svc:14268/api/traces.
    jaeger: ""
    health:
      tcpListen:
        port: 40901
      path: /server/ping
    host:
      # -- IDC deployed by daemon.
      idc: ""
      # -- Geographical location, separated by "|" characters.
      location: ""
    # -- Scheduler config, netAddrs is auto-configured in templates/dfdaemon/dfdaemon-configmap.yaml.
    scheduler:
      manager:
        # -- Get scheduler list dynamically from manager.
        enable: true
        # -- Manager service address, netAddr is a list, there are two fields type and addr.
        netAddrs:
        # -- Scheduler list refresh interval.
        refreshInterval: 10m
        seedPeer:
          # -- Enable seed peer mode.
          enable: true
          # -- Seed peer supports "super", "strong" and "weak" types.
          type: "super"
          # -- Associated seed peer cluster id.
          clusterID: 1
          keepAlive:
            # -- Manager keepalive interval.
            interval: 5s
      # -- Schedule timeout.
      scheduleTimeout: 30s
      # -- Disable auto back source in dfdaemon.
      disableAutoBackSource: false
    download:
      # -- Total download limit per second.
      totalRateLimit: 2048Mi
      # -- Per peer task limit per second.
      perPeerRateLimit: 1024Mi
      # -- Calculate digest, when only pull images, can be false to save cpu and memory.
      calculateDigest: true
      # -- When request data with range header, prefetch data not in range.
      prefetch: false
      downloadGRPC:
        # -- Download grpc security option.
        security:
          insecure: true
          tlsVerify: true
        # -- Download service listen address.
        # current, only support unix domain socket.
        unixListen:
          socket: ""
      peerGRPC:
        # -- Peer grpc security option.
        security:
          insecure: true
        tcpListen:
          # -- Listen port.
          port: 65000
    upload:
      # -- Upload limit per second.
      rateLimit: 2048Mi
      # -- Upload grpc security option.
      security:
        insecure: true
        tlsVerify: false
      tcpListen:
        # -- Listen port.
        port: 65002
    objectStorage:
      # -- Enable object storage service.
      enable: false
      # -- Filter is used to generate a unique Task ID by
      # filtering unnecessary query params in the URL,
      # it is separated by & character.
      # When filter: "Expires&Signature&ns", for example:
      #  http://localhost/xyz?Expires=111&Signature=222&ns=docker.io and http://localhost/xyz?Expires=333&Signature=999&ns=docker.io
      # is same task.
      filter: "Expires&Signature&ns"
      # -- MaxReplicas is the maximum number of replicas of an object cache in seed peers.
      maxReplicas: 3
      # -- Object storage service security option.
      security:
        insecure: true
        tlsVerify: true
      tcpListen:
        # -- Listen port.
        port: 65004
    storage:
      # -- Task data expire time.
      # when there is no access to a task data, this task will be gc.
      taskExpireTime: 6h
      # -- Storage strategy when process task data.
      # io.d7y.storage.v2.simple : download file to data directory first, then copy to output path, this is default action.
      #                           the download file in date directory will be the peer data for uploading to other peers.
      # io.d7y.storage.v2.advance: download file directly to output path with postfix, hard link to final output,
      #                            avoid copy to output path, fast than simple strategy, but:
      #                            the output file with postfix will be the peer data for uploading to other peers.
      #                            when user delete or change this file, this peer data will be corrupted.
      # default is io.d7y.storage.v2.advance.
      strategy: io.d7y.storage.v2.simple
      # -- Set to ture for reusing underlying storage for same task id.
      multiplex: true
      # -- Disk GC Threshold Percent, when the disk usage is above 90%, start to gc the oldest tasks.
      diskGCThresholdPercent: 90
    proxy:
      # -- Filter for hash url.
      # when defaultFilter: "Expires&Signature&ns", for example:
      # http://localhost/xyz?Expires=111&Signature=222&ns=docker.io and http://localhost/xyz?Expires=333&Signature=999&ns=docker.io
      # is same task, it is also possible to override the default filter by adding the X-Dragonfly-Filter header through the proxy.
      defaultFilter: "Expires&Signature&ns"
      # -- Tag the task.
      # when the value of the default tag is different,
      # the same download url can be divided into different tasks according to the tag,
      # it is also possible to override the default tag by adding
      # the X-Dragonfly-Tag header through the proxy.
      defaultTag: ''
      # -- Proxy security option.
      security:
        insecure: true
        tlsVerify: false
      tcpListen:
        # -- Namespace stands the linux net namespace, like /proc/1/ns/net.
        # it's useful for running daemon in pod with ip allocated and listening the special port in host net namespace.
        # Linux only.
        namespace: /run/dragonfly/net
        # If you want to change port, please update hostPort in $.Values.dfdaemon.hostPort.
        # port in configmap is generated from $.Values.dfdaemon.hostPort.
        # port: 65001.
      registryMirror:
        # -- When enabled, use value of "X-Dragonfly-Registry" in http header for remote instead of url host.
        dynamic: true
        # -- URL for the registry mirror.
        url: https://index.docker.io
        # -- When the cert of above url is secure, set insecure to true.
        insecure: false
      proxies:
        # -- Proxy all http image layer download requests with dfget.
        - regx: blobs/sha256.*
    security:
      # -- AutoIssueCert indicates to issue client certificates for all grpc call.
      # If AutoIssueCert is false, any other option in Security will be ignored.
      autoIssueCert: false
      # -- CACert is the root CA certificate for all grpc tls handshake, it can be path or PEM format string.
      caCert: ""
      # -- TLSVerify indicates to verify certificates.
      tlsVerify: false
      # -- TLSPolicy controls the grpc shandshake behaviors:
      #   force: both ClientHandshake and ServerHandshake are only support tls.
      #   prefer: ServerHandshake supports tls and insecure (non-tls), ClientHandshake will only support tls.
      #   default: ServerHandshake supports tls and insecure (non-tls), ClientHandshake will only support insecure (non-tls).
      # Notice: If the drgaonfly service has been deployed, a two-step upgrade is required.
      # The first step is to set tlsPolicy to default, and then upgrade the dragonfly services.
      # The second step is to set tlsPolicy to prefer, and tthen completely upgrade the dragonfly services.
      tlsPolicy: "prefer"
      certSpec:
        # -- DNSNames is a list of dns names be set on the certificate.
        dnsNames:
          - "dragonfly-seed-peer"
          - "dragonfly-seed-peer.dragonfly-system.svc"
          - "dragonfly-seed-peer.dragonfly-system.svc.cluster.local"
        # -- IPAddresses is a list of ip addresses be set on the certificate.
        ipAddresses:
        # -- ValidityPeriod is the validity period  of certificate.
        validityPeriod: 4320h
    network:
      # -- enableIPv6 enables ipv6.
      enableIPv6: false
    announcer:
      # -- schedulerInterval is the interval of announcing scheduler.
      # Announcer will provide the scheduler with peer information for scheduling.
      # Peer information includes cpu, memory, etc.
      schedulerInterval: 30s
  metrics:
    # -- Enable seed peer metrics.
    enable: false
    service:
      # -- Service type.
      type: ClusterIP
      # -- Service labels.
      labels: {}
      # -- Service annotations.
      annotations: {}
    serviceMonitor:
      # -- Enable prometheus service monitor.
      # ref: https://github.com/coreos/prometheus-operator.
      enable: false
      # -- Additional labels
      additionalLabels: {}
      # -- Interval at which metrics should be scraped.
      interval: 30s
      # -- Timeout after which the scrape is ended.
      scrapeTimeout: 10s
    prometheusRule:
      # -- Enable prometheus rule
      # ref: https://github.com/coreos/prometheus-operator.
      enable: false
      # -- Additional labels.
      additionalLabels: {}
      # -- Prometheus rules.
      rules:
        - alert: SeedPeerDown
          expr: sum(dragonfly_dfdaemon_version{container="seed-peer"}) == 0
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Seed peer instance is down
            message: Seed peer instance {{ "{{ $labels.instance }}" }} is down
        - alert: SeedPeerHighNumberOfFailedDownloadTask
          expr: sum(increase(dragonfly_dfdaemon_seed_peer_download_failure_total{}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Seed peer has a high number of failed download task
            message: Seed peer has a high number of failed download task
        - alert: SeedPeerSuccessRateOfDownloadingTask
          expr: (sum(rate(dragonfly_dfdaemon_seed_peer_download_total{}[1m])) - sum(rate(dragonfly_dfdaemon_seed_peer_download_failure_total{}[1m]))) / sum(rate(dragonfly_dfdaemon_seed_peer_download_total{}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Seed peer's success rate of downloading task is low
            message: Seed peer's success rate of downloading task is low
        - alert: SeedPeerHighNumberOfFailedGRPCRequest
          expr: sum(rate(grpc_server_started_total{grpc_service="cdnsystem.Seeder",grpc_type="unary"}[1m])) - sum(rate(grpc_server_handled_total{grpc_service="cdnsystem.Seeder",grpc_type="unary",grpc_code="OK"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="cdnsystem.Seeder",grpc_type="unary",grpc_code="NotFound"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="cdnsystem.Seeder",grpc_type="unary",grpc_code="PermissionDenied"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="cdnsystem.Seeder",grpc_type="unary",grpc_code="InvalidArgument"}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Seed peer has a high number of failed grpc request
            message: Seed peer has a high number of failed grpc request
        - alert: SeedPeerSuccessRateOfGRPCRequest
          expr: (sum(rate(grpc_server_handled_total{grpc_service="cdnsystem.Seeder",grpc_type="unary",grpc_code="OK"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="cdnsystem.Seeder",grpc_type="unary",grpc_code="NotFound"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="cdnsystem.Seeder",grpc_type="unary",grpc_code="PermissionDenied"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="cdnsystem.Seeder",grpc_type="unary",grpc_code="InvalidArgument"}[1m]))) / sum(rate(grpc_server_started_total{grpc_service="cdnsystem.Seeder",grpc_type="unary"}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Seed peer's success rate of grpc request is low
            message: Seed peer's success rate of grpc request is low

dfdaemon:
  # -- Enable dfdaemon.
  enable: true
  # -- Dfdaemon name.
  name: dfdaemon
  # -- Override dfdaemon name.
  nameOverride: ""
  # -- Override dfdaemon fullname.
  fullnameOverride: ""
  # -- Image repository.
  image: dragonflyoss/dfdaemon
  # -- Image tag.
  tag: v2.1.0
  # -- Image pull policy.
  pullPolicy: IfNotPresent
  # -- Image pull secrets.
  # @default -- `[]` (defaults to global.imagePullSecrets).
  pullSecrets: []
  # -- Host Aliases.
  hostAliases: []
  # -- Pod resource requests and limits.
  resources:
    requests:
      cpu: "0"
      memory: "0"
    limits:
      cpu: "2"
      memory: "2Gi"
  # -- Pod priorityClassName.
  priorityClassName: ""
  # -- Node labels for pod assignment.
  nodeSelector: {}
  # -- Pod terminationGracePeriodSeconds.
  terminationGracePeriodSeconds:
  # -- List of node taints to tolerate.
  tolerations: []
  # -- Pod annotations.
  podAnnotations: {}
  # -- Pod labels.
  podLabels: {}
  # -- Daemonset annotations.
  daemonsetAnnotations: {}
  initContainer:
    # -- Init container image repository.
    image: busybox
    # -- Init container image tag.
    tag: latest
  # -- Extra volumes for dfdaemon.
  extraVolumes:
    - name: logs
      emptyDir: {}
  # -- Extra volumeMounts for dfdaemon.
  extraVolumeMounts:
    - name: logs
      mountPath: "/var/log/dragonfly/daemon"
  # -- Pod containerPort.
  containerPort: 65001
  # -- When .hostNetwork == false, and .config.proxy.tcpListen.namespace is empty.
  # many network add-ons do not yet support hostPort.
  # https://kubernetes.io/docs/setup/production-environment/tools/kubeadm/troubleshooting-kubeadm/#hostport-services-do-not-work
  # by default, dfdaemon injects the 65001 port to host network by sharing host network namespace,
  # if you want to use hostPort, please empty .config.proxy.tcpListen.namespace below, and keep .hostNetwork == false.
  # for performance, injecting the 65001 port to host network is better than hostPort.
  hostPort: 65001
  # -- Using hostNetwork when pod with host network can communicate with normal pods with cni network.
  hostNetwork: false
  # -- Mount data directory from host.
  # when enabled, mount host path to dfdaemon, or just emptyDir in dfdaemon.
  mountDataDirAsHostPath: false
  config:
    # -- Daemon alive time, when sets 0s, daemon will not auto exit, it is useful for longtime running.
    aliveTime: 0s
    # -- Daemon gc task running interval.
    gcInterval: 1m0s
    # -- When daemon exit, keep peer task data or not.
    # it is usefully when upgrade daemon service, all local cache will be saved.
    # default is false.
    keepStorage: false
    # -- Work directory.
    workHome: ""
    # -- Log directory.
    logDir: ""
    # -- Dynconfig cache directory.
    cacheDir: ""
    # -- Plugin directory.
    pluginDir: ""
    # -- Daemon data storage directory.
    dataDir: "/var/lib/dragonfly"
    # -- Console shows log on console.
    console: false
    # -- Whether to enable debug level logger and enable pprof.
    verbose: false
    # -- Listen port for pprof, only valid when the verbose option is true.
    # default is -1. If it is 0, pprof will use a random port.
    pprofPort: -1
    # - Jaeger endpoint url, like: http://jaeger.dragonfly.svc:14268/api/traces.
    jaeger: ""
    health:
      tcpListen:
        port: 40901
      path: /server/ping
    host:
      # -- IDC deployed by daemon.
      idc: ""
      # -- Geographical location, separated by "|" characters.
      location: ""
    # -- Scheduler config, netAddrs is auto-configured in templates/dfdaemon/dfdaemon-configmap.yaml.
    scheduler:
      manager:
        # -- Get scheduler list dynamically from manager.
        enable: true
        # -- Manager service address, netAddr is a list, there are two fields type and addr.
        netAddrs:
        # -- Scheduler list refresh interval.
        refreshInterval: 10m
        seedPeer:
          # -- Enable seed peer mode.
          enable: false
          # -- Seed peer supports "super", "strong" and "weak" types.
          type: "super"
          # -- Associated seed peer cluster id.
          clusterID: 1
      # -- Scheduler service address, netAddr is a list, there are two fields type and addr.
      #    Also set dfdaemon.config.scheduler.manager.enable to false to take effect.
      netAddrs:
      # -- Schedule timeout.
      scheduleTimeout: 30s
      # -- Disable auto back source in dfdaemon.
      disableAutoBackSource: false
    download:
      # -- Total download limit per second.
      totalRateLimit: 1024Mi
      # -- Per peer task limit per second.
      perPeerRateLimit: 512Mi
      # -- Calculate digest, when only pull images, can be false to save cpu and memory.
      calculateDigest: true
      # -- When request data with range header, prefetch data not in range.
      prefetch: false
      downloadGRPC:
        # -- Download grpc security option.
        security:
          insecure: true
          tlsVerify: true
        # -- Download service listen address.
        # current, only support unix domain socket.
        unixListen:
          socket: ""
      peerGRPC:
        # -- Peer grpc security option.
        security:
          insecure: true
        tcpListen:
          # -- Listen port.
          port: 65000
    upload:
      # -- Upload limit per second.
      rateLimit: 1024Mi
      # -- Upload grpc security option.
      security:
        insecure: true
        tlsVerify: false
      tcpListen:
        # -- Listen port.
        port: 65002
    objectStorage:
      # -- Enable object storage service.
      enable: false
      # -- Filter is used to generate a unique Task ID by
      # filtering unnecessary query params in the URL,
      # it is separated by & character.
      # When filter: "Expires&Signature&ns", for example:
      #  http://localhost/xyz?Expires=111&Signature=222&ns=docker.io and http://localhost/xyz?Expires=333&Signature=999&ns=docker.io
      # is same task.
      filter: "Expires&Signature&ns"
      # -- MaxReplicas is the maximum number of replicas of an object cache in seed peers.
      maxReplicas: 3
      # -- Object storage service security option.
      security:
        insecure: true
        tlsVerify: true
      tcpListen:
        # -- Listen port.
        port: 65004
    storage:
      # -- Task data expire time.
      # when there is no access to a task data, this task will be gc.
      taskExpireTime: 6h
      # -- Storage strategy when process task data.
      # io.d7y.storage.v2.simple : download file to data directory first, then copy to output path, this is default action
      #                           the download file in date directory will be the peer data for uploading to other peers.
      # io.d7y.storage.v2.advance: download file directly to output path with postfix, hard link to final output,
      #                            avoid copy to output path, fast than simple strategy, but:
      #                            the output file with postfix will be the peer data for uploading to other peers
      #                            when user delete or change this file, this peer data will be corrupted.
      # default is io.d7y.storage.v2.advance.
      strategy: io.d7y.storage.v2.simple
      # -- Set to ture for reusing underlying storage for same task id.
      multiplex: true
      # -- Disk GC Threshold.
      diskGCThreshold: 50Gi
    proxy:
      # -- Filter for hash url.
      # when defaultFilter: "Expires&Signature&ns", for example:
      # http://localhost/xyz?Expires=111&Signature=222&ns=docker.io and http://localhost/xyz?Expires=333&Signature=999&ns=docker.io
      # is same task, it is also possible to override the default filter by adding the X-Dragonfly-Filter header through the proxy.
      defaultFilter: "Expires&Signature&ns"
      # -- Tag the task.
      # when the value of the default tag is different,
      # the same download url can be divided into different tasks according to the tag,
      # it is also possible to override the default tag by adding
      # the X-Dragonfly-Tag header through the proxy.
      defaultTag: ''
      # -- Proxy security option.
      security:
        insecure: true
        tlsVerify: false
      tcpListen:
        # -- Namespace stands the linux net namespace, like /proc/1/ns/net.
        # it's useful for running daemon in pod with ip allocated and listening the special port in host net namespace.
        # Linux only.
        namespace: /run/dragonfly/net
        # If you want to change port, please update hostPort in $.Values.dfdaemon.hostPort.
        # port in configmap is generated from $.Values.dfdaemon.hostPort.
        # port: 65001.
      registryMirror:
        # -- When enabled, use value of "X-Dragonfly-Registry" in http header for remote instead of url host.
        dynamic: true
        # -- URL for the registry mirror.
        url: https://index.docker.io
        # -- When the cert of above url is secure, set insecure to true.
        insecure: false
      proxies:
        # -- Proxy all http image layer download requests with dfget.
        - regx: blobs/sha256.*
    security:
      # -- AutoIssueCert indicates to issue client certificates for all grpc call.
      # If AutoIssueCert is false, any other option in Security will be ignored.
      autoIssueCert: false
      # -- CACert is the root CA certificate for all grpc tls handshake, it can be path or PEM format string.
      caCert: ""
      # -- TLSVerify indicates to verify certificates.
      tlsVerify: false
      # -- TLSPolicy controls the grpc shandshake behaviors:
      #   force: both ClientHandshake and ServerHandshake are only support tls.
      #   prefer: ServerHandshake supports tls and insecure (non-tls), ClientHandshake will only support tls.
      #   default: ServerHandshake supports tls and insecure (non-tls), ClientHandshake will only support insecure (non-tls).
      # Notice: If the drgaonfly service has been deployed, a two-step upgrade is required.
      # The first step is to set tlsPolicy to default, and then upgrade the dragonfly services.
      # The second step is to set tlsPolicy to prefer, and tthen completely upgrade the dragonfly services.
      tlsPolicy: "prefer"
      certSpec:
        # -- DNSNames is a list of dns names be set on the certificate.
        dnsNames:
        # -- IPAddresses is a list of ip addresses be set on the certificate.
        ipAddresses:
        # -- ValidityPeriod is the validity period  of certificate.
        validityPeriod: 4320h
    network:
      # -- enableIPv6 enables ipv6.
      enableIPv6: false
    announcer:
      # -- schedulerInterval is the interval of announcing scheduler.
      # Announcer will provide the scheduler with peer information for scheduling.
      # Peer information includes cpu, memory, etc.
      schedulerInterval: 30s
  metrics:
    # -- Enable peer metrics.
    enable: false
    service:
      # -- Service type.
      type: ClusterIP
      # -- Service labels.
      labels: {}
      # -- Service annotations.
      annotations: {}
    serviceMonitor:
      # -- Enable prometheus service monitor.
      # ref: https://github.com/coreos/prometheus-operator.
      enable: false
      # -- Additional labels.
      additionalLabels: {}
      # -- Interval at which metrics should be scraped.
      interval: 30s
      # -- Timeout after which the scrape is ended.
      scrapeTimeout: 10s
    prometheusRule:
      # -- Enable prometheus rule.
      # ref: https://github.com/coreos/prometheus-operator.
      enable: false
      # -- Additional labels.
      additionalLabels: {}
      # -- Prometheus rules.
      rules:
        - alert: PeerDown
          expr: sum(dragonfly_dfdaemon_version{}) == 0
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Peer instance is down
            message: Peer instance {{ "{{ $labels.instance }}" }} is down
        - alert: PeerHighNumberOfFailedDownloadTask
          expr: sum(increase(dragonfly_dfdaemon_peer_task_failed_total{}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Peer has a high number of failed download task
            message: Peer has a high number of failed download task
        - alert: PeerSuccessRateOfDownloadingTask
          expr: (sum(rate(dragonfly_dfdaemon_peer_task_total{container="seed-peer"}[1m])) - sum(rate(dragonfly_dfdaemon_peer_task_failed_total{container="seed-peer"}[1m]))) / sum(rate(dragonfly_dfdaemon_peer_task_total{container="seed-peer"}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Peer's success rate of downloading task is low
            message: Peer's success rate of downloading task is low
        - alert: PeerHighNumberOfFailedGRPCRequest
          expr: sum(rate(grpc_server_started_total{grpc_service="dfdaemon.Daemon",grpc_type="unary"}[1m])) - sum(rate(grpc_server_handled_total{grpc_service="dfdaemon.Daemon",grpc_type="unary",grpc_code="OK"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="dfdaemon.Daemon",grpc_type="unary",grpc_code="NotFound"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="dfdaemon.Daemon",grpc_type="unary",grpc_code="PermissionDenied"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="dfdaemon.Daemon",grpc_type="unary",grpc_code="InvalidArgument"}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Peer has a high number of failed grpc request
            message: Peer has a high number of failed grpc request
        - alert: PeerSuccessRateOfGRPCRequest
          expr: (sum(rate(grpc_server_handled_total{grpc_service="dfdaemon.Daemon",grpc_type="unary",grpc_code="OK"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="dfdaemon.Daemon",grpc_type="unary",grpc_code="NotFound"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="dfdaemon.Daemon",grpc_type="unary",grpc_code="PermissionDenied"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="dfdaemon.Daemon",grpc_type="unary",grpc_code="InvalidArgument"}[1m]))) / sum(rate(grpc_server_started_total{grpc_service="dfdaemon.Daemon",grpc_type="unary"}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Peer's success rate of grpc request is low
            message: Peer's success rate of grpc request is low

manager:
  # -- Enable manager.
  enable: true
  # -- Manager name.
  name: manager
  # -- Override manager name.
  nameOverride: ""
  # -- Override manager fullname.
  fullnameOverride: ""
  # -- Number of Pods to launch.
  replicas: 3
  # -- Image repository.
  image: dragonflyoss/manager
  # -- Image tag.
  tag: v2.1.0
  # -- Image pull policy.
  pullPolicy: IfNotPresent
  # -- Image pull secrets.
  # @default -- `[]` (defaults to global.imagePullSecrets).
  pullSecrets: []
  # -- Host Aliases.
  hostAliases: []
  # -- Pod resource requests and limits.
  resources:
    requests:
      cpu: "0"
      memory: "0"
    limits:
      cpu: "2"
      memory: "4Gi"
  # -- Pod priorityClassName.
  priorityClassName: ""
  # -- Node labels for pod assignment.
  nodeSelector: {}
  # -- Pod terminationGracePeriodSeconds.
  terminationGracePeriodSeconds:
  # -- List of node taints to tolerate.
  tolerations: []
  # -- Pod annotations.
  podAnnotations: {}
  # -- Pod labels.
  podLabels: {}
  # -- Deployment annotations.
  deploymentAnnotations: {}
  # -- Extra volumes for manager.
  extraVolumes:
    - name: logs
      emptyDir: {}
  # -- Extra volumeMounts for manager.
  extraVolumeMounts:
    - name: logs
      mountPath: "/var/log/dragonfly/manager"
  # -- REST service port.
  restPort: 8080
  # -- GRPC service port.
  grpcPort: 65003
  service:
    # -- Service type.
    type: ClusterIP
    # -- Service labels.
    labels: {}
    # -- Service annotations.
    annotations: {}
  initContainer:
    # -- Init container image repository.
    image: busybox
    # -- Init container image tag.
    tag: latest
    # -- Container image pull policy.
    pullPolicy: IfNotPresent
  ingress:
    # -- Enable ingress.
    enable: false
    # -- Ingress class name. Requirement: kubernetes >=1.18.
    className: ""
    # -- Ingress annotations.
    annotations: {}
    # -- Ingress host path.
    path: /
    # -- Ingress path type. Requirement: kubernetes >=1.18.
    pathType: ImplementationSpecific
    # -- Manager ingress hosts.
    hosts: []
    # -- Ingress TLS configuration.
    tls: []
  config:
    server:
      grpc:
        # -- GRPC advertise ip.
        advertiseIP: ""
        # -- GRPC advertise port.
        advertisePort: 65003
      rest:
        tls:
          # -- Certificate file path.
          cert: ""
          # -- Key file path.
          key: ""
      # -- Work directory.
      workHome: ""
      # -- Log directory.
      logDir: ""
      # -- Dynconfig cache directory.
      cacheDir: ""
      # -- Plugin directory.
      pluginDir: ""
    auth:
      jwt:
        # -- Realm name to display to the user, default value is Dragonfly.
        realm: "Dragonfly"
        # -- Key is secret key used for signing, default value is
        # encoded base64 of dragonfly.
        # Please change the key in production.
        key: "ZHJhZ29uZmx5Cg=="
        # -- Timeout is duration that a jwt token is valid,
        # default duration is two days.
        timeout: 48h
        # -- MaxRefresh field allows clients to refresh their token
        # until MaxRefresh has passed, default duration is two days.
        maxRefresh: 48h
    cache:
      redis:
        # -- Redis cache TTL duration.
        ttl: 5m
      local:
        # -- Size of LFU cache.
        size: 200000
        # -- Local cache TTL duration.
        ttl: 3m
    job:
      # -- Preheat configuration.
      preheat:
        # -- registryTimeout is the timeout for requesting registry to get token and manifest.
        registryTimeout: 1m
      # # TLS configuration.
      # tls:
      #   # caCert is the CA certificate for preheat tls handshake, it can be path or PEM format string.
      #   caCert: ''
    objectStorage:
      # -- Enable object storage.
      enable: false
      # -- Name is object storage name of type, it can be s3 or oss.
      name: s3
      # -- Reigon is storage region.
      region: ""
      # -- Endpoint is datacenter endpoint.
      endpoint: ""
      # -- AccessKey is access key ID.
      accessKey: ""
      # -- SecretKey is access key secret.
      secretKey: ""
      # -- S3ForcePathStyle sets force path style for s3, true by default.
      # Set this to `true` to force the request to use path-style addressing,
      # i.e., `http://s3.amazonaws.com/BUCKET/KEY`. By default, the S3 client
      # will use virtual hosted bucket addressing when possible
      # (`http://BUCKET.s3.amazonaws.com/KEY`).
      # Refer to https://github.com/aws/aws-sdk-go/blob/main/aws/config.go#L118.
      s3ForcePathStyle: true
    security:
      # -- AutoIssueCert indicates to issue client certificates for all grpc call.
      # If AutoIssueCert is false, any other option in Security will be ignored.
      autoIssueCert: false
      # -- CACert is the CA certificate for all grpc tls handshake, it can be path or PEM format string.
      caCert: ""
      # -- CAKey is the CA private key, it can be path or PEM format string.
      caKey: ""
      # -- TLSPolicy controls the grpc shandshake behaviors:
      #   force: both ClientHandshake and ServerHandshake are only support tls.
      #   prefer: ServerHandshake supports tls and insecure (non-tls), ClientHandshake will only support tls.
      #   default: ServerHandshake supports tls and insecure (non-tls), ClientHandshake will only support insecure (non-tls).
      # Notice: If the drgaonfly service has been deployed, a two-step upgrade is required.
      # The first step is to set tlsPolicy to default, and then upgrade the dragonfly services.
      # The second step is to set tlsPolicy to prefer, and tthen completely upgrade the dragonfly services.
      tlsPolicy: "prefer"
      certSpec:
        # -- DNSNames is a list of dns names be set on the certificate.
        dnsNames:
          - "dragonfly-manager"
          - "dragonfly-manager.dragonfly-system.svc"
          - "dragonfly-manager.dragonfly-system.svc.cluster.local"
        # -- IPAddresses is a list of ip addresses be set on the certificate.
        ipAddresses:
        # -- ValidityPeriod is the validity period  of certificate.
        validityPeriod: 87600h
    network:
      # -- enableIPv6 enables ipv6.
      enableIPv6: false
    # -- Console shows log on console.
    console: false
    # -- Whether to enable debug level logger and enable pprof.
    verbose: false
    # -- Listen port for pprof, only valid when the verbose option is true
    # default is -1. If it is 0, pprof will use a random port.
    pprofPort: -1
    # - Jaeger endpoint url, like: http://jaeger.dragonfly.svc:14268/api/traces.
    jaeger: ""
  metrics:
    # -- Enable manager metrics.
    enable: false
    service:
      # -- Service type.
      type: ClusterIP
      # -- Service labels.
      labels: {}
      # -- Service annotations.
      annotations: {}
    serviceMonitor:
      # -- Enable prometheus service monitor.
      # ref: https://github.com/coreos/prometheus-operator.
      enable: false
      # -- Additional labels.
      additionalLabels: {}
      # -- Interval at which metrics should be scraped.
      interval: 30s
      # -- Timeout after which the scrape is ended.
      scrapeTimeout: 10s
    prometheusRule:
      # -- Enable prometheus rule.
      # ref: https://github.com/coreos/prometheus-operator.
      enable: false
      # -- Additional labels.
      additionalLabels: {}
      # -- Prometheus rules.
      rules:
        - alert: ManagerDown
          expr: sum(dragonfly_manager_version{}) == 0
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Manager instance is down
            message: Manager instance {{ "{{ $labels.instance }}" }} is down
        - alert: ManagerHighNumberOfFailedGRPCRequest
          expr: sum(rate(grpc_server_started_total{grpc_service="manager.Manager",grpc_type="unary"}[1m])) - sum(rate(grpc_server_handled_total{grpc_service="manager.Manager",grpc_type="unary",grpc_code="OK"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="manager.Manager",grpc_type="unary",grpc_code="NotFound"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="manager.Manager",grpc_type="unary",grpc_code="PermissionDenied"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="manager.Manager",grpc_type="unary",grpc_code="InvalidArgument"}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Manager has a high number of failed grpc request
            message: Manager has a high number of failed grpc request
        - alert: ManagerSuccessRateOfGRPCRequest
          expr: (sum(rate(grpc_server_handled_total{grpc_service="manager.Manager",grpc_type="unary",grpc_code="OK"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="manager.Manager",grpc_type="unary",grpc_code="NotFound"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="manager.Manager",grpc_type="unary",grpc_code="PermissionDenied"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="manager.Manager",grpc_type="unary",grpc_code="InvalidArgument"}[1m]))) / sum(rate(grpc_server_started_total{grpc_service="manager.Manager",grpc_type="unary"}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Manager's success rate of grpc request is low
            message: Manager's success rate of grpc request is low
        - alert: ManagerHighNumberOfFailedRESTRequest
          expr: sum(rate(dragonfly_manager_requests_total{}[1m])) - sum(rate(dragonfly_manager_requests_total{code=~"[12].."}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Manager has a high number of failed rest request
            message: Manager has a high number of failed rest request
        - alert: ManagerSuccessRateOfRESTRequest
          expr: sum(rate(dragonfly_manager_requests_total{code=~"[12].."}[1m])) / sum(rate(dragonfly_manager_requests_total{}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Manager's success rate of rest request is low
            message: Manager's success rate of rest request is low

externalManager:
  # -- External manager hostname.
  host:
  # -- External REST service port.
  restPort: 8080
  # -- External GRPC service port.
  grpcPort: 65003

database:
  # -- Enable mysql with docker container.
  enable: true
  # -- Cluster domain.
  clusterDomain: "cluster.local"
  # -- Running GORM migration.
  migrate: true
  # -- Database type mysql or postgres
  type: mysql
  auth:
    # -- Mysql hostname.
    host: ""
    # -- Mysql root password.
    rootPassword: dragonfly-root
    # -- Mysql username.
    username: dragonfly
    # -- Mysql password.
    password: dragonfly
    # -- Mysql database name.
    database: manager
  primary:
    service:
      # -- Mysql port.
      port: 3306

externalDatabase:
  # -- Running GORM migration.
  migrate: true
  # -- External mysql hostname.
  host:
  # -- External mysql username.
  username: dragonfly
  # -- External mysql password.
  password: dragonfly
  # -- External mysql database name.
  database: manager
  # -- External mysql port.
  port: 3306
  # -- SSL mode.
  sslMode: disable
  # -- Server timezone.
  timezone: UTC

redis:
  # -- Enable redis cluster with docker container.
  enable: true
  # -- Cluster domain.
  clusterDomain: "cluster.local"
  auth:
    # -- Enable password authentication.
    enabled: true
    # -- Redis password.
    password: dragonfly
  master:
    service:
      ports:
        # -- Redis master service port.
        redis: 6379

externalRedis:
  # -- External redis server addresses.
  addrs:
    - "redis.example.com:6379"
  # -- External redis sentinel master name.
  masterName: ""
  # -- External redis username.
  username: ""
  # -- External redis password.
  password: ""
  # -- External redis db.
  db: 0
  # -- External redis broker db.
  brokerDB: 1
  # -- External redis backend db.
  backendDB: 2

jaeger:
  # -- Enable an all-in-one jaeger for tracing every downloading event should not use in production environment.
  enable: false
  provisionDataStore:
    cassandra: false
  allInOne:
    enabled: true
  storage:
    type: none
  agent:
    enabled: false
  collector:
    enabled: false
  query:
    enabled: false

trainer:
  # -- Enable trainer.
  enable: false
  # -- trainer name.
  name: trainer
  # -- Override trainer name.
  nameOverride: ""
  # -- Override trainer fullname.
  fullnameOverride: ""
  # -- Number of Pods to launch.
  replicas: 1
  # -- Image repository.
  image: dragonflyoss/trainer
  # -- Image tag.
  tag: v2.1.0
  # -- Image pull policy.
  pullPolicy: IfNotPresent
  # -- Image pull secrets.
  # @default -- `[]` (defaults to global.imagePullSecrets).
  pullSecrets: []
  # -- Host Aliases.
  hostAliases: []
  # -- Pod resource requests and limits.
  resources:
    requests:
      cpu: "0"
      memory: "0"
    limits:
      cpu: "2"
      memory: "4Gi"
  service:
    # -- Service type.
    type: ClusterIP
    # -- Service labels.
    labels: {}
    # -- Service annotations.
    annotations: {}
  # -- Pod priorityClassName.
  priorityClassName: ""
  # -- Node labels for pod assignment.
  nodeSelector: {}
  # -- Pod terminationGracePeriodSeconds.
  terminationGracePeriodSeconds:
  # -- List of node taints to tolerate.
  tolerations: []
  # -- Pod annotations.
  podAnnotations: {}
  # -- Pod labels.
  podLabels: {}
  # -- Deployment annotations.
  deploymentAnnotations: {}
  # -- Pod containerPort.
  containerPort: 9090
  # -- Extra volumes for trainer.
  extraVolumes:
    - name: logs
      emptyDir: {}
  # -- Extra volumeMounts for trainer.
  extraVolumeMounts:
    - name: logs
      mountPath: "/var/log/dragonfly/trainer"
  initContainer:
    # -- Init container image repository.
    image: busybox
    # -- Init container image tag.
    tag: latest
    # -- Container image pull policy.
    pullPolicy: IfNotPresent
  config:
    server:
      # -- Advertise ip.
      advertiseIP: ""
      # -- Advertise port.
      advertisePort: 9090
      # -- Listen ip.
      listenIP: "0.0.0.0"
      # -- Server port.
      port: 9090
      # -- Work directory.
      workHome: ""
      # -- Log directory.
      logDir: ""
      # -- Storage directory.
      dataDir: ""
    security:
      # -- AutoIssueCert indicates to issue client certificates for all grpc call.
      # If AutoIssueCert is false, any other option in Security will be ignored.
      autoIssueCert: false
      # -- CACert is the root CA certificate for all grpc tls handshake, it can be path or PEM format string.
      caCert: ""
      # -- TLSVerify indicates to verify certificates.
      tlsVerify: false
      # -- TLSPolicy controls the grpc shandshake behaviors:
      #   force: both ClientHandshake and ServerHandshake are only support tls.
      #   prefer: ServerHandshake supports tls and insecure (non-tls), ClientHandshake will only support tls.
      #   default: ServerHandshake supports tls and insecure (non-tls), ClientHandshake will only support insecure (non-tls).
      # Notice: If the drgaonfly service has been deployed, a two-step upgrade is required.
      # The first step is to set tlsPolicy to default, and then upgrade the dragonfly services.
      # The second step is to set tlsPolicy to prefer, and tthen completely upgrade the dragonfly services.
      tlsPolicy: "prefer"
      certSpec:
        # -- DNSNames is a list of dns names be set on the certificate.
        dnsNames:
          - "dragonfly-trainer"
          - "dragonfly-trainer.dragonfly-system.svc"
          - "dragonfly-trainer.dragonfly-system.svc.cluster.local"
        # -- IPAddresses is a list of ip addresses be set on the certificate.
        ipAddresses:
        # -- ValidityPeriod is the validity period of certificate.
        validityPeriod: 4320h
    network:
      # -- enableIPv6 enables ipv6.
      enableIPv6: false
    manager:
      # -- Manager Service Address.
      Addr: "127.0.0.1:65003"
    # -- Console shows log on console.
    console: false
    # -- Whether to enable debug level logger and enable pprof.
    verbose: false
    # -- Listen port for pprof, only valid when the verbose option is true.
    # default is -1. If it is 0, pprof will use a random port.
    pprofPort: -1
    # - Jaeger endpoint url, like: http://jaeger.dragonfly.svc:14268/api/traces.
    jaeger: ""
  metrics:
    # -- Enable trainer metrics.
    enable: false
    service:
      # -- Service type.
      type: ClusterIP
      # -- Service labels.
      labels: {}
      # -- Service annotations.
      annotations: {}
    serviceMonitor:
      # -- Enable prometheus service monitor.
      # ref: https://github.com/coreos/prometheus-operator.
      enable: false
      # -- Additional labels.
      additionalLabels: {}
      # -- Interval at which metrics should be scraped.
      interval: 30s
      # -- Timeout after which the scrape is ended.
      scrapeTimeout: 10s
    prometheusRule:
      # -- Enable prometheus rule.
      # ref: https://github.com/coreos/prometheus-operator.
      enable: false
      # -- Additional labels.
      additionalLabels: {}
      # -- Prometheus rules.
      rules:
        - alert: TrainerDown
          expr: sum(dragonfly_trainer_version{}) == 0
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Trainer instance is down
            message: Trainer instance {{ "{{ $labels.instance }}" }} is down
        - alert: TrainerHighNumberOfFailedGRPCRequest
          expr: sum(rate(grpc_server_started_total{grpc_service="trainer.Trainer",grpc_type="unary"}[1m])) - sum(rate(grpc_server_handled_total{grpc_service="trainer.Trainer",grpc_type="unary",grpc_code="OK"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="trainer.Trainer",grpc_type="unary",grpc_code="NotFound"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="trainer.Trainer",grpc_type="unary",grpc_code="PermissionDenied"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="trainer.Trainer",grpc_type="unary",grpc_code="InvalidArgument"}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Trainer has a high number of failed grpc request
            message: Trainer has a high number of failed grpc request
        - alert: TrainerSuccessRateOfGRPCRequest
          expr: (sum(rate(grpc_server_handled_total{grpc_service="trainer.Trainer",grpc_type="unary",grpc_code="OK"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="trainer.Trainer",grpc_type="unary",grpc_code="NotFound"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="trainer.Trainer",grpc_type="unary",grpc_code="PermissionDenied"}[1m])) + sum(rate(grpc_server_handled_total{grpc_service="trainer.Trainer",grpc_type="unary",grpc_code="InvalidArgument"}[1m]))) / sum(rate(grpc_server_started_total{grpc_service="trainer.Trainer",grpc_type="unary"}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Trainer's success rate of grpc request is low
            message: Trainer's success rate of grpc request is low
        - alert: TrainerHighNumberOfFailedRESTRequest
          expr: sum(rate(dragonfly_trainer_requests_total{}[1m])) - sum(rate(dragonfly_trainer_requests_total{code=~"[12].."}[1m])) > 100
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Trainer has a high number of failed rest request
            message: Trainer has a high number of failed rest request
        - alert: TrainerSuccessRateOfRESTRequest
          expr: sum(rate(dragonfly_trainer_requests_total{code=~"[12].."}[1m])) / sum(rate(dragonfly_trainer_requests_total{}[1m])) < 0.6
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Trainer's success rate of rest request is low
            message: Trainer's success rate of rest request is low

triton:
  # -- Enable triton.
  enable: false
  # -- triton name.
  name: triton
  # -- Override triton name.
  nameOverride: ""
  # -- Override triton fullname.
  fullnameOverride: ""
  # -- Number of Pods to launch.
  replicas: 3
  # -- Image repository.
  image: nvcr.io/nvidia/tritonserver
  # -- Image tag.
  tag: 23.06-py3
  # -- Image pull policy.
  pullPolicy: IfNotPresent
  # -- Model repository path.
  modelRepositoryPath: ""
  # -- Credentials information.
  aws:
    region: ""
    accessKeyID: ""
    secretAccessKey: ""
  service:
    # -- Service type.
    type: LoadBalancer
  # -- REST service port.
  restPort: 8000
  # -- GRPC service port.
  grpcPort: 8001
