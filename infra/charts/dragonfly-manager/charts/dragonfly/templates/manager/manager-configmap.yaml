{{- if .Values.manager.enable }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "dragonfly.manager.fullname" . }}
  labels:
    app: {{ template "dragonfly.fullname" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.manager.name }}
data:
  manager.yaml: |+
    server:
      rest:
        addr: :{{ .Values.manager.restPort }}
        {{- if and .Values.manager.config.server.rest.tls.cert .Values.manager.config.server.rest.tls.cert}}
        tls:
          cert: {{ .Values.manager.config.server.rest.tls.cert }}
          key: {{ .Values.manager.config.server.rest.tls.key }}
        {{- end }}
      grpc:
        advertiseIP: {{ .Values.manager.config.server.grpc.advertiseIP }}
        advertisePort: {{ .Values.manager.config.server.grpc.advertisePort }}
        port:
          start: {{ .Values.manager.grpcPort }}
          end: {{ .Values.manager.grpcPort }}
      workHome: {{ .Values.manager.config.server.workHome }}
      logDir: {{ .Values.manager.config.server.logDir }}
      cacheDir: {{ .Values.manager.config.server.cacheDir }}
      pluginDir: {{ .Values.manager.config.server.pluginDir }}
    auth:
{{ toYaml .Values.manager.config.auth | indent 6 }}
    database:
      type: {{ .Values.database.type }}
      config:
        {{- if and .Values.database.enable (empty .Values.externalDatabase.host)}}
        user: {{ .Values.database.auth.username }}
        password: {{ .Values.database.auth.password }}
        host: {{ .Release.Name }}-{{ default "mysql" .Values.mysql.fullname }}.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }}
        port: {{ .Values.database.primary.service.port }}
        dbname: {{ .Values.database.auth.database }}
        migrate: {{ .Values.database.migrate }}
        {{- else }}
        user: {{ .Values.externalDatabase.username }}
        password: {{ .Values.externalDatabase.password }}
        host: {{ .Values.externalDatabase.host }}
        port: {{ .Values.externalDatabase.port }}
        dbname: {{ .Values.externalDatabase.database }}
        sslMode: {{ .Values.externalDatabase.sslMode }}
        timezone: {{ .Values.externalDatabase.timezone }}
        migrate: {{ .Values.externalDatabase.migrate }}
        {{- end }}
    redis:
      {{- if .Values.redis.enable }}
      addrs:
      - {{ .Release.Name }}-{{ default "redis" .Values.redis.fullname }}-master.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }}:{{ .Values.redis.master.service.ports.redis }}
      password: {{ .Values.redis.auth.password }}
      {{- else }}
      addrs:
{{ toYaml .Values.externalRedis.addrs | indent 8 }}
      masterName: {{ .Values.externalRedis.masterName }}
      username: {{ .Values.externalRedis.username }}
      password: {{ .Values.externalRedis.password }}
      db: {{ .Values.externalRedis.db }}
      brokerDB: {{ .Values.externalRedis.brokerDB }}
      backendDB: {{ .Values.externalRedis.backendDB }}
      {{- end }}
    cache:
{{ toYaml .Values.manager.config.cache | indent 6 }}
    job:
{{ toYaml .Values.manager.config.job | indent 6 }}
    objectStorage:
{{ toYaml .Values.manager.config.objectStorage | indent 6 }}
    security:
{{ toYaml .Values.manager.config.security | indent 6 }}
    network:
{{ toYaml .Values.manager.config.network | indent 6 }}
    metrics:
      enable: {{ .Values.manager.metrics.enable }}
      addr: ":8000"
    console: {{ .Values.manager.config.console }}
    verbose: {{ .Values.manager.config.verbose }}
    {{- if .Values.manager.config.verbose }}
    pprof-port: {{ .Values.manager.config.pprofPort }}
    {{- end }}
    {{- if .Values.manager.config.jaeger }}
    jaeger: {{ .Values.manager.config.jaeger }}
    {{- else if .Values.jaeger.enable }}
    jaeger: http://{{ $.Release.Name }}-jaeger-collector.{{ $.Release.Namespace }}.svc.{{ $.Values.clusterDomain }}:14268/api/traces
    {{- end }}
{{- end }}
