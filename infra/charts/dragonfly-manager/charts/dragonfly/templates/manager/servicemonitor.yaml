{{- if and .Values.manager.metrics.enable .Values.manager.metrics.serviceMonitor.enable }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "dragonfly.manager.fullname" . }}
  labels:
    app: {{ template "dragonfly.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.manager.name }}
    {{- if .Values.manager.metrics.serviceMonitor.additionalLabels }}
{{ toYaml .Values.manager.metrics.serviceMonitor.additionalLabels | indent 4 }}
    {{- end }}
spec:
  endpoints:
  - port: http-metrics
    {{- if .Values.manager.metrics.serviceMonitor.interval }}
    interval: {{ .Values.manager.metrics.serviceMonitor.interval }}
    {{- end }}
    {{- if .Values.manager.metrics.serviceMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.manager.metrics.serviceMonitor.scrapeTimeout }}
    {{- end }}
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
  selector:
    matchLabels:
      app: {{ template "dragonfly.name" . }}
      release: {{ .Release.Name }}
      component: {{ .Values.manager.name }}-metrics
{{- end }}
