{{- if and .Values.manager.metrics.enable .Values.manager.metrics.prometheusRule.enable }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ template "dragonfly.manager.fullname" . }}
  labels:
    app: {{ template "dragonfly.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.manager.name }}
    {{- if .Values.manager.metrics.prometheusRule.additionalLabels }}
{{ toYaml .Values.manager.metrics.prometheusRule.additionalLabels | indent 4 }}
    {{- end }}
spec:
  groups:
    - name: {{ template "dragonfly.manager.fullname" $ }}
      rules:
{{ toYaml .Values.manager.metrics.prometheusRule.rules | indent 8 }}
{{- end }}
