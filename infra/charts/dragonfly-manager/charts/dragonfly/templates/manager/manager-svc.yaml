{{- if .Values.manager.enable }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "dragonfly.manager.fullname" . }}
  labels:
    app: {{ template "dragonfly.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.manager.name }}
{{- if .Values.manager.service.labels }}
{{ toYaml .Values.manager.service.labels | indent 4 }}
{{- end }}
{{- if .Values.manager.service.annotations }}
  annotations:
{{ toYaml .Values.manager.service.annotations | indent 4 }}
{{- end }}
spec:
  type: {{ .Values.manager.service.type }}
  ports:
    - port: {{ .Values.manager.restPort }}
      name: http-rest
      protocol: TCP
      targetPort: {{ .Values.manager.restPort }}
    - port: {{ .Values.manager.grpcPort }}
      name: http-grpc
      protocol: TCP
      targetPort: {{ .Values.manager.grpcPort }}
  selector:
    app: {{ template "dragonfly.fullname" . }}
    release: {{ .Release.Name }}
    component: {{ .Values.manager.name }}
{{- end }}
