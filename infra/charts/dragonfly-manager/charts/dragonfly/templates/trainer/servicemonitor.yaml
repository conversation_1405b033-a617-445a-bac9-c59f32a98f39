{{- if and .Values.trainer.metrics.enable .Values.trainer.metrics.serviceMonitor.enable }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "dragonfly.trainer.fullname" . }}
  labels:
    app: {{ template "dragonfly.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.trainer.name }}
    {{- if .Values.trainer.metrics.serviceMonitor.additionalLabels }}
{{ toYaml .Values.trainer.metrics.serviceMonitor.additionalLabels | indent 4 }}
    {{- end }}
spec:
  endpoints:
  - port: http-metrics
    {{- if .Values.trainer.metrics.serviceMonitor.interval }}
    interval: {{ .Values.trainer.metrics.serviceMonitor.interval }}
    {{- end }}
    {{- if .Values.trainer.metrics.serviceMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.trainer.metrics.serviceMonitor.scrapeTimeout }}
    {{- end }}
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
  selector:
    matchLabels:
      app: {{ template "dragonfly.name" . }}
      release: {{ .Release.Name }}
      component: {{ .Values.trainer.name }}-metrics
{{- end }}
