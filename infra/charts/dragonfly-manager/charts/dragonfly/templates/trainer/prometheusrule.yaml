{{- if and .Values.trainer.metrics.enable .Values.trainer.metrics.prometheusRule.enable }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ template "dragonfly.trainer.fullname" . }}
  labels:
    app: {{ template "dragonfly.name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    component: {{ .Values.trainer.name }}
    {{- if .Values.trainer.metrics.prometheusRule.additionalLabels }}
{{ toYaml .Values.trainer.metrics.prometheusRule.additionalLabels | indent 4 }}
    {{- end }}
spec:
  groups:
    - name: {{ template "dragonfly.trainer.fullname" $ }}
      rules:
{{ toYaml .Values.trainer.metrics.prometheusRule.rules | indent 8 }}
{{- end }}
