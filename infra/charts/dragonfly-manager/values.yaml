host: dragonfly-manager-internal.cnc.gztest.bceccr.com


dragonfly:
  global:
    # -- Image pull secrets.
    imagePullSecrets:
      - name: dragonfly-registry-secret

  scheduler:
    enable: false

  seedPeer:
    enable: false

  dfdaemon:
    enable: false

  manager:
    replicas: 3
    image: registry.baidubce.com/ccr-image/dragonfly-manager
    tag: v2.1.0-0.3-fix20240510
    config:
      console: true
      verbose: true
    metrics:
      # -- Enable manager metrics.
      enable: true
      service:
        annotations:
          prometheus.io/scrape: "true"

  database:
    enable: false
    type: mysql

  redis:
    enable: false