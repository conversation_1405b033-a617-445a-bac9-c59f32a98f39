
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: (devel)
  creationTimestamp: null
  name: mysql.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    kind: Mysql
    listKind: MysqlList
    plural: mysql
    singular: mysql
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: PHASE
      type: string
    - jsonPath: .status.host
      name: Host
      type: string
    - jsonPath: .status.reason
      name: REASON
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Mysql is the Schema for the mysql API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: MysqlSpec defines the desired state of Mysql
            properties:
              instanceType:
                type: string
              password:
                type: string
              username:
                type: string
            type: object
          status:
            description: MysqlStatus defines the observed state of Mysql
            properties:
              host:
                type: string
              instanceID:
                type: string
              lastProbeTime:
                format: date-time
                type: string
              lastTransitionTime:
                format: date-time
                type: string
              message:
                type: string
              phase:
                type: string
              port:
                type: string
              reason:
                type: string
              type:
                description: 'INSERT ADDITIONAL STATUS FIELD - define observed state of cluster Important: Run "make" to regenerate code after modifying this file'
                type: string
              userName:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
