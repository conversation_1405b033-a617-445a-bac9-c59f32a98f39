# GZ values for resource-controller.

image:
  # In <PERSON><PERSON>'s pipeline, the variable will be replaced according to the component
  tag: r09c882c_20240528


cluster:
  ak: ALTAKrl2vcxH633BZpOeuvxHC6
  sk: 41bb7511667f461598b7d49c644f5b8d
  clusterId: cce-e3ca4ffj
  endpoint:
    rds: rds.gz.baidubce.com
    scs: redis.gz.baidubce.com
    sts: http://sts.gz.iam.sdns.baidu.com:8586
    eip: eip.gz.baidubce.com
    vpc: http://vpc.gz.bce-internal.baidu.com
    blb: blb.gz.baidubce.com
    cce: cce.gz.baidubce.com
    iam: http://iam.gz.bce-internal.baidu.com
    bos: gz.bcebos.com
    neutron: http://neutron-a.gz.bce-internal.baidu.com:9696
    vpcDns: http://bcc.gz.baidubce.com
  region: gz
  servicePassword: srIkFXOwnRiPavuKtX2k8EiGY5vlFbTb

  postgresql:
    subnetID: sbn-2dnc193m7ae5
    zoneName: cn-gz-c

  mysql:
    subnetID: sbn-2dnc193m7ae5
    zoneName: cn-gz-c

  redis:
    subnetID: sbn-2dnc193m7ae5
    zoneName: cn-gz-c