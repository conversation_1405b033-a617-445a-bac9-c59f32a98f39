# HK values for resource-controller.

image:
  # In <PERSON><PERSON>'s pipeline, the variable will be replaced according to the component
  tag: resource-controller.image.tag


cluster:
  ak: REPLACEME_ACCESSKEY
  sk: REPLACEME_SECRETKEY
  clusterId: cce-0g68wl24
  endpoint:
    rds: rds.fwh.baidubce.com
    scs: redis.fwh.baidubce.com
    sts: http://sts.fwh.bce.baidu-int.com:8586
    eip: eip.fwh.baidubce.com
    vpc: http://vpc.fwh.bce.baidu-int.com
    blb: blb.fwh.baidubce.com
    cce: cce.fwh.baidubce.com
    iam: http://iam.fwh.bce.baidu-int.com
    bos: fwh.bcebos.com
    neutron: http://neutron.fwh.bce-internal.baidu.com:9696
    vpcDns: http://bcc.fwh.baidubce.com
  region: fwh
  servicePassword: REPLACEME_SERVICE_PASSWORD
  mysqlConnection: ccr:6Ue27h1@rvLMgFx7@tcp(mysql57.rdsmvzxj3paq4au.rds.fwh.baidubce.com:3306)/ccr?parseTime=True&loc=Asia%2fShanghai
  devopsConnection: devops:we6MN2sE88JTGXUqJu7v@tcp(***********:3306)/devops?parseTime=True&loc=Asia%2fShanghai

  postgresql:
    subnetID: sbn-mgs2mnqrhxkf
    zoneName: cn-fwh-a

  mysql:
    subnetID: sbn-mgs2mnqrhxkf
    zoneName: cn-fwh-a

  redis:
    subnetID: sbn-mgs2mnqrhxkf
    zoneName: cn-fwh-a