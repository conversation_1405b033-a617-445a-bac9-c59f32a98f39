# HK values for ccr-service.

image:
  # In <PERSON><PERSON>'s pipeline, the variable will be replaced according to the component
  tag: ccr-service.image.tag


config:
  ak: REPLACEME_ACCESSKEY
  sk: REPLACEME_SECRETKEY
  clusterId: cce-harhrfgw
  endpoint:
    ccr: *************
    sts: http://sts.hkg.bce.baidu-int.com:8586
    vpc: http://vpc.hkg.bce.baidu-int.com
    cce: cce.hkg.baidubce.com
    iam: http://iam.hkg.bce.baidu-int.com
    ccrService: **********:8500
  region: hkg
  servicePassword: REPLACEME_SERVICE_PASSWORD
  mysqlConnection: ccr:qAnikdKni!I_eH6p@tcp(mysql57.rdsmq588d1toqph.rds.hkg.baidubce.com:3306)/ccr?parseTime=True&loc=Asia%2fShanghai


affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution: []
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app.kubernetes.io/name
                operator: In
                values:
                  - ccr-service
          topologyKey: kubernetes.io/hostname