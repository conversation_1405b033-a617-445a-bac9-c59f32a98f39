apiVersion: v1
data:
  prometheus.yaml: |-
    scrape_configs:
      - job_name: 'registry-server'
        scrape_interval: 10s
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
              - {{ .Values.installation.namespace }}
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
          action: replace
          target_label: __scheme__
          regex: (https?)
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_pod_label_app]
          action: keep
          regex: registry-server
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
          target_label: __address__
        - action: labelmap
          regex: __meta_kubernetes_pod_label_(.+)
        - action: labeldrop
          regex: (.+)_revision_hash|(.+)_template_generation
        - target_label: "registry_id"
          replacement: {{ .Values.installation.namespace }}
        metric_relabel_configs:
        - source_labels: [namespace]
          regex: Polaris
          action: drop
        - action: labeldrop
          regex: polaris_server_instance|namespace|instance
        - source_labels: [__name__]
          action: keep
          regex: eureka_request_duration_seconds_bucket|eureka_request_duration_seconds_sum|eureka_request_duration_seconds_count|instance_abnormal_count|instance_count|instance_isolate_count|instance_online_count|service_abnormal_count|service_count|service_online_count|service_offline_count|eureka_response_status
kind: ConfigMap
metadata:
  name: vmagent-config
  namespace: {{ .Values.installation.namespace }}