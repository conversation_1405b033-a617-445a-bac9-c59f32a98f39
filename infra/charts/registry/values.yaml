# Default values for polaris.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# 1.0 支持eureka协议
# 1.1 支持nacos协议
release: 1.0.0 # registry实例版本, 第三位是bugfix版本

owner: eca97e148cb74e9683d7b7240829d1ff # 实例所属客户账号id

global:
  # mode: standalone
  mode: cluster

console:
  image:
    proxyRepository: registry.baidubce.com/csm-online/mse-proxy
    consoleRepository: registry.baidubce.com/csm-online/mse-console
  limit:
    cpu: "500m"
    memory: "1000Mi"

polaris:
  image:
    repository: registry.baidubce.com/csm-online/registry-server
    # tag: v0.3 改用release作为tag
    pullPolicy: Always
  limit:
    cpu: "500m"
    memory: "1000Mi"
  healthChecker:
    type: heartbeatRedis
    redis:
      address: localhost:6379
      passwd: cce@1234
  replicaCount: 1
  auth:
    consoleOpen: true
    clientOpen: false
  storage:
    db:
      address: localhost:3306
      name: polaris_server
      user: root
      password: polaris@123456

service:
  type: LoadBalancer
  performanceLevel: small1
  exposeHttpPort: 43298
  consolePort: 43296
  eurekaPort: 8761
  nacosPort: 8848
  nacosPortGrpc: 9848
  httpPort: 8090
  serviceGrpcPort: 8091
  xdsv3Port: 15010
  configGrpcPort: 8093
  limiterHttpPort: 8100
  limiterGrpcPort: 8101

monitor:
  enable: true
  image: registry.baidubce.com/cce-plugin-dev/vmagent:v1.93.9
  port: 8429
  remoteWriteURL: https://cprom.gz.baidubce.com/insert/prometheus/api/v1/write
  region: gz

installation:
  namespace: polaris-system

maintain:
  user: cse

args:
  deltaExpireInterval: 60
  deleteEmptyAutoCreatedServiceEnable: false
  deleteEmptyAutoCreatedServiceTimeout: 30m
  deleteUnHealthyInstanceEnable: false
  deleteUnHealthyInstanceTimeout: 60m
  namespaceAutoCreate: true
  monitorReplicas: 0
  monitorCpromId:
  monitorCpromToken:
  consoleReplicas: 0