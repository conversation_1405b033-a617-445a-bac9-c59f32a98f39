apiVersion: argoproj.io/v1alpha1
kind: ClusterWorkflowTemplate
metadata:
  name: image-converter-template
spec:
  # 最迟60分钟，必须完成任务
  activeDeadlineSeconds: 3600
  # 最大10个pod并发
  parallelism: 10
  ttlStrategy:
    secondsAfterCompletion: 172800
  # 工作流完成，触发pod gc，回收临时存储资源  
  podGc:
    strategy: OnWorkflowCompletion
  workflowMetadata:
    labels:
      task.cce.baidubce.com/type: "image-converter"
  imagePullSecrets:
  - name: {{ .Values.config.imagePullSecret }}    
  entrypoint: converter
  arguments:
    parameters:
      # 原始镜像
      - name: source
        value: ""
      # 转换镜像后缀
      - name: tagSuffix
        value: _accelerate
      # 临时存储路径
      - name: path
        value: /tmp
      # 原始镜像仓库用户名
      - name: username
        value: ""
      # 原始镜像仓库密码
      - name: password
        value: ""
      - name: insecure
        value: true
      - name: harborAnnotation
        value: true
      - name: docker2oci
        value: true
      - name: driver
        value: stargz            
  templates:
    - name: converter
      # 快速失败
      failFast: true
      container:
        image:  {{ .Values.config.converterImage }}
        command: ["/image-converter"]
        args:
        - "{{`{{workflow.parameters.source}}`}}"
        - "-v"
        - "--tag-suffix={{`{{workflow.parameters.tagSuffix}}`}}"
        - "--path={{`{{workflow.parameters.path}}`}}"
        - "--username={{`{{workflow.parameters.username}}`}}"
        - "--password={{`{{workflow.parameters.password}}`}}"
        - "--insecure={{`{{workflow.parameters.insecure}}`}}"
        - "--harbor-annotation={{`{{workflow.parameters.harborAnnotation}}`}}"
        - "--docker2oci={{`{{workflow.parameters.docker2oci}}`}}"
        - "--driver-type={{`{{workflow.parameters.driver}}`}}"
        resources:
          # 镜像转换工具比较消耗cpu和临时存储
          requests:
            cpu: 2
            memory: 2Gi
            ephemeral-storage: 50Gi
          limits:
            cpu: 8
            memory: 8Gi
            ephemeral-storage: 200Gi  
  # 镜像转换使用专属机器      
  nodeSelector:
    eccr.baidubce.com/node-type: "image-converter"
  tolerations:
  - key: eccr.baidubce.com/node-type
    operator: Equal
    value: image-converter
    effect: NoSchedule
