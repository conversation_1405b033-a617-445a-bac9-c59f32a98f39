apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "acceleration-service.fullname" . }}
  labels:
    {{- include "acceleration-service.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "acceleration-service.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "acceleration-service.selectorLabels" . | nindent 8 }}
    spec:
      serviceAccountName: {{ include "acceleration-service.serviceAccountName" . }}
      {{- with .Values.images.pullSecrets }}
      imagePullSecrets:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          args:
          - --config=/home/<USER>/config/config.yaml
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.images.repository }}:{{ default .Chart.AppVersion .Values.images.tag }}"
          imagePullPolicy: {{ .Values.images.pullPolicy }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
          - mountPath: /home/<USER>/config
            name: config
            readOnly: true
      volumes:
      - name: config
        configMap:
          name: {{ include "acceleration-service.fullname" . }}
          items:
            - key: config.yaml
              path: config.yaml
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
