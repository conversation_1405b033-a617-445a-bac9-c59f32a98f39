apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    rbac.authorization.kubernetes.io/autoupdate: "true"
  labels:
    {{- include "acceleration-service.labels" . | nindent 4 }}
  name: {{ include "acceleration-service.fullname" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "acceleration-service.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "acceleration-service.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}