apiVersion: v1
kind: Service
metadata:
  name: {{ include "acceleration-service.fullname" . }}
  labels:
  {{- include "acceleration-service.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - name: acceleration
      port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
  selector:
  {{- include "acceleration-service.selectorLabels" . | nindent 4 }}
