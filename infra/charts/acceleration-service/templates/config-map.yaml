apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "acceleration-service.fullname" . }}
  labels:
    {{- include "acceleration-service.labels" . | nindent 4 }}
data:
  config.yaml: |
    # Configuration file of Harbor Acceleration Service

    # http related config
    server:
      name: API
      # listened host for http
      host: 0.0.0.0
      # port for http
      port: 2077

    metric:
      # export metrics on `/metrics` endpoint
      enabled: true

    accelerator:
      # enable to add harbor specified annotations to converted image for tracking.
      harborAnnotation: true
      driver:
        # accelerator driver type: estargz`
        type: estargz
        config:
          docker2oci: true
      # add suffix to tag of source image reference as target image reference
      tagSuffix: _accelerate
      # kubeconfig path
      kubeconfig: ""
      converterImage: {{ .Values.config.converterImage }}
      imagePullSecret: {{ .Values.config.imagePullSecret }}
      skipInsecureVerify: true