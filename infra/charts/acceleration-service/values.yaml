# Default values for acceleration-service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 2

images:
  # -- imagePullPolicy to apply to all containers
  pullPolicy: IfNotPresent
  # -- Secrets with credentials to pull images from a private registry
  pullSecrets:
    - name: acceleration-service-registry-secret
  # -- repository
  repository: registry.baidubce.com/ccr-image/acceleration-service
  # In Baidu's pipeline, the variable will be replaced according to the component
  tag: acceleration-service.image.tag


nameOverride: ""
fullnameOverride: ""

## Custom config
config:
  converterImage: registry.baidubce.com/ccr-image/image-converter:*******
  imagePullSecret: ccr-registry-secret

## Kubernetes configuration
## For minikube, set this to NodePort, elsewhere use LoadBalancer or ClusterIP
##
service:
  type: ClusterIP
  # HTTP Port
  port: 2077
  # metrics Port
  metricsPort: 2077
  targetPort: 2077


serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations:
  prometheus.io/scrape: 'true'
  prometheus.io/port: '2077'

podSecurityContext: {}
  # fsGroup: 2000

securityContext:
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
   limits:
     cpu: 1
     memory: 256Mi
   requests:
     cpu: 500m
     memory: 128Mi

nodeSelector: {}

tolerations: []

affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
            - key: app.kubernetes.io/name
              operator: In
              values:
                - acceleration-service
        topologyKey: "kubernetes.io/hostname"
