apiVersion: operator.victoriametrics.com/v1beta1
kind: VMRule
metadata:
  name: ccr-bci-cluster-alert
  namespace: cce-monitor
spec:
  groups:
    - name: ccr-bci-cluster-alert
      rules:
        - alert: ccr_KubernetesContainerRestarted
          annotations:
            description: |
              Container {{ $labels.container }} in region {{ $labels.region }}({{ $labels.clusterID }}) pod {{ $labels.namespace }}/{{ $labels.pod }} has been restarted {{ $value }} times in the last 10 minutes.
                VALUE = {{ $value }}
            summary: Kubernetes container restarted
          expr: increase(kube_pod_container_status_restarts_total{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-ptpenwpk|cce-harhrfgw|cce-0g68wl24", namespace=~"ccr-40fogfn9|ccr-2uvujvk6|ccr-58jbkit5|ccr-3zn5pkkp"}[10m])
            > 1
          for: 2m
          labels:
            severity: critical
    - name: ccr-bci-resource-usage-alert
      rules:
        - alert: ccr_container_CPU_usage_high
          annotations:
            description: "container memory utilization (> 90%)  in region {{ $labels.region
          }}({{ $labels.clusterID }}) namespace {{ $labels.namespace }} pod {{ $labels.pod
          }} container {{ $labels.container }}\n VALUE = {{ $value }}% \n"
            summary: Container memory utilization high
          expr: sum(rate(container_cpu_usage_seconds_total{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24",
            container!="", namespace=~"ccr-40fogfn9|ccr-2uvujvk6|ccr-58jbkit5|ccr-3zn5pkkp"}[5m])) by(clusterID, region, namespace, pod, container) / sum(kube_pod_container_resource_limits{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24",
            resource="cpu", container!="", namespace=~"ccr-40fogfn9|ccr-2uvujvk6|ccr-58jbkit5|ccr-3zn5pkkp"} != 0 )  by(clusterID, region, namespace, pod,
            container) * 100 > 90
          for: 2m
          labels:
            severity: warning
        - alert: ccr_container_memory_usage_high
          annotations:
            description: "container memory utilization (> 90%)  in region {{ $labels.region
          }}({{ $labels.clusterID }}) namespace {{ $labels.namespace }} pod {{ $labels.pod
          }} container {{ $labels.container}} \n VALUE = {{ $value }}% \n"
            summary: Container memory utilization high
          expr: sum(container_memory_rss{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24",
            container!="", namespace=~"ccr-40fogfn9|ccr-2uvujvk6|ccr-58jbkit5|ccr-3zn5pkkp"}) by(clusterID, region, namespace, pod, container) / sum(kube_pod_container_resource_limits{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24",
            resource="memory", container!="", namespace=~"ccr-40fogfn9|ccr-2uvujvk6|ccr-58jbkit5|ccr-3zn5pkkp"} != 0 )  by(clusterID, region, namespace,
            pod, container) * 100 > 90
          for: 2m
          labels:
            severity: warning
    - name: ccr-bci-nginx-alert
      rules:
        - alert: ccr_nginx_high_http_5xx_error_rate
          annotations:
            description: "Too many HTTP requests with status {{ $labels.status }} in region
          {{ $labels.region }}({{ $labels.clusterID }}) namespace{{ $labels.namespace
          }} ingress {{ $labels.ingress }} \n  VALUE = {{ $value }}%\n"
            summary: Nginx ingress HTTP 5xx error
          expr: increase(nginx_ingress_controller_requests{status=~"^5..",clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24", namespace=~"ccr-40fogfn9|ccr-2uvujvk6|ccr-58jbkit5|ccr-3zn5pkkp"}[2m])
            > 0
          for: 2m
          labels:
            serverity: warning
        - alert: ccr_nginx_ingress_high_http_4xx_error_rate
          annotations:
            description: "Too many HTTP requests with status 4xx(> 100%) in region {{
          $labels.region }}({{ $labels.clusterID }}) namespace{{ $labels.namespace
          }} ingress {{ $labels.ingress }} \n  VALUE = {{ $value }}%\n"
            summary: Nginx ingress controller HTTP 4xx error rate high
          expr: sum(rate(nginx_ingress_controller_requests{status=~"^4..", status!~"404|401",
            clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24", namespace=~"ccr-40fogfn9|ccr-2uvujvk6|ccr-58jbkit5|ccr-3zn5pkkp"}[2m]))
            by(clusterID, namespace, region, ingress) / sum(rate(nginx_ingress_controller_requests{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24", namespace=~"ccr-40fogfn9|ccr-2uvujvk6|ccr-58jbkit5|ccr-3zn5pkkp"}[2m]))
            by(clusterID, namespace, region, ingress) * 100 > 50
          for: 60m
          labels:
            serverity: warning
    - name: ccr-bci-harbor-alert
      rules:
        - alert: ccr_harbor_unhealth
          annotations:
            description: |
              Harbor running status is exception in region = {{ $labels.region }}({{ $labels.clusterID }})  namespace = {{ $labels.pod_namespace}} pod = {{ $labels.pod_name}}
            summary: Harber unhealth
          expr: harbor_health{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24", pod_namespace=~"ccr-40fogfn9|ccr-2uvujvk6|ccr-58jbkit5|ccr-3zn5pkkp"}
            == 0
          for: 0m
          labels:
            severity: critical