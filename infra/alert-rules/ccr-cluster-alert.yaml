apiVersion: operator.victoriametrics.com/v1beta1
kind: VMRule
metadata:
  name: ccr-cluster-alert
  namespace: cce-monitor
spec:
  groups:
    - name: ccr-cluster-alert
      rules:
        - alert: ccr_KubernetesContainerRestarted
          annotations:
            description: |
              Container {{ $labels.container }} in region {{ $labels.region }}({{ $labels.clusterID }}) pod {{ $labels.namespace }}/{{ $labels.pod }} has been restarted {{ $value }} times in the last 10 minutes.
                VALUE = {{ $value }}
            summary: Kubernetes container restarted
          expr: increase(kube_pod_container_status_restarts_total{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-ptpenwpk|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[10m])
            > 1
          for: 2m
          labels:
            severity: critical
    - name: ccr-resource-usage-alert
      rules:
        - alert: ccr_node_memory_high_utilized
          annotations:
            description: "node memory utilization > 90% in region {{ $labels.region }}({{
          $labels.clusterID }}) instance {{ $labels.instance }} \n VALUE = {{ $value
          }}% \n"
            summary: Node memory utilization high
          expr: (1 - node_memory_MemAvailable_bytes{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}/
            node_memory_MemTotal_bytes{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"})
            * 100 > 80
          for: 2m
          labels:
            severity: warning
        - alert: ccr_node_memory_under_memory_pressure
          annotations:
            description: "The node is under heavy memory pressure. High rate of major
          page faults in region = {{ $labels.region }}({{ $labels.clusterID }}) instance
          = {{ $labels.instance }}\n  VALUE = {{ $value }}\n "
            summary: Node memory under memory pressure
          expr: rate(node_vmstat_pgmajfault{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[1m])
            > 1000
          for: 2m
          labels:
            severity: warning
        - alert: ccr_node_out_of_disk_space
          annotations:
            description: "Disk is almost full (> 90% ) in region {{ $labels.region }}({{
          $labels.instance }})\n  VALUE = {{ $value }}%\n "
            summary: Host out of disk space (instance {{ $labels.instance }})
          expr: (1 - (node_filesystem_avail_bytes{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}
            ) / node_filesystem_size_bytes{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"})
            * 100 > 80
          for: 2m
          labels:
            severity: warning
        - alert: ccr_node_high_CPU_load
          annotations:
            description: "CPU load is > 90% in region = {{ $labels.region }}({{ $labels.clusterID
          }}) instance {{ $labels.instance }}\n  VALUE = {{ $value }}%\n  "
            summary: Node high CPU load
          expr: (1 - avg by(instance,region,clusterID,address) (rate(node_cpu_seconds_total{mode="idle",
            clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[2m])))
            * 100  > 90
          for: 0m
          labels:
            severity: warning
        - alert: ccr_node_CPU_usage_high
          annotations:
            description: "node CPU utilization has exceeded 90%  in region {{ $labels.region
          }}({{ $labels.clusterID }}) instance {{ $labels.instance }}\n VALUE = {{
          $value }}% \n"
            summary: node cpu utilization high
          expr: (1 - sum(increase(node_cpu_seconds_total{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn",
            mode="idle"}[30m])) by(clusterID, instance, region) / sum(increase(node_cpu_seconds_total{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[30m]))
            by (clusterID, instance, region)) * 100  > 90
          for: 2m
          labels:
            severity: warning
        - alert: ccr_node_oom_kill_detected
          annotations:
            description: |
              OOM kill detected in region {{ $labels.region }}({{ $labels.clusterID }}) instance {{ $labels.instance }}
                VALUE = {{ $value }}
            summary: Host OOM kill detected
          expr: increase(node_vmstat_oom_kill{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[1m])
            > 0
          for: 2m
          labels:
            severity: warning
        - alert: ccr_container_CPU_usage_high
          annotations:
            description: "container memory utilization (> 90%)  in region {{ $labels.region
          }}({{ $labels.clusterID }}) namespace {{ $labels.namespace }} pod {{ $labels.pod
          }} container {{ $labels.container }}\n VALUE = {{ $value }}% \n"
            summary: Container memory utilization high
          expr: sum(rate(container_cpu_usage_seconds_total{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn",
            container!=""}[5m])) by(clusterID, region, namespace, pod, container) / sum(kube_pod_container_resource_limits{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn",
            resource="cpu", container!=""} != 0 )  by(clusterID, region, namespace, pod,
            container) * 100 > 90
          for: 2m
          labels:
            severity: warning
        - alert: ccr_container_memory_usage_high
          annotations:
            description: "container memory utilization (> 90%)  in region {{ $labels.region
          }}({{ $labels.clusterID }}) namespace {{ $labels.namespace }} pod {{ $labels.pod
          }} container {{ $labels.container}} \n VALUE = {{ $value }}% \n"
            summary: Container memory utilization high
          expr: sum(container_memory_rss{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn",
            container!=""}) by(clusterID, region, namespace, pod, container) / sum(kube_pod_container_resource_limits{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn",
            resource="memory", container!=""} != 0 )  by(clusterID, region, namespace,
            pod, container) * 100 > 90
          for: 2m
          labels:
            severity: warning
    - name: ccr-nginx-alert
      rules:
        - alert: ccr_nginx_high_http_5xx_error_rate
          annotations:
            description: "Too many HTTP requests with status {{ $labels.status }} in region
          {{ $labels.region }}({{ $labels.clusterID }}) namespace{{ $labels.namespace
          }} ingress {{ $labels.ingress }} \n  VALUE = {{ $value }}%\n"
            summary: Nginx ingress HTTP 5xx error
          expr: increase(nginx_ingress_controller_requests{status=~"^5..",clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[2m])
            > 0
          for: 2m
          labels:
            serverity: warning
        - alert: ccr_nginx_ingress_high_http_4xx_error_rate
          annotations:
            description: "Too many HTTP requests with status 4xx(> 100%) in region {{
          $labels.region }}({{ $labels.clusterID }}) namespace{{ $labels.namespace
          }} ingress {{ $labels.ingress }} \n  VALUE = {{ $value }}%\n"
            summary: Nginx ingress controller HTTP 4xx error rate high
          expr: sum(rate(nginx_ingress_controller_requests{status=~"^4..", status!~"404|401",
            clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[2m]))
            by(clusterID, namespace, region, ingress) / sum(rate(nginx_ingress_controller_requests{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[2m]))
            by(clusterID, namespace, region, ingress) * 100 > 50
          for: 60m
          labels:
            serverity: warning
        - alert: ccr_iregistry_RPS_overlimit_than_400
          annotations:
            description: "The CCR iRegistry RPS exceeds the limit \n VALUE = {{ $value
          }}\n"
            summary: CCR iRegistry RPS overlimit than 400
          expr: sum(rate(nginx_ingress_controller_requests{clusterID="cce-s3yvgr5v", namespace="ccr-222toie4"}[2m]))
            by (clusterID) > 400
          for: 2m
          labels:
            serverity: warning
        - alert: ccr_iregistry_RPS_overlimit_than_600
          annotations:
            description: "The CCR iRegistry RPS exceeds the limit \n VALUE = {{ $value
          }}\n"
            summary: CCR iRegistry RPS overlimit than 600
          expr: sum(rate(nginx_ingress_controller_requests{clusterID="cce-s3yvgr5v", namespace="ccr-222toie4"}[2m]))
            by (clusterID) > 600
          for: 2m
          labels:
            serverity: critical
    - name: ccr-redis-alert
      rules:
        - alert: ccr_redis_down
          annotations:
            description: "Redis instance is down in region = {{ $labels.region }}({{ $labels.clusterID
          }})  namespace = {{ $labels.pod_namespace }} pod = {{ $labels.pod_name }}
          \n "
            summary: Redis down
          expr: redis_up{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}
            == 0
          for: 0m
          labels:
            severity: warning
        - alert: ccr_redis_too_many_masters
          annotations:
            description: |
              Redis cluster has too many nodes marked as master in region = {{ $labels.region }}({{ $labels.clusterID }})  namespace = {{ $labels.pod_namespace }}
                VALUE = {{ $value }}
            summary: Redis too many masters
          expr: count(redis_instance_info{role="master", clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"})
            by(clusterID, region, pod_namespace) > 1
          for: 0m
          labels:
            severity: warning
        - alert: ccr_redis_disconnected_slaves
          annotations:
            description: |
              Redis not replicating for all slaves. Consider reviewing the redis replication status in region = {{ $labels.region }}({{ $labels.clusterID }})  namespace = {{ $labels.pod_namespace }} pod = {{ $labels.pod_name }}.
                VALUE = {{ $value }}
            summary: Redis disconnected slaves (instance {{ $labels.instance }})
          expr: count(redis_connected_slaves{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"})
            by(clusterID, region, pod_namespace) - sum(redis_connected_slaves{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"})
            by(clusterID, region, pod_namespace) - 1 > 1
          for: 0m
          labels:
            severity: warning
        - alert: ccr_redis_replication_broken
          annotations:
            description: |
              Redis instance lost a slave in region = {{ $labels.region }}({{ $labels.clusterID }})  namespace = {{ $labels.pod_namespace}} pod = {{ $labels.pod_name}}
                VALUE = {{ $value }}
            summary: Redis replication broken
          expr: delta(redis_connected_slaves{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[1m])
            < 0
          for: 0m
          labels:
            severity: warning
        - alert: ccr_redis_cluster_flapping
          annotations:
            description: |
              Changes have been detected in Redis replica connection in region = {{ $labels.region }}({{ $labels.clusterID }})  namespace = {{ $labels.pod_namespace}} pod = {{ $labels.pod_name}}.
               This can occur when replica nodes lose connection to the master and reconnect (a.k.a flapping).
                VALUE = {{ $value }}
            summary: Redis cluster flapping
          expr: changes(redis_connected_slaves{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[1m])
            > 1
          for: 2m
          labels:
            severity: warning
        - alert: ccr_redis_out_of_configured_max_memory
          annotations:
            description: "Redis is running out of configured maxmemory (> 90%) in region
          = {{ $labels.region }}({{ $labels.clusterID }})  namespace = {{ $labels.pod_namespace}}
          pod = {{ $labels.pod_name}}\n  VALUE = {{ $value }}%\n "
            summary: Redis out of configured maxmemory (instance {{ $labels.instance }})
          expr: redis_memory_used_bytes{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}
            / (redis_memory_max_bytes{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}
            != 0) * 100  > 90
          for: 2m
          labels:
            severity: warning
        - alert: ccr_redis_rejected_connections
          annotations:
            description: |
              Some connections to Redis has been rejected in region = {{ $labels.region }}({{ $labels.clusterID }})  namespace = {{ $labels.pod_namespace}} pod = {{ $labels.pod_name}}
                VALUE = {{ $value }}
            summary: Redis rejected connections (instance {{ $labels.instance }})
          expr: increase(redis_rejected_connections_total{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}[1m])
            > 0
          for: 0m
          labels:
            severity: warning
    - name: ccr-harbor-alert
      rules:
        - alert: ccr_harbor_unhealth
          annotations:
            description: |
              Harbor running status is exception in region = {{ $labels.region }}({{ $labels.clusterID }})  namespace = {{ $labels.pod_namespace}} pod = {{ $labels.pod_name}}
            summary: Harber unhealth
          expr: harbor_health{clusterID=~"cce-567hbeyy|cce-s3yvgr5v|cce-t1jqcsca|cce-9wyj2x6l|cce-harhrfgw|cce-0g68wl24|cce-p5su5w7i|cce-eahngvhn"}
            == 0
          for: 0m
          labels:
            severity: critical