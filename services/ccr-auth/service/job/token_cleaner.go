package job

import (
	"time"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/clientset"
)

type TokenCleaner struct {
	interval time.Duration
	client   *clientset.ClientSet
}

func NewTokenCleaner(cli *clientset.ClientSet, interval time.Duration) *TokenCleaner {
	return &TokenCleaner{
		interval: interval,
		client:   cli,
	}
}

func (t *TokenCleaner) Start(stopChan <-chan struct{}) {
	for {
		select {
		case <-time.After(t.interval):
			t.do()
		case <-stopChan:
			return
		}
	}
}

func (t *TokenCleaner) do() {
	t.client.SqlClient().DeleteExpiredUserToken()
}
