package router

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/ginprom"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/handler"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/model"
)

func NewGin(clientset clientset.ClientSetInterface, conf *config.ServiceConfig) *gin.Engine {
	route := gin.Default()
	route.Use(middleware.LoggerMiddleware())

	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		if err := v.RegisterValidation("password", model.PasswordValidate); err != nil {
			return nil
		}
	}

	tokenMaker := token.NewTokenMaker(conf.TokenExpiredInMinute, conf.TokenIssuer)

	tokenHandler := handler.NewTokenHandler(tokenMaker, clientset, conf.Region, conf.TokenCachedInMinute)
	// service/token
	route.GET("/service/token", ginprom.PromMiddleware(func(*gin.Context) string { return "GetToken" }), tokenHandler.GetToken)

	route.GET("/healthz", func(c *gin.Context) {
		c.String(http.StatusOK, "ok")
	})

	route.GET("/metrics", ginprom.PromHanlder)

	return route
}
