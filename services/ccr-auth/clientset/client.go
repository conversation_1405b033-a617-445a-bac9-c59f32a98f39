package clientset

import (
	"context"
	"fmt"
	"net/http"
	"net/url"

	"github.com/baidubce/bce-sdk-go/auth"
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/cce"
	"github.com/baidubce/bce-sdk-go/util"
	"sigs.k8s.io/controller-runtime/pkg/client"

	sdkiam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	internaliam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	lister "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/config"
)

type (
	ClientSetInterface interface {
		SqlClient() *models.Client
		K8sClient() client.Client
		InternalIam() internaliam.ClientInterface
		Lister() lister.ListerInterface
		HarborClient(instanceID string) (*harbor.HarborClient, error)
		BuildFakeHttpRequest(reqId, accountId, userId, endpoint, uri string) (*http.Request, error)

		LogicTagClientForAccount(accountId, userId string) (logictag.Client, error)
	}
)

type ClientSet struct {
	sqlClient   *models.Client
	cceClient   *cce.Client
	internalIam internaliam.ClientInterface

	k8sClient    client.Client
	harborClient *harbor.Client
	stsClient    *sts.Client

	conf   config.ServiceConfig
	lister lister.ListerInterface
}

func NewClientSet(conf config.ServiceConfig, cceCli *cce.Client, k8sCli client.Client, lister lister.ListerInterface) (*ClientSet, error) {

	sqlCli, err := models.NewClient(context.Background(), conf.MysqlConnection)
	if err != nil {
		return nil, err
	}

	internalIamCli := internaliam.NewClient(sdkiam.NewBceClient(&sdkiam.BceClientConfiguration{
		Endpoint: conf.IAMEndpoint,
		UserName: conf.ServiceName,
		Password: conf.ServicePassword,
		Retry:    sdkiam.DEFAULT_RETRY_POLICY,
		Domain:   "Default",
		Version:  "/v3",
	}))

	stsCli, err := sts.NewClient(conf.STSEndpoint, conf.IAMEndpoint, conf.ServiceName, conf.ServicePassword, conf.RoleName)
	if err != nil {
		return nil, err
	}

	return &ClientSet{
		sqlClient:    sqlCli,
		cceClient:    cceCli,
		k8sClient:    k8sCli,
		stsClient:    stsCli,
		internalIam:  internalIamCli,
		lister:       lister,
		harborClient: harbor.NewClient(conf.CCRDomainEndpoint),
		conf:         conf,
	}, nil
}

func (c *ClientSet) SqlClient() *models.Client {
	return c.sqlClient
}

func (c *ClientSet) K8sClient() client.Client {
	return c.k8sClient
}

func (c *ClientSet) InternalIam() internaliam.ClientInterface {
	return c.internalIam
}

func (c *ClientSet) Lister() lister.ListerInterface {
	return c.lister
}

func (c *ClientSet) HarborClient(instanceID string) (*harbor.HarborClient, error) {
	insInfo, err := c.lister.GetInstanceInfo(instanceID)
	if err != nil {
		return nil, fmt.Errorf("get instance info failed: %w", err)
	}
	harborHost := insInfo.ClusterURL
	harborPasswd := insInfo.Password

	return c.harborClient.HarborClientFor(harborHost, "admin", harborPasswd)
}

func (c *ClientSet) stsCredential(accountId, userId string) (*auth.BceCredentials, error) {
	cred, err := c.stsClient.GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	stsCredential, err := auth.NewSessionBceCredentials(
		cred.AccessKeyId,
		cred.SecretAccessKey,
		cred.SessionToken,
	)
	if err != nil {
		return nil, fmt.Errorf("create session credential failed: %w", err)
	}

	return stsCredential, nil
}

// used to do iam check
func (c *ClientSet) BuildFakeHttpRequest(reqId, accountId, userId, endpoint, uri string) (*http.Request, error) {
	// get cred
	stsCred, err := c.stsClient.GetCredential(accountId, userId)
	if err != nil {
		return nil, fmt.Errorf("get sts credential failed: %w", err)
	}

	cred := &auth.BceCredentials{
		AccessKeyId:     stsCred.AccessKeyId,
		SecretAccessKey: stsCred.SecretAccessKey,
		SessionToken:    stsCred.SessionToken,
	}

	// BceRequest used to calculate signature
	bceReq := &bce.BceRequest{}

	defaultSignOptions := &auth.SignOptions{
		HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
		ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS}

	v1Signer := &auth.BceV1Signer{}

	bceReq.SetRequestId(reqId)
	bceReq.SetMethod(http.MethodGet)
	bceReq.SetUri(uri)
	bceReq.SetEndpoint(endpoint)
	if bceReq.Protocol() == "" {
		bceReq.SetProtocol("http")
	}
	bceReq.SetTimeout(60)
	bceReq.SetHeader("Host", bceReq.Host())
	bceReq.SetHeader("User-Agent", "ccr-enterprise")
	bceReq.SetHeader("x-bce-date", util.FormatISO8601Date(util.NowUTCSeconds()))

	bceReq.BuildHttpRequest()

	v1Signer.Sign(&bceReq.Request, cred, defaultSignOptions)

	// construct http request from bcerequest
	httpRequest := &http.Request{
		Proto:      "HTTP/1.1",
		ProtoMajor: 1,
		ProtoMinor: 1,
	}

	// Set the request method
	httpRequest.Method = bceReq.Method()

	// Set the request url
	internalUrl := &url.URL{
		Scheme:   bceReq.Protocol(),
		Host:     bceReq.Host(),
		Path:     bceReq.Uri(),
		RawQuery: bceReq.QueryString()}
	httpRequest.URL = internalUrl

	// Set the request headers
	internalHeader := make(http.Header)
	for k, v := range bceReq.Headers() {
		val := make([]string, 0, 1)
		val = append(val, v)
		internalHeader[k] = val
	}
	httpRequest.Header = internalHeader

	return httpRequest, nil
}

func (c *ClientSet) LogicTagClientForAccount(accountId, userId string) (logictag.Client, error) {
	stsCred, err := c.stsCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	tagClient, err := logictag.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, c.conf.LogicTagEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create tag client failed: %w", err)
	}
	tagClient.Client.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return tagClient, nil
}
