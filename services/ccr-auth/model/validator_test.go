package model

import (
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/assert"
)

func Test_PasswordValidator(t *testing.T) {
	validate := validator.New()
	err := validate.RegisterValidation("password", PasswordValidate)
	assert.Nil(t, err)

	err = validate.VarWithValue("abcdefg", "var", "password")
	assert.NotNil(t, err)

	err = validate.VarWithValue("abcdefg123", "var", "password")
	assert.NotNil(t, err)

	err = validate.VarWithValue("abcdefg123ABC", "var", "password")
	assert.Nil(t, err)

	err = validate.VarWithValue("abcdefg123@#!", "var", "password")
	assert.Nil(t, err)

	err = validate.VarWithValue("123@#!", "var", "password")
	assert.NotNil(t, err)
}
