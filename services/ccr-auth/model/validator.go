package model

import (
	"github.com/go-playground/validator/v10"
)

func PasswordValidate(f validator.FieldLevel) bool {
	pass, ok := f.Field().Interface().(string)
	if ok {
		if len(pass) < 8 {
			return false
		}

		hasUpper, hasLower, hasNumber, hasSpecial := 0, 0, 0, 0
		for _, v := range pass {
			if v >= 'A' && v <= 'Z' {
				hasUpper = 1
				continue
			}

			if v >= 'a' && v <= 'z' {
				hasLower = 1
				continue
			}

			if v >= '0' && v <= '9' {
				hasNumber = 1
				continue
			}

			if int(v) > 32 {
				hasSpecial = 1
			}
		}

		if hasUpper+hasLower+hasNumber+hasSpecial < 3 {
			return false
		}
	}
	return true
}
