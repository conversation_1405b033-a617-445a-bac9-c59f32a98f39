package config

import (
	"io/ioutil"

	"k8s.io/apimachinery/pkg/util/yaml"
)

type ServiceConfig struct {
	AccessKey     string `yaml:"accessKey,omitempty"`
	SecretKey     string `yaml:"secretKey,omitempty"`
	ListenAddress string `yaml:"listenAddress,omitempty"`
	ClusterID     string `yaml:"clusterId,omitempty"`
	TokenIssuer   string `yaml:"tokenIssuer,omitempty"`
	Region        string `yaml:"region,omitempty"`

	TokenExpiredInMinute int `yaml:"tokenExpiredInMinute,omitempty"`
	TokenCachedInMinute  int `yaml:"tokenCachedInMinute,omitempty"`

	CCRDomainEndpoint string `yaml:"ccrDomainEndpoint,omitempty"`
	STSEndpoint       string `yaml:"stsEndpoint,omitempty"`
	CCEEndpoint       string `yaml:"cceEndpoint,omitempty"`
	IAMEndpoint       string `yaml:"iamEndpoint,omitempty"`
	ServiceName       string `yaml:"serviceName,omitempty"`
	ServicePassword   string `yaml:"servicePassword,omitempty"`
	RoleName          string `yaml:"roleName,omitempty"`
	MysqlConnection   string `yaml:"mysqlConnection,omitempty"`

	LogicTagEndpoint string `yaml:"logicTagEndpoint,omitempty"`
}

func NewConfig(filepath string) (*ServiceConfig, error) {
	content, err := ioutil.ReadFile(filepath)
	if err != nil {
		return nil, err
	}

	return NewConfigFromString(string(content))
}

func NewConfigFromString(content string) (*ServiceConfig, error) {
	var serviceConfig ServiceConfig
	err := yaml.Unmarshal([]byte(content), &serviceConfig)

	if err != nil {
		return nil, err
	}

	if serviceConfig.TokenExpiredInMinute == 0 {
		serviceConfig.TokenExpiredInMinute = 30
	}

	if serviceConfig.TokenCachedInMinute == 0 {
		serviceConfig.TokenCachedInMinute = 10
	}

	return &serviceConfig, nil
}
