package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_NewConfigFromString(t *testing.T) {
	src := `
tokenExpiredInMinute: 50
	ccrDomainEndpoint: test.com
`

	sc, err := NewConfigFromString(src)
	assert.Error(t, err)
	assert.Nil(t, sc)

	src = `
tokenExpiredInMinute: 50
ccrDomainEndpoint: test.com
`
	sc, err = NewConfigFromString(src)
	assert.NoError(t, err)
	assert.NotNil(t, sc)

	src = `
ccrDomainEndpoint: test.com
`
	sc, err = NewConfigFromString(src)
	assert.NoError(t, err)
	assert.NotNil(t, sc)
	assert.Equal(t, 30, sc.TokenExpiredInMinute)
	assert.Equal(t, 10, sc.TokenCachedInMinute)
}
