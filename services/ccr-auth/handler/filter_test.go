package handler

import (
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	ccriam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/clientset"
	testingiam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/iam/mock"
	testinglogictag "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/logictag"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-auth/clientset"
)

// Test_getResourceTagMap 用于测试 getResourceTagMap 函数
func Test_getResourceTagMap(t *testing.T) {
	type args struct {
		reqID                              string
		instanceIDs                        []string
		accountID, userID, region, service string
		clients                            clientset.ClientSetInterface
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "normal",
			args: func() args {

				cli := testingclientset.NewMockClientSetInterface(gomock.NewController(t))
				logicTagClient := testinglogictag.NewMockClient(gomock.NewController(t))

				logicTagClient.EXPECT().ListTags(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&logictag.ListTagsResult{
						TagAssociationFulls: []logictag.FullTagAssociation{
							{
								// 服务类型 如 BCC
								ServiceType: "CCR",
								// 标签所属账户ID
								AccountID: "xxxx",
								// 资源的ID 如 i-LGzaQZWI
								ResourceID: "xxxx",
								// 资源UUID
								ResourceUUID: "xxxx",
								// 标签内部ID，无需关注此字段
								TagID: 100,
								// 标签值
								TagValue: "value1",
								// 标签键
								TagKey: "key1",
								// 资源所在region
								Region: "gz",
								// 关联类型 associationType
								AssociationType: 1,
							},
						},
					}, nil).AnyTimes()

				cli.EXPECT().LogicTagClientForAccount(gomock.Any(), gomock.Any()).Return(logicTagClient, nil).AnyTimes()

				return args{
					reqID:       "reqId",
					instanceIDs: []string{"instanceId1", "instanceId2"},
					region:      "gz",
					accountID:   "xxx",
					userID:      "xxx",
					service:     "ccr-1xxxxxxx",
					clients:     cli,
				}

			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := getResourceTagMap(tt.args.reqID, tt.args.instanceIDs, tt.args.accountID, tt.args.userID, tt.args.region, tt.args.service, tt.args.clients)
			if err != nil {
				t.Errorf("case %s failed", tt.name)
			}
		})
	}
}

func Test_filterResourceActionByIAM(t *testing.T) {
	type args struct {
		reqId           string
		resourceActions *token.ResourceActions
		req             *permRequest
		clis            clientset.ClientSetInterface
	}
	tests := []struct {
		name    string
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "normal",
			args: func() args {
				cli := testingclientset.NewMockClientSetInterface(gomock.NewController(t))
				logicTagClient := testinglogictag.NewMockClient(gomock.NewController(t))

				logicTagClient.EXPECT().ListTags(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&logictag.ListTagsResult{
						TagAssociationFulls: []logictag.FullTagAssociation{
							{
								// 服务类型 如 BCC
								ServiceType: "CCR",
								// 标签所属账户ID
								AccountID: "xxxx",
								// 资源的ID 如 i-LGzaQZWI
								ResourceID: "xxxx",
								// 资源UUID
								ResourceUUID: "xxxx",
								// 标签内部ID，无需关注此字段
								TagID: 100,
								// 标签值
								TagValue: "value1",
								// 标签键
								TagKey: "key1",
								// 资源所在region
								Region: "gz",
								// 关联类型 associationType
								AssociationType: 1,
							},
						},
					}, nil).AnyTimes()

				cli.EXPECT().LogicTagClientForAccount(gomock.Any(), gomock.Any()).Return(logicTagClient, nil).AnyTimes()

				iamClient := testingiam.NewMockClientInterface(gomock.NewController(t))
				iamClient.EXPECT().VerifyUserPermission(gomock.Any(), gomock.Any()).Return(&ccriam.VerifyResult{
					VerifyResult: iam.VerifyResult{
						Effect: "ALLOW",
					},
				}, nil).AnyTimes()

				cli.EXPECT().InternalIam().Return(iamClient).AnyTimes()

				return args{
					reqId: "reqId",
					resourceActions: &token.ResourceActions{
						Type:    "repository",
						Actions: []string{"push", "pull"},
						Name:    "library/test",
					},
					req:  &permRequest{},
					clis: cli,
				}
			}(),
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return false
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.wantErr(t, filterResourceActionByIAM(tt.args.reqId, tt.args.resourceActions, tt.args.req, tt.args.clis), fmt.Sprintf("filterResourceActionByIAM(%v, %v, %v, %v)", tt.args.reqId, tt.args.resourceActions, tt.args.req, tt.args.clis))
		})
	}
}
