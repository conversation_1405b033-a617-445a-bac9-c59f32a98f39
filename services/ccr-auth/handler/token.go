package handler

import (
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/docker/distribution/registry/api/errcode"
	"github.com/gin-gonic/gin"
	"github.com/karlseguin/ccache/v2"
	"golang.org/x/crypto/bcrypt"
	apierrors "k8s.io/apimachinery/pkg/api/errors"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/middleware"
)

type TokenHandler struct {
	client        clientset.ClientSetInterface
	tokenMaker    *token.TokenMaker
	region        string
	cache         *ccache.Cache
	cacheInMinute int
}

func NewTokenHandler(tokenMaker *token.TokenMaker, cli clientset.ClientSetInterface, region string, cacheInMinute int) *TokenHandler {
	return &TokenHandler{
		tokenMaker:    tokenMaker,
		client:        cli,
		region:        region,
		cacheInMinute: cacheInMinute,
		cache:         ccache.New(ccache.Configure().MaxSize(5000).ItemsToPrune(500).GetsPerPromote(10)),
	}
}

// GetToken return token information
// url: xxxx/service/token?service=xxxx&scope=repository:ccr-image/harbor-addon:pull,push&scope=repository:ccr-image/harbor-addon:pull&scope=ormb:ccr:push
func (t *TokenHandler) GetToken(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	svc := c.Query("service")
	if svc == "" {
		logger.Errorf("no service parameter provided")
		c.AbortWithStatusJSON(http.StatusUnauthorized, errcode.Errors{errcode.ErrorCodeUnauthorized.WithDetail("no service parameter provided")})
		return
	}

	insInfo, err := t.client.Lister().GetInstanceInfo(svc)
	if err != nil {
		if apierrors.IsNotFound(err) {
			logger.Errorf("get instance info: %s", err)
			c.AbortWithStatusJSON(http.StatusUnauthorized, errcode.Errors{errcode.ErrorCodeUnauthorized.WithDetail(fmt.Sprintf("unknown instance : %s", svc))})
			return
		}
		logger.Errorf("get instance info failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, errcode.Errors{errcode.ErrorCodeUnknown.WithDetail(fmt.Sprintf("get instance info failed: %s", err))})
		return
	}
	resourceActions := token.GetResourceActions(c.QueryArray("scope"))
	expectedResourceActionStr := getResourceActionsExpr(resourceActions)

	accountID, userID := insInfo.Owner, ""
	username, password, isBasic := c.Request.BasicAuth()
	auth := c.Request.Header.Get("Authorization")

	// 如果head中包含Authorization信息，则表示用户为登录状态,那么需要校验用户auth正确性，
	// 即从缓存中查询basic信息，不再查询数据库，如果缓存中没有auth，那么调用IAM和验证密码
	if isBasic {
		// get token from cache first
		item := t.cache.Get(genUsernameAtAccountId(username, accountID, "auth"))
		var isValidAuth bool
		if item != nil && !item.Expired() {
			if a, ok := item.Value().(string); ok {
				if a == auth {
					isValidAuth = true
				}
			}
		}
		if !isValidAuth {
			userID, err = t.getUserID(c, accountID, username)
			if err != nil {
				logger.Errorf("get user ID failed: %v", err)
				c.AbortWithStatusJSON(http.StatusUnauthorized, errcode.Errors{errcode.ErrorCodeUnauthorized.WithDetail("user is disabled")})
				return
			}

			passwordMatched, err := t.passwordCheck(accountID, userID, insInfo.ID, password)
			if err != nil {
				logger.Errorf("check %v/%v password failed: %v", accountID, userID, err)
				c.AbortWithStatusJSON(http.StatusInternalServerError, errcode.Errors{errcode.ErrorCodeUnknown.WithDetail(fmt.Sprintf("check password failed: %s", err))})
				return
			}

			if !passwordMatched {
				logger.Errorf("user check failed, password not matched: %v, %v", username, password)
				c.AbortWithStatusJSON(http.StatusUnauthorized, errcode.Errors{errcode.ErrorCodeUnauthorized.WithDetail("user and password not matched")})
				return
			}
			// 如果用户校验通过，则缓存basic auth信息
			// 设置为5分钟主要为既可以解决短时间同一用户多长查询用户信息，又可以保证密码失效后可以短时间更新新
			t.cache.Set(genUsernameAtAccountId(username, accountID, auth), auth, 5*time.Minute)
		}
	}

	// get token from cache first
	item := t.cache.Get(genKey(username, svc, resourceActions))
	if item != nil && !item.Expired() {
		if token, ok := item.Value().(*token.Token); ok {
			c.JSON(http.StatusOK, token)
			return
		}
	}

	// check resource action
	if err := checkResourceActions(requestID, svc, accountID, userID, t.region, t.client, resourceActions); err != nil {
		logger.Errorf("filter action failed: %s", err)
		if errors.As(err, &errcode.Errors{}) {
			c.AbortWithStatusJSON(http.StatusBadRequest, errcode.Errors{errcode.ErrorCodeUnsupported.WithDetail(err.Error())})
			return
		}
		c.AbortWithStatusJSON(http.StatusInternalServerError, errcode.Errors{errcode.ErrorCodeUnknown.WithDetail(fmt.Sprintf("filter action failed: %s", err))})
		return
	}

	actualResourceActionStr := getResourceActionsExpr(resourceActions)

	token, err := t.tokenMaker.MakeToken(username, insInfo.PrivatePemKey, resourceActions)
	if err != nil {
		logger.Errorf("generate token failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, errcode.Errors{errcode.ErrorCodeUnknown.WithDetail(fmt.Sprintf("generate token failed: %s", err))})
		return
	}

	// token在有效期内，且token是有效token
	if t.cacheInMinute > 0 && token.ExpiresIn > t.cacheInMinute*60 && expectedResourceActionStr == actualResourceActionStr {
		t.cache.Set(genKey(username, svc, resourceActions), token, time.Duration(t.cacheInMinute)*time.Minute)
	}

	c.JSON(http.StatusOK, token)
}

func getResourceActionsExpr(ras []*token.ResourceActions) string {
	strs := []string{}

	for _, v := range ras {
		strs = append(strs, v.String())
	}

	return strings.Join(strs, " ")
}

// checkResourceActions check resource action
func checkResourceActions(requestID, svc, accountID, userID, region string, clis clientset.ClientSetInterface, resourceActions []*token.ResourceActions) error {
	for _, v := range resourceActions {

		if err := getFilter(v.Name).filter(requestID, svc, accountID, userID, region, clis, v); err != nil {
			return err
		}
	}
	return nil
}

func (t *TokenHandler) getUserID(c *gin.Context, accountID, username string) (string, error) {
	logger := middleware.LoggerFromContext(c)
	// 缓存用户信息是为了生成token这种高并发的接口，尽可能的减少外链调用，提高并发能力和可靠性
	item := t.cache.Get(genUsernameAtAccountId(username, accountID, "userid"))
	if item != nil && !item.Expired() {
		if userID, ok := item.Value().(string); ok {
			return userID, nil
		}
	}

	listResp, err := t.client.InternalIam().GetUsers(&iam.UserListRequest{
		DomainID: accountID,
		Name:     username,
	})
	if err != nil {
		logger.Errorf("list users for account %v failed: %v", accountID, err)
		return "", err
	}

	if len(listResp.UserList) != 1 {
		logger.Errorf("the users with name %v is not only one: %v", username, listResp.UserList)
		return "", err
	}

	if !listResp.UserList[0].Enabled {
		logger.Errorf("user is disabled: %v", listResp.UserList[0])
		return "", fmt.Errorf("user is disabled")
	}

	userID := listResp.UserList[0].ID

	// 用户变更属于低频操作，且可以提前产品层面说明用户失效的时间周期
	t.cache.Set(genUsernameAtAccountId(username, accountID, "userid"), userID, 30*time.Minute)
	return userID, nil
}

func (t *TokenHandler) passwordCheck(accountID, userID, instanceID, password string) (bool, error) {

	tokens, err := t.client.SqlClient().FindUserTokenByUser(accountID, userID, instanceID)
	if err != nil {
		return false, fmt.Errorf("get user by %v %v %v failed: %v", accountID, userID, instanceID, err)
	}

	if len(tokens) == 0 {
		return false, nil
	}

	for _, v := range tokens {
		if err = bcrypt.CompareHashAndPassword([]byte(v.PasswordHash), []byte(password)); err == nil {
			return true, nil
		}
	}

	return false, nil
}

func genKey(username string, service string, scopes []*token.ResourceActions) string {
	stringSlice := sort.StringSlice{username, service}
	for _, scope := range scopes {
		stringSlice = append(stringSlice, scope.String())
	}

	sort.Sort(stringSlice)

	return strings.Join(stringSlice, "#")
}

func genUsernameAtAccountId(username, accountId, typ string) string {
	return fmt.Sprintf("%s@%s@%s", username, accountId, typ)
}

// basicAuth "To receive authorization, the client sends the userid and password,
// separated by a single colon (":") character, within a base64
// encoded string in the credentials."
// It is not meant to be urlencoded.
func basicAuth(username, password string) string {
	auth := username + ":" + password
	return base64.StdEncoding.EncodeToString([]byte(auth))
}
