package handler

import (
	"errors"
	"fmt"
	"strings"
	"time"

	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"

	"github.com/docker/distribution/registry/api/errcode"
	"github.com/sirupsen/logrus"

	"icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
	ccrerr "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/errors"
	projectaddon "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/clientset"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	ActionPull        = "pull"
	ActionPush        = "push"
	ActionDelete      = "delete"
	ActionScannerPull = "scanner-pull"
)

type Filter interface {
	filter(reqId, service, accountId, userId, region string, clis clientset.ClientSetInterface, a *token.ResourceActions) error
}

func getFilter(t string) Filter {
	switch t {
	case "catalog":
		return &registryFilter{}
	default:
		return &repositoryFilter{parser: basicParser{}}
	}
}

type registryFilter struct {
}

// filter 函数用于根据IAM策略对用户对资源的操作进行过滤
//
// 参数：
// - reqId: 请求ID
// - service: 服务名
// - accountId: 账号ID
// - userId: 用户ID
// - region: 地域
// - clis: 客户端集合
// - resourceActions: 资源操作信息
//
// 返回值：
// - error: 如果用户对资源的操作不符合IAM策略，返回该错误
// - nil: 如果用户对资源的操作符合IAM策略，返回nil
func (rf *registryFilter) filter(reqId, service, accountId, userId, region string, clis clientset.ClientSetInterface, resourceActions *token.ResourceActions) error {
	if userId == "" {
		resourceActions.Actions = []string{}
		return nil
	}

	return filterResourceActionByIAM(
		reqId,
		resourceActions,
		&permRequest{
			region:    region,
			accountID: accountId,
			userID:    userId,
			resources: []string{fmt.Sprintf("instance/%s", service)},
			perms:     sets.NewString("OPERATE", "FULL_CONTROL", "CatalogRepository"),
			service:   service,
		},
		clis)
}

type repositoryFilter struct {
	parser imageParser
}

func (rf *repositoryFilter) filter(reqId, service, accountId, userId, region string, clis clientset.ClientSetInterface, resourceActions *token.ResourceActions) error {
	logger := logrus.WithField("request-id", reqId)

	img, err := rf.parser.parse(resourceActions.Name)
	if err != nil {
		errs := errcode.Errors{errcode.Error{
			Message: err.Error(),
			Detail:  err.Error(),
		}}
		return errs
	}

	if len(resourceActions.Actions) == 0 {
		logger.Infof("no permision required")
		resourceActions.Actions = []string{}
		return nil
	}

	// 参考linux 权限 rwx
	// ScannerPull和Pull使用r表示
	// Push 使用w表示
	// Delete 使用x表示
	permSet := sets.NewString()

	permission := "---"
	permissionBytes := []byte(permission)

	for _, action := range resourceActions.Actions {
		switch action {
		case ActionScannerPull, ActionPull:
			permissionBytes[0] = 'r'
		case ActionPush:
			permissionBytes[1] = 'w'
		case ActionDelete:
			permissionBytes[2] = 'x'
		}
	}
	permission = string(permissionBytes)

	// IAM FULL_CONTROL是包含READ和OPERATE的，但是READ和OPERATE是独立的关系。
	switch permission {
	case "r--":
		permSet = sets.NewString("READ", "OPERATE") // 用户action只有pull,READ,OPERATE都可以。
	case "rw-", "rwx", "r-x", "--x", "-wx", "-w-":
		permSet = sets.NewString("OPERATE", "FULL_CONTROL")
	case "---":
		permSet = sets.NewString("FULL_CONTROL")
	default:
		logger.Infof("no permision required")
		resourceActions.Actions = []string{}
		return nil
	}

	projectOverview, err := getProjectOverview(reqId, img.namespace, service, clis)
	if err != nil {
		var notFoundErr *project.HeadProjectNotFound
		if errors.As(err, &notFoundErr) {
			logger.Infof("project %s is not found, return empty permission", img.namespace)
			resourceActions.Actions = []string{}
			return nil
		}
		return fmt.Errorf("head project %s failed: %s", img.namespace, err)
	}

	// 逻辑: 命名空间为public类型,用户action只有pull,这直接放行,不在校验IAM权限。
	// 用户action只有pull,我们向IAM 申请权限的permSet会同时包含READ和OPERATE。
	if projectOverview != nil && projectOverview.Public == "true" {
		if permSet.HasAll("READ", "OPERATE") {
			// nothing change, has permission
			logger.Infof("project %s is public, return empty permission", img.namespace)
			return nil
		}
	}
	if userId == "" {
		logger.Infof("user id and account id is not provided, no permission")
		resourceActions.Actions = []string{}
		return nil
	}

	resources := []string{fmt.Sprintf("instance/%s", service), fmt.Sprintf("instance/%s/project/%s", service, img.namespace)}
	// check permission from IAM
	return filterResourceActionByIAM(
		reqId,
		resourceActions,
		&permRequest{
			region:    region,
			accountID: accountId,
			userID:    userId,
			resources: resources,
			perms:     permSet,
			service:   service,
		},
		clis)
}

// getProjectOverview 返回指定命名空间的服务对应的项目概览信息
//
// 参数:
//
//	reqId string - 请求 ID
//	namespace string - 命名空间
//	service string - 服务名
//	clis clientset.ClientSetInterface - Kubernetes 客户端集合接口
//
// 返回值:
//
//	*harbormodel.ModelsProjectOverview - 项目概览模型
//	error - 错误信息
func getProjectOverview(reqId string, namespace, service string, clis clientset.ClientSetInterface) (*harbormodel.ModelsProjectOverview, error) {
	logger := logrus.WithField("request-id", reqId)

	// project check, if porject is public, return directly
	harborCli, err := clis.HarborClient(service)
	if err != nil {
		return nil, fmt.Errorf("get harbor client failed: %w", err)
	}

	_, err = harborCli.V2Client.Project.HeadProject(
		project.NewHeadProjectParams().WithXRequestID(&reqId).WithProjectName(namespace).WithTimeout(5*time.Second),
		harborCli.AuthInfo,
	)
	if err != nil {
		logger.Errorf("head project %s failed: %s", namespace, err)
		return nil, err
	}

	resp, err := harborCli.AddonClient.Project.GetProjectOverview(
		projectaddon.NewGetProjectOverviewParams().WithXRequestID(&reqId).WithProjectName(namespace),
		harborCli.AuthInfo)

	if err != nil {
		return nil, fmt.Errorf("get project overview %s failed: %w", namespace, err)
	}
	return resp.GetPayload(), nil
}

type imageParser interface {
	parse(s string) (*image, error)
}

type image struct {
	namespace string
	repo      string
	tag       string
}

type basicParser struct{}

func (b basicParser) parse(s string) (*image, error) {
	return parseImg(s)
}

type endpointParser struct {
	endpoint string
}

func (e endpointParser) parse(s string) (*image, error) {
	repo := strings.SplitN(s, "/", 2)
	if len(repo) < 2 {
		return nil, fmt.Errorf("unable to parse image from string: %s", s)
	}
	if repo[0] != e.endpoint {
		return nil, fmt.Errorf("mismatch endpoint from string: %s, expected endpoint: %s", s, e.endpoint)
	}
	return parseImg(repo[1])
}

// build Image accepts a string like library/ubuntu:14.04 and build a image struct
func parseImg(s string) (*image, error) {
	repo := strings.SplitN(s, "/", 2)
	if len(repo) < 2 {
		return nil, fmt.Errorf("invalid repository name: %s", repo)
	}
	i := strings.SplitN(repo[1], ":", 2)
	res := &image{
		namespace: repo[0],
		repo:      i[0],
	}
	if len(i) == 2 {
		res.tag = i[1]
	}
	return res, nil
}

// getResourceTagMap 返回一个map，其中包含instanceIDs对应的标签信息
// reqID: 请求ID
// instanceIDs: CCR实例ID列表
// accountID: 账户ID
// userID: 用户ID
// region: 地域
// service: 服务名
// clients: ClientSetInterface接口
// 返回值1: 标签和资源的映射map[string][]model.Tag
// 返回值2: 错误信息，如果没有错误则为nil
func getResourceTagMap(reqID string, instanceIDs []string, accountID, userID, region,
	service string, clients clientset.ClientSetInterface) (map[string][]model.Tag, error) {
	logger := logrus.WithField("request-id", reqID).WithField("instance-id", service)

	tagResourceMap := map[string][]model.Tag{}
	if len(instanceIDs) == 0 {
		return tagResourceMap, nil
	}

	tagClient, err := clients.LogicTagClientForAccount(accountID, userID)
	if err != nil {
		logger.Errorf("new logic tag client failed: %s", err)
		return nil, err
	}

	tags, err := tagClient.ListTags(reqID, true, &logictag.ListTagsArgs{
		Regions:      []string{common.GetRealRegion(region)},
		ServiceTypes: []string{"CCR"},
		ResourceIds:  instanceIDs,
	})
	if err != nil {
		return nil, fmt.Errorf("[ListInstanceLocal] list tags failed: %s", err)
	}

	if tags != nil && len(tags.TagAssociationFulls) > 0 {

		for _, tagAssociation := range tags.TagAssociationFulls {
			resourceID := tagAssociation.ResourceID
			if _, ok := tagResourceMap[resourceID]; ok {
				tagResourceMap[resourceID] = append(tagResourceMap[resourceID], model.Tag{
					TagKey:   tagAssociation.TagKey,
					TagValue: tagAssociation.TagValue,
				})
			} else {
				tagResourceMap[resourceID] = []model.Tag{{
					TagKey:   tagAssociation.TagKey,
					TagValue: tagAssociation.TagValue,
				}}
			}
		}
	}
	return tagResourceMap, nil
}

// filterResourceActionByIAM 根据IAM权限过滤资源操作
//
// 参数:
// reqID string - 请求ID
// resourceActions *token.ResourceActions - 资源操作
// req *permRequest - 权限请求
// clis clientset.ClientSetInterface - 客户端集合接口
//
// 返回值:
// error - 错误信息，如果请求成功则为nil，否则为错误类型
func filterResourceActionByIAM(reqID string, resourceActions *token.ResourceActions, req *permRequest, clis clientset.ClientSetInterface) error {
	logger := logrus.WithField("request-id", reqID)

	var hasPermission bool
	for _, res := range req.resources {

		if err := verifyPermission(reqID, req.accountID, req.userID, res, req.region, req.service, req.perms.List(), clis); err != nil {
			logger.Errorf("verify permission failed: %s", err)
			var unauthorizedError *ccrerr.UnauthorizedError
			if errors.As(err, &unauthorizedError) {
				continue
			}
			return err
		}
		hasPermission = true
		break
	}

	if !hasPermission {
		logger.Errorf("no permission to do the request")
		resourceActions.Actions = []string{}
		return nil
	}

	return nil
}

// verifyPermission 用于验证用户对资源的权限
//
// 参数：
// reqID - 请求ID
// accountID - 用户账户ID
// userID - 用户ID
// res - 资源
// region - 地域
// service - 服务名
// perms - 权限列表
// clis - 客户端集合
//
// 返回值：
// error - 错误信息
func verifyPermission(reqID, accountID, userID, res, region, service string, perms []string, clis clientset.ClientSetInterface) error {
	logger := logrus.WithField("request-id", reqID)

	pr := &iam.PermissionRequest{
		Service:       common.IamServiceName,
		Region:        common.GetRealRegion(region),
		Resource:      res,
		ResourceOwner: common.DefaultIamOwner,
		Permission:    perms,
	}

	if service != "" {
		tagMap, err := getResourceTagMap(reqID, []string{service}, accountID, userID, region, service, clis)
		if err != nil {
			logger.Errorf("get resource tag failed: %v", err)
			return err
		}
		variables := make(map[string]interface{})
		if tags, ok := tagMap[service]; ok {
			for _, tag := range tags {
				variables[tag.TagKey] = tag.TagValue
			}
		}
		pr.RequestContext = iam.RequestContext{
			Variables: variables,
		}
	}

	result, err := clis.InternalIam().VerifyUserPermission(userID, pr)

	if err != nil {
		logger.Errorf("verify permission failed: %s", err)
		return err
	}
	logger.Infof("verify user permission response: %#v\n", result)

	if result.VerifyResult.Effect == "DENY" || result.VerifyResult.Effect == "DEFAULT_DENY" {
		return &ccrerr.UnauthorizedError{Effect: result.VerifyResult.Effect}
	}
	return nil
}

// who has resource perm
type permRequest struct {
	region    string
	accountID string
	userID    string
	resources []string
	perms     sets.String
	service   string
}
