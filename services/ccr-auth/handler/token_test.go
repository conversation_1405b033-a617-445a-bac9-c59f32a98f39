package handler

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/golang/mock/gomock"
	"github.com/karlseguin/ccache/v2"
	"github.com/stretchr/testify/assert"
	"k8s.io/apimachinery/pkg/api/errors"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/clientset"
	testinglisters "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/listers"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-auth/clientset"
)

type GinResponseWriter struct {
	http.ResponseWriter
}

func (g *GinResponseWriter) CloseNotify() <-chan bool {
	return make(chan bool)
}

func newGinResponseWriter() http.ResponseWriter {
	return &GinResponseWriter{httptest.NewRecorder()}
}

func Test_NewTokenHandler(t *testing.T) {
	th := NewTokenHandler(nil, nil, "test", 30)

	assert.NotNil(t, th)
	assert.Nil(t, th.tokenMaker)
	assert.Nil(t, th.client)
	assert.Equal(t, "test", th.region)
	assert.Equal(t, 30, th.cacheInMinute)
	assert.NotNil(t, th.cache)
}

func TestTokenHandler_GetToken(t *testing.T) {
	type fields struct {
		client        clientset.ClientSetInterface
		tokenMaker    *token.TokenMaker
		region        string
		cache         *ccache.Cache
		cacheInMinute int
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "instance not found",
			fields: func() fields {
				clients := testingclientset.NewMockClientSetInterface(gomock.NewController(t))
				lister := &testinglisters.MockLister{}
				mock.OnAnything(lister, "GetInstanceInfo").Return(
					nil,
					errors.NewNotFound(v1alpha1.Resource("ccr"), "ccr-xxxxxxxx"),
				)
				clients.EXPECT().Lister().Return(lister).AnyTimes()

				return fields{
					client: clients,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())

				ctx.Request, _ = http.NewRequest("GET", "/", nil)

				q := ctx.Request.URL.Query()
				q.Add("service", "ccr-xxxxxxxx")
				ctx.Request.URL.RawQuery = q.Encode()

				ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

				return args{
					c: ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			t := &TokenHandler{
				client:        tt.fields.client,
				tokenMaker:    tt.fields.tokenMaker,
				region:        tt.fields.region,
				cache:         tt.fields.cache,
				cacheInMinute: tt.fields.cacheInMinute,
			}
			t.GetToken(tt.args.c)
		})
	}
}
