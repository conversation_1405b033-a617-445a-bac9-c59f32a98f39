package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/baidubce/bce-sdk-go/services/cce"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	lister "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	_ "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/log"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/router"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/service/job"
)

var (
	serviceConfigFile string // ccr-auth 配置文件
	logFile           string // 日志打印文件
	runMode           string // gin 模式: debug release test
	version           string // 版本
)

var cmd = &cobra.Command{
	Use:     "ccr-auth",
	Short:   "CCR enterprise ccr service.",
	Long:    `CCR enterprise directly communicate with the front end and call the business interface of area a according to the requirement.`,
	Example: "go run main.go api --config=config.yaml",
	Run:     run,
	Version: version,
}

func init() {

	flags := cmd.Flags()

	flags.StringVar(&serviceConfigFile, "config", "", "ccr-auth service 配置文件.")
	flags.StringVar(&logFile, "log-file", "", "ccr-auth 日志路径, 不设置则打印到 STDOUT.")
	flags.StringVar(&runMode, "run-mode", "debug", "run 模式: debug release test.")
}

func main() {
	if err := cmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "error: %v\n", err)
		os.Exit(1)
	}
}

func run(*cobra.Command, []string) {

	if serviceConfigFile == "" {
		panic("config file is empty")
	}

	signalChan := make(chan os.Signal, 0)
	signal.Notify(signalChan, syscall.SIGTERM, syscall.SIGINT)

	stopChan := make(chan struct{})

	go func() {
		<-signalChan
		close(stopChan)
	}()

	conf, err := config.NewConfig(serviceConfigFile)
	if err != nil {
		panic(fmt.Sprintf("read config file failed: %s", err))
	}

	cceCli, err := cce.NewClient(conf.AccessKey, conf.SecretKey, conf.CCEEndpoint)
	if err != nil {
		panic(fmt.Sprintf("new cce client failed: %s", err))
	}

	kubeResult, err := cceCli.GetKubeConfig(&cce.GetKubeConfigArgs{
		ClusterUuid: conf.ClusterID,
		Type:        cce.KubeConfigTypeInternal,
	})
	if err != nil {
		panic(fmt.Sprintf("get kube config failed: %s", err))
	}

	k8sCli, err := utils.NewK8sClient(kubeResult.Data, scheme)
	if err != nil {
		panic(fmt.Sprintf("new k8s clinet failed: %s", err))
	}

	cachedClient, err := utils.NewK8sClientWithCache(context.Background(), kubeResult.Data, scheme, "", 30*time.Minute)
	if err != nil {
		panic(fmt.Errorf("new cached k8s client failed: %s", err))
	}

	clients, err := clientset.NewClientSet(*conf, cceCli, k8sCli, lister.NewLister(cachedClient))
	if err != nil {
		panic(fmt.Sprintf("create client set failed: %s", err))
	}

	// clean expired token every 10 minute
	tokenCleaner := job.NewTokenCleaner(clients, 10*time.Minute)

	go tokenCleaner.Start(stopChan)

	NewServer(conf, clients).Start(stopChan)
}

type Server struct {
	httpSrv *http.Server

	conf      *config.ServiceConfig
	clientset *clientset.ClientSet
}

func NewServer(conf *config.ServiceConfig, clientset *clientset.ClientSet) *Server {

	return &Server{
		conf:      conf,
		clientset: clientset,
	}
}

func (server *Server) Start(stopChan <-chan struct{}) {
	// set gin mode
	gin.SetMode(runMode)
	if runMode == "debug" {
		logrus.SetLevel(logrus.DebugLevel)
	}
	logrus.SetFormatter(&logrus.TextFormatter{DisableColors: true})

	r := router.NewGin(server.clientset, server.conf)
	server.httpSrv = &http.Server{
		Addr:           server.conf.ListenAddress,
		Handler:        r,
		ReadTimeout:    10 * time.Second,
		WriteTimeout:   10 * time.Second,
		MaxHeaderBytes: 1 << 20,
	}

	go func() {
		<-stopChan
		timeoutCtx, cancelCtx := context.WithTimeout(context.TODO(), 10*time.Second)
		defer cancelCtx()
		server.httpSrv.Shutdown(timeoutCtx)
	}()

	if err := server.httpSrv.ListenAndServe(); err != nil {
		fmt.Println("server was shutdown gracefully")
		panic(err)
	}
}
