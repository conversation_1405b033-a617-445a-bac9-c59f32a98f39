package router

import (
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/rest"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/acceleration-service/pkg/errdefs"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/acceleration-service/pkg/metrics"
)

const imageAccelerateWorkflowTemplateName = "image-converter-template"

func (r *LocalRouter) CreateAccelerate(ctx echo.Context) error {

	payload := new(rest.AccelerateRequest)
	if err := ctx.Bind(payload); err != nil {
		logger.Errorf("invalid accelerate payload: %s", err)
		return ReplyError(
			ctx, http.StatusBadRequest, errdefs.ErrIllegalParameter,
			"invalid accelerate payload",
		)
	}

	u, err := url.Parse(payload.Registry.URL)
	if err != nil {
		logger.Errorf("parse registry url err: %s", err)
		return ReplyError(
			ctx, http.StatusInternalServerError, errdefs.ErrConvertFailed,
			errors.Wrap(err, "parse registry url err").Error(),
		)
	}
	image := fmt.Sprintf("%s/%s:%s", u.Host, payload.Repository.RepoFullName, payload.Repository.Reference)

	if strings.HasSuffix(image, r.cfg.Accelerator.TagSuffix) {
		// FIXME: To check if an image has been converted, a better solution
		// is to use the annotation on image manifest.
		logger.Errorf("image has been converted")
		return ReplyError(
			ctx, http.StatusConflict, errdefs.ErrAlreadyConverted,
			"image has been converted",
		)
	}

	wfId, err := metrics.Conversion.OpWrap(func() (string, error) {
		id, err := r.accelerate(ctx, payload.Registry.Namespace, image, payload.Registry.Authorization)
		if err != nil {
			return "", err
		}
		return id, nil
	}, "convert")

	if err != nil {
		logger.Errorf("accelrate image is err: %s", err)
		return ReplyError(
			ctx, http.StatusInternalServerError, errdefs.ErrConvertFailed,
			err.Error(),
		)
	}

	res := &AccelerateResponse{
		ID: wfId,
	}
	return ctx.JSON(http.StatusAccepted, res)
}

func (r *LocalRouter) GetAccelerateState(ctx echo.Context) error {
	id := ctx.Param("id")

	imageConverterName, namespace, ok := strings.Cut(id, "@")
	if !ok {

		return fmt.Errorf("cut accelerate id failed: '@' does not appear in id")
	}

	wfClient := r.wfClientSet.ArgoprojV1alpha1().Workflows(namespace)

	wf, err := wfClient.Get(ctx.Request().Context(), imageConverterName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf("query argo workflow err: %s", err)
		return ReplyError(
			ctx, http.StatusInternalServerError, errdefs.ErrConvertFailed,
			"get argo-workflow error",
		)
	}

	if wf == nil {
		logger.Errorf("query argo workflow is nil")
		return ReplyError(
			ctx, http.StatusNotFound, errdefs.ErrNotFound,
			"get argo-workflow error",
		)
	}

	logger.Infof("Workflow %s %s at %v. Message: %s.\n", wf.Name, wf.Status.Phase, wf.Status.FinishedAt, wf.Status.Message)
	res := &AccelerateStateResponse{
		State: string(wf.Status.Phase),
	}
	if wf.Status.Phase == wfv1.WorkflowPending || wf.Status.Phase == wfv1.WorkflowRunning {
		ctx.Response().Header().Add("Location", ctx.Request().URL.String())
		ctx.Response().WriteHeader(http.StatusFound)
		ctx.JSON(http.StatusFound, res)
	}

	return ctx.JSON(http.StatusOK, res)
}

func (r *LocalRouter) accelerate(ctx echo.Context, namespace, source, accessCred string) (string, error) {

	username, password, ok := parseBasicAuth(accessCred)
	if !ok {
		return "", fmt.Errorf("no basic auth")
	}

	docker2oci, err := getDocker2oci(r.cfg.Accelerator.Driver.Config)
	if err != nil {
		return "", errors.Wrap(err, "parse get docker2oci from config")
	}

	wfClient := r.wfClientSet.ArgoprojV1alpha1().Workflows(namespace)

	convertWorkflow := &wfv1.Workflow{
		ObjectMeta: metav1.ObjectMeta{
			GenerateName: "image-convert-",
			Namespace:    namespace,
		},
		Spec: wfv1.WorkflowSpec{
			Arguments: wfv1.Arguments{
				Parameters: []wfv1.Parameter{
					{
						Name:  "source",
						Value: wfv1.AnyStringPtr(source),
					}, {
						Name:  "tagSuffix",
						Value: wfv1.AnyStringPtr(r.cfg.Accelerator.TagSuffix),
					}, {
						Name:  "path",
						Value: wfv1.AnyStringPtr("/tmp"),
					}, {
						Name:  "username",
						Value: wfv1.AnyStringPtr(username),
					}, {
						Name:  "password",
						Value: wfv1.AnyStringPtr(password),
					}, {
						Name:  "insecure",
						Value: wfv1.AnyStringPtr(r.cfg.Accelerator.SkipInsecureVerify),
					}, {
						Name:  "harborAnnotation",
						Value: wfv1.AnyStringPtr(r.cfg.Accelerator.HarborAnnotation),
					}, {
						Name:  "docker2oci",
						Value: wfv1.AnyStringPtr(docker2oci),
					}, {
						Name:  "driver",
						Value: wfv1.AnyStringPtr(r.cfg.Accelerator.Driver.Type),
					},
				},
			},
			WorkflowTemplateRef: &wfv1.WorkflowTemplateRef{
				Name:         imageAccelerateWorkflowTemplateName,
				ClusterScope: true,
			},
		},
	}
	logger.Infof("creating argo workflow convert image %s", source)
	createdWf, err := wfClient.Create(ctx.Request().Context(), convertWorkflow, metav1.CreateOptions{})
	if err != nil {
		return "", errors.Wrap(err, "create argo workflow convert image")
	}

	return fmt.Sprintf("%s@%s", createdWf.Name, namespace), nil
}

// parseBasicAuth parses an HTTP Basic Authentication string.
// "Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==" returns ("Aladdin", "open sesame", true).
func parseBasicAuth(auth string) (username, password string, ok bool) {
	const prefix = "Basic "
	// Case insensitive prefix match. See Issue 22736.
	if len(auth) < len(prefix) || !equalFold(auth[:len(prefix)], prefix) {
		return "", "", false
	}
	c, err := base64.StdEncoding.DecodeString(auth[len(prefix):])
	if err != nil {
		return "", "", false
	}
	cs := string(c)
	username, password, ok = strings.Cut(cs, ":")
	if !ok {
		return "", "", false
	}
	return username, password, true
}

// EqualFold is strings.EqualFold, ASCII only. It reports whether s and t
// are equal, ASCII-case-insensitively.
func equalFold(s, t string) bool {
	if len(s) != len(t) {
		return false
	}
	for i := 0; i < len(s); i++ {
		if lower(s[i]) != lower(t[i]) {
			return false
		}
	}
	return true
}

// lower returns the ASCII lowercase version of b.
func lower(b byte) byte {
	if 'A' <= b && b <= 'Z' {
		return b + ('a' - 'A')
	}
	return b
}

func getDocker2oci(cfg map[string]string) (docker2oci bool, err error) {
	if s, ok := cfg["docker2oci"]; ok {
		b, err := strconv.ParseBool(s)
		if err != nil {
			return false, err
		}
		docker2oci = b
	}
	return
}
