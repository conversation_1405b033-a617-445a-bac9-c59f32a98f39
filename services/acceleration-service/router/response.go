package router

import "github.com/labstack/echo/v4"

type ErrorResp struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func ReplyError(ctx echo.Context, status int, err error, message string) error {
	resp := &ErrorResp{
		Code:    err.Error(),
		Message: message,
	}
	return ctx.JSON(status, resp)
}

// AccelerateResponse represents the response returned by the accelerator adapter after accelerate request successfully
// submitted.
type AccelerateResponse struct {
	ID string `json:"id"`
}

// AccelerateStateResponse represents the response returned by the accelerator adapter after accelerate request successfully
// submitted.
type AccelerateStateResponse struct {
	State string `json:"state"`
}
