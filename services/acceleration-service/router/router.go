package router

import (
	"errors"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/sirupsen/logrus"
	"k8s.io/client-go/tools/clientcmd"

	wfclientset "github.com/argoproj/argo-workflows/v3/pkg/client/clientset/versioned"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/acceleration-service/config"
)

var logger = logrus.WithField("module", "api")

type Router interface {
	Register(server *echo.Echo) error
}

type LocalRouter struct {
	cfg         *config.Config
	wfClientSet *wfclientset.Clientset
}

func NewLocalRouter(cfg *config.Config) Router {
	// use the current context in kubeconfig
	config, err := clientcmd.BuildConfigFromFlags("", cfg.Accelerator.Kubeconfig)
	if err != nil {
		panic(err)
	}
	// create the workflow client
	wfClientSet := wfclientset.NewForConfigOrDie(config)

	return &LocalRouter{
		cfg:         cfg,
		wfClientSet: wfClientSet,
	}
}

func (r *LocalRouter) Register(server *echo.Echo) error {
	server.GET("/api/v1/health", r.CheckHealth)
	server.POST("/api/v1/accelerate", r.CreateAccelerate)
	server.GET("/api/v1/accelerate/:id/state", r.GetAccelerateState)

	// Any unexpected endpoint will return an error.
	server.Any("*", func(ctx echo.Context) error {
		return ReplyError(ctx, http.StatusNotFound, errors.New("ERR_INVALID_ENDPOINT"), "Endpoint not found")
	})

	return nil
}
