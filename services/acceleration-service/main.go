package main

import (
	"fmt"
	"os"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"github.com/urfave/cli/v2"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/acceleration-service/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/acceleration-service/router"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/acceleration-service/server"
)

var versionGitCommit string
var versionBuildTime string

func main() {
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: time.RFC3339Nano,
	})

	version := fmt.Sprintf("%s.%s", versionGitCommit, versionBuildTime)
	logrus.Infof("Version: %s\n", version)

	app := &cli.App{
		Name:    "acceleration-service",
		Usage:   "Provides a general service to support image acceleration based on kinds of accelerator like eStargz etc.",
		Version: version,
		Flags: []cli.Flag{
			&cli.StringFlag{Name: "config", Required: true, Usage: "Specify the path of config in yaml format"},
		},
		Action: func(c *cli.Context) error {
			cfg, err := config.Parse(c.String("config"))
			if err != nil {
				return err
			}

			router := router.NewLocalRouter(cfg)
			srv, err := server.NewHTTPServer(&cfg.Server, &cfg.Metric, router)
			if err != nil {
				return errors.Wrap(err, "create http server")
			}

			if err := srv.Run(); err != nil {
				return errors.Wrap(err, "run server")
			}

			return nil
		},
	}

	err := app.Run(os.Args)
	if err != nil {
		logrus.Fatal(err)
	}
}
