apiVersion: apps/v1
kind: Deployment
metadata:
  name: registry-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: registry-controller
  template:
    metadata:
      labels:
        app.kubernetes.io/name: registry-controller
      annotations:
        prometheus.io/path: /metrics
        prometheus.io/port: "8080"
        prometheus.io/scrape-pods: "true"
    spec:
      serviceAccountName: registry-controller
      containers:
        - name: registry-controller
          image: registry.baidubce.com/cce-plugin-dev/registry-controller
          args: ["-store-vpc-id=vpc-1fbm29puznk4", "-leader-elect=true"]
