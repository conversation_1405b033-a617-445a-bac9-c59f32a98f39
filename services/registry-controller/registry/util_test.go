package registry

import (
	"fmt"
	"golang.org/x/crypto/bcrypt"
	"testing"
	"time"
)

func TestCreateToken(t *testing.T) {
	//  polaris 用户的ID
	uid := "eca97e148cb74e9683d7b7240829d1ff"
	fmt.Printf("uid=%s\n", uid)

	token, err := createToken(uid)
	if err != nil {
		t.Fatal(err)
	}

	// 输出最终的 token 信息
	fmt.Printf("token=%s\n", token)

	// 对 polaris 用户的密码进行加密
	password, err := bcrypt.GenerateFromPassword([]byte("cse@2024"), bcrypt.DefaultCost)
	if err != nil {
		t.Fatal(err)
	}

	// 输出最终的密码值
	fmt.Printf("password=%s\n", string(password))

	time.Sleep(time.Second)
}

func TestDecodeToken(t *testing.T) {
	token := "+UyZGrZQ6cwdrxFpdGC9rpzZ3EmBbk+6sczGxK/DOFviXBqHyEjmwMXGxud6PVZRYYHFMffKYPrbyo5RVCA="
	v, err := decryptMessage([]byte("csebaidubce@2024"), token)

	if err != nil {
		t.Fatal(err)
	}

	t.Log(v)
}
