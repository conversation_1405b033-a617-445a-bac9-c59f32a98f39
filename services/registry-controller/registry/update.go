package registry

import (
	"context"
	"fmt"

	pkgrelease "helm.sh/helm/v3/pkg/release"
	appv1 "k8s.io/api/apps/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	csev1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/cse/v1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/helm"
)

func (c *RegisterCenterHandler) handleUpdate(ctx context.Context, object *csev1.Registry) error {
	esgUpdated, err := c.ensureEsg(ctx, object)
	if err != nil {
		_ = c.updateReason(ctx, object, "calibrateEsgFailed", err.<PERSON>rror())
		return err
	}
	serverConfigUpdated, err := c.ensureServerConfig(ctx, object)
	if err != nil {
		_ = c.updateReason(ctx, object, "calibrateServerConfigFailed", err.Error())
		return err
	}
	if esgUpdated || serverConfigUpdated {
		c.updatePhase(ctx, object, ccrv1alpha1.CCRCalibrating)
		_ = c.updateReason(ctx, object, "waitCalibrating", fmt.Sprintf("esgUpdated:%v,serverUpdated:%v", esgUpdated, serverConfigUpdated))
		return fmt.Errorf("waitCalibrating")
	}
	return nil
}

func (c *RegisterCenterHandler) ensureServerConfig(ctx context.Context, object *csev1.Registry) (bool, error) {
	values := c.getRegistryConfig(object)
	// expectRegistryCrdVersion 当前registry spec的hash值
	expectRegistryCrdVersion, err := getHash(values)
	if err != nil {
		return false, err
	}
	// currentRegistryCrdVersion 当前registry status的hash值
	currentRegistryCrdVersion := object.Status.GetRegistryCrdVersion()
	if expectRegistryCrdVersion == currentRegistryCrdVersion {
		// 两者一样，不需要调整，直接退出
		return false, nil
	}
	object.Status.WriteConfigServerVersion("", expectRegistryCrdVersion)

	st, err := helm.Status(ctx, object.Name, object.Name, c.mgr)
	if err != nil {
		return false, fmt.Errorf("helm status failed: %v", err)
	}

	if err = helm.Upgrade(ctx, object.GetName(), object.GetName(), ChartPath, values, c.mgr); err != nil {
		return false, fmt.Errorf("upgrade helm failed: %v", err)
	}
	if st == pkgrelease.StatusFailed {
		return false, fmt.Errorf("chart status is failed")
	}

	// helm upgrade后，确认configmap是否变化
	serverConfigVersion, err := c.currentServerConfigVersion(ctx, object)
	if err != nil {
		return false, fmt.Errorf("get cm failed: %v", err)
	}
	if serverConfigVersion == object.Status.GetServerConfigVersion() {
		// registry需要调整，但是server的configmap没有变化, 说明这里仅是vmagent调整了
		return true, nil
	}
	object.Status.WriteConfigServerVersion(serverConfigVersion, "")

	// server configmap有变化，需要重启server应用配置更新
	if err = c.updateServer(ctx, object); err != nil {
		return false, fmt.Errorf("update server failed: %v", err)
	}
	return true, nil
}

func (c *RegisterCenterHandler) ensureEsg(ctx context.Context, object *csev1.Registry) (bool, error) {
	if object.Spec.EsgID == "" {
		return false, nil
	}
	var netObj ccrv1alpha1.CNCNetwork
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &netObj)
	if err != nil {
		return false, fmt.Errorf("get cncnetwork %v failed: %w", object.GetName(), err)
	}

	if len(netObj.Spec.PrivateLinks) != 1 {
		return false, fmt.Errorf("unexpectPrivateLinkStatus, expect:%v, actual:%v", 1, len(netObj.Spec.PrivateLinks))
	}

	// 比较registry的esgId和cncnetwork的esgId，判断是否需要重新绑定安全组
	actualEsgId := netObj.Spec.PrivateLinks[0].EsgID
	expectEsgId := object.Spec.EsgID
	if actualEsgId == expectEsgId {
		return false, nil
	}

	netObj.Spec.PrivateLinks[0].EsgID = expectEsgId
	if err = c.client.Update(ctx, &netObj); err != nil {
		return false, err
	}
	return true, nil
}

func (c *RegisterCenterHandler) ensureCalibratingDone(ctx context.Context, object *csev1.Registry) error {
	// 1. 确认server状态ok
	err := c.ensureServerConfigCalibrateDone(ctx, object)
	if err != nil {
		_ = c.updateReason(ctx, object, "ensureServerConfigCalibrateDone", err.Error())
		return err
	}
	// 2. 确认esg状态ok
	err = c.ensureEsgCalibrateDone(ctx, object)
	if err != nil {
		_ = c.updateReason(ctx, object, "ensureEsgCalibrateDone", err.Error())
		return err
	}

	c.updatePhase(ctx, object, ccrv1alpha1.CCRRunning)
	return c.updateReason(ctx, object, "", "")
}

func (c *RegisterCenterHandler) ensureServerConfigCalibrateDone(ctx context.Context, object *csev1.Registry) error {
	ensureStsReplicas := func(stsName string) error {
		// 1. 获取sts
		var sts appv1.StatefulSet
		if err := c.client.Get(context.Background(), client.ObjectKey{Namespace: object.GetName(), Name: stsName}, &sts); err != nil {
			return fmt.Errorf("get sts failed: %v", err)
		}

		// 2. 确认sts副本状态
		if sts.Status.UpdatedReplicas != sts.Status.Replicas {
			return fmt.Errorf("wait server ready, replicas: %v, updated: %v", sts.Status.Replicas, sts.Status.UpdatedReplicas)
		}
		return nil
	}

	// 1. 确认registry-server副本更新ok
	if err := ensureStsReplicas(serviceName); err != nil {
		return err
	}

	// 2. 确认vmagent副本更新ok
	if err := ensureStsReplicas(vmagentName); err != nil {
		return err
	}

	return nil
}

func (c *RegisterCenterHandler) ensureEsgCalibrateDone(ctx context.Context, object *csev1.Registry) error {
	// 1. 获取服务网卡
	var netObj ccrv1alpha1.CNCNetwork
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &netObj)
	if err != nil {
		return err
	}
	if len(netObj.Status.LinkStatus) != 1 {
		return fmt.Errorf("get snic failed")
	}
	linkStatus := netObj.Status.LinkStatus[0]

	// 2. 确认服务网卡绑定的安全组
	if linkStatus.Status != ccrv1alpha1.PrivateLinkStatusCreated || linkStatus.EsgID != object.Spec.EsgID {
		return fmt.Errorf("wait binding esg")
	}

	return nil
}
