package registry

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	csev1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/cse/v1"
)

func (rc *RegisterCenterHandler) handleDelete(ctx context.Context, obj *csev1.Registry) error {
	rc.updatePhase(ctx, obj, ccrv1alpha1.CCRTerminating)
	_ = rc.client.Status().Update(ctx, obj)

	var err error
	// 1. 删除服务发布点和服务网卡
	if err = rc.deletePublishPoint(ctx, obj); err != nil {
		return err
	}

	// 2. 删除redis
	if err = rc.deleteRedis(ctx, obj); err != nil {
		return err
	}

	// 3. 删除 store
	if err = rc.deleteStore(ctx, obj); err != nil {
		return err
	}

	// 4. 删除namespace和polaris server
	if err = rc.deleteNamespace(ctx, obj); err != nil {
		return err
	}

	// 5. 删除cr
	if err = rc.removeFinalizer(ctx, obj); err != nil {
		return err
	}

	return nil
}

func (rc *RegisterCenterHandler) deleteNamespace(ctx context.Context, obj *csev1.Registry) error {
	var ns corev1.Namespace
	err := rc.client.Get(ctx, client.ObjectKey{Namespace: metav1.NamespaceNone, Name: obj.GetName()}, &ns)
	if err != nil && !apierrors.IsNotFound(err) {
		return fmt.Errorf("get namespace failed: %w", err)
	}

	if err == nil {
		if ns.DeletionTimestamp == nil {
			err = rc.client.Delete(ctx, &ns)
			if err != nil {
				return fmt.Errorf("delete namespace failed: %w", err)
			}
		}
		return fmt.Errorf("deleteing namespace, wait for next round")
	}
	return nil
}

func (rc *RegisterCenterHandler) deletePublishPoint(ctx context.Context, obj *csev1.Registry) error {
	var netObj ccrv1alpha1.CNCNetwork
	err := rc.client.Get(ctx, client.ObjectKey{Namespace: obj.GetName(), Name: obj.GetName()}, &netObj)
	if err != nil && !apierrors.IsNotFound(err) {
		_ = rc.updateReason(ctx, obj, "getCNCNetworkFailed", err.Error())
		return fmt.Errorf("get cncnetwork %v failed: %w", obj.GetName(), err)
	}

	if err == nil && netObj.DeletionTimestamp == nil {
		err = rc.client.Delete(ctx, &netObj)
		if err != nil {
			_ = rc.updateReason(ctx, obj, "deleteCNCNetworkFailed", err.Error())
			return fmt.Errorf("delete cncnetwork failed: %w", err)
		}
	}
	return nil
}

func (rc *RegisterCenterHandler) deleteRedis(ctx context.Context, obj *csev1.Registry) error {
	logger := log.FromContext(ctx)

	redis := ccrv1alpha1.Redis{}
	err := rc.client.Get(ctx, client.ObjectKey{Namespace: obj.GetName(), Name: obj.GetName()}, &redis)
	if err != nil && !apierrors.IsNotFound(err) {
		logger.V(2).Error(err, "get redis failed")
		_ = rc.updateReason(ctx, obj, ReasonRedisDeleteFailed, err.Error())
		return err
	}

	if apierrors.IsNotFound(err) {
		return nil
	}

	if redis.DeletionTimestamp == nil {
		err = rc.client.Delete(ctx, &redis)
		if err != nil {
			logger.V(2).Error(err, "delete redis failed")
			_ = rc.updateReason(ctx, obj, ReasonRedisDeleteFailed, err.Error())
			return err
		}
		return fmt.Errorf("deleteing redis, wait for next round")
	}
	return nil
}

func (rc *RegisterCenterHandler) deleteStore(ctx context.Context, obj *csev1.Registry) error {
	if obj == nil {
		return nil
	}
	return rc.storePool.Put(ctx, obj.GetName())
}
