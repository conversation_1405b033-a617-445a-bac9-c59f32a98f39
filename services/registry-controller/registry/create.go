package registry

import (
	"context"
	"database/sql"
	"encoding/base64"
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"

	pkgrelease "helm.sh/helm/v3/pkg/release"
	"helm.sh/helm/v3/pkg/storage/driver"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	csev1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/cse/v1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/helm"
)

func (rc *RegisterCenterHandler) toCreating(ctx context.Context, obj *csev1.Registry) error {
	finalizerSetted, err := rc.setFinalizer(ctx, obj)
	if err != nil {
		return err
	}
	if !finalizerSetted {
		return fmt.Errorf("need next round")
	}

	rc.updatePhase(ctx, obj, ccrv1alpha1.CCRCreating)
	_ = rc.updateReason(ctx, obj, "", "")
	return fmt.Errorf("wait create")
}

func (rc *RegisterCenterHandler) handleCreate(ctx context.Context, object *csev1.Registry) error {
	logger := log.FromContext(ctx)

	// 0. 创建namespace
	if err := rc.createNamespace(ctx, object); err != nil {
		_ = rc.updateReason(ctx, object, ccrv1alpha1.ReasonNamespaceNotReady, err.Error())
		return err
	}

	// 创建服务发布点和服务网卡
	if err := rc.createServicePublishPoint(ctx, object); err != nil {
		logger.V(2).Error(err, "publish point is not ready now")
		_ = rc.updateReason(ctx, object, ccrv1alpha1.ReasonServicePublishPointNotReady, err.Error())
		return err
	}

	// 1. 创建redis、mysql
	if err := rc.createStore(ctx, object); err != nil {
		logger.V(2).Error(err, "store not ready")
		_ = rc.updateReason(ctx, object, ReasonStoreNotReady, err.Error())
		return err
	}
	if err := rc.createRedis(ctx, object); err != nil {
		logger.V(2).Error(err, "redis not ok")
		_ = rc.updateReason(ctx, object, ReasonRedisNotReady, err.Error())
		return err
	}

	// 创建数据库和表结构
	if err := rc.importSQL(ctx, object); err != nil {
		logger.V(2).Error(err, "import sql not ok")
		_ = rc.updateReason(ctx, object, ReasonInitDBFailed, err.Error())
		return err
	}

	// 2. 部署注册中心
	if err := rc.helmInstall(ctx, object); err != nil {
		logger.V(2).Error(err, "helm install not ok")
		_ = rc.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseInstallFailed, err.Error())
		return err
	}

	// 3. 打通网络
	//serverBLB, err := rc.getServerBLB(ctx, object)
	//if err != nil {
	//	logger.V(2).Error(err, "get serverBLB failed")
	//	_ = rc.updateReason(ctx, object, ReasonServerBLBNotReady, err.Error())
	//	return err
	//}
	//object.Status.ServerBLB = serverBLB

	// 4. 更新状态，记录配置版本
	// 4.1 记录server的configmap版本
	serverConfigVersion, err := rc.currentServerConfigVersion(ctx, object)
	if err != nil {
		_ = rc.updateReason(ctx, object, "get config version failed", err.Error())
		return err
	}
	// 4.2 记录registry的版本
	registryCrdVersion, err := getHash(rc.getRegistryConfig(object))
	if err != nil {
		_ = rc.updateReason(ctx, object, "get server version failed", err.Error())
		return err
	}

	object.Status.WriteConfigServerVersion(serverConfigVersion, registryCrdVersion)
	object.Status.Token, err = rc.getToken(ctx, object)
	if err != nil {
		_ = rc.updateReason(ctx, object, "get token failed", err.Error())
		return err
	}
	rc.updatePhase(ctx, object, ccrv1alpha1.CCRRunning)
	return rc.updateReason(ctx, object, "", "")
}

func (rc *RegisterCenterHandler) createStore(ctx context.Context, object *csev1.Registry) error {
	owner := object.Name
	database := rc.storePool.FormatDatabase(object.Name)

	// 1. 确认是否已有 store
	if object.Status.Store.MySQL.Address != "" {
		return nil
	}

	// 2. 没有的话，从池子里取一个
	store, err := rc.storePool.Get(ctx, owner, database)
	if err != nil {
		return err
	}

	// 3. 等待 store 状态可用 且 database 符合预期
	if store.Status.Phase != ccrv1alpha1.GaiaDBStatusRunning || store.Status.Database != database {
		return fmt.Errorf("sotre not ready")
	}

	// 4. 记录 store 信息
	realPassword, err := base64.StdEncoding.DecodeString(store.Spec.Password)
	if err != nil {
		return err
	}

	object.Status.Store.MySQL.Address = store.Status.ClusterDetail.Host
	object.Status.Store.MySQL.Port = store.Status.ClusterDetail.Port
	object.Status.Store.MySQL.User = store.Status.AccountDetail.Username
	object.Status.Store.MySQL.Password = string(realPassword)
	object.Status.Store.MySQL.Database = database
	return nil
}

func (c *RegisterCenterHandler) createServicePublishPoint(ctx context.Context, object *csev1.Registry) error {
	var netObj ccrv1alpha1.CNCNetwork
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &netObj)
	if err != nil && !apierrors.IsNotFound(err) {
		_ = c.updateReason(ctx, object, "getCNCNetworkFailed", err.Error())
		return fmt.Errorf("get cncnetwork %v failed: %w", object.GetName(), err)
	}

	if apierrors.IsNotFound(err) {
		err = c.client.Create(ctx, &ccrv1alpha1.CNCNetwork{
			TypeMeta: metav1.TypeMeta{
				Kind:       "CNCNetwork",
				APIVersion: "cce.baidubce.com/v1alpha1",
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      object.GetName(),
				Namespace: object.GetName(),
				OwnerReferences: []metav1.OwnerReference{
					*ownerReferenceFor(object),
				},
				Labels: object.GetLabels(),
				Annotations: map[string]string{
					"auto-create-appblb": "true",
				},
			},
			Spec: ccrv1alpha1.CNCNetworkSpec{
				AccountID: object.Spec.AccountID,
				UserID:    object.Spec.UserID,
				Domain:    ccrv1alpha1.DomainSpec{},
				//BLBID:     object.Status.ServerBLB.ID,
				PrivateLinks: []ccrv1alpha1.PrivateLink{
					{
						VPCID:     object.Spec.VpcID,
						SubnetID:  object.Spec.SubnetID,
						CreatedBy: object.Spec.AccountID,
						EsgID:     object.Spec.EsgID,
					},
				},
			},
		})
		if err != nil {
			_ = c.updateReason(ctx, object, "createCNCNetworkFailed", err.Error())
			return fmt.Errorf("create cnc network failed: %w", err)
		}
	}

	if netObj.Status.BLBID == "" || netObj.Status.PublishPoint == "" || len(netObj.Status.LinkStatus) == 0 {
		_ = c.updateReason(ctx, object, "PublishPointNotReady", "waiting for publish point ready")
		return fmt.Errorf("waiting publish point ready, for next round")
	}

	object.Status.ServerBLB = &csev1.ServerBLB{
		ID:        netObj.Status.BLBID,
		Port:      serverBLBPort,
		BRegionIP: netObj.Status.BlbVip,
	}

	object.Status.EndpointList = []*csev1.Endpoint{}
	for _, pl := range netObj.Status.LinkStatus {
		if pl.IP == "" || pl.ServiceID == "" || pl.Status != ccrv1alpha1.PrivateLinkStatusCreated {
			_ = c.updateReason(ctx, object, "SNICNotReady", "waiting for SNIC ready")
			return fmt.Errorf("waiting SNIC ready, for next round")
		}
		object.Status.EndpointList = append(object.Status.EndpointList, &csev1.Endpoint{
			Type: csev1.EndpointTypePrivate,
			ID:   pl.ServiceID,
			IP:   pl.IP,
		})
	}

	return nil
}

func (c *RegisterCenterHandler) getServerBLB(ctx context.Context, object *csev1.Registry) (*csev1.ServerBLB, error) {
	namespace := object.Name
	name := serviceName
	svc := corev1.Service{}
	err := c.client.Get(ctx, client.ObjectKey{Namespace: namespace, Name: name}, &svc)
	if err != nil {
		return nil, err
	}

	annotations := svc.GetAnnotations()
	if annotations == nil {
		return nil, fmt.Errorf("no annotation in service %s/%s", namespace, name)
	}

	lbID := svc.GetAnnotations()[annotationLoadBalancerID]
	vip := svc.GetAnnotations()[annotationLoadBalancerVIP]

	if lbID == "" || vip == "" {
		return nil, fmt.Errorf("no lbID or vip in service %s/%s", namespace, name)
	}
	return &csev1.ServerBLB{
		ID:        lbID,
		IP:        "", // 暂时用不上
		Port:      serverBLBPort,
		BRegionIP: vip,
	}, nil
}

func (c *RegisterCenterHandler) helmInstall(ctx context.Context, object *csev1.Registry) error {
	logger := log.FromContext(ctx)
	st, err := helm.Status(ctx, object.Name, object.Name, c.mgr)
	if err != nil {
		if !errors.Is(err, driver.ErrReleaseNotFound) {
			logger.V(2).Error(err, fmt.Sprintf("find release %v failed", object.Name))
			_ = c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseStatusFailed, err.Error())
			return err
		}

		values := c.getRegistryConfig(object)
		err = helm.Install(ctx, object.GetName(), object.Name, ChartPath, values, c.mgr)
		if err != nil {
			logger.V(2).Error(err, "install chart failed")
			_ = c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseInstallFailed, err.Error())
			return err
		}

		return fmt.Errorf("after release install, wait for release ready")
	}
	if st == pkgrelease.StatusFailed {
		logger.V(2).Error(fmt.Errorf("install release failed"), "install relase failed")
		_ = c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseInstallFailed, "install release failed")
		return fmt.Errorf("install release failed")
	}
	return nil
}

func (c *RegisterCenterHandler) importSQL(ctx context.Context, object *csev1.Registry) error {
	logger := log.FromContext(ctx)
	mysql := object.Status.Store.MySQL

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local", mysql.User, mysql.Password, mysql.GetMasterAddress(), mysql.Port,
		c.storePool.FormatDatabase(object.Name))
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return err
	}
	defer db.Close()

	sqls, err := os.ReadFile("/home/<USER>/charts/server.sql")
	if err != nil {
		logger.V(2).Error(err, fmt.Sprintf("readfile %s failed", "/home/<USER>/charts/server.sql"))
		return err
	}

	for _, sqlStr := range strings.Split(string(sqls), ";") {
		sqlStr = strings.TrimSpace(sqlStr)
		if sqlStr == "" {
			continue
		}
		if strings.Contains(sqlStr, "%s") {
			sqlStr = fmt.Sprintf(sqlStr, c.storePool.FormatDatabase(object.Name))
		}
		sqlStr = strings.ReplaceAll(sqlStr, "$OWNER", object.Spec.AccountID)
		token, err := createToken(object.Spec.AccountID)
		if err != nil {
			logger.V(2).Error(err, "create token failed")
			return err
		}
		sqlStr = strings.ReplaceAll(sqlStr, "$TOKEN", token)
		logger.V(2).Info(sqlStr)
		if _, err = db.Exec(sqlStr); err != nil {
			logger.V(2).Error(err, fmt.Sprintf("exec %s failed", sqlStr))
			return err
		}
	}
	return nil
}

func (rc *RegisterCenterHandler) createRedis(ctx context.Context, object *csev1.Registry) error {
	logger := log.FromContext(ctx)
	var err error

	// 1. 先查后写
	redis := ccrv1alpha1.Redis{}
	err = rc.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &redis)
	if err != nil && !apierrors.IsNotFound(err) {
		logger.V(2).Error(err, "get object failed")
		return err
	}
	if apierrors.IsNotFound(err) {
		redis = ccrv1alpha1.Redis{
			TypeMeta: metav1.TypeMeta{
				Kind:       "Redis",
				APIVersion: "cce.baidubce.com/v1alpha1",
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      object.GetName(),
				Namespace: object.GetName(),
				Labels:    object.GetLabels(),
				OwnerReferences: []metav1.OwnerReference{
					*ownerReferenceFor(object),
				},
			},
			Spec: ccrv1alpha1.RedisSpec{
				InstanceType: string(ccrv1alpha1.AdvancedCCRType),
			},
		}

		err = rc.client.Create(ctx, &redis)
		if err != nil {
			logger.V(2).Error(err, "create redis failed")
			_ = rc.updateReason(ctx, object, ReasonRedisNotReady, err.Error())
			return err
		}
	}

	switch redis.Status.Phase {
	case ccrv1alpha1.RedisStatusRunning:
		// skip
	case ccrv1alpha1.RedisStatusFailed:
		rc.updatePhase(ctx, object, ccrv1alpha1.CCRStartingFailed)
		return errors.New(redis.Status.Message)
	default:
		return fmt.Errorf("waiting for redis ready")
	}

	object.Status.Store.Redis.Address = redis.Status.Host
	port, err := strconv.Atoi(redis.Status.Port)
	if err != nil {
		return err
	}
	object.Status.Store.Redis.Port = port
	object.Status.Store.Redis.User = redis.Status.UserName
	return nil
}

func (c *RegisterCenterHandler) createNamespace(ctx context.Context, obj *csev1.Registry) error {
	var ns corev1.Namespace
	err := c.client.Get(ctx, types.NamespacedName{Namespace: metav1.NamespaceNone, Name: obj.GetName()}, &ns)
	if err != nil && !apierrors.IsNotFound(err) {
		_ = c.updateReason(ctx, obj, ccrv1alpha1.ReasonNamespaceNotReady, err.Error())
		return fmt.Errorf("list namespace failed: %w", err)
	}

	if apierrors.IsNotFound(err) {
		err = c.client.Create(ctx, &corev1.Namespace{
			TypeMeta: metav1.TypeMeta{
				APIVersion: "v1",
				Kind:       "Namespace",
			},
			ObjectMeta: metav1.ObjectMeta{
				Name: obj.GetName(),
			},
		})
		if err != nil {
			_ = c.updateReason(ctx, obj, ccrv1alpha1.ReasonNamespaceNotReady, err.Error())
			return fmt.Errorf("create namespace failed: %w", err)
		}
	}
	return nil
}

func (c *RegisterCenterHandler) getToken(ctx context.Context, object *csev1.Registry) (string, error) {
	mysql := object.Status.Store.MySQL
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local", mysql.User, mysql.Password, mysql.GetMasterAddress(), mysql.Port,
		c.storePool.FormatDatabase(object.Name))
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return "", err
	}
	defer db.Close()

	queryToken := fmt.Sprintf("select token from user where id='%s'", object.Spec.AccountID)
	row := db.QueryRow(queryToken)
	if row == nil {
		return "", fmt.Errorf("select token failed")
	}
	var token string
	if err = row.Scan(&token); err != nil {
		return "", err
	}
	return token, nil
}
