package gaiadb

import (
	"regexp"
	"testing"

	"gotest.tools/assert"
)

func TestRandomPassword(t *testing.T) {
	p := randomPassword(10)

	assert.Equal(t, 10, len(p))

	matched, err := regexp.MatchString(`[a-zA-Z]`, p)
	assert.Nil<PERSON><PERSON><PERSON>(t, err)
	assert.Assert(t, matched)

	matchedNum, err := regexp.MatchString(`[0-9]`, p)
	assert.<PERSON>l<PERSON>rror(t, err)
	assert.Assert(t, matchedNum)
}
