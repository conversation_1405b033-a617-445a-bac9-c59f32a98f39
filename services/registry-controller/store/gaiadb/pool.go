package gaiadb

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

const (
	labelOwner = "cse.baidubce.com/owner"
	noOwner    = "none"

	defaultNamespace = "registry-system"
	namePrefix       = "cse"
	defaultUsername  = "registry"
)

type Pool struct {
	client client.Client

	config *Config
}

func NewPool(client client.Client, config *Config) (*Pool, error) {
	if config == nil {
		return nil, fmt.Errorf("config is empty")
	}
	return &Pool{
		client: client,
		config: config,
	}, nil
}

func (p *Pool) Start() {
	// 定期判断是否有足够多空闲的 gaiadb
	go func() {
		wait.Forever(func() {
			ctx := context.WithValue(context.Background(), "job", "idleChecker")
			logger := log.FromContext(ctx)

			idleList := &v1alpha1.GaiaDBList{}
			err := p.client.List(ctx, idleList, client.MatchingLabels{
				labelOwner: noOwner,
			})
			if err != nil {
				logger.Error(err, "list idle gaiadb failed")
				return
			}

			idleCount := 0
			for _, item := range idleList.Items {
				if item.Status.Phase == v1alpha1.GaiaDBStatusFailed {
					logger.Error(fmt.Errorf("gaiadb %s status is failed", item.Name), "")
					continue
				}
				idleCount++
			}
			if idleCount >= p.config.GetIdleNum() {
				logger.Info("has enough idle gaiadb")
				return
			}
			logger.Info("create more gaiadb")
			err = p.createIdleItem()
			if err != nil {
				logger.Error(err, "create idle gaiadb failed")
			}

		}, time.Duration(p.config.GetPeriodSeconds())*time.Second)
	}()
}

func (p *Pool) createIdleItem() error {
	ctx := context.WithValue(context.Background(), "job", "idleCreator")
	logger := log.FromContext(ctx)
	logger.Info("create more gaiadb")

	password := randomPassword(10)
	base64Password := base64.StdEncoding.EncodeToString([]byte(password))

	idleItem := &v1alpha1.GaiaDB{
		TypeMeta: metav1.TypeMeta{
			Kind:       "GaiaDB",
			APIVersion: "cce.baidubce.com/v1alpha1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-%s", namePrefix, utils.SimpleRandomAlphaNumberInLowerCase(10)),
			Namespace: defaultNamespace,
			Labels: map[string]string{
				labelOwner: noOwner,
			},
		},
		Spec: v1alpha1.GaiaDBSpec{
			InstanceParam: v1alpha1.InstanceParam{
				EngineVersion:       v1alpha1.EngineVersion(p.config.GetEngineVersion()),
				AllocatedCpuInCore:  p.config.GetAllocatedCpuInCore(),
				AllocatedMemoryInMB: p.config.GetAllocatedMemoryInMB(),
				VpcID:               p.config.GetVpcID(),
			},
			Username: defaultUsername,
			Password: base64Password,
		},
	}
	return p.client.Create(ctx, idleItem)
}

func (p *Pool) Get(ctx context.Context, owner, database string) (*v1alpha1.GaiaDB, error) {
	// 1. 获取 db
	selected, err := p.tryToGet(ctx, owner)
	if err != nil {
		return nil, err
	}

	// 2. 标上正在使用
	return p.mark(ctx, selected, owner, database)
}

// tryToGet
// 1. 先尝试根据 owner 获取对象
// 2. 不存在则从 无 owner 的对象列表里选一个 running 的对象
func (p *Pool) tryToGet(ctx context.Context, owner string) (*v1alpha1.GaiaDB, error) {
	// 1. 先尝试根据 owner 获取对象
	ownedList := &v1alpha1.GaiaDBList{}
	err := p.client.List(ctx, ownedList, client.MatchingLabels{
		labelOwner: owner,
	})
	if err != nil {
		return nil, err
	}
	if len(ownedList.Items) > 0 {
		owned := ownedList.Items[0]
		return &owned, nil
	}

	// 2. 不存在则从 无 owner 的对象列表里选一个 running 的对象
	idleList := &v1alpha1.GaiaDBList{}
	err = p.client.List(ctx, idleList, client.MatchingLabels{
		labelOwner: noOwner,
	})
	if err != nil {
		return nil, err
	}
	if len(idleList.Items) == 0 {
		return nil, fmt.Errorf("no avaiable gaiadb, please wait a moment")
	}

	for i := range idleList.Items {
		item := idleList.Items[i]
		if item.Status.Phase == v1alpha1.GaiaDBStatusRunning {
			return &item, nil
		}
	}
	return nil, fmt.Errorf("no avaiable gaiadb, please wait a moment")
}

func (p *Pool) mark(ctx context.Context, selected *v1alpha1.GaiaDB, owner, database string) (*v1alpha1.GaiaDB, error) {
	// 1. 校验 gaiadb 状态
	if selected.Status.Phase != v1alpha1.GaiaDBStatusRunning {
		return nil, fmt.Errorf("no avaiable gaiadb, please wait a moment")
	}

	// 2. 标记 owner
	needUpdate := false
	updated := selected.DeepCopy()
	if selected.Labels[labelOwner] != owner {
		updated.Labels[labelOwner] = owner
		needUpdate = true
	}

	// 3. 设置 database
	if selected.Spec.Database != database {
		updated.Spec.Database = database
		needUpdate = true
	}

	if !needUpdate {
		return updated, nil
	}
	// 4. 需要更新
	err := p.client.Update(ctx, updated)
	if err != nil {
		return nil, err
	}
	return updated, nil
}

func (p *Pool) Put(ctx context.Context, owner string) error {
	// 1. 查询 gaiadb
	ownedList := &v1alpha1.GaiaDBList{}
	err := p.client.List(ctx, ownedList, client.MatchingLabels{
		labelOwner: owner,
	})
	// todo 怎么判断 not found
	if err != nil {
		return err
	}
	if len(ownedList.Items) == 0 {
		return nil
	}
	if len(ownedList.Items) > 1 {
		return fmt.Errorf("%s has too more gaiadb, num=%d", owner, len(ownedList.Items))
	}
	owned := ownedList.Items[0]

	// 2. 去除标记
	return p.unmark(ctx, &owned)
}

func (p *Pool) unmark(ctx context.Context, obj *v1alpha1.GaiaDB) error {
	updated := obj.DeepCopy()
	updated.Labels[labelOwner] = noOwner
	updated.Spec.Database = ""

	return p.client.Update(ctx, updated)
}
