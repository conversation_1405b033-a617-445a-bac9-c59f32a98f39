package gaiadb

import (
	"math/rand"
	"strings"
	"time"
)

// FormatDatabase 格式化 database，gaiadb 不支持中划线
func (p *Pool) FormatDatabase(database string) string {
	// gaiadb 不支持中划线
	return strings.ReplaceAll(database, "-", "_")
}

func randomPassword(length int) string {
	rand.Seed(time.Now().UnixNano())
	chars := []rune("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz")
	numbers := []rune("0123456789")
	var b []rune
	for i := 0; i < length-2; i++ {
		b = append(b, chars[rand.Intn(len(chars))])
	}
	b = append(b, numbers[rand.Intn(len(numbers))])
	b = append(b, chars[rand.Intn(len(chars))])
	rand.Shuffle(len(b), func(i, j int) {
		b[i], b[j] = b[j], b[i]
	})
	return string(b)
}
