package gaiadb

import "fmt"

const (
	defaultIdleNum       = 2
	defaultPeriodSeconds = 10

	defaultEngineVersion       = "8.0"
	defaultAllocatedCpuInCore  = 1
	defaultAllocatedMemoryInMB = 1024
)

type Config struct {
	IdleConfig IdleConfig
	DBConfig   DBConfig
}

type IdleConfig struct {
	IdleNum       int
	PeriodSeconds int
}

type DBConfig struct {
	EngineVersion       string
	AllocatedCpuInCore  int
	AllocatedMemoryInMB int
	VpcID               string
}

func NewConfig(idleNum, periodSeconds int, engineVersion string, allocatedCpuInCore, allocatedMemoryInMB int, vpcID string) (*Config, error) {
	if vpcID == "" {
		return nil, fmt.Errorf("must set vpcID")
	}
	return &Config{
		IdleConfig: IdleConfig{
			IdleNum:       idleNum,
			PeriodSeconds: periodSeconds,
		},
		DBConfig: DBConfig{
			EngineVersion:       engineVersion,
			AllocatedCpuInCore:  allocatedCpuInCore,
			AllocatedMemoryInMB: allocatedMemoryInMB,
			VpcID:               vpcID,
		},
	}, nil
}

func (c *Config) GetIdleNum() int {
	if c != nil && c.IdleConfig.IdleNum > 0 {
		return c.IdleConfig.IdleNum
	}
	return defaultIdleNum
}

func (c *Config) GetPeriodSeconds() int {
	if c != nil && c.IdleConfig.PeriodSeconds > 0 {
		return c.IdleConfig.PeriodSeconds
	}
	return defaultPeriodSeconds
}

func (c *Config) GetEngineVersion() string {
	if c != nil && c.DBConfig.EngineVersion != "" {
		return c.DBConfig.EngineVersion
	}
	return defaultEngineVersion
}

func (c *Config) GetAllocatedCpuInCore() int {
	if c != nil && c.DBConfig.AllocatedCpuInCore > 0 {
		return c.DBConfig.AllocatedCpuInCore
	}
	return defaultAllocatedCpuInCore
}

func (c *Config) GetAllocatedMemoryInMB() int {
	if c != nil && c.DBConfig.AllocatedMemoryInMB > 0 {
		return c.DBConfig.AllocatedMemoryInMB
	}
	return defaultAllocatedMemoryInMB
}

func (c *Config) GetVpcID() string {
	return c.DBConfig.VpcID
}
