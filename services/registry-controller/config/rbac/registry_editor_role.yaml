# permissions for end users to edit registries.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: cse-controller
    app.kubernetes.io/managed-by: kustomize
  name: cse-editor-role
rules:
- apiGroups:
  - cse.baidubce.com
  resources:
  - registries
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - cse.baidubce.com
  resources:
  - registries/status
  verbs:
  - get
