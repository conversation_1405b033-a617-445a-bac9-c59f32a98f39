# permissions for end users to view registries.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: cse-controller
    app.kubernetes.io/managed-by: kustomize
  name: cse-viewer-role
rules:
- apiGroups:
  - cse.baidubce.com
  resources:
  - registries
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - cse.baidubce.com
  resources:
  - registries/status
  verbs:
  - get
