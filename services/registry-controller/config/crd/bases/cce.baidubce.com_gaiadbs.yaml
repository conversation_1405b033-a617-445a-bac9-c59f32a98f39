---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: gaiadbs.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    kind: GaiaDB
    listKind: GaiaDBList
    plural: gaiadbs
    singular: gaiadb
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: PHASE
      type: string
    - jsonPath: .status.clusterID
      name: DBID
      type: string
    - jsonPath: .spec.database
      name: DATABASE
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: GaiaDB is the Schema for the gaiadb API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: GaiaDBSpec defines the desired state of GaiaDB
            properties:
              database:
                type: string
              instanceParam:
                properties:
                  allocatedCpuInCore:
                    description: AllocatedCpuInCore 取值参考 https://cloud.baidu.com/doc/GaiaDB/s/nl89lanve#%E9%9B%86%E7%BE%A4%E5%8F%AF%E9%80%89%E5%A5%97%E9%A4%90
                    type: integer
                  allocatedMemoryInMB:
                    type: integer
                  engineVersion:
                    type: string
                  subnetID:
                    type: string
                  vpcID:
                    type: string
                required:
                - allocatedCpuInCore
                - allocatedMemoryInMB
                - engineVersion
                - subnetID
                - vpcID
                type: object
              password:
                type: string
              username:
                type: string
            required:
            - instanceParam
            - password
            - username
            type: object
          status:
            description: GaiaDBStatus defines the observed state of GaiaDB
            properties:
              accountDetail:
                properties:
                  database:
                    type: string
                  username:
                    type: string
                type: object
              clusterDetail:
                properties:
                  allocatedCpuInCore:
                    type: integer
                  allocatedMemoryInMB:
                    type: integer
                  host:
                    type: string
                  port:
                    type: integer
                required:
                - allocatedCpuInCore
                - allocatedMemoryInMB
                type: object
              clusterID:
                type: string
              database:
                type: string
              lastProbeTime:
                format: date-time
                type: string
              lastTransitionTime:
                format: date-time
                type: string
              message:
                type: string
              phase:
                description: |-
                  INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                type: string
              reason:
                type: string
            required:
            - accountDetail
            - clusterDetail
            - database
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
