---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: ccrs.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    kind: CCR
    listKind: CCRList
    plural: ccrs
    singular: ccr
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.type
      name: CCR_TYPE
      type: string
    - jsonPath: .spec.storage.bucket
      name: STORAGE
      priority: 1
      type: string
    - jsonPath: .status.phase
      name: PHASE
      type: string
    - jsonPath: .metadata.labels.name
      name: INSTANCE_NAME
      type: string
    - jsonPath: .spec.accountID
      name: ACCOUNT_ID
      priority: 1
      type: string
    - jsonPath: .status.startTime
      name: START_TIME
      type: string
    - jsonPath: .spec.expireTime
      name: EXPIRE_TIME
      type: string
    - jsonPath: .spec.action
      name: BILLING_ACTION
      priority: 1
      type: string
    - jsonPath: .status.reason
      name: REASON
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: CCR is the Schema for the ccrs API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: CCRSpec defines the desired state of CCR
            properties:
              accountID:
                description: AccountID used to record the owner of ccr
                type: string
              action:
                description: Action the billing resource action
                type: string
              database:
                description: Database
                properties:
                  databases:
                    description: Databases means the databases provided
                    items:
                      type: string
                    type: array
                  host:
                    description: Host database host
                    type: string
                  password:
                    description: Password should be base64
                    type: string
                  port:
                    description: Port means the port of database can be access
                    type: integer
                  username:
                    description: Username means the username used to access database
                    type: string
                type: object
              expireTime:
                description: ExpireTime the  billing expire time of CCR
                format: date-time
                type: string
              storage:
                description: Storage spec of storage, only support BOS now
                properties:
                  bucket:
                    description: Bucket name of bos bucket
                    type: string
                type: object
              type:
                description: Type the specification of CCR
                type: string
              userID:
                description: AccountID used to record the owner of ccr
                type: string
            type: object
          status:
            description: CCRStatus defines the observed state of CCR
            properties:
              action:
                type: string
              bucket:
                description: Bucket bos bucket name
                type: string
              ccrDomain:
                description: CCRDomain the domain name used to be access by CCR service
                type: string
              conditions:
                description: Conditions indicate the component condition
                items:
                  description: ComponentCondition the current status of one component
                  properties:
                    health:
                      description: Health health level
                      type: string
                    lastProbeTime:
                      description: LastProbeTime the lastest time do the health probe
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: LastTransitionTime the lastest time that the Health
                        change
                      format: date-time
                      type: string
                    message:
                      description: Message the manual message along with Reason
                      type: string
                    name:
                      description: Name component name
                      type: string
                    reason:
                      description: Reason the reason why the component is unhealthy
                      type: string
                  type: object
                type: array
              database:
                description: Database means the status of RDS instance
                properties:
                  databases:
                    items:
                      type: string
                    type: array
                type: object
              isResGroupSynchronized:
                description: isResGroupSynchronized indicates whether the resource
                  group is synchronized
                type: string
              lastProbeTime:
                description: LastProbeTime the last access time
                format: date-time
                type: string
              message:
                description: Message manual message along with Reason
                type: string
              p2pManagerDomain:
                description: P2PManagerDomain the service domain name used to access
                  in the P2P manager network
                type: string
              phase:
                description: Phase current phase
                type: string
              privateDomain:
                description: PrivateDomain the service domain name used in the private
                  network
                type: string
              publicDomain:
                description: PublicDomain the service domain name used to access in
                  the public network
                type: string
              publishPoint:
                description: PublishPoint the service publish point, related with
                  BLB
                type: string
              reason:
                description: Reason indicate why the component cannot work. Empty
                  if everything is ok
                type: string
              startTime:
                description: StartTime the time ccr can work
                format: date-time
                type: string
              type:
                description: CCRType defines the types supported
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
