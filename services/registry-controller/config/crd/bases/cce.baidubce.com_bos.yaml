---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: bos.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    kind: Bos
    listKind: BosList
    plural: bos
    singular: bos
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: PHASE
      type: string
    - jsonPath: .status.reason
      name: REASON
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Bos is the Schema for the bos API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: BosSpec defines the desired state of Bos
            properties:
              externalAccountID:
                type: string
              externalUserID:
                type: string
              needEncrypt:
                type: boolean
              needLifeCycle:
                type: boolean
              needTrash:
                type: boolean
              userID:
                type: string
            type: object
          status:
            description: BosStatus defines the observed state of Bos
            properties:
              acl:
                type: string
              bucket:
                type: string
              lastProbeTime:
                format: date-time
                type: string
              lastTransitionTime:
                format: date-time
                type: string
              message:
                type: string
              phase:
                type: string
              reason:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
