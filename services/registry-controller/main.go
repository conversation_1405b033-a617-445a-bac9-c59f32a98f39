/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"flag"
	"os"

	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/registry-controller/store/gaiadb"

	ccev1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	csev1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/cse/v1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/registry-controller/controller"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/registry-controller/registry"
	//+kubebuilder:scaffold:imports
)

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("setup")
)

// 启动参数
var (
	metricsAddr          string
	enableLeaderElection bool
	probeAddr            string
	secureMetrics        bool
	enableHTTP2          bool

	// 存储配置
	storePoolIdleNum            int
	storePoolCheckPeriodSeconds int

	storeEngineVersion       string
	storeAllocatedCpuInCore  int
	storeAllocatedMemoryInMB int
	storeVpcID               string

	// 地域 gz/bj
	region string
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))

	utilruntime.Must(csev1.AddToScheme(scheme))

	utilruntime.Must(ccev1alpha1.AddToScheme(scheme))
	//+kubebuilder:scaffold:scheme
}

func initFlags() {
	flag.StringVar(&metricsAddr, "metrics-bind-address", ":8080", "The address the metric endpoint binds to. "+
		"Use the port :8080. If not set, it will be '0 in order to disable the metrics server")
	flag.StringVar(&probeAddr, "health-probe-bind-address", ":8081", "The address the probe endpoint binds to.")
	flag.BoolVar(&enableLeaderElection, "leader-elect", false,
		"Enable leader election for controller manager. "+
			"Enabling this will ensure there is only one active controller manager.")
	flag.BoolVar(&secureMetrics, "metrics-secure", false,
		"If set the metrics endpoint is served securely")
	flag.BoolVar(&enableHTTP2, "enable-http2", false,
		"If set, HTTP/2 will be enabled for the metrics and webhook servers")

	// store argument
	flag.IntVar(&storePoolIdleNum, "store-pool-idle-num", 2, "idle num of store pool")
	flag.IntVar(&storePoolCheckPeriodSeconds, "store-pool-check-period-seconds", 10, "period of checking idle item in store pool")
	flag.StringVar(&storeEngineVersion, "store-engin-version", "8.0", "engine version of store, such as 8.0")
	flag.IntVar(&storeAllocatedCpuInCore, "store-allocated-cpu-in-core", 1, "cpu core of store, such as 1")
	flag.IntVar(&storeAllocatedMemoryInMB, "store-allocated-memory-in-mb", 1024, "memory of store, such as 1024")
	flag.StringVar(&storeVpcID, "store-vpc-id", "", "vpcID of store, required")

	flag.StringVar(&region, "region", "gz", "region")

	opts := zap.Options{
		Development: true,
	}
	opts.BindFlags(flag.CommandLine)
	flag.Parse()

	ctrl.SetLogger(zap.New(zap.UseFlagOptions(&opts)))
}

func main() {
	initFlags()

	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme:                 scheme,
		MetricsBindAddress:     metricsAddr,
		HealthProbeBindAddress: probeAddr,
		LeaderElection:         enableLeaderElection,
		LeaderElectionID:       "37c6196e.baidubce.com",
	})
	if err != nil {
		setupLog.Error(err, "unable to start manager")
		os.Exit(1)
	}

	client, err := utils.NewK8sClient("", scheme)
	if err != nil {
		setupLog.Error(err, "unable create cluster client")
		os.Exit(1)
	}

	storeConfig, err := gaiadb.NewConfig(storePoolIdleNum, storePoolCheckPeriodSeconds, storeEngineVersion,
		storeAllocatedCpuInCore, storeAllocatedMemoryInMB, storeVpcID)
	if err != nil {
		setupLog.Error(err, "unable init store config")
		os.Exit(1)
	}
	storePool, err := gaiadb.NewPool(client, storeConfig)
	if err != nil {
		setupLog.Error(err, "unable init store pool")
		os.Exit(1)
	}
	storePool.Start()

	if err = (&controller.RegistryReconciler{
		Client:  mgr.GetClient(),
		Scheme:  mgr.GetScheme(),
		Handler: registry.NewRegisterCenterHandler(client, mgr, storePool, region),
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "RegisterInstance")
		os.Exit(1)
	}
	//+kubebuilder:scaffold:builder

	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up health check")
		os.Exit(1)
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up ready check")
		os.Exit(1)
	}

	setupLog.Info("starting manager")
	if err := mgr.Start(ctrl.SetupSignalHandler()); err != nil {
		setupLog.Error(err, "problem running manager")
		os.Exit(1)
	}
}
