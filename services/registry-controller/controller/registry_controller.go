/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"time"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	csev1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/cse/v1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/registry-controller/registry"
)

// RegistryReconciler reconciles a Registry object
type RegistryReconciler struct {
	client.Client
	Scheme  *runtime.Scheme
	Handler *registry.RegisterCenterHandler
}

//+kubebuilder:rbac:groups=cse.baidubce.com,resources=registries,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=cse.baidubce.com,resources=registries/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=cse.baidubce.com,resources=registries/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the Registry object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.17.3/pkg/reconcile
func (r *RegistryReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	object := csev1.Registry{}
	err := r.Client.Get(ctx, req.NamespacedName, &object)
	if apierrors.IsNotFound(err) {
		logger.V(2).Info(fmt.Sprintf("object %v is not found, mybe deleted", req.NamespacedName))
		return ctrl.Result{}, nil
	}

	if err != nil {
		logger.V(2).Error(err, fmt.Sprintf("get object failed for %v", req.NamespacedName))
		return ctrl.Result{RequeueAfter: time.Second}, err
	}

	err = r.Handler.Handle(ctx, &object)
	if err != nil {
		logger.V(2).Error(err, "handle object failed")
		return ctrl.Result{RequeueAfter: time.Second}, nil
	}

	return ctrl.Result{}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *RegistryReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&csev1.Registry{}).
		Complete(r)
}
