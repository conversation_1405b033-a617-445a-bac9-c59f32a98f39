package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
)

var (
	PhaseChangeDurationSeconds = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "phase_change_duration_seconds",
			Buckets: []float64{1, 3, 5, 10, 30, 60, 120, 300, 600, 1800},
		},
		[]string{"instanceId", "srcPhase", "dstPhase"},
	)

	CseInstanceStatusPhase = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "cse_instance_status_phase",
		},
		[]string{"instanceId", "phase"},
	)
)

func init() {
	metrics.Registry.MustRegister(PhaseChangeDurationSeconds)
	metrics.Registry.MustRegister(CseInstanceStatusPhase)
}
