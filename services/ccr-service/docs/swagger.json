{"swagger": "2.0", "info": {"description": "CCR Service 提供 RESTFUL 风格 API, 对接 Console 及 OpenAPI 请求", "title": "CCR Service API", "contact": {"name": "duzhanwei,wenmanxiang", "email": "<EMAIL>"}, "version": "0.0.1"}, "host": "ccr.baidubce.com", "basePath": "/v1", "paths": {"/accelerators/policies/filters": {"post": {"description": "测试加速器策略规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["accelerator"], "summary": "测试加速器策略规则", "parameters": [{"description": "测试加速器规则", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.TestPolicyFilter"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.TestPolicyFilterResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/features": {"get": {"description": "查询用户是否在白名单内", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["feature"], "summary": "查询用户是否在白名单内", "parameters": [{"type": "string", "description": "功能类型", "name": "featureType", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "boolean"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances": {"get": {"description": "列举CCR实例", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["instance"], "summary": "列举CCR实例", "parameters": [{"type": "integer", "description": "页码", "name": "pageNo", "in": "query"}, {"type": "integer", "description": "页大小", "name": "pageSize", "in": "query"}, {"type": "string", "description": "关键字类型", "name": "keywordType", "in": "query"}, {"type": "string", "description": "关键字", "name": "keyword", "in": "query"}, {"type": "string", "description": "是否在全区域内搜索", "name": "acrossregion", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/model.ListInstanceResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建CCR实例", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["instance"], "summary": "创建CCR实例", "parameters": [{"description": "创建实例参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.CreateInstanceRequest"}}], "responses": {"201": {"description": "Success", "schema": {"$ref": "#/definitions/model.CreateInstanceResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/renew": {"post": {"description": "续费CCR实例", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["instance"], "summary": "续费CCR实例", "parameters": [{"description": "续费实例参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.ConfirmOrderRequest"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.Result"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{id}/credential": {"put": {"description": "重置密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["credential"], "summary": "重置密码", "parameters": [{"type": "string", "description": "实例ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "用户id", "name": "userId", "in": "query"}, {"description": "新密码", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.ResetPasswordArgs"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "default": {"description": "", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建临时密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["credential"], "summary": "创建临时密码", "parameters": [{"type": "string", "description": "实例ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "用户id", "name": "userId", "in": "query"}, {"description": "新密码", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.TemporaryPasswordArgs"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.TemporaryPasswordResponse"}}, "default": {"description": "", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}": {"get": {"description": "获取CCR实例", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["instance"], "summary": "获取CCR实例", "parameters": [{"type": "string", "description": "实例id", "name": "instanceId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.InstanceDetail"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "更改CCR实例信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["instance"], "summary": "更改CCR实例信息", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "更新请求", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.UpdateInstanceRequest"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.InstanceInfo"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除CCR实例", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["instance"], "summary": "删除CCR实例", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}], "responses": {"204": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/accelerators/policies": {"get": {"description": "获取加速器策略列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["accelerator"], "summary": "获取加速器策略列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "加速器策略名称", "name": "policyName", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListAcceleratorPolicyResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建加速器策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["accelerator"], "summary": "创建加速器策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "加速器参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.AcceleratorPolicyRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "批量删除加速器策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["accelerator"], "summary": "批量删除加速器策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "加速器策略ID数组", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.BatchDeleteInt64Request"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/accelerators/policies/{policyId}": {"get": {"description": "查询加速器策略详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["accelerator"], "summary": "查询加速器策略详情", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "加速器策略ID", "name": "policyId", "in": "path", "required": true}], "responses": {"200": {"description": "accelerator policy", "schema": {"$ref": "#/definitions/model.AcceleratorPolicy"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "修改加速器策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["accelerator"], "summary": "修改加速器策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "加速器策略ID", "name": "policyId", "in": "path", "required": true}, {"description": "加速器参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.AcceleratorPolicyRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除加速器策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["accelerator"], "summary": "删除加速器策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "加速器策略ID", "name": "policyId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/accelerators/policies/{policyId}/enable": {"put": {"description": "启动或关闭加速器策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["accelerator"], "summary": "启动或关闭加速器策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "加速器策略ID", "name": "policyId", "in": "path", "required": true}, {"type": "string", "description": "是否开启", "name": "enabled", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/domain": {"get": {"description": "获取自定义域名列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["domain"], "summary": "获取自定义域名列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListDomainResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建自定义域名", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["domain"], "summary": "创建自定义域名", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "自定义域名创建参数", "name": "CustomDomainRequest", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.DomainRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/domain/{domainName}": {"get": {"description": "查询自定义域名详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["domain"], "summary": "查询自定义域名详情", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "域名", "name": "domainName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.DomainResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "更新自定义域名", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["domain"], "summary": "更新自定义域名", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "域名", "name": "domainName", "in": "path", "required": true}, {"description": "自定义域名创建参数", "name": "CustomDomainRequest", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.DomainRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除自定义域名", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["domain"], "summary": "删除自定义域名", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "域名", "name": "domainName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/domain/{domainName}/check": {"get": {"description": "检查域名备案信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["domain"], "summary": "检查域名备案信息", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "域名", "name": "domainName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.CheckDomainICPResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/executions": {"get": {"description": "查询镜像迁移或者实例同步执行记录列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["replication", "sync"], "summary": "查询镜像迁移或者实例同步执行记录列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "策略ID", "name": "policyId", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListExecutionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "执行镜像迁移或者实例同步", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["replication", "sync"], "summary": "执行镜像迁移或者实例同步", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "start execution body", "name": "execution", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.StartExecutionRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/executions/{executionId}": {"get": {"description": "查询镜像迁移或者实例同步执行记录详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["replication", "sync"], "summary": "查询镜像迁移或者实例同步执行记录详情", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "镜像迁移或者实例同步执行记录ID", "name": "executionId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ExecutionResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "停止镜像迁移或者实例同步", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["replication", "sync"], "summary": "停止镜像迁移或者实例同步", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "执行ID", "name": "executionId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/executions/{executionId}/tasks": {"get": {"description": "查询镜像迁移或者实例同步执行执行任务记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["replication", "sync"], "summary": "查询镜像迁移或者实例同步执行执行任务记录", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "迁移任务ID", "name": "executionId", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListTaskResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/executions/{executionId}/tasks/{taskId}/log": {"get": {"description": "查询镜像迁移或者实例同步执行任务日志", "consumes": ["text/plain"], "produces": ["text/plain"], "tags": ["replication", "sync"], "summary": "查询镜像迁移或者实例同步执行任务日志", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "执行ID", "name": "executionId", "in": "path", "required": true}, {"type": "string", "description": "任务ID", "name": "taskId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/gcs": {"get": {"description": "查询垃圾回收调度任务历史记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["gc"], "summary": "查询垃圾回收调度任务历史记录", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/model.ListGCHistoryResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "执行垃圾回收调度任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["gc"], "summary": "执行垃圾回收调度任务", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "create gc schedule body", "name": "schedule", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.CreateGCRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/gcs/{gcId}/log": {"get": {"description": "查询垃圾回收调度任务日志", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["gc"], "summary": "查询垃圾回收调度任务日志", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "任务ID", "name": "gcId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/immutable": {"get": {"description": "获取版本不可变规则列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["immutable"], "summary": "获取版本不可变规则列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListImmutableRuleResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建版本不可变规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["immutable"], "summary": "创建版本不可变规则", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "版本不可变规则参数", "name": "immutableRule", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.ImmutableRuleRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "批量删除版本不可变规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["immutable"], "summary": "批量删除版本不可变规则", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "版本不可变规则ID数组", "name": "immutableIds", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.BatchDeleteInt64Request"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/immutable/project": {"get": {"description": "查询命名空间列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["immutable"], "summary": "查询命名空间列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}], "responses": {"200": {"description": "get immutable project list success", "schema": {"type": "array", "items": {"$ref": "#/definitions/model.ImmutableRuleProjectResult"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/immutable/{immutableId}": {"get": {"description": "查询版本不可变策略详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["immutable"], "summary": "查询版本不可变策略详情", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "版本不可变规则ID", "name": "immutableId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ImmutableRuleResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "更新版本不可变规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["immutable"], "summary": "更新版本不可变规则", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "版本不可变规则ID", "name": "immutableId", "in": "path", "required": true}, {"description": "版本不可变规则参数", "name": "immutableRule", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.ImmutableRuleRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除版本不可变规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["immutable"], "summary": "删除版本不可变规则", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "版本不可变规则ID", "name": "immutableId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/immutable/{immutableId}/enable": {"put": {"description": "启用或禁用版本不可变规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["immutable"], "summary": "启用或禁用版本不可变规则", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "版本不可变规则ID", "name": "immutableId", "in": "path", "required": true}, {"type": "string", "description": "是否启用(true or false)", "name": "enabled", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/privatelinks": {"get": {"description": "获取专有网络列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["network"], "summary": "获取专有网络列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.PrivateNetworks"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建一个专有网络", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["network"], "summary": "创建一个专有网络", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "创建参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.CreatePrivateLinkArgs"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "释放一个私有网络", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["network"], "summary": "释放一个私有网络", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "白名单信息", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.DeletePrivateLinkArgs"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects": {"get": {"description": "获取当前用户的命名空间列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["project"], "summary": "获取当前用户的命名空间列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query"}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query"}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListProjectResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建命名空间", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["project"], "summary": "创建命名空间", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "create project body", "name": "project", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.CreateProjectRequest"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ProjectResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "批量删除命名空间", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["project"], "summary": "批量删除命名空间", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "命名空间名称数组", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.BatchDeleteRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}": {"get": {"description": "通过命名空间名称projectName查询命名空间", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["project"], "summary": "通过命名空间名称projectName查询命名空间", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ProjectResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "更新命名空间", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["project"], "summary": "更新命名空间", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"description": "update project request", "name": "project", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.UpdateProjectRequest"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ProjectResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除命名空间", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["project"], "summary": "删除命名空间", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/charts": {"get": {"description": "查询 helm chart 列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["chart"], "summary": "查询 helm chart 列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "helm chart名称", "name": "chartName", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListChartResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "上传helm chart 文件", "consumes": ["multipart/form-data"], "produces": ["multipart/form-data"], "tags": ["chart"], "summary": "上传helm chart 文件", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "file", "description": "The chart file", "name": "chart", "in": "formData", "required": true}, {"type": "file", "description": "The prov file", "name": "prov", "in": "formData"}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "批量删除helm chart", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["chart"], "summary": "批量删除helm chart", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"description": "helm chart名称数组", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.BatchDeleteRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/charts/download/{filename}": {"get": {"description": "下载 helm chart", "consumes": ["application/json", " text/plain", " */*"], "produces": ["application/x-tar"], "tags": ["chart"], "summary": "下载 helm chart", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "helm chart 文件名", "name": "filename", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/charts/{chartName}": {"delete": {"description": "删除 helm chart", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["chart"], "summary": "删除 helm chart", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "helm chart名称", "name": "chartName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"$ref": "#/definitions/model.ChartVersion"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions": {"get": {"description": "查询chart version 列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["chart"], "summary": "查询chart version 列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "helm chart名称", "name": "chartName", "in": "path", "required": true}, {"type": "string", "description": "helm chart版本", "name": "chartVersion", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListChartVersionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "批量删除helm chart版本", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["chart"], "summary": "批量删除helm chart版本", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "helm chart 名称", "name": "chartName", "in": "path", "required": true}, {"description": "helm chart版本名称数组", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.BatchDeleteRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions/{chartVersion}": {"delete": {"description": "删除helm chart版本", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["chart"], "summary": "删除helm chart版本", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "helm chart名称", "name": "chartName", "in": "path", "required": true}, {"type": "string", "description": "helm chart版本", "name": "chartVersion", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories": {"get": {"description": "查询镜像仓库列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "查询镜像仓库列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query"}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query"}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListRepositoryResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "批量删除镜像仓库", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "批量删除镜像仓库", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"description": "镜像仓库名称数组", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.BatchDeleteRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}": {"get": {"description": "通过镜像仓库名称查询镜像仓库", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "通过镜像仓库名称查询镜像仓库", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.RepositoryResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "修改单个镜像仓库", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "修改单个镜像仓库", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"description": "update repository request", "name": "repository", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.UpdateRepositoryRequest"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.RepositoryResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除单个镜像仓库", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "删除单个镜像仓库", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds": {"get": {"description": "获取镜像构建任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "获取镜像构建任务", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query"}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query"}, {"type": "string", "description": "关键字类型", "name": "keywordType", "in": "query"}, {"type": "string", "description": "关键字", "name": "keyword", "in": "query"}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListBuildRepositoryTaskResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建镜像构建任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "创建镜像构建任务", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"description": "create build repository task body", "name": "imagebuild", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.BuildRepositoryTaskRequest"}}], "responses": {"201": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "批量删除镜像构建任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "批量删除镜像构建任务", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"description": "镜像构建任务ID数组", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.BatchDeleteRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds/{buildId}": {"get": {"description": "获取镜像构建任务详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "获取镜像构建任务详情", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "string", "description": "镜像构建任务ID", "name": "buildId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.BuildRepositoryTaskResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除镜像构建任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "删除镜像构建任务", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "string", "description": "镜像构建任务ID", "name": "buildId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds/{buildId}/log": {"get": {"description": "获取镜像构建任务日志", "consumes": ["application/json"], "produces": ["text/plain"], "tags": ["repository"], "summary": "获取镜像构建任务日志", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "string", "description": "镜像构建任务ID", "name": "buildId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags": {"get": {"description": "查询镜像仓库tag列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tag"], "summary": "查询镜像仓库tag列表", "parameters": [{"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "tag名称", "name": "tagName", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query"}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query"}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListTagResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "批量删除镜像tag", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tag"], "summary": "批量删除镜像tag", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"description": "tag名称数组", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.BatchDeleteRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}": {"get": {"description": "查询单个单个镜像tag", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tag"], "summary": "查询单个单个镜像tag", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "string", "description": "tag名称", "name": "tagName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.TagResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除单个镜像tag", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tag"], "summary": "删除单个镜像tag", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "string", "description": "tag名称", "name": "tagName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/buildhistory": {"get": {"description": "查询构建历史", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tag"], "summary": "查询构建历史", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "string", "description": "tag名称", "name": "tagName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.BuildHistoryResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scan": {"post": {"description": "镜像扫描", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tag"], "summary": "镜像扫描", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "string", "description": "tag名称", "name": "tagName", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scan/{reportId}/log": {"get": {"description": "镜像扫描日志", "consumes": ["text/plain"], "produces": ["text/plain"], "tags": ["tag"], "summary": "镜像扫描日志", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "string", "description": "tag名称", "name": "tagName", "in": "path", "required": true}, {"type": "string", "description": "扫描日志ID", "name": "reportId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scanoverview": {"get": {"description": "查询镜像漏洞", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tag"], "summary": "查询镜像漏洞", "parameters": [{"type": "string", "description": "命名空间名称", "name": "projectName", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "path", "required": true}, {"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "tag名称", "name": "tagName", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ScanOverviewResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/publiclinks": {"get": {"description": "获取公网信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["network"], "summary": "获取公网信息", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.PublicNetworkInfo"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "更新公有网络", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["network"], "summary": "更新公有网络", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "更新参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.PublicLinkAction"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/publiclinks/whitelist": {"post": {"description": "添加白名单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["network"], "summary": "添加白名单", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "白名单信息", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.PublicNetworkInfoWhitelist"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除白名单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["network"], "summary": "删除白名单", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "白名单信息", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.DeleteWhiteListArgs"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/registries": {"get": {"description": "获取远程仓库列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["registry"], "summary": "获取远程仓库列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "远程仓库名称", "name": "registryName", "in": "query"}, {"type": "string", "description": "远程仓库类型", "name": "registryType", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListRegistryResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建新的远程仓库", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["registry"], "summary": "创建新的远程仓库", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "create registry body", "name": "registry", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.RegistryRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/registries/ping": {"post": {"description": "检查远程仓库健康状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["registry"], "summary": "检查远程仓库健康状态", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "check registry health request", "name": "registryHealth", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.RegistryRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/registries/{registryId}": {"get": {"description": "通过registryId查询远程仓库", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["registry"], "summary": "通过registryId查询远程仓库", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "镜像远程仓库ID", "name": "registryId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.RegistryResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "修改单个远程仓库", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["registry"], "summary": "修改单个远程仓库", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "远程仓库ID", "name": "registryId", "in": "path", "required": true}, {"description": "update registry request", "name": "registry", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.RegistryRequest"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.RegistryResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除远程仓库", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["registry"], "summary": "删除远程仓库", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "远程仓库ID", "name": "registryId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/replications": {"get": {"description": "获取策略列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["replication"], "summary": "获取策略列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "镜像迁移策略名称", "name": "policyName", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListReplicationResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建镜像迁移策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["replication"], "summary": "创建镜像迁移策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "create policy body", "name": "policy", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.PolicyRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/replications/{policyId}": {"get": {"description": "通过policyId查询策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["replication"], "summary": "通过policyId查询策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "镜像迁移策略名称ID", "name": "policyId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.PolicyResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "更新镜像迁移策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["replication"], "summary": "更新镜像迁移策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "策略ID", "name": "policyId", "in": "path", "required": true}, {"description": "update policy body", "name": "policy", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.PolicyRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除镜像迁移策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["replication"], "summary": "删除镜像迁移策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "策略ID", "name": "policyId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/repositories": {"get": {"description": "查询所有的镜像仓库列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "查询所有的镜像仓库列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repositoryName", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query"}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query"}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListRepositoryResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/retentions": {"post": {"description": "创建命名空间", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["retention"], "summary": "创建命名空间", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "create retention body", "name": "retention", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.RetentionPolicy"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/syncs": {"get": {"description": "获取实例同步策略列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sync"], "summary": "获取实例同步策略列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "实例同步策略名称", "name": "policyName", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListSyncResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建实例同步策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sync"], "summary": "创建实例同步策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "用户id", "name": "userId", "in": "query"}, {"description": "create sync body", "name": "Sync", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.SyncRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/syncs/{policyId}": {"get": {"description": "查询实例同步策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sync"], "summary": "查询实例同步策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "实例同步策略名称ID", "name": "policyId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.SyncPolicyResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "更新实例同步策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sync"], "summary": "更新实例同步策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "实例同步策略名称ID", "name": "policyId", "in": "path", "required": true}, {"type": "string", "description": "用户id", "name": "userId", "in": "query"}, {"description": "update sync body", "name": "Sync", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.SyncRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除实例同步策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["sync"], "summary": "删除实例同步策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "策略ID", "name": "policyId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/tags": {"put": {"description": "更改CCR实例tag", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["instance"], "summary": "更改CCR实例tag", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "更新tag请求", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.AssignTagsRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/triggers/policies": {"get": {"description": "获取触发器策略列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["trigger"], "summary": "获取触发器策略列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "触发器策略名称", "name": "policyName", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListTriggerPolicyResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "post": {"description": "创建触发器策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["trigger"], "summary": "创建触发器策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "触发器参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.TriggerPolicyRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "批量删除触发器策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["trigger"], "summary": "批量删除触发器策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "触发器策略ID数组", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.BatchDeleteInt64Request"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/triggers/policies/targets": {"post": {"description": "测试触发器策略目标地址", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["trigger"], "summary": "测试触发器策略目标地址", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "触发器参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.TriggerTarget"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/triggers/policies/{policyId}": {"get": {"description": "查询触发器策略详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["trigger"], "summary": "查询触发器策略详情", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "触发器策略ID", "name": "policyId", "in": "path", "required": true}], "responses": {"200": {"description": "trigger policy", "schema": {"$ref": "#/definitions/model.TriggerPolicy"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "put": {"description": "修改触发器策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["trigger"], "summary": "修改触发器策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "触发器策略ID", "name": "policyId", "in": "path", "required": true}, {"description": "触发器参数", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.TriggerPolicyRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}, "delete": {"description": "删除触发器策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["trigger"], "summary": "删除触发器策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "触发器策略ID", "name": "policyId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/triggers/policies/{policyId}/enable": {"put": {"description": "启动或关闭触发器策略", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["trigger"], "summary": "启动或关闭触发器策略", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "触发器策略ID", "name": "policyId", "in": "path", "required": true}, {"type": "string", "description": "是否开启", "name": "enabled", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/triggers/policies/{policyId}/jobs": {"get": {"description": "获取触发器任务列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["trigger"], "summary": "获取触发器任务列表", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "触发器策略ID", "name": "policyId", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "当前页", "name": "pageNo", "in": "query", "required": true}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "pageSize", "in": "query", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListTriggerJobResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/triggers/policies/{policyId}/jobs/{jobId}/retry": {"put": {"description": "重新执行触发器任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["trigger"], "summary": "重新执行触发器任务", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"type": "string", "description": "触发器策略ID", "name": "policyId", "in": "path", "required": true}, {"type": "string", "description": "触发器任务ID", "name": "jobId", "in": "path", "required": true}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/instances/{instanceId}/upgrade": {"put": {"description": "升级CCR实例", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["instance"], "summary": "升级CCR实例", "parameters": [{"type": "string", "description": "实例ID", "name": "instanceId", "in": "path", "required": true}, {"description": "升级配置", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.UpgradeInstanceRequest"}}], "responses": {"200": {"description": "Success", "schema": {"type": "string"}}, "501": {"description": "Not Implemented", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/registrytypes": {"get": {"description": "获取远程仓库类型列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["registry"], "summary": "获取远程仓库类型列表", "responses": {"200": {"description": "Success", "schema": {"type": "object", "additionalProperties": {"$ref": "#/definitions/model.EndpointPattern"}}}}}}, "/users/cert": {"get": {"description": "获取用户证书列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "获取用户证书列表", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.ListCertResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}, "/users/profile": {"get": {"description": "获取用户详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "获取用户详情", "parameters": [{"type": "string", "description": "用户id", "name": "userId", "in": "query"}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/model.UserProfile"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/bce.BceServiceError"}}}}}}, "definitions": {"bce.BceServiceError": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "statusCode": {"type": "integer"}}}, "model.AcceleratorFilter": {"type": "object", "required": ["type"], "properties": {"type": {"description": "The accelerator policy filter type.", "type": "string", "enum": ["project_name", "repository_name", "tag_name"]}, "value": {"description": "The value of accelerator policy filter.", "type": "string"}}}, "model.AcceleratorPolicy": {"type": "object", "properties": {"creationTime": {"description": "creation time", "type": "string"}, "description": {"description": "description", "type": "string"}, "enabled": {"description": "enabled", "type": "boolean"}, "filters": {"description": "filters", "type": "array", "items": {"$ref": "#/definitions/model.AcceleratorFilter"}}, "id": {"description": "id", "type": "integer"}, "name": {"description": "name", "type": "string"}, "updateTime": {"description": "update time", "type": "string"}}}, "model.AcceleratorPolicyRequest": {"type": "object", "required": ["name"], "properties": {"description": {"description": "The description of the policy.", "type": "string", "maxLength": 300}, "filters": {"description": "The policy filters", "type": "array", "items": {"$ref": "#/definitions/model.AcceleratorFilter"}}, "name": {"description": "The policy name.", "type": "string", "maxLength": 65, "minLength": 2}}}, "model.ArtifactTrashRecord": {"type": "object", "properties": {"repositoryName": {"type": "string"}}}, "model.AssignTagsRequest": {"type": "object", "properties": {"tags": {"type": "array", "items": {"$ref": "#/definitions/model.Tag"}}}}, "model.BatchDeleteInt64Request": {"type": "object", "required": ["items"], "properties": {"items": {"type": "array", "items": {"type": "integer"}}}}, "model.BatchDeleteRequest": {"type": "object", "required": ["items"], "properties": {"items": {"type": "array", "items": {"type": "string"}}}}, "model.Billing": {"type": "object", "required": ["autoRenewTime", "reservationTime", "reservationTimeUnit"], "properties": {"autoRenew": {"type": "boolean"}, "autoRenewTime": {"type": "integer", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9]}, "autoRenewTimeUnit": {"type": "string", "enum": ["MONTH", "YEAR"]}, "reservationTime": {"type": "integer", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9]}, "reservationTimeUnit": {"type": "string", "enum": ["MONTH", "YEAR"]}}}, "model.BuildHistoryResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.BuildHistoryResult"}}}}, "model.BuildHistoryResult": {"type": "object", "properties": {"comment": {"type": "string"}, "created": {"type": "string"}, "createdBy": {"type": "string"}, "emptyLayer": {"type": "boolean"}}}, "model.BuildRepositoryTaskRequest": {"type": "object", "required": ["fromType"], "properties": {"dockerfile": {"type": "string"}, "fromType": {"type": "string"}, "imageBuildConfig": {"$ref": "#/definitions/model.ImageBuildConfigBasedImage"}, "isLatest": {"type": "boolean"}, "tagName": {"type": "string"}}}, "model.BuildRepositoryTaskResult": {"type": "object", "properties": {"createAt": {"type": "string"}, "createBy": {"type": "string"}, "dockerfile": {"type": "string"}, "fromType": {"type": "string"}, "id": {"type": "string"}, "image": {"type": "string"}, "imageBuildConfig": {"$ref": "#/definitions/model.ImageBuildConfigBasedImage"}, "isLatest": {"type": "boolean"}, "status": {"type": "string"}, "tagName": {"type": "string"}}}, "model.Cert": {"type": "object", "properties": {"certExpireTime": {"type": "string"}, "certId": {"type": "string"}, "certName": {"type": "string"}}}, "model.ChartInfoResult": {"type": "object", "properties": {"created": {"description": "The created time of chart\nRequired: true", "type": "string"}, "deprecated": {"description": "Flag to indicate if the chart is deprecated", "type": "boolean"}, "home": {"description": "The home website of chart", "type": "string"}, "icon": {"description": "The icon path of chart", "type": "string"}, "latestVersion": {"description": "latest version of chart", "type": "string"}, "name": {"description": "Name of chart\nRequired: true", "type": "string"}, "totalVersions": {"description": "Total count of chart versions\nRequired: true", "type": "integer"}, "updated": {"description": "The created time of chart", "type": "string"}}}, "model.ChartVersion": {"type": "object", "properties": {"apiVersion": {"description": "The API version of this chart\nRequired: true", "type": "string"}, "appVersion": {"description": "The version of the application enclosed in the chart\nRequired: true", "type": "string"}, "created": {"description": "The created time of the chart entry", "type": "string"}, "deprecated": {"description": "Whether or not this chart is deprecated", "type": "boolean"}, "description": {"description": "A one-sentence description of chart", "type": "string"}, "digest": {"description": "The digest value of the chart entry", "type": "string"}, "engine": {"description": "The name of template engine\nRequired: true", "type": "string"}, "home": {"description": "The URL to the relevant project page", "type": "string"}, "icon": {"description": "The URL to an icon file\nRequired: true", "type": "string"}, "keywords": {"description": "A list of string keywords", "type": "array", "items": {"type": "string"}}, "labels": {"description": "labels", "type": "array", "items": {"$ref": "#/definitions/model.Label"}}, "name": {"description": "The name of the chart\nRequired: true", "type": "string"}, "removed": {"description": "A flag to indicate if the chart entry is removed", "type": "boolean"}, "sources": {"description": "The URL to the source code of chart", "type": "array", "items": {"type": "string"}}, "urls": {"description": "The urls of the chart entry", "type": "array", "items": {"type": "string"}}, "version": {"description": "A SemVer 2 version of chart\nRequired: true", "type": "string"}}}, "model.ChartVersionResult": {"type": "object", "properties": {"apiVersion": {"description": "The API version of this chart\nRequired: true", "type": "string"}, "appVersion": {"description": "The version of the application enclosed in the chart\nRequired: true", "type": "string"}, "created": {"description": "The created time of the chart entry", "type": "string"}, "deprecated": {"description": "Whether or not this chart is deprecated", "type": "boolean"}, "description": {"description": "A one-sentence description of chart", "type": "string"}, "digest": {"description": "The digest value of the chart entry", "type": "string"}, "engine": {"description": "The name of template engine\nRequired: true", "type": "string"}, "home": {"description": "The URL to the relevant project page", "type": "string"}, "icon": {"description": "The URL to an icon file\nRequired: true", "type": "string"}, "maintainers": {"description": "maintainers", "type": "array", "items": {"type": "string"}}, "name": {"description": "The name of the chart\nRequired: true", "type": "string"}, "removed": {"description": "A flag to indicate if the chart entry is removed", "type": "boolean"}, "sources": {"description": "The URL to the source code of chart", "type": "array", "items": {"type": "string"}}, "urls": {"description": "The urls of the chart entry", "type": "array", "items": {"type": "string"}}, "version": {"description": "A SemVer 2 version of chart\nRequired: true", "type": "string"}}}, "model.CheckDomainICPResponse": {"type": "object", "properties": {"domainICPed": {"type": "boolean"}, "domainName": {"type": "string"}}}, "model.Config": {"type": "object", "properties": {"duration": {"description": "续费时长", "type": "integer"}, "instanceId": {"description": "实例Id", "type": "string"}, "serviceType": {"description": "产品名称", "type": "string"}, "timeUnit": {"description": "续费时长单位 'year' | 'month' | 'day'; 不传默认取`month`", "type": "string"}, "unionExpireOrderFlag": {"description": "是否为统一到期日订单，默认（不是）不传，是传 1", "type": "string"}, "uuid": {"description": "UUID", "type": "string"}}}, "model.ConfirmOrderRequest": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.Item"}}, "paymentMethod": {"description": "支付信息", "type": "array", "items": {"$ref": "#/definitions/model.PaymentMethod"}}}}, "model.CreateGCRequest": {"type": "object", "required": ["type"], "properties": {"deleteUntagged": {"description": "The DeleteUntagged is used to delete the untagged images.", "type": "boolean"}, "dryRun": {"description": "The DryRun is used to dry run the schedule.", "type": "boolean"}, "type": {"description": "The schedule type. The valid values are 'Manual'.\n'Manual' means to trigger it right away.\n\nEnum: Manual", "type": "string", "enum": ["Manual"]}}}, "model.CreateInstanceRequest": {"type": "object", "required": ["name", "paymentTiming", "type"], "properties": {"billing": {"$ref": "#/definitions/model.Billing"}, "bucket": {"type": "string"}, "groupIds": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string", "maxLength": 256, "minLength": 1}, "paymentMethod": {"type": "array", "items": {"$ref": "#/definitions/model.PaymentMethod"}}, "paymentTiming": {"type": "string", "enum": ["prepay"]}, "tags": {"type": "array", "items": {"$ref": "#/definitions/model.Tag"}}, "type": {"type": "string", "enum": ["BASIC", "STANDARD", "ADVANCED"]}}}, "model.CreateInstanceResponse": {"type": "object", "properties": {"instanceID": {"type": "string"}, "orderID": {"type": "string"}}}, "model.CreatePrivateLinkArgs": {"type": "object", "required": ["subnetID", "vpcID"], "properties": {"autoDnsResolve": {"type": "boolean"}, "ipAddress": {"type": "string"}, "ipType": {"type": "string"}, "subnetID": {"type": "string"}, "vpcID": {"type": "string"}}}, "model.CreateProjectRequest": {"type": "object", "required": ["projectName", "public"], "properties": {"projectName": {"description": "命名空间名称.", "type": "string"}, "public": {"description": "The public status of the project. The valid values are \"true\", \"false\".", "type": "string"}}}, "model.DeletePrivateLinkArgs": {"type": "object", "properties": {"subnetID": {"type": "string"}, "vpcID": {"type": "string"}}}, "model.DeleteWhiteListArgs": {"type": "object", "required": ["items"], "properties": {"items": {"type": "array", "items": {"type": "string"}}}}, "model.DomainRequest": {"type": "object", "properties": {"certId": {"type": "string"}, "certName": {"type": "string"}, "domainName": {"type": "string"}}}, "model.DomainResult": {"type": "object", "properties": {"certExpireTime": {"type": "string"}, "certId": {"type": "string"}, "certName": {"type": "string"}, "domainICPed": {"description": "domainICPed 域名是否备案,默认false", "type": "boolean"}, "domainName": {"type": "string"}, "domainStatus": {"description": "DomainStatus 域名状态(creating:生效中,created:正常,failed:异常)", "type": "string"}, "domainStatusDesc": {"type": "string"}}}, "model.Endpoint": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}}}, "model.EndpointPattern": {"type": "object", "properties": {"endpointType": {"type": "string"}, "endpoints": {"type": "array", "items": {"$ref": "#/definitions/model.Endpoint"}}}}, "model.ExecutionResult": {"type": "object", "properties": {"endTime": {"description": "The end time\nFormat: date-time", "type": "string"}, "failed": {"description": "The count of failed executions", "type": "integer"}, "id": {"description": "The ID of the execution", "type": "integer"}, "inProgress": {"description": "The count of in_progress executions", "type": "integer"}, "policyId": {"description": "The ID if the policy that the execution belongs to", "type": "integer"}, "startTime": {"description": "The start time\nFormat: date-time", "type": "string"}, "status": {"description": "The status of the execution", "type": "string"}, "statusText": {"description": "The status text", "type": "string"}, "stopped": {"description": "The count of stopped executions", "type": "integer"}, "succeed": {"description": "The count of succeed executions", "type": "integer"}, "total": {"description": "The total count of all executions", "type": "integer"}, "trigger": {"description": "The trigger mode", "type": "string"}}}, "model.Filter": {"type": "object", "properties": {"type": {"description": "The replication policy filter type.", "type": "string"}, "value": {"description": "The value of replication policy filter.", "type": "string"}}}, "model.GCHistoryResult": {"type": "object", "properties": {"Status": {"description": "the status of gc job.\nEnums [Pending Running Stopped Error Success Scheduled]", "type": "string"}, "artifactTrashRecords": {"description": "the artifact trash record of gc job.", "type": "array", "items": {"$ref": "#/definitions/model.ArtifactTrashRecord"}}, "deleted": {"description": "if gc job was deleted.", "type": "boolean"}, "freedSpace": {"description": "the freed space of gc job.", "type": "string"}, "id": {"description": "the id of gc job.", "type": "integer"}, "kind": {"description": "the job kind of gc job.", "type": "string"}, "name": {"description": "the job name of gc job.", "type": "string"}, "parameters": {"description": "the job parameters of gc job.", "$ref": "#/definitions/model.JobParameters"}, "result": {"description": "the result of gc job.\nEnums [Error Success]", "type": "string"}, "schedule": {"description": "schedule", "$ref": "#/definitions/model.ScheduleObj"}, "startTime": {"description": "the start time of gc job.\nFormat: date-time", "type": "string"}, "updateTime": {"description": "the finish time of gc job.\nFormat: date-time", "type": "string"}}}, "model.ImageBuildConfigBasedImage": {"type": "object", "properties": {"dependencies": {"type": "array", "items": {"$ref": "#/definitions/model.ImageBuildConfigPackage"}}, "image": {"type": "string"}, "tag": {"type": "string"}}}, "model.ImageBuildConfigPackage": {"type": "object", "properties": {"opCode": {"type": "string"}, "package": {"type": "string"}}}, "model.ImmutableRuleProjectResult": {"type": "object", "properties": {"projectID": {"description": "projectID，命名空间ID", "type": "integer"}, "projectName": {"description": "projectName，命名空间名称", "type": "string"}}}, "model.ImmutableRuleRequest": {"type": "object", "properties": {"action": {"description": "action,默认取值：immutable", "type": "string"}, "disabled": {"description": "disabled：是否禁用,默认为false", "type": "boolean"}, "id": {"description": "id，规则ID", "type": "integer"}, "priority": {"description": "priority，优先级，默认0", "type": "integer"}, "projectID": {"description": "projectID，命名空间ID", "type": "integer"}, "scope_selectors": {"description": "scope_selectors：镜像仓库匹配规则", "type": "array", "items": {"$ref": "#/definitions/model.ImmutableSelector"}}, "tag_selectors": {"description": "tag_selectors：镜像版本匹配规则", "type": "array", "items": {"$ref": "#/definitions/model.ImmutableSelector"}}, "template": {"description": "template，默认取值：immutable_template", "type": "string"}}}, "model.ImmutableRuleResult": {"type": "object", "properties": {"action": {"description": "action,values is: immutable", "type": "string"}, "disabled": {"description": "disabled：是否禁用", "type": "boolean"}, "id": {"description": "id，规则ID", "type": "integer"}, "priority": {"description": "priority，优先级", "type": "integer"}, "projectID": {"description": "projectID，命名空间ID", "type": "integer"}, "projectName": {"description": "ProjectName，命名空间名称", "type": "string"}, "scope_selectors": {"description": "scope_selectors,镜像仓库匹配规则", "type": "array", "items": {"$ref": "#/definitions/model.ImmutableSelector"}}, "tag_selectors": {"description": "tag_selectors,版本匹配规则", "type": "array", "items": {"$ref": "#/definitions/model.ImmutableSelector"}}, "template": {"description": "template", "type": "string"}}}, "model.ImmutableSelector": {"type": "object", "properties": {"decoration": {"description": "decoration,scope_selectors value is: repoMatches、repoExcludes；tag_selectors value is: matches、excludes", "type": "string"}, "extras": {"description": "extras，扩展字段", "type": "string"}, "kind": {"description": "kind,默认值：doublestar", "type": "string"}, "pattern": {"description": "pattern,规则取值，正则表达式", "type": "string"}}}, "model.InstanceDetail": {"type": "object", "properties": {"bucket": {"type": "string"}, "info": {"$ref": "#/definitions/model.InstanceInfo"}, "quota": {"$ref": "#/definitions/model.UserQuota"}, "region": {"type": "string"}, "statistic": {"$ref": "#/definitions/model.InstanceStatistic"}}}, "model.InstanceInfo": {"type": "object", "properties": {"createTime": {"type": "string"}, "customDomains": {"type": "array", "items": {"type": "string"}}, "expireTime": {"type": "string"}, "groupIds": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string"}, "instanceType": {"type": "string"}, "name": {"type": "string"}, "p2pManagerURL": {"type": "string"}, "paymentTiming": {"type": "string"}, "privateURL": {"type": "string"}, "publicURL": {"type": "string"}, "region": {"type": "string"}, "status": {"type": "string"}, "tags": {"type": "array", "items": {"$ref": "#/definitions/model.Tag"}}}}, "model.InstanceStatistic": {"type": "object", "properties": {"chart": {"type": "integer"}, "namespace": {"type": "integer"}, "repo": {"type": "integer"}, "storage": {"type": "integer"}}}, "model.Item": {"type": "object", "properties": {"config": {"$ref": "#/definitions/model.Config"}, "paymentMethod": {"description": "支付信息", "type": "array", "items": {"$ref": "#/definitions/model.PaymentMethod"}}}}, "model.JobParameters": {"type": "object", "properties": {"deleteUntagged": {"description": "delete untagged artifacts", "type": "boolean"}, "dryTun": {"description": "dry run", "type": "boolean"}}}, "model.Label": {"type": "object", "properties": {"color": {"description": "The color of label.", "type": "string"}, "creation_time": {"description": "The creation time of label.", "type": "string"}, "deleted": {"description": "The label is deleted or not.", "type": "boolean"}, "description": {"description": "The description of label.", "type": "string"}, "id": {"description": "The ID of label.", "type": "integer"}, "name": {"description": "The name of label.", "type": "string"}, "project_id": {"description": "The project ID if the label is a project label.", "type": "integer"}, "scope": {"description": "The scope of label, g for global labels and p for project labels.", "type": "string"}, "update_time": {"description": "The update time of label.", "type": "string"}}}, "model.ListAcceleratorPolicyResponse": {"type": "object", "properties": {"pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "policies": {"type": "array", "items": {"$ref": "#/definitions/model.AcceleratorPolicy"}}, "total": {"type": "integer"}}}, "model.ListBuildRepositoryTaskResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.BuildRepositoryTaskResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListCertResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.Cert"}}}}, "model.ListChartResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.ChartInfoResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListChartVersionResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.ChartVersionResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListDomainResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.DomainResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListExecutionResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.ExecutionResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListGCHistoryResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.GCHistoryResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListImmutableRuleResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.ImmutableRuleResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListInstanceResponse": {"type": "object", "properties": {"instances": {"type": "array", "items": {"$ref": "#/definitions/model.InstanceInfo"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListProjectResponse": {"type": "object", "properties": {"pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "projects": {"type": "array", "items": {"$ref": "#/definitions/model.ProjectResult"}}, "total": {"type": "integer"}}}, "model.ListRegistryResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.RegistryResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListReplicationResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.PolicyResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListRepositoryResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.RepositoryResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListSyncResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.SyncPolicyResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListTagResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.TagResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListTaskResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.TaskResult"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListTriggerJobResponse": {"type": "object", "properties": {"jobs": {"type": "array", "items": {"$ref": "#/definitions/model.TriggerJob"}}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}, "model.ListTriggerPolicyResponse": {"type": "object", "properties": {"pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "policies": {"type": "array", "items": {"$ref": "#/definitions/model.TriggerPolicy"}}, "total": {"type": "integer"}}}, "model.PaymentMethod": {"type": "object", "properties": {"type": {"type": "string"}, "values": {"type": "array", "items": {"type": "string"}}}}, "model.PolicyRequest": {"type": "object", "properties": {"description": {"description": "The description of the policy.", "type": "string"}, "destProjectName": {"description": "The destination namespace.", "type": "string"}, "filters": {"description": "The replication policy filter array.\nExample filters: [{\"type\": \"name\",\"value\":\"garenwen/virt-launcher\"},{\"type\":\"tag\", \"value\":\"\"}]", "type": "array", "items": {"$ref": "#/definitions/model.Filter"}}, "name": {"description": "The policy name.", "type": "string"}, "override": {"description": "Whether to override the resources on the destination registry.", "type": "boolean"}, "srcRegistry": {"description": "The source registry.", "$ref": "#/definitions/model.SrcRegistry"}, "trigger": {"description": "trigger", "$ref": "#/definitions/model.TriggerReq"}}}, "model.PolicyResult": {"type": "object", "properties": {"creationTime": {"description": "The create time of the policy.", "type": "string"}, "deletion": {"description": "Whether to replicate the deletion operation.", "type": "boolean"}, "description": {"description": "The description of the policy.", "type": "string"}, "destProjectName": {"description": "The destination namespace.", "type": "string"}, "destRegistry": {"description": "The destination registry.", "$ref": "#/definitions/model.Registry"}, "enabled": {"description": "Whether the policy is enabled or not.", "type": "boolean"}, "executionTimes": {"description": "THe execution times of the policy", "type": "integer"}, "filters": {"description": "The replication policy filter array.", "type": "array", "items": {"$ref": "#/definitions/model.Filter"}}, "id": {"description": "The policy ID.", "type": "integer"}, "name": {"description": "The policy name.", "type": "string"}, "override": {"description": "Whether to override the resources on the destination registry.", "type": "boolean"}, "srcRegistry": {"description": "The source registry.", "$ref": "#/definitions/model.Registry"}, "trigger": {"description": "trigger", "$ref": "#/definitions/model.Trigger"}, "updateTime": {"description": "The update time of the policy.", "type": "string"}}}, "model.PrivateNetworks": {"type": "object", "properties": {"domain": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/definitions/model.PrivateNetworksItems"}}}}, "model.PrivateNetworksItems": {"type": "object", "properties": {"ipAddress": {"type": "string"}, "resourceSource": {"type": "string"}, "serviceNetID": {"type": "string"}, "status": {"type": "string"}, "subnetID": {"type": "string"}, "vpcID": {"type": "string"}}}, "model.ProjectResult": {"type": "object", "properties": {"autoScan": {"description": "Whether scan images automatically when pushing. The valid values are \"true\", \"false\".", "type": "string"}, "chartCount": {"description": "The total number of charts under this project.", "type": "integer"}, "creationTime": {"description": "The creation time of the project.\nFormat: date-time", "type": "string"}, "projectId": {"description": "Project ID", "type": "integer"}, "projectName": {"description": "The name of the project.", "type": "string"}, "public": {"description": "The public status of the project. The valid values are \"true\", \"false\".", "type": "string"}, "repoCount": {"description": "The number of the repositories under this project.", "type": "integer"}, "updateTime": {"description": "The update time of the project.\nFormat: date-time", "type": "string"}}}, "model.PublicLinkAction": {"type": "object", "required": ["action"], "properties": {"action": {"type": "string", "enum": ["open", "close"]}}}, "model.PublicNetworkInfo": {"type": "object", "properties": {"domain": {"type": "string"}, "status": {"type": "string"}, "whitelist": {"type": "array", "items": {"$ref": "#/definitions/model.PublicNetworkInfoWhitelist"}}}}, "model.PublicNetworkInfoWhitelist": {"type": "object", "required": ["ipAddr"], "properties": {"description": {"type": "string"}, "ipAddr": {"type": "string"}}}, "model.Registry": {"type": "object", "properties": {"creationTime": {"description": "The create time of the policy.", "type": "string"}, "credential": {"description": "credential", "$ref": "#/definitions/model.RegistryCredential"}, "description": {"description": "Description of the registry.", "type": "string"}, "id": {"description": "The registry ID.", "type": "integer"}, "insecure": {"description": "Whether or not the certificate will be verified when <PERSON> tries to access the server.", "type": "boolean"}, "name": {"description": "The registry name.", "type": "string"}, "region": {"description": "The region of the registry.", "type": "string"}, "status": {"description": "Health status of the registry.", "type": "string"}, "type": {"description": "Type of the registry, e.g. 'harbor'.", "type": "string"}, "updateTime": {"description": "The update time of the policy.", "type": "string"}, "url": {"description": "The registry URL string.", "type": "string"}}}, "model.RegistryCredential": {"type": "object", "properties": {"accessKey": {"description": "Access key, e.g. user name when credential type is 'basic'.", "type": "string"}, "accessSecret": {"description": "Access secret, e.g. password when credential type is 'basic'.", "type": "string"}, "type": {"description": "Credential type, such as 'basic', 'oauth'.", "type": "string"}}}, "model.RegistryRequest": {"type": "object", "required": ["type"], "properties": {"credential": {"description": "credential", "$ref": "#/definitions/model.RegistryCredential"}, "description": {"description": "Description of the registry.", "type": "string"}, "insecure": {"description": "Whether or not the certificate will be verified when <PERSON> tries to access the server.", "type": "boolean"}, "name": {"description": "The registry name.", "type": "string"}, "type": {"description": "Type of the registry, e.g. 'harbor'.", "type": "string", "enum": ["harbor", "docker-hub", "docker-registry", "baidu-ccr"]}, "url": {"description": "The registry URL string.", "type": "string"}}}, "model.RegistryResult": {"type": "object", "properties": {"creationTime": {"description": "creation time", "type": "string"}, "credential": {"description": "credential", "$ref": "#/definitions/model.RegistryCredential"}, "description": {"description": "description", "type": "string"}, "id": {"description": "id", "type": "integer"}, "insecure": {"description": "insecure", "type": "boolean"}, "name": {"description": "name", "type": "string"}, "status": {"description": "status", "type": "string"}, "type": {"description": "type", "type": "string"}, "updateTime": {"description": "update time", "type": "string"}, "url": {"description": "url", "type": "string"}}}, "model.RepositoryResult": {"type": "object", "properties": {"creationTime": {"description": "The creation time of the repository\nFormat: date-time", "type": "string"}, "description": {"description": "The description of the repository", "type": "string"}, "privateRepositoryPath": {"description": "The path of private repository", "type": "string"}, "projectName": {"description": "The project name the repository", "type": "string"}, "public": {"description": "project is public or not", "type": "string"}, "pullCount": {"description": "The count that the artifact inside the repository pulled", "type": "integer"}, "repositoryName": {"description": "The name of the repository", "type": "string"}, "repositoryPath": {"description": "The path of the repository", "type": "string"}, "tagCount": {"description": "The count of the tags inside the repository", "type": "integer"}, "updateTime": {"description": "The update time of the repository\nFormat: date-time", "type": "string"}}}, "model.ResetPasswordArgs": {"type": "object", "required": ["password"], "properties": {"password": {"type": "string"}}}, "model.Result": {"type": "object", "properties": {"orderId": {"type": "string"}}}, "model.RetentionPolicy": {"type": "object", "properties": {"algorithm": {"description": "algorithm", "type": "string"}, "id": {"description": "id", "type": "integer"}, "rules": {"description": "rules", "type": "array", "items": {"$ref": "#/definitions/model.RetentionRule"}}, "scope": {"description": "scope", "$ref": "#/definitions/model.RetentionPolicyScope"}, "trigger": {"description": "trigger", "$ref": "#/definitions/model.RetentionRuleTrigger"}}}, "model.RetentionPolicyScope": {"type": "object", "properties": {"level": {"description": "level", "type": "string"}, "ref": {"description": "ref", "type": "integer"}}}, "model.RetentionRule": {"type": "object", "properties": {"action": {"description": "action", "type": "string"}, "disabled": {"description": "disabled", "type": "boolean"}, "id": {"description": "id", "type": "integer"}, "params": {"description": "params", "type": "object", "additionalProperties": true}, "priority": {"description": "priority", "type": "integer"}, "scope_selectors": {"description": "scope selectors", "type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/model.RetentionSelector"}}}, "tag_selectors": {"description": "tag selectors", "type": "array", "items": {"$ref": "#/definitions/model.RetentionSelector"}}, "template": {"description": "template", "type": "string"}}}, "model.RetentionRuleTrigger": {"type": "object", "properties": {"kind": {"description": "kind", "type": "string"}, "references": {"description": "references"}, "settings": {"description": "settings"}}}, "model.RetentionSelector": {"type": "object", "properties": {"decoration": {"description": "decoration", "type": "string"}, "extras": {"description": "extras", "type": "string"}, "kind": {"description": "kind", "type": "string"}, "pattern": {"description": "pattern", "type": "string"}}}, "model.ScanOverview": {"type": "object", "properties": {"description": {"type": "string"}, "fixVersion": {"type": "string"}, "id": {"type": "string"}, "links": {"type": "array", "items": {"type": "string"}}, "package": {"type": "string"}, "severity": {"type": "string"}, "version": {"type": "string"}}}, "model.ScanOverviewResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/model.ScanOverview"}}, "lastScanTime": {"type": "string"}, "pageNo": {"type": "integer"}, "pageSize": {"type": "integer"}, "summary": {"type": "object", "additionalProperties": {"type": "integer"}}, "total": {"type": "integer"}}}, "model.ScheduleObj": {"type": "object", "properties": {"type": {"description": "The schedule type. The valid values are 'Hourly', 'Daily', 'Weekly', 'Custom', 'Manual' and 'None'.\n'Manual' means to trigger it right away and 'None' means to cancel the schedule.\n\nEnum: [Hourly Daily Weekly Custom Manual None]", "type": "string"}}}, "model.Settings": {"type": "object", "properties": {"cron": {"description": "The cron string for scheduled trigger", "type": "string"}}}, "model.SrcRegistry": {"type": "object", "properties": {"id": {"description": "The registry ID.", "type": "integer"}}}, "model.StartExecutionRequest": {"type": "object", "required": ["policyId"], "properties": {"policyId": {"description": "The ID of policy that the execution belongs to.", "type": "integer"}}}, "model.SyncPolicyResult": {"type": "object", "properties": {"creationTime": {"description": "The create time of the policy.", "type": "string"}, "description": {"description": "The description of the policy.", "type": "string"}, "destInstanceId": {"description": "The destination instance.", "type": "string"}, "destProjectName": {"description": "The destination namespace.", "type": "string"}, "destRegion": {"type": "string"}, "id": {"description": "The policy ID.", "type": "integer"}, "name": {"description": "The policy name.", "type": "string"}, "override": {"description": "Whether to override the resources on the destination registry.", "type": "boolean"}, "srcProjectName": {"description": "SrcProjectName src project name", "type": "string"}, "srcRegion": {"type": "string"}, "srcRepositoryName": {"description": "SrcRepository src repository", "type": "string"}, "srcTagName": {"description": "SrcTag scr tag", "type": "string"}, "syncType": {"description": "SyncType of the Sync, e.g. 'image'.", "type": "string"}, "trigger": {"description": "trigger", "$ref": "#/definitions/model.SyncTrigger"}, "updateTime": {"description": "The update time of the policy.", "type": "string"}}}, "model.SyncRequest": {"type": "object", "required": ["destInstanceId", "name", "srcProjectName"], "properties": {"description": {"description": "Description of the Sync.", "type": "string"}, "destInstanceId": {"description": "The destination instance.", "type": "string"}, "destProjectName": {"description": "DestProjectName dest project name", "type": "string"}, "destRegion": {"description": "The destination region.", "type": "string"}, "name": {"description": "The Sync name.", "type": "string"}, "override": {"description": "Whether to override the resources on the destination registry.", "type": "boolean"}, "srcProjectName": {"description": "SrcProjectName src project name", "type": "string"}, "srcRepository": {"description": "SrcRepository src repository", "type": "string"}, "srcTag": {"description": "SrcTag scr tag", "type": "string"}, "syncType": {"description": "SyncType of the Sync, e.g. 'image'.", "type": "string"}, "trigger": {"description": "trigger", "$ref": "#/definitions/model.Trigger"}}}, "model.SyncTrigger": {"type": "object", "properties": {"type": {"description": "The replication policy trigger type. The valid values are manual, event_based and scheduled.", "type": "string"}}}, "model.Tag": {"type": "object", "properties": {"tagKey": {"type": "string"}, "tagValue": {"type": "string"}}}, "model.TagResult": {"type": "object", "properties": {"acceleratorStatus": {"description": "The tag of repository accelerate status", "type": "string"}, "architecture": {"description": "Architecture The architecture of repository", "type": "string"}, "author": {"description": "Author", "type": "string"}, "digest": {"description": "The digest of the artifact", "type": "string"}, "immutabled": {"description": "The tag where immutabled", "type": "boolean"}, "os": {"description": "OS", "type": "string"}, "projectId": {"description": "The ID of the project that the artifact belongs to", "type": "integer"}, "pullTime": {"description": "The latest pull time of the tag\nFormat: date-time", "type": "string"}, "pushTime": {"description": "The push time of the tag\nFormat: date-time", "type": "string"}, "repositoryId": {"description": "The ID of the repository that the artifact belongs to", "type": "integer"}, "scanOverview": {"description": "The scan overview", "$ref": "#/definitions/model.TagScanOverview"}, "size": {"description": "The size of the artifact", "type": "integer"}, "tagName": {"description": "The name of the tag", "type": "string"}, "type": {"description": "The type of the artifact, e.g. image, chart, etc", "type": "string"}}}, "model.TagScanOverview": {"type": "object", "properties": {"endTime": {"description": "The end time of the scan process that generating report\nExample: 2006-01-02T15:04:05\nFormat: date-time", "type": "string"}, "fixable": {"description": "The number of the fixable vulnerabilities\nExample: 100", "type": "integer"}, "reportId": {"description": "id of the native scan report\nExample: 5f62c830-f996-11e9-957f-0242c0a89008", "type": "string"}, "scanStatus": {"description": "The status of the report generating process\nExample: Success", "type": "string"}, "severity": {"description": "漏洞等级 Critical 危及 High 严重  Medium 中等 Low 较低", "type": "string"}, "startTime": {"description": "The start time of the scan process that generating report\nExample: 2006-01-02T14:04:05\nFormat: date-time", "type": "string"}, "summary": {"description": "Numbers of the vulnerabilities with different severity\nExample: {\"Critical\":5,\"High\":5}", "type": "object", "additionalProperties": {"type": "integer"}}, "total": {"description": "The total number of the found vulnerabilities\nExample: 500", "type": "integer"}}}, "model.TaskResult": {"type": "object", "properties": {"destResource": {"description": "The destination resource that the task operates", "type": "string"}, "endTime": {"description": "The end time of the task\nFormat: date-time", "type": "string"}, "executionId": {"description": "The ID of the execution that the task belongs to", "type": "integer"}, "id": {"description": "The ID of the task", "type": "integer"}, "jobId": {"description": "The ID of the underlying job that the task related to", "type": "string"}, "operation": {"description": "The operation of the task", "type": "string"}, "resourceType": {"description": "The type of the resource that the task operates", "type": "string"}, "srcResource": {"description": "The source resource that the task operates", "type": "string"}, "startTime": {"description": "The start time of the task\nFormat: date-time", "type": "string"}, "status": {"description": "The status of the task", "type": "string"}}}, "model.TemporaryPasswordArgs": {"type": "object", "required": ["duration"], "properties": {"duration": {"type": "integer", "maximum": 24, "minimum": 1}}}, "model.TemporaryPasswordResponse": {"type": "object", "properties": {"password": {"type": "string"}}}, "model.TestPolicyFilter": {"type": "object", "properties": {"filters": {"description": "The policy filters", "type": "array", "items": {"$ref": "#/definitions/model.AcceleratorFilter"}}, "repository": {"type": "string"}}}, "model.TestPolicyFilterResponse": {"type": "object", "properties": {"matched": {"type": "boolean"}}}, "model.Trigger": {"type": "object", "properties": {"triggerSettings": {"description": "trigger settings", "$ref": "#/definitions/model.Settings"}, "type": {"description": "The replication policy trigger type. The valid values are manual, event_based and scheduled.", "type": "string"}}}, "model.TriggerFilter": {"type": "object", "required": ["type"], "properties": {"type": {"description": "The trigger policy filter type.", "type": "string", "enum": ["project_name", "repository_name", "tag_name"]}, "value": {"description": "The value of trigger policy filter.", "type": "string"}}}, "model.TriggerJob": {"type": "object", "properties": {"creationTime": {"description": "creation time", "type": "string"}, "eventType": {"description": "event type", "type": "string"}, "id": {"description": "id", "type": "integer"}, "image": {"description": "FIXME 后期会使用images 取代image,这里过渡期保留", "type": "string"}, "images": {"type": "array", "items": {"type": "string"}}, "notifyType": {"description": "notify type", "type": "string"}, "operator": {"description": "operator", "type": "string"}, "policyId": {"description": "policy id", "type": "integer"}, "status": {"description": "status", "type": "string"}, "updateTime": {"description": "update time", "type": "string"}}}, "model.TriggerPolicy": {"type": "object", "properties": {"creationTime": {"description": "creation time", "type": "string"}, "description": {"description": "description", "type": "string"}, "enabled": {"description": "enabled", "type": "boolean"}, "eventTypes": {"description": "event types", "type": "array", "items": {"type": "string"}}, "filters": {"description": "filters", "type": "array", "items": {"$ref": "#/definitions/model.TriggerFilter"}}, "id": {"description": "id", "type": "integer"}, "name": {"description": "name", "type": "string"}, "targets": {"description": "targets", "type": "array", "items": {"$ref": "#/definitions/model.TriggerTarget"}}, "updateTime": {"description": "update time", "type": "string"}}}, "model.TriggerPolicyRequest": {"type": "object", "required": ["eventTypes", "name"], "properties": {"description": {"description": "The description of the policy.", "type": "string", "maxLength": 300}, "eventTypes": {"description": "The event type one of PUSH_ARTIFACT UPLOAD_CHART PULL_ARTIFACT DOWNLOAD_CHART DELETE_ARTIFACT DELETE_CHART", "type": "array", "items": {"type": "string"}}, "filters": {"description": "The policy filters", "type": "array", "items": {"$ref": "#/definitions/model.TriggerFilter"}}, "name": {"description": "The policy name.", "type": "string", "maxLength": 65, "minLength": 2}, "targets": {"type": "array", "items": {"$ref": "#/definitions/model.TriggerTarget"}}}}, "model.TriggerReq": {"type": "object", "properties": {"type": {"description": "The replication policy trigger type. The valid values are manual, event_based and scheduled.\n镜像迁移只填写这个 manual\n实例同步可以写 manual event_base", "type": "string"}}}, "model.TriggerTarget": {"type": "object", "required": ["address"], "properties": {"address": {"type": "string", "maxLength": 1024}, "headers": {"description": "header key just support 'Authorization'", "type": "object", "additionalProperties": {"type": "string"}}}}, "model.UpdateInstanceRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "maxLength": 256, "minLength": 1}}}, "model.UpdateProjectRequest": {"type": "object", "required": ["autoScan", "public"], "properties": {"autoScan": {"description": "Whether scan images automatically when pushing. The valid values are \"true\", \"false\".", "type": "string"}, "public": {"description": "The public status of the project. The valid values are \"true\", \"false\".", "type": "string"}}}, "model.UpdateRepositoryRequest": {"type": "object", "properties": {"description": {"description": "The description of the repository", "type": "string"}}}, "model.UpgradeInstanceRequest": {"type": "object", "required": ["type"], "properties": {"paymentMethod": {"type": "array", "items": {"$ref": "#/definitions/model.PaymentMethod"}}, "type": {"type": "string", "enum": ["BASIC", "STANDARD", "ADVANCED"]}}}, "model.UserProfile": {"type": "object", "properties": {"name": {"type": "string"}}}, "model.UserQuota": {"type": "object", "properties": {"chart": {"type": "integer"}, "namespace": {"type": "integer"}, "repo": {"type": "integer"}}}}}