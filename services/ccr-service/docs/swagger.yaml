basePath: /v1
definitions:
  bce.BceServiceError:
    properties:
      code:
        type: string
      message:
        type: string
      requestId:
        type: string
      statusCode:
        type: integer
    type: object
  model.AcceleratorFilter:
    properties:
      type:
        description: The accelerator policy filter type.
        enum:
        - project_name
        - repository_name
        - tag_name
        type: string
      value:
        description: The value of accelerator policy filter.
        type: string
    required:
    - type
    type: object
  model.AcceleratorPolicy:
    properties:
      creationTime:
        description: creation time
        type: string
      description:
        description: description
        type: string
      enabled:
        description: enabled
        type: boolean
      filters:
        description: filters
        items:
          $ref: '#/definitions/model.AcceleratorFilter'
        type: array
      id:
        description: id
        type: integer
      name:
        description: name
        type: string
      updateTime:
        description: update time
        type: string
    type: object
  model.AcceleratorPolicyRequest:
    properties:
      description:
        description: The description of the policy.
        maxLength: 300
        type: string
      filters:
        description: The policy filters
        items:
          $ref: '#/definitions/model.AcceleratorFilter'
        type: array
      name:
        description: The policy name.
        maxLength: 65
        minLength: 2
        type: string
    required:
    - name
    type: object
  model.ArtifactTrashRecord:
    properties:
      repositoryName:
        type: string
    type: object
  model.AssignTagsRequest:
    properties:
      tags:
        items:
          $ref: '#/definitions/model.Tag'
        type: array
    type: object
  model.BatchDeleteInt64Request:
    properties:
      items:
        items:
          type: integer
        type: array
    required:
    - items
    type: object
  model.BatchDeleteRequest:
    properties:
      items:
        items:
          type: string
        type: array
    required:
    - items
    type: object
  model.Billing:
    properties:
      autoRenew:
        type: boolean
      autoRenewTime:
        enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        type: integer
      autoRenewTimeUnit:
        enum:
        - MONTH
        - YEAR
        type: string
      reservationTime:
        enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        type: integer
      reservationTimeUnit:
        enum:
        - MONTH
        - YEAR
        type: string
    required:
    - autoRenewTime
    - reservationTime
    - reservationTimeUnit
    type: object
  model.BuildHistoryResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.BuildHistoryResult'
        type: array
    type: object
  model.BuildHistoryResult:
    properties:
      comment:
        type: string
      created:
        type: string
      createdBy:
        type: string
      emptyLayer:
        type: boolean
    type: object
  model.BuildRepositoryTaskRequest:
    properties:
      dockerfile:
        type: string
      fromType:
        type: string
      imageBuildConfig:
        $ref: '#/definitions/model.ImageBuildConfigBasedImage'
      isLatest:
        type: boolean
      tagName:
        type: string
    required:
    - fromType
    type: object
  model.BuildRepositoryTaskResult:
    properties:
      createAt:
        type: string
      createBy:
        type: string
      dockerfile:
        type: string
      fromType:
        type: string
      id:
        type: string
      image:
        type: string
      imageBuildConfig:
        $ref: '#/definitions/model.ImageBuildConfigBasedImage'
      isLatest:
        type: boolean
      status:
        type: string
      tagName:
        type: string
    type: object
  model.Cert:
    properties:
      certExpireTime:
        type: string
      certId:
        type: string
      certName:
        type: string
    type: object
  model.ChartInfoResult:
    properties:
      created:
        description: |-
          The created time of chart
          Required: true
        type: string
      deprecated:
        description: Flag to indicate if the chart is deprecated
        type: boolean
      home:
        description: The home website of chart
        type: string
      icon:
        description: The icon path of chart
        type: string
      latestVersion:
        description: latest version of chart
        type: string
      name:
        description: |-
          Name of chart
          Required: true
        type: string
      totalVersions:
        description: |-
          Total count of chart versions
          Required: true
        type: integer
      updated:
        description: The created time of chart
        type: string
    type: object
  model.ChartVersion:
    properties:
      apiVersion:
        description: |-
          The API version of this chart
          Required: true
        type: string
      appVersion:
        description: |-
          The version of the application enclosed in the chart
          Required: true
        type: string
      created:
        description: The created time of the chart entry
        type: string
      deprecated:
        description: Whether or not this chart is deprecated
        type: boolean
      description:
        description: A one-sentence description of chart
        type: string
      digest:
        description: The digest value of the chart entry
        type: string
      engine:
        description: |-
          The name of template engine
          Required: true
        type: string
      home:
        description: The URL to the relevant project page
        type: string
      icon:
        description: |-
          The URL to an icon file
          Required: true
        type: string
      keywords:
        description: A list of string keywords
        items:
          type: string
        type: array
      labels:
        description: labels
        items:
          $ref: '#/definitions/model.Label'
        type: array
      name:
        description: |-
          The name of the chart
          Required: true
        type: string
      removed:
        description: A flag to indicate if the chart entry is removed
        type: boolean
      sources:
        description: The URL to the source code of chart
        items:
          type: string
        type: array
      urls:
        description: The urls of the chart entry
        items:
          type: string
        type: array
      version:
        description: |-
          A SemVer 2 version of chart
          Required: true
        type: string
    type: object
  model.ChartVersionResult:
    properties:
      apiVersion:
        description: |-
          The API version of this chart
          Required: true
        type: string
      appVersion:
        description: |-
          The version of the application enclosed in the chart
          Required: true
        type: string
      created:
        description: The created time of the chart entry
        type: string
      deprecated:
        description: Whether or not this chart is deprecated
        type: boolean
      description:
        description: A one-sentence description of chart
        type: string
      digest:
        description: The digest value of the chart entry
        type: string
      engine:
        description: |-
          The name of template engine
          Required: true
        type: string
      home:
        description: The URL to the relevant project page
        type: string
      icon:
        description: |-
          The URL to an icon file
          Required: true
        type: string
      maintainers:
        description: maintainers
        items:
          type: string
        type: array
      name:
        description: |-
          The name of the chart
          Required: true
        type: string
      removed:
        description: A flag to indicate if the chart entry is removed
        type: boolean
      sources:
        description: The URL to the source code of chart
        items:
          type: string
        type: array
      urls:
        description: The urls of the chart entry
        items:
          type: string
        type: array
      version:
        description: |-
          A SemVer 2 version of chart
          Required: true
        type: string
    type: object
  model.CheckDomainICPResponse:
    properties:
      domainICPed:
        type: boolean
      domainName:
        type: string
    type: object
  model.Config:
    properties:
      duration:
        description: 续费时长
        type: integer
      instanceId:
        description: 实例Id
        type: string
      serviceType:
        description: 产品名称
        type: string
      timeUnit:
        description: 续费时长单位 'year' | 'month' | 'day'; 不传默认取`month`
        type: string
      unionExpireOrderFlag:
        description: 是否为统一到期日订单，默认（不是）不传，是传 1
        type: string
      uuid:
        description: UUID
        type: string
    type: object
  model.ConfirmOrderRequest:
    properties:
      items:
        items:
          $ref: '#/definitions/model.Item'
        type: array
      paymentMethod:
        description: 支付信息
        items:
          $ref: '#/definitions/model.PaymentMethod'
        type: array
    type: object
  model.CreateGCRequest:
    properties:
      deleteUntagged:
        description: The DeleteUntagged is used to delete the untagged images.
        type: boolean
      dryRun:
        description: The DryRun is used to dry run the schedule.
        type: boolean
      type:
        description: |-
          The schedule type. The valid values are 'Manual'.
          'Manual' means to trigger it right away.

          Enum: Manual
        enum:
        - Manual
        type: string
    required:
    - type
    type: object
  model.CreateInstanceRequest:
    properties:
      billing:
        $ref: '#/definitions/model.Billing'
      bucket:
        type: string
      groupIds:
        items:
          type: string
        type: array
      name:
        maxLength: 256
        minLength: 1
        type: string
      paymentMethod:
        items:
          $ref: '#/definitions/model.PaymentMethod'
        type: array
      paymentTiming:
        enum:
        - prepay
        type: string
      tags:
        items:
          $ref: '#/definitions/model.Tag'
        type: array
      type:
        enum:
        - BASIC
        - STANDARD
        - ADVANCED
        type: string
    required:
    - name
    - paymentTiming
    - type
    type: object
  model.CreateInstanceResponse:
    properties:
      instanceID:
        type: string
      orderID:
        type: string
    type: object
  model.CreatePrivateLinkArgs:
    properties:
      autoDnsResolve:
        type: boolean
      ipAddress:
        type: string
      ipType:
        type: string
      subnetID:
        type: string
      vpcID:
        type: string
    required:
    - subnetID
    - vpcID
    type: object
  model.CreateProjectRequest:
    properties:
      projectName:
        description: 命名空间名称.
        type: string
      public:
        description: The public status of the project. The valid values are "true",
          "false".
        type: string
    required:
    - projectName
    - public
    type: object
  model.DeletePrivateLinkArgs:
    properties:
      subnetID:
        type: string
      vpcID:
        type: string
    type: object
  model.DeleteWhiteListArgs:
    properties:
      items:
        items:
          type: string
        type: array
    required:
    - items
    type: object
  model.DomainRequest:
    properties:
      certId:
        type: string
      certName:
        type: string
      domainName:
        type: string
    type: object
  model.DomainResult:
    properties:
      certExpireTime:
        type: string
      certId:
        type: string
      certName:
        type: string
      domainICPed:
        description: domainICPed 域名是否备案,默认false
        type: boolean
      domainName:
        type: string
      domainStatus:
        description: DomainStatus 域名状态(creating:生效中,created:正常,failed:异常)
        type: string
      domainStatusDesc:
        type: string
    type: object
  model.Endpoint:
    properties:
      key:
        type: string
      value:
        type: string
    type: object
  model.EndpointPattern:
    properties:
      endpointType:
        type: string
      endpoints:
        items:
          $ref: '#/definitions/model.Endpoint'
        type: array
    type: object
  model.ExecutionResult:
    properties:
      endTime:
        description: |-
          The end time
          Format: date-time
        type: string
      failed:
        description: The count of failed executions
        type: integer
      id:
        description: The ID of the execution
        type: integer
      inProgress:
        description: The count of in_progress executions
        type: integer
      policyId:
        description: The ID if the policy that the execution belongs to
        type: integer
      startTime:
        description: |-
          The start time
          Format: date-time
        type: string
      status:
        description: The status of the execution
        type: string
      statusText:
        description: The status text
        type: string
      stopped:
        description: The count of stopped executions
        type: integer
      succeed:
        description: The count of succeed executions
        type: integer
      total:
        description: The total count of all executions
        type: integer
      trigger:
        description: The trigger mode
        type: string
    type: object
  model.Filter:
    properties:
      type:
        description: The replication policy filter type.
        type: string
      value:
        description: The value of replication policy filter.
        type: string
    type: object
  model.GCHistoryResult:
    properties:
      Status:
        description: |-
          the status of gc job.
          Enums [Pending Running Stopped Error Success Scheduled]
        type: string
      artifactTrashRecords:
        description: the artifact trash record of gc job.
        items:
          $ref: '#/definitions/model.ArtifactTrashRecord'
        type: array
      deleted:
        description: if gc job was deleted.
        type: boolean
      freedSpace:
        description: the freed space of gc job.
        type: string
      id:
        description: the id of gc job.
        type: integer
      kind:
        description: the job kind of gc job.
        type: string
      name:
        description: the job name of gc job.
        type: string
      parameters:
        $ref: '#/definitions/model.JobParameters'
        description: the job parameters of gc job.
      result:
        description: |-
          the result of gc job.
          Enums [Error Success]
        type: string
      schedule:
        $ref: '#/definitions/model.ScheduleObj'
        description: schedule
      startTime:
        description: |-
          the start time of gc job.
          Format: date-time
        type: string
      updateTime:
        description: |-
          the finish time of gc job.
          Format: date-time
        type: string
    type: object
  model.ImageBuildConfigBasedImage:
    properties:
      dependencies:
        items:
          $ref: '#/definitions/model.ImageBuildConfigPackage'
        type: array
      image:
        type: string
      tag:
        type: string
    type: object
  model.ImageBuildConfigPackage:
    properties:
      opCode:
        type: string
      package:
        type: string
    type: object
  model.ImmutableRuleProjectResult:
    properties:
      projectID:
        description: projectID，命名空间ID
        type: integer
      projectName:
        description: projectName，命名空间名称
        type: string
    type: object
  model.ImmutableRuleRequest:
    properties:
      action:
        description: action,默认取值：immutable
        type: string
      disabled:
        description: disabled：是否禁用,默认为false
        type: boolean
      id:
        description: id，规则ID
        type: integer
      priority:
        description: priority，优先级，默认0
        type: integer
      projectID:
        description: projectID，命名空间ID
        type: integer
      scope_selectors:
        description: scope_selectors：镜像仓库匹配规则
        items:
          $ref: '#/definitions/model.ImmutableSelector'
        type: array
      tag_selectors:
        description: tag_selectors：镜像版本匹配规则
        items:
          $ref: '#/definitions/model.ImmutableSelector'
        type: array
      template:
        description: template，默认取值：immutable_template
        type: string
    type: object
  model.ImmutableRuleResult:
    properties:
      action:
        description: 'action,values is: immutable'
        type: string
      disabled:
        description: disabled：是否禁用
        type: boolean
      id:
        description: id，规则ID
        type: integer
      priority:
        description: priority，优先级
        type: integer
      projectID:
        description: projectID，命名空间ID
        type: integer
      projectName:
        description: ProjectName，命名空间名称
        type: string
      scope_selectors:
        description: scope_selectors,镜像仓库匹配规则
        items:
          $ref: '#/definitions/model.ImmutableSelector'
        type: array
      tag_selectors:
        description: tag_selectors,版本匹配规则
        items:
          $ref: '#/definitions/model.ImmutableSelector'
        type: array
      template:
        description: template
        type: string
    type: object
  model.ImmutableSelector:
    properties:
      decoration:
        description: 'decoration,scope_selectors value is: repoMatches、repoExcludes；tag_selectors
          value is: matches、excludes'
        type: string
      extras:
        description: extras，扩展字段
        type: string
      kind:
        description: kind,默认值：doublestar
        type: string
      pattern:
        description: pattern,规则取值，正则表达式
        type: string
    type: object
  model.InstanceDetail:
    properties:
      bucket:
        type: string
      info:
        $ref: '#/definitions/model.InstanceInfo'
      quota:
        $ref: '#/definitions/model.UserQuota'
      region:
        type: string
      statistic:
        $ref: '#/definitions/model.InstanceStatistic'
    type: object
  model.InstanceInfo:
    properties:
      createTime:
        type: string
      customDomains:
        items:
          type: string
        type: array
      expireTime:
        type: string
      groupIds:
        items:
          type: string
        type: array
      id:
        type: string
      instanceType:
        type: string
      name:
        type: string
      p2pManagerURL:
        type: string
      paymentTiming:
        type: string
      privateURL:
        type: string
      publicURL:
        type: string
      region:
        type: string
      status:
        type: string
      tags:
        items:
          $ref: '#/definitions/model.Tag'
        type: array
    type: object
  model.InstanceStatistic:
    properties:
      chart:
        type: integer
      namespace:
        type: integer
      repo:
        type: integer
      storage:
        type: integer
    type: object
  model.Item:
    properties:
      config:
        $ref: '#/definitions/model.Config'
      paymentMethod:
        description: 支付信息
        items:
          $ref: '#/definitions/model.PaymentMethod'
        type: array
    type: object
  model.JobParameters:
    properties:
      deleteUntagged:
        description: delete untagged artifacts
        type: boolean
      dryTun:
        description: dry run
        type: boolean
    type: object
  model.Label:
    properties:
      color:
        description: The color of label.
        type: string
      creation_time:
        description: The creation time of label.
        type: string
      deleted:
        description: The label is deleted or not.
        type: boolean
      description:
        description: The description of label.
        type: string
      id:
        description: The ID of label.
        type: integer
      name:
        description: The name of label.
        type: string
      project_id:
        description: The project ID if the label is a project label.
        type: integer
      scope:
        description: The scope of label, g for global labels and p for project labels.
        type: string
      update_time:
        description: The update time of label.
        type: string
    type: object
  model.ListAcceleratorPolicyResponse:
    properties:
      pageNo:
        type: integer
      pageSize:
        type: integer
      policies:
        items:
          $ref: '#/definitions/model.AcceleratorPolicy'
        type: array
      total:
        type: integer
    type: object
  model.ListBuildRepositoryTaskResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.BuildRepositoryTaskResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListCertResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.Cert'
        type: array
    type: object
  model.ListChartResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.ChartInfoResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListChartVersionResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.ChartVersionResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListDomainResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.DomainResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListExecutionResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.ExecutionResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListGCHistoryResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.GCHistoryResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListImmutableRuleResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.ImmutableRuleResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListInstanceResponse:
    properties:
      instances:
        items:
          $ref: '#/definitions/model.InstanceInfo'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListProjectResponse:
    properties:
      pageNo:
        type: integer
      pageSize:
        type: integer
      projects:
        items:
          $ref: '#/definitions/model.ProjectResult'
        type: array
      total:
        type: integer
    type: object
  model.ListRegistryResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.RegistryResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListReplicationResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.PolicyResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListRepositoryResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.RepositoryResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListSyncResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.SyncPolicyResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListTagResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.TagResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListTaskResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.TaskResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListTriggerJobResponse:
    properties:
      jobs:
        items:
          $ref: '#/definitions/model.TriggerJob'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListTriggerPolicyResponse:
    properties:
      pageNo:
        type: integer
      pageSize:
        type: integer
      policies:
        items:
          $ref: '#/definitions/model.TriggerPolicy'
        type: array
      total:
        type: integer
    type: object
  model.PaymentMethod:
    properties:
      type:
        type: string
      values:
        items:
          type: string
        type: array
    type: object
  model.PolicyRequest:
    properties:
      description:
        description: The description of the policy.
        type: string
      destProjectName:
        description: The destination namespace.
        type: string
      filters:
        description: |-
          The replication policy filter array.
          Example filters: [{"type": "name","value":"garenwen/virt-launcher"},{"type":"tag", "value":""}]
        items:
          $ref: '#/definitions/model.Filter'
        type: array
      name:
        description: The policy name.
        type: string
      override:
        description: Whether to override the resources on the destination registry.
        type: boolean
      srcRegistry:
        $ref: '#/definitions/model.SrcRegistry'
        description: The source registry.
      trigger:
        $ref: '#/definitions/model.TriggerReq'
        description: trigger
    type: object
  model.PolicyResult:
    properties:
      creationTime:
        description: The create time of the policy.
        type: string
      deletion:
        description: Whether to replicate the deletion operation.
        type: boolean
      description:
        description: The description of the policy.
        type: string
      destProjectName:
        description: The destination namespace.
        type: string
      destRegistry:
        $ref: '#/definitions/model.Registry'
        description: The destination registry.
      enabled:
        description: Whether the policy is enabled or not.
        type: boolean
      executionTimes:
        description: THe execution times of the policy
        type: integer
      filters:
        description: The replication policy filter array.
        items:
          $ref: '#/definitions/model.Filter'
        type: array
      id:
        description: The policy ID.
        type: integer
      name:
        description: The policy name.
        type: string
      override:
        description: Whether to override the resources on the destination registry.
        type: boolean
      srcRegistry:
        $ref: '#/definitions/model.Registry'
        description: The source registry.
      trigger:
        $ref: '#/definitions/model.Trigger'
        description: trigger
      updateTime:
        description: The update time of the policy.
        type: string
    type: object
  model.PrivateNetworks:
    properties:
      domain:
        type: string
      items:
        items:
          $ref: '#/definitions/model.PrivateNetworksItems'
        type: array
    type: object
  model.PrivateNetworksItems:
    properties:
      ipAddress:
        type: string
      resourceSource:
        type: string
      serviceNetID:
        type: string
      status:
        type: string
      subnetID:
        type: string
      vpcID:
        type: string
    type: object
  model.ProjectResult:
    properties:
      autoScan:
        description: Whether scan images automatically when pushing. The valid values
          are "true", "false".
        type: string
      chartCount:
        description: The total number of charts under this project.
        type: integer
      creationTime:
        description: |-
          The creation time of the project.
          Format: date-time
        type: string
      projectId:
        description: Project ID
        type: integer
      projectName:
        description: The name of the project.
        type: string
      public:
        description: The public status of the project. The valid values are "true",
          "false".
        type: string
      repoCount:
        description: The number of the repositories under this project.
        type: integer
      updateTime:
        description: |-
          The update time of the project.
          Format: date-time
        type: string
    type: object
  model.PublicLinkAction:
    properties:
      action:
        enum:
        - open
        - close
        type: string
    required:
    - action
    type: object
  model.PublicNetworkInfo:
    properties:
      domain:
        type: string
      status:
        type: string
      whitelist:
        items:
          $ref: '#/definitions/model.PublicNetworkInfoWhitelist'
        type: array
    type: object
  model.PublicNetworkInfoWhitelist:
    properties:
      description:
        type: string
      ipAddr:
        type: string
    required:
    - ipAddr
    type: object
  model.Registry:
    properties:
      creationTime:
        description: The create time of the policy.
        type: string
      credential:
        $ref: '#/definitions/model.RegistryCredential'
        description: credential
      description:
        description: Description of the registry.
        type: string
      id:
        description: The registry ID.
        type: integer
      insecure:
        description: Whether or not the certificate will be verified when Harbor tries
          to access the server.
        type: boolean
      name:
        description: The registry name.
        type: string
      region:
        description: The region of the registry.
        type: string
      status:
        description: Health status of the registry.
        type: string
      type:
        description: Type of the registry, e.g. 'harbor'.
        type: string
      updateTime:
        description: The update time of the policy.
        type: string
      url:
        description: The registry URL string.
        type: string
    type: object
  model.RegistryCredential:
    properties:
      accessKey:
        description: Access key, e.g. user name when credential type is 'basic'.
        type: string
      accessSecret:
        description: Access secret, e.g. password when credential type is 'basic'.
        type: string
      type:
        description: Credential type, such as 'basic', 'oauth'.
        type: string
    type: object
  model.RegistryRequest:
    properties:
      credential:
        $ref: '#/definitions/model.RegistryCredential'
        description: credential
      description:
        description: Description of the registry.
        type: string
      insecure:
        description: Whether or not the certificate will be verified when Harbor tries
          to access the server.
        type: boolean
      name:
        description: The registry name.
        type: string
      type:
        description: Type of the registry, e.g. 'harbor'.
        enum:
        - harbor
        - docker-hub
        - docker-registry
        - baidu-ccr
        type: string
      url:
        description: The registry URL string.
        type: string
    required:
    - type
    type: object
  model.RegistryResult:
    properties:
      creationTime:
        description: creation time
        type: string
      credential:
        $ref: '#/definitions/model.RegistryCredential'
        description: credential
      description:
        description: description
        type: string
      id:
        description: id
        type: integer
      insecure:
        description: insecure
        type: boolean
      name:
        description: name
        type: string
      status:
        description: status
        type: string
      type:
        description: type
        type: string
      updateTime:
        description: update time
        type: string
      url:
        description: url
        type: string
    type: object
  model.RepositoryResult:
    properties:
      creationTime:
        description: |-
          The creation time of the repository
          Format: date-time
        type: string
      description:
        description: The description of the repository
        type: string
      privateRepositoryPath:
        description: The path of private repository
        type: string
      projectName:
        description: The project name the repository
        type: string
      public:
        description: project is public or not
        type: string
      pullCount:
        description: The count that the artifact inside the repository pulled
        type: integer
      repositoryName:
        description: The name of the repository
        type: string
      repositoryPath:
        description: The path of the repository
        type: string
      tagCount:
        description: The count of the tags inside the repository
        type: integer
      updateTime:
        description: |-
          The update time of the repository
          Format: date-time
        type: string
    type: object
  model.ResetPasswordArgs:
    properties:
      password:
        type: string
    required:
    - password
    type: object
  model.Result:
    properties:
      orderId:
        type: string
    type: object
  model.RetentionPolicy:
    properties:
      algorithm:
        description: algorithm
        type: string
      id:
        description: id
        type: integer
      rules:
        description: rules
        items:
          $ref: '#/definitions/model.RetentionRule'
        type: array
      scope:
        $ref: '#/definitions/model.RetentionPolicyScope'
        description: scope
      trigger:
        $ref: '#/definitions/model.RetentionRuleTrigger'
        description: trigger
    type: object
  model.RetentionPolicyScope:
    properties:
      level:
        description: level
        type: string
      ref:
        description: ref
        type: integer
    type: object
  model.RetentionRule:
    properties:
      action:
        description: action
        type: string
      disabled:
        description: disabled
        type: boolean
      id:
        description: id
        type: integer
      params:
        additionalProperties: true
        description: params
        type: object
      priority:
        description: priority
        type: integer
      scope_selectors:
        additionalProperties:
          items:
            $ref: '#/definitions/model.RetentionSelector'
          type: array
        description: scope selectors
        type: object
      tag_selectors:
        description: tag selectors
        items:
          $ref: '#/definitions/model.RetentionSelector'
        type: array
      template:
        description: template
        type: string
    type: object
  model.RetentionRuleTrigger:
    properties:
      kind:
        description: kind
        type: string
      references:
        description: references
      settings:
        description: settings
    type: object
  model.RetentionSelector:
    properties:
      decoration:
        description: decoration
        type: string
      extras:
        description: extras
        type: string
      kind:
        description: kind
        type: string
      pattern:
        description: pattern
        type: string
    type: object
  model.ScanOverview:
    properties:
      description:
        type: string
      fixVersion:
        type: string
      id:
        type: string
      links:
        items:
          type: string
        type: array
      package:
        type: string
      severity:
        type: string
      version:
        type: string
    type: object
  model.ScanOverviewResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.ScanOverview'
        type: array
      lastScanTime:
        type: string
      pageNo:
        type: integer
      pageSize:
        type: integer
      summary:
        additionalProperties:
          type: integer
        type: object
      total:
        type: integer
    type: object
  model.ScheduleObj:
    properties:
      type:
        description: |-
          The schedule type. The valid values are 'Hourly', 'Daily', 'Weekly', 'Custom', 'Manual' and 'None'.
          'Manual' means to trigger it right away and 'None' means to cancel the schedule.

          Enum: [Hourly Daily Weekly Custom Manual None]
        type: string
    type: object
  model.Settings:
    properties:
      cron:
        description: The cron string for scheduled trigger
        type: string
    type: object
  model.SrcRegistry:
    properties:
      id:
        description: The registry ID.
        type: integer
    type: object
  model.StartExecutionRequest:
    properties:
      policyId:
        description: The ID of policy that the execution belongs to.
        type: integer
    required:
    - policyId
    type: object
  model.SyncPolicyResult:
    properties:
      creationTime:
        description: The create time of the policy.
        type: string
      description:
        description: The description of the policy.
        type: string
      destInstanceId:
        description: The destination instance.
        type: string
      destProjectName:
        description: The destination namespace.
        type: string
      destRegion:
        type: string
      id:
        description: The policy ID.
        type: integer
      name:
        description: The policy name.
        type: string
      override:
        description: Whether to override the resources on the destination registry.
        type: boolean
      srcProjectName:
        description: SrcProjectName src project name
        type: string
      srcRegion:
        type: string
      srcRepositoryName:
        description: SrcRepository src repository
        type: string
      srcTagName:
        description: SrcTag scr tag
        type: string
      syncType:
        description: SyncType of the Sync, e.g. 'image'.
        type: string
      trigger:
        $ref: '#/definitions/model.SyncTrigger'
        description: trigger
      updateTime:
        description: The update time of the policy.
        type: string
    type: object
  model.SyncRequest:
    properties:
      description:
        description: Description of the Sync.
        type: string
      destInstanceId:
        description: The destination instance.
        type: string
      destProjectName:
        description: DestProjectName dest project name
        type: string
      destRegion:
        description: The destination region.
        type: string
      name:
        description: The Sync name.
        type: string
      override:
        description: Whether to override the resources on the destination registry.
        type: boolean
      srcProjectName:
        description: SrcProjectName src project name
        type: string
      srcRepository:
        description: SrcRepository src repository
        type: string
      srcTag:
        description: SrcTag scr tag
        type: string
      syncType:
        description: SyncType of the Sync, e.g. 'image'.
        type: string
      trigger:
        $ref: '#/definitions/model.Trigger'
        description: trigger
    required:
    - destInstanceId
    - name
    - srcProjectName
    type: object
  model.SyncTrigger:
    properties:
      type:
        description: The replication policy trigger type. The valid values are manual,
          event_based and scheduled.
        type: string
    type: object
  model.Tag:
    properties:
      tagKey:
        type: string
      tagValue:
        type: string
    type: object
  model.TagResult:
    properties:
      acceleratorStatus:
        description: The tag of repository accelerate status
        type: string
      architecture:
        description: Architecture The architecture of repository
        type: string
      author:
        description: Author
        type: string
      digest:
        description: The digest of the artifact
        type: string
      immutabled:
        description: The tag where immutabled
        type: boolean
      os:
        description: OS
        type: string
      projectId:
        description: The ID of the project that the artifact belongs to
        type: integer
      pullTime:
        description: |-
          The latest pull time of the tag
          Format: date-time
        type: string
      pushTime:
        description: |-
          The push time of the tag
          Format: date-time
        type: string
      repositoryId:
        description: The ID of the repository that the artifact belongs to
        type: integer
      scanOverview:
        $ref: '#/definitions/model.TagScanOverview'
        description: The scan overview
      size:
        description: The size of the artifact
        type: integer
      tagName:
        description: The name of the tag
        type: string
      type:
        description: The type of the artifact, e.g. image, chart, etc
        type: string
    type: object
  model.TagScanOverview:
    properties:
      endTime:
        description: |-
          The end time of the scan process that generating report
          Example: 2006-01-02T15:04:05
          Format: date-time
        type: string
      fixable:
        description: |-
          The number of the fixable vulnerabilities
          Example: 100
        type: integer
      reportId:
        description: |-
          id of the native scan report
          Example: 5f62c830-f996-11e9-957f-0242c0a89008
        type: string
      scanStatus:
        description: |-
          The status of the report generating process
          Example: Success
        type: string
      severity:
        description: 漏洞等级 Critical 危及 High 严重  Medium 中等 Low 较低
        type: string
      startTime:
        description: |-
          The start time of the scan process that generating report
          Example: 2006-01-02T14:04:05
          Format: date-time
        type: string
      summary:
        additionalProperties:
          type: integer
        description: |-
          Numbers of the vulnerabilities with different severity
          Example: {"Critical":5,"High":5}
        type: object
      total:
        description: |-
          The total number of the found vulnerabilities
          Example: 500
        type: integer
    type: object
  model.TaskResult:
    properties:
      destResource:
        description: The destination resource that the task operates
        type: string
      endTime:
        description: |-
          The end time of the task
          Format: date-time
        type: string
      executionId:
        description: The ID of the execution that the task belongs to
        type: integer
      id:
        description: The ID of the task
        type: integer
      jobId:
        description: The ID of the underlying job that the task related to
        type: string
      operation:
        description: The operation of the task
        type: string
      resourceType:
        description: The type of the resource that the task operates
        type: string
      srcResource:
        description: The source resource that the task operates
        type: string
      startTime:
        description: |-
          The start time of the task
          Format: date-time
        type: string
      status:
        description: The status of the task
        type: string
    type: object
  model.TemporaryPasswordArgs:
    properties:
      duration:
        maximum: 24
        minimum: 1
        type: integer
    required:
    - duration
    type: object
  model.TemporaryPasswordResponse:
    properties:
      password:
        type: string
    type: object
  model.TestPolicyFilter:
    properties:
      filters:
        description: The policy filters
        items:
          $ref: '#/definitions/model.AcceleratorFilter'
        type: array
      repository:
        type: string
    type: object
  model.TestPolicyFilterResponse:
    properties:
      matched:
        type: boolean
    type: object
  model.Trigger:
    properties:
      triggerSettings:
        $ref: '#/definitions/model.Settings'
        description: trigger settings
      type:
        description: The replication policy trigger type. The valid values are manual,
          event_based and scheduled.
        type: string
    type: object
  model.TriggerFilter:
    properties:
      type:
        description: The trigger policy filter type.
        enum:
        - project_name
        - repository_name
        - tag_name
        type: string
      value:
        description: The value of trigger policy filter.
        type: string
    required:
    - type
    type: object
  model.TriggerJob:
    properties:
      creationTime:
        description: creation time
        type: string
      eventType:
        description: event type
        type: string
      id:
        description: id
        type: integer
      image:
        description: FIXME 后期会使用images 取代image,这里过渡期保留
        type: string
      images:
        items:
          type: string
        type: array
      notifyType:
        description: notify type
        type: string
      operator:
        description: operator
        type: string
      policyId:
        description: policy id
        type: integer
      status:
        description: status
        type: string
      updateTime:
        description: update time
        type: string
    type: object
  model.TriggerPolicy:
    properties:
      creationTime:
        description: creation time
        type: string
      description:
        description: description
        type: string
      enabled:
        description: enabled
        type: boolean
      eventTypes:
        description: event types
        items:
          type: string
        type: array
      filters:
        description: filters
        items:
          $ref: '#/definitions/model.TriggerFilter'
        type: array
      id:
        description: id
        type: integer
      name:
        description: name
        type: string
      targets:
        description: targets
        items:
          $ref: '#/definitions/model.TriggerTarget'
        type: array
      updateTime:
        description: update time
        type: string
    type: object
  model.TriggerPolicyRequest:
    properties:
      description:
        description: The description of the policy.
        maxLength: 300
        type: string
      eventTypes:
        description: The event type one of PUSH_ARTIFACT UPLOAD_CHART PULL_ARTIFACT
          DOWNLOAD_CHART DELETE_ARTIFACT DELETE_CHART
        items:
          type: string
        type: array
      filters:
        description: The policy filters
        items:
          $ref: '#/definitions/model.TriggerFilter'
        type: array
      name:
        description: The policy name.
        maxLength: 65
        minLength: 2
        type: string
      targets:
        items:
          $ref: '#/definitions/model.TriggerTarget'
        type: array
    required:
    - eventTypes
    - name
    type: object
  model.TriggerReq:
    properties:
      type:
        description: |-
          The replication policy trigger type. The valid values are manual, event_based and scheduled.
          镜像迁移只填写这个 manual
          实例同步可以写 manual event_base
        type: string
    type: object
  model.TriggerTarget:
    properties:
      address:
        maxLength: 1024
        type: string
      headers:
        additionalProperties:
          type: string
        description: header key just support 'Authorization'
        type: object
    required:
    - address
    type: object
  model.UpdateInstanceRequest:
    properties:
      name:
        maxLength: 256
        minLength: 1
        type: string
    required:
    - name
    type: object
  model.UpdateProjectRequest:
    properties:
      autoScan:
        description: Whether scan images automatically when pushing. The valid values
          are "true", "false".
        type: string
      public:
        description: The public status of the project. The valid values are "true",
          "false".
        type: string
    required:
    - autoScan
    - public
    type: object
  model.UpdateRepositoryRequest:
    properties:
      description:
        description: The description of the repository
        type: string
    type: object
  model.UpgradeInstanceRequest:
    properties:
      paymentMethod:
        items:
          $ref: '#/definitions/model.PaymentMethod'
        type: array
      type:
        enum:
        - BASIC
        - STANDARD
        - ADVANCED
        type: string
    required:
    - type
    type: object
  model.UserProfile:
    properties:
      name:
        type: string
    type: object
  model.UserQuota:
    properties:
      chart:
        type: integer
      namespace:
        type: integer
      repo:
        type: integer
    type: object
host: ccr.baidubce.com
info:
  contact:
    email: <EMAIL>
    name: duzhanwei,wenmanxiang
  description: CCR Service 提供 RESTFUL 风格 API, 对接 Console 及 OpenAPI 请求
  title: CCR Service API
  version: 0.0.1
paths:
  /accelerators/policies/filters:
    post:
      consumes:
      - application/json
      description: 测试加速器策略规则
      parameters:
      - description: 测试加速器规则
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.TestPolicyFilter'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.TestPolicyFilterResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 测试加速器策略规则
      tags:
      - accelerator
  /features:
    get:
      consumes:
      - application/json
      description: 查询用户是否在白名单内
      parameters:
      - description: 功能类型
        in: query
        name: featureType
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: boolean
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询用户是否在白名单内
      tags:
      - feature
  /instances:
    get:
      consumes:
      - application/json
      description: 列举CCR实例
      parameters:
      - description: 页码
        in: query
        name: pageNo
        type: integer
      - description: 页大小
        in: query
        name: pageSize
        type: integer
      - description: 关键字类型
        in: query
        name: keywordType
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      - description: 是否在全区域内搜索
        in: query
        name: acrossregion
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.ListInstanceResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 列举CCR实例
      tags:
      - instance
    post:
      consumes:
      - application/json
      description: 创建CCR实例
      parameters:
      - description: 创建实例参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.CreateInstanceRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Success
          schema:
            $ref: '#/definitions/model.CreateInstanceResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建CCR实例
      tags:
      - instance
  /instances/{id}/credential:
    post:
      consumes:
      - application/json
      description: 创建临时密码
      parameters:
      - description: 实例ID
        in: path
        name: id
        required: true
        type: string
      - description: 用户id
        in: query
        name: userId
        type: string
      - description: 新密码
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.TemporaryPasswordArgs'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.TemporaryPasswordResponse'
        default:
          description: ""
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建临时密码
      tags:
      - credential
    put:
      consumes:
      - application/json
      description: 重置密码
      parameters:
      - description: 实例ID
        in: path
        name: id
        required: true
        type: string
      - description: 用户id
        in: query
        name: userId
        type: string
      - description: 新密码
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.ResetPasswordArgs'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        default:
          description: ""
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 重置密码
      tags:
      - credential
  /instances/{instanceId}:
    delete:
      consumes:
      - application/json
      description: 删除CCR实例
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除CCR实例
      tags:
      - instance
    get:
      consumes:
      - application/json
      description: 获取CCR实例
      parameters:
      - description: 实例id
        in: path
        name: instanceId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.InstanceDetail'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取CCR实例
      tags:
      - instance
    put:
      consumes:
      - application/json
      description: 更改CCR实例信息
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 更新请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.UpdateInstanceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.InstanceInfo'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 更改CCR实例信息
      tags:
      - instance
  /instances/{instanceId}/accelerators/policies:
    delete:
      consumes:
      - application/json
      description: 批量删除加速器策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 加速器策略ID数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteInt64Request'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除加速器策略
      tags:
      - accelerator
    get:
      consumes:
      - application/json
      description: 获取加速器策略列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 加速器策略名称
        in: query
        name: policyName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListAcceleratorPolicyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取加速器策略列表
      tags:
      - accelerator
    post:
      consumes:
      - application/json
      description: 创建加速器策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 加速器参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.AcceleratorPolicyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建加速器策略
      tags:
      - accelerator
  /instances/{instanceId}/accelerators/policies/{policyId}:
    delete:
      consumes:
      - application/json
      description: 删除加速器策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 加速器策略ID
        in: path
        name: policyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除加速器策略
      tags:
      - accelerator
    get:
      consumes:
      - application/json
      description: 查询加速器策略详情
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 加速器策略ID
        in: path
        name: policyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: accelerator policy
          schema:
            $ref: '#/definitions/model.AcceleratorPolicy'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询加速器策略详情
      tags:
      - accelerator
    put:
      consumes:
      - application/json
      description: 修改加速器策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 加速器策略ID
        in: path
        name: policyId
        required: true
        type: string
      - description: 加速器参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.AcceleratorPolicyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 修改加速器策略
      tags:
      - accelerator
  /instances/{instanceId}/accelerators/policies/{policyId}/enable:
    put:
      consumes:
      - application/json
      description: 启动或关闭加速器策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 加速器策略ID
        in: path
        name: policyId
        required: true
        type: string
      - description: 是否开启
        in: query
        name: enabled
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 启动或关闭加速器策略
      tags:
      - accelerator
  /instances/{instanceId}/domain:
    get:
      consumes:
      - application/json
      description: 获取自定义域名列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListDomainResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取自定义域名列表
      tags:
      - domain
    post:
      consumes:
      - application/json
      description: 创建自定义域名
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 自定义域名创建参数
        in: body
        name: CustomDomainRequest
        required: true
        schema:
          $ref: '#/definitions/model.DomainRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建自定义域名
      tags:
      - domain
  /instances/{instanceId}/domain/{domainName}:
    delete:
      consumes:
      - application/json
      description: 删除自定义域名
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 域名
        in: path
        name: domainName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除自定义域名
      tags:
      - domain
    get:
      consumes:
      - application/json
      description: 查询自定义域名详情
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 域名
        in: path
        name: domainName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.DomainResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询自定义域名详情
      tags:
      - domain
    put:
      consumes:
      - application/json
      description: 更新自定义域名
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 域名
        in: path
        name: domainName
        required: true
        type: string
      - description: 自定义域名创建参数
        in: body
        name: CustomDomainRequest
        required: true
        schema:
          $ref: '#/definitions/model.DomainRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 更新自定义域名
      tags:
      - domain
  /instances/{instanceId}/domain/{domainName}/check:
    get:
      consumes:
      - application/json
      description: 检查域名备案信息
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 域名
        in: path
        name: domainName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.CheckDomainICPResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 检查域名备案信息
      tags:
      - domain
  /instances/{instanceId}/executions:
    get:
      consumes:
      - application/json
      description: 查询镜像迁移或者实例同步执行记录列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 策略ID
        in: query
        name: policyId
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListExecutionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询镜像迁移或者实例同步执行记录列表
      tags:
      - replication
      - sync
    post:
      consumes:
      - application/json
      description: 执行镜像迁移或者实例同步
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: start execution body
        in: body
        name: execution
        required: true
        schema:
          $ref: '#/definitions/model.StartExecutionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 执行镜像迁移或者实例同步
      tags:
      - replication
      - sync
  /instances/{instanceId}/executions/{executionId}:
    get:
      consumes:
      - application/json
      description: 查询镜像迁移或者实例同步执行记录详情
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 镜像迁移或者实例同步执行记录ID
        in: path
        name: executionId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ExecutionResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询镜像迁移或者实例同步执行记录详情
      tags:
      - replication
      - sync
    put:
      consumes:
      - application/json
      description: 停止镜像迁移或者实例同步
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 执行ID
        in: path
        name: executionId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 停止镜像迁移或者实例同步
      tags:
      - replication
      - sync
  /instances/{instanceId}/executions/{executionId}/tasks:
    get:
      consumes:
      - application/json
      description: 查询镜像迁移或者实例同步执行执行任务记录
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 迁移任务ID
        in: path
        name: executionId
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListTaskResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询镜像迁移或者实例同步执行执行任务记录
      tags:
      - replication
      - sync
  /instances/{instanceId}/executions/{executionId}/tasks/{taskId}/log:
    get:
      consumes:
      - text/plain
      description: 查询镜像迁移或者实例同步执行任务日志
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 执行ID
        in: path
        name: executionId
        required: true
        type: string
      - description: 任务ID
        in: path
        name: taskId
        required: true
        type: string
      produces:
      - text/plain
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询镜像迁移或者实例同步执行任务日志
      tags:
      - replication
      - sync
  /instances/{instanceId}/gcs:
    get:
      consumes:
      - application/json
      description: 查询垃圾回收调度任务历史记录
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.ListGCHistoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询垃圾回收调度任务历史记录
      tags:
      - gc
    post:
      consumes:
      - application/json
      description: 执行垃圾回收调度任务
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: create gc schedule body
        in: body
        name: schedule
        required: true
        schema:
          $ref: '#/definitions/model.CreateGCRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 执行垃圾回收调度任务
      tags:
      - gc
  /instances/{instanceId}/gcs/{gcId}/log:
    get:
      consumes:
      - application/json
      description: 查询垃圾回收调度任务日志
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 任务ID
        in: path
        name: gcId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询垃圾回收调度任务日志
      tags:
      - gc
  /instances/{instanceId}/immutable:
    delete:
      consumes:
      - application/json
      description: 批量删除版本不可变规则
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 版本不可变规则ID数组
        in: body
        name: immutableIds
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteInt64Request'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除版本不可变规则
      tags:
      - immutable
    get:
      consumes:
      - application/json
      description: 获取版本不可变规则列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListImmutableRuleResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取版本不可变规则列表
      tags:
      - immutable
    post:
      consumes:
      - application/json
      description: 创建版本不可变规则
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 版本不可变规则参数
        in: body
        name: immutableRule
        required: true
        schema:
          $ref: '#/definitions/model.ImmutableRuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建版本不可变规则
      tags:
      - immutable
  /instances/{instanceId}/immutable/{immutableId}:
    delete:
      consumes:
      - application/json
      description: 删除版本不可变规则
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 版本不可变规则ID
        in: path
        name: immutableId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除版本不可变规则
      tags:
      - immutable
    get:
      consumes:
      - application/json
      description: 查询版本不可变策略详情
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 版本不可变规则ID
        in: path
        name: immutableId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ImmutableRuleResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询版本不可变策略详情
      tags:
      - immutable
    put:
      consumes:
      - application/json
      description: 更新版本不可变规则
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 版本不可变规则ID
        in: path
        name: immutableId
        required: true
        type: string
      - description: 版本不可变规则参数
        in: body
        name: immutableRule
        required: true
        schema:
          $ref: '#/definitions/model.ImmutableRuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 更新版本不可变规则
      tags:
      - immutable
  /instances/{instanceId}/immutable/{immutableId}/enable:
    put:
      consumes:
      - application/json
      description: 启用或禁用版本不可变规则
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 版本不可变规则ID
        in: path
        name: immutableId
        required: true
        type: string
      - description: 是否启用(true or false)
        in: query
        name: enabled
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 启用或禁用版本不可变规则
      tags:
      - immutable
  /instances/{instanceId}/immutable/project:
    get:
      consumes:
      - application/json
      description: 查询命名空间列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: get immutable project list success
          schema:
            items:
              $ref: '#/definitions/model.ImmutableRuleProjectResult'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询命名空间列表
      tags:
      - immutable
  /instances/{instanceId}/privatelinks:
    delete:
      consumes:
      - application/json
      description: 释放一个私有网络
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 白名单信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.DeletePrivateLinkArgs'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 释放一个私有网络
      tags:
      - network
    get:
      consumes:
      - application/json
      description: 获取专有网络列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.PrivateNetworks'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取专有网络列表
      tags:
      - network
    post:
      consumes:
      - application/json
      description: 创建一个专有网络
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 创建参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.CreatePrivateLinkArgs'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建一个专有网络
      tags:
      - network
  /instances/{instanceId}/projects:
    delete:
      consumes:
      - application/json
      description: 批量删除命名空间
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除命名空间
      tags:
      - project
    get:
      consumes:
      - application/json
      description: 获取当前用户的命名空间列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: query
        name: projectName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListProjectResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取当前用户的命名空间列表
      tags:
      - project
    post:
      consumes:
      - application/json
      description: 创建命名空间
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: create project body
        in: body
        name: project
        required: true
        schema:
          $ref: '#/definitions/model.CreateProjectRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ProjectResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建命名空间
      tags:
      - project
  /instances/{instanceId}/projects/{projectName}:
    delete:
      consumes:
      - application/json
      description: 删除命名空间
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除命名空间
      tags:
      - project
    get:
      consumes:
      - application/json
      description: 通过命名空间名称projectName查询命名空间
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ProjectResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 通过命名空间名称projectName查询命名空间
      tags:
      - project
    put:
      consumes:
      - application/json
      description: 更新命名空间
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: update project request
        in: body
        name: project
        required: true
        schema:
          $ref: '#/definitions/model.UpdateProjectRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ProjectResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 更新命名空间
      tags:
      - project
  /instances/{instanceId}/projects/{projectName}/charts:
    delete:
      consumes:
      - application/json
      description: 批量删除helm chart
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除helm chart
      tags:
      - chart
    get:
      consumes:
      - application/json
      description: 查询 helm chart 列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称
        in: query
        name: chartName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListChartResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询 helm chart 列表
      tags:
      - chart
    post:
      consumes:
      - multipart/form-data
      description: 上传helm chart 文件
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: The chart file
        in: formData
        name: chart
        required: true
        type: file
      - description: The prov file
        in: formData
        name: prov
        type: file
      produces:
      - multipart/form-data
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 上传helm chart 文件
      tags:
      - chart
  /instances/{instanceId}/projects/{projectName}/charts/{chartName}:
    delete:
      consumes:
      - application/json
      description: 删除 helm chart
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称
        in: path
        name: chartName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            items:
              $ref: '#/definitions/model.ChartVersion'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除 helm chart
      tags:
      - chart
  /instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions:
    delete:
      consumes:
      - application/json
      description: 批量删除helm chart版本
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart 名称
        in: path
        name: chartName
        required: true
        type: string
      - description: helm chart版本名称数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除helm chart版本
      tags:
      - chart
    get:
      consumes:
      - application/json
      description: 查询chart version 列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称
        in: path
        name: chartName
        required: true
        type: string
      - description: helm chart版本
        in: query
        name: chartVersion
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListChartVersionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询chart version 列表
      tags:
      - chart
  /instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions/{chartVersion}:
    delete:
      consumes:
      - application/json
      description: 删除helm chart版本
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称
        in: path
        name: chartName
        required: true
        type: string
      - description: helm chart版本
        in: path
        name: chartVersion
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除helm chart版本
      tags:
      - chart
  /instances/{instanceId}/projects/{projectName}/charts/download/{filename}:
    get:
      consumes:
      - application/json
      - ' text/plain'
      - ' */*'
      description: 下载 helm chart
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart 文件名
        in: path
        name: filename
        required: true
        type: string
      produces:
      - application/x-tar
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 下载 helm chart
      tags:
      - chart
  /instances/{instanceId}/projects/{projectName}/repositories:
    delete:
      consumes:
      - application/json
      description: 批量删除镜像仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除镜像仓库
      tags:
      - repository
    get:
      consumes:
      - application/json
      description: 查询镜像仓库列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: query
        name: repositoryName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListRepositoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询镜像仓库列表
      tags:
      - repository
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}:
    delete:
      consumes:
      - application/json
      description: 删除单个镜像仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除单个镜像仓库
      tags:
      - repository
    get:
      consumes:
      - application/json
      description: 通过镜像仓库名称查询镜像仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.RepositoryResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 通过镜像仓库名称查询镜像仓库
      tags:
      - repository
    put:
      consumes:
      - application/json
      description: 修改单个镜像仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: update repository request
        in: body
        name: repository
        required: true
        schema:
          $ref: '#/definitions/model.UpdateRepositoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.RepositoryResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 修改单个镜像仓库
      tags:
      - repository
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds:
    delete:
      consumes:
      - application/json
      description: 批量删除镜像构建任务
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: 镜像构建任务ID数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除镜像构建任务
      tags:
      - repository
    get:
      consumes:
      - application/json
      description: 获取镜像构建任务
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        type: integer
      - description: 关键字类型
        in: query
        name: keywordType
        type: string
      - description: 关键字
        in: query
        name: keyword
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListBuildRepositoryTaskResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取镜像构建任务
      tags:
      - repository
    post:
      consumes:
      - application/json
      description: 创建镜像构建任务
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: create build repository task body
        in: body
        name: imagebuild
        required: true
        schema:
          $ref: '#/definitions/model.BuildRepositoryTaskRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建镜像构建任务
      tags:
      - repository
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds/{buildId}:
    delete:
      consumes:
      - application/json
      description: 删除镜像构建任务
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: 镜像构建任务ID
        in: path
        name: buildId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除镜像构建任务
      tags:
      - repository
    get:
      consumes:
      - application/json
      description: 获取镜像构建任务详情
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: 镜像构建任务ID
        in: path
        name: buildId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.BuildRepositoryTaskResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取镜像构建任务详情
      tags:
      - repository
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds/{buildId}/log:
    get:
      consumes:
      - application/json
      description: 获取镜像构建任务日志
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: 镜像构建任务ID
        in: path
        name: buildId
        required: true
        type: string
      produces:
      - text/plain
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取镜像构建任务日志
      tags:
      - repository
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags:
    delete:
      consumes:
      - application/json
      description: 批量删除镜像tag
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除镜像tag
      tags:
      - tag
    get:
      consumes:
      - application/json
      description: 查询镜像仓库tag列表
      parameters:
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: tag名称
        in: query
        name: tagName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListTagResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询镜像仓库tag列表
      tags:
      - tag
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}:
    delete:
      consumes:
      - application/json
      description: 删除单个镜像tag
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除单个镜像tag
      tags:
      - tag
    get:
      consumes:
      - application/json
      description: 查询单个单个镜像tag
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.TagResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询单个单个镜像tag
      tags:
      - tag
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/buildhistory:
    get:
      consumes:
      - application/json
      description: 查询构建历史
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.BuildHistoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询构建历史
      tags:
      - tag
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scan:
    post:
      consumes:
      - application/json
      description: 镜像扫描
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 镜像扫描
      tags:
      - tag
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scan/{reportId}/log:
    get:
      consumes:
      - text/plain
      description: 镜像扫描日志
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      - description: 扫描日志ID
        in: path
        name: reportId
        required: true
        type: string
      produces:
      - text/plain
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 镜像扫描日志
      tags:
      - tag
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scanoverview:
    get:
      consumes:
      - application/json
      description: 查询镜像漏洞
      parameters:
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ScanOverviewResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询镜像漏洞
      tags:
      - tag
  /instances/{instanceId}/publiclinks:
    get:
      consumes:
      - application/json
      description: 获取公网信息
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.PublicNetworkInfo'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取公网信息
      tags:
      - network
    put:
      consumes:
      - application/json
      description: 更新公有网络
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 更新参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.PublicLinkAction'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 更新公有网络
      tags:
      - network
  /instances/{instanceId}/publiclinks/whitelist:
    delete:
      consumes:
      - application/json
      description: 删除白名单
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 白名单信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.DeleteWhiteListArgs'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除白名单
      tags:
      - network
    post:
      consumes:
      - application/json
      description: 添加白名单
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 白名单信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.PublicNetworkInfoWhitelist'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 添加白名单
      tags:
      - network
  /instances/{instanceId}/registries:
    get:
      consumes:
      - application/json
      description: 获取远程仓库列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 远程仓库名称
        in: query
        name: registryName
        type: string
      - description: 远程仓库类型
        in: query
        name: registryType
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListRegistryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取远程仓库列表
      tags:
      - registry
    post:
      consumes:
      - application/json
      description: 创建新的远程仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: create registry body
        in: body
        name: registry
        required: true
        schema:
          $ref: '#/definitions/model.RegistryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建新的远程仓库
      tags:
      - registry
  /instances/{instanceId}/registries/{registryId}:
    delete:
      consumes:
      - application/json
      description: 删除远程仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 远程仓库ID
        in: path
        name: registryId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除远程仓库
      tags:
      - registry
    get:
      consumes:
      - application/json
      description: 通过registryId查询远程仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 镜像远程仓库ID
        in: path
        name: registryId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.RegistryResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 通过registryId查询远程仓库
      tags:
      - registry
    put:
      consumes:
      - application/json
      description: 修改单个远程仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 远程仓库ID
        in: path
        name: registryId
        required: true
        type: string
      - description: update registry request
        in: body
        name: registry
        required: true
        schema:
          $ref: '#/definitions/model.RegistryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.RegistryResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 修改单个远程仓库
      tags:
      - registry
  /instances/{instanceId}/registries/ping:
    post:
      consumes:
      - application/json
      description: 检查远程仓库健康状态
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: check registry health request
        in: body
        name: registryHealth
        required: true
        schema:
          $ref: '#/definitions/model.RegistryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 检查远程仓库健康状态
      tags:
      - registry
  /instances/{instanceId}/replications:
    get:
      consumes:
      - application/json
      description: 获取策略列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 镜像迁移策略名称
        in: query
        name: policyName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListReplicationResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取策略列表
      tags:
      - replication
    post:
      consumes:
      - application/json
      description: 创建镜像迁移策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: create policy body
        in: body
        name: policy
        required: true
        schema:
          $ref: '#/definitions/model.PolicyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建镜像迁移策略
      tags:
      - replication
  /instances/{instanceId}/replications/{policyId}:
    delete:
      consumes:
      - application/json
      description: 删除镜像迁移策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 策略ID
        in: path
        name: policyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除镜像迁移策略
      tags:
      - replication
    get:
      consumes:
      - application/json
      description: 通过policyId查询策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 镜像迁移策略名称ID
        in: path
        name: policyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.PolicyResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 通过policyId查询策略
      tags:
      - replication
    put:
      consumes:
      - application/json
      description: 更新镜像迁移策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 策略ID
        in: path
        name: policyId
        required: true
        type: string
      - description: update policy body
        in: body
        name: policy
        required: true
        schema:
          $ref: '#/definitions/model.PolicyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 更新镜像迁移策略
      tags:
      - replication
  /instances/{instanceId}/repositories:
    get:
      consumes:
      - application/json
      description: 查询所有的镜像仓库列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 镜像仓库名称
        in: query
        name: repositoryName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListRepositoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询所有的镜像仓库列表
      tags:
      - repository
  /instances/{instanceId}/retentions:
    post:
      consumes:
      - application/json
      description: 创建命名空间
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: create retention body
        in: body
        name: retention
        required: true
        schema:
          $ref: '#/definitions/model.RetentionPolicy'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建命名空间
      tags:
      - retention
  /instances/{instanceId}/syncs:
    get:
      consumes:
      - application/json
      description: 获取实例同步策略列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 实例同步策略名称
        in: query
        name: policyName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListSyncResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取实例同步策略列表
      tags:
      - sync
    post:
      consumes:
      - application/json
      description: 创建实例同步策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 用户id
        in: query
        name: userId
        type: string
      - description: create sync body
        in: body
        name: Sync
        required: true
        schema:
          $ref: '#/definitions/model.SyncRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建实例同步策略
      tags:
      - sync
  /instances/{instanceId}/syncs/{policyId}:
    delete:
      consumes:
      - application/json
      description: 删除实例同步策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 策略ID
        in: path
        name: policyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除实例同步策略
      tags:
      - sync
    get:
      consumes:
      - application/json
      description: 查询实例同步策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 实例同步策略名称ID
        in: path
        name: policyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.SyncPolicyResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询实例同步策略
      tags:
      - sync
    put:
      consumes:
      - application/json
      description: 更新实例同步策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 实例同步策略名称ID
        in: path
        name: policyId
        required: true
        type: string
      - description: 用户id
        in: query
        name: userId
        type: string
      - description: update sync body
        in: body
        name: Sync
        required: true
        schema:
          $ref: '#/definitions/model.SyncRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 更新实例同步策略
      tags:
      - sync
  /instances/{instanceId}/tags:
    put:
      consumes:
      - application/json
      description: 更改CCR实例tag
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 更新tag请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.AssignTagsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 更改CCR实例tag
      tags:
      - instance
  /instances/{instanceId}/triggers/policies:
    delete:
      consumes:
      - application/json
      description: 批量删除触发器策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 触发器策略ID数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteInt64Request'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除触发器策略
      tags:
      - trigger
    get:
      consumes:
      - application/json
      description: 获取触发器策略列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 触发器策略名称
        in: query
        name: policyName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListTriggerPolicyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取触发器策略列表
      tags:
      - trigger
    post:
      consumes:
      - application/json
      description: 创建触发器策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 触发器参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.TriggerPolicyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建触发器策略
      tags:
      - trigger
  /instances/{instanceId}/triggers/policies/{policyId}:
    delete:
      consumes:
      - application/json
      description: 删除触发器策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 触发器策略ID
        in: path
        name: policyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除触发器策略
      tags:
      - trigger
    get:
      consumes:
      - application/json
      description: 查询触发器策略详情
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 触发器策略ID
        in: path
        name: policyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: trigger policy
          schema:
            $ref: '#/definitions/model.TriggerPolicy'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询触发器策略详情
      tags:
      - trigger
    put:
      consumes:
      - application/json
      description: 修改触发器策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 触发器策略ID
        in: path
        name: policyId
        required: true
        type: string
      - description: 触发器参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.TriggerPolicyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 修改触发器策略
      tags:
      - trigger
  /instances/{instanceId}/triggers/policies/{policyId}/enable:
    put:
      consumes:
      - application/json
      description: 启动或关闭触发器策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 触发器策略ID
        in: path
        name: policyId
        required: true
        type: string
      - description: 是否开启
        in: query
        name: enabled
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 启动或关闭触发器策略
      tags:
      - trigger
  /instances/{instanceId}/triggers/policies/{policyId}/jobs:
    get:
      consumes:
      - application/json
      description: 获取触发器任务列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 触发器策略ID
        in: path
        name: policyId
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListTriggerJobResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取触发器任务列表
      tags:
      - trigger
  /instances/{instanceId}/triggers/policies/{policyId}/jobs/{jobId}/retry:
    put:
      consumes:
      - application/json
      description: 重新执行触发器任务
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 触发器策略ID
        in: path
        name: policyId
        required: true
        type: string
      - description: 触发器任务ID
        in: path
        name: jobId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 重新执行触发器任务
      tags:
      - trigger
  /instances/{instanceId}/triggers/policies/targets:
    post:
      consumes:
      - application/json
      description: 测试触发器策略目标地址
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 触发器参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.TriggerTarget'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 测试触发器策略目标地址
      tags:
      - trigger
  /instances/{instanceId}/upgrade:
    put:
      consumes:
      - application/json
      description: 升级CCR实例
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 升级配置
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.UpgradeInstanceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "501":
          description: Not Implemented
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 升级CCR实例
      tags:
      - instance
  /instances/renew:
    post:
      consumes:
      - application/json
      description: 续费CCR实例
      parameters:
      - description: 续费实例参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.ConfirmOrderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.Result'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 续费CCR实例
      tags:
      - instance
  /registrytypes:
    get:
      consumes:
      - application/json
      description: 获取远程仓库类型列表
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            additionalProperties:
              $ref: '#/definitions/model.EndpointPattern'
            type: object
      summary: 获取远程仓库类型列表
      tags:
      - registry
  /users/cert:
    get:
      consumes:
      - application/json
      description: 获取用户证书列表
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListCertResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取用户证书列表
      tags:
      - user
  /users/profile:
    get:
      consumes:
      - application/json
      description: 获取用户详情
      parameters:
      - description: 用户id
        in: query
        name: userId
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.UserProfile'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取用户详情
      tags:
      - user
swagger: "2.0"
