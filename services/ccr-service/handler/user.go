package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type User struct {
	clients clientset.ClientSetInterface
}

func NewUserHandler(clis clientset.ClientSetInterface) *User {
	return &User{
		clients: clis,
	}
}

// GetUserProfile 获取用户详情
// @Summary 获取用户详情
// @Description 获取用户详情
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param userId query string false "用户id"
// @Success 200 {object} model.UserProfile "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /users/profile [get]
func (h *User) GetUserProfile(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	username := middleware.UserNameFromContext(c)
	if username == "" {
		logger.Errorf("no username provided")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	userProfile := &model.UserProfile{Name: username}

	c.JSON(http.StatusOK, userProfile)
}

// ListUserCert 获取用户证书列表
// @Summary 获取用户证书列表
// @Description 获取用户证书列表
// @Tags user
// @Accept application/json
// @Produce application/json
// @Success 200 {object} model.ListCertResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /users/cert [get]
func (h *User) ListUserCert(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	logger.Infof("ListUserCert")
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	accountID := middleware.AccountIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	if accountID == "" || userID == "" {
		logger.Errorf("no user account provided")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	domainService := service.NewDomainService(h.clients)
	res, err := domainService.ListUserCert(c, accountID, userID)
	if err != nil {
		logger.Errorf("list user cert failed:%s", err)
	}
	c.JSON(http.StatusOK, res)
}
