package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/pkg/reg/model"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	ccrmodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	registry2 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/registry"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service/harbor/registry"
	"icode.baidu.com/baidu/scan/safesdk-go/safehttp"
)

func TestRegistryHandler_checkRegistryUrl(t *testing.T) {
	type fields struct {
		safeclient          *safehttp.Client
		registryService     registry2.RegistryServiceInterface
		registryTypeService registry2.RegistryTypeServiceInterface
	}
	allowlist := "hub.baidubce.com;iregistry.baidu-int.com;registry.baidubce.com"

	// 从 http.Client 获取一个 safehttp.Client
	sc := safehttp.FromHTTPClient(&http.Client{})
	// Add 追加模式
	sc.Mode = safehttp.Add
	// 设置内网白名单
	allows := strings.Split(allowlist, ";")
	sc.SetWhitelist(allows...)

	type args struct {
		c            *gin.Context
		registryType string
		urlStr       string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "empty",
			fields: fields{
				safeclient:          sc,
				registryTypeService: registry2.NewRegistryTypeService(),
			},
			args: args{
				c:            &gin.Context{},
				registryType: "docker-hub",
				urlStr:       "",
			},
			wantErr: true,
		},
		{
			name: "ccr",
			fields: fields{
				safeclient:          sc,
				registryTypeService: registry2.NewRegistryTypeService(),
			},
			args: args{
				c:            &gin.Context{},
				registryType: "harbor",
				urlStr:       "https://registry.baidubce.com",
			},
			wantErr: false,
		},
		{
			name: "cce",
			fields: fields{
				safeclient:          sc,
				registryTypeService: registry2.NewRegistryTypeService(),
			},
			args: args{
				c:            &gin.Context{},
				registryType: "docker-registry",
				urlStr:       "https://hub.baidubce.com",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &RegistryHandler{
				safeclient:          tt.fields.safeclient,
				registryService:     tt.fields.registryService,
				registryTypeService: tt.fields.registryTypeService,
			}
			if err := h.checkRegistryUrl(tt.args.c, tt.args.registryType, tt.args.urlStr); (err != nil) != tt.wantErr {
				t.Errorf("checkRegistryUrl() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRegistryHandler_CreateRegistry(t *testing.T) {

	ctx, _ := gin.CreateTestContext(newGinResponseWriter())

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type fields struct {
		safeclient          *safehttp.Client
		registryService     registry2.RegistryServiceInterface
		registryTypeService registry2.RegistryTypeServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *Response
	}{
		{
			name: "case1",
			fields: fields{
				safeclient:          safehttp.DefaultClient,
				registryService:     registry.NewMockRegistryServiceInterface(ctrl),
				registryTypeService: registry.NewMockRegistryTypeServiceInterface(ctrl),
			},
			args: args{
				c: ctx,
			},
			want: NewResponse(http.StatusOK, "success", "xxx", ctx),
		},
	}
	for _, tt := range tests {
		t.SkipNow()
		req := ccrmodel.RegistryRequest{
			Name: "test",
			Type: "harbor",
			Credential: &ccrmodel.RegistryCredential{
				AccessKey:    "xxxx",
				AccessSecret: "xxxx",
				Type:         "basic",
			},
		}

		data, err := json.Marshal(req)
		if err != nil {
			t.Errorf("json marshal failed: %s", err)
		}
		ctx.Params = append(ctx.Params, gin.Param{Key: "instanceId", Value: "xxxx"})

		ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
		ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

		mockRegistryTypeService := registry.NewMockRegistryTypeServiceInterface(ctrl)

		adapterInfos := make(map[ccrmodel.RegistryType]*model.EndpointPattern)
		adapterInfos[model.RegistryTypeDockerHub] = &model.EndpointPattern{
			model.EndpointPatternTypeFix,
			[]*model.Endpoint{
				{"docker-hub", "https://hub.docker.com"},
			},
		}
		adapterInfos[model.RegistryTypeHarbor] = &model.EndpointPattern{
			model.EndpointPatternTypeList,
			[]*model.Endpoint{
				{"baidu-ccr", "https://registry.baidubce.com"},
				{"iregistry", "https://iregistry.baidu-int.com"}},
		}
		adapterInfos[model.RegistryTypeDockerRegistry] = &model.EndpointPattern{
			model.EndpointPatternTypeList,
			[]*model.Endpoint{
				{"baidu-cce", "https://hub.baidubce.com"},
			},
		}

		mockRegistryTypeService.EXPECT().ListRegistryTypes(tt.args.c).AnyTimes().Return(adapterInfos)

		mockRegistryService := registry.NewMockRegistryServiceInterface(ctrl)

		mockRegistryService.EXPECT().PostRegistriesPing(tt.args.c, req.Name, req.Credential.AccessSecret,
			req.Credential.AccessSecret, req.URL, req.Type, ccrmodel.RegistryForRepl, req.Insecure)

		mockRegistryService.EXPECT().CreateRegistry(tt.args.c, req.Name, req.Credential.AccessSecret,
			req.Credential.AccessSecret, req.URL, req.Type, ccrmodel.RegistryForRepl, req.Description, req.Insecure)

		t.Run(tt.name, func(t *testing.T) {
			h := &RegistryHandler{
				safeclient:          tt.fields.safeclient,
				registryService:     tt.fields.registryService,
				registryTypeService: tt.fields.registryTypeService,
			}
			assert.Equalf(t, tt.want, h.CreateRegistry(tt.args.c), "CreateRegistry(%v)", tt.args.c)
		})
	}
}
