package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-openapi/runtime"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	registryapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/registry"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/registry"
	"icode.baidu.com/baidu/scan/safesdk-go/safehttp"
)

type RegistryHandler struct {
	safeclient          *safehttp.Client
	registryService     registry.RegistryServiceInterface
	registryTypeService registry.RegistryTypeServiceInterface
}

func NewRegistryHandler(allowlist string) *RegistryHandler {

	// 从 http.Client 获取一个 safehttp.Client
	sc := safehttp.FromHTTPClient(&http.Client{})
	// Add 追加模式
	sc.Mode = safehttp.Add
	// 设置内网白名单
	allows := strings.Split(allowlist, ";")
	sc.SetWhitelist(allows...)

	return &RegistryHandler{
		safeclient:          sc,
		registryService:     registry.NewRegistryService(),
		registryTypeService: registry.NewRegistryTypeService(),
	}
}

// ListRegistries 获取远程仓库列表
// @Summary 获取远程仓库列表
// @Description 获取远程仓库列表
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registryName query string false "远程仓库名称"
// @Param registryType query string false "远程仓库类型"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListRegistryResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries [get]
func (h *RegistryHandler) ListRegistries(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	registryName, registryType := c.Query("registryName"), c.Query("registryType")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	registries, total, err := h.registryService.ListRegistries(c, registryName, registryType, pageNo, pageSize)
	if err != nil {
		logger.Errorf("list registry failed: %s", err)
		return HandleRegistrySwaggerErrors(c, err)
	}

	lrr := &model.ListRegistryResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: registries,
	}
	return NewResponse(http.StatusOK, "success", lrr, c)
}

// GetRegistry 通过registryId查询远程仓库
// @Summary 通过registryId查询远程仓库
// @Description 通过registryId查询远程仓库
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registryId path string true "镜像远程仓库ID"
// @Success 200 {object} model.RegistryResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries/{registryId} [get]
func (h *RegistryHandler) GetRegistry(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	registryId, err := strconv.ParseInt(c.Param("registryId"), 10, 64)
	if err != nil {
		logger.Errorf("registry ID is invalid")
		return NewResponse(http.StatusBadRequest, "registry ID is invalid", nil, c)
	}

	registryObj, err := h.registryService.GetRegistry(c, registryId)
	if err != nil {
		logger.Errorf("get registory failed: %s", err)
		return HandleRegistrySwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", registryObj, c)
}

// CreateRegistry 创建新的远程仓库
// @Summary 创建新的远程仓库
// @Description 创建新的远程仓库
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registry body model.RegistryRequest true "create registry body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries [post]
func (h *RegistryHandler) CreateRegistry(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	var crr model.RegistryRequest
	if err := c.Bind(&crr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}
	registryType := crr.Type
	urlStr := crr.URL
	if !strings.HasPrefix(urlStr, "https://") {
		urlStr = "https://" + strings.TrimPrefix(urlStr, "http://")
	}

	if err := h.checkRegistryUrl(c, registryType, urlStr); err != nil {
		logger.Errorf("check registry url failed: %s", err)
		return NewResponse(http.StatusBadRequest, "The registry url has been blocked", nil, c)
	}

	if err := h.registryService.PostRegistriesPing(c, crr.Name, crr.Credential.AccessKey, crr.Credential.AccessSecret,
		urlStr, registryType, model.RegistryForRepl, crr.Insecure); err != nil {
		logger.Errorf("ping registry failed: %s", err)
		return NewResponse(http.StatusBadRequest, "the registry is unhealthy", nil, c)
	}

	registryId, err := h.registryService.CreateRegistry(c, crr.Name, crr.Credential.AccessKey, crr.Credential.AccessSecret,
		urlStr, registryType, model.RegistryForRepl, crr.Description, crr.Insecure)
	if err != nil {
		logger.Errorf("create registry failed: %s", err)
		_, ok := err.(*runtime.APIError)
		if ok {
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
		switch err.(type) {
		case *registryapi.CreateRegistryBadRequest:
			return NewResponse(http.StatusBadRequest, "the registry is unhealthy,or username/passwd not correct", nil, c)
		case *registryapi.CreateRegistryConflict:
			return NewResponse(http.StatusConflict, "registry name already exists", nil, c)
		default:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}
	return NewResponse(http.StatusOK, "success", registryId, c)
}

// UpdateRegistry 修改单个远程仓库
// @Summary 修改单个远程仓库
// @Description 修改单个远程仓库
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registryId path string true "远程仓库ID"
// @Param registry body model.RegistryRequest true "update registry request"
// @Success 200 {object} model.RegistryResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries/{registryId} [put]
func (h *RegistryHandler) UpdateRegistry(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	registryId, err := strconv.ParseInt(c.Param("registryId"), 10, 64)
	if err != nil {
		logger.Errorf("registry ID is invalid")
		return NewResponse(http.StatusBadRequest, "registry ID is invalid", nil, c)
	}

	var urr model.RegistryRequest
	if err := c.Bind(&urr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}
	registryType := urr.Type
	urlStr := urr.URL
	if !strings.HasPrefix(urlStr, "https://") {
		urlStr = "https://" + strings.TrimPrefix(urlStr, "http://")
	}

	if err := h.checkRegistryUrl(c, registryType, urlStr); err != nil {
		logger.Errorf("check registry url failed: %s", err)
		return NewResponse(http.StatusBadRequest, "The registry url has been blocked", nil, c)
	}

	if err := h.registryService.PostRegistriesPing(c, urr.Name, urr.Credential.AccessKey, urr.Credential.AccessSecret, urlStr,
		urr.Type, model.RegistryForRepl, false); err != nil {
		logger.Errorf("ping registry failed: %s", err)
		return NewResponse(http.StatusBadRequest, "the registry is unhealthy", nil, c)
	}

	if _, err := h.registryService.UpdateRegistry(c, registryId, urr.Name, urr.Credential.AccessKey, urr.Credential.AccessSecret,
		urlStr, model.RegistryForRepl, urr.Description, false); err != nil {
		logger.Errorf("update registory failed: %s", err)
		//return HandleRegistrySwaggerErrors(c, err)
		if err != nil {
			logger.Errorf("create registry failed: %s", err)
			_, ok := err.(*runtime.APIError)
			if ok {
				return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
			}
			switch err.(type) {
			case *registryapi.UpdateRegistryNotFound:
				return NewResponse(http.StatusNotFound, fmt.Sprintf("registry: %s not found", urr.Name), nil, c)
			default:
				return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
			}
		}

	}

	registryResult, err := h.registryService.GetRegistry(c, registryId)
	if err != nil {
		logger.Errorf("get registory failed: %s", err)
		return HandleRegistrySwaggerErrors(c, err)
	}

	// trim ccr prefix
	registryResult.Name = registry.RealNameFromRegistry(registryResult.Name)

	return NewResponse(http.StatusOK, "success", registryResult, c)

}

// CheckHealth 检查远程仓库健康状态
// @Summary 检查远程仓库健康状态
// @Description 检查远程仓库健康状态
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registryHealth body model.RegistryRequest true "check registry health request"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries/ping [post]
func (h *RegistryHandler) CheckHealth(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	var chr model.RegistryRequest
	if err := c.Bind(&chr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	if err := h.registryService.PostRegistriesPing(c, chr.Name, chr.Credential.AccessKey, chr.Credential.AccessSecret,
		chr.URL, chr.Type, model.RegistryForRepl, chr.Insecure); err != nil {
		logger.Errorf("post registory ping failed: %s", err)
		return HandleRegistrySwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", nil, c)

}

// DeleteRegistry 删除远程仓库
// @Summary 删除远程仓库
// @Description 删除远程仓库
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registryId path string true "远程仓库ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries/{registryId} [delete]
func (h *RegistryHandler) DeleteRegistry(c *gin.Context) *Response {

	logger := gin_context.LoggerFromContext(c)

	registryId, err := strconv.ParseInt(c.Param("registryId"), 10, 64)
	if err != nil {
		logger.Errorf("registry ID is invalid")
		return NewResponse(http.StatusBadRequest, "registry ID is invalid", nil, c)
	}

	if err := h.registryService.DeleteRegistry(c, registryId); err != nil {
		logger.Errorf("delete registory failed: %s", err)
		return HandleRegistrySwaggerErrors(c, err)
	}
	return NewResponse(http.StatusOK, "success", nil, c)
}

// HandleRegistrySwaggerErrors takes a swagger generated error as input,
// which usually does not contain any form of error message,
// and outputs a new error with a proper message.
func HandleRegistrySwaggerErrors(c *gin.Context, in error) *Response {
	t, ok := in.(*runtime.APIError)
	if ok {
		switch t.Code {
		case http.StatusBadRequest:
			return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
		case http.StatusUnauthorized:
			return NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
		case http.StatusForbidden:
			return NewResponse(http.StatusForbidden, "user does not have permission to the registry", nil, c)
		case http.StatusNotFound:
			return NewResponse(http.StatusNotFound, "registry not found", nil, c)
		case http.StatusConflict:
			return NewResponse(http.StatusConflict, "registry name already exists", nil, c)
		case http.StatusPreconditionFailed:
			return NewResponse(http.StatusPreconditionFailed, "registry precondition failed", nil, c)
		case http.StatusInternalServerError:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		default:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}

	switch in.(type) {
	case *registryapi.CreateRegistryBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *registryapi.CreateRegistryConflict:
		return NewResponse(http.StatusConflict, "registry name already exists", nil, c)
	case *registryapi.DeleteRegistryNotFound:
		return NewResponse(http.StatusNotFound, "registry not found", nil, c)
	case *registryapi.UpdateRegistryNotFound:
		return NewResponse(http.StatusNotFound, "registry not found", nil, c)
	case *registryapi.GetRegistryNotFound:
		return NewResponse(http.StatusNotFound, "registry not found", nil, c)
	default:
		return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
	}
}

func (h *RegistryHandler) checkRegistryUrl(c *gin.Context, registryType, urlStr string) error {

	if len(urlStr) == 0 {
		return fmt.Errorf("registry url is empty")
	}

	registryTypes := h.registryTypeService.ListRegistryTypes(c)
	endpointPattern, ok := registryTypes[model.RegistryType(registryType)]
	if !ok {
		return fmt.Errorf("registry type: %s is not supported", registryType)
	}

	if endpointPattern.EndpointType == model.EndpointPatternTypeFix {
		if urlStr != endpointPattern.Endpoints[0].Value {
			return fmt.Errorf("registry type: %s url is not supported %s", registryType, urlStr)
		}
	}

	req, err := http.NewRequest(http.MethodHead, urlStr, nil)
	if err != nil {
		return fmt.Errorf("create registry head request failed: %w", err)
	}

	resp, err := h.safeclient.Do(req)
	if err != nil {
		return fmt.Errorf("check registry url failed: %s", err)
	}

	if resp != nil && resp.StatusCode < http.StatusOK && resp.StatusCode >= http.StatusInternalServerError {
		return fmt.Errorf("check registry url:%s is failed, status code: %d", urlStr, resp.StatusCode)
	}

	return nil
}

func (h *RegistryHandler) check(c *gin.Context, registryType, urlStr string) error {
	registryTypes := h.registryTypeService.ListRegistryTypes(c)
	endpointPattern, ok := registryTypes[model.RegistryType(registryType)]
	if !ok {
		return fmt.Errorf("registry type: %s is not supported", registryType)
	}

	if endpointPattern.EndpointType == model.EndpointPatternTypeFix {
		if urlStr != endpointPattern.Endpoints[0].Value {
			return fmt.Errorf("registry type: %s url is not supported %s", registryType, urlStr)
		}
	}
	return nil
}
