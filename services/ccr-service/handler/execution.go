package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-openapi/runtime"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/replication"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	ccrmodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/execution"
)

type ExecutionHandler struct {
	executionService *execution.ExecutionService
}

func NewExecutionHandler() *ExecutionHandler {
	return &ExecutionHandler{
		executionService: execution.NewExecutionService(),
	}
}

// StartExecution 执行镜像迁移或者实例同步
// @Summary 执行镜像迁移或者实例同步
// @Description 执行镜像迁移或者实例同步
// @Tags replication,sync
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param execution body model.StartExecutionRequest true "start execution body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/executions [post]
func (h *ExecutionHandler) StartExecution(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	var sre ccrmodel.StartExecutionRequest
	if err := c.Bind(&sre); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	execution := &model.StartReplicationExecution{
		PolicyID: sre.PolicyId,
	}
	if _, err := h.executionService.StartExecution(c, execution); err != nil {
		logger.Errorf("start execution failed: %s", err)
		return HandleExecutionSwaggerErrors(c, err)
	}
	return NewResponse(http.StatusOK, "success", nil, c)
}

// StopExecution 停止镜像迁移或者实例同步
// @Summary 停止镜像迁移或者实例同步
// @Description 停止镜像迁移或者实例同步
// @Tags replication,sync
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param executionId path string true "执行ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/executions/{executionId} [put]
func (h *ExecutionHandler) StopExecution(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	executionId, err := strconv.ParseInt(c.Param("executionId"), 10, 64)
	if err != nil {
		logger.Errorf("execution ID is invalid")
		return NewResponse(http.StatusBadRequest, "execution ID is invalid", nil, c)
	}

	if err = h.executionService.StopExecution(c, executionId); err != nil {
		logger.Errorf("stop execution failed: %s", err)
		return HandleExecutionSwaggerErrors(c, err)
	}
	return NewResponse(http.StatusOK, "success", nil, c)
}

// ListExecutions 查询镜像迁移或者实例同步执行记录列表
// @Summary 查询镜像迁移或者实例同步执行记录列表
// @Description 查询镜像迁移或者实例同步执行记录列表
// @Tags replication,sync
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId query string true "策略ID"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListExecutionResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/executions [get]
func (h *ExecutionHandler) ListExecutions(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	policyId, err := strconv.ParseInt(c.Query("policyId"), 10, 64)
	if err != nil {
		logger.Errorf("policy ID is invalid")
		return NewResponse(http.StatusBadRequest, "policy ID is invalid", nil, c)
	}

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	executions, total, err := h.executionService.ListExecutions(c, &policyId, &pageNo, &pageSize)
	if err != nil {
		logger.Errorf("list execution failed: %s", err)
		return HandleExecutionSwaggerErrors(c, err)
	}
	lper := &ccrmodel.ListExecutionResponse{
		PageInfo: ccrmodel.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: executions,
	}
	return NewResponse(http.StatusOK, "success", lper, c)
}

// GetExecution 查询镜像迁移或者实例同步执行记录详情
// @Summary 查询镜像迁移或者实例同步执行记录详情
// @Description 查询镜像迁移或者实例同步执行记录详情
// @Tags replication,sync
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param executionId path string true "镜像迁移或者实例同步执行记录ID"
// @Success 200 {object} model.ExecutionResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/executions/{executionId} [get]
func (h *ExecutionHandler) GetExecution(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	executionId, err := strconv.ParseInt(c.Param("executionId"), 10, 64)
	if err != nil {
		logger.Errorf("execution ID is invalid")
		return NewResponse(http.StatusBadRequest, "execution ID is invalid", nil, c)
	}

	execution, err := h.executionService.GetExecution(c, executionId)
	if err != nil {
		logger.Errorf("get execution failed: %s", err)
		return HandleExecutionSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", execution, c)
}

// ListTasks 查询镜像迁移或者实例同步执行任务记录
// @Summary 查询镜像迁移或者实例同步执行执行任务记录
// @Description 查询镜像迁移或者实例同步执行执行任务记录
// @Tags replication,sync
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param executionId path string true "迁移任务ID"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListTaskResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/executions/{executionId}/tasks [get]
func (h *ExecutionHandler) ListTasks(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	executionId, err := strconv.ParseInt(c.Param("executionId"), 10, 64)
	if err != nil {
		logger.Errorf("execution ID is invalid")
		return NewResponse(http.StatusBadRequest, "execution ID is invalid", nil, c)
	}

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	tasks, total, err := h.executionService.ListTasks(c, executionId, &pageNo, &pageSize)
	if err != nil {
		logger.Errorf("get execution failed: %s", err)
		return HandleExecutionSwaggerErrors(c, err)
	}

	ltr := &ccrmodel.ListTaskResponse{
		PageInfo: ccrmodel.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: tasks,
	}

	return NewResponse(http.StatusOK, "success", ltr, c)
}

// GetTaskLog 查询镜像迁移或者实例同步执行任务日志
// @Summary 查询镜像迁移或者实例同步执行任务日志
// @Description 查询镜像迁移或者实例同步执行任务日志
// @Tags replication,sync
// @Accept text/plain
// @Produce text/plain
// @Param instanceId path string true "实例ID"
// @Param executionId path string true "执行ID"
// @Param taskId path string true "任务ID"
// @Success 200 {object} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/executions/{executionId}/tasks/{taskId}/log [get]
func (h *ExecutionHandler) GetTaskLog(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	executionId, err := strconv.ParseInt(c.Param("executionId"), 10, 64)
	if err != nil {
		logger.Errorf("execution ID is invalid")
		return NewResponse(http.StatusBadRequest, "execution ID is invalid", nil, c)
	}

	taskId, err := strconv.ParseInt(c.Param("taskId"), 10, 64)
	if err != nil {
		logger.Errorf("execution ID is invalid")
		return NewResponse(http.StatusBadRequest, "execution ID is invalid", nil, c)
	}

	log, err := h.executionService.GetTaskLog(c, executionId, taskId)
	if err != nil {
		logger.Errorf("get execution failed: %s", err)
		return HandleExecutionSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", log, c)
}

// HandleExecutionSwaggerErrors takes a swagger generated error as input,
// which usually does not contain any form of error message,
// and outputs a new error with a proper message.
func HandleExecutionSwaggerErrors(c *gin.Context, in error) *Response {
	t, ok := in.(*runtime.APIError)
	if ok {
		switch t.Code {
		case http.StatusBadRequest:
			return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
		case http.StatusUnauthorized:
			return NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
		case http.StatusForbidden:
			return NewResponse(http.StatusForbidden, "user does not have permission to the execution", nil, c)
		case http.StatusNotFound:
			return NewResponse(http.StatusNotFound, "execution not found", nil, c)
		case http.StatusConflict:
			return NewResponse(http.StatusConflict, "execution name already exists", nil, c)
		case http.StatusPreconditionFailed:
			return NewResponse(http.StatusPreconditionFailed, "execution precondition failed", nil, c)
		case http.StatusInternalServerError:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		default:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}

	switch in.(type) {
	case *replication.StartReplicationBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *replication.StopReplicationNotFound:
		return NewResponse(http.StatusNotFound, "execution not found", nil, c)
	case *replication.GetReplicationExecutionNotFound:
		return NewResponse(http.StatusBadRequest, "execution not found", nil, c)
	default:
		return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
	}
}
