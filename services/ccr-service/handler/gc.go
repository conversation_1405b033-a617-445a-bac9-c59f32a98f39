package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	ccrmodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/gc"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type GCHandler struct {
	gcService gc.GCServiceInterface
}

func NewGcHandler() *GCHandler {
	return &GCHandler{
		gcService: gc.NewGCService(),
	}
}

// CreateGCSchedule 执行垃圾回收调度任务
// @Summary 执行垃圾回收调度任务
// @Description 执行垃圾回收调度任务
// @Tags gc
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param schedule body model.CreateGCRequest true "create gc schedule body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/gcs [post]
func (h *GCHandler) CreateGCSchedule(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var req *ccrmodel.CreateGCRequest
	if err := c.Bind(&req); err != nil {
		logger.Errorf("[GC] bind request failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	if !req.DeleteUntagged {
		logger.Errorf("deleteUntagged is not true")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	_, err := h.gcService.CreateGCSchedule(c, req)
	if err != nil {
		logger.Errorf("new gc schedule failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, nil)
}

// ListGCHistory 查询垃圾回收调度任务历史记录
// @Summary 查询垃圾回收调度任务历史记录
// @Description 查询垃圾回收调度任务历史记录
// @Tags gc
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListGCHistoryResponse
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/gcs [get]
func (h *GCHandler) ListGCHistory(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	gcHistories, total, err := h.gcService.ListGCHistory(c, &pageNo, &pageSize)
	if err != nil {
		logger.Errorf("get gc history failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	lrr := &ccrmodel.ListGCHistoryResponse{
		PageInfo: ccrmodel.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: gcHistories,
	}

	c.JSON(http.StatusOK, lrr)
}

// GetGCLog 查询垃圾回收调度任务日志
// @Summary 查询垃圾回收调度任务日志
// @Description 查询垃圾回收调度任务日志
// @Tags gc
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param gcId path string true "任务ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/gcs/{gcId}/log [get]
func (h *GCHandler) GetGCLog(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	gcID, err := strconv.ParseInt(c.Param("gcId"), 10, 64)
	if err != nil {
		logger.Errorf("gc id is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	gcLog, err := h.gcService.GetGCLog(c, gcID)
	if err != nil {
		logger.Errorf("get gc log failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	c.JSON(http.StatusOK, gcLog)

}
