package domain

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type DomainHandler struct {
	clients       clientset.ClientSetInterface
	domainService service.DomainServiceInterface
}

// NewDomainHandler create domain handler
func NewDomainHandler(clients clientset.ClientSetInterface) *DomainHandler {
	return &DomainHandler{
		clients:       clients,
		domainService: service.NewDomainService(clients),
	}
}

// CreateDomain 创建自定义域名
// @Summary 创建自定义域名
// @Description 创建自定义域名
// @Tags domain
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param CustomDomainRequest body model.DomainRequest true "自定义域名创建参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/domain [post]
func (h *DomainHandler) CreateDomain(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var domainReq model.DomainRequest
	if err := c.Bind(&domainReq); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	domainName := domainReq.DomainName
	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)
	curObj := networkObj.DeepCopy()
	if curObj.Spec.CustomDomains == nil {
		curObj.Spec.CustomDomains = make([]v1alpha1.CustomDomain, 0)
	}

	if len(curObj.Spec.CustomDomains) > 0 {
		for _, v := range curObj.Spec.CustomDomains {
			// 域名已存在，不允许重复创建
			if v.DomainName == domainName {
				logger.Errorf("domain %s already exist in custom domain", domainName)
				gin_context.E(c, gin_context.AlreadyExist(requestID))
				return
			}
		}
	}

	curObj.Spec.CustomDomains = append(curObj.Spec.CustomDomains, v1alpha1.CustomDomain{
		DomainName: domainName,
		CertID:     domainReq.CertID,
		CertName:   domainReq.CertName,
	})

	err := h.clients.K8sClient().Patch(c, curObj, client.MergeFrom(networkObj))
	if err != nil {
		logger.Errorf("patch to ccr cnc network  %v failed: %v", curObj.GetName(), err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return

	}

	c.JSON(http.StatusOK, map[string]string{
		"msg": "success",
	})

}

// UpdateDomain 更新自定义域名
// @Summary 更新自定义域名
// @Description 更新自定义域名
// @Tags domain
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param domainName path string true "域名"
// @Param CustomDomainRequest body model.DomainRequest true "自定义域名创建参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/domain/{domainName} [put]
func (h *DomainHandler) UpdateDomain(c *gin.Context) {
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	logger := gin_context.LoggerFromContext(c)

	var domainReq model.DomainRequest
	if err := c.Bind(&domainReq); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		c.JSON(http.StatusBadRequest, nil)
		return
	}
	domainName := c.Param("domainName")
	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)
	curObj := networkObj.DeepCopy()
	if len(curObj.Spec.CustomDomains) == 0 {
		logger.Errorf("custom domain is empty")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	customDomains := make([]v1alpha1.CustomDomain, 0)
	for _, v := range curObj.Spec.CustomDomains {
		if v.DomainName == domainName {
			v.CertID = domainReq.CertID
			v.CertName = domainReq.CertName
		}
		customDomains = append(customDomains, v)
	}
	curObj.Spec.CustomDomains = customDomains

	err := h.clients.K8sClient().Patch(c, curObj, client.MergeFrom(networkObj))
	if err != nil {
		logger.Errorf("patch to ccr cnc network  %v failed: %v", curObj.GetName(), err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return

	}

	c.JSON(http.StatusOK, map[string]string{
		"msg": "success",
	})
}

// DeleteDomain 删除自定义域名
// @Summary 删除自定义域名
// @Description 删除自定义域名
// @Tags domain
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param domainName path string true "域名"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/domain/{domainName} [delete]
func (h *DomainHandler) DeleteDomain(c *gin.Context) {
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	logger := gin_context.LoggerFromContext(c)

	domainName := c.Param("domainName")
	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)
	curObj := networkObj.DeepCopy()
	if len(curObj.Spec.CustomDomains) == 0 {
		logger.Errorf("custom domain is empty")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	customDamins := make([]v1alpha1.CustomDomain, 0)
	for _, v := range curObj.Spec.CustomDomains {
		if v.DomainName == domainName {
			continue
		}
		customDamins = append(customDamins, v)
	}
	curObj.Spec.CustomDomains = customDamins

	err := h.clients.K8sClient().Patch(c, curObj, client.MergeFrom(networkObj))
	if err != nil {
		logger.Errorf("patch to ccr cnc network  %v failed: %v", curObj.GetName(), err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, map[string]string{
		"msg": "success",
	})
}

// ListDomain 获取自定义域名列表
// @Summary 获取自定义域名列表
// @Description 获取自定义域名列表
// @Tags domain
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListDomainResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/domain [get]
func (h *DomainHandler) ListDomain(c *gin.Context) {

	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var pagedInfo model.PagedListOption
	var err error
	if err = c.BindQuery(&pagedInfo); err != nil {
		logger.Errorf("bind query failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	if pagedInfo.PageNo < 0 || pagedInfo.PageSize < 0 {
		logger.Errorf("page is not valid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	if pagedInfo.PageNo == 0 {
		pagedInfo.PageNo = 1
	}

	if pagedInfo.PageSize == 0 {
		pagedInfo.PageSize = 10
	}

	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)
	curObj := networkObj.DeepCopy()
	curObjDomains := curObj.Spec.CustomDomains
	curObjDomainStatus := curObj.Status.CustomDomainStatus
	domains := make([]*model.DomainResult, 0)
	if len(curObjDomains) > 0 {
		for _, v := range curObjDomains {
			domain := &model.DomainResult{}
			domain.DomainName = v.DomainName
			domain.CertID = v.CertID
			domain.CertName = v.CertName
			for _, s := range curObjDomainStatus {
				if s.DomainName == v.DomainName {
					domain.DomainICPed = s.DomainICPed
					domain.CertExpireTime = s.CertExpireTime.Time
					domain.DomainStatus = s.DomainStatus
					domain.DomainStatusDesc = s.DomainStatusDesc
				}
			}
			domains = append(domains, domain)
		}
	}

	listDomain := &model.ListDomainResponse{
		PageInfo: model.PageInfo{
			Total:    len(domains),
			PageNo:   pagedInfo.PageNo,
			PageSize: pagedInfo.PageSize,
		},
		Items: domains,
	}

	// paged
	startPos := (pagedInfo.PageNo - 1) * pagedInfo.PageSize
	if startPos > len(domains) {
		listDomain.Items = []*model.DomainResult{}
	}
	if startPos <= len(domains) && startPos+pagedInfo.PageSize > len(domains) {
		listDomain.Items = domains[startPos:]
	}
	if startPos+pagedInfo.PageSize <= len(domains) {
		listDomain.Items = domains[startPos : startPos+pagedInfo.PageSize]
	}

	c.JSON(http.StatusOK, listDomain)
}

// GetDomain 查询自定义域名详情
// @Summary 查询自定义域名详情
// @Description 查询自定义域名详情
// @Tags domain
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param domainName path string true "域名"
// @Success 200 {object} model.DomainResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/domain/{domainName} [get]
func (h *DomainHandler) GetDomain(c *gin.Context) {
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	logger := gin_context.LoggerFromContext(c)

	domainName := c.Param("domainName")
	if domainName == "" {
		logger.Errorf("domainName is empty")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	domain := &model.DomainResult{}

	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)
	curObj := networkObj.DeepCopy()
	curObjDomains := curObj.Spec.CustomDomains
	curObjDomainStatus := curObj.Status.CustomDomainStatus
	if len(curObjDomains) > 0 {
		for _, v := range curObjDomains {
			if domainName == v.DomainName {
				domain.DomainName = v.DomainName
				domain.CertID = v.CertID
				domain.CertName = v.CertName
				for _, s := range curObjDomainStatus {
					if s.DomainName == v.DomainName {
						domain.DomainICPed = s.DomainICPed
						domain.CertExpireTime = s.CertExpireTime.Time
						domain.DomainStatus = s.DomainStatus
						domain.DomainStatusDesc = s.DomainStatusDesc
					}
				}
			}
		}
	}

	c.JSON(http.StatusOK, domain)

}

// CheckDomainICP 检查域名备案信息
// @Summary 检查域名备案信息
// @Description 检查域名备案信息
// @Tags domain
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param domainName path string true "域名"
// @Success 200 {object} model.CheckDomainICPResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/domain/{domainName}/check [get]
func (h *DomainHandler) CheckDomainICP(c *gin.Context) {
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	logger := gin_context.LoggerFromContext(c)

	domainName := c.Param("domainName")
	if domainName == "" {
		logger.Errorf("domainName is empty")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	// 检查域名备案状态
	domainICPed, err := h.domainService.CheckDomainIcp(c, domainName)
	if err != nil {
		logger.Errorf("check domain [%s] icp failed: %v", domainName, err)
		c.JSON(http.StatusBadRequest, map[string]string{})
		return
	}

	domainICp := &model.CheckDomainICPResponse{}
	domainICp.DomainName = domainName
	domainICp.DomainICPed = domainICPed

	c.JSON(http.StatusOK, domainICp)

}
