package domain

import (
	"bytes"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/golang/mock/gomock"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
	testtineservice "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

// TestDomainHandler_CreateDomain 函数用于测试 DomainHandler 结构体的 CreateDomain 方法
func TestDomainHandler_CreateDomain(t *testing.T) {
	type fields struct {
		client        clientset.ClientSetInterface
		domainService service.DomainServiceInterface
	}
	type args struct {
		c *gin.Context
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name: "domain exist",
			fields: func() fields {
				client := &testingclientset.ClientSet{}
				domainService := testtineservice.NewMockDomainServiceInterface(gomock.NewController(t))
				domainService.EXPECT().CheckDomainIcp(gomock.Any(), gomock.Any()).AnyTimes().Return(true, nil)
				return fields{
					client:        client,
					domainService: domainService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

				req := model.DomainRequest{
					DomainName: "www.baidu.com",
				}
				data, err := json.Marshal(req)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				ctx.Request, _ = http.NewRequest(http.MethodPost, "/", bytes.NewReader(data))
				ctx.Request.Header.Add("Content-Type", "application/json")
				ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec: v1alpha1.CNCNetworkSpec{
						CustomDomains: []v1alpha1.CustomDomain{
							{
								DomainName: "www.baidu.com",
								CertID:     "cert-id",
								CertName:   "cert-name",
							},
						},
					},
				},
				)

				return args{
					c: ctx,
				}
			}(),
			code: 409,
		},
		{
			name: "success",
			fields: func() fields {
				client := &testingclientset.ClientSet{}
				domainService := testtineservice.NewMockDomainServiceInterface(gomock.NewController(t))
				domainService.EXPECT().CheckDomainIcp(gomock.Any(), gomock.Any()).AnyTimes().Return(true, nil)
				return fields{
					client:        client,
					domainService: domainService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				req := model.DomainRequest{
					DomainName: "www.aliyun.com",
					CertID:     "cert-x9tttwu7ydvz",
					CertName:   "ccr-cnc-baidubce.com",
				}
				data, err := json.Marshal(req)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				ctx.Request, _ = http.NewRequest(http.MethodPost, "/", bytes.NewReader(data))
				ctx.Request.Header.Add("Content-Type", "application/json")
				ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CNCNetworkSpec{},
				},
				)
				ctx.Set(middleware.INSTANCE_OBJECT_IDENTITY, &v1alpha1.CNCNetwork{})

				return args{
					c: ctx,
				}
			}(),
			code: http.StatusOK,
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {
			h := DomainHandler{
				clients:       tt.fields.client,
				domainService: tt.fields.domainService,
			}
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)
			mock.OnAnything(h.clients, "K8sClient").Return(fake.NewClientBuilder().WithScheme(scheme).
				WithRuntimeObjects(&v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CNCNetworkSpec{},
				}).Build())

			h.CreateDomain(tt.args.c)
			if tt.args.c.Writer.Status() != tt.code {
				t.Errorf("DomainHandler.CreateDomain() = %d, want %v", tt.args.c.Writer.Status(), tt.code)
			}

		})
	}
}

// TestDomainHandler_DeleteDomain 单元测试函数
func TestDomainHandler_UpdateDomain(t *testing.T) {
	type fields struct {
		client clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name: "domain is empty",
			fields: fields{
				client: &testingclientset.ClientSet{},
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

				req := model.DomainRequest{
					DomainName: "",
				}
				data, err := json.Marshal(req)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				ctx.Request, _ = http.NewRequest(http.MethodPut, "", bytes.NewReader(data))
				ctx.Params = append(ctx.Params, gin.Param{Key: "domainName", Value: ""})
				ctx.Request.Header.Add("Content-Type", "application/json")
				ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CNCNetworkSpec{},
				},
				)

				return args{
					c: ctx,
				}
			}(),
			code: http.StatusBadRequest,
		},
		{
			name: "success",
			fields: fields{
				client: &testingclientset.ClientSet{},
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				req := model.DomainRequest{
					DomainName: "www.baidu.com",
					CertID:     "cert-x9tttwu7ydvz",
					CertName:   "ccr-cnc-baidubce.com",
				}
				data, err := json.Marshal(req)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				ctx.Request, _ = http.NewRequest(http.MethodPut, "", bytes.NewReader(data))
				ctx.Params = append(ctx.Params, gin.Param{Key: "domainName", Value: "www.baidu.com"})
				ctx.Request.Header.Add("Content-Type", "application/json")
				ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec: v1alpha1.CNCNetworkSpec{
						CustomDomains: []v1alpha1.CustomDomain{
							{
								DomainName: "www.baidu.com",
								CertID:     "cert-id",
								CertName:   "cert-name",
							},
						},
					},
				},
				)
				ctx.Set(middleware.INSTANCE_OBJECT_IDENTITY, &v1alpha1.CNCNetwork{})

				return args{
					c: ctx,
				}
			}(),
			code: http.StatusOK,
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {
			h := DomainHandler{
				clients: tt.fields.client,
			}
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)
			mock.OnAnything(h.clients, "K8sClient").Return(fake.NewClientBuilder().WithScheme(scheme).
				WithRuntimeObjects(&v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CNCNetworkSpec{},
				}).Build())

			h.UpdateDomain(tt.args.c)
			if tt.args.c.Writer.Status() != tt.code {
				t.Errorf("DomainHandler.UpdateDomain() = %d, want %v", tt.args.c.Writer.Status(), tt.code)
			}

		})
	}
}

// TestDomainHandler_DeleteDomain 单元测试函数
func TestDomainHandler_DeleteDomain(t *testing.T) {
	type fields struct {
		client clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name: "domain is empty",
			fields: fields{
				client: &testingclientset.ClientSet{},
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

				ctx.Params = append(ctx.Params, gin.Param{Key: "domainName", Value: ""})
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "/", nil)
				ctx.Request.Header.Add("Content-Type", "application/json")
				ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CNCNetworkSpec{},
				},
				)

				return args{
					c: ctx,
				}
			}(),
			code: http.StatusBadRequest,
		},
		{
			name: "success",
			fields: fields{
				client: &testingclientset.ClientSet{},
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

				ctx.Params = append(ctx.Params, gin.Param{Key: "domainName", Value: "www.baidu.com"})
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "/", nil)
				ctx.Request.Header.Add("Content-Type", "application/json")
				ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec: v1alpha1.CNCNetworkSpec{
						CustomDomains: []v1alpha1.CustomDomain{
							{
								DomainName: "www.baidu.com",
								CertID:     "cert-id",
								CertName:   "cert-name",
							},
						},
					},
				},
				)
				ctx.Set(middleware.INSTANCE_OBJECT_IDENTITY, &v1alpha1.CNCNetwork{})

				return args{
					c: ctx,
				}
			}(),
			code: http.StatusOK,
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {
			h := DomainHandler{
				clients: tt.fields.client,
			}
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)
			mock.OnAnything(h.clients, "K8sClient").Return(fake.NewClientBuilder().WithScheme(scheme).
				WithRuntimeObjects(&v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CNCNetworkSpec{},
				}).Build())

			h.DeleteDomain(tt.args.c)
			if tt.args.c.Writer.Status() != tt.code {
				t.Errorf("DomainHandler.DeleteDomain() = %d, want %v", tt.args.c.Writer.Status(), tt.code)
			}

		})
	}
}

// TestDomainHandler_ListDomain 单元测试函数
func TestDomainHandler_ListDomain(t *testing.T) {
	type fields struct {
		client clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name: "success",
			fields: fields{
				client: &testingclientset.ClientSet{},
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

				ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
				ctx.Request.Header.Add("Content-Type", "application/json")
				ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec: v1alpha1.CNCNetworkSpec{
						CustomDomains: []v1alpha1.CustomDomain{
							{
								DomainName: "www.baidu.com",
								CertID:     "cert-id",
								CertName:   "cert-name",
							},
							{
								DomainName: "www.aliyun.com",
								CertID:     "cert-id2",
								CertName:   "cert-name2",
							},
						},
					},
					Status: v1alpha1.CNCNetworkStatus{
						CustomDomainStatus: []v1alpha1.CustomDomainStatus{
							{
								DomainName:       "www.baidu.com",
								DomainStatus:     "ready",
								DomainStatusDesc: "正常",
								CertID:           "cert-id",
								CertName:         "cert-name",
								DomainICPed:      true,
								CertExpireTime:   &v1.Time{Time: time.Now()},
							},
							{
								DomainName:       "www.aliyun.com",
								DomainICPed:      true,
								DomainStatus:     "notready",
								DomainStatusDesc: "异常",
								CertID:           "cert-id2",
								CertName:         "cert-name2",
								CertExpireTime:   &v1.Time{Time: time.Now()},
							},
						},
					},
				},
				)
				ctx.Set(middleware.INSTANCE_OBJECT_IDENTITY, &v1alpha1.CNCNetwork{})

				return args{
					c: ctx,
				}
			}(),
			code: http.StatusOK,
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {
			h := DomainHandler{
				clients: tt.fields.client,
			}
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)
			mock.OnAnything(h.clients, "K8sClient").Return(fake.NewClientBuilder().WithScheme(scheme).
				WithRuntimeObjects(&v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CNCNetworkSpec{},
				}).Build())

			h.ListDomain(tt.args.c)
			if tt.args.c.Writer.Status() != tt.code {
				t.Errorf("DomainHandler.ListDomain() = %d, want %v", tt.args.c.Writer.Status(), tt.code)
			}

		})
	}
}

// TestDomainHandler_GetDomain 单元测试函数
func TestDomainHandler_GetDomain(t *testing.T) {
	type fields struct {
		client clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name: "success",
			fields: fields{
				client: &testingclientset.ClientSet{},
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "domainName", Value: "www.baidu.com"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
				ctx.Request.Header.Add("Content-Type", "application/json")
				ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec: v1alpha1.CNCNetworkSpec{
						CustomDomains: []v1alpha1.CustomDomain{
							{
								DomainName: "www.baidu.com",
								CertID:     "cert-id",
								CertName:   "cert-name",
							},
							{
								DomainName: "www.aliyun.com",
								CertID:     "cert-id2",
								CertName:   "cert-name2",
							},
						},
					},
					Status: v1alpha1.CNCNetworkStatus{
						CustomDomainStatus: []v1alpha1.CustomDomainStatus{
							{
								DomainName:       "www.baidu.com",
								DomainICPed:      true,
								DomainStatus:     "ready",
								DomainStatusDesc: "正常",
								CertID:           "cert-id",
								CertName:         "cert-name",
								CertExpireTime:   &v1.Time{Time: time.Now()},
							},
							{
								DomainName:       "www.aliyun.com",
								DomainICPed:      true,
								DomainStatus:     "notready",
								DomainStatusDesc: "异常",
								CertID:           "cert-id2",
								CertName:         "cert-name2",
								CertExpireTime:   &v1.Time{Time: time.Now()},
							},
						},
					},
				},
				)
				ctx.Set(middleware.INSTANCE_OBJECT_IDENTITY, &v1alpha1.CNCNetwork{})

				return args{
					c: ctx,
				}
			}(),
			code: http.StatusOK,
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {
			h := DomainHandler{
				clients: tt.fields.client,
			}
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)
			mock.OnAnything(h.clients, "K8sClient").Return(fake.NewClientBuilder().WithScheme(scheme).
				WithRuntimeObjects(&v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CNCNetworkSpec{},
				}).Build())

			h.GetDomain(tt.args.c)
			if tt.args.c.Writer.Status() != tt.code {
				t.Errorf("DomainHandler.GetDomain() = %d, want %v", tt.args.c.Writer.Status(), tt.code)
			}

		})
	}
}

// TestDomainHandler_CheckDomainICP 单元测试函数
func TestDomainHandler_CheckDomainICP(t *testing.T) {
	type fields struct {
		client        clientset.ClientSetInterface
		domainService service.DomainServiceInterface
	}
	type args struct {
		c *gin.Context
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name: "domain empty",
			fields: func() fields {
				client := &testingclientset.ClientSet{}
				domainService := testtineservice.NewMockDomainServiceInterface(gomock.NewController(t))
				domainService.EXPECT().CheckDomainIcp(gomock.Any(), gomock.Any()).AnyTimes().Return(true, nil)
				return fields{
					client:        client,
					domainService: domainService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "domainName", Value: ""})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
				ctx.Request.Header.Add("Content-Type", "application/json")

				return args{
					c: ctx,
				}
			}(),
			code: http.StatusBadRequest,
		},
		{
			name: "success",
			fields: func() fields {
				client := &testingclientset.ClientSet{}
				domainService := testtineservice.NewMockDomainServiceInterface(gomock.NewController(t))
				domainService.EXPECT().CheckDomainIcp(gomock.Any(), gomock.Any()).AnyTimes().Return(true, nil)
				return fields{
					client:        client,
					domainService: domainService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "domainName", Value: "www.baidu.com"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)

				return args{
					c: ctx,
				}
			}(),
			code: http.StatusOK,
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {
			h := DomainHandler{
				clients:       tt.fields.client,
				domainService: tt.fields.domainService,
			}
			h.CheckDomainICP(tt.args.c)
			if tt.args.c.Writer.Status() != tt.code {
				t.Errorf("DomainHandler.CheckDomainICP() = %d, want %v", tt.args.c.Writer.Status(), tt.code)
			}

		})
	}
}
