package chart

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-openapi/runtime"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/chart/client/chart_repository"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/chart"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler"
)

const (
	formFieldNameForChart = "chart"
	formFiledNameForProv  = "prov"
	headerContentType     = "Content-Type"
	contentTypeMultipart  = "multipart/form-data"
)

// formFile is used to represent the uploaded files in the form
type formFile struct {
	// form field key contains the form file
	formField string

	// flag to indicate if the file identified by the 'formField'
	// must exist
	mustHave bool
}

type ChartHandler struct {
	ClientSet    clientset.ClientSetInterface
	chartService *chart.ChartService
}

func NewChartHandler(clientset clientset.ClientSetInterface) *ChartHandler {
	return &ChartHandler{
		ClientSet:    clientset,
		chartService: chart.NewChartService(),
	}
}

// PostCharts 上传helm chart 文件
// @Summary 上传helm chart 文件
// @Description 上传helm chart 文件
// @Tags chart
// @Accept multipart/form-data
// @Produce multipart/form-data
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param chart formData file true "The chart file"
// @Param prov formData file false "The prov file"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/charts [post]
func (h *ChartHandler) PostCharts(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	projectName := c.Param("projectName")

	// TODO 优化为使用proxy
	formFiles := make([]formFile, 0)
	formFiles = append(formFiles,
		formFile{
			formField: formFieldNameForChart,
			mustHave:  true,
		},
		formFile{
			formField: formFiledNameForProv,
		})

	readers := make([]runtime.NamedReadCloser, 0)
	// Process files by key one by one
	for _, f := range formFiles {
		mFileHeader, err := c.FormFile(f.formField)
		if err != nil {
			formattedErr := fmt.Sprintf("Get file content with multipart header from key '%s' failed with error: %s", f.formField, err.Error())
			if f.mustHave || err != http.ErrMissingFile {
				logger.Errorf("form chart file failed: %s", err)
				return handler.NewResponse(http.StatusBadRequest, formattedErr, nil, c)
			}
			// Error can be ignored, just log it
			logger.Warning(formattedErr)
			continue
		}

		mFile, err := mFileHeader.Open()
		if err != nil {
			logger.Errorf("open chart file failed: %s", err)
			return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
		}
		reader := runtime.NamedReader(f.formField, mFile)
		readers = append(readers, reader)
	}

	if err := h.chartService.PostCharts(c, projectName, readers); err != nil {
		logger.Errorf("post charts failed: %s", err)
		return HandleChartSwaggerErrors(c, err)
	}
	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// ListCharts 查询 helm chart 列表
// @Summary 查询 helm chart 列表
// @Description 查询 helm chart 列表
// @Tags chart
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param chartName query string false "helm chart名称"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListChartResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/charts [get]
func (h *ChartHandler) ListCharts(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, chartName := c.Param("projectName"), c.Query("chartName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return handler.NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return handler.NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	cr, total, err := h.chartService.ListCharts(c, projectName, chartName, pageNo, pageSize, false)
	if err != nil {
		logger.Errorf("list charts failed: %s", err)
		return HandleChartSwaggerErrors(c, err)
	}
	lcr := &model.ListChartResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: cr,
	}
	return handler.NewResponse(http.StatusOK, "success", lcr, c)
}

// ListChartVersions 查询chart version 列表
// @Summary 查询chart version 列表
// @Description 查询chart version 列表
// @Tags chart
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param chartName path string true "helm chart名称"
// @Param chartVersion query string false "helm chart版本"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListChartVersionResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions [get]
func (h *ChartHandler) ListChartVersions(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, chartName, chartVersion := c.Param("projectName"), c.Param("chartName"), c.Query("chartVersion")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return handler.NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}

	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return handler.NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	cvs, total, err := h.chartService.ListChartVersion(c, projectName, chartName, chartVersion, pageNo, pageSize, false)
	if err != nil {
		logger.Errorf("get chart failed: %s", err)
		return HandleChartSwaggerErrors(c, err)
	}
	lcvr := &model.ListChartVersionResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: cvs,
	}
	return handler.NewResponse(http.StatusOK, "success", lcvr, c)
}

// DeleteChart 删除 helm chart
// @Summary 删除 helm chart
// @Description 删除 helm chart
// @Tags chart
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param chartName path string true "helm chart名称"
// @Success 200 {object} model.ChartVersions "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/charts/{chartName} [delete]
func (h *ChartHandler) DeleteChart(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, chartName := c.Param("projectName"), c.Param("chartName")

	if err := h.chartService.DeleteChart(c, projectName, chartName, false); err != nil {
		logger.Errorf("delete chart failed: %s", err)
		return HandleChartSwaggerErrors(c, err)
	}

	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// BatchDeleteChart 批量删除helm chart
// @Summary 批量删除helm chart
// @Description 批量删除helm chart
// @Tags chart
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param body body model.BatchDeleteRequest true "helm chart名称数组"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/charts [delete]
func (h *ChartHandler) BatchDeleteChart(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	projectName := c.Param("projectName")

	var deleteRequest model.BatchDeleteRequest
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("bind chart list item failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "bind delete chart item failed", nil, c)
	}

	if err := h.chartService.BatchDeleteChart(c, projectName, deleteRequest.Items, false); err != nil {
		logger.Errorf("batch delete chart failed: %s", err)
		return HandleChartSwaggerErrors(c, err)
	}

	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// DeleteChartVersion 删除helm chart版本
// @Summary 删除helm chart版本
// @Description 删除helm chart版本
// @Tags chart
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param chartName path string true "helm chart名称"
// @Param chartVersion path string true "helm chart版本"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions/{chartVersion} [delete]
func (h *ChartHandler) DeleteChartVersion(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, chartName, chartVersion := c.Param("projectName"), c.Param("chartName"), c.Param("chartVersion")

	if err := h.chartService.DeleteChartVersion(c, projectName, chartName, chartVersion, false); err != nil {
		logger.Errorf("delete helm chart version failed: %s", err)
		return HandleChartSwaggerErrors(c, err)
	}

	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// BatchDeleteChartVersion 批量删除helm chart版本
// @Summary 批量删除helm chart版本
// @Description 批量删除helm chart版本
// @Tags chart
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param chartName path string true "helm chart 名称"
// @Param body body model.BatchDeleteRequest true "helm chart版本名称数组"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions [delete]
func (h *ChartHandler) BatchDeleteChartVersion(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, chartName := c.Param("projectName"), c.Param("chartName")

	var deleteRequest model.BatchDeleteRequest
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("bind chart version list item failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "bind delete chart version item failed", nil, c)
	}

	if err := h.chartService.BatchDeleteChartVersion(c, projectName, chartName, deleteRequest.Items, false); err != nil {
		logger.Errorf("batch delete chart version failed: %s", err)
		return HandleChartSwaggerErrors(c, err)
	}

	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// HandleChartSwaggerErrors takes a swagger generated error as input,
// which usually does not contain any form of error message,
// and outputs a new error with a proper message.
func HandleChartSwaggerErrors(c *gin.Context, in error) *handler.Response {
	t, ok := in.(*runtime.APIError)
	if ok {
		switch t.Code {
		case http.StatusBadRequest:
			return handler.NewResponse(http.StatusBadRequest, "invalid request", nil, c)
		case http.StatusUnauthorized:
			return handler.NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
		case http.StatusForbidden:
			return handler.NewResponse(http.StatusForbidden, "user does not have permission to the chart", nil, c)
		case http.StatusNotFound:
			return handler.NewResponse(http.StatusNotFound, "chart not found", nil, c)
		case http.StatusConflict:
			return handler.NewResponse(http.StatusConflict, "chart name already exists", nil, c)
		case http.StatusPreconditionFailed:
			return handler.NewResponse(http.StatusPreconditionFailed, "chart precondition failed", nil, c)
		case http.StatusInternalServerError:
			return handler.NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		default:
			return handler.NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}

	switch in.(type) {
	case *chart_repository.DeleteChartrepoRepoChartsNameVersionNotFound:
		return handler.NewResponse(http.StatusNotFound, "chart not found", nil, c)
	default:
		return handler.NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
	}
}
