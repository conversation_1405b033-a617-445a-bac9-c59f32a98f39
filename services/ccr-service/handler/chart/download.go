package chart

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	lister "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type DownloadHandler struct {
	ip     string
	lister lister.ListerInterface
}

func NewDownloadHandler(ip string, lister lister.ListerInterface) *DownloadHandler {
	return &DownloadHandler{
		ip:     ip,
		lister: lister,
	}
}

// DownloadChart 下载 helm chart
// @Summary 下载 helm chart
// @Description 下载 helm chart
// @Tags chart
// @Accept application/json, text/plain, */*
// @Produce application/x-tar
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param filename path string true "helm chart 文件名"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/charts/download/{filename} [get]
func (d *DownloadHandler) DownloadChart(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	requestId := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId, projectName, filename := c.Param("instanceId"), c.Param("projectName"), c.Param("filename")

	// 查询实例缓存
	instanceInfo, err := d.lister.GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestId))
		return
	}

	targetUrl, err := url.Parse(fmt.Sprintf("https://%s/chartrepo/%s/charts/%s", d.ip, projectName, filename))
	if err != nil {
		logger.Errorf("parse url failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestId))
		return
	}

	director := func(req *http.Request) {
		req.URL = targetUrl
		req.Host = instanceInfo.ClusterURL
		req.SetBasicAuth("admin", instanceInfo.Password)

		if _, ok := req.Header["User-Agent"]; !ok {
			// explicitly disable User-Agent so it's not set to default value
			req.Header.Set("User-Agent", "")
		}
	}

	proxy := &httputil.ReverseProxy{
		Director: director,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	proxy.ServeHTTP(c.Writer, c.Request)

	if c.Writer.Status() == 200 {
		c.Writer.Header().Add("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	}
}
