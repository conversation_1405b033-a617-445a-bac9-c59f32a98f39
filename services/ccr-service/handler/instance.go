package handler

import (
	"errors"
	"net/http"
	"strings"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/gin-gonic/gin"
	"gopkg.in/yaml.v3"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/resourcegroup"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	ccrerr "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/errors"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/product"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type Instance struct {
	clients clientset.ClientSetInterface

	region string

	endpoints map[string]string

	instanceService service.InstanceServiceInterface
}

func NewInstance(clis clientset.ClientSetInterface, region string, endpoints map[string]string) *Instance {
	return &Instance{
		clients:         clis,
		region:          region,
		endpoints:       endpoints,
		instanceService: service.NewInstanceService(clis, region, endpoints),
	}
}

// Create 创建CCR实例
// @Summary 创建CCR实例
// @Description 创建CCR实例
// @Tags instance
// @Accept application/json
// @Produce application/json
// @Param body body model.CreateInstanceRequest true "创建实例参数"
// @Success 201 {object} model.CreateInstanceResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances [post]
func (cls *Instance) Create(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var req model.CreateInstanceRequest
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	accountId, userId := middleware.AccountIDFromContext(c), middleware.UserIDFromContext(c)

	// TODO ID生成需要完善
	instanceId, err := cls.instanceService.GenerateInstanceId(c)
	if err != nil {
		logger.Errorf("generate instance ID failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	if err := cls.instanceService.CheckInstanceQuota(c, accountId); err != nil {
		var ccrError *ccrerr.CcrError
		if errors.As(err, &ccrError) {
			logger.Errorf("quota has exceed: %s", err)
			gin_context.E(c, gin_context.QuotaExceedError(requestID))
			return
		}
		logger.Errorf("generate instance ID failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	var canary bool
	if cls.region == common.RegionGZTEST {
		canary = true
	}
	tags := make([]billing.Tag, 0)
	for _, tag := range req.Tags {
		if len(tag.TagKey) == 0 {
			continue
		}
		tags = append(tags, billing.Tag{TagKey: tag.TagKey, TagValue: tag.TagValue})
	}

	groupIDs := make([]string, 0)
	for _, groupID := range req.GroupIDs {
		groupIDs = append(groupIDs, groupID)
	}
	extra := &billing.Extra{
		HistoryOrderImport: false,
		Canary:             canary,
		AutoRenew:          req.Billing.AutoRenew,
		AutoRenewTimeUnit:  strings.ToLower(req.Billing.AutoRenewTimeUnit),
		AutoRenewTime:      req.Billing.AutoRenewTime,
		InstanceId:         instanceId,
		InstanceName:       req.Name,
		InstanceType:       req.Type,
		Tags:               tags,
		GroupIDs:           groupIDs,
	}

	content, err := yaml.Marshal(extra)
	if err != nil {
		logger.Errorf("yaml marshal extra failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	orderId, err := cls.instanceService.CreateNewFacadeOrder(c, accountId, userId, instanceId, req.Type, string(content), req.Billing.ReservationTimeUnit,
		req.Billing.ReservationTime, req.PaymentMethod)
	if err != nil {
		logger.Errorf("create new facade order failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusAccepted, &model.CreateInstanceResponse{OrderID: orderId})
}

// Upgrade 升级CCR实例
// @Summary 升级CCR实例
// @Description 升级CCR实例
// @Tags instance
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.UpgradeInstanceRequest true "升级配置"
// @Success 200 {string} string "Success"
// @Failure 501 {object} bce.BceServiceError
// @Router /instances/{instanceId}/upgrade [put]
func (cls *Instance) Upgrade(c *gin.Context) {
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	logger := gin_context.LoggerFromContext(c)

	accountId, userId := middleware.AccountIDFromContext(c), middleware.UserIDFromContext(c)

	logger.Infof("[upgradeInstance] accountId: %s, userId: %s", accountId, userId)

	var canary bool
	if cls.region == common.RegionGZTEST {
		canary = true
		cls.region = common.RegionGZ
	}

	var upgradeReq model.UpgradeInstanceRequest
	if err := c.Bind(&upgradeReq); err != nil {
		logger.Errorf("bind upgrade request failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	instance := middleware.InstanceFromContext(c)

	if !cls.instanceService.ValidType(string(instance.Spec.Type), upgradeReq.Type) {
		logger.Errorf("does not support to upgrade to %s from %s", upgradeReq.Type, string(instance.Spec.Type))
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	resource, err := cls.instanceService.GetResourceDetail(c, accountId, instance.GetName())
	if err != nil {
		logger.Errorf("[upgradeinstance] get resource detail failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	var resourceExtra *billing.Extra
	if err := yaml.Unmarshal([]byte(resource.Extra), &resourceExtra); err != nil {
		logger.Errorf("[upgradeinstance] unmarshal resource extra failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	extra := &billing.Extra{
		HistoryOrderImport: false,
		Canary:             canary,
		AutoRenew:          resourceExtra.AutoRenew,
		AutoRenewTimeUnit:  resourceExtra.AutoRenewTimeUnit,
		AutoRenewTime:      resourceExtra.AutoRenewTime,
		InstanceId:         instance.GetName(),
		InstanceName:       instance.GetLabels()["name"],
		InstanceType:       upgradeReq.Type,
	}

	content, err := yaml.Marshal(extra)
	if err != nil {
		logger.Errorf("yaml marshal extra failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	orderId, err := cls.instanceService.CreateResizeFacadeOrder(c, accountId, userId, resource.Uuid, upgradeReq.Type, string(content), upgradeReq.PaymentMethod)
	if err != nil {
		logger.Errorf("[upgradeInstance] create resize facade order failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, &model.UpgradeInstanceResponse{OrderID: orderId})
}

// Renew order confirmation interface

// Renew 续费CCR实例
// @Summary 续费CCR实例
// @Description 续费CCR实例
// @Tags instance
// @Accept application/json
// @Produce application/json
// @Param body body model.ConfirmOrderRequest true "续费实例参数"
// @Success 200 {object} model.Result "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/renew [post]
func (cls *Instance) Renew(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestId := gin_context.RequestIdFromContext(c)

	orderType := c.Query("orderType")
	if orderType != billing.OrderTypeRENEW {
		logger.Errorf("[RenewInstance] orderType: %s is not equal RENEW", orderType)
		gin_context.E(c, gin_context.BadRequestError(requestId))
		return
	}

	var req *model.ConfirmOrderRequest
	if err := c.Bind(&req); err != nil {
		logger.Errorf("[RenewInstance] bind request body failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestId))
		return
	}

	orderId, err := cls.instanceService.CreateRenewFacadeOrder(c, req)
	if err != nil {

		var bceError *bce.BceServiceError
		logger.Errorf("[RenewInstance] create renew facade order failed: %s", err)
		if errors.As(err, &bceError) && (bceError.StatusCode == http.StatusConflict || bceError.Code == "OrderExceptions.ResourceInTaskException") {
			gin_context.E(c, gin_context.ConflictError(requestId, bceError.Message))
			return
		}

		gin_context.E(c, gin_context.InternalServerError(requestId))
		return
	}

	c.JSON(http.StatusOK, model.Result{OrderId: orderId})
}

// Delete 删除CCR实例
// @Summary 删除CCR实例
// @Description 删除CCR实例
// @Tags instance
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Success 204 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId} [delete]
func (cls *Instance) Delete(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	ccrObj := middleware.InstanceFromContext(c)

	err := cls.clients.K8sClient().Delete(c, ccrObj)

	if err != nil {
		logger.Error("failed to delete instance", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// Get 获取CCR实例
// @Summary 获取CCR实例
// @Description 获取CCR实例
// @Tags instance
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例id"
// @Success 200 {object} model.InstanceDetail "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId} [get]
func (cls *Instance) Get(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	instanceID := c.Param("instanceId")

	ccrObj := middleware.InstanceFromContext(c)

	if ccrObj.Status.Phase != v1alpha1.CCRRunning {
		logger.Errorf("instance is not ready for access")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	var ccrQuota v1alpha1.CCRQuota
	err := cls.clients.K8sClient().Get(c, client.ObjectKey{Namespace: instanceID, Name: instanceID}, &ccrQuota)
	if err != nil {
		logger.Errorf("get ccrquota failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	harborCli, err := cls.clients.HarborClient(instanceID)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	statistic, err := harborCli.AddonClient.Product.GetProductsStatistic(
		product.NewGetProductsStatisticParams().WithXRequestID(&requestID),
		harborCli.AuthInfo,
	)

	if err != nil {
		logger.Errorf("get statistic info from harbor failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	instanceInfo := service.InstanceInfoFromObject(ccrObj)

	tagResourceMap, err := cls.instanceService.GetResourceTagMap(c, []string{instanceID})
	if err != nil {
		logger.Errorf("get instance tag failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	if tags, ok := tagResourceMap[instanceID]; ok {
		instanceInfo.Tags = tags
	}

	resGroupsMap, err := cls.instanceService.GetResourceGroupMap(c, []string{instanceID})
	if err != nil {
		logger.Errorf("get instance groups map failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	if resourcegroup, ok := resGroupsMap[instanceID]; ok {
		instanceInfo.GroupIDs = resourcegroup
	}

	var networkObj v1alpha1.CNCNetwork
	err = cls.clients.K8sClient().Get(c, client.ObjectKey{Namespace: instanceID, Name: instanceID}, &networkObj)
	if err == nil {
		// set customDomain from cncnetwork
		var customDomains []string
		if networkObj.Spec.CustomDomains != nil {
			for _, domain := range networkObj.Spec.CustomDomains {
				customDomains = append(customDomains, domain.DomainName)
			}
		}
		instanceInfo.CustomDomains = customDomains
	}

	instanceDetail := model.InstanceDetail{
		Info: instanceInfo,
		Statistic: model.InstanceStatistic{
			Repo:      statistic.GetPayload().TotalRepoCount,
			Namespace: statistic.GetPayload().TotalProjectCount,
			Chart:     statistic.GetPayload().TotalChartCount,
			Storage:   statistic.GetPayload().TotalStorage,
		},
		Bucket: ccrObj.Status.Bucket,
		Region: cls.region,
	}

	if ccrQuota.Status.ChartRepo != nil {
		instanceDetail.Quota.Chart = ccrQuota.Status.ChartRepo.Value()
	}

	if ccrQuota.Status.ImageRepo != nil {
		instanceDetail.Quota.Repo = ccrQuota.Status.ImageRepo.Value()
	}

	if ccrQuota.Status.Project != nil {
		instanceDetail.Quota.Namespace = ccrQuota.Status.Project.Value()
	}

	c.JSON(http.StatusOK, instanceDetail)
}

// ListForLogicTag 获取CCR实例列表
// @route /v1/tag/resources
func (cls *Instance) ListForLogicTag(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	accountID := middleware.AccountIDFromContext(c)

	var req model.ListInstanceForLogicTagRequest
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind upgrade request failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}
	logger.Infof("List instance for tag begin: %v", req)

	if req.PageNo == 0 {
		req.PageNo = 1
	}

	if req.PageSize == 0 {
		req.PageSize = 10
	}

	keywordType := ""
	if req.TagKey != "" {
		keywordType = "tag/" + req.TagKey
	}
	keyword := req.TagValue

	instances, err := cls.instanceService.ListInstanceLocal(c, accountID, keywordType, keyword)

	if err != nil {
		logger.Errorf("get instances failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	tagInstances := make([]model.TagListResult, 0)
	for _, instance := range instances {

		// TODO 后面CCR 实例这面应该做异步任务同步真实的billing侧resource状态，此处先不处理
		status := "RUNNING"
		if instance.Status == string(v1alpha1.CCRStopped) {
			status = "STOPPED"
		}

		tagInstance := model.TagListResult{
			Name:         instance.Name,
			ResourceID:   instance.ID,
			ResourceUUID: instance.ID,
			Region:       common.GetRealRegion(instance.Region),
			Status:       status,   // 需要返回billing resource的状态 RUNNING/STOPPED
			ProductType:  "prepay", // TODO 后面要支持通过billing resource 同步该状态
			CreateTime:   instance.CreateTime,
		}
		tagInstances = append(tagInstances, tagInstance)
	}

	tagInstancesResp := model.ListInstanceForLogicTagResponse{
		Page: model.TagListPage{
			Order:      req.Order,
			OrderBy:    req.OrderBy,
			PageNo:     req.PageNo,
			PageSize:   req.PageSize,
			Result:     tagInstances,
			TotalCount: len(instances),
		},
	}

	c.JSON(http.StatusOK, tagInstancesResp)
}

// ListForLogicIam 获取CCR实例列表给IAM鉴权
// @route /v1/iam/resources
func (cls *Instance) ListForLogicIam(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	accountID := middleware.AccountIDFromContext(c)

	tagKey, tagValue := c.Query("tagKey"), c.Query("tagValue")

	keywordType := ""
	if tagKey != "" {
		keywordType = "tag/" + tagKey
	}
	keyword := tagValue

	instances, err := cls.instanceService.ListInstanceLocal(c, accountID, keywordType, keyword)

	if err != nil {
		logger.Errorf("get instances failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	tagInstances := make([]*model.IamInstance, 0)
	for _, instance := range instances {
		tagInstance := &model.IamInstance{
			InstanceID:   instance.ID,
			InstanceName: instance.Name,
		}
		tagInstances = append(tagInstances, tagInstance)
	}

	tagInstancesResp := model.ListInstanceForIamResponse{
		Instances: tagInstances,
	}
	c.JSON(http.StatusOK, tagInstancesResp)
}

// List 列举CCR实例
// @Summary 列举CCR实例
// @Description 列举CCR实例
// @Tags instance
// @Accept application/json
// @Produce application/json
// @Param pageNo query integer false "页码"
// @Param pageSize query integer false "页大小"
// @Param keywordType query string false "关键字类型"
// @Param keyword query string false "关键字"
// @Param acrossregion query string false "是否在全区域内搜索"
// @Success 200 {object} model.ListInstanceResponse
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances [get]
func (cls *Instance) List(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	accountId := middleware.AccountIDFromContext(c)
	userId := middleware.UserIDFromContext(c)

	var pagedInfo model.PagedListOption
	var err error
	if err = c.BindQuery(&pagedInfo); err != nil {
		logger.Errorf("bind query failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	if pagedInfo.PageNo < 0 || pagedInfo.PageSize < 0 ||
		(pagedInfo.KeywordType != "id" && pagedInfo.KeywordType != "name" &&
			!strings.HasPrefix(pagedInfo.KeywordType, "tag/") && pagedInfo.KeywordType != "resGroupId" && pagedInfo.KeywordType != "") {
		logger.Errorf("page is not valid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	if pagedInfo.PageNo == 0 {
		pagedInfo.PageNo = 1
	}

	if pagedInfo.PageSize == 0 {
		pagedInfo.PageSize = 10
	}

	if pagedInfo.KeywordType == "" {
		pagedInfo.Keyword = ""
	}

	acrossRegion := c.DefaultQuery("acrossregion", "false")

	var instances []*model.InstanceInfo
	if acrossRegion == "false" {
		instances, err = cls.instanceService.ListInstanceLocal(c, accountId, pagedInfo.KeywordType, pagedInfo.Keyword)
	} else {
		instances, err = cls.instanceService.ListInstances(c, accountId, userId, pagedInfo.KeywordType, pagedInfo.Keyword)
	}

	if err != nil {
		logger.Errorf("get instances failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	listResp := &model.ListInstanceResponse{
		PageInfo: model.PageInfo{
			Total:    len(instances),
			PageNo:   pagedInfo.PageNo,
			PageSize: pagedInfo.PageSize,
		},
	}

	// return all records
	if acrossRegion == "true" {
		listResp.Instances = instances
		listResp.PageSize = len(instances)
		listResp.PageNo = 1
		c.JSON(http.StatusOK, listResp)
		return
	}

	// paged
	startPos := (pagedInfo.PageNo - 1) * pagedInfo.PageSize

	if startPos > len(instances) {
		listResp.Instances = []*model.InstanceInfo{}
	}

	if startPos <= len(instances) && startPos+pagedInfo.PageSize > len(instances) {
		listResp.Instances = instances[startPos:]
	}

	if startPos+pagedInfo.PageSize <= len(instances) {
		listResp.Instances = instances[startPos : startPos+pagedInfo.PageSize]
	}

	c.JSON(http.StatusOK, listResp)
}

// Update 更改CCR实例信息
// @Summary 更改CCR实例信息
// @Description 更改CCR实例信息
// @Tags instance
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.UpdateInstanceRequest true "更新请求"
// @Success 200 {object} model.InstanceInfo "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId} [put]
func (cls *Instance) Update(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	var req model.UpdateInstanceRequest
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	ccrObj := middleware.InstanceFromContext(c)

	updateObj := ccrObj.DeepCopy()
	updateObj.GetLabels()["name"] = req.Name

	err := cls.clients.K8sClient().Patch(c, updateObj, client.MergeFrom(ccrObj))
	if err != nil {
		logger.Errorf("patch to name failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, service.InstanceInfoFromObject(updateObj))
}

// AssignTag 更改CCR实例tag
// @Summary 更改CCR实例tag
// @Description 更改CCR实例tag
// @Tags instance
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.AssignTagsRequest true "更新tag请求"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/tags [put]
func (cls *Instance) AssignTag(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	instanceID := c.Param("instanceId")

	var req model.AssignTagsRequest
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	accountID := middleware.AccountIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	tags := make([]logictag.Tag, 0)
	for _, tag := range req.Tags {
		if len(tag.TagKey) == 0 {
			continue
		}
		tags = append(tags, logictag.Tag{TagKey: tag.TagKey, TagValue: tag.TagValue})
	}
	logger.Infof("logic tag: %v", tags)

	tagClient, err := cls.clients.LogicTagClientForAccount(accountID, userID)
	if err != nil {
		logger.Errorf("new logic tag client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	if err := tagClient.CreateAndAssignTag(requestID, &logictag.CreateAndAssignArgs{
		Resources: []logictag.ResourceWithTag{
			{
				ServiceType:     "CCR",
				ResourceID:      instanceID,
				ResourceUUID:    instanceID,
				AssociationType: "floating",
				Region:          common.GetRealRegion(cls.region),
				Tags:            tags,
			},
		},
	}); err != nil {
		logger.Errorf("create and assign tag failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, nil)
}

// BindResGroup 将指定实例绑定到资源组
func (cls *Instance) BindResGroup(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	instanceID := c.Param("instanceId")
	var req model.BindResGroupRequest

	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	accountID := middleware.AccountIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	groupIDs := make([]string, 0)
	for _, groupID := range req.GroupIDs {
		if len(groupID) == 0 {
			continue
		}
		groupIDs = append(groupIDs, groupID)
	}
	logger.Infof("group ids: %v", groupIDs)
	resGroupClient, err := cls.clients.ResourceGroupClientForAccount(accountID, userID)
	if err != nil {
		logger.Errorf("new resource group client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	for _, groupID := range groupIDs {
		if _, err = resGroupClient.BindResourceToGroup(requestID, true, &resourcegroup.BindResourceToGroupArgs{
			Bindings: []resourcegroup.Binding{
				{
					ResourceID:     instanceID,
					ResourceType:   "CCR",
					ResourceRegion: common.GetRealRegion(cls.region),
					GroupID:        groupID,
				},
			},
		}); err != nil {
			logger.Errorf("bind resource to group failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}
	}

	c.JSON(http.StatusOK, nil)
}
