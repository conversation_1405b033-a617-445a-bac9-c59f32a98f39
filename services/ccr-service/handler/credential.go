package handler

import (
	"errors"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type CredentialHandler struct {
	client                clientset.ClientSetInterface
	maxTemporyTokenNumber int
}

func NewCredentialHandler(cli clientset.ClientSetInterface) *CredentialHandler {
	return &CredentialHandler{
		client:                cli,
		maxTemporyTokenNumber: 1000,
	}
}

// RestPassword 重置密码
// @Summary 重置密码
// @Description 重置密码
// @Tags credential
// @Accept application/json
// @Produce application/json
// @Param id path string true "实例ID"
// @Param userId query string false "用户id"
// @Param body body model.ResetPasswordArgs true "新密码"
// @Success 200 {string} string "Success"
// @Failure default {object} bce.BceServiceError
// @Router /instances/{id}/credential [put]
func (u *CredentialHandler) RestPassword(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	var req model.ResetPasswordArgs
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	accountId, userId, username := middleware.AccountIDFromContext(c), middleware.UserIDFromContext(c), middleware.UserNameFromContext(c)

	encryptedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("generate password hash failed")
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	now := time.Now().UTC()
	requiredToken := models.UserToken{
		CreatedAt:    now,
		UpdatedAt:    now,
		ExpiredAt:    time.Unix(1, 0),
		AccountID:    accountId,
		UserID:       userId,
		InstanceID:   instanceId,
		Username:     username,
		PasswordHash: string(encryptedPassword),
	}

	_, err = u.client.SqlClient().GetPermanentTokenByUser(accountId, userId, instanceId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Errorf("find permanent user failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// need insert operation
		err = u.client.SqlClient().InsertUserToken(&requiredToken)
		if err != nil {
			logger.Errorf("create user token failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}

		c.JSON(http.StatusOK, map[string]string{})
		return
	}

	err = u.client.SqlClient().UpdateUserPassword(&requiredToken)
	if err != nil {
		logger.Errorf("update user token failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, map[string]string{})
}

// CreateTemporaryToken 创建临时密码
// @Summary 创建临时密码
// @Description 创建临时密码
// @Tags credential
// @Accept application/json
// @Produce application/json
// @Param id path string true "实例ID"
// @Param userId query string false "用户id"
// @Param body body model.TemporaryPasswordArgs true "新密码"
// @Success 200 {object} model.TemporaryPasswordResponse "Success"
// @Failure default {object} bce.BceServiceError
// @Router /instances/{id}/credential [post]
func (u *CredentialHandler) CreateTemporaryToken(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	var req model.TemporaryPasswordArgs
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	accountId, userId, username := middleware.AccountIDFromContext(c), middleware.UserIDFromContext(c), middleware.UserNameFromContext(c)

	tokens, err := u.client.SqlClient().FindUserTokenByUser(accountId, userId, instanceId)
	if err != nil {
		logger.Errorf("find user token by %v/%v failed: %v", accountId, userId, err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	if len(tokens) >= u.maxTemporyTokenNumber {
		logger.Errorf("there is more than %d tokens exists", u.maxTemporyTokenNumber)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	randomPassword := utils.SimpleRandomPassword(16)
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(randomPassword), bcrypt.MinCost)
	if err != nil {
		logger.Errorf("generate password hash failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	now := time.Now().UTC()
	err = u.client.SqlClient().InsertUserToken(&models.UserToken{
		CreatedAt:    now,
		UpdatedAt:    now,
		ExpiredAt:    now.Add(time.Duration(req.Duration) * time.Hour),
		AccountID:    accountId,
		UserID:       userId,
		Username:     username,
		InstanceID:   instanceId,
		PasswordHash: string(passwordHash),
	})

	if err != nil {
		logger.Errorf("insert temporary password failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, &model.TemporaryPasswordResponse{Password: randomPassword})
}
