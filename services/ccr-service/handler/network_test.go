package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/vpc"
	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
	testingusersetting "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/usersetting"
	testingvpc "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/vpc"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

func newMockNetworkHandler() *NetworkHandler {
	return &NetworkHandler{
		clients: &testingclientset.ClientSet{},
	}
}

func TestNetworkHandler_CreatePrivateNetwork(t *testing.T) {
	nh := newMockNetworkHandler()
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	assert.NotNil(t, ctx)

	req := model.CreatePrivateLinkArgs{
		VpcID:          "vpc-x0pimxnr49rf",
		SubnetID:       "sbn-sh2nbn37wpfg",
		AutoDNSResolve: true,
	}

	data, err := json.Marshal(req)
	if err != nil {
		t.Errorf("json marshal failed: %s", err)
	}

	ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
	ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

	ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
		ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
		Spec: v1alpha1.CNCNetworkSpec{
			PrivateLinks: []v1alpha1.PrivateLink{
				{VPCID: "vpc-xxx", SubnetID: "sbn-aaa", AutoDNS: true},
			},
		},
	},
	)

	usersettingCli := &testingusersetting.MockClient{}
	mock.OnAnything(nh.clients, "Usersetting").Return(usersettingCli)
	mock.OnAnything(usersettingCli, "GetUserQuota").Return(&usersetting.GetQuotaResponse{
		ServiceType: "CCR",
		QuotaType:   "ccrVpcQuota",
		Quota:       "3",
	}, nil)

	vpcClient := &testingvpc.Client{}
	mock.OnAnything(nh.clients, "VpcClientForAccount").Return(vpcClient, nil)
	mock.OnAnything(vpcClient, "GetVPCDetail").Return(&vpc.GetVPCDetailResult{}, nil)

	scheme := runtime.NewScheme()
	v1alpha1.AddToScheme(scheme)
	mock.OnAnything(nh.clients, "K8sClient").Return(fake.NewClientBuilder().WithScheme(scheme).
		WithRuntimeObjects(&v1alpha1.CNCNetwork{
			ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
			Spec: v1alpha1.CNCNetworkSpec{
				PrivateLinks: []v1alpha1.PrivateLink{
					{VPCID: "vpc-xxx", SubnetID: "sbn-aaa", AutoDNS: true},
				},
			},
		}).Build())

	nh.CreatePrivateNetwork(ctx)
	assert.Equal(t, http.StatusOK, ctx.Writer.Status())

	req = model.CreatePrivateLinkArgs{
		VpcID:          "vpc-x0pimxnr49rf",
		SubnetID:       "sbn-sh2nbn37wpfg",
		AutoDNSResolve: true,
	}

	data, err = json.Marshal(req)
	if err != nil {
		t.Errorf("json marshal failed: %s", err)
	}

	ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
	ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

	ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
		ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
		Spec: v1alpha1.CNCNetworkSpec{
			PrivateLinks: []v1alpha1.PrivateLink{
				{VPCID: "vpc-x0pimxnr49rf", SubnetID: "sbn-sh2nbn37wpfg", AutoDNS: true},
			},
		},
	},
	)

	usersettingCli = &testingusersetting.MockClient{}
	mock.OnAnything(nh.clients, "Usersetting").Return(usersettingCli)
	mock.OnAnything(usersettingCli, "GetUserQuota").Return(&usersetting.GetQuotaResponse{
		ServiceType: "CCR",
		QuotaType:   "ccrVpcQuota",
		Quota:       "3",
	}, nil)

	nh.CreatePrivateNetwork(ctx)
	assert.Equal(t, http.StatusConflict, ctx.Writer.Status())

	req = model.CreatePrivateLinkArgs{
		VpcID:          "vpc-x0pimxnr49rf",
		SubnetID:       "sbn-sh2nbn37wpfg",
		AutoDNSResolve: true,
	}

	data, err = json.Marshal(req)
	if err != nil {
		t.Errorf("json marshal failed: %s", err)
	}

	ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
	ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

	ctx.Set(middleware.NETWORK_OBJECT_IDENTIY, &v1alpha1.CNCNetwork{
		ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
		Spec: v1alpha1.CNCNetworkSpec{
			PrivateLinks: []v1alpha1.PrivateLink{
				{VPCID: "vpc-dfs", SubnetID: "sbn-gsx", AutoDNS: true},
			},
		},
	},
	)

	usersettingCli = &testingusersetting.MockClient{}
	mock.OnAnything(nh.clients, "Usersetting").Return(usersettingCli)
	mock.OnAnything(usersettingCli, "GetUserQuota").Return(&usersetting.GetQuotaResponse{
		ServiceType: "CCR",
		QuotaType:   "ccrVpcQuota",
		Quota:       "3",
	}, nil)

	vpcClient = &testingvpc.Client{}
	mock.OnAnything(nh.clients, "VpcClientForAccount").Return(vpcClient, nil)
	mock.OnAnything(vpcClient, "GetVPCDetail").Return(nil, &bce.BceServiceError{
		StatusCode: http.StatusNotFound,
	})

	nh.CreatePrivateNetwork(ctx)
	assert.Equal(t, http.StatusOK, ctx.Writer.Status())

}
