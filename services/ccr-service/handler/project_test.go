package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/go-openapi/strfmt"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	ccrmodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	testingusersetting "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/usersetting"
	testinglisters "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/listers"
	testingproject "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service/harbor/project"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

func TestProjectHandler_CreateProject(t *testing.T) {

	ctx, _ := gin.CreateTestContext(newGinResponseWriter())

	type fields struct {
		ClientSet      clientset.ClientSetInterface
		projectService project.ProjectServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *Response
	}{
		{
			name: "case1",
			fields: fields{
				ClientSet:      &testingclientset.ClientSet{},
				projectService: &testingproject.MockProjectService{},
			},
			args: args{
				c: ctx,
			},
			want: NewResponse(http.StatusOK, "success", &ccrmodel.ProjectResult{
				ProjectID:    int32(1),
				ProjectName:  "test",
				ChartCount:   int64(1),
				RepoCount:    int64(1),
				CreationTime: strfmt.NewDateTime(),
				UpdateTime:   strfmt.NewDateTime(),
				AutoScan:     "true",
				Public:       "true",
			}, ctx),
		},
	}
	for _, tt := range tests {

		req := ccrmodel.CreateProjectRequest{
			ProjectName: "test",
			Public:      "true",
		}

		data, err := json.Marshal(req)
		if err != nil {
			t.Errorf("json marshal failed: %s", err)
		}
		ctx.Params = append(ctx.Params, gin.Param{Key: "instanceId", Value: "xxxx"})

		ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
		ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

		lister := &testinglisters.MockLister{}
		mock.OnAnything(tt.fields.ClientSet, "Lister").Return(lister)
		mock.OnAnything(lister, "GetInstanceInfo").Return(&listers.InstanceInfo{
			CCRType: "BASIC",
		}, nil)

		usersettingCli := &testingusersetting.MockClient{}
		mock.OnAnything(tt.fields.ClientSet, "Usersetting").Return(usersettingCli)
		mock.OnAnything(usersettingCli, "GetUserQuota").Return(&usersetting.GetQuotaResponse{
			ServiceType: "CCR",
			QuotaType:   "small-nsQuota",
			Quota:       "10",
		}, nil)

		mock.OnAnything(tt.fields.projectService, "TotalProjects").Return(int64(8), nil)
		mock.OnAnything(tt.fields.projectService, "NewProject").Return(nil)
		mock.OnAnything(tt.fields.projectService, "GetProject").Return(&ccrmodel.ProjectResult{
			ProjectID:    int32(1),
			ProjectName:  "test",
			ChartCount:   int64(1),
			RepoCount:    int64(1),
			CreationTime: strfmt.NewDateTime(),
			UpdateTime:   strfmt.NewDateTime(),
			AutoScan:     "true",
			Public:       "true",
		}, nil)

		t.Run(tt.name, func(t *testing.T) {
			h := &ProjectHandler{
				clientSet:      tt.fields.ClientSet,
				projectService: tt.fields.projectService,
			}
			assert.Equalf(t, tt.want, h.CreateProject(tt.args.c), "CreateProject(%v)", tt.args.c)
		})
	}
}
