package handler

import (
	"net/http"
	"strconv"

	_ "github.com/baidubce/bce-sdk-go/bce"
	"github.com/gin-gonic/gin"
	"github.com/go-openapi/runtime"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	tagapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/tag"
	artifactapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/artifact"
	scanapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/scan"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/artifact"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/tag"
)

type TagHandler struct {
	artifactService *artifact.ArtifactService
	tagService      *tag.TagService
}

func NewTagHandler() *TagHandler {
	return &TagHandler{
		artifactService: artifact.NewArtifactService(),
		tagService:      tag.NewTagService(),
	}
}

// ListTag 查询镜像仓库tag列表
// @Summary 查询镜像仓库tag列表
// @Description 查询镜像仓库tag列表
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param instanceId path string true "实例ID"
// @Param tagName query string false "tag名称"
// @Param pageNo query integer false "当前页" default(1)
// @Param pageSize query integer false "每页记录数" default(10)
// @Success 200 {object} model.ListTagResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags [get]
func (h TagHandler) ListTag(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, repositoryName, tagName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c), c.Query("tagName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	tags, total, err := h.tagService.ListTags(c, projectName, repositoryName, tagName, &pageNo, &pageSize, false)
	if err != nil {
		logger.Errorf("list tags failed: %s", err)
		return HandleTagSwaggerErrors(c, err)
	}
	ltr := &model.ListTagResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: tags,
	}
	return NewResponse(http.StatusOK, "success", ltr, c)
}

// BatchDeleteTag 批量删除镜像tag
// @Summary 批量删除镜像tag
// @Description 批量删除镜像tag
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param body body model.BatchDeleteRequest true "tag名称数组"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags [delete]
func (h TagHandler) BatchDeleteTag(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, repositoryName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c)

	var deleteRequest model.BatchDeleteRequest
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("bind project list item failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind project list item", nil, c)
	}

	if err := h.tagService.BatchDeleteTag(c, projectName, repositoryName, deleteRequest.Items); err != nil {
		logger.Errorf("delete tag failed: %s", err)
		return HandleTagSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", nil, c)
}

// GetTag 查询单个单个镜像tag
// @Summary 查询单个单个镜像tag
// @Description 查询单个单个镜像tag
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param tagName path string true "tag名称"
// @Success 200 {object} model.TagResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName} [get]
func (h TagHandler) GetTag(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, repositoryName, tagName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c), c.Param("tagName")

	tag, err := h.tagService.GetTag(c, projectName, repositoryName, tagName, false)
	if err != nil {
		logger.Errorf("get tag failed: %s", err)
		return HandleTagSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", tag, c)
}

// DeleteTag 删除单个镜像tag
// @Summary 删除单个镜像tag
// @Description 删除单个镜像tag
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param tagName path string true "tag名称"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName} [delete]
func (h TagHandler) DeleteTag(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, repositoryName, tagName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c), c.Param("tagName")

	if err := h.tagService.DeleteTag(c, projectName, repositoryName, tagName); err != nil {
		logger.Errorf("delete tag failed: %s", err)
		return HandleTagSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", nil, c)
}

// ScanOverview 查询镜像漏洞
// @Summary 查询镜像漏洞
// @Description 查询镜像漏洞
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param instanceId path string true "实例ID"
// @Param tagName path string true "tag名称"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ScanOverviewResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scanoverview [get]
func (h TagHandler) ScanOverview(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, repositoryName, tagName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c), c.Param("tagName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	scanOverviewResult, total, err := h.artifactService.ArtifactVulnerabilities(c, projectName, repositoryName, tagName, pageNo, pageSize, false)
	if err != nil {
		logger.Errorf("artifact vulner ability failed: %s", err)
		return HandleTagSwaggerErrors(c, err)
	}

	tbr := &model.ScanOverviewResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items:        scanOverviewResult.ScanOverviews,
		Summary:      scanOverviewResult.Summary,
		LastScanTime: scanOverviewResult.LastScanTime,
	}

	return NewResponse(http.StatusOK, "success", tbr, c)
}

// BuildHistory 查询构建历史
// @Summary 查询构建历史
// @Description 查询构建历史
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param tagName path string true "tag名称"
// @Success 200 {object} model.BuildHistoryResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/buildhistory [get]
func (h TagHandler) BuildHistory(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, repositoryName, tagName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c), c.Param("tagName")

	buildHistoryResult, err := h.artifactService.BuildHistory(c, projectName, repositoryName, tagName, false)
	if err != nil {
		logger.Errorf("get tag build history failed: %s", err)
		return HandleTagSwaggerErrors(c, err)
	}

	tbr := &model.BuildHistoryResponse{
		Items: buildHistoryResult,
	}
	return NewResponse(http.StatusOK, "success", tbr, c)
}

// TagScan 镜像扫描
// @Summary 镜像扫描
// @Description 镜像扫描
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param tagName path string true "tag名称"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scan [post]
func (h TagHandler) TagScan(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, repositoryName, tagName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c), c.Param("tagName")

	if err := h.artifactService.ScanArtifact(c, projectName, repositoryName, tagName, false); err != nil {
		logger.Errorf("scan artifact failed: %s", err)
		return HandleTagSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", nil, c)
}

// TagScanLog 镜像扫描日志
// @Summary 镜像扫描日志
// @Description 镜像扫描日志
// @Tags tag
// @Accept text/plain
// @Produce text/plain
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param tagName path string true "tag名称"
// @Param reportId path string true "扫描日志ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scan/{reportId}/log [get]
func (h TagHandler) TagScanLog(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, repositoryName, tagName, reportId := c.Param("projectName"), gin_context.RepositoryNameFromContext(c), c.Param("tagName"), c.Param("reportId")

	log, err := h.artifactService.ScanArtifactLog(c, projectName, repositoryName, tagName, reportId, false)
	if err != nil {
		logger.Errorf("scan artifact log failed: %s", err)
		return HandleTagSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", log, c)
}

// HandleTagSwaggerErrors takes a swagger generated error as input,
// which usually does not contain any form of error message,
// and outputs a new error with a proper message.
func HandleTagSwaggerErrors(c *gin.Context, in error) *Response {
	t, ok := in.(*runtime.APIError)
	if ok {
		switch t.Code {
		case http.StatusBadRequest:
			return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
		case http.StatusUnauthorized:
			return NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
		case http.StatusForbidden:
			return NewResponse(http.StatusForbidden, "user does not have permission to the tag", nil, c)
		case http.StatusNotFound:
			return NewResponse(http.StatusNotFound, "tag not found", nil, c)
		case http.StatusConflict:
			return NewResponse(http.StatusConflict, "tag name already exists", nil, c)
		case http.StatusPreconditionFailed:
			return NewResponse(http.StatusPreconditionFailed, "tag precondition failed", nil, c)
		case http.StatusInternalServerError:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		default:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}

	switch in.(type) {
	case *tagapi.ListTagsBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *tagapi.CountTagsBadRequest:
		return NewResponse(http.StatusConflict, "invalid request", nil, c)
	case *artifactapi.GetVulnerabilitiesAdditionNotFound:
		return NewResponse(http.StatusNotFound, "tag not found", nil, c)
	case *artifactapi.DeleteTagNotFound:
		return NewResponse(http.StatusNotFound, "tag not found", nil, c)
	case *artifactapi.GetAdditionNotFound:
		return NewResponse(http.StatusNotFound, "tag not found", nil, c)
	case *scanapi.ScanArtifactNotFound:
		return NewResponse(http.StatusNotFound, "tag not found", nil, c)
	case *scanapi.ScanArtifactForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *artifact.ScanArtifactUnsupportedMediaType:
		return NewResponse(http.StatusUnsupportedMediaType, "tag type unsupported", nil, c)
	default:
		return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
	}
}
