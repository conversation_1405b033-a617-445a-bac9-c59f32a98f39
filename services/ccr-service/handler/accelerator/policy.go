package accelerator

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/accelerator"
	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type PolicyHandler struct {
	clients clientset.ClientSetInterface
}

func NewPolicyHandler(clis clientset.ClientSetInterface) *PolicyHandler {
	return &PolicyHandler{
		clients: clis,
	}
}

// CreatePolicy 创建加速器策略
// @Summary 创建加速器策略
// @Description 创建加速器策略
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.AcceleratorPolicyRequest true "加速器参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 409 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/accelerators/policies [post]
func (h *PolicyHandler) CreatePolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")
	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	instanceInfo, err := h.clients.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	if instanceInfo.CCRType != listers.AdvancedCCRType {
		logger.Errorf("image accelerate just open to CCR type advance: now instance is %s", instanceInfo.CCRType)
		gin_context.E(c, gin_context.ForbiddenError(requestID))
		return
	}

	var pr model.AcceleratorPolicyRequest
	if err := c.Bind(&pr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	filters, err := h.validAcceleratorFilter(pr.Filters)
	if err != nil {
		logger.Errorf("check triger policy filter failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	acceleratorPolicy := &harbormodel.PolicyAcceleratorPolicy{
		Name:        pr.Name,
		Description: pr.Description,
		Enabled:     true,
		Filters:     filters,
	}

	if _, err := harborClient.AddonClient.Accelerator.CreateAcceleratorPolicy(accelerator.NewCreateAcceleratorPolicyParamsWithContext(c).
		WithXRequestID(&requestID).WithPolicy(acceleratorPolicy), harborClient.AuthInfo); err != nil {

		logger.Errorf("create accelerator policy from harbor failed: %s", err)
		if _, ok := err.(*accelerator.CreateAcceleratorPolicyConflict); ok {
			gin_context.E(c, gin_context.ConflictError(requestID, "accelerator policy conflict"))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "success")
}

// GetPolicy 查询加速器策略详情
// @Summary 查询加速器策略详情
// @Description 查询加速器策略详情
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "加速器策略ID"
// @Success 200 {object} model.AcceleratorPolicy "accelerator policy"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/accelerators/policies/{policyId} [get]
func (h *PolicyHandler) GetPolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	policyId := c.Param("policyId")

	resp, err := harborClient.AddonClient.Accelerator.GetAcceleratorPolicy(accelerator.NewGetAcceleratorPolicyParamsWithContext(c).
		WithXRequestID(&requestID).WithPolicyID(policyId), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("get accelerator policy from harbor failed: %s", err)
		if _, ok := err.(*accelerator.GetAcceleratorPolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	c.JSON(http.StatusOK, convertToPolicy(resp.GetPayload()))
}

// UpdatePolicy 修改加速器策略
// @Summary 修改加速器策略
// @Description 修改加速器策略
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "加速器策略ID"
// @Param body body model.AcceleratorPolicyRequest true "加速器参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/accelerators/policies/{policyId} [put]
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	policyId := c.Param("policyId")

	var pr model.AcceleratorPolicyRequest
	if err := c.Bind(&pr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	resp, err := harborClient.AddonClient.Accelerator.GetAcceleratorPolicy(accelerator.NewGetAcceleratorPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("get accelerator policy from harbor failed: %s", err)
		logger.Errorf("get accelerator policy from harbor failed: %s", err)
		if _, ok := err.(*accelerator.GetAcceleratorPolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	acceleratorPolicy := resp.GetPayload()
	if resp.GetPayload() == nil {
		logger.Errorf("get accelerator policy from harbor failed: %s", err)
		gin_context.E(c, gin_context.NotFoundError(requestID, fmt.Sprintf("not found policy with policy ID: %s", policyId)))
		return
	}

	filters, err := h.validAcceleratorFilter(pr.Filters)
	if err != nil {
		logger.Errorf("check triger policy filter failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	acceleratorPolicy.Name = pr.Name
	acceleratorPolicy.Description = pr.Description

	acceleratorPolicy.Filters = filters

	if _, err := harborClient.AddonClient.Accelerator.UpdateAcceleratorPolicy(accelerator.NewUpdateAcceleratorPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId).WithPolicy(acceleratorPolicy), harborClient.AuthInfo); err != nil {
		logger.Errorf("update accelerator policy from harbor failed: %s", err)
		logger.Errorf("get accelerator policy from harbor failed: %s", err)
		if _, ok := err.(*accelerator.UpdateAcceleratorPolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	c.JSON(http.StatusOK, "success")
}

// ListPolicies 获取加速器策略列表
// @Summary 获取加速器策略列表
// @Description 获取加速器策略列表
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyName query string false "加速器策略名称"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListAcceleratorPolicyResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/accelerators/policies [get]
func (h *PolicyHandler) ListPolicies(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	policyName := c.Query("policyName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	listPolicyParams := accelerator.NewListAcceleratorPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPage(&pageNo).
		WithPageSize(&pageSize)
	if policyName != "" {
		q := fmt.Sprintf("Name=%s", policyName)
		listPolicyParams.WithQ(&q)
	}

	resp, err := harborClient.AddonClient.Accelerator.ListAcceleratorPolicy(listPolicyParams, harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("list accelerator policy from harbor failed: %s", err)
		if _, ok := err.(*accelerator.ListAcceleratorPolicyBadRequest); ok {
			gin_context.E(c, gin_context.BadRequestError(requestID))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	policies := make([]*model.AcceleratorPolicy, 0)

	if resp != nil && len(resp.GetPayload()) > 0 {
		plys := resp.GetPayload()
		for _, ply := range plys {
			policies = append(policies, convertToPolicy(ply))
		}
	}

	ltp := &model.ListAcceleratorPolicyResponse{
		PageInfo: model.PageInfo{
			Total:    int(resp.XTotalCount),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Policies: policies,
	}
	c.JSON(http.StatusOK, ltp)
}

// DeletePolicy 删除加速器策略
// @Summary 删除加速器策略
// @Description 删除加速器策略
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "加速器策略ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/accelerators/policies/{policyId} [delete]
func (h *PolicyHandler) DeletePolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	policyId := c.Param("policyId")

	if _, err := harborClient.AddonClient.Accelerator.DeleteAcceleratorPolicy(accelerator.NewDeleteAcceleratorPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId), harborClient.AuthInfo); err != nil {
		logger.Errorf("delete accelerator policy from harbor failed: %s", err)
		if _, ok := err.(*accelerator.DeleteAcceleratorPolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "success")
}

// BatchDeletePolicy 批量删除加速器策略
// @Summary 批量删除加速器策略
// @Description 批量删除加速器策略
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.BatchDeleteInt64Request true "加速器策略ID数组"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/accelerators/policies [delete]
func (h *PolicyHandler) BatchDeletePolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	var deleteRequest model.BatchDeleteInt64Request
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("bind project list item failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
	}

	if _, err := harborClient.AddonClient.Accelerator.BatchDeleteAcceleratorPolicy(accelerator.NewBatchDeleteAcceleratorPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithRequest(&harbormodel.ModelsBatchDeleteRequest{Items: deleteRequest.Items}), harborClient.AuthInfo); err != nil {
		logger.Errorf("get accelerator policy from harbor failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "success")
}

// EnablePolicy 启动或关闭加速器策略
// @Summary 启动或关闭加速器策略
// @Description 启动或关闭加速器策略
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "加速器策略ID"
// @Param enabled query string true "是否开启"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/accelerators/policies/{policyId}/enable [put]
func (h *PolicyHandler) EnablePolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	policyId := c.Param("policyId")
	resp, err := harborClient.AddonClient.Accelerator.GetAcceleratorPolicy(accelerator.NewGetAcceleratorPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("get accelerator policy from harbor failed: %s", err)
		if _, ok := err.(*accelerator.GetAcceleratorPolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	acceleratorPolicy := resp.GetPayload()
	if acceleratorPolicy == nil {
		logger.Errorf("get accelerator policy from harbor failed: %s", err)
		gin_context.E(c, gin_context.NotFoundError(requestID, fmt.Sprintf("not found policy with policy ID: %s", policyId)))
		return
	}

	enabled, err := strconv.ParseBool(c.Query("enabled"))
	if err != nil {
		logger.Errorf("parse enabledis failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	if acceleratorPolicy.Enabled == enabled {
		logger.Errorf("policy enabled is %t", acceleratorPolicy.Enabled)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	acceleratorPolicy.Enabled = enabled

	_, err = harborClient.AddonClient.Accelerator.UpdateAcceleratorPolicy(accelerator.NewUpdateAcceleratorPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId).
		WithPolicy(acceleratorPolicy), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("update accelerator policy from harbor failed: %s", err)
		if _, ok := err.(*accelerator.UpdateAcceleratorPolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "success")
}

func (h *PolicyHandler) validAcceleratorFilter(acceleratorFilters []*model.AcceleratorFilter) ([]*harbormodel.PolicyAcceleratorFilter, error) {

	var isValid bool
	filters := make([]*harbormodel.PolicyAcceleratorFilter, 0)
	for _, fl := range acceleratorFilters {
		if fl.Type == model.FilterTypeProject {
			pattern := fl.Value
			if len(pattern) == 0 {
				return nil, fmt.Errorf("project pattern is not found")
			}
			isValid = true
		}
		if fl.Value == "" {
			fl.Value = "**"
		}
		filter := &harbormodel.PolicyAcceleratorFilter{
			Type:  string(fl.Type),
			Value: fl.Value,
		}
		filters = append(filters, filter)
	}

	if !isValid {
		return nil, fmt.Errorf("filters is invalid")
	}

	return filters, nil

}

func convertToPolicy(acceleratorPolicy *harbormodel.PolicyAcceleratorPolicy) *model.AcceleratorPolicy {

	filters := make([]*model.AcceleratorFilter, 0)
	for _, fl := range acceleratorPolicy.Filters {
		filters = append(filters, &model.AcceleratorFilter{
			Type:  model.FilterType(fl.Type),
			Value: fl.Value.(string),
		})
	}

	policy := &model.AcceleratorPolicy{
		CreationTime: acceleratorPolicy.CreationTime,
		Description:  acceleratorPolicy.Description,
		Enabled:      acceleratorPolicy.Enabled,
		Filters:      filters,
		ID:           acceleratorPolicy.ID,
		Name:         acceleratorPolicy.Name,
		UpdateTime:   acceleratorPolicy.UpdateTime,
	}

	return policy
}
