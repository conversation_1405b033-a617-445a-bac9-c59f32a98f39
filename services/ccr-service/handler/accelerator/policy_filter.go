package accelerator

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/pkg/reg/util"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type PolicyFilterHandler struct{}

func NewPolicyFilterHandler() *PolicyFilterHandler {
	return &PolicyFilterHandler{}
}

// TestPolicyFilter 测试加速器策略规则
// @Summary 测试加速器策略规则
// @Description 测试加速器策略规则
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Param body body model.TestPolicyFilter true "测试加速器规则"
// @Success 200 {object} model.TestPolicyFilterResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /accelerators/policies/filters [post]
func (h *PolicyFilterHandler) TestPolicyFilter(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var testPolicyFilter model.TestPolicyFilter
	if err := c.Bind(&testPolicyFilter); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	if testPolicyFilter.Repository == "" {
		logger.Errorf("repository is empty")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	image, err := parseImg(testPolicyFilter.Repository)
	if err != nil {
		logger.Errorf("invalid source image reference: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	err = h.validAcceleratorFilter(testPolicyFilter.Filters)
	if err != nil {
		logger.Errorf("check accelerator policy filter failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}
	matched := false

	for _, filter := range testPolicyFilter.Filters {
		switch filter.Type {
		case model.FilterTypeProject:
			pattern := filter.Value
			if len(pattern) > 0 {
				matched, err = util.Match(pattern, image.namespace)
				if err != nil {
					logger.Errorf("match project failed: %s", err)
					gin_context.E(c, gin_context.InternalServerError(requestID))
					return
				}
				if !matched {
					continue
				}
			}
			break
		case model.FilterTypeRepository:
			pattern := filter.Value
			if len(pattern) > 0 {
				matched, err = util.Match(pattern, image.repo)
				if err != nil {
					logger.Errorf("match repo failed: %s", err)
					gin_context.E(c, gin_context.InternalServerError(requestID))
					return
				}
				if !matched {
					continue
				}
			}
			break
		case model.FilterTypeTag:
			pattern := filter.Value
			if len(pattern) > 0 {
				matched, err = util.Match(pattern, image.tag)
				if err != nil {
					logger.Errorf("match tag failed: %s", err)
					gin_context.E(c, gin_context.InternalServerError(requestID))
					return
				}
				if !matched {
					continue
				}
			}
			break
		}
	}

	c.JSON(http.StatusOK, &model.TestPolicyFilterResponse{Matched: matched})
}

func (h *PolicyFilterHandler) validAcceleratorFilter(acceleratorFilters []*model.AcceleratorFilter) error {

	var isValid bool
	for _, fl := range acceleratorFilters {
		pattern := fl.Value
		if fl.Type == model.FilterTypeProject {

			if len(pattern) == 0 {
				return fmt.Errorf("project pattern is not found")
			}
			isValid = true
		}

		if pattern == "" {
			fl.Value = "**"
		}
	}

	if !isValid {
		return fmt.Errorf("filters is invalid")
	}
	return nil

}

// build Image accepts a string like library/ubuntu:14.04 and build a image struct
func parseImg(ref string) (*image, error) {

	images := strings.SplitN(ref, "/", 2)
	if len(images) < 2 {
		return nil, fmt.Errorf("unable to parse image from string: %s", ref)
	}
	res := &image{
		host: images[0],
	}

	repo := strings.SplitN(images[1], "/", 2)
	if len(repo) < 2 {
		return nil, fmt.Errorf("unable to parse image from string: %s", images[1])
	}
	i := strings.SplitN(repo[1], ":", 2)

	res.namespace = repo[0]
	res.repo = i[0]

	if len(i) == 2 {
		res.tag = i[1]
	}
	return res, nil
}

type image struct {
	host      string
	namespace string
	repo      string
	tag       string
}
