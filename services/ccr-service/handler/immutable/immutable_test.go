package immutable

import (
	"fmt"
	"net/http"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/immutable"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	mockrepo "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service/harbor/immutable"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
)

func TestImmutableRuleHandler_CreateImmutableRule(t *testing.T) {
	type fields struct {
		immutableRuleService immutable.Interface
		clientset            clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}

	body := strings.NewReader(`{
    "projectID":1,
    "disabled":false,
    "scope_selectors":[{
		"kind":"doublestar",
      	"decoration":"repoMatches",
	  	"pattern":""
    }],
    "tag_selectors":[{
		"kind":"doublestar",
		"decoration":"matches",
		"pattern":""
    }],
    "priority":0
  }`)

	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name:   "body is nil",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodPost, "http://test.com", nil)
				ctx.Request.Header.Add("Content-Type", "application/json")
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name: "success",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().ProjectExistsImmutableRule(gomock.Any(), gomock.Any()).AnyTimes().Return(false, nil)
				immutableService.EXPECT().CreateImmutableRule(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil)
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodPost, "http://test.com", body)
				ctx.Request.Header.Add("Content-Type", "application/json")
				return args{
					c: ctx,
				}
			}(),
			code: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := ImmutableRuleHandler{
				immutableRuleService: tt.fields.immutableRuleService,
				clients:              tt.fields.clientset,
			}

			if got := h.CreateImmutableRule(tt.args.c); got.StatusCode() != tt.code {
				t.Errorf("ImmutableRuleHandler.CreateImmutableRule() = %v, want %v", got.StatusCode(), tt.code)
			}
		})
	}
}

func TestImmutableRuleHandler_UpdateImmutableRule(t *testing.T) {
	type fields struct {
		immutableRuleService immutable.Interface
		clientset            clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	body := strings.NewReader(`{
    "id":1,
    "projectID":1,
    "disabled":false,
    "scope_selectors":[{
		"kind":"doublestar",
      	"decoration":"repoMatches",
	  	"pattern":""
    }],
    "tag_selectors":[{
		"kind":"doublestar",
		"decoration":"matches",
		"pattern":""
    }],
    "priority":0
  }`)
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name:   "body is nil",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodPost, "http://test.com", nil)
				ctx.Request.Header.Add("Content-Type", "application/json")
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name: "success",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().UpdateImmutableRule(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil)
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodPost, "http://test.com", body)
				ctx.Request.Header.Add("Content-Type", "application/json")
				return args{
					c: ctx,
				}
			}(),
			code: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := ImmutableRuleHandler{
				immutableRuleService: tt.fields.immutableRuleService,
				clients:              tt.fields.clientset,
			}

			if got := h.UpdateImmutableRule(tt.args.c); got.StatusCode() != tt.code {
				t.Errorf("ImmutableRuleHandler.UpdateImmutableRule() = %v, want %v", got.StatusCode(), tt.code)
			}
		})
	}
}

func TestImmutableRuleHandler_ListImmutableRule(t *testing.T) {
	type fields struct {
		immutableRuleService immutable.Interface
		clientset            clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name:   "invalid page number",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?pageNo=xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name:   "invalid page size",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?pageNo=1&pageSize=xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name: "list immutable failed",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().ListImmutableRule(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil, int64(0), fmt.Errorf("failed"))
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?pageNo=1&pageSize=10", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 500,
		},
		{
			name: "success",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().ListImmutableRule(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(
					[]*model.ImmutableRuleResult{
						{
							ID: 1,
						},
					}, int64(1), nil)
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?pageNo=1&pageSize=10", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := ImmutableRuleHandler{
				immutableRuleService: tt.fields.immutableRuleService,
				clients:              tt.fields.clientset,
			}

			if got := h.ListImmutableRule(tt.args.c); got.StatusCode() != tt.code {
				t.Errorf("ImmutableRuleHandler.ListImmutableRule() = %v, want %v", got.StatusCode(), tt.code)
			}
		})
	}
}

func TestImmutableRuleHandler_GetImmutableRule(t *testing.T) {
	type fields struct {
		immutableRuleService immutable.Interface
		clientset            clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name: "get immutable failed",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().GetImmutableRule(gomock.Any(), gomock.Any()).AnyTimes().Return(nil, fmt.Errorf("failed"))
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "immutableId", Value: "666"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com/instances/xxx/immutable/666", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 500,
		},
		{
			name: "success",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))

				immutableService.EXPECT().GetImmutableRule(gomock.Any(), gomock.Any()).AnyTimes().Return(
					&model.ImmutableRuleResult{
						ID: 1,
					}, nil)
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "immutableId", Value: "666"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com/instances/xxx/immutable/666", nil)

				return args{
					c: ctx,
				}
			}(),
			code: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := ImmutableRuleHandler{
				immutableRuleService: tt.fields.immutableRuleService,
				clients:              tt.fields.clientset,
			}

			if got := h.GetImmutableRule(tt.args.c); got.StatusCode() != tt.code {
				t.Errorf("ImmutableRuleHandler.GetImmutableRule() = %v, want %v", got.StatusCode(), tt.code)
			}
		})
	}
}

func TestImmutableRuleHandler_DeleteImmutableRule(t *testing.T) {
	type fields struct {
		immutableRuleService immutable.Interface
		clientset            clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name:   "invalid immutable rule ID",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "http://test.com/instances/xxx/immutable/xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name: "immutable rule not exist",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().ImmutableRuleExists(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "immutableId", Value: "666"})
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "http://test.com/instances/xxx/immutable/1", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name: "delete immutable rule failed",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().ImmutableRuleExists(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
				immutableService.EXPECT().DeleteImmutableRule(gomock.Any(), gomock.Any()).Return(fmt.Errorf("failed")).AnyTimes()
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "immutableId", Value: "666"})
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "http://test.com/instances/xxx/immutable/1", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 500,
		},
		{
			name: "success",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().ImmutableRuleExists(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
				immutableService.EXPECT().DeleteImmutableRule(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "immutableId", Value: "666"})
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "http://test.com/instances/xxx/immutable/666", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := ImmutableRuleHandler{
				immutableRuleService: tt.fields.immutableRuleService,
				clients:              tt.fields.clientset,
			}

			if got := h.DeleteImmutableRule(tt.args.c); got.StatusCode() != tt.code {
				t.Errorf("ImmutableRuleHandler.DeleteImmutableRule() = %v, want %v", got.StatusCode(), tt.code)
			}
		})
	}
}

func TestImmutableRuleHandler_BatchDeleteImmutableRule(t *testing.T) {
	type fields struct {
		immutableRuleService immutable.Interface
		clientset            clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name:   "invalid immutable ids",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "http://test.com/instances/xxx/immutable", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name: "delete immutable rule failed",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().BatchDeleteImmutableRule(gomock.Any(), gomock.Any()).Return(fmt.Errorf("failed")).AnyTimes()
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "http://test.com/instances/xxx/immutable", strings.NewReader(`{"items":[1,2]}`))
				return args{
					c: ctx,
				}
			}(),
			code: 500,
		},
		{
			name: "success",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().BatchDeleteImmutableRule(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "http://test.com/instances/xxx/immutable", strings.NewReader(`{"items":[1,2]}`))
				return args{
					c: ctx,
				}
			}(),
			code: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := ImmutableRuleHandler{
				immutableRuleService: tt.fields.immutableRuleService,
				clients:              tt.fields.clientset,
			}

			if got := h.BatchDeleteImmutableRule(tt.args.c); got.StatusCode() != tt.code {
				t.Errorf("ImmutableRuleHandler.BatchDeleteImmutableRule() = %v, want %v", got.StatusCode(), tt.code)
			}
		})
	}
}

func TestImmutableRuleHandler_EnableImmutableRule(t *testing.T) {
	type fields struct {
		immutableRuleService immutable.Interface
		clientset            clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name:   "invalid params1",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodPut, "http://test.com/instances/xxx/immutable/xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name:   "invalid params2",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodPut, "http://test.com/instances/xxx/immutable/xxx?enabled=true", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name: "immutable rule not exist",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().GetImmutableRule(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("not exist")).AnyTimes()
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "immutableId", Value: "666"})
				ctx.Request, _ = http.NewRequest(http.MethodPut, "http://test.com/instances/xxx/immutable/666?enabled=true", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name: "enable immutable rule failed",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().GetImmutableRule(gomock.Any(), gomock.Any()).Return(&model.ImmutableRuleResult{
					ID:          1,
					ProjectID:   1,
					ProjectName: "test",
					Priority:    0,
					Action:      "immutable",
					Template:    "immutable_template",
					ScopeSelectors: []model.ImmutableSelector{{
						Kind:       "doublestar",
						Decoration: "matches",
						Pattern:    "tag",
					}},
					TagSelectors: []*model.ImmutableSelector{{
						Kind:       "doublestar",
						Decoration: "repoMatches",
						Pattern:    "image",
					}},
				}, nil).AnyTimes()
				immutableService.EXPECT().UpdateImmutableRule(gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("failed")).AnyTimes()
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "immutableId", Value: "666"})
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "http://test.com/instances/xxx/immutable/666?enabled=true", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 500,
		},
		{
			name: "success",
			fields: func() fields {
				immutableService := mockrepo.NewMockInterface(gomock.NewController(t))
				immutableService.EXPECT().GetImmutableRule(gomock.Any(), gomock.Any()).Return(&model.ImmutableRuleResult{
					ID:          1,
					ProjectID:   1,
					ProjectName: "test",
					Priority:    0,
					Action:      "immutable",
					Template:    "immutable_template",
					ScopeSelectors: []model.ImmutableSelector{{
						Kind:       "doublestar",
						Decoration: "matches",
						Pattern:    "tag",
					}},
					TagSelectors: []*model.ImmutableSelector{{
						Kind:       "doublestar",
						Decoration: "repoMatches",
						Pattern:    "image",
					}},
				}, nil).AnyTimes()
				immutableService.EXPECT().UpdateImmutableRule(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				return fields{
					immutableRuleService: immutableService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "immutableId", Value: "666"})
				ctx.Request, _ = http.NewRequest(http.MethodDelete, "http://test.com/instances/xxx/immutable/666?enabled=false", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := ImmutableRuleHandler{
				immutableRuleService: tt.fields.immutableRuleService,
				clients:              tt.fields.clientset,
			}

			if got := h.EnableImmutableRule(tt.args.c); got.StatusCode() != tt.code {
				t.Errorf("ImmutableRuleHandler.EnableImmutableRule() = %v, want %v", got.StatusCode(), tt.code)
			}
		})
	}
}
