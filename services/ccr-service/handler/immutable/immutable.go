package immutable

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	projectapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/project"
	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/immutable"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler"
)

type ImmutableRuleHandler struct {
	clients              clientset.ClientSetInterface
	immutableRuleService immutable.Interface
}

func NewImmutableHandler(cli clientset.ClientSetInterface) *ImmutableRuleHandler {
	return &ImmutableRuleHandler{
		clients:              cli,
		immutableRuleService: immutable.NewImmutableRuleService(),
	}
}

// CreateImmutableRule 创建版本不可变规则
// @Summary 创建版本不可变规则
// @Description 创建版本不可变规则
// @Tags immutable
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param immutableRule body model.ImmutableRuleRequest true "版本不可变规则参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/immutable [post]
func (h *ImmutableRuleHandler) CreateImmutableRule(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	var ir model.ImmutableRuleRequest
	if err := c.Bind(&ir); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	projectId := ir.ProjectID

	// 同一个project只允许存在一条版本不可变规则
	exist, err := h.immutableRuleService.ProjectExistsImmutableRule(c, projectId)
	if exist {
		logger.Errorf("project [%d] contain immutable rule,not allow create.", projectId)
		return handler.NewResponse(http.StatusBadRequest, "project contain immutable rule,not allow create.", nil, c)
	}

	if ir.Template == "" {
		ir.Template = "immutable_template"
	}
	if ir.Action == "" {
		ir.Action = "immutable"
	}

	scopeSelector := make([]harbormodel.ImmutableSelector, 0)
	for _, t := range ir.ScopeSelectors {
		if t.Pattern == "" {
			t.Pattern = "**"
		}
		sc := &harbormodel.ImmutableSelector{
			Decoration: t.Decoration,
			Kind:       t.Kind,
			Pattern:    t.Pattern,
		}
		scopeSelector = append(scopeSelector, *sc)
	}
	scopeSelectors := make(map[string][]harbormodel.ImmutableSelector)
	scopeSelectors["repository"] = scopeSelector

	tagSelectors := make([]*harbormodel.ImmutableSelector, 0)
	for _, t := range ir.TagSelectors {
		if t.Pattern == "" {
			t.Pattern = "**"
		}
		tagSelector := &harbormodel.ImmutableSelector{
			Decoration: t.Decoration,
			Extras:     t.Extras,
			Kind:       t.Kind,
			Pattern:    t.Pattern,
		}
		tagSelectors = append(tagSelectors, tagSelector)
	}

	rule := &harbormodel.ImmutableRule{
		Action:         ir.Action,
		Disabled:       ir.Disabled,
		Template:       ir.Template,
		Priority:       ir.Priority,
		ScopeSelectors: scopeSelectors,
		TagSelectors:   tagSelectors,
	}

	err = h.immutableRuleService.CreateImmutableRule(c, projectId, rule)

	if err != nil {
		logger.Errorf("create immutable rule failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "create immutable rule failed", err, c)
	}
	return handler.NewResponse(http.StatusOK, "success", "", c)
}

// UpdateImmutableRule 更新版本不可变规则
// @Summary 更新版本不可变规则
// @Description 更新版本不可变规则
// @Tags immutable
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param immutableId path string true "版本不可变规则ID"
// @Param immutableRule body model.ImmutableRuleRequest true "版本不可变规则参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/immutable/{immutableId} [put]
func (h *ImmutableRuleHandler) UpdateImmutableRule(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	var ir model.ImmutableRuleRequest
	if err := c.Bind(&ir); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}
	projectId := ir.ProjectID

	scopeSelector := make([]harbormodel.ImmutableSelector, 0)
	for _, t := range ir.ScopeSelectors {
		if t.Pattern == "" {
			t.Pattern = "**"
		}
		sc := &harbormodel.ImmutableSelector{
			Decoration: t.Decoration,
			Kind:       t.Kind,
			Pattern:    t.Pattern,
		}
		scopeSelector = append(scopeSelector, *sc)
	}
	scopeSelectors := make(map[string][]harbormodel.ImmutableSelector)
	scopeSelectors["repository"] = scopeSelector

	tagSelectors := make([]*harbormodel.ImmutableSelector, 0)
	for _, t := range ir.TagSelectors {
		if t.Pattern == "" {
			t.Pattern = "**"
		}
		tagSelector := &harbormodel.ImmutableSelector{
			Decoration: t.Decoration,
			Extras:     t.Extras,
			Kind:       t.Kind,
			Pattern:    t.Pattern,
		}
		tagSelectors = append(tagSelectors, tagSelector)
	}

	rule := &harbormodel.ImmutableRule{
		ID:             ir.ID,
		Action:         ir.Action,
		Disabled:       ir.Disabled,
		Template:       ir.Template,
		Priority:       ir.Priority,
		ScopeSelectors: scopeSelectors,
		TagSelectors:   tagSelectors,
	}

	err := h.immutableRuleService.UpdateImmutableRule(c, projectId, rule)

	if err != nil {
		logger.Errorf("update immutable failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "update immutable rule failed", err, c)
	}
	return handler.NewResponse(http.StatusOK, "success", "", c)
}

// DeleteImmutableRule 删除版本不可变规则
// @Summary 删除版本不可变规则
// @Description 删除版本不可变规则
// @Tags immutable
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param immutableId path string true "版本不可变规则ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/immutable/{immutableId} [delete]
func (h *ImmutableRuleHandler) DeleteImmutableRule(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	immutableId, err := strconv.ParseInt(c.Param("immutableId"), 10, 64)
	if err != nil {
		logger.Errorf("immutable rule ID is invalid")
		return handler.NewResponse(http.StatusBadRequest, "immutable rule ID is invalid", nil, c)
	}

	exist, err := h.immutableRuleService.ImmutableRuleExists(c, immutableId)
	if !exist {
		info := fmt.Sprintf("the immutableId %d not exist.", immutableId)
		logger.Errorf(info)
		return handler.NewResponse(http.StatusBadRequest, info, nil, c)
	}

	if err := h.immutableRuleService.DeleteImmutableRule(c, immutableId); err != nil {
		logger.Errorf("delete immutable rule failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "delete immutable rule failed", err, c)

	}
	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// BatchDeleteImmutableRule 批量删除版本不可变规则
// @Summary 批量删除版本不可变规则
// @Description 批量删除版本不可变规则
// @Tags immutable
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param immutableIds body model.BatchDeleteInt64Request true "版本不可变规则ID数组"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/immutable [delete]
func (h *ImmutableRuleHandler) BatchDeleteImmutableRule(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	var deleteRequest model.BatchDeleteInt64Request
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("immutable ids is invalid")
		return handler.NewResponse(http.StatusBadRequest, "immutable ids is invalid", nil, c)
	}

	if err := h.immutableRuleService.BatchDeleteImmutableRule(c, deleteRequest.Items); err != nil {
		logger.Errorf("delete immutable rule failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "delete immutable rule failed", err, c)

	}
	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// EnableImmutableRule 启用或禁用版本不可变规则
// @Summary 启用或禁用版本不可变规则
// @Description 启用或禁用版本不可变规则
// @Tags immutable
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param immutableId path string true "版本不可变规则ID"
// @Param enabled query string true "是否启用(true or false)"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/immutable/{immutableId}/enable [put]
func (h *ImmutableRuleHandler) EnableImmutableRule(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)
	enabled, err := strconv.ParseBool(c.Query("enabled"))
	if err != nil {
		logger.Errorf("parse enabled is failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "Parse params failed", nil, c)
	}

	immutableId, err := strconv.ParseInt(c.Param("immutableId"), 10, 64)
	if err != nil {
		logger.Errorf("immutable rule ID is invalid")
		return handler.NewResponse(http.StatusBadRequest, "immutable rule ID is invalid", nil, c)
	}

	ir, err := h.immutableRuleService.GetImmutableRule(c, immutableId)
	if err != nil {
		logger.Errorf("get immutable rule failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "get immutable rule failed", nil, c)

	}

	scopeSelector := make([]harbormodel.ImmutableSelector, 0)
	for _, t := range ir.ScopeSelectors {
		sc := &harbormodel.ImmutableSelector{
			Decoration: t.Decoration,
			Kind:       t.Kind,
			Pattern:    t.Pattern,
		}
		scopeSelector = append(scopeSelector, *sc)
	}
	scopeSelectors := make(map[string][]harbormodel.ImmutableSelector)
	scopeSelectors["repository"] = scopeSelector

	tagSelectors := make([]*harbormodel.ImmutableSelector, 0)
	for _, t := range ir.TagSelectors {
		tagSelector := &harbormodel.ImmutableSelector{
			Decoration: t.Decoration,
			Extras:     t.Extras,
			Kind:       t.Kind,
			Pattern:    t.Pattern,
		}
		tagSelectors = append(tagSelectors, tagSelector)
	}

	rule := &harbormodel.ImmutableRule{
		ID:             ir.ID,
		Action:         ir.Action,
		Template:       ir.Template,
		Priority:       ir.Priority,
		ScopeSelectors: scopeSelectors,
		TagSelectors:   tagSelectors,
	}
	if enabled {
		//enabled为true，表示启用规则,disable置为false
		rule.Disabled = false
	} else {
		rule.Disabled = true
	}

	projectId := ir.ProjectID
	er := h.immutableRuleService.UpdateImmutableRule(c, projectId, rule)
	if er != nil {
		logger.Errorf("enable immutable rule failed: %s", er)
		return handler.NewResponse(http.StatusInternalServerError, "enable immutable rule failed", nil, c)
	}
	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// ListImmutableRule 获取版本不可变规则列表
// @Summary 获取版本不可变规则列表
// @Description 获取版本不可变规则列表
// @Tags immutable
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListImmutableRuleResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/immutable [get]
func (h *ImmutableRuleHandler) ListImmutableRule(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return handler.NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return handler.NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	immutables, total, err := h.immutableRuleService.ListImmutableRule(c, &pageNo, &pageSize)
	if err != nil {
		logger.Errorf("list immutable rule failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "list immutable rule failed", nil, c)
	}

	lrr := &model.ListImmutableRuleResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: immutables,
	}
	return handler.NewResponse(http.StatusOK, "success", lrr, c)
}

// GetImmutableRule 查询版本不可变策略详情
// @Summary 查询版本不可变策略详情
// @Description 查询版本不可变策略详情
// @Tags immutable
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param immutableId path string true "版本不可变规则ID"
// @Success 200 {object} model.ImmutableRuleResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/immutable/{immutableId} [get]
func (h *ImmutableRuleHandler) GetImmutableRule(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	immutableId, err := strconv.ParseInt(c.Param("immutableId"), 10, 64)
	if err != nil {
		logger.Errorf("immutableRule ID is invalid")
		return handler.NewResponse(http.StatusBadRequest, "immutableRule ID is invalid", nil, c)
	}

	immutableRuleResult, err := h.immutableRuleService.GetImmutableRule(c, immutableId)
	if err != nil {
		logger.Errorf("get immutable rule failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "get immutable rule failed", nil, c)

	}

	return handler.NewResponse(http.StatusOK, "success", immutableRuleResult, c)
}

// GetImmutableProjectList 查询命名空间列表
// @Summary 查询命名空间列表
// @Description 查询命名空间列表
// @Tags immutable
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Success 200 {array} model.ImmutableRuleProjectResult "get immutable project list success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/immutable/project [get]
func (h *ImmutableRuleHandler) GetImmutableProjectList(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)
	requestId := gin_context.RequestIdFromContext(c)

	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "get immutable project failed", nil, c)
	}

	// 分页查询project最大允许单页100条，暂不考虑超过100的场景
	var page, size = int64(1), int64(100)
	resp, err := harborClient.V2Client.Project.ListProjects(projectapi.NewListProjectsParamsWithContext(c).
		WithPage(&page).WithPageSize(&size).WithXRequestID(&requestId), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("list projects failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "list projects failed", nil, c)
	}
	projects := resp.Payload

	result := make([]*model.ImmutableRuleProjectResult, 0)
	for i := range projects {
		projectId := projects[i].ProjectID
		projectName := projects[i].Name

		exist, err := h.immutableRuleService.ProjectExistsImmutableRule(c, int64(projectId))
		if err != nil {
			logger.Errorf("check project exist immutable rule failed:%s", err)
		}
		// 同一个project只允许存在一条版本不可变规则,如果已有则不返回project
		if !exist {
			project := model.ImmutableRuleProjectResult{
				ProjectID:   int64(projectId),
				ProjectName: projectName,
			}

			result = append(result, &project)
		}
	}

	return handler.NewResponse(http.StatusOK, "success", result, c)
}
