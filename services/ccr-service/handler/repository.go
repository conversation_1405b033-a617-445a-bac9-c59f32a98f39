package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	_ "github.com/baidubce/bce-sdk-go/bce"
	"github.com/gin-gonic/gin"
	"github.com/go-openapi/runtime"
	v1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	repositoryapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/repository"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/repository"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type RepositoryHandler struct {
	repositoryService repository.Interface
	clientset         clientset.ClientSetInterface
	repoService       service.RepositoryServiceInterface
}

func NewRepositoryHandler(clientset clientset.ClientSetInterface) *RepositoryHandler {
	return &RepositoryHandler{
		clientset:         clientset,
		repositoryService: repository.NewRepositoryService(),
		repoService:       service.NewRepositoryService(clientset),
	}
}

// ListRepository 查询镜像仓库列表
// @Summary 查询镜像仓库列表
// @Description 查询镜像仓库列表
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName query string false "镜像仓库名称"
// @Param pageNo query int false "当前页" default(1)
// @Param pageSize query int false "每页记录数" default(10)
// @Success 200 {object} model.ListRepositoryResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories [get]
func (h RepositoryHandler) ListRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	instanceId, projectName, repositoryName := c.Param("instanceId"), c.Param("projectName"), c.Query("repositoryName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	// 查询实例缓存
	instanceInfo, err := h.clientset.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	repositoryResults, total, err := h.repositoryService.ListRepositories(c, projectName, repositoryName, &pageNo, &pageSize, false)
	if err != nil {
		logger.Errorf("list repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}
	for i := range repositoryResults {
		repositoryResults[i].RepositoryPath = fmt.Sprintf("%s/%s/%s", instanceInfo.PublicURL, projectName, repositoryResults[i].RepositoryName)
		repositoryResults[i].PrivateRepositoryPath = fmt.Sprintf("%s/%s/%s", instanceInfo.PrivateURL, projectName, repositoryResults[i].RepositoryName)
	}

	lrr := &model.ListRepositoryResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: repositoryResults,
	}

	return NewResponse(http.StatusOK, "success", lrr, c)
}

// ListAllRepository 查询所有的镜像仓库列表
// @Summary 查询所有的镜像仓库列表
// @Description 查询所有的镜像仓库列表
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param repositoryName query string false "镜像仓库名称"
// @Param pageNo query int false "当前页" default(1)
// @Param pageSize query int false "每页记录数" default(10)
// @Success 200 {object} model.ListRepositoryResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/repositories [get]
func (h RepositoryHandler) ListAllRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	repositoryName := c.Query("repositoryName")

	instanceId := c.Param("instanceId")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	// 查询实例缓存
	instanceInfo, err := h.clientset.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	repositoryResults, total, err := h.repositoryService.ListAllRepositories(c, repositoryName, pageNo, pageSize)
	if err != nil {
		logger.Errorf("list repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}

	for _, v := range repositoryResults {
		v.RepositoryPath = fmt.Sprintf("%s/%s/%s", instanceInfo.PublicURL, v.ProjectName, v.RepositoryName)
		v.PrivateRepositoryPath = fmt.Sprintf("%s/%s/%s", instanceInfo.PrivateURL, v.ProjectName, v.RepositoryName)
	}

	lrr := &model.ListRepositoryResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: repositoryResults,
	}

	return NewResponse(http.StatusOK, "success", lrr, c)
}

// BatchDeleteRepository 批量删除镜像仓库
// @Summary 批量删除镜像仓库
// @Description 批量删除镜像仓库
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param body body model.BatchDeleteRequest true "镜像仓库名称数组"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories [delete]
func (h *RepositoryHandler) BatchDeleteRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName := c.Param("projectName")

	var deleteRequest model.BatchDeleteRequest
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("bind project list item failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind project list item", nil, c)
	}

	if err := h.repositoryService.BatchDeleteRepository(c, projectName, deleteRequest.Items); err != nil {
		logger.Errorf("delete repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", nil, c)
}

// DeleteRepository 删除单个镜像仓库
// @Summary 删除单个镜像仓库
// @Description 删除单个镜像仓库
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName} [delete]
func (h *RepositoryHandler) DeleteRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName, repositoryName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c)

	if err := h.repositoryService.DeleteRepository(c, projectName, repositoryName); err != nil {
		logger.Errorf("delete repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", nil, c)
}

// GetRepository 通过镜像仓库名称查询镜像仓库
// @Summary 通过镜像仓库名称查询镜像仓库
// @Description 通过镜像仓库名称查询镜像仓库
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Success 200 {object} model.RepositoryResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName} [get]
func (h *RepositoryHandler) GetRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	instanceId, projectName, repositoryName := c.Param("instanceId"), c.Param("projectName"), gin_context.RepositoryNameFromContext(c)

	// 查询实例缓存
	instanceInfo, err := h.clientset.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}

	repositoryResult, err := h.repositoryService.GetRepository(c, projectName, repositoryName, false)
	if err != nil {
		logger.Errorf("get repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}
	repositoryResult.RepositoryPath = fmt.Sprintf("%s/%s/%s", instanceInfo.PublicURL, projectName, repositoryResult.RepositoryName)
	repositoryResult.PrivateRepositoryPath = fmt.Sprintf("%s/%s/%s", instanceInfo.PrivateURL, projectName, repositoryResult.RepositoryName)

	return NewResponse(http.StatusOK, "success", repositoryResult, c)
}

// UpdateRepository 修改单个镜像仓库
// @Summary 修改单个镜像仓库
// @Description 修改单个镜像仓库
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param repository body model.UpdateRepositoryRequest true "update repository request"
// @Success 200 {object} model.RepositoryResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName} [put]
func (h *RepositoryHandler) UpdateRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	instanceId, projectName, repositoryName := c.Param("instanceId"), c.Param("projectName"), gin_context.RepositoryNameFromContext(c)

	// 查询实例缓存
	instanceInfo, err := h.clientset.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	var urr *model.UpdateRepositoryRequest
	if err := c.Bind(&urr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	if err := h.repositoryService.UpdateRepository(c, projectName, repositoryName, urr.Description, false); err != nil {
		logger.Errorf("update repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}
	repositoryResult, err := h.repositoryService.GetRepository(c, projectName, repositoryName, false)
	if err != nil {
		logger.Errorf("get repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}
	repositoryResult.RepositoryPath = fmt.Sprintf("%s/%s/%s", instanceInfo.PublicURL, projectName, repositoryResult.RepositoryName)
	repositoryResult.PrivateRepositoryPath = fmt.Sprintf("%s/%s/%s", instanceInfo.PrivateURL, projectName, repositoryResult.RepositoryName)

	return NewResponse(http.StatusOK, "success", repositoryResult, c)

}

// BuildRepository 创建镜像构建任务
// @Summary 创建镜像构建任务
// @Description 创建镜像构建任务
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param imagebuild body model.BuildRepositoryTaskRequest true "create build repository task body"
// @Success 201 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds [post]
func (h *RepositoryHandler) BuildRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	userID, userName, accountID := middleware.UserIDFromContext(c), middleware.UserNameFromContext(c), middleware.AccountIDFromContext(c)
	instanceId, projectName, repositoryName := c.Param("instanceId"), c.Param("projectName"), gin_context.RepositoryNameFromContext(c)

	var brt model.BuildRepositoryTaskRequest
	if err := c.Bind(&brt); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	dockerfileInDB, dockerfileContent := brt.Dockerfile, brt.Dockerfile
	if brt.FromType == model.FromTypeBaseimage {
		content, err := json.Marshal(brt.ImageBuildConfig)
		if err != nil {
			logger.Errorf("marshal image build config failed: %s", err)
			return NewResponse(http.StatusBadRequest, "marshal image build config failed", nil, c)
		}

		dockerfileInDB = string(content)
		dockerfileContent = brt.ImageBuildConfig.String()
	}

	if dockerfileContent == "" {
		logger.Errorf("no docker file content provided")
		return NewResponse(http.StatusBadRequest, "no build content provided", nil, c)
	}

	tagName := brt.TagName
	if brt.IsLatest {
		tagName = "latest"
	}
	if len(tagName) == 0 {
		logger.Errorf("tag name is invalid")
		return NewResponse(http.StatusBadRequest, "tag name is invalid", nil, c)
	}

	ib := &models.ImageBuild{
		CreatedAt:  time.Now(),
		AccountID:  middleware.AccountIDFromContext(c),
		UserID:     middleware.UserIDFromContext(c),
		Userame:    middleware.UserNameFromContext(c),
		InstanceID: instanceId,
		Status:     model.ImageBuildStatusPending,
		Project:    projectName,
		Repository: repositoryName,
		TagName:    brt.TagName,
		IsLatest:   brt.IsLatest,
		FromType:   string(brt.FromType),
		Dockerfile: dockerfileInDB,
	}

	err := h.clientset.SqlClient().InsertImageBuild(ib)
	if err != nil {
		logger.Errorf("insert image build failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "create imange build record failed", nil, c)
	}

	instanceInfo, err := h.clientset.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	recordID := fmt.Sprintf("%d", ib.ID)

	_, err = h.repoService.CreateBuildRepositoryTask(c, projectName, repositoryName, tagName,
		dockerfileContent, brt.FromType, accountID, userID, userName, recordID, instanceInfo)
	if err != nil {
		logger.Errorf("create kaniko workfile failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	// BuildRepositoryTaskResponse
	return NewResponse(http.StatusCreated, "success", &model.BuildRepositoryTaskResponse{
		ID: recordID,
	}, c)
}

// ListBuildRepositoryTask 获取镜像构建任务
// @Summary 获取镜像构建任务
// @Description 获取镜像构建任务
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param pageNo query int false "当前页" default(1)"
// @Param pageSize query int false "每页记录数" default(10) 最大100"
// @Param keywordType query string false "关键字类型"
// @Param keyword query string false "关键字"
// @Success 200 {object} model.ListBuildRepositoryTaskResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds [get]
func (h *RepositoryHandler) ListBuildRepositoryTask(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	instanceId, projectName, repositoryName := c.Param("instanceId"), c.Param("projectName"), gin_context.RepositoryNameFromContext(c)

	var pagedInfo model.PagedListOption
	var err error
	if err = c.BindQuery(&pagedInfo); err != nil {
		logger.Errorf("bind query failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind query failed", nil, c)
	}

	if pagedInfo.PageNo == 0 {
		pagedInfo.PageNo = 1
	}

	if pagedInfo.PageSize == 0 {
		pagedInfo.PageSize = 10
	}

	filter := map[string]interface{}{
		"instance_id": instanceId,
		"project":     projectName,
		"repository":  repositoryName,
	}

	if pagedInfo.KeywordType == "id" && pagedInfo.Keyword != "" {
		id, err := strconv.ParseInt(pagedInfo.Keyword, 10, 64)
		if err != nil {
			return NewResponse(http.StatusOK, "success", &model.ListBuildRepositoryTaskResponse{
				PageInfo: model.PageInfo{
					Total:    0,
					PageNo:   pagedInfo.PageNo,
					PageSize: pagedInfo.PageSize,
				},
				Items: []*model.BuildRepositoryTaskResult{},
			}, c)
		}

		filter["id"] = id
	}

	// keyword type only support id now
	ibs, total, err := h.clientset.SqlClient().ListImageBuildPaged("",
		filter, pagedInfo.PageNo, pagedInfo.PageSize)
	if err != nil {
		logger.Errorf("list image build failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "list image build failed", nil, c)
	}

	brt := make([]*model.BuildRepositoryTaskResult, len(ibs))
	for i, v := range ibs {
		brt[i] = &model.BuildRepositoryTaskResult{
			ID:         fmt.Sprintf("%d", v.ID),
			TagName:    v.TagName,
			IsLatest:   v.IsLatest,
			FromType:   v.FromType,
			Dockerfile: v.Dockerfile,
			Status:     v.Status,
			CreateAt:   v.CreatedAt,
			CreateBy:   v.Userame,
			Image:      fmt.Sprintf("%s/%s:%s", v.Project, v.Repository, v.TagName),
		}
		if v.IsLatest {
			brt[i].Image = fmt.Sprintf("%s/%s:latest", v.Project, v.Repository)
		}
	}

	lrt := &model.ListBuildRepositoryTaskResponse{
		PageInfo: model.PageInfo{
			Total:    total,
			PageNo:   pagedInfo.PageNo,
			PageSize: pagedInfo.PageSize,
		},
		Items: brt,
	}

	return NewResponse(http.StatusOK, "success", lrt, c)
}

// GetBuildRepositoryTask 获取镜像构建任务详情
// @Summary 获取镜像构建任务详情
// @Description 获取镜像构建任务详情
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param buildId path string true "镜像构建任务ID"
// @Success 200 {object} model.BuildRepositoryTaskResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds/{buildId} [get]
func (h *RepositoryHandler) GetBuildRepositoryTask(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	projectName, repositoryName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c)

	ib := middleware.ImageBuildFromContext(c)

	brt := &model.BuildRepositoryTaskResult{
		ID:         fmt.Sprintf("%d", ib.ID),
		TagName:    ib.TagName,
		IsLatest:   ib.IsLatest,
		Status:     ib.Status,
		FromType:   ib.FromType,
		Dockerfile: ib.Dockerfile,
		CreateBy:   ib.Userame,
		CreateAt:   ib.CreatedAt,
		Image:      fmt.Sprintf("%s/%s:%s", projectName, repositoryName, ib.TagName),
	}
	if ib.IsLatest {
		brt.Image = fmt.Sprintf("%s/%s:latest", ib.Project, ib.Repository)
	}

	if ib.FromType == string(model.FromTypeBaseimage) {
		brt.ImageBuildConfig = &model.ImageBuildConfigBasedImage{}
		err := json.Unmarshal([]byte(ib.Dockerfile), brt.ImageBuildConfig)
		if err != nil {
			logger.Errorf("unmarshal dockerfile failed: %s", err)
			return NewResponse(http.StatusInternalServerError, "unmarshal dockerfile failed", nil, c)
		}
	}

	return NewResponse(http.StatusOK, "success", brt, c)
}

// GetBuildRepositoryTaskLog 获取镜像构建任务日志
// @Summary 获取镜像构建任务日志
// @Description 获取镜像构建任务日志
// @Tags repository
// @Accept application/json
// @Produce text/plain
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param buildId path string true "镜像构建任务ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds/{buildId}/log [get]
func (h *RepositoryHandler) GetBuildRepositoryTaskLog(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	logger.Infof("c.Params %#v", c.Params)

	instanceID, buildID := c.Param("instanceId"), c.Param("buildId")
	logger.Infof("get gcID %s", buildID)
	ib := middleware.ImageBuildFromContext(c)

	if ib.Status == model.ImageBuildStatusInitializing || ib.Status == model.ImageBuildStatusPending {
		c.Header("Content-Type", "text/plain")
		return NewResponse(http.StatusOK, "success", "build is in initializing", c)
	}

	logStr := ""

	if ib.Status == model.ImageBuildStatusRunning {
		raw, err := h.clientset.LegacyKubernetesClientset().
			CoreV1().
			Pods(instanceID).
			GetLogs("image-build-"+buildID, &v1.PodLogOptions{
				Container: "main",
				TailLines: utils.Int64Ptr(100),
			}).
			Do(c).Raw()

		if err != nil {
			logger.Errorf("get kaniko workflow log failed: %s", err)
			return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
		}

		logStr = string(raw)
	} else {
		logStr = ib.Log
	}

	c.Header("Content-Type", "text/plain")
	return NewResponse(http.StatusOK, "success", string(logStr), c)
}

// BatchDeleteBuildRepositoryTask 批量删除镜像构建任务
// @Summary 批量删除镜像构建任务
// @Description 批量删除镜像构建任务
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param body body model.BatchDeleteRequest true "镜像构建任务ID数组"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds [delete]
func (h *RepositoryHandler) BatchDeleteBuildRepositoryTask(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	instanceID := c.Param("instanceId")

	var deleteRequest model.BatchDeleteRequest
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("bind delete list item failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind delete list item", nil, c)
	}

	// convert to build id
	buildIDs := []int64{}
	for _, item := range deleteRequest.Items {
		if val, err := strconv.ParseInt(item, 10, 64); err != nil {
			logger.Warningf("item %v invalid", item)
		} else {
			buildIDs = append(buildIDs, val)
		}
	}

	if err := h.repoService.BatchDeleteBuildRepositoryTask(c, instanceID, deleteRequest.Items); err != nil {
		logger.Errorf("batch delete kaniko workflow failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	if err := h.clientset.SqlClient().Delete(buildIDs); err != nil {
		logger.Errorf("delete in database failed:%v", err)
		return NewResponse(http.StatusInternalServerError, "", nil, c)
	}

	return NewResponse(http.StatusOK, "success", "", c)
}

// DeleteBuildRepositoryTask 删除镜像构建任务
// @Summary 删除镜像构建任务
// @Description 删除镜像构建任务
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param buildId path string true "镜像构建任务ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/imagebuilds/{buildId} [delete]
func (h *RepositoryHandler) DeleteBuildRepositoryTask(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	ib := middleware.ImageBuildFromContext(c)

	instanceID := c.Param("instanceId")

	if err := h.repoService.BatchDeleteBuildRepositoryTask(c, instanceID, []string{c.Param("buildId")}); err != nil {
		logger.Errorf("batch delete kaniko workflow failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	err := h.clientset.SqlClient().Delete([]int64{ib.ID})
	if err != nil {
		logger.Errorf("delete image build %v failed: %v", ib.ID, err)
		return NewResponse(http.StatusInternalServerError, "build delete failed", nil, c)
	}

	return NewResponse(http.StatusOK, "success", "", c)
}

// HandleRepositorySwaggerErrors takes a swagger generated error as input,
// which usually does not contain any form of error message,
// and outputs a new error with a proper message.
func HandleRepositorySwaggerErrors(c *gin.Context, in error) *Response {
	t, ok := in.(*runtime.APIError)
	if ok {
		switch t.Code {
		case http.StatusBadRequest:
			return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
		case http.StatusUnauthorized:
			return NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
		case http.StatusForbidden:
			return NewResponse(http.StatusForbidden, "user does not have permission to the repository", nil, c)
		case http.StatusNotFound:
			return NewResponse(http.StatusNotFound, "repository not found", nil, c)
		case http.StatusConflict:
			return NewResponse(http.StatusConflict, "repository name already exists", nil, c)
		case http.StatusPreconditionFailed:
			return NewResponse(http.StatusPreconditionFailed, "repository precondition failed", nil, c)
		case http.StatusInternalServerError:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		default:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}

	switch in.(type) {
	case *repositoryapi.ListRepositoriesForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *repositoryapi.ListRepositoriesNotFound:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *repositoryapi.DeleteRepositoryNotFound:
		return NewResponse(http.StatusNotFound, "repository not found", nil, c)
	case *repositoryapi.DeleteRepositoryBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *repositoryapi.DeleteRepositoryForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *repositoryapi.UpdateRepositoryNotFound:
		return NewResponse(http.StatusNotFound, "repository not found", nil, c)
	case *repositoryapi.UpdateRepositoryBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *repositoryapi.UpdateRepositoryForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *repositoryapi.GetRepositoryNotFound:
		return NewResponse(http.StatusNotFound, "repository not found", nil, c)
	case *repositoryapi.GetRepositoryForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	default:
		return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
	}
}
