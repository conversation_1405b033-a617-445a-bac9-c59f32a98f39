package handler

import (
	"net/http"
	"strconv"

	_ "github.com/baidubce/bce-sdk-go/bce"
	"github.com/gin-gonic/gin"
	"github.com/go-openapi/runtime"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	projectapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type ProjectHandler struct {
	clientSet      clientset.ClientSetInterface
	projectService project.ProjectServiceInterface
}

func NewProjectHandler(clientset clientset.ClientSetInterface) *ProjectHandler {
	return &ProjectHandler{
		clientSet:      clientset,
		projectService: project.NewProjectService(),
	}
}

// CreateProject 创建命名空间
// @Summary 创建命名空间
// @Description 创建命名空间
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param project body model.CreateProjectRequest true "create project body"
// @Success 200 {object} model.ProjectResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects [post]
func (h *ProjectHandler) CreateProject(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	requestId := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	// 查询实例信息，根据实例类型，验证配额信息
	accountId := middleware.AccountIDFromContext(c)

	instanceInfo, err := h.clientSet.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	userQuota, err := h.clientSet.Usersetting().GetUserQuota(requestId, &usersetting.GetQuotaRequest{
		ServiceType: usersetting.ServiceTypeCCR,
		UserType:    usersetting.UserTypeAccountID,
		UserValue:   accountId,
		QuotaType:   usersetting.NSQuotaForSpec(instanceInfo.CCRType),
	})
	if err != nil {
		logger.Errorf("get user quotas failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	nsQuota, err := strconv.ParseInt(userQuota.Quota, 10, 64)
	if err != nil {
		logger.Errorf("parse ns quota failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	total, err := h.projectService.TotalProjects(c)
	if err != nil {
		logger.Errorf("get projects total failed: %s", err)
		return HandleProjectSwaggerErrors(c, err)
	}
	if total >= nsQuota {
		logger.Errorln("the created namespace exceeds the quota")
		return NewResponse(http.StatusForbidden, "the created namespace exceeds the quota", nil, c)
	}

	var cpr model.CreateProjectRequest
	if err := c.Bind(&cpr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	pr := &model.ProjectReq{
		ProjectName: cpr.ProjectName,
		Public:      cpr.Public,
	}

	if err := h.projectService.NewProject(c, pr); err != nil {
		logger.Errorf("new project failed: %s", err)
		return HandleProjectSwaggerErrors(c, err)
	}
	projectObj, err := h.projectService.GetProject(c, cpr.ProjectName, false)
	if err != nil {
		logger.Errorf("get project failed: %s", err)
		return HandleProjectSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", projectObj, c)
}

// GetProject 通过命名空间名称projectName查询命名空间
// @Summary 通过命名空间名称projectName查询命名空间
// @Description 通过命名空间名称projectName查询命名空间
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Success 200 {object} model.ProjectResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName} [get]
func (h *ProjectHandler) GetProject(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName := c.Param("projectName")

	projectResult, err := h.projectService.GetProject(c, projectName, false)
	if err != nil {
		logger.Errorf("get project failed: %s", err)
		return HandleProjectSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", projectResult, c)
}

// ListProject 获取当前用户的命名空间列表
// @Summary 获取当前用户的命名空间列表
// @Description 获取当前用户的命名空间列表
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName query string false "命名空间名称"
// @Param pageNo query integer false "当前页" default(1)
// @Param pageSize query integer false "每页记录数" default(10)
// @Success 200 {object} model.ListProjectResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects [get]
func (h *ProjectHandler) ListProject(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName := c.Query("projectName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	projects, total, err := h.projectService.ListProjects(c, projectName, &pageNo, &pageSize)
	if err != nil {
		logger.Errorf("list project failed: %s", err)
		return HandleProjectSwaggerErrors(c, err)
	}
	lpr := &model.ListProjectResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Projects: projects,
	}

	return NewResponse(http.StatusOK, "success", lpr, c)

}

// BatchDeleteProject 批量删除命名空间
// @Summary 批量删除命名空间
// @Description 批量删除命名空间
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.BatchDeleteRequest true "命名空间名称数组"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects [delete]
func (h *ProjectHandler) BatchDeleteProject(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	var deleteRequest model.BatchDeleteRequest
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("bind project list item failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind project list item", nil, c)
	}

	if err := h.projectService.BatchDeleteProject(c, deleteRequest.Items); err != nil {
		logger.Errorf("batch delete project failed: %s", err)
		return HandleProjectSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", nil, c)
}

// DeleteProject 删除命名空间
// @Summary 删除命名空间
// @Description 删除命名空间
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName} [delete]
func (h *ProjectHandler) DeleteProject(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName := c.Param("projectName")

	if err := h.projectService.DeleteProject(c, projectName); err != nil {
		logger.Errorf("delete project failed: %s", err)
		return HandleProjectSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", nil, c)
}

// UpdateProject 更新命名空间
// @Summary 更新命名空间
// @Description 更新命名空间
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param project body model.UpdateProjectRequest true "update project request"
// @Success 200 {object} model.ProjectResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName} [put]
func (h *ProjectHandler) UpdateProject(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	projectName := c.Param("projectName")

	var req model.UpdateProjectRequest
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	pReq := &model.ProjectReq{
		Public:      req.Public,
		AutoScan:    req.AutoScan,
		ProjectName: projectName,
	}

	if err := h.projectService.UpdateProject(c, pReq); err != nil {
		logger.Errorf("update project failed: %s", err)
		return HandleProjectSwaggerErrors(c, err)
	}

	pr, err := h.projectService.GetProject(c, projectName, false)
	if err != nil {
		logger.Errorf("get project failed: %s", err)
		return HandleProjectSwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", pr, c)
}

// HandleProjectSwaggerErrors takes a swagger generated error as input,
// which usually does not contain any form of error message,
// and outputs a new error with a proper message.
func HandleProjectSwaggerErrors(c *gin.Context, in error) *Response {
	t, ok := in.(*runtime.APIError)
	if ok {
		switch t.Code {
		case http.StatusBadRequest:
			return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
		case http.StatusUnauthorized:
			return NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
		case http.StatusForbidden:
			return NewResponse(http.StatusNotFound, "project not found", nil, c)
		case http.StatusNotFound:
			return NewResponse(http.StatusNotFound, "project not found", nil, c)
		case http.StatusConflict:
			return NewResponse(http.StatusConflict, "project name already exists", nil, c)
		case http.StatusPreconditionFailed:
			return NewResponse(http.StatusPreconditionFailed, "project precondition failed", nil, c)
		case http.StatusInternalServerError:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		default:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}

	switch in.(type) {
	case *projectapi.CreateProjectBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *projectapi.CreateProjectConflict:
		return NewResponse(http.StatusConflict, "project name already exists", nil, c)
	case *projectapi.DeleteProjectNotFound:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *projectapi.DeleteProjectBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *projectapi.DeleteProjectForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *projectapi.DeleteProjectPreconditionFailed:
		return NewResponse(http.StatusPreconditionFailed, "delete project precondition failed", nil, c)
	case *projectapi.UpdateProjectBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *projectapi.UpdateProjectNotFound:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	default:
		return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
	}
}
