package billing

import (
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	testingservice "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

func TestNewResource(t *testing.T) {
	type args struct {
		clis   clientset.ClientSetInterface
		region string
	}
	tests := []struct {
		name string
		args args
		want *Resource
	}{
		{
			name: "case1",
			args: args{
				clis:   &testingclientset.ClientSet{},
				region: "gz",
			},
			want: NewResource(&testingclientset.ClientSet{}, "gz"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, NewResource(tt.args.clis, tt.args.region), "NewResource(%v, %v)", tt.args.clis, tt.args.region)
		})
	}
}

func TestResource_Action(t *testing.T) {
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())

	type fields struct {
		clients         clientset.ClientSetInterface
		region          string
		resourceService service.ResourceServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "case1",
			fields: fields{
				clients:         &testingclientset.ClientSet{},
				region:          "gz",
				resourceService: &testingservice.ResourceService{},
			},
			args: args{c: ctx},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &Resource{
				clients:         tt.fields.clients,
				region:          tt.fields.region,
				resourceService: tt.fields.resourceService,
			}
			req, _ := http.NewRequest("POST", "/", nil)
			req.Header.Add("Content-Type", gin.MIMEJSON)

			q := req.URL.Query()
			q.Add("action", "START")
			req.URL.RawQuery = q.Encode()
			tt.args.c.Request = req

			mock.OnAnything(tt.fields.resourceService, "Start").Return(nil)

			r.Action(tt.args.c)
			assert.Equal(t, 200, tt.args.c.Writer.Status())
		})
	}
}

func TestResource_Get(t *testing.T) {
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())

	type fields struct {
		clients         clientset.ClientSetInterface
		region          string
		resourceService service.ResourceServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "case1",
			fields: fields{
				clients:         &testingclientset.ClientSet{},
				region:          "gz",
				resourceService: &testingservice.ResourceService{},
			},
			args: args{c: ctx},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &Resource{
				clients:         tt.fields.clients,
				region:          tt.fields.region,
				resourceService: tt.fields.resourceService,
			}

			req, _ := http.NewRequest("GET", "/", nil)
			req.Header.Add("Content-Type", gin.MIMEJSON)

			tt.args.c.Params = append(tt.args.c.Params, gin.Param{Key: "service", Value: "cce"})

			r.Get(tt.args.c)
			assert.Equal(t, 200, tt.args.c.Writer.Status())
		})
	}
}
