package billing

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
)

const (
	ResourceStatusStarting string = "STARTING"
	ResourceStatusStarted  string = "STARTED"

	ResourceStatusStopping string = "STOPPING"
	ResourceStatusStopped  string = "STOPPED"

	ResourceStatusDeleting string = "DELETING"
	ResourceStatusDeleted  string = "DELETED"

	ResourceStatusUnknown string = "UNKNOWN"
)

type Resource struct {
	clients         clientset.ClientSetInterface
	region          string
	resourceService service.ResourceServiceInterface
}

func NewResource(clis clientset.ClientSetInterface, region string) *Resource {

	if region == common.RegionGZTEST {
		region = common.RegionGZ
	}
	return &Resource{
		clients:         clis,
		region:          region,
		resourceService: service.NewResourceService(clis, region),
	}
}

// Get the resource
// router /{accountId}/{service}/resources/{resourceId}?region={region} [get]
func (r *Resource) Get(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	accountId, serviceType, resourceId := c.Param("accountId"), c.Param("service"), c.Param("resourceId")
	region := c.Query("region")

	if r.region == common.RegionGZTEST {
		r.region = common.RegionGZ
	}

	if region != r.region {
		logger.Errorf("[GetResource] region: %s not equal %s", region, r.region)
		c.JSON(http.StatusOK, model.ActionResourceResponse{Status: ResourceStatusUnknown})
		return
	}

	if serviceType != strings.ToLower(billing.ServiceType) {
		logger.Errorf("[GetResource] service type is: %s not equal %s", serviceType, strings.ToLower(billing.ServiceType))
		c.JSON(http.StatusOK, model.ActionResourceResponse{Status: ResourceStatusUnknown})
		return
	}

	var status string
	ccr := &v1alpha1.CCR{}
	err := r.clients.K8sClient().Get(c, client.ObjectKey{Name: resourceId}, ccr)
	if err != nil {
		logger.Errorf("[GetResource] get ccr: %s failed: %s", resourceId, err)
		if apierrors.IsNotFound(err) {
			status = "DESTROYED"
		}
		c.JSON(http.StatusInternalServerError, nil)
		return
	}

	var instanceName string
	if status != "DESTROYED" {
		instanceName = ccr.GetLabels()["name"]
		if ccr.Status.Phase == v1alpha1.CCRRunning {
			status = "RUNNING"
		}
		if ccr.Status.Phase == v1alpha1.CCRStopped {
			status = "STOPPED"
		}
	}

	resp := model.GetResourceResponse{
		ID:        resourceId,
		AccountID: accountId,
		Service:   billing.ServiceType,
		Region:    region,
		Name:      instanceName,
		Status:    status,
	}

	c.JSON(http.StatusOK, resp)
}

// Action
// router api /{accountId}/{service}/resources/{resourceId}?action={action} [put]
func (r *Resource) Action(c *gin.Context) {

	logger := gin_context.LoggerFromContext(c)

	resourceId, accountId, action := c.Param("resourceId"), c.Param("accountId"), c.Query("action")

	if r.region == common.RegionGZTEST {
		r.region = common.RegionGZ
	}

	logger.Infof("[ActionResource] billing action request: resourceId: %s, accountId: %s, action: %s", resourceId, accountId, action)

	switch action {
	case "START":
		if err := r.resourceService.Start(c, resourceId, accountId); err != nil {
			logger.Errorf("[ActionResource] action START, resourceId: %s, accountId: %s failed: %s ", resourceId, accountId, err)
			c.JSON(http.StatusOK, model.ActionResourceResponse{Status: ResourceStatusStarting})
			return
		}
		logger.Infof("[ActionResource] action START, resourceId: %s, accountId: %s ,response: %s ", resourceId, accountId, ResourceStatusStarted)
		c.JSON(http.StatusOK, model.ActionResourceResponse{Status: ResourceStatusStarted})
		break
	case "STOP":
		if err := r.resourceService.Stop(c, resourceId); err != nil {
			logger.Errorf("[ActionResource] action STOP, resourceId: %s, accountId: %s failed: %s ", resourceId, accountId, err)
			c.JSON(http.StatusOK, model.ActionResourceResponse{Status: ResourceStatusStopping})
			return
		}
		logger.Infof("[ActionResource] action STOP, resourceId: %s, accountId: %s ,response: %s ", resourceId, accountId, ResourceStatusStopped)
		c.JSON(http.StatusOK, model.ActionResourceResponse{Status: ResourceStatusStopped})
		break
	case "DELETE":
		if err := r.resourceService.Deleted(c, resourceId); err != nil {
			logger.Errorf("[ActionResource] action DELETED, resourceId: %s, accountId: %s failed: %s ", resourceId, accountId, err)
			c.JSON(http.StatusOK, model.ActionResourceResponse{Status: ResourceStatusDeleting})
			return
		}
		logger.Infof("[ActionResource] action DELETED, resourceId: %s, accountId: %s ,response: %s ", resourceId, accountId, ResourceStatusDeleted)
		c.JSON(http.StatusOK, model.ActionResourceResponse{Status: ResourceStatusDeleted})
		break
	default:
		logger.Errorf("[ActionResource] action is: %s not in %s", action, "START|STOP|DELETE")
		c.JSON(http.StatusBadRequest, model.ActionResourceResponse{Status: ResourceStatusUnknown})
	}
}
