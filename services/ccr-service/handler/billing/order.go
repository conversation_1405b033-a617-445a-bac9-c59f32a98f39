package billing

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

// order status
const (
	OrderStatusReadyForCreate = "READY_FOR_CREATE"
	OrderStatusCreating       = "CREATING"
	OrderStatusCreated        = "CREATED"
	OrderStatusCreateFailed   = "CREATE_FAILED"

	InstanceStatusInit    = "INIT"
	InstanceStatusRunning = "RUNNING"
)

type Order struct {
	clients clientset.ClientSetInterface
	region  string

	orderService service.OrderServiceInterface
}

func NewOrder(clis clientset.ClientSetInterface, region string) *Order {
	return &Order{
		clients:      clis,
		region:       region,
		orderService: service.NewOrderService(clis, region),
	}
}

func (o *Order) Execute(c *gin.Context) {

	logger := gin_context.LoggerFromContext(c)
	requestId := gin_context.RequestIdFromContext(c)

	if o.region == common.RegionGZTEST {
		o.region = common.RegionGZ
	}

	orderId := middleware.OrderIDFromContext(c)

	// 查询billing订单
	resp, err := o.clients.OrderClient().GetOrderDetail(requestId, orderId)
	if err != nil {
		logger.Errorf("[order-execute] get order detail failed: %s", err)
		c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusReadyForCreate})
		return
	}
	logger.Infof("[order-execute] orderId: %s, orderType: %s", orderId, resp.Type)

	// 判断订单类型
	switch resp.Type {
	case billing.OrderTypeNEW:
		if err := o.orderService.New(c, resp); err != nil {
			logger.Errorf("[order-execute] new instance failed: %s", err)
			c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusReadyForCreate})
			return
		}
	case billing.OrderTypeRENEW:
		if err := o.orderService.Renew(c, resp); err != nil {
			logger.Errorf("[order-execute] renew instance failed: %s", err)
			c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusReadyForCreate})
			return
		}
	case billing.OrderTypeREDILATATION:
		if err := o.orderService.Dilatation(c, resp); err != nil {
			logger.Errorf("[order-execute] upgrade instance failed: %s", err)
			c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusReadyForCreate})
			return
		}
	}
	c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusCreating})
}

func (o *Order) Check(c *gin.Context) {

	logger := gin_context.LoggerFromContext(c)
	requestId := gin_context.RequestIdFromContext(c)
	orderId := c.Query("orderId")
	serviceType := c.Query("serviceType")

	if o.region == common.RegionGZTEST {
		o.region = common.RegionGZ
	}

	if serviceType != billing.ServiceType {
		logger.Errorf("serviceType is not CCR")
		c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusCreateFailed})
		return
	}

	// 查询billing订单
	order, err := o.clients.OrderClient().GetOrderDetail(requestId, orderId)
	if err != nil {
		logger.Errorf("[order-check] get order detail failed: %s", err)
		c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusCreateFailed})
		return
	}
	logger.Infof("[order-check] orderId: %s, orderType: %s", orderId, order.Type)

	// 判断订单类型
	switch order.Type {
	case billing.OrderTypeNEW:
		if err := o.orderService.CheckNew(c, order); err != nil {
			logger.Errorf("[order-check] new instance failed: %s", err)
			c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusCreateFailed})
			return
		}
	case billing.OrderTypeRENEW:
		if err := o.orderService.CheckReNew(c, order); err != nil {
			logger.Errorf("[order-check] renew instance failed: %s", err)
			c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusCreateFailed})
			return
		}
	case billing.OrderTypeREDILATATION:
		if err := o.orderService.CheckDilatation(c, order); err != nil {
			logger.Errorf("[order-check] upgrade instance failed: %s", err)
			c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusCreateFailed})
			return
		}
	}

	c.JSON(http.StatusOK, &model.OrderResponse{ExecutionStatus: OrderStatusCreated})
}
