package billing

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	testbilling "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/billing"
	testingservice "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

type GinResponseWriter struct {
	http.ResponseWriter
}

func (g *GinResponseWriter) CloseNotify() <-chan bool {
	return make(chan bool)
}

func newGinResponseWriter() http.ResponseWriter {
	return &GinResponseWriter{httptest.NewRecorder()}
}

func TestNewOrder(t *testing.T) {
	type args struct {
		clis   clientset.ClientSetInterface
		region string
	}
	tests := []struct {
		name string
		args args
		want *Order
	}{
		{
			name: "case1",
			args: args{
				clis:   &testingclientset.ClientSet{},
				region: "gz",
			},
			want: NewOrder(&testingclientset.ClientSet{}, "gz"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, NewOrder(tt.args.clis, tt.args.region), "NewOrder(%v, %v)", tt.args.clis, tt.args.region)
		})
	}
}

func TestOrder_Check(t *testing.T) {

	ctx, _ := gin.CreateTestContext(newGinResponseWriter())

	type fields struct {
		clients      clientset.ClientSetInterface
		region       string
		orderService service.OrderServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "case1",
			fields: fields{
				clients:      &testingclientset.ClientSet{},
				region:       "gz",
				orderService: &testingservice.OrderService{},
			},
			args: args{c: ctx},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &Order{
				clients:      tt.fields.clients,
				region:       tt.fields.region,
				orderService: tt.fields.orderService,
			}

			req, _ := http.NewRequest("GET", "/", nil)

			q := req.URL.Query()
			q.Add("orderId", "xxx")
			q.Add("serviceType", "CCR")
			req.URL.RawQuery = q.Encode()
			tt.args.c.Request = req

			orderClient := &testbilling.OrderClient{}

			mock.OnAnything(tt.fields.clients, "OrderClient").Return(orderClient, nil)
			mock.OnAnything(tt.fields.clients.OrderClient(), "GetOrderDetail").Return(&billing.GetOrderDetail{Type: billing.OrderTypeNEW}, nil)
			mock.OnAnything(tt.fields.orderService, "CheckNew").Return(nil)

			o.Check(tt.args.c)
			assert.Equal(t, 200, tt.args.c.Writer.Status())
		})
	}
}

func TestOrder_Execute(t *testing.T) {
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())

	type fields struct {
		clients      clientset.ClientSetInterface
		region       string
		orderService service.OrderServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "case1",
			fields: fields{
				clients:      &testingclientset.ClientSet{},
				region:       "gz",
				orderService: &testingservice.OrderService{},
			},
			args: args{c: ctx},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &Order{
				clients:      tt.fields.clients,
				region:       tt.fields.region,
				orderService: tt.fields.orderService,
			}
			eo := model.ExecuteOrderRequest{
				OrderId: "xxxx",
			}

			data, err := json.Marshal(eo)
			if err != nil {
				t.Errorf("json marshal failed: %s", err)
			}

			req, _ := http.NewRequest("POST", "/", bytes.NewReader(data))
			req.Header.Add("Content-Type", gin.MIMEJSON)

			q := req.URL.Query()
			q.Add("orderId", "xxx")
			q.Add("serviceType", "CCR")
			req.URL.RawQuery = q.Encode()
			tt.args.c.Request = req

			orderClient := &testbilling.OrderClient{}

			mock.OnAnything(tt.fields.clients, "OrderClient").Return(orderClient, nil)
			mock.OnAnything(tt.fields.clients.OrderClient(), "GetOrderDetail").Return(&billing.GetOrderDetail{Type: billing.OrderTypeNEW}, nil)
			mock.OnAnything(tt.fields.orderService, "New").Return(nil)

			o.Execute(tt.args.c)
			assert.Equal(t, 200, tt.args.c.Writer.Status())
		})
	}
}
