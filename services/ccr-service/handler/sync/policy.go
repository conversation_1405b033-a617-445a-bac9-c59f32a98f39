package sync

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-openapi/runtime"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	productapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/replication"
	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	ccrmodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/registry"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/replication"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type PolicyHandler struct {
	clients         clientset.ClientSetInterface
	projectService  *project.ProjectService
	registryService *registry.RegistryService
	policyService   *replication.PolicyService
	instanceService service.InstanceServiceInterface
}

func NewPolicyHandler(clis clientset.ClientSetInterface, region string, endpoints map[string]string) *PolicyHandler {
	return &PolicyHandler{
		clients:         clis,
		projectService:  project.NewProjectService(),
		registryService: registry.NewRegistryService(),
		policyService:   replication.NewPolicyService(),
		instanceService: service.NewInstanceService(clis, region, endpoints),
	}
}

// CreatePolicy 创建实例同步策略
// @Summary 创建实例同步策略
// @Description 创建实例同步策略
// @Tags sync
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param userId query string false "用户id"
// @Param Sync body model.SyncRequest true "create sync body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/syncs [post]
func (h *PolicyHandler) CreatePolicy(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	instanceId := c.Param("instanceId")

	var req model.SyncRequest
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}
	destInstanceId := req.DestInstanceID
	destRegion := req.DestRegion

	if destRegion == "" {
		destRegion = common.GetRegionFromInstanceID(destInstanceId)
		if destRegion == "" {
			logger.Errorf("get region from instanceID failed: dest region is empty")
			return handler.NewResponse(http.StatusBadRequest, "get region from instanceID failed: dest region is empty", nil, c)
		}
	}

	if destRegion == common.RegionGZTEST {
		destRegion = common.RegionGZ
	}

	accountId, userId, username := middleware.AccountIDFromContext(c), middleware.UserIDFromContext(c), middleware.UserNameFromContext(c)

	logger.Infof("create sync policy with accountId: %s, userId: %s, userName: %s", accountId, userId, username)

	instanceInfo, err := h.clients.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}
	if instanceInfo.CCRType == "BASIC" {
		return handler.NewResponse(http.StatusForbidden, "Instance synchronization is not open to CCR type basic", nil, c)
	}

	// step1. 检查命名空间是否存在
	if err := h.projectService.HeadProject(c, req.SrcProjectName); err != nil {
		logger.Errorf("get project failed: %s", err)
		return handler.NewResponse(http.StatusNotFound, "the project does not exist", nil, c)
	}

	// step2. 先查询目标实例信息
	destInstances, err := h.instanceService.ListInstances(c, accountId, userId, "id", destInstanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	var destInstance *model.InstanceInfo
	for _, v := range destInstances {
		if v.ID == destInstanceId {
			destInstance = v
			break
		}
	}

	logger.Infof("dest instance found: %s", destInstanceId)
	if destInstance == nil {
		logger.Errorf("instance %v is not found", destInstance)
		return handler.NewResponse(http.StatusNotFound, "dest instance is not found", nil, c)
	}

	// hack the url as inter-sync url
	dstURL := destInstanceId + "-inter-sync-" + destInstance.Region
	if err := h.createSyncDestinationService(c, dstURL, instanceId, destInstance.Region); err != nil {
		logger.Errorf("createSyncDestinationService failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "destination unavailable", nil, c)
	}

	// step3. 创建临时密码
	accessSecret, err := h.generateTemporaryToken(c, accountId, userId, destRegion, destInstanceId)
	if err != nil {
		logger.Errorf("generate temporary access secret failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	// step4. 创建临时token
	if err := h.patchTemporaryCredential(c, destInstanceId, instanceId, accountId, userId, username); err != nil {
		logger.Errorf("patch temporary credential failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	// step5. 创建目标远程仓库 使用 destInstanceId+username 做唯一性
	destRegistry, err := h.createDestRegistry(c, destInstanceId, username, accessSecret, dstURL)
	if err != nil {
		logger.Errorf("create registry failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	// step6. 创建实例同步实例同步策略
	policyReq := &harbormodel.ReplicationPolicy{
		Name:          req.Name,
		Description:   req.Description,
		DestRegistry:  destRegistry,
		DestNamespace: req.DestProjectName,
		Filters:       srcFilter(req),
		Trigger: &harbormodel.ReplicationTrigger{
			Type: req.Trigger.Type,
		},
		Enabled:  true,
		Deletion: false,
		Override: req.Override,
	}

	if _, err = h.policyService.NewPolicy(c, ccrmodel.PolicyForSync, policyReq); err != nil {
		logger.Errorf("new policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// UpdatePolicy 更新实例同步策略
// @Summary 更新实例同步策略
// @Description 更新实例同步策略
// @Tags sync
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "实例同步策略名称ID"
// @Param userId query string false "用户id"
// @Param Sync body model.SyncRequest true "update sync body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/syncs/{policyId} [put]
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	instanceId := c.Param("instanceId")
	policyId, err := strconv.ParseInt(c.Param("policyId"), 10, 64)
	if err != nil {
		logger.Errorf("policy ID is invalid")
		return handler.NewResponse(http.StatusBadRequest, "policy ID is invalid", nil, c)
	}
	var req model.SyncRequest
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}
	destInstanceId := req.DestInstanceID
	destRegion := req.DestRegion

	if destRegion == "" {
		destRegion = common.GetRegionFromInstanceID(destInstanceId)
		if destRegion == "" {
			logger.Errorf("get region from instanceID failed: dest region is empty")
			return handler.NewResponse(http.StatusBadRequest, "get region from instanceID failed: dest region is empty", nil, c)
		}
	}

	if destRegion == common.RegionGZTEST {
		destRegion = common.RegionGZ
	}

	accountId, userId, username := middleware.AccountIDFromContext(c), middleware.UserIDFromContext(c), middleware.UserNameFromContext(c)
	logger.Infof("update sync policy with accountId: %s, userId: %s, userName: %s", accountId, userId, username)

	instanceInfo, err := h.clients.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}
	if instanceInfo.CCRType == "BASIC" {
		return handler.NewResponse(http.StatusForbidden, "Instance synchronization is not open to CCR type basic", nil, c)
	}

	// step1. 先查询实例同步策略
	if _, err := h.policyService.GetPolicy(c, policyId); err != nil {
		logger.Errorf("get policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	// step2. 检查命名空间是否存在
	if err = h.projectService.HeadProject(c, req.SrcProjectName); err != nil {
		logger.Errorf("get project failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	// step3. 先查询目标实例信息
	destInstances, err := h.instanceService.ListInstances(c, accountId, userId, "id", destInstanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	var destInstance *model.InstanceInfo
	for _, v := range destInstances {
		if v.ID == destInstanceId {
			destInstance = v
			break
		}
	}

	// step4. 创建临时密码
	accessSecret, err := h.generateTemporaryToken(c, accountId, userId, destRegion, destInstanceId)
	if err != nil {
		logger.Errorf("generate temporary access secret failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	// step5. 创建临时token
	if err := h.patchTemporaryCredential(c, destInstanceId, instanceId, accountId, userId, username); err != nil {
		logger.Errorf("patch temporary credential failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	// step6. 创建目标远程仓库
	destRegistry, err := h.createDestRegistry(c, destInstanceId, username, accessSecret, destInstance.PublicURL)
	if err != nil {
		logger.Errorf("create registry failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	// step6. 更新实例同步实例同步策略
	policyReq := &harbormodel.ReplicationPolicy{
		Name:          req.Name,
		Description:   req.Description,
		DestRegistry:  destRegistry,
		DestNamespace: req.DestProjectName,
		Filters:       srcFilter(req),
		Trigger: &harbormodel.ReplicationTrigger{
			Type: req.Trigger.Type,
		},
		Enabled:  true,
		Deletion: false,
		Override: req.Override,
	}

	if err = h.policyService.UpdatePolicy(c, policyId, ccrmodel.PolicyForSync, policyReq); err != nil {
		logger.Errorf("update policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// ListPolicies 获取实例同步策略列表
// @Summary 获取实例同步策略列表
// @Description 获取实例同步策略列表
// @Tags sync
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyName query string false "实例同步策略名称"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListSyncResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/syncs [get]
func (h *PolicyHandler) ListPolicies(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	instanceId := c.Param("instanceId")

	instanceInfo, err := h.clients.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	policyName := c.Query("policyName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return handler.NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return handler.NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	policies, total, err := h.policyService.ListSyncPolicies(c, instanceInfo.PublicURL, policyName, pageNo, pageSize)
	if err != nil {
		logger.Errorf("list policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}
	lrr := &model.ListSyncResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: policies,
	}
	return handler.NewResponse(http.StatusOK, "success", lrr, c)
}

// GetPolicy 查询实例同步策略
// @Summary 查询实例同步策略
// @Description 查询实例同步策略
// @Tags sync
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "实例同步策略名称ID"
// @Success 200 {object} model.SyncPolicyResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/syncs/{policyId} [get]
func (h *PolicyHandler) GetPolicy(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	instanceId := c.Param("instanceId")

	instanceInfo, err := h.clients.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		return handler.NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	policyId, err := strconv.ParseInt(c.Param("policyId"), 10, 64)
	if err != nil {
		logger.Errorf("policy ID is invalid")
		return handler.NewResponse(http.StatusBadRequest, "policy ID is invalid", nil, c)
	}
	policyResult, err := h.policyService.GetSyncPolicy(c, instanceInfo.PublicURL, policyId)
	if err != nil {
		logger.Errorf("get policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	return handler.NewResponse(http.StatusOK, "success", policyResult, c)
}

func (h *PolicyHandler) createDestRegistry(c *gin.Context, instanceId, username, accessSecret, url string) (*harbormodel.Registry, error) {
	name := fmt.Sprintf("%s-%s", instanceId, username)

	if !strings.HasPrefix(url, "https://") {
		url = "https://" + strings.TrimPrefix(url, "http://")
	}

	return h.registryService.CreateRegistry(c, name, username, accessSecret, url,
		string(ccrmodel.RegistryTypeBaiduCCR), ccrmodel.RegistryForSync, "created by intance sync", true)
}

func srcFilter(csr model.SyncRequest) []*harbormodel.ReplicationFilter {
	filters := make([]*harbormodel.ReplicationFilter, 0)

	if csr.SrcRepository == "" {
		csr.SrcRepository = "**"
	}
	destFilter := &harbormodel.ReplicationFilter{
		Type:  "name",
		Value: fmt.Sprintf("%s/%s", csr.SrcProjectName, csr.SrcRepository),
	}
	filters = append(filters, destFilter)

	if csr.SrcTag == "" {
		csr.SrcTag = "**"
	}
	destFilter = &harbormodel.ReplicationFilter{
		Type:  "tag",
		Value: csr.SrcTag,
	}
	filters = append(filters, destFilter)

	if csr.SyncType != "" {
		if csr.SyncType != "all" {
			destFilter := &harbormodel.ReplicationFilter{
				Type:  "resource",
				Value: csr.SyncType,
			}
			filters = append(filters, destFilter)
		}
	}
	return filters
}

// DeletePolicy 删除实例同步策略
// @Summary 删除实例同步策略
// @Description 删除实例同步策略
// @Tags sync
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "策略ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/syncs/{policyId} [delete]
func (h *PolicyHandler) DeletePolicy(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	policyId, err := strconv.ParseInt(c.Param("policyId"), 10, 64)
	if err != nil {
		logger.Errorf("policy ID is invalid")
		return handler.NewResponse(http.StatusBadRequest, "policy ID is invalid", nil, c)
	}

	if err := h.policyService.DeletePolicy(c, policyId); err != nil {
		logger.Errorf("delete policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}
	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

func (h *PolicyHandler) generateTemporaryToken(c *gin.Context, accountId, userId, destRegion, instanceId string) (string, error) {
	logger := gin_context.LoggerFromContext(c)

	endpoint := "ccr." + destRegion + ".baidubce.com"
	ccrClient, err := h.clients.CCRClientForAccount(accountId, userId, endpoint)
	if err != nil {
		return "", fmt.Errorf("create ccr client failed: %s", err)
	}

	resp, err := ccrClient.CreateTemporaryPassword(instanceId, &ccr.TemporaryPasswordArgs{
		Duration: 2,
	})
	if err != nil {
		logger.Errorf("create temporary password failed: %s", err)
		return "", fmt.Errorf("create temporary password failed: %s", err)
	}

	return resp.Password, nil
}

func (h *PolicyHandler) patchTemporaryCredential(c *gin.Context, destInstanceId, instanceId, accountId, userId, username string) error {
	logger := gin_context.LoggerFromContext(c)

	var temporaryCredential v1.Secret
	if err := h.clients.K8sClient().Get(c, client.ObjectKey{Namespace: instanceId, Name: "temporary-credential"}, &temporaryCredential); err != nil {
		logger.Errorf("get temporary credential secret failed: %s", err)
		return err
	}

	obj := temporaryCredential.DeepCopy()

	usernameMap := make(map[string]*model.Credential)
	if bytes, ok := obj.Data[destInstanceId]; ok {
		if err := json.Unmarshal(bytes, &usernameMap); err != nil {
			logger.Errorf("json unmarshal usernameMap failed: %s", err)
			return err
		}
	}

	cred, err := h.clients.StsClient().GetCredential(accountId, userId)
	if err != nil {
		logger.Errorf("get credential failed: %s", err)
		return err
	}

	credential := &model.Credential{
		Ak:           cred.AccessKeyId,
		Sk:           cred.SecretAccessKey,
		SessionToken: cred.SessionToken,
		ExpiredAt:    cred.Expiration,
	}
	usernameMap[username] = credential

	usernames, err := json.Marshal(usernameMap)
	if err != nil {
		logger.Errorf("json marshal instanceMap failed: %s", err)
		return err
	}

	if obj.Data != nil {
		obj.Data[destInstanceId] = usernames
	} else {
		obj.Data = map[string][]byte{destInstanceId: usernames}
	}

	objPatch := client.MergeFrom(temporaryCredential.DeepCopyObject().(client.Object))
	if err := h.clients.K8sClient().Patch(c, obj, objPatch); err != nil {
		logger.Errorf("update k8s secret failed: %s", err)
		return err
	}
	return nil
}

func (h *PolicyHandler) createSyncDestinationService(ctx context.Context, dstURL, srcInstanceId, region string) error {
	svc := v1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      dstURL,
			Namespace: srcInstanceId,
		},
		Spec: v1.ServiceSpec{
			Type:         v1.ServiceTypeExternalName,
			ExternalName: "ccr-inter-sync-" + region + ".ccr-system.svc.cluster.local",
		},
	}
	err := h.clients.K8sClient().Create(ctx, &svc)
	if err != nil && !apierrors.IsAlreadyExists(err) {
		return err
	}

	return nil
}

// HandlePolicySwaggerErrors takes a swagger generated error as input,
// which usually does not contain any form of error message,
// and outputs a new error with a proper message.
func HandlePolicySwaggerErrors(c *gin.Context, in error) *handler.Response {
	t, ok := in.(*runtime.APIError)
	if ok {
		switch t.Code {
		case http.StatusBadRequest:
			return handler.NewResponse(http.StatusBadRequest, "invalid request", nil, c)
		case http.StatusUnauthorized:
			return handler.NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
		case http.StatusForbidden:
			return handler.NewResponse(http.StatusForbidden, "user does not have permission to the policy", nil, c)
		case http.StatusNotFound:
			return handler.NewResponse(http.StatusNotFound, "policy not found", nil, c)
		case http.StatusConflict:
			return handler.NewResponse(http.StatusConflict, "policy name already exists", nil, c)
		case http.StatusPreconditionFailed:
			return handler.NewResponse(http.StatusPreconditionFailed, "policy precondition failed", nil, c)
		case http.StatusInternalServerError:
			return handler.NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		default:
			return handler.NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}

	switch in.(type) {
	case *productapi.CreateReplicationPolicyBadRequest:
		return handler.NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *productapi.CreateReplicationPolicyConflict:
		return handler.NewResponse(http.StatusConflict, "policy name already exists", nil, c)
	case *productapi.DeleteReplicationPolicyNotFound:
		return handler.NewResponse(http.StatusNotFound, "policy not found", nil, c)
	case *productapi.DeleteReplicationPolicyPreconditionFailed:
		return handler.NewResponse(http.StatusPreconditionFailed, "delete policy precondition failed", nil, c)
	case *productapi.UpdateReplicationPolicyNotFound:
		return handler.NewResponse(http.StatusNotFound, "policy not found", nil, c)
	case *productapi.UpdateReplicationPolicyConflict:
		return handler.NewResponse(http.StatusConflict, "policy name already exists", nil, c)
	default:
		return handler.NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
	}
}
