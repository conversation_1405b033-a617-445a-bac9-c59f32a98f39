package sync

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"k8s.io/apimachinery/pkg/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/registry"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/replication"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
	testingccr "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/ccr"
	testinglisters "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/listers"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

type GinResponseWriter struct {
	http.ResponseWriter
}

func (g *GinResponseWriter) CloseNotify() <-chan bool {
	return make(chan bool)
}

func newGinResponseWriter() http.ResponseWriter {
	return &GinResponseWriter{httptest.NewRecorder()}
}

func newMockPolicyHandler() *PolicyHandler {
	return &PolicyHandler{
		clients: &testingclientset.ClientSet{},
	}
}

func Test_createSyncDestinationService(t *testing.T) {
	cs := testingclientset.NewMockClientSetInterface(gomock.NewController(t))
	scheme := runtime.NewScheme()
	clientgoscheme.AddToScheme(scheme)

	cs.EXPECT().K8sClient().Return(fake.NewFakeClientWithScheme(scheme))

	ph := &PolicyHandler{
		clients: cs,
	}

	err := ph.createSyncDestinationService(context.Background(), "test", "ccr-srcid", "test")
	assert.NoError(t, err)
}

func TestPolicyHandler_generateTemporaryToken(t *testing.T) {
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())

	ph := &PolicyHandler{
		clients: &testingclientset.ClientSet{},
	}

	ccrClient := &testingccr.Client{}
	mock.OnAnything(ph.clients, "CCRClientForAccount").Return(ccrClient, nil)
	mock.OnAnything(ccrClient, "CreateTemporaryPassword").Return(&ccr.TemporaryPasswordResponse{
		Password: "foo",
	}, nil)

	password, err := ph.generateTemporaryToken(ctx, "xxx", "xxxx", "ccr-1xxxxxxx", "")
	assert.NoError(t, err)
	assert.NotNil(t, password)
	assert.Equal(t, password, "foo")

	ccrClient = &testingccr.Client{}
	ph.clients = &testingclientset.ClientSet{}
	mock.OnAnything(ph.clients, "CCRClientForAccount").Return(nil, errors.New("create ccr client failed"))
	password, err = ph.generateTemporaryToken(ctx, "xxx", "xxxx", "ccr-1xxxxxxx", "")
	assert.NotNil(t, err)

	ccrClient = &testingccr.Client{}
	ph.clients = &testingclientset.ClientSet{}
	mock.OnAnything(ph.clients, "CCRClientForAccount").Return(ccrClient, nil)
	mock.OnAnything(ccrClient, "CreateTemporaryPassword").Return(nil, fmt.Errorf("create temporary password failed"))
	password, err = ph.generateTemporaryToken(ctx, "xxx", "xxxx", "ccr-1xxxxxxx", "")
	assert.NotNil(t, err)

}

func TestPolicyHandler_CreatePolicy(t *testing.T) {
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())

	type fields struct {
		clients         clientset.ClientSetInterface
		projectService  *project.ProjectService
		registryService *registry.RegistryService
		policyService   *replication.PolicyService
		instanceService service.InstanceServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "case1",
			fields: fields{
				clients: &testingclientset.ClientSet{},
			},
			args: args{
				c: ctx,
			},
		},
		{
			name: "case2",
			fields: fields{
				clients: &testingclientset.ClientSet{},
			},
			args: args{
				c: ctx,
			},
		},
	}
	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {

			if tt.name == "case1" {
				ph := &PolicyHandler{
					clients: tt.fields.clients,
				}
				eo := model.SyncRequest{
					Name:           "case1",
					SrcProjectName: "library",
					SyncType:       "all",
					DestRegion:     "bj",
					DestInstanceID: "ccr-2xxxxxxxx",
				}

				data, err := json.Marshal(eo)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				req, _ := http.NewRequest("POST", "/", bytes.NewReader(data))
				req.Header.Add("Content-Type", gin.MIMEJSON)

				ctx.Set(middleware.ACCOUNT_ID_IDENTITY, "100")
				ctx.Set(middleware.ACCOUNT_NAME_IDENTITY, "foo")
				ctx.Set(middleware.USER_DI_IDENTITY, "200")
				ctx.Set(middleware.USER_NAME_IDENTITY, "bar")

				lister := &testinglisters.MockLister{}
				mock.OnAnything(tt.fields.clients, "Lister").Return(lister)
				mock.OnAnything(lister, "GetInstanceInfo").Return(&listers.InstanceInfo{
					CCRType: "BASIC",
				}, nil)

				tt.args.c.Request = req
				ph.CreatePolicy(tt.args.c)
				assert.Equal(t, 200, tt.args.c.Writer.Status())

			}
			if tt.name == "case2" {
				ph := &PolicyHandler{
					clients: tt.fields.clients,
				}
				eo := model.SyncRequest{
					Name:           "case1",
					SrcProjectName: "library",
					SyncType:       "all",
					DestRegion:     "bj",
					DestInstanceID: "ccr-2xxxxxxxx",
				}

				data, err := json.Marshal(eo)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				req, _ := http.NewRequest("POST", "/", bytes.NewReader(data))
				req.Header.Add("Content-Type", gin.MIMEJSON)

				ctx.Set(middleware.ACCOUNT_ID_IDENTITY, "100")
				ctx.Set(middleware.ACCOUNT_NAME_IDENTITY, "foo")
				ctx.Set(middleware.USER_DI_IDENTITY, "200")
				ctx.Set(middleware.USER_NAME_IDENTITY, "bar")

				lister := &testinglisters.MockLister{}
				mock.OnAnything(tt.fields.clients, "Lister").Return(lister)
				mock.OnAnything(lister, "GetInstanceInfo").Return(&listers.InstanceInfo{
					CCRType: "BASIC",
				}, nil)

				tt.args.c.Request = req
				ph.CreatePolicy(tt.args.c)
				assert.Equal(t, 200, tt.args.c.Writer.Status())
			}
		})
	}
}

func TestPolicyHandler_UpdatePolicy(t *testing.T) {
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	type fields struct {
		clients         clientset.ClientSetInterface
		projectService  *project.ProjectService
		registryService *registry.RegistryService
		policyService   *replication.PolicyService
		instanceService service.InstanceServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "case1",
			fields: fields{
				clients: &testingclientset.ClientSet{},
			},
			args: args{
				c: ctx,
			},
		},
		{
			name: "case2",
			fields: fields{
				clients: &testingclientset.ClientSet{},
			},
			args: args{
				c: ctx,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name == "case1" {
				ph := &PolicyHandler{
					clients: tt.fields.clients,
				}
				eo := model.SyncRequest{
					Name:           "case1",
					SrcProjectName: "library",
					SyncType:       "all",
					DestRegion:     "",
					DestInstanceID: "ccr-2xxxxxxxx",
				}

				data, err := json.Marshal(eo)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				req, _ := http.NewRequest("POST", "/", bytes.NewReader(data))
				req.Header.Add("Content-Type", gin.MIMEJSON)
				tt.args.c.Params = gin.Params{
					gin.Param{Key: "instanceId", Value: "ccr-2xxxxxxx"},
					gin.Param{Key: "policyId", Value: "100"},
				}

				ctx.Set(middleware.ACCOUNT_ID_IDENTITY, "100")
				ctx.Set(middleware.ACCOUNT_NAME_IDENTITY, "foo")
				ctx.Set(middleware.USER_DI_IDENTITY, "200")
				ctx.Set(middleware.USER_NAME_IDENTITY, "bar")

				lister := &testinglisters.MockLister{}
				mock.OnAnything(tt.fields.clients, "Lister").Return(lister)
				mock.OnAnything(lister, "GetInstanceInfo").Return(&listers.InstanceInfo{
					CCRType: "BASIC",
				}, nil)

				tt.args.c.Request = req
				ph.UpdatePolicy(tt.args.c)
				assert.Equal(t, 200, tt.args.c.Writer.Status())
			}

			if tt.name == "case2" {
				ph := &PolicyHandler{
					clients: tt.fields.clients,
				}
				eo := model.SyncRequest{
					Name:           "case1",
					SrcProjectName: "library",
					SyncType:       "all",
					DestRegion:     "",
					DestInstanceID: "ccr-2xxxxxxxx",
				}

				data, err := json.Marshal(eo)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				req, _ := http.NewRequest("POST", "/", bytes.NewReader(data))
				req.Header.Add("Content-Type", gin.MIMEJSON)
				tt.args.c.Params = gin.Params{
					gin.Param{Key: "instanceId", Value: "ccr-2xxxxxxx"},
					gin.Param{Key: "policyId", Value: "100"},
				}

				ctx.Set(middleware.ACCOUNT_ID_IDENTITY, "100")
				ctx.Set(middleware.ACCOUNT_NAME_IDENTITY, "foo")
				ctx.Set(middleware.USER_DI_IDENTITY, "200")
				ctx.Set(middleware.USER_NAME_IDENTITY, "bar")

				lister := &testinglisters.MockLister{}
				mock.OnAnything(tt.fields.clients, "Lister").Return(lister)
				mock.OnAnything(lister, "GetInstanceInfo").Return(&listers.InstanceInfo{
					CCRType: "BASIC",
				}, nil)

				tt.args.c.Request = req
				ph.UpdatePolicy(tt.args.c)
				assert.Equal(t, 200, tt.args.c.Writer.Status())
			}
		})
	}
}
