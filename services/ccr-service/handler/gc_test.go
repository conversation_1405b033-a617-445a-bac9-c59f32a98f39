package handler

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	ccrmodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/gc"
	mackgc "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service/harbor/gc"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
)

// TestGCHandler_CreateGCSchedule test create gc schedule
func TestGCHandler_CreateGCSchedule(t *testing.T) {

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type fields struct {
		gcService gc.GCServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		{
			name: "deleteUntagged is not true",
			fields: func() fields {
				return fields{
					gcService: mackgc.NewMockGCServiceInterface(ctrl),
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

				csr := ccrmodel.CreateGCRequest{
					DeleteUntagged: false,
					DryRun:         true,
					Type:           "Manual",
				}
				data, err := json.Marshal(csr)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				ctx.Request, _ = http.NewRequest(http.MethodPost, "/instances/ccr-xxxxxxxx/gcs", bytes.NewReader(data))
				ctx.Request.Header.Add("Content-Type", "application/json")

				return args{
					c: ctx,
				}
			}(),
			want: 400,
		},
		{
			name: "success",
			fields: func() fields {
				gcService := mackgc.NewMockGCServiceInterface(ctrl)
				gcService.EXPECT().CreateGCSchedule(gomock.Any(), gomock.Any()).AnyTimes().Return("", nil)

				return fields{
					gcService: gcService,
				}
			}(),
			args: func() args {
				csr := ccrmodel.CreateGCRequest{
					DeleteUntagged: true,
					DryRun:         true,
					Type:           "Manual",
				}
				data, err := json.Marshal(csr)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodPost, "http://test.com/instances/ccr-xxxxxxxx/gcs", bytes.NewReader(data))
				ctx.Request.Header.Add("Content-Type", "application/json")

				return args{
					c: ctx,
				}
			}(),
			want: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := GCHandler{
				gcService: tt.fields.gcService,
			}
			h.CreateGCSchedule(tt.args.c)
			t.Logf("tt.args.c.Writer.Status(): %d", tt.args.c.Writer.Status())
			if tt.args.c.Writer.Status() != tt.want {
				t.Errorf("GCHandler.CreateGCSchedule() = %d, want %v", tt.args.c.Writer.Status(), tt.want)
			}
		})
	}
}

// TestGCHandler_GetGCHistory 测试GCHandler的GetGCHistory方法，包括参数校验、获取垃圾回收历史记录失败和成功三种情况。
func TestGCHandler_GetGCHistory(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type fields struct {
		gcService gc.GCServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		{
			name: "invalid page number",
			fields: func() fields {
				return fields{
					gcService: mackgc.NewMockGCServiceInterface(ctrl),
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?pageNo=xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			want: 400,
		},
		{
			name: "invalid page size",
			fields: func() fields {
				return fields{
					gcService: mackgc.NewMockGCServiceInterface(ctrl),
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?pageNo=1&pageSize=xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			want: 400,
		},
		{
			name: "get gc history failed",
			fields: func() fields {

				gcService := mackgc.NewMockGCServiceInterface(ctrl)
				gcService.EXPECT().ListGCHistory(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, int64(0), errors.New("get gc history failed"))
				return fields{
					gcService: gcService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?pageNo=1&pageSize=10", nil)
				return args{
					c: ctx,
				}
			}(),
			want: 500,
		},
		{
			name: "success",
			fields: func() fields {

				gcService := mackgc.NewMockGCServiceInterface(ctrl)
				gcHistoryList := make([]*ccrmodel.GCHistoryResult, 0)
				gcHistoryList = append(gcHistoryList, &ccrmodel.GCHistoryResult{
					ID: 1,
				})
				gcHistoryList = append(gcHistoryList, &ccrmodel.GCHistoryResult{
					ID: 2,
				})

				gcService.EXPECT().ListGCHistory(gomock.Any(), gomock.Any(), gomock.Any()).Return(gcHistoryList, int64(2), nil)
				return fields{
					gcService: gcService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?pageNo=1&pageSize=10", nil)
				return args{
					c: ctx,
				}
			}(),
			want: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &GCHandler{
				gcService: tt.fields.gcService,
			}
			h.ListGCHistory(tt.args.c)
			t.Logf("tt.args.c.Writer.Status(): %d", tt.args.c.Writer.Status())
			if tt.args.c.Writer.Status() != tt.want {
				t.Errorf("GCHandler.ListGCHistory() = %d, want %v", tt.args.c.Writer.Status(), tt.want)
			}
		})
	}
}

// TestGCHandler_GetGCLog 测试GCHandler的GetGCLog方法，包括成功和失败两种情况。
func TestGCHandler_GetGCLog(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type fields struct {
		gcService gc.GCServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		{
			name: "success",
			fields: func() fields {

				gcService := mackgc.NewMockGCServiceInterface(ctrl)

				gcService.EXPECT().GetGCLog(gomock.Any(), gomock.Any()).Return("", nil)
				return fields{
					gcService: gcService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "gcId", Value: "1"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com/v1/instances/ccr-xxxxxxxx/gcs/1/log", nil)

				return args{
					c: ctx,
				}
			}(),
			want: 200, // TODO 此处单测返回值不对
		},
		{
			name: "invalid gc ID",
			fields: func() fields {

				gcService := mackgc.NewMockGCServiceInterface(ctrl)
				return fields{
					gcService: gcService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com/v1/instances/ccr-xxxxxxxx/gcs/1/log", nil)

				return args{
					c: ctx,
				}
			}(),
			want: 400,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &GCHandler{
				gcService: tt.fields.gcService,
			}
			h.GetGCLog(tt.args.c)
			t.Logf("tt.args.c.Writer.Status(): %d", tt.args.c.Writer.Status())
			if tt.args.c.Writer.Status() != tt.want {
				t.Errorf("GCHandler.ListGCHistory() = %d, want %v", tt.args.c.Writer.Status(), tt.want)
			}
		})
	}
}

func TestNewGcHandler(t *testing.T) {
	tests := []struct {
		name string
		want *GCHandler
	}{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, NewGcHandler(), "NewGcHandler()")
		})
	}
}
