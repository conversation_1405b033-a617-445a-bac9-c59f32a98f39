package handler

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/baidubce/bce-sdk-go/bce"
	_ "github.com/baidubce/bce-sdk-go/bce"
	"github.com/gin-gonic/gin"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type NetworkHandler struct {
	clients clientset.ClientSetInterface
}

func NewNetworkHandler(clis clientset.ClientSetInterface) *NetworkHandler {
	return &NetworkHandler{
		clients: clis,
	}
}

// ListPrivateNetwork 获取专有网络列表
// @Summary 获取专有网络列表
// @Description 获取专有网络列表
// @Tags network
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Success 200 {object} model.PrivateNetworks "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/privatelinks [get]
func (ins *NetworkHandler) ListPrivateNetwork(c *gin.Context) {
	instanceId := c.Param("instanceId")
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	netWorkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)

	var resp model.PrivateNetworks
	instanceInfo, err := ins.clients.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	resp.Domain = instanceInfo.PrivateURL

	statusSet := make(map[string]*model.PrivateNetworksItems)
	if len(netWorkObj.Status.LinkStatus) != 0 {
		for _, v := range netWorkObj.Status.LinkStatus {
			statusSet[v.VPCID+v.SubnetID] = &model.PrivateNetworksItems{
				VpcID:          v.VPCID,
				SubnetID:       v.SubnetID,
				ServiceNetID:   v.ServiceID,
				Status:         v.Status,
				IPAddress:      v.IP,
				ResourceSource: v.ResourceSource,
			}
		}
	}

	networks := make([]model.PrivateNetworksItems, 0)

	if len(netWorkObj.Spec.PrivateLinks) != 0 {
		for _, v := range netWorkObj.Spec.PrivateLinks {
			if st, ok := statusSet[v.VPCID+v.SubnetID]; ok {
				networks = append(networks, *st)
			} else {
				networks = append(networks, model.PrivateNetworksItems{
					VpcID:          v.VPCID,
					SubnetID:       v.SubnetID,
					ServiceNetID:   "",
					Status:         "creating",
					IPAddress:      "",
					ResourceSource: "ccr",
				})
			}
		}
	}

	resp.Items = networks

	c.JSON(http.StatusOK, resp)
}

// CreatePrivateNetwork 创建一个专有网络
// @Summary 创建一个专有网络
// @Description 创建一个专有网络
// @Tags network
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.CreatePrivateLinkArgs true "创建参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/privatelinks [post]
func (ins *NetworkHandler) CreatePrivateNetwork(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	accountID := middleware.AccountIDFromContext(c)
	userID := middleware.UserIDFromContext(c)
	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)

	var req model.CreatePrivateLinkArgs
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	userQuota, err := ins.clients.Usersetting().GetUserQuota(requestID, &usersetting.GetQuotaRequest{
		ServiceType: usersetting.ServiceTypeCCR,
		UserType:    usersetting.UserTypeAccountID,
		UserValue:   accountID,
		QuotaType:   usersetting.QuotaTypeCcrVpcQuota,
	})
	if err != nil {
		logger.Errorf("get user quotas failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	vpcQuota, err := strconv.ParseInt(userQuota.Quota, 10, 64)
	if err != nil {
		logger.Errorf("parse ns quota failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	vpcTotal := int64(len(networkObj.Spec.PrivateLinks))
	if vpcTotal >= vpcQuota {
		logger.Errorln("the created vpc exceeds the quota")
		gin_context.E(c, gin_context.QuotaExceedError(requestID))
		return
	}

	if vpcTotal != 0 {
		for _, v := range networkObj.Spec.PrivateLinks {
			if v.VPCID == req.VpcID {
				logger.Errorf("already has an privatelink for %v", req.VpcID)
				gin_context.E(c, gin_context.AlreadyExist(requestID))
				return
			}
		}
	}

	vpcCli, err := ins.clients.VpcClientForAccount(accountID, userID)
	if err != nil {
		logger.Errorf("get vpc client for account %s failed: %s", accountID, err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	_, err = vpcCli.GetVPCDetail(req.VpcID)
	if err != nil {
		var bceErr *bce.BceServiceError
		logger.Errorf("get vpc details for %s failed: %s", req.VpcID, err)
		if errors.As(err, &bceErr) && bceErr.StatusCode == http.StatusNotFound {
			gin_context.E(c, gin_context.NotFoundError(requestID, "vpc is not found"))
			return
		}

		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	// TODO: subnet check???

	curObj := networkObj.DeepCopy()
	curObj.Spec.PrivateLinks = append(curObj.Spec.PrivateLinks, v1alpha1.PrivateLink{
		VPCID:     req.VpcID,
		SubnetID:  req.SubnetID,
		IPAddr:    req.IPAddress,
		AutoDNS:   req.AutoDNSResolve,
		CreatedBy: userID,
	})

	err = ins.clients.K8sClient().Patch(c, curObj, client.MergeFrom(networkObj))
	if err != nil {
		logger.Errorf("patch to object %v failed: %v", curObj.GetName(), err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, map[string]string{})
}

// UpdatePublicLink 更新公有网络
// @Summary 更新公有网络
// @Description 更新公有网络
// @Tags network
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.PublicLinkAction true "更新参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/publiclinks [put]
func (ins *NetworkHandler) UpdatePublicLink(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)

	var req model.PublicLinkAction
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	curObj := networkObj.DeepCopy()
	if req.Action == "open" {
		curObj.Spec.PublicLink.EIPOn = true
	}
	if req.Action == "close" {
		curObj.Spec.PublicLink.EIPOn = false
	}

	err := ins.clients.K8sClient().Patch(c, curObj, client.MergeFrom(networkObj))
	if err != nil {
		logger.Errorf("patch to object %v failed: %v", curObj.GetName(), err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, nil)
}

// GetPublicLink 获取公网信息
// @Summary 获取公网信息
// @Description 获取公网信息
// @Tags network
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Success 200 {object} model.PublicNetworkInfo "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/publiclinks [get]
func (ins *NetworkHandler) GetPublicLink(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	instanceId := c.Param("instanceId")
	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var resp model.PublicNetworkInfo

	instanceInfo, err := ins.clients.Lister().GetInstanceInfo(instanceId)
	if err != nil {
		logger.Errorf("get instance info failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	resp.Domain = instanceInfo.PublicURL
	if networkObj.Status.PublicStatus.Status == "" {
		resp.Status = "closed"
	} else {
		resp.Status = networkObj.Status.PublicStatus.Status
	}

	resp.Whitelist = make([]model.PublicNetworkInfoWhitelist, 0)
	if len(networkObj.Spec.PublicLink.WhiteList) != 0 {
		for _, v := range networkObj.Spec.PublicLink.WhiteList {
			resp.Whitelist = append(resp.Whitelist, model.PublicNetworkInfoWhitelist{
				IPAddr:      v.IPCidr,
				Description: v.Description,
			})
		}
	}

	c.JSON(http.StatusOK, &resp)
}

// AddWhiteList 添加白名单
// @Summary 添加白名单
// @Description 添加白名单
// @Tags network
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.PublicNetworkInfoWhitelist true "白名单信息"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/publiclinks/whitelist [post]
func (ins *NetworkHandler) AddWhiteList(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var whitelistItem model.PublicNetworkInfoWhitelist
	if err := c.BindJSON(&whitelistItem); err != nil {
		logger.Errorf("bind white list item failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	curObj := networkObj.DeepCopy()
	if curObj.Spec.PublicLink.WhiteList == nil {
		curObj.Spec.PublicLink.WhiteList = make([]v1alpha1.NetworkWhiteItem, 0)
	}

	for _, v := range curObj.Spec.PublicLink.WhiteList {
		if v.IPCidr == whitelistItem.IPAddr {
			logger.Errorf("required item %s already in whitelist", v.IPCidr)
			gin_context.E(c, gin_context.AlreadyExist(requestID))
			return
		}
	}

	curObj.Spec.PublicLink.WhiteList = append(curObj.Spec.PublicLink.WhiteList, v1alpha1.NetworkWhiteItem{
		IPCidr:      whitelistItem.IPAddr,
		Description: whitelistItem.Description,
	})

	err := ins.clients.K8sClient().Patch(c, curObj, client.MergeFrom(networkObj))
	if err != nil {
		logger.Errorf("patch ccr network failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, nil)
}

// DeleteWhitelist 删除白名单
// @Summary 删除白名单
// @Description 删除白名单
// @Tags network
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.DeleteWhiteListArgs true "白名单信息"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/publiclinks/whitelist [delete]
func (ins *NetworkHandler) DeleteWhitelist(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var deleteItemArgs model.DeleteWhiteListArgs
	if err := c.BindJSON(&deleteItemArgs); err != nil || len(deleteItemArgs.Items) == 0 {
		logger.Errorf("bind white list item failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	set := make(map[string]interface{})
	for _, v := range deleteItemArgs.Items {
		set[v] = nil
	}

	curObj := networkObj.DeepCopy()
	if curObj.Spec.PublicLink.WhiteList == nil {
		logger.Warnf("white list is empty, may be deleting")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	whiteList := make([]v1alpha1.NetworkWhiteItem, 0)
	for _, v := range curObj.Spec.PublicLink.WhiteList {
		if _, ok := set[v.IPCidr]; !ok {
			whiteList = append(whiteList, v)
		}
	}

	curObj.Spec.PublicLink.WhiteList = whiteList

	err := ins.clients.K8sClient().Patch(c, curObj, client.MergeFrom(networkObj))
	if err != nil {
		logger.Errorf("patch ccr network failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, nil)
}

// DeletePrivateLink 释放一个私有网络
// @Summary 释放一个私有网络
// @Description 释放一个私有网络
// @Tags network
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.DeletePrivateLinkArgs true "白名单信息"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/privatelinks [delete]
func (ins *NetworkHandler) DeletePrivateLink(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	networkObj := c.Value(middleware.NETWORK_OBJECT_IDENTIY).(*v1alpha1.CNCNetwork)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var privateLink model.DeletePrivateLinkArgs
	if err := c.BindJSON(&privateLink); err != nil {
		logger.Errorf("bind private link item failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	curObj := networkObj.DeepCopy()
	if len(curObj.Spec.PrivateLinks) == 0 {
		logger.Errorf("private link is empty")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	plinks := make([]v1alpha1.PrivateLink, 0)
	for _, v := range curObj.Spec.PrivateLinks {
		if v.VPCID == privateLink.VpcID && v.SubnetID == privateLink.SubnetID {
			continue
		}
		plinks = append(plinks, v)
	}
	curObj.Spec.PrivateLinks = plinks

	err := ins.clients.K8sClient().Patch(c, curObj, client.MergeFrom(networkObj))
	if err != nil {
		logger.Errorf("patch ccr network failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, nil)
}
