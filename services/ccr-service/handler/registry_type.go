package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/registry"
)

type RegistryTypeHandler struct {
	registryTypeService *registry.RegistryTypeService
}

func NewRegistryTypeHandler() *RegistryTypeHandler {
	return &RegistryTypeHandler{
		registryTypeService: registry.NewRegistryTypeService(),
	}
}

// ListRegistryTypes 获取远程仓库类型列表
// @Summary 获取远程仓库类型列表
// @Description 获取远程仓库类型列表
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Success 200 {object} map[string]model.EndpointPattern "Success"
// @Router /registrytypes [get]
func (h *RegistryTypeHandler) ListRegistryTypes(c *gin.Context) *Response {

	registryTypes := h.registryTypeService.ListRegistryTypes(c)

	return NewResponse(http.StatusOK, "success", registryTypes, c)
}
