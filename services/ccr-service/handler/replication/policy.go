package replication

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-openapi/runtime"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	productapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/replication"
	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	ccrmodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/registry"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/replication"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler"
)

type PolicyHandler struct {
	projectService  *project.ProjectService
	policyService   *replication.PolicyService
	registryService registry.RegistryServiceInterface
}

func NewPolicyHandler() *PolicyHandler {
	return &PolicyHandler{
		projectService:  project.NewProjectService(),
		policyService:   replication.NewPolicyService(),
		registryService: registry.NewRegistryService(),
	}
}

// CreatePolicy 创建镜像迁移策略
// @Summary 创建镜像迁移策略
// @Description 创建镜像迁移策略
// @Tags replication
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policy body model.PolicyRequest true "create policy body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/replications [post]
func (h *PolicyHandler) CreatePolicy(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	var cpr model.PolicyRequest
	if err := c.Bind(&cpr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	// step.1 验证project
	_, err := h.projectService.GetProject(c, cpr.DestProjectName, false)
	if err != nil {
		logger.Errorf("get project failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}
	// step.2 查询 查询src registry
	srcRegistry, err := h.registryService.GetRegistry(c, cpr.SrcRegistry.ID)
	if err != nil {
		logger.Errorf("get registory failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	filters := make([]*harbormodel.ReplicationFilter, 0)
	for ind := range cpr.Filters {
		filter := cpr.Filters[ind]
		if filter.Value == "" {
			continue
		}
		destFilter := &harbormodel.ReplicationFilter{
			Type:  filter.Type,
			Value: filter.Value,
		}
		filters = append(filters, destFilter)
	}

	policyReq := &harbormodel.ReplicationPolicy{
		Enabled:       true,
		Deletion:      false,
		Description:   cpr.Description,
		DestNamespace: cpr.DestProjectName,
		Name:          cpr.Name,
		SrcRegistry: &harbormodel.Registry{
			Credential: &harbormodel.RegistryCredential{
				Type:         srcRegistry.Credential.Type,
				AccessKey:    srcRegistry.Credential.AccessKey,
				AccessSecret: srcRegistry.Credential.AccessSecret,
			},
			Description: srcRegistry.Description,
			Insecure:    srcRegistry.Insecure,
			Name:        srcRegistry.Name,
			Type:        srcRegistry.Type,
			URL:         srcRegistry.URL,
			ID:          srcRegistry.ID,
		},
		Override: cpr.Override,
		Trigger: &harbormodel.ReplicationTrigger{
			Type: cpr.Trigger.Type,
		},
		Filters: filters,
	}

	location, err := h.policyService.NewPolicy(c, ccrmodel.PolicyForRepl, policyReq)
	if err != nil {
		logger.Errorf("new policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	return handler.NewResponse(http.StatusOK, "success", location, c)
}

// UpdatePolicy 更新镜像迁移策略
// @Summary 更新镜像迁移策略
// @Description 更新镜像迁移策略
// @Tags replication
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "策略ID"
// @Param policy body model.PolicyRequest true "update policy body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/replications/{policyId} [put]
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	policyId, err := strconv.ParseInt(c.Param("policyId"), 10, 64)
	if err != nil {
		logger.Errorf("policy ID is invalid")
		return handler.NewResponse(http.StatusBadRequest, "policy ID is invalid", nil, c)
	}

	var cpr model.PolicyRequest
	if err := c.Bind(&cpr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return handler.NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	if _, err = h.projectService.GetProject(c, cpr.DestProjectName, false); err != nil {
		logger.Errorf("get project failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	srcRegistry, err := h.registryService.GetRegistry(c, cpr.SrcRegistry.ID)
	if err != nil {
		logger.Errorf("get registory failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	if _, err = h.policyService.GetPolicy(c, policyId); err != nil {
		logger.Errorf("get policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	filters := make([]*harbormodel.ReplicationFilter, 0)
	for ind := range cpr.Filters {
		filter := cpr.Filters[ind]
		if filter.Value == "" {
			continue
		}
		destFilter := &harbormodel.ReplicationFilter{
			Type:  filter.Type,
			Value: filter.Value,
		}
		filters = append(filters, destFilter)
	}

	updatePolicy := &harbormodel.ReplicationPolicy{
		Enabled:       true,
		Deletion:      false,
		Description:   cpr.Description,
		DestNamespace: cpr.DestProjectName,
		Name:          cpr.Name,
		SrcRegistry: &harbormodel.Registry{
			Credential: &harbormodel.RegistryCredential{
				Type:         srcRegistry.Credential.Type,
				AccessKey:    srcRegistry.Credential.AccessKey,
				AccessSecret: srcRegistry.Credential.AccessSecret,
			},
			Description: srcRegistry.Description,
			Insecure:    srcRegistry.Insecure,
			Name:        srcRegistry.Name,
			Type:        srcRegistry.Type,
			URL:         srcRegistry.URL,
			ID:          srcRegistry.ID,
		},
		Override: cpr.Override,
		Trigger: &harbormodel.ReplicationTrigger{
			Type: cpr.Trigger.Type,
		},
		Filters: filters,
	}

	if err := h.policyService.UpdatePolicy(c, policyId, ccrmodel.PolicyForRepl, updatePolicy); err != nil {
		logger.Errorf("update policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}
	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// DeletePolicy 删除镜像迁移策略
// @Summary 删除镜像迁移策略
// @Description 删除镜像迁移策略
// @Tags replication
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "策略ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/replications/{policyId} [delete]
func (h *PolicyHandler) DeletePolicy(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	policyId, err := strconv.ParseInt(c.Param("policyId"), 10, 64)
	if err != nil {
		logger.Errorf("policy ID is invalid")
		return handler.NewResponse(http.StatusBadRequest, "policy ID is invalid", nil, c)
	}

	if err := h.policyService.DeletePolicy(c, policyId); err != nil {
		logger.Errorf("delete policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}
	return handler.NewResponse(http.StatusOK, "success", nil, c)
}

// ListPolicies 获取策略列表
// @Summary 获取策略列表
// @Description 获取策略列表
// @Tags replication
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyName query string false "镜像迁移策略名称"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListReplicationResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/replications [get]
func (h *PolicyHandler) ListPolicies(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	policyName := c.Query("policyName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return handler.NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return handler.NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	policies, total, err := h.policyService.ListPolicies(c, policyName, ccrmodel.CCRRepositoryReplicationPrefix, pageNo, pageSize)
	if err != nil {
		logger.Errorf("list policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}
	lrr := &model.ListReplicationResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: policies,
	}
	return handler.NewResponse(http.StatusOK, "success", lrr, c)
}

// GetPolicy 通过policyId查询策略
// @Summary 通过policyId查询策略
// @Description 通过policyId查询策略
// @Tags replication
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "镜像迁移策略名称ID"
// @Success 200 {object} model.PolicyResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/replications/{policyId} [get]
func (h *PolicyHandler) GetPolicy(c *gin.Context) *handler.Response {
	logger := gin_context.LoggerFromContext(c)

	policyId, err := strconv.ParseInt(c.Param("policyId"), 10, 64)
	if err != nil {
		logger.Errorf("policy ID is invalid")
		return handler.NewResponse(http.StatusBadRequest, "policy ID is invalid", nil, c)
	}

	policyResult, err := h.policyService.GetPolicy(c, policyId)
	if err != nil {
		logger.Errorf("get policy failed: %s", err)
		return HandlePolicySwaggerErrors(c, err)
	}

	return handler.NewResponse(http.StatusOK, "success", policyResult, c)
}

// HandlePolicySwaggerErrors takes a swagger generated error as input,
// which usually does not contain any form of error message,
// and outputs a new error with a proper message.
func HandlePolicySwaggerErrors(c *gin.Context, in error) *handler.Response {
	t, ok := in.(*runtime.APIError)
	if ok {
		switch t.Code {
		case http.StatusBadRequest:
			return handler.NewResponse(http.StatusBadRequest, "invalid request", nil, c)
		case http.StatusUnauthorized:
			return handler.NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
		case http.StatusForbidden:
			return handler.NewResponse(http.StatusForbidden, "user does not have permission to the policy", nil, c)
		case http.StatusNotFound:
			return handler.NewResponse(http.StatusNotFound, "policy not found", nil, c)
		case http.StatusConflict:
			return handler.NewResponse(http.StatusConflict, "policy name already exists", nil, c)
		case http.StatusPreconditionFailed:
			return handler.NewResponse(http.StatusPreconditionFailed, "policy precondition failed", nil, c)
		case http.StatusInternalServerError:
			return handler.NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		default:
			return handler.NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}

	switch in.(type) {
	case *productapi.CreateReplicationPolicyBadRequest:
		return handler.NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *productapi.CreateReplicationPolicyConflict:
		return handler.NewResponse(http.StatusConflict, "policy name already exists", nil, c)
	case *productapi.DeleteReplicationPolicyNotFound:
		return handler.NewResponse(http.StatusNotFound, "policy not found", nil, c)
	case *productapi.DeleteReplicationPolicyPreconditionFailed:
		return handler.NewResponse(http.StatusPreconditionFailed, "delete policy precondition failed", nil, c)
	case *productapi.UpdateReplicationPolicyNotFound:
		return handler.NewResponse(http.StatusNotFound, "policy not found", nil, c)
	case *productapi.UpdateReplicationPolicyConflict:
		return handler.NewResponse(http.StatusConflict, "policy name already exists", nil, c)
	default:
		return handler.NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
	}
}
