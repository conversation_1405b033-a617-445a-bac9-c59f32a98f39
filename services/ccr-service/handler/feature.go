package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type FeatureHandler struct {
	clients     *clientset.ClientSet
	enableCheck bool
	region      string
}

func NewFeatureHandler(clis *clientset.ClientSet, region string, enableCheck bool) *FeatureHandler {
	return &FeatureHandler{clients: clis, region: region, enableCheck: enableCheck}
}

// CheckFeatureACL 查询用户是否在白名单内
// @Summary 查询用户是否在白名单内
// @Description 查询用户是否在白名单内
// @Tags feature
// @Accept application/json
// @Produce application/json
// @Param featureType query string true "功能类型"
// @Success 200 {boolean} true "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /features [get]
func (h *FeatureHandler) CheckFeatureACL(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	requestId := gin_context.RequestIdFromContext(c)
	accountId := middleware.AccountIDFromContext(c)

	if !h.enableCheck {
		return NewResponse(http.StatusOK, "success", true, c)
	}

	featureType := c.Query("featureType")
	if len(featureType) == 0 {
		logger.Errorf("featureType is invalid")
		return NewResponse(http.StatusBadRequest, "featureType is invalid", nil, c)
	}

	resp, err := h.clients.Usersetting().CheckFeatureACL(requestId, &usersetting.FeatureAclRequest{
		FeatureType: featureType,
		Region:      h.region,
		AclType:     "AccountId",
		AclName:     accountId,
	})
	if err != nil {
		logger.Errorf("checkout user feature acl falied: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	return NewResponse(http.StatusOK, "success", resp.IsExist, c)
}
