package handler

import (
	"fmt"
	"net/http"
	"strings"
	"testing"

	_ "github.com/baidubce/bce-sdk-go/bce"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/repository"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
	mocklisters "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/listers"
	mockmodels "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/models/mock"
	mockservice "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service"
	mockrepo "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service/harbor/repository"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
	mockclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

func TestRepositoryHandler_ListAllRepository(t *testing.T) {
	type fields struct {
		repositoryService repository.Interface
		clientset         clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		// TODO: Add test cases.
		{
			name:   "invalid page number",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?repositoryName=xxx&pageNo=xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name:   "invalid page size",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?repositoryName=xxx&pageNo=1&pageSize=xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name: "get lister failed",
			fields: func() fields {
				lis := &mocklisters.MockLister{}
				lis.On("GetInstanceInfo", mock.Anything).Return(nil, fmt.Errorf("failed"))
				cli := &mockclientset.ClientSet{}
				cli.On("Lister").Return(lis)

				return fields{
					clientset: cli,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?repositoryName=xxx&pageNo=1&pageSize=100", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 500,
		},
		{
			name: "list repository failed",
			fields: func() fields {
				lis := &mocklisters.MockLister{}
				lis.On("GetInstanceInfo", mock.Anything).Return(&listers.InstanceInfo{}, nil)
				cli := &mockclientset.ClientSet{}
				cli.On("Lister").Return(lis)

				repoSvc := mockrepo.NewMockInterface(gomock.NewController(t))
				repoSvc.EXPECT().ListAllRepositories(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, int64(0), fmt.Errorf("failed"))
				return fields{
					repositoryService: repoSvc,
					clientset:         cli,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?repositoryName=xxx&pageNo=1&pageSize=100", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 500,
		},
		{
			name: "success",
			fields: func() fields {
				lis := &mocklisters.MockLister{}
				lis.On("GetInstanceInfo", mock.Anything).Return(&listers.InstanceInfo{}, nil)
				cli := &mockclientset.ClientSet{}
				cli.On("Lister").Return(lis)

				repoSvc := mockrepo.NewMockInterface(gomock.NewController(t))
				repoSvc.EXPECT().ListAllRepositories(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*model.RepositoryResult{
						{
							RepositoryName: "test/test",
						},
					}, int64(1), nil)
				return fields{
					repositoryService: repoSvc,
					clientset:         cli,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?repositoryName=xxx&pageNo=1&pageSize=100", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := RepositoryHandler{
				repositoryService: tt.fields.repositoryService,
				clientset:         tt.fields.clientset,
			}
			if got := h.ListAllRepository(tt.args.c); got.StatusCode() != tt.code {
				t.Errorf("RepositoryHandler.ListAllRepository() = %v, want %v", got.StatusCode(), tt.code)
			}
		})
	}
}

func TestRepositoryHandler_BuildRepository(t *testing.T) {
	type fields struct {
		repositoryService service.RepositoryServiceInterface
		clientset         clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		// TODO: Add test cases.
		{
			name: "success",
			fields: func() fields {
				cs := &mockclientset.ClientSet{}

				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().InsertImageBuild(gomock.Any()).Return(nil)

				lister := &mocklisters.MockLister{}
				lister.On("GetInstanceInfo", mock.Anything).Return(&listers.InstanceInfo{}, nil)

				repoSvc := mockservice.NewMockRepositoryServiceInterface(gomock.NewController(t))
				repoSvc.EXPECT().CreateBuildRepositoryTask(gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)

				cs.On("SqlClient").Return(sqlCli)
				cs.On("Lister").Return(lister)

				return fields{
					clientset:         cs,
					repositoryService: repoSvc,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				body := `{"fromType": "dockerfile", "dockerfile": "FROM test:test", "isLatest": true}`
				ctx.Request, _ = http.NewRequest(http.MethodPost,
					"http://test.com/v1/instances/xxx/projects/xxx/repositories/xxx/imagebuilds",
					strings.NewReader(body))
				ctx.Request.Header.Set("Content-Type", "application/json")
				return args{
					c: ctx,
				}
			}(),
			want: 201,
		},
		{
			name:   "invalid dockerfile",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				body := `{"fromType": "dockerfile", "dockerfile": "", "isLatest": true}`
				ctx.Request, _ = http.NewRequest(http.MethodPost,
					"http://test.com/v1/instances/xxx/projects/xxx/repositories/xxx/imagebuilds",
					strings.NewReader(body))
				ctx.Request.Header.Set("Content-Type", "application/json")
				return args{
					c: ctx,
				}
			}(),
			want: 400,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &RepositoryHandler{
				repoService: tt.fields.repositoryService,
				clientset:   tt.fields.clientset,
			}
			assert.Equalf(t, tt.want, h.BuildRepository(tt.args.c).StatusCode(), "BuildRepository(%v)", tt.args.c)
		})
	}
}

func TestRepositoryHandler_ListBuildRepositoryTask(t *testing.T) {
	type fields struct {
		repositoryService repository.Interface
		clientset         clientset.ClientSetInterface
		repoService       service.RepositoryServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		// TODO: Add test cases.
		{
			name: "success",
			fields: func() fields {
				cs := &mockclientset.ClientSet{}

				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().ListImageBuildPaged(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.ImageBuild{
					{
						ID: 1,
					},
				}, 1, nil)

				cs.On("SqlClient").Return(sqlCli)

				return fields{
					clientset: cs,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com/instances/xxx/projects/xxx/repositories/xxx/imagebuilds", nil)
				return args{
					c: ctx,
				}
			}(),
			want: 200,
		},
		{
			name: "invalid id",
			fields: func() fields {
				cs := &mockclientset.ClientSet{}

				return fields{
					clientset: cs,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet,
					"http://test.com/instances/xxx/projects/xxx/repositories/xxx/imagebuilds?keywordType=id&keyword=xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			want: 200,
		},
		{
			name: "with keyword",
			fields: func() fields {
				cs := &mockclientset.ClientSet{}

				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().ListImageBuildPaged(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.ImageBuild{
					{
						ID: 1,
					},
				}, 1, nil)

				cs.On("SqlClient").Return(sqlCli)

				return fields{
					clientset: cs,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet,
					"http://test.com/instances/xxx/projects/xxx/repositories/xxx/imagebuilds?keywordType=id&keyword=1", nil)
				return args{
					c: ctx,
				}
			}(),
			want: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &RepositoryHandler{
				repositoryService: tt.fields.repositoryService,
				clientset:         tt.fields.clientset,
			}
			assert.Equalf(t, tt.want, h.ListBuildRepositoryTask(tt.args.c).StatusCode(), "ListBuildRepositoryTask(%v)", tt.args.c)
		})
	}
}

func TestRepositoryHandler_GetBuildRepositoryTaskLog(t *testing.T) {
	type fields struct {
		repositoryService repository.Interface
		clientset         clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		// TODO: Add test cases.
		{
			name:   "success",
			fields: fields{},
			args: func() args {
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com/v1/instances/xxx/projects/xxx/repositories/xxx/imagebuilds/1/log", nil)
				ctx.Set(middleware.IMAGE_BUILD_IDENTITY, &models.ImageBuild{
					Log: "test",
				})
				return args{
					c: ctx,
				}
			}(),
			want: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &RepositoryHandler{
				repositoryService: tt.fields.repositoryService,
				clientset:         tt.fields.clientset,
			}
			assert.Equalf(t, tt.want, h.GetBuildRepositoryTaskLog(tt.args.c).StatusCode(), "GetBuildRepositoryTaskLog(%v)", tt.args.c)
		})
	}
}

func TestRepositoryHandler_BatchDeleteBuildRepositoryTask(t *testing.T) {
	type fields struct {
		repositoryService service.RepositoryServiceInterface
		clientset         clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		// TODO: Add test cases.
		{
			name: "success",
			fields: func() fields {
				cs := &mockclientset.ClientSet{}

				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().Delete(gomock.Any()).Return(nil)

				repoSvc := mockservice.NewMockRepositoryServiceInterface(gomock.NewController(t))
				repoSvc.EXPECT().BatchDeleteBuildRepositoryTask(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				cs.On("SqlClient").Return(sqlCli)

				return fields{
					clientset:         cs,
					repositoryService: repoSvc,
				}
			}(),
			args: func() args {
				body := `{"items": ["11"]}`
				ctx.Request, _ = http.NewRequest(http.MethodGet,
					"http://test.com/v1/instances/xxx/projects/xxx/repositories/xxx/imagebuilds", strings.NewReader(body))
				return args{
					c: ctx,
				}
			}(),
			want: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &RepositoryHandler{
				repoService: tt.fields.repositoryService,
				clientset:   tt.fields.clientset,
			}
			assert.Equalf(t, tt.want, h.BatchDeleteBuildRepositoryTask(tt.args.c).StatusCode(), "BatchDeleteBuildRepositoryTask(%v)", tt.args.c)
		})
	}
}

func TestRepositoryHandler_DeleteBuildRepositoryTask(t *testing.T) {
	type fields struct {
		repositoryService service.RepositoryServiceInterface
		clientset         clientset.ClientSetInterface
	}
	type args struct {
		c *gin.Context
	}
	ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		// TODO: Add test cases.
		{
			name: "success",
			fields: func() fields {
				cs := &mockclientset.ClientSet{}

				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().Delete(gomock.Any()).Return(nil)

				repoSvc := mockservice.NewMockRepositoryServiceInterface(gomock.NewController(t))
				repoSvc.EXPECT().BatchDeleteBuildRepositoryTask(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				cs.On("SqlClient").Return(sqlCli)

				return fields{
					clientset:         cs,
					repositoryService: repoSvc,
				}
			}(),
			args: func() args {
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com/instances/xxx/projects/xxx/repositories/xxx/imagebuilds", nil)
				ctx.Set(middleware.IMAGE_BUILD_IDENTITY, &models.ImageBuild{
					ID: 1,
				})
				return args{
					c: ctx,
				}
			}(),
			want: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &RepositoryHandler{
				repoService: tt.fields.repositoryService,
				clientset:   tt.fields.clientset,
			}
			assert.Equalf(t, tt.want, h.DeleteBuildRepositoryTask(tt.args.c).StatusCode(), "DeleteBuildRepositoryTask(%v)", tt.args.c)
		})
	}
}

func TestRepositoryHandler_GetBuildRepositoryTask(t *testing.T) {
	type fields struct {
		repositoryService repository.Interface
		clientset         clientset.ClientSetInterface
		repoService       service.RepositoryServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())

	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		{
			name: "success",
			fields: func() fields {
				return fields{}
			}(),
			args: func() args {
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com/instances/xxx/projects/xxx/repositories/xxx/imagebuilds/1", nil)
				ctx.Set(middleware.IMAGE_BUILD_IDENTITY, &models.ImageBuild{
					ID: 1,
				})
				return args{
					c: ctx,
				}
			}(),
			want: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &RepositoryHandler{
				repositoryService: tt.fields.repositoryService,
				clientset:         tt.fields.clientset,
				repoService:       tt.fields.repoService,
			}
			assert.Equalf(t, tt.want, h.GetBuildRepositoryTask(tt.args.c).StatusCode(), "GetBuildRepositoryTask(%v)", tt.args.c)
		})
	}
}
