package handler

import (
	"bytes"
	"encoding/json"
	"fmt"

	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/resourcegroup"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/errors"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	addonclient "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/product"
	mockproduct "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/product/mocks"
	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
	testinglogictag "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/logictag"
	testingresourcegroup "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/resourcegroup"
	testingservice "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

func newMockInstanceHandler() *Instance {
	return &Instance{
		clients:         &clientset.ClientSet{},
		region:          "gz",
		endpoints:       map[string]string{},
		instanceService: &testingservice.InstanceService{},
	}

}

type GinResponseWriter struct {
	http.ResponseWriter
}

func (g *GinResponseWriter) CloseNotify() <-chan bool {
	return make(chan bool)
}

func newGinResponseWriter() http.ResponseWriter {
	return &GinResponseWriter{httptest.NewRecorder()}
}

func TestInstance_Create(t *testing.T) {
	ih := newMockInstanceHandler()
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	assert.NotNil(t, ctx)

	req := model.CreateInstanceRequest{
		Name:          "test",
		Type:          "BASIC",
		PaymentTiming: "prepay",
		Billing: model.Billing{
			ReservationTimeUnit: "MONTH",
			ReservationTime:     1,
			AutoRenew:           true,
			AutoRenewTimeUnit:   "MONTH",
			AutoRenewTime:       1,
		},
		GroupIDs: []string{"group-test", "xxx"},
	}

	data, err := json.Marshal(req)
	if err != nil {
		t.Errorf("json marshal failed: %s", err)
	}

	ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
	ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

	mock.OnAnything(ih.instanceService, "GenerateInstanceId").Return("ccr-xxxxxxxx", nil)
	mock.OnAnything(ih.instanceService, "CheckInstanceQuota").Return(nil)
	mock.OnAnything(ih.instanceService, "CreateNewFacadeOrder").Return("1", nil)
	ih.Create(ctx)
	assert.Equal(t, 202, ctx.Writer.Status())

	ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
	ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)
	mock.OnAnything(ih.instanceService, "GenerateInstanceId").Return("ccr-xxxxxxxx", nil)
	mock.OnAnything(ih.instanceService, "CheckInstanceQuota").Return(&errors.CcrError{
		Message: "quota has exceed",
	})
	mock.OnAnything(ih.instanceService, "CreateNewFacadeOrder").Return("1", nil)
	ih.Create(ctx)
	assert.Equal(t, 202, ctx.Writer.Status())

	ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
	ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)
	mock.OnAnything(ih.instanceService, "GenerateInstanceId").Return("ccr-xxxxxxxx", nil)
	mock.OnAnything(ih.instanceService, "CheckInstanceQuota").Return(nil)
	mock.OnAnything(ih.instanceService, "CreateNewFacadeOrder").Return("", fmt.Errorf("create new order failed"))

	ih.Create(ctx)

	assert.Equal(t, 202, ctx.Writer.Status())

}

func TestInstance_Upgrade(t *testing.T) {
	ih := newMockInstanceHandler()
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	assert.NotNil(t, ctx)

	req := model.UpgradeInstanceRequest{
		Type: "STANDARD",
	}

	data, err := json.Marshal(req)
	if err != nil {
		t.Errorf("json marshal failed: %s", err)
	}

	ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
	ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

	ctx.Set(middleware.INSTANCE_OBJECT_IDENTITY, &v1alpha1.CCR{
		Spec: v1alpha1.CCRSpec{Type: "BASIC"},
	},
	)

	mock.OnAnything(ih.instanceService, "ValidType").Return(true)

	mock.OnAnything(ih.instanceService, "GetResourceDetail").Return(
		&billing.GetResourceDetailResponse{Extra: `historyOrderImport: false
canary: true
autoRenew: false
autoRenewTimeUnit: MONTH
autoRenewTime: 1
instanceId: ccr-387l1gkh
instanceName: test
instanceType: STANDARD`},
		nil)
	mock.OnAnything(ih.instanceService, "CreateResizeFacadeOrder").Return("xxxxxx", nil)

	ih.Upgrade(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())
}

func TestInstance_Renew(t *testing.T) {

	ctx, _ := gin.CreateTestContext(newGinResponseWriter())

	type fields struct {
		clients         clientset.ClientSetInterface
		region          string
		instanceService service.InstanceServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "case1",
			fields: fields{
				clients:         &testingclientset.ClientSet{},
				region:          "gz",
				instanceService: &testingservice.InstanceService{},
			},
			args: args{c: ctx},
		},
	}
	for _, tt := range tests {
		assert.NotNil(t, tt.args.c)

		t.Run(tt.name, func(t *testing.T) {
			cls := &Instance{
				clients:         tt.fields.clients,
				region:          tt.fields.region,
				instanceService: tt.fields.instanceService,
			}
			co := model.ConfirmOrderRequest{
				Items: []model.Item{
					{
						Config: model.Config{
							Duration: 1,
						},
					},
				},
			}

			data, err := json.Marshal(co)
			if err != nil {
				t.Errorf("json marshal failed: %s", err)
			}

			// test request, must instantiate a request first
			req, _ := http.NewRequest("POST", "/", bytes.NewReader(data))
			req.Header.Add("Content-Type", gin.MIMEJSON)

			q := req.URL.Query()
			q.Add("orderType", "RENEW")
			req.URL.RawQuery = q.Encode()
			tt.args.c.Request = req

			mock.OnAnything(tt.fields.instanceService, "CreateRenewFacadeOrder").Return("1", nil)

			cls.Renew(tt.args.c)
			assert.Equal(t, 200, ctx.Writer.Status())

		})
	}
}

func TestInstance_AssignTag(t *testing.T) {

	type fields struct {
		clients         clientset.ClientSetInterface
		region          string
		instanceService service.InstanceServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "normal",
			fields: func() fields {
				cli := testingclientset.NewMockClientSetInterface(gomock.NewController(t))

				logicTagClient := testinglogictag.NewMockClient(gomock.NewController(t))
				logicTagClient.EXPECT().CreateAndAssignTag(gomock.Any(), gomock.Any()).Return(nil)

				cli.EXPECT().LogicTagClientForAccount(gomock.Any(), gomock.Any()).Return(logicTagClient, nil)

				return fields{
					clients:         cli,
					region:          "gz",
					instanceService: &testingservice.InstanceService{},
				}
			}(),
			args: func() args {

				at := model.AssignTagsRequest{
					Tags: []model.Tag{
						{
							TagKey:   "key1",
							TagValue: "value1",
						},
					},
				}

				data, err := json.Marshal(at)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest("PUT", "/", bytes.NewReader(data))
				ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

				return args{
					c: ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		assert.NotNil(t, tt.args.c)

		t.Run(tt.name, func(t *testing.T) {
			cls := &Instance{
				clients:         tt.fields.clients,
				region:          tt.fields.region,
				instanceService: tt.fields.instanceService,
			}

			cls.AssignTag(tt.args.c)
		})
	}
}

func TestInstance_ListForLogicTag(t *testing.T) {
	type fields struct {
		clients         clientset.ClientSetInterface
		region          string
		endpoints       map[string]string
		instanceService service.InstanceServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "normal",
			fields: func() fields {
				instanceService := testingservice.NewMockInstanceServiceInterface(gomock.NewController(t))
				instanceService.EXPECT().ListInstanceLocal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*model.InstanceInfo{
						{
							ID:           "ccr-1xxxxxxx",
							Name:         "ccr",
							Status:       "Running",
							InstanceType: "BASIC",
							Region:       "gz",
							Tags: []model.Tag{
								{
									TagKey:   "key1",
									TagValue: "value1",
								},
							},
						},
					},
					nil).AnyTimes()
				return fields{
					clients:         testingclientset.NewMockClientSetInterface(gomock.NewController(t)),
					region:          "gz",
					instanceService: instanceService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest("GET", "/", nil)
				ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)
				return args{
					c: ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cls := &Instance{
				clients:         tt.fields.clients,
				region:          tt.fields.region,
				endpoints:       tt.fields.endpoints,
				instanceService: tt.fields.instanceService,
			}
			cls.ListForLogicTag(tt.args.c)
		})
	}
}

func TestInstance_Get(t *testing.T) {
	type fields struct {
		clients         clientset.ClientSetInterface
		region          string
		endpoints       map[string]string
		instanceService service.InstanceServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "normal",
			fields: func() fields {
				cli := testingclientset.NewMockClientSetInterface(gomock.NewController(t))

				scheme := runtime.NewScheme()
				v1alpha1.AddToScheme(scheme)
				k8sClient := fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects(
					&v1alpha1.CCRQuota{
						ObjectMeta: v1.ObjectMeta{
							Name:      "ccr-1xxxxxxx",
							Namespace: "ccr-1xxxxxxx",
						},
					},
				).Build()

				cli.EXPECT().K8sClient().Return(k8sClient)

				productCli := &mockproduct.MockProductClientService{}
				productCli.On("GetProductsStatistic", mock.Anything, mock.Anything).Return(&product.GetProductsStatisticOK{
					Payload: &harbormodel.ModelsStatistic{
						PrivateProjectCount: 1200,
					},
				}, nil)

				cli.EXPECT().HarborClient(gomock.Any()).Return(&harbor.HarborClient{
					AddonClient: &addonclient.Harbor{
						Product: productCli,
					},
				}, nil).AnyTimes()

				instanceService := testingservice.NewMockInstanceServiceInterface(gomock.NewController(t))
				instanceService.EXPECT().GetResourceTagMap(gomock.Any(), gomock.Any()).Return(
					map[string][]model.Tag{
						"xxx": {
							{
								TagKey:   "key1",
								TagValue: "value1",
							},
						},
					}, nil).AnyTimes()
				instanceService.EXPECT().GetResourceGroupMap(gomock.Any(), gomock.Any()).Return(map[string][]string{
					"group": {
						"xxx",
					},
				}, nil).AnyTimes()

				k8sClient = fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects(
					&v1alpha1.CNCNetwork{
						ObjectMeta: v1.ObjectMeta{
							Name:      "ccr-1xxxxxxx",
							Namespace: "ccr-1xxxxxxx",
						},
						Spec: v1alpha1.CNCNetworkSpec{
							CustomDomains: []v1alpha1.CustomDomain{
								{
									DomainName: "www.baidu.com",
								},
							},
						},
					},
				).Build()
				cli.EXPECT().K8sClient().Return(k8sClient)

				return fields{
					clients:         cli,
					region:          "gz",
					instanceService: instanceService,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "instanceId", Value: "ccr-1xxxxxxx"})
				ctx.Request, _ = http.NewRequest("GET", "/", nil)
				ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

				ctx.Set(middleware.INSTANCE_OBJECT_IDENTITY, &v1alpha1.CCR{
					Spec:   v1alpha1.CCRSpec{Type: "BASIC"},
					Status: v1alpha1.CCRStatus{Phase: v1alpha1.CCRRunning},
				})

				return args{
					c: ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cls := &Instance{
				clients:         tt.fields.clients,
				region:          tt.fields.region,
				endpoints:       tt.fields.endpoints,
				instanceService: tt.fields.instanceService,
			}
			cls.Get(tt.args.c)
		})
	}
}

func TestInstance_BindResGroup(t *testing.T) {

	type fields struct {
		clients         clientset.ClientSetInterface
		region          string
		instanceService service.InstanceServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "normal",
			fields: func() fields {
				cli := testingclientset.NewMockClientSetInterface(gomock.NewController(t))

				resGrouopClient := testingresourcegroup.NewMockClient(gomock.NewController(t))
				resGrouopClient.EXPECT().BindResourceToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&resourcegroup.BindResourceResult{
						Status: "200",
						ResGroups: []resourcegroup.ResGroup{
							{
								ID:   "resgroup-xxx",
								Name: "resgroup-xxx",
								Groups: []resourcegroup.Group{
									{
										GroupID: "group-xxx",
										Name:    "group-xxx",
									},
								},
							},
						},
					}, nil).AnyTimes()

				cli.EXPECT().ResourceGroupClientForAccount(gomock.Any(), gomock.Any()).Return(resGrouopClient, nil)

				return fields{
					clients:         cli,
					region:          "gz",
					instanceService: &testingservice.InstanceService{},
				}
			}(),
			args: func() args {

				at := model.BindResGroupRequest{
					GroupIDs: []string{"resgroup-xxx", "group-xxx"},
				}

				data, err := json.Marshal(at)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
				ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

				return args{
					c: ctx,
				}
			}(),
		},
		{
			name: "groupID is nil",
			fields: func() fields {
				cli := testingclientset.NewMockClientSetInterface(gomock.NewController(t))

				resGrouopClient := testingresourcegroup.NewMockClient(gomock.NewController(t))
				resGrouopClient.EXPECT().BindResourceToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&resourcegroup.BindResourceResult{
						Status: "200",
						ResGroups: []resourcegroup.ResGroup{
							{
								ID:   "resgroup-xxx",
								Name: "resgroup-xxx",
								Groups: []resourcegroup.Group{
									{
										GroupID: "RESG-EF2DjQjVFPo",
										Name:    "group-default",
									},
								},
							},
						},
					}, nil).AnyTimes()

				cli.EXPECT().ResourceGroupClientForAccount(gomock.Any(), gomock.Any()).Return(resGrouopClient, nil)

				return fields{
					clients:         cli,
					region:          "gz",
					instanceService: &testingservice.InstanceService{},
				}
			}(),
			args: func() args {

				at := model.BindResGroupRequest{
					GroupIDs: []string{},
				}

				data, err := json.Marshal(at)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}

				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
				ctx.Request.Header.Add("Content-Type", gin.MIMEJSON)

				return args{
					c: ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		assert.NotNil(t, tt.args.c)

		t.Run(tt.name, func(t *testing.T) {
			cls := &Instance{
				clients:         tt.fields.clients,
				region:          tt.fields.region,
				instanceService: tt.fields.instanceService,
			}

			cls.BindResGroup(tt.args.c)
		})
	}
}
