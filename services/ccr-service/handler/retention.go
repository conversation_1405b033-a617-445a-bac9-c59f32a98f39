package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/retention"
)

// CreateRetentionRequest create registry request
//
// swagger:model CreateRetentionRequest
type CreateRetentionRequest struct {
	ProjectId int64 `json:"project_id"`
	// retain
	Retain Retain `json:"retain"`

	// immutable
	Immutable bool `json:"immutable"`
}

type Retain struct {
	Disabled bool `json:"disabled"`
	// rule
	Rule *RetentionRule `json:"rule"`
	//
	Schedule Schedule `json:"schedule"`
}

type Schedule struct {
	// schedule
	Disabled bool `json:"disabled"`
	// cron
	Settings string `json:"settings"`
}

// RetentionPolicy retention policy
//
// swagger:model RetentionPolicy
type RetentionPolicy struct {

	// scope
	//Scope *RetentionPolicyScope `json:"scope"`

	// trigger
	//Trigger *RetentionRuleTrigger `json:"trigger"`
}

// RetentionRule retention rule
//
// swagger:model RetentionRule
type RetentionRule struct {

	// params
	Params map[string]interface{} `json:"params"`

	// template
	Template string `json:"template"`
}

type RetentionHandler struct {
	retentionService *retention.RetentionService
}

func NewRetentionHandler() *RetentionHandler {
	return &RetentionHandler{
		retentionService: retention.NewRetentionService(),
	}
}

// CreateRetention 创建命名空间
// @Summary 创建命名空间
// @Description 创建命名空间
// @Tags retention
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param retention body model.RetentionPolicy true "create retention body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/retentions [post]
func (h *RetentionHandler) CreateRetention(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)

	var req *model.RetentionPolicy
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	location, err := h.retentionService.NewRetention(c, req)
	if err != nil {
		logger.Errorf("new retention failed: %s", err)
		return NewResponse(http.StatusInternalServerError, "internal server error", nil, c)
	}

	return NewResponse(http.StatusOK, "success", location, c)
}
