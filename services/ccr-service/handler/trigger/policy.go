package trigger

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/trigger"
	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type PolicyHandler struct {
	clients clientset.ClientSetInterface
	// SupportedNotifyTypes is a map to store trigger type, eg. trigger etc
	supportedHeader map[string]struct{}
}

func NewPolicyHandler(clis clientset.ClientSetInterface) *PolicyHandler {
	return &PolicyHandler{
		clients:         clis,
		supportedHeader: initSupportedHeader(),
	}
}

// CreatePolicy 创建触发器策略
// @Summary 创建触发器策略
// @Description 创建触发器策略
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.TriggerPolicyRequest true "触发器参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 409 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/triggers/policies [post]
func (h *PolicyHandler) CreatePolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")
	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	var pr model.TriggerPolicyRequest
	if err := c.Bind(&pr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	targets := make([]*harbormodel.PolicyTriggerTarget, 0)
	for _, tg := range pr.Targets {
		target := &harbormodel.PolicyTriggerTarget{
			Type:           "trigger",
			Address:        tg.Address,
			Headers:        tg.Headers,
			SkipCertVerify: true,
		}
		targets = append(targets, target)
	}

	filters, err := h.validTriggerFilter(pr.Filters)
	if err != nil {
		logger.Errorf("check triger policy filter failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	triggerPolicy := &harbormodel.PolicyTriggerPolicy{
		Name:        pr.Name,
		Description: pr.Description,
		Enabled:     true,
		EventTypes:  pr.EventTypes,
		Filters:     filters,
		Targets:     targets,
	}

	if _, err := harborClient.AddonClient.Trigger.CreatePolicy(trigger.NewCreatePolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicy(triggerPolicy), harborClient.AuthInfo); err != nil {

		logger.Errorf("create trigger policy from harbor failed: %s", err)
		if _, ok := err.(*trigger.CreatePolicyConflict); ok {
			gin_context.E(c, gin_context.ConflictError(requestID, "trigger policy conflict"))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "success")
}

// TestPolicyTarget 测试触发器策略目标地址
// @Summary 测试触发器策略目标地址
// @Description 测试触发器策略目标地址
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.TriggerTarget true "触发器参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/triggers/policies/targets [post]
func (h *PolicyHandler) TestPolicyTarget(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")
	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	var triggerTarget model.TriggerTarget
	if err := c.Bind(&triggerTarget); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	target := &harbormodel.PolicyTriggerTarget{
		Type:           "trigger",
		Address:        triggerTarget.Address,
		Headers:        triggerTarget.Headers,
		SkipCertVerify: true,
	}

	if _, err := harborClient.AddonClient.Trigger.TestPolicyTargets(trigger.NewTestPolicyTargetsParamsWithContext(c).
		WithXRequestID(&requestID).
		WithTarget(target), harborClient.AuthInfo); err != nil {
		logger.Errorf("test trigger policy target from harbor failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "success")
}

// GetPolicy 查询触发器策略详情
// @Summary 查询触发器策略详情
// @Description 查询触发器策略详情
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "触发器策略ID"
// @Success 200 {object} model.TriggerPolicy "trigger policy"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/triggers/policies/{policyId} [get]
func (h *PolicyHandler) GetPolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	policyId := c.Param("policyId")

	resp, err := harborClient.AddonClient.Trigger.GetPolicy(trigger.NewGetPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("get trigger policy from harbor failed: %s", err)
		if _, ok := err.(*trigger.GetPolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	c.JSON(http.StatusOK, convertToPolicy(resp.GetPayload()))
}

// UpdatePolicy 修改触发器策略
// @Summary 修改触发器策略
// @Description 修改触发器策略
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "触发器策略ID"
// @Param body body model.TriggerPolicyRequest true "触发器参数"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/triggers/policies/{policyId} [put]
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	policyId := c.Param("policyId")

	var pr model.TriggerPolicyRequest
	if err := c.Bind(&pr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	resp, err := harborClient.AddonClient.Trigger.GetPolicy(trigger.NewGetPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("get trigger policy from harbor failed: %s", err)
		logger.Errorf("get trigger policy from harbor failed: %s", err)
		if _, ok := err.(*trigger.GetPolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	triggerPolicy := resp.GetPayload()
	if resp.GetPayload() == nil {
		logger.Errorf("get trigger policy from harbor failed: %s", err)
		gin_context.E(c, gin_context.NotFoundError(requestID, fmt.Sprintf("not found policy with policy ID: %s", policyId)))
		return
	}

	targets := make([]*harbormodel.PolicyTriggerTarget, 0)
	for _, tg := range pr.Targets {
		target := &harbormodel.PolicyTriggerTarget{
			Type:           "trigger",
			Address:        tg.Address,
			Headers:        tg.Headers,
			SkipCertVerify: true,
		}
		targets = append(targets, target)
	}

	filters, err := h.validTriggerFilter(pr.Filters)
	if err != nil {
		logger.Errorf("check triger policy filter failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	triggerPolicy.Name = pr.Name
	triggerPolicy.Description = pr.Description
	triggerPolicy.EventTypes = pr.EventTypes
	triggerPolicy.Filters = filters
	triggerPolicy.Targets = targets

	if _, err := harborClient.AddonClient.Trigger.UpdatePolicy(trigger.NewUpdatePolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId).
		WithPolicy(triggerPolicy), harborClient.AuthInfo); err != nil {
		logger.Errorf("update trigger policy from harbor failed: %s", err)
		logger.Errorf("get trigger policy from harbor failed: %s", err)
		if _, ok := err.(*trigger.UpdatePolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	c.JSON(http.StatusOK, "success")
}

// ListPolicies 获取触发器策略列表
// @Summary 获取触发器策略列表
// @Description 获取触发器策略列表
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyName query string false "触发器策略名称"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListTriggerPolicyResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/triggers/policies [get]
func (h *PolicyHandler) ListPolicies(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	policyName := c.Query("policyName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	listPolicyParams := trigger.NewListPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPage(&pageNo).
		WithPageSize(&pageSize)
	if policyName != "" {
		q := fmt.Sprintf("Name=%s", policyName)
		listPolicyParams.WithQ(&q)
	}

	resp, err := harborClient.AddonClient.Trigger.ListPolicy(listPolicyParams, harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("list trigger policy from harbor failed: %s", err)
		if _, ok := err.(*trigger.ListPolicyBadRequest); ok {
			gin_context.E(c, gin_context.BadRequestError(requestID))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	policies := make([]*model.TriggerPolicy, 0)

	if resp != nil && len(resp.GetPayload()) > 0 {
		plys := resp.GetPayload()
		for _, ply := range plys {
			policies = append(policies, convertToPolicy(ply))
		}
	}

	ltp := &model.ListTriggerPolicyResponse{
		PageInfo: model.PageInfo{
			Total:    int(resp.XTotalCount),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Policies: policies,
	}
	c.JSON(http.StatusOK, ltp)
}

// DeletePolicy 删除触发器策略
// @Summary 删除触发器策略
// @Description 删除触发器策略
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "触发器策略ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/triggers/policies/{policyId} [delete]
func (h *PolicyHandler) DeletePolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	policyId := c.Param("policyId")

	if _, err := harborClient.AddonClient.Trigger.DeletePolicy(trigger.NewDeletePolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId), harborClient.AuthInfo); err != nil {
		logger.Errorf("delete trigger policy from harbor failed: %s", err)
		if _, ok := err.(*trigger.DeletePolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "success")
}

// BatchDeletePolicy 批量删除触发器策略
// @Summary 批量删除触发器策略
// @Description 批量删除触发器策略
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param body body model.BatchDeleteInt64Request true "触发器策略ID数组"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/triggers/policies [delete]
func (h *PolicyHandler) BatchDeletePolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	var deleteRequest model.BatchDeleteInt64Request
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("bind project list item failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
	}

	if _, err := harborClient.AddonClient.Trigger.BatchDeletePolicy(trigger.NewBatchDeletePolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithRequest(&harbormodel.ModelsBatchDeleteRequest{Items: deleteRequest.Items}), harborClient.AuthInfo); err != nil {
		logger.Errorf("get trigger policy from harbor failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "success")
}

// EnablePolicy 启动或关闭触发器策略
// @Summary 启动或关闭触发器策略
// @Description 启动或关闭触发器策略
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "触发器策略ID"
// @Param enabled query string true "是否开启"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/triggers/policies/{policyId}/enable [put]
func (h *PolicyHandler) EnablePolicy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	policyId := c.Param("policyId")
	resp, err := harborClient.AddonClient.Trigger.GetPolicy(trigger.NewGetPolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("get trigger policy from harbor failed: %s", err)
		if _, ok := err.(*trigger.GetPolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	triggerPolicy := resp.GetPayload()
	if triggerPolicy == nil {
		logger.Errorf("get trigger policy from harbor failed: %s", err)
		gin_context.E(c, gin_context.NotFoundError(requestID, fmt.Sprintf("not found policy with policy ID: %s", policyId)))
		return
	}

	enabled, err := strconv.ParseBool(c.Query("enabled"))
	if err != nil {
		logger.Errorf("parse enabledis failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	if triggerPolicy.Enabled == enabled {
		logger.Errorf("policy enabled is %t", triggerPolicy.Enabled)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	triggerPolicy.Enabled = enabled

	_, err = harborClient.AddonClient.Trigger.UpdatePolicy(trigger.NewUpdatePolicyParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(policyId).
		WithPolicy(triggerPolicy), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("update trigger policy from harbor failed: %s", err)
		if _, ok := err.(*trigger.UpdatePolicyNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "success")
}

func initSupportedHeader() map[string]struct{} {
	headers := []string{"Authorization"}

	var supportedHeader = make(map[string]struct{})
	for _, notifyType := range headers {
		supportedHeader[notifyType] = struct{}{}
	}
	return supportedHeader
}

func (h *PolicyHandler) validTriggerFilter(targetFilters []*model.TriggerFilter) ([]*harbormodel.PolicyTriggerFilter, error) {

	var isValid bool
	filters := make([]*harbormodel.PolicyTriggerFilter, 0)
	for _, fl := range targetFilters {
		if fl.Type == model.FilterTypeProject {
			pattern := fl.Value
			if len(pattern) == 0 {
				return nil, fmt.Errorf("project pattern is not found")
			}
			isValid = true
		}
		if fl.Value == "" {
			fl.Value = "**"
		}
		filter := &harbormodel.PolicyTriggerFilter{
			Type:  string(fl.Type),
			Value: fl.Value,
		}

		filters = append(filters, filter)
	}

	if !isValid {
		return nil, fmt.Errorf("filters is invalid")
	}

	return filters, nil

}

func convertToPolicy(triggerPolicy *harbormodel.PolicyTriggerPolicy) *model.TriggerPolicy {

	targets := make([]*model.TriggerTarget, 0)
	for _, tg := range triggerPolicy.Targets {
		targets = append(targets, &model.TriggerTarget{
			Address: tg.Address,
			Headers: tg.Headers,
		})
	}

	filters := make([]*model.TriggerFilter, 0)
	for _, fl := range triggerPolicy.Filters {
		filters = append(filters, &model.TriggerFilter{
			Type:  model.FilterType(fl.Type),
			Value: fl.Value.(string),
		})
	}

	policy := &model.TriggerPolicy{
		CreationTime: triggerPolicy.CreationTime,
		Description:  triggerPolicy.Description,
		Enabled:      triggerPolicy.Enabled,
		EventTypes:   triggerPolicy.EventTypes,
		Filters:      filters,
		ID:           triggerPolicy.ID,
		Name:         triggerPolicy.Name,
		Targets:      targets,
		UpdateTime:   triggerPolicy.UpdateTime,
	}

	return policy
}
