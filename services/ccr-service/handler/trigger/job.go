package trigger

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/addon/client/trigger"
	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type JobHandler struct {
	clients clientset.ClientSetInterface
}

func NewJobHandler(clis clientset.ClientSetInterface) *JobHandler {
	return &JobHandler{
		clients: clis,
	}
}

// ListJobs 获取触发器任务列表
// @Summary 获取触发器任务列表
// @Description 获取触发器任务列表
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "触发器策略ID"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListTriggerJobResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/triggers/policies/{policyId}/jobs [get]
func (h *JobHandler) ListJobs(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")
	policyId := c.Param("policyId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	resp, err := harborClient.AddonClient.Trigger.ListJob(trigger.NewListJobParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPage(&pageNo).
		WithPageSize(&pageSize).
		WithPolicyID(policyId), harborClient.AuthInfo)
	if err != nil {
		if _, ok := err.(*trigger.ListJobNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		logger.Errorf("create trigger policy from harbor failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	jobs := make([]*model.TriggerJob, 0)

	if resp != nil && len(resp.GetPayload()) > 0 {
		js := resp.GetPayload()
		for _, job := range js {
			jobs = append(jobs, convertToJob(job))
		}
	}

	ltp := &model.ListTriggerJobResponse{
		PageInfo: model.PageInfo{
			Total:    int(resp.XTotalCount),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Jobs: jobs,
	}
	c.JSON(http.StatusOK, ltp)
}

// RetryExecuteJob 重新执行触发器任务
// @Summary 重新执行触发器任务
// @Description 重新执行触发器任务
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "触发器策略ID"
// @Param jobId path string true "触发器任务ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 404 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/triggers/policies/{policyId}/jobs/{jobId}/retry [put]
func (h *JobHandler) RetryExecuteJob(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)
	instanceId := c.Param("instanceId")

	harborClient, err := h.clients.HarborClient(instanceId)
	if err != nil {
		logger.Errorf("get harbor client failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	_, err = harborClient.AddonClient.Trigger.RetryExecuteJob(trigger.NewRetryExecuteJobParamsWithContext(c).
		WithXRequestID(&requestID).
		WithPolicyID(c.Param("policyId")).
		WithJobID(c.Param("jobId")), harborClient.AuthInfo)
	if err != nil {
		logger.Errorf("retry trigger job from harbor failed: %s", err)
		if _, ok := err.(*trigger.RetryExecuteJobNotFound); ok {
			gin_context.E(c, gin_context.NotFoundError(requestID, err.Error()))
			return
		}
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.AbortWithStatus(http.StatusOK)
}

func convertToJob(triggerJob *harbormodel.JobTriggerJob) *model.TriggerJob {
	resources := triggerJob.JobDetail.EventData.Resources
	images := make([]string, 0)
	var image string
	if len(resources) > 0 {
		for _, resource := range resources {
			images = append(images, fmt.Sprintf("%s:%s", triggerJob.JobDetail.EventData.Repository.RepoFullName, resource.Tag))
		}
		image = fmt.Sprintf("%s:%s", triggerJob.JobDetail.EventData.Repository.RepoFullName, resources[0].Tag)
	}

	return &model.TriggerJob{
		CreationTime: triggerJob.CreationTime,
		ID:           triggerJob.ID,
		UpdateTime:   triggerJob.UpdateTime,
		EventType:    triggerJob.EventType,
		NotifyType:   triggerJob.NotifyType,
		Status:       triggerJob.Status,
		Operator:     triggerJob.JobDetail.Operator,
		Images:       images,
		Image:        image,
	}
}
