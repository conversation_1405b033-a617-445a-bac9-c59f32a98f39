package config

import (
	"fmt"
	"io/ioutil"

	"k8s.io/apimachinery/pkg/util/yaml"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
)

type ServiceConfig struct {
	AccessKey     string `yaml:"accessKey,omitempty"`
	SecretKey     string `yaml:"secretKey,omitempty"`
	ListenAddress string `yaml:"listenAddress,omitempty"`
	ClusterID     string `yaml:"clusterId,omitempty"`

	CCRDomainEndpoint   string `yaml:"ccrDomainEndpoint,omitempty"`
	STSEndpoint         string `yaml:"stsEndpoint,omitempty"`
	CCEEndpoint         string `yaml:"cceEndpoint,omitempty"`
	IAMEndpoint         string `yaml:"iamEndpoint,omitempty"`
	VPCEndpoint         string `yaml:"vpcEndpoint,omitempty"`
	UserSettingEndpoint string `yaml:"userSettingEndpoint,omitempty"`
	CertificateEndpoint string `yaml:"certificateEndpoint,omitempty"`
	DomainCheckEndpoint string `yaml:"domainCheckEndpoint,omitempty"`

	OrderFacadeEndpoint    string `yaml:"orderFacadeEndpoint,omitempty"`
	OrderManagerEndpoint   string `yaml:"orderManagerEndpoint,omitempty"`
	ResourceManageEndpoint string `yaml:"resourceManageEndpoint,omitempty"`
	AutoRenewEndpoint      string `yaml:"autoRenewEndpoint,omitempty"`

	BusEndpoint      string `yaml:"busEndpoint,omitempty"`
	LogicTagEndpoint string `yaml:"logicTagEndpoint,omitempty"`

	ResGroupEndpoint string `yaml:"resGroupEndpoint,omitempty"`

	CCRServiceEndpoint string `yaml:"ccrServiceEndpoint,omitempty"`

	Endpoints map[string]string `yaml:"endpoints,omitempty"`

	Billing         Billing `yaml:"billing"`
	Region          string  `yaml:"region,omitempty"`
	Env             string  `yaml:"env,omitempty"`
	RoleName        string  `yaml:"roleName,omitempty"`
	ServiceName     string  `yaml:"serviceName,omitempty"`
	ServicePassword string  `yaml:"servicePassword,omitempty"`
	MysqlConnection string  `yaml:"mysqlConnection,omitempty"`

	RegistryAllowlist   string `yaml:"registryAllowlist,omitempty"`
	CheckUserFeatureACL bool   `yaml:"checkUserFeatureACL,omitempty"`
}

type Billing struct {
	RoleName        string `yaml:"roleName,omitempty"`
	ServiceName     string `yaml:"serviceName,omitempty"`
	ServicePassword string `yaml:"servicePassword,omitempty"`
	AccessKey       string `yaml:"accessKey,omitempty"`
	SecretKey       string `yaml:"secretKey,omitempty"`
}

func NewConfig(filepath string) (*ServiceConfig, error) {
	content, err := ioutil.ReadFile(filepath)
	if err != nil {
		return nil, err
	}

	var serviceConfig ServiceConfig
	err = yaml.Unmarshal(content, &serviceConfig)
	if err != nil {
		return nil, err
	}

	if common.IDGenerator.Next(serviceConfig.Region) == "" {
		return nil, fmt.Errorf("unknown region: %v", serviceConfig.Region)
	}

	return &serviceConfig, nil
}
