package middleware

import (
	"context"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
)

const (
	IMAGE_BUILD_IDENTITY = "IMAGE_BUILD_IDENTITY"
)

func ImageBuildIDCheck(cli models.Interface) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestId := gin_context.RequestIdFromContext(c)

		if c.<PERSON>m("buildId") == "" {
			return
		}

		buildID, err := strconv.ParseInt(c.Param("buildId"), 10, 64)
		if err != nil {
			logger.Errorf("parse build id %s failed: %v", c<PERSON>("buildId"), err)
			gin_context.E(c, gin_context.BadRequestError(requestId))
			return
		}

		ib, err := cli.GetImageBuild(buildID)
		if err != nil {
			logger.Errorf("get image build %v failed: %v", buildID, err)
			if gorm.ErrRecordNotFound == err {
				gin_context.E(c, gin_context.NotFoundError(requestId, "image build not found"))
				return
			}

			gin_context.E(c, gin_context.InternalServerError(requestId))
			return
		}

		if ib.AccountID != AccountIDFromContext(c) {
			logger.Warnf("build account id mismatch")
			gin_context.E(c, gin_context.NotFoundError(requestId, "image build not found"))
			return
		}

		c.Set(IMAGE_BUILD_IDENTITY, ib)
	}
}

func ImageBuildFromContext(ctx context.Context) *models.ImageBuild {
	return ctx.Value(IMAGE_BUILD_IDENTITY).(*models.ImageBuild)
}
