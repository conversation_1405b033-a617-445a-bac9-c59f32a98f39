package middleware

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	testinglogictag "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/logictag"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

func newPermissionCheck() *PermissionCheck {
	return &PermissionCheck{
		clients: &clientset.ClientSet{},
		region:  "bj",
		service: "",
	}
}

func TestPermissionTable(t *testing.T) {
	pc := newPermissionCheck()

	key := "GET/v1/instances"
	assert.NotEmpty(t, pc.permissionTable(), key)
}

func Test_GetOperation(t *testing.T) {
	pc := NewPermissionCheck(nil, "test", "test", "test")

	op := pc.getOperation("GET", "/v1/instances/:instanceId")
	assert.Equal(t, "DescribeInstance", op)

	op = pc.getOperation("test", "test")
	assert.Equal(t, "", op)
}

func TestPermissionCheck_GetResourceTagMap(t *testing.T) {
	type fields struct {
		region  string
		env     string
		service string
		clients clientset.ClientSetInterface
	}
	type args struct {
		ctx         context.Context
		instanceIDs []string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "normal",
			fields: func() fields {
				cli := testingclientset.NewMockClientSetInterface(gomock.NewController(t))
				logicTagClient := testinglogictag.NewMockClient(gomock.NewController(t))

				logicTagClient.EXPECT().ListTags(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&logictag.ListTagsResult{
						TagAssociationFulls: []logictag.FullTagAssociation{
							{
								// 服务类型 如 BCC
								ServiceType: "CCR",
								// 标签所属账户ID
								AccountID: "xxxx",
								// 资源的ID 如 i-LGzaQZWI
								ResourceID: "xxxx",
								// 资源UUID
								ResourceUUID: "xxxx",
								// 标签内部ID，无需关注此字段
								TagID: 100,
								// 标签值
								TagValue: "value1",
								// 标签键
								TagKey: "key1",
								// 资源所在region
								Region: "gz",
								// 关联类型 associationType
								AssociationType: 1,
							},
						},
					}, nil).AnyTimes()

				cli.EXPECT().LogicTagClientForAccount(gomock.Any(), gomock.Any()).Return(logicTagClient, nil).AnyTimes()

				return fields{
					clients: cli,
					region:  "gz",
				}
			}(),
			args: args{
				ctx:         context.Background(),
				instanceIDs: []string{"xxxx"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pc := &PermissionCheck{
				region:  tt.fields.region,
				env:     tt.fields.env,
				service: tt.fields.service,
				clients: tt.fields.clients,
			}
			_, err := pc.GetResourceTagMap(tt.args.ctx, tt.args.instanceIDs)
			if err != nil {
				t.Errorf("case %s failed", tt.name)
			}
		})
	}
}

func TestPermissionCheck_permissionTable(t *testing.T) {
	type fields struct {
		region  string
		env     string
		service string
		clients clientset.ClientSetInterface
	}
	tests := []struct {
		name   string
		fields fields
		want   map[string][]string
	}{
		{
			name:   "normal",
			fields: fields{},
			want: map[string][]string{

				"GET/v1/registrytypes":                                       {Read, Operate, "GetRegistryTypes"},
				"GET/v1/users/profile":                                       {Read, Operate, "GetUserProfile"},
				"GET/v1/users/cert":                                          {Read, Operate, "GetUserCert"},
				"POST/v1/order_executor/execute":                             {FullControl, "ExecuteOrder"},
				"GET/v1/order_executor/check":                                {Read, Operate, "CheckOrder"},
				"POST/v1/instances":                                          {FullControl, "CreateInstance"},
				"POST/v1/accelerators/policies/filters":                      {Operate, "TestPolicyFilter"},
				"GET/v1/instances":                                           {Read, Operate, "ListInstance"},
				"POST/v1/tag/resources":                                      {Read, Operate, "ListInstanceForTag"},
				"GET/v1/iam/resources":                                       {Read, Operate, "ListInstanceForIam"},
				"POST/v1/instances/renew":                                    {FullControl, "RenewInstance"},
				"GET/v1/instances/:instanceId":                               {Read, Operate, "DescribeInstance"},
				"PUT/v1/instances/:instanceId":                               {Operate, "UpdateInstance"},
				"PUT/v1/instances/:instanceId/upgrade":                       {FullControl, "UpgradeInstance"},
				"PUT/v1/instances/:instanceId/tags":                          {Operate, "AddInstanceTag"},
				"PUT/v1/instances/:instanceId/resgroups":                     {Operate, "BindResourceGroup"},
				"GET/v1/instances/:instanceId/repositories":                  {Read, Operate, "ListAllRepositories"},
				"GET/v1/instances/:instanceId/privatelinks":                  {Read, Operate, "GetPrivateNetworks"},
				"POST/v1/instances/:instanceId/privatelinks":                 {Operate, "CreatePrivateNetwork"},
				"DELETE/v1/instances/:instanceId/privatelinks":               {Operate, "DeletePrivateNetwork"},
				"GET/v1/instances/:instanceId/publiclinks":                   {Read, Operate, "GetPublicNetworks"},
				"PUT/v1/instances/:instanceId/publiclinks":                   {Operate, "UpdatePublicNetwork"},
				"DELETE/v1/instances/:instanceId/publiclinks/whitelist":      {Operate, "DeletePublicWhitelist"},
				"POST/v1/instances/:instanceId/publiclinks/whitelist":        {Operate, "AddPublicWhitelist"},
				"PUT/v1/instances/:instanceId/credential":                    {Read, Operate, "ResetCredential"},
				"POST/v1/instances/:instanceId/credential":                   {Read, Operate, "CreateCredential"},
				"POST/v1/instances/:instanceId/gcs":                          {Operate, "CreateGarbageCollection"},
				"GET/v1/instances/:instanceId/gcs":                           {Read, Operate, "ListGarbageCollection"},
				"GET/v1/instances/:instanceId/gcs/:gcId/log":                 {Read, Operate, "DescribeGarbageCollectionLog"},
				"POST/v1/instances/:instanceId/registries":                   {Operate, "CreateRemoteRegistry"},
				"GET/v1/instances/:instanceId/registries/:registryId":        {Read, Operate, "DescribeRemoteRegistry"},
				"GET/v1/instances/:instanceId/registries":                    {Read, Operate, "ListRemoteRegistries"},
				"PUT/v1/instances/:instanceId/registries/:registryId":        {Operate, "UpdateRemoteRegistry"},
				"DELETE/v1/instances/:instanceId/registries/:registryId":     {Operate, "DeleteRemoteRegistry"},
				"GET/v1/instances/:instanceId/executions":                    {Read, Operate, "ListReplicationExecutions"},
				"POST/v1/instances/:instanceId/executions":                   {Operate, "StartReplication"},
				"PUT/v1/instances/:instanceId/executions/:executionId":       {Operate, "StopReplication"},
				"GET/v1/instances/:instanceId/executions/:executionId/tasks": {Read, Operate, "ListExecutionTasks"},
				"GET/v1/instances/:instanceId/executions" +
					"/:executionId/tasks/:taskId/log": {Read, Operate, "GetExecutionTaskLog"},
				"POST/v1/instances/:instanceId/replications":                    {Operate, "CreateReplication"},
				"GET/v1/instances/:instanceId/replications":                     {Read, Operate, "ListReplication"},
				"GET/v1/instances/:instanceId/replications/:policyId":           {Read, Operate, "DescribeReplication"},
				"DELETE/v1/instances/:instanceId/replications/:policyId":        {Operate, "DeleteReplication"},
				"PUT/v1/instances/:instanceId/replications/:policyId":           {Operate, "UpdateReplication"},
				"POST/v1/instances/:instanceId/syncs":                           {Operate, "CreateSync"},
				"GET/v1/instances/:instanceId/syncs":                            {Read, Operate, "ListSync"},
				"GET/v1/instances/:instanceId/syncs/:policyId":                  {Read, Operate, "DescribeSync"},
				"DELETE/v1/instances/:instanceId/syncs/:policyId":               {Operate, "DeleteSync"},
				"PUT/v1/instances/:instanceId/syncs/:policyId":                  {Operate, "UpdateSync"},
				"POST/v1/instances/:instanceId/triggers/policies":               {Operate, "CreateTriggerPolicy"},
				"GET/v1/instances/:instanceId/triggers/policies":                {Read, Operate, "ListTriggerPolicy"},
				"DELETE/v1/instances/:instanceId/triggers/policies":             {Operate, "BatchDeleteTriggerPolicy"},
				"GET/v1/instances/:instanceId/triggers/policies/:policyId":      {Read, Operate, "DescribeTriggerPolicy"},
				"DELETE/v1/instances/:instanceId/triggers/policies/:policyId":   {Operate, "DeleteTriggerPolicy"},
				"PUT/v1/instances/:instanceId/triggers/policies/:policyId":      {Operate, "UpdateTriggerPolicy"},
				"POST/v1/instances/:instanceId/triggers/policies/targets":       {Operate, "TestTriggerPolicyTarget"},
				"GET/v1/instances/:instanceId/triggers/policies/:policyId/jobs": {Read, Operate, "ListTriggerPolicyJob"},
				"PUT/v1/instances/:instanceId/triggers/policies" +
					"/:policyId/jobs/:jobId/retry": {Operate, "RetryTriggerPolicyJob"},
				"PUT/v1/instances/:instanceId/triggers/policies/:policyId/enable":     {Operate, "EnableTriggerPolicy"},
				"POST/v1/instances/:instanceId/accelerators/policies":                 {Operate, "CreateAcceleratorPolicy"},
				"GET/v1/instances/:instanceId/accelerators/policies":                  {Read, Operate, "ListAcceleratorPolicy"},
				"GET/v1/instances/:instanceId/accelerators/policies/:policyId":        {Read, Operate, "DescribeAcceleratorPolicy"},
				"DELETE/v1/instances/:instanceId/accelerators/policies/:policyId":     {Operate, "DeleteAcceleratorPolicy"},
				"PUT/v1/instances/:instanceId/accelerators/policies/:policyId":        {Operate, "UpdateAcceleratorPolicy"},
				"DELETE/v1/instances/:instanceId/accelerators/policies":               {Operate, "BatchDeleteAcceleratorPolicy"},
				"PUT/v1/instances/:instanceId/accelerators/policies/:policyId/enable": {Operate, "EnableAcceleratorPolicy"},
				"POST/v1/instances/:instanceId/projects":                              {Operate, "CreateProject"},
				"DELETE/v1/instances/:instanceId/projects":                            {Operate, "DeleteProjects"},
				"GET/v1/instances/:instanceId/projects":                               {Read, Operate, "ListProjects"},
				"PUT/v1/instances/:instanceId/projects/:projectName":                  {Operate, "UpdateProject"},
				"GET/v1/instances/:instanceId/projects/:projectName":                  {Read, Operate, "DescribeProject"},
				"DELETE/v1/instances/:instanceId/projects/:projectName":               {Operate, "DeleteProject"},
				"GET/v1/instances/:instanceId/projects/:projectName/charts" +
					"/download/:filename": {Read, Operate, "DownloadChart"},
				"POST/v1/instances/:instanceId/projects/:projectName/charts":   {Operate, "UploadChart"},
				"GET/v1/instances/:instanceId/projects/:projectName/charts":    {Read, Operate, "ListCharts"},
				"DELETE/v1/instances/:instanceId/projects/:projectName/charts": {Operate, "DeleteCharts"},
				"DELETE/v1/instances/:instanceId/projects/:projectName/charts" +
					"/:chartName": {Operate, "DeleteChart"},
				"GET/v1/instances/:instanceId/projects/:projectName/charts" +
					"/:chartName/versions": {Read, Operate, "GetChartVersion"},
				"DELETE/v1/instances/:instanceId/projects/:projectName/charts" +
					"/:chartName/versions": {Operate, "DeleteChartVersions"},
				"DELETE/v1/instances/:instanceId/projects/:projectName/charts" +
					"/:chartName/versions/:chartVersion": {Operate, "DeleteChartVersion"},
				"GET/v1/instances/:instanceId/projects/:projectName/repositories":    {Read, Operate, "ListRepositories"},
				"DELETE/v1/instances/:instanceId/projects/:projectName/repositories": {Operate, "DeleteRepositories"},
				"DELETE/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName": {Operate, "DeleteRepository"},
				"PUT/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName": {Operate, "UpdateRepository"},
				"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName": {Read, Operate, "DescribeRepository"},
				"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/tags": {Read, Operate, "ListTags"},
				"DELETE/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/tags": {Operate, "DeleteTags"},
				"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/tags/:tagName": {Read, Operate, "DescribeTag"},
				"DELETE/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/tags/:tagName": {Operate, "DeleteTag"},
				"POST/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/tags/:tagName/scan": {Operate, "ScanImage"},
				"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/tags/:tagName/scan/:reportId/log": {Read, Operate, "DescribeScanImageLog"},
				"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/tags/:tagName/scanoverview": {Read, Operate, "DescribeTagScan"},
				"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/tags/:tagName/buildhistory": {Read, Operate, "GetTagBuildHistory"},
				"POST/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/imagebuilds": {Operate, "PostBuildRepository"},
				"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/imagebuilds": {Read, Operate, "GetListBuildRepositoryTask"},
				"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/imagebuilds/:buildId": {Read, Operate, "GetBuildRepositoryTask"},
				"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/imagebuilds/:buildId/log": {Read, Operate, "GetBuildRepositoryTaskLog"},
				"DELETE/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/imagebuilds/:buildId": {Operate, "DeleteBuildRepositoryTask"},
				"DELETE/v1/instances/:instanceId/projects/:projectName/repositories" +
					"/:repositoryName/imagebuilds": {Operate, "BatchDeleteBuildRepositoryTask"},
				"GET/v1/instances/:instanceId/immutable":                     {Read, Operate, "ListImmutable"},
				"POST/v1/instances/:instanceId/immutable":                    {Operate, "CreateImmutable"},
				"DELETE/v1/instances/:instanceId/immutable":                  {Operate, "DeleteImmutables"},
				"GET/v1/instances/:instanceId/immutable/project":             {Read, Operate, "ListImmutableProject"},
				"GET/v1/instances/:instanceId/immutable/:immutableId":        {Read, Operate, "GetImmutable"},
				"PUT/v1/instances/:instanceId/immutable/:immutableId":        {Operate, "UpdateImmutable"},
				"DELETE/v1/instances/:instanceId/immutable/:immutableId":     {Operate, "DeleteImmutable"},
				"PUT/v1/instances/:instanceId/immutable/:immutableId/enable": {Operate, "EnableImmutable"},
				"GET/v1/instances/:instanceId/domain":                        {Read, Operate, "ListDomains"},
				"GET/v1/instances/:instanceId/domain/:domainName":            {Read, Operate, "GetDomain"},
				"GET/v1/instances/:instanceId/domain/:domainName/check":      {Read, Operate, "CheckDomain"},
				"POST/v1/instances/:instanceId/domain":                       {Operate, "CreateDomain"},
				"PUT/v1/instances/:instanceId/domain/:domainName":            {Operate, "UpdateDomain"},
				"DELETE/v1/instances/:instanceId/domain/:domainName":         {Operate, "DeleteDomain"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pc := &PermissionCheck{
				region:  tt.fields.region,
				env:     tt.fields.env,
				service: tt.fields.service,
				clients: tt.fields.clients,
			}
			assert.Equalf(t, tt.want, pc.permissionTable(), "permissionTable()")
		})
	}
}
