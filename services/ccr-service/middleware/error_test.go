package middleware

import (
	"testing"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/stretchr/testify/assert"
)

func TestConflictError(t *testing.T) {
	type args struct {
		requestId string
		message   string
	}
	tests := []struct {
		name string
		args args
		want *bce.BceServiceError
	}{
		{
			name: "case1",
			args: args{
				requestId: "xxxx",
				message:   "error is empty",
			},
			want: gin_context.ConflictError("xxxx", "error is empty"),
		},
		{
			name: "case2",
			args: args{
				requestId: "xxxx",
			},
			want: gin_context.ConflictError("xxxx", ""),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, gin_context.ConflictError(tt.args.requestId, tt.args.message), "ConflictError(%v, %v)", tt.args.requestId, tt.args.message)
		})
	}
}
