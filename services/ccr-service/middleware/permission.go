package middleware

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	sdkiam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
	ccrerr "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/errors"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
)

type PermissionCheck struct {
	region  string
	env     string
	service string

	clients clientset.ClientSetInterface
}

func NewPermissionCheck(clis clientset.ClientSetInterface, region, env, service string) *PermissionCheck {
	return &PermissionCheck{
		clients: clis,
		region:  region,
		service: service,
		env:     env,
	}
}

func (pc *PermissionCheck) getResolver(c *gin.Context) ResourceResolver {
	// v1/instances/xxxx/projects/xxxx
	switch {
	case c.Param("instanceId") != "" && c.Param("projectName") != "":
		return NamespaceResolver
	case c.Param("instanceId") != "":
		return InstanceResolver
	default:
		return DefaultResourceResolver
	}
}

func (pc *PermissionCheck) permissionTable() map[string][]string {
	return map[string][]string{
		"GET/v1/registrytypes":                                       {Read, Operate, "GetRegistryTypes"},
		"GET/v1/users/profile":                                       {Read, Operate, "GetUserProfile"},
		"GET/v1/users/cert":                                          {Read, Operate, "GetUserCert"},
		"POST/v1/order_executor/execute":                             {FullControl, "ExecuteOrder"},
		"GET/v1/order_executor/check":                                {Read, Operate, "CheckOrder"},
		"POST/v1/instances":                                          {FullControl, "CreateInstance"},
		"POST/v1/accelerators/policies/filters":                      {Operate, "TestPolicyFilter"},
		"GET/v1/instances":                                           {Read, Operate, "ListInstance"},
		"POST/v1/tag/resources":                                      {Read, Operate, "ListInstanceForTag"},
		"GET/v1/iam/resources":                                       {Read, Operate, "ListInstanceForIam"},
		"POST/v1/instances/renew":                                    {FullControl, "RenewInstance"},
		"GET/v1/instances/:instanceId":                               {Read, Operate, "DescribeInstance"},
		"PUT/v1/instances/:instanceId":                               {Operate, "UpdateInstance"},
		"PUT/v1/instances/:instanceId/upgrade":                       {FullControl, "UpgradeInstance"},
		"PUT/v1/instances/:instanceId/tags":                          {Operate, "AddInstanceTag"},
		"PUT/v1/instances/:instanceId/resgroups":                     {Operate, "BindResourceGroup"},
		"GET/v1/instances/:instanceId/repositories":                  {Read, Operate, "ListAllRepositories"},
		"GET/v1/instances/:instanceId/privatelinks":                  {Read, Operate, "GetPrivateNetworks"},
		"POST/v1/instances/:instanceId/privatelinks":                 {Operate, "CreatePrivateNetwork"},
		"DELETE/v1/instances/:instanceId/privatelinks":               {Operate, "DeletePrivateNetwork"},
		"GET/v1/instances/:instanceId/publiclinks":                   {Read, Operate, "GetPublicNetworks"},
		"PUT/v1/instances/:instanceId/publiclinks":                   {Operate, "UpdatePublicNetwork"},
		"DELETE/v1/instances/:instanceId/publiclinks/whitelist":      {Operate, "DeletePublicWhitelist"},
		"POST/v1/instances/:instanceId/publiclinks/whitelist":        {Operate, "AddPublicWhitelist"},
		"PUT/v1/instances/:instanceId/credential":                    {Read, Operate, "ResetCredential"},
		"POST/v1/instances/:instanceId/credential":                   {Read, Operate, "CreateCredential"},
		"POST/v1/instances/:instanceId/gcs":                          {Operate, "CreateGarbageCollection"},
		"GET/v1/instances/:instanceId/gcs":                           {Read, Operate, "ListGarbageCollection"},
		"GET/v1/instances/:instanceId/gcs/:gcId/log":                 {Read, Operate, "DescribeGarbageCollectionLog"},
		"POST/v1/instances/:instanceId/registries":                   {Operate, "CreateRemoteRegistry"},
		"GET/v1/instances/:instanceId/registries/:registryId":        {Read, Operate, "DescribeRemoteRegistry"},
		"GET/v1/instances/:instanceId/registries":                    {Read, Operate, "ListRemoteRegistries"},
		"PUT/v1/instances/:instanceId/registries/:registryId":        {Operate, "UpdateRemoteRegistry"},
		"DELETE/v1/instances/:instanceId/registries/:registryId":     {Operate, "DeleteRemoteRegistry"},
		"GET/v1/instances/:instanceId/executions":                    {Read, Operate, "ListReplicationExecutions"},
		"POST/v1/instances/:instanceId/executions":                   {Operate, "StartReplication"},
		"PUT/v1/instances/:instanceId/executions/:executionId":       {Operate, "StopReplication"},
		"GET/v1/instances/:instanceId/executions/:executionId/tasks": {Read, Operate, "ListExecutionTasks"},
		"GET/v1/instances/:instanceId/executions" +
			"/:executionId/tasks/:taskId/log": {Read, Operate, "GetExecutionTaskLog"},
		"POST/v1/instances/:instanceId/replications":                    {Operate, "CreateReplication"},
		"GET/v1/instances/:instanceId/replications":                     {Read, Operate, "ListReplication"},
		"GET/v1/instances/:instanceId/replications/:policyId":           {Read, Operate, "DescribeReplication"},
		"DELETE/v1/instances/:instanceId/replications/:policyId":        {Operate, "DeleteReplication"},
		"PUT/v1/instances/:instanceId/replications/:policyId":           {Operate, "UpdateReplication"},
		"POST/v1/instances/:instanceId/syncs":                           {Operate, "CreateSync"},
		"GET/v1/instances/:instanceId/syncs":                            {Read, Operate, "ListSync"},
		"GET/v1/instances/:instanceId/syncs/:policyId":                  {Read, Operate, "DescribeSync"},
		"DELETE/v1/instances/:instanceId/syncs/:policyId":               {Operate, "DeleteSync"},
		"PUT/v1/instances/:instanceId/syncs/:policyId":                  {Operate, "UpdateSync"},
		"POST/v1/instances/:instanceId/triggers/policies":               {Operate, "CreateTriggerPolicy"},
		"GET/v1/instances/:instanceId/triggers/policies":                {Read, Operate, "ListTriggerPolicy"},
		"DELETE/v1/instances/:instanceId/triggers/policies":             {Operate, "BatchDeleteTriggerPolicy"},
		"GET/v1/instances/:instanceId/triggers/policies/:policyId":      {Read, Operate, "DescribeTriggerPolicy"},
		"DELETE/v1/instances/:instanceId/triggers/policies/:policyId":   {Operate, "DeleteTriggerPolicy"},
		"PUT/v1/instances/:instanceId/triggers/policies/:policyId":      {Operate, "UpdateTriggerPolicy"},
		"POST/v1/instances/:instanceId/triggers/policies/targets":       {Operate, "TestTriggerPolicyTarget"},
		"GET/v1/instances/:instanceId/triggers/policies/:policyId/jobs": {Read, Operate, "ListTriggerPolicyJob"},
		"PUT/v1/instances/:instanceId/triggers/policies" +
			"/:policyId/jobs/:jobId/retry": {Operate, "RetryTriggerPolicyJob"},
		"PUT/v1/instances/:instanceId/triggers/policies/:policyId/enable":     {Operate, "EnableTriggerPolicy"},
		"POST/v1/instances/:instanceId/accelerators/policies":                 {Operate, "CreateAcceleratorPolicy"},
		"GET/v1/instances/:instanceId/accelerators/policies":                  {Read, Operate, "ListAcceleratorPolicy"},
		"GET/v1/instances/:instanceId/accelerators/policies/:policyId":        {Read, Operate, "DescribeAcceleratorPolicy"},
		"DELETE/v1/instances/:instanceId/accelerators/policies/:policyId":     {Operate, "DeleteAcceleratorPolicy"},
		"PUT/v1/instances/:instanceId/accelerators/policies/:policyId":        {Operate, "UpdateAcceleratorPolicy"},
		"DELETE/v1/instances/:instanceId/accelerators/policies":               {Operate, "BatchDeleteAcceleratorPolicy"},
		"PUT/v1/instances/:instanceId/accelerators/policies/:policyId/enable": {Operate, "EnableAcceleratorPolicy"},
		"POST/v1/instances/:instanceId/projects":                              {Operate, "CreateProject"},
		"DELETE/v1/instances/:instanceId/projects":                            {Operate, "DeleteProjects"},
		"GET/v1/instances/:instanceId/projects":                               {Read, Operate, "ListProjects"},
		"PUT/v1/instances/:instanceId/projects/:projectName":                  {Operate, "UpdateProject"},
		"GET/v1/instances/:instanceId/projects/:projectName":                  {Read, Operate, "DescribeProject"},
		"DELETE/v1/instances/:instanceId/projects/:projectName":               {Operate, "DeleteProject"},
		"GET/v1/instances/:instanceId/projects/:projectName/charts" +
			"/download/:filename": {Read, Operate, "DownloadChart"},
		"POST/v1/instances/:instanceId/projects/:projectName/charts":   {Operate, "UploadChart"},
		"GET/v1/instances/:instanceId/projects/:projectName/charts":    {Read, Operate, "ListCharts"},
		"DELETE/v1/instances/:instanceId/projects/:projectName/charts": {Operate, "DeleteCharts"},
		"DELETE/v1/instances/:instanceId/projects/:projectName/charts" +
			"/:chartName": {Operate, "DeleteChart"},
		"GET/v1/instances/:instanceId/projects/:projectName/charts" +
			"/:chartName/versions": {Read, Operate, "GetChartVersion"},
		"DELETE/v1/instances/:instanceId/projects/:projectName/charts" +
			"/:chartName/versions": {Operate, "DeleteChartVersions"},
		"DELETE/v1/instances/:instanceId/projects/:projectName/charts" +
			"/:chartName/versions/:chartVersion": {Operate, "DeleteChartVersion"},
		"GET/v1/instances/:instanceId/projects/:projectName/repositories":    {Read, Operate, "ListRepositories"},
		"DELETE/v1/instances/:instanceId/projects/:projectName/repositories": {Operate, "DeleteRepositories"},
		"DELETE/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName": {Operate, "DeleteRepository"},
		"PUT/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName": {Operate, "UpdateRepository"},
		"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName": {Read, Operate, "DescribeRepository"},
		"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/tags": {Read, Operate, "ListTags"},
		"DELETE/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/tags": {Operate, "DeleteTags"},
		"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/tags/:tagName": {Read, Operate, "DescribeTag"},
		"DELETE/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/tags/:tagName": {Operate, "DeleteTag"},
		"POST/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/tags/:tagName/scan": {Operate, "ScanImage"},
		"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/tags/:tagName/scan/:reportId/log": {Read, Operate, "DescribeScanImageLog"},
		"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/tags/:tagName/scanoverview": {Read, Operate, "DescribeTagScan"},
		"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/tags/:tagName/buildhistory": {Read, Operate, "GetTagBuildHistory"},
		"POST/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/imagebuilds": {Operate, "PostBuildRepository"},
		"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/imagebuilds": {Read, Operate, "GetListBuildRepositoryTask"},
		"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/imagebuilds/:buildId": {Read, Operate, "GetBuildRepositoryTask"},
		"GET/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/imagebuilds/:buildId/log": {Read, Operate, "GetBuildRepositoryTaskLog"},
		"DELETE/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/imagebuilds/:buildId": {Operate, "DeleteBuildRepositoryTask"},
		"DELETE/v1/instances/:instanceId/projects/:projectName/repositories" +
			"/:repositoryName/imagebuilds": {Operate, "BatchDeleteBuildRepositoryTask"},
		"GET/v1/instances/:instanceId/immutable":                     {Read, Operate, "ListImmutable"},
		"POST/v1/instances/:instanceId/immutable":                    {Operate, "CreateImmutable"},
		"DELETE/v1/instances/:instanceId/immutable":                  {Operate, "DeleteImmutables"},
		"GET/v1/instances/:instanceId/immutable/project":             {Read, Operate, "ListImmutableProject"},
		"GET/v1/instances/:instanceId/immutable/:immutableId":        {Read, Operate, "GetImmutable"},
		"PUT/v1/instances/:instanceId/immutable/:immutableId":        {Operate, "UpdateImmutable"},
		"DELETE/v1/instances/:instanceId/immutable/:immutableId":     {Operate, "DeleteImmutable"},
		"PUT/v1/instances/:instanceId/immutable/:immutableId/enable": {Operate, "EnableImmutable"},
		"GET/v1/instances/:instanceId/domain":                        {Read, Operate, "ListDomains"},
		"GET/v1/instances/:instanceId/domain/:domainName":            {Read, Operate, "GetDomain"},
		"GET/v1/instances/:instanceId/domain/:domainName/check":      {Read, Operate, "CheckDomain"},
		"POST/v1/instances/:instanceId/domain":                       {Operate, "CreateDomain"},
		"PUT/v1/instances/:instanceId/domain/:domainName":            {Operate, "UpdateDomain"},
		"DELETE/v1/instances/:instanceId/domain/:domainName":         {Operate, "DeleteDomain"},
	}
}

func (pc *PermissionCheck) getPermissions(c *gin.Context) []string {
	return pc.permissionTable()[c.Request.Method+c.FullPath()]
}

func (pc *PermissionCheck) GetOperation(c *gin.Context) string {
	return pc.getOperation(c.Request.Method, c.FullPath())
}

// getOperation 返回给定方法名和完整路径对应的操作
//
// 参数:
// method - 请求方法名
// fullpath - 请求的完整路径
//
// 返回值:
// 返回对应的操作，如果未找到则返回空字符串
func (pc *PermissionCheck) getOperation(method string, fullpath string) string {
	perms := pc.permissionTable()[method+fullpath]
	if len(perms) > 1 {
		return perms[len(perms)-1]
	}

	return ""
}

// Verify 返回一个gin.HandlerFunc，用于在处理请求之前验证权限
func (pc *PermissionCheck) Verify() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestId := c.Request.Header.Get(REQID_HEADER)
		logger := gin_context.LoggerFromContext(c)

		resources, err := pc.getResolver(c).Resource(c)
		if err != nil {
			logger.Errorf("resolve resource failed: %v", err)
			gin_context.E(c, gin_context.InternalServerError(requestId))
			return
		}

		perms := pc.getPermissions(c)
		if len(perms) == 0 {
			logger.Errorf("no perms found for request: %v", c.Request.Method+c.FullPath())
			gin_context.E(c, gin_context.UnauthorizedError(requestId))
			return
		}

		var hasPermission bool
		for _, res := range resources {

			if err := pc.VerifyPermission(c, res, perms); err != nil {
				logger.Errorf("verify permission failed: %s", err)
				var unauthorizedError *ccrerr.UnauthorizedError
				if errors.As(err, &unauthorizedError) {
					continue
				}
				gin_context.E(c, gin_context.InternalServerError(requestId))
				return
			}
			hasPermission = true
			break
		}

		if !hasPermission {
			logger.Errorf("no permission to do the request")
			gin_context.E(c, gin_context.UnauthorizedError(requestId))
			return
		}

		if pc.env == common.EnvSANDBOX {
			c.AbortWithStatusJSON(http.StatusOK, "sandbox environment, stop")
		}
	}
}

// VerifyPermission 用于验证用户对某个资源的权限
//
// c: gin.Context类型，请求上下文
// res: string类型，资源标识
// perms: []string类型，权限列表
//
// 返回 error 类型，如果验证通过则返回 nil，否则返回错误信息
func (pc *PermissionCheck) VerifyPermission(c *gin.Context, res string, perms []string) error {
	logger := gin_context.LoggerFromContext(c)
	userId := UserIDFromContext(c)

	pr := &sdkiam.PermissionRequest{
		Service:       pc.service,
		Region:        common.GetRealRegion(pc.region),
		Resource:      res,
		ResourceOwner: common.DefaultIamOwner,
		Permission:    perms,
	}

	instanceID := c.Param("instanceId")

	if instanceID != "" {
		tagMap, err := pc.GetResourceTagMap(c, []string{instanceID})
		if err != nil {
			logger.Errorf("get resource tag failed: %v", err)
			return err
		}
		variables := make(map[string]interface{})
		if tags, ok := tagMap[instanceID]; ok {
			for _, tag := range tags {
				variables[tag.TagKey] = tag.TagValue
			}
		}
		pr.RequestContext = sdkiam.RequestContext{
			Variables: variables,
		}
	}

	result, err := pc.clients.InternalIam().VerifyUserPermission(userId, pr)
	if err != nil {
		logger.Errorf("verify permission failed: %s", err)
		return err
	}
	logger.Infof("verify user permission response: %#v\n", result)

	if result.VerifyResult.Effect == "DENY" || result.VerifyResult.Effect == "DEFAULT_DENY" {
		return &ccrerr.UnauthorizedError{Effect: result.VerifyResult.Effect}
	}
	return nil
}

func (pc *PermissionCheck) GetResourceTagMap(ctx context.Context, instanceIDs []string) (map[string][]model.Tag, error) {
	requestId := gin_context.RequestIdFromContext(ctx)
	logger := gin_context.LoggerFromContext(ctx)
	accountID := AccountIDFromContext(ctx)
	userID := UserIDFromContext(ctx)

	tagResourceMap := map[string][]model.Tag{}
	if len(instanceIDs) == 0 {
		return tagResourceMap, nil
	}

	tagClient, err := pc.clients.LogicTagClientForAccount(accountID, userID)
	if err != nil {
		logger.Errorf("new logic tag client failed: %s", err)
		return nil, err
	}

	tags, err := tagClient.ListTags(requestId, true, &logictag.ListTagsArgs{
		Regions:      []string{common.GetRealRegion(pc.region)},
		ServiceTypes: []string{"CCR"},
		ResourceIds:  instanceIDs,
	})
	if err != nil {
		return nil, fmt.Errorf("[ListInstanceLocal] list tags failed: %s", err)
	}

	if tags != nil && len(tags.TagAssociationFulls) > 0 {

		for _, tagAssociation := range tags.TagAssociationFulls {
			resourceID := tagAssociation.ResourceID
			if _, ok := tagResourceMap[resourceID]; ok {
				tagResourceMap[resourceID] = append(tagResourceMap[resourceID], model.Tag{
					TagKey:   tagAssociation.TagKey,
					TagValue: tagAssociation.TagValue,
				})
			} else {
				tagResourceMap[resourceID] = []model.Tag{{
					TagKey:   tagAssociation.TagKey,
					TagValue: tagAssociation.TagValue,
				}}
			}
		}
	}
	return tagResourceMap, nil
}
