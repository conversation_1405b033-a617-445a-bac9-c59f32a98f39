package middleware

import (
	"fmt"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	mockmodels "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/models/mock"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
)

func TestImageBuildIDCheck(t *testing.T) {
	type args struct {
		cli models.Interface
		ctx *gin.Context
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
		{
			name: "no build id",
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				return args{
					ctx: ctx,
				}
			}(),
			want: 200,
		},
		{
			name: "invalid build id",
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "buildId", Value: "a"})
				return args{
					ctx: ctx,
				}
			}(),
			want: 400,
		},
		{
			name: "build not found",
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "buildId", Value: "1"})
				cli := mockmodels.NewMockInterface(gomock.NewController(t))
				cli.EXPECT().GetImageBuild(gomock.Any()).Return(nil, gorm.ErrRecordNotFound)
				return args{
					ctx: ctx,
					cli: cli,
				}
			}(),
			want: 404,
		},
		{
			name: "get build failed",
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "buildId", Value: "1"})
				cli := mockmodels.NewMockInterface(gomock.NewController(t))
				cli.EXPECT().GetImageBuild(gomock.Any()).Return(nil, fmt.Errorf("failed"))
				return args{
					ctx: ctx,
					cli: cli,
				}
			}(),
			want: 500,
		},
		{
			name: "account id mismatch",
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "buildId", Value: "12"})
				ctx.Set(ACCOUNT_ID_IDENTITY, "t1")

				cli := mockmodels.NewMockInterface(gomock.NewController(t))
				cli.EXPECT().GetImageBuild(gomock.Any()).Return(&models.ImageBuild{}, nil)

				return args{
					ctx: ctx,
					cli: cli,
				}
			}(),
			want: 404,
		},
		{
			name: "normal",
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "buildId", Value: "12"})
				ctx.Set(ACCOUNT_ID_IDENTITY, "t1")

				cli := mockmodels.NewMockInterface(gomock.NewController(t))
				cli.EXPECT().GetImageBuild(gomock.Any()).Return(&models.ImageBuild{
					AccountID: "t1",
				}, nil)

				return args{
					ctx: ctx,
					cli: cli,
				}
			}(),
			want: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ImageBuildIDCheck(tt.args.cli)(tt.args.ctx)
			if tt.want != tt.args.ctx.Writer.Status() {
				t.Errorf("ImageBuildIDCheck status = %d, want %d", tt.args.ctx.Writer.Status(), tt.want)
			}
		})
	}
}
