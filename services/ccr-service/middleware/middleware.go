package middleware

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/gin-gonic/gin"
	harbormodels "github.com/goharbor/harbor/src/common/models"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"sigs.k8s.io/controller-runtime/pkg/client"

	sdkiam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	projectapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/session"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
)

const (
	REQID_HEADER                    = "x-bce-request-id"
	ACCOUNT_ID_IDENTITY             = "ACCOUNT_ID"
	ACCOUNT_NAME_IDENTITY           = "ACCOUNT_NAME"
	USER_DI_IDENTITY                = "USER_ID"
	USER_NAME_IDENTITY              = "USER_NAME"
	NETWORK_OBJECT_IDENTIY          = "NETWORK_IDENTITY"
	INSTANCE_OBJECT_IDENTITY        = "INSTANCE_IDENTITY"
	HARBOR_CLIENT_IDENTITY          = "HARBOR_CLIENT_IDENTITY"
	HARBOR_CLIENT_REAL_IDENTITY     = "HARBOR_CLIENT_REAL_IDENTITY"
	HARBOR_CLIENT_SIMULATE_IDENTITY = "HARBOR_CLIENT_SIMULATE_IDENTITY"

	ORDER_DI_IDENTITY = "ORDER_ID"
)

func IAMMiddleware(clis clientset.ClientSetInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestId := gin_context.RequestIdFromContext(c)
		var accountId, accountName, userId, userName string

		token, err := clis.InternalIam().ValidatorRequest(c.Request)
		if err != nil {
			logger.Errorf("get user info failed: %s", err)

			var bceErr *sdkiam.BceServiceError
			if errors.As(err, &bceErr) {
				gin_context.E(c, bce.NewBceServiceError(bceErr.Code, bceErr.Message, bceErr.RequestId, bceErr.StatusCode))
				return
			}

			gin_context.E(c, gin_context.InternalServerError(requestId))
			return
		}

		if token.User.Domain.ID == "" || token.User.ID == "" {
			logger.Errorf("no user provided")
			gin_context.E(c, bce.NewBceServiceError(
				http.StatusText(http.StatusBadRequest),
				"cannot get user info",
				requestId,
				http.StatusBadRequest,
			))
			return
		}
		logger.Infof("account name: %s", token.User.Domain.Name)

		accountId = token.User.Domain.ID
		accountName = token.User.Domain.Name
		userId = token.User.ID
		userName = token.User.Name

		if strings.ToLower(accountId) == "default" {
			accountName = token.User.Name
		}

		logger.Infof("set context with accountId: %s,accountName: %s, userId: %s, userName: %s", accountId, accountName, userId, userName)
		c.Set(ACCOUNT_ID_IDENTITY, accountId)
		c.Set(ACCOUNT_NAME_IDENTITY, accountName)
		c.Set(USER_DI_IDENTITY, userId)
		c.Set(USER_NAME_IDENTITY, userName)
	}
}

func NetworkItemMiddleware(clis clientset.ClientSetInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestID := c.Request.Header.Get(REQID_HEADER)
		var netWorkObj v1alpha1.CNCNetwork
		instanceId := c.Param("instanceId")

		err := clis.K8sClient().Get(c, client.ObjectKey{Namespace: instanceId, Name: instanceId}, &netWorkObj)
		if err != nil && !apierrors.IsNotFound(err) {
			logger.Errorf("get object failed for instance %v: %v", instanceId, err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}

		if apierrors.IsNotFound(err) {
			gin_context.E(c, gin_context.NotFoundError(requestID, "instance not found"))
			return
		}

		c.Set(NETWORK_OBJECT_IDENTIY, &netWorkObj)
	}
}

func InstanceMiddleware(clis clientset.ClientSetInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestID := c.Request.Header.Get(REQID_HEADER)
		var ccr v1alpha1.CCR
		instanceId := c.Param("instanceId")

		err := clis.K8sClient().Get(c, client.ObjectKey{Name: instanceId}, &ccr)
		if apierrors.IsNotFound(err) {
			logger.Errorf("ccr object is not found: %v", instanceId)
			gin_context.E(c, gin_context.NotFoundError(requestID, fmt.Sprintf("instance %v is not found", instanceId)))
			return
		}

		if err != nil {
			logger.Errorf("get ccr object failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}
		logger.WithField("instanceId", ccr.GetName())

		c.Set(INSTANCE_OBJECT_IDENTITY, &ccr)
	}
}

func OrderExecuteMiddleware(clis clientset.ClientSetInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestId := gin_context.RequestIdFromContext(c)

		if AccountNameFromContext(c) != "order_execute" {
			logger.Errorf("no permission to do the request account name: %s", AccountNameFromContext(c))
			gin_context.E(c, gin_context.UnauthorizedError(requestId))
			return
		}

		var req model.ExecuteOrderRequest
		if err := c.Bind(&req); err != nil {
			logger.Errorf("bind request body failed: %s", err)
			gin_context.E(c, gin_context.BadRequestError(requestId))
			return
		}
		orderId := req.OrderId

		// 查询billing订单
		order, err := clis.OrderClient().GetOrderDetail(requestId, orderId)
		if err != nil {
			logger.Errorf("get order detail failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestId))
			return
		}
		logger.WithField("orderId", order.Uuid)
		// 替换accountId 和userId 为订单中实际用户的信息
		accountId := order.AccountId
		userId := order.UserId
		if userId == "" {
			userId = accountId
		}
		logger.Infof("orderExecute: set context with accountId:  %s, userId: %s, orderId: %s", accountId, userId, orderId)
		c.Set(ACCOUNT_ID_IDENTITY, accountId)
		c.Set(USER_DI_IDENTITY, userId)
		c.Set(ORDER_DI_IDENTITY, orderId)
	}
}

func OrderCheckMiddleware(clis clientset.ClientSetInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestId := gin_context.RequestIdFromContext(c)

		logger.Debugf("account name: %s", AccountNameFromContext(c))
		if AccountNameFromContext(c) != "order_execute" {
			logger.Errorf("no permission to do the request account name: %s", AccountNameFromContext(c))
			gin_context.E(c, gin_context.UnauthorizedError(requestId))
			return
		}

		orderId := c.Query("orderId")

		// 查询billing订单
		order, err := clis.OrderClient().GetOrderDetail(requestId, orderId)
		if err != nil {
			logger.Errorf("get order detail failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestId))
			return
		}
		logger.WithField("orderId", order.Uuid)
		// 替换accountId 和userId 为订单中实际用户的信息
		accountId := order.AccountId
		userId := order.UserId
		if userId == "" {
			userId = accountId
		}
		logger.Infof("orderCheck: set context with accountId:  %s, userId: %s", accountId, userId)
		c.Set(ACCOUNT_ID_IDENTITY, accountId)
		c.Set(USER_DI_IDENTITY, userId)
	}
}

func ResourceMiddleware(clis clientset.ClientSetInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestId := gin_context.RequestIdFromContext(c)

		if AccountNameFromContext(c) != "billing" {
			logger.Errorf("no permission to do the request account name: %s", AccountNameFromContext(c))
			gin_context.E(c, gin_context.UnauthorizedError(requestId))
			return
		}
	}
}

func ProjectMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestID := c.Request.Header.Get(REQID_HEADER)
		projectName := c.Param("projectName")

		harborClient := c.Value(HARBOR_CLIENT_IDENTITY).(*harbor.HarborClient)

		if _, err := harborClient.V2Client.Project.HeadProject(
			projectapi.NewHeadProjectParamsWithContext(c).WithProjectName(projectName).WithXRequestID(&requestID),
			harborClient.AuthInfo); err != nil {

			if _, ok := err.(*projectapi.HeadProjectNotFound); ok {
				logger.Errorf("use swagger client call harbor failed: %s", err)
				gin_context.E(c, gin_context.NotFoundError(requestID, fmt.Sprintf("project %v is not found", projectName)))
				return
			}
			logger.Errorf("get project failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}
	}
}

func HarborClientMiddleware(clis clientset.ClientSetInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestID := c.Request.Header.Get(REQID_HEADER)
		instanceId := c.Param("instanceId")
		username := UserNameFromContext(c)

		harborClient, err := clis.HarborClient(instanceId)
		if err != nil {
			logger.Errorf("get harbor client failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}
		c.Set(HARBOR_CLIENT_IDENTITY, harborClient)

		sessionTokenService := session.NewSessionToken(harborClient)
		sid, err := sessionTokenService.GetSessionToken(c, &harbormodels.User{
			Username:        username,
			SysAdminFlag:    true,
			AdminRoleInAuth: true,
		})
		if err != nil {
			logger.Errorf("get session token failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}

		harborClientSimulate, err := clis.HarborClientSimulate(instanceId, sid)
		if err != nil {
			logger.Errorf("get harbor client failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}
		c.Set(HARBOR_CLIENT_SIMULATE_IDENTITY, harborClientSimulate)

	}
}

func RealUserNameMiddleware(iamCli iam.ClientInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestID := gin_context.RequestIdFromContext(c)
		// check root
		accountID, userID := AccountIDFromContext(c), UserIDFromContext(c)
		if accountID == "" || accountID != userID {
			return
		}

		userID = c.Query("userId")
		if userID == "" || userID == accountID {
			return
		}

		userInfo, err := iamCli.GetUser(accountID, userID)
		if err != nil {
			logger.Errorf("get user info of %s failed: %s", userID, err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}

		if userInfo == nil {
			logger.Errorf("no user found for %s", userID)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}

		c.Set(USER_DI_IDENTITY, userInfo.ID)
		c.Set(USER_NAME_IDENTITY, userInfo.Name)
	}
}

func AccountIDFromContext(ctx context.Context) string {
	if accountId, ok := ctx.Value(ACCOUNT_ID_IDENTITY).(string); ok {
		return accountId
	}
	return ""
}

func AccountNameFromContext(ctx context.Context) string {
	if accountId, ok := ctx.Value(ACCOUNT_NAME_IDENTITY).(string); ok {
		return accountId
	}
	return ""
}

func UserIDFromContext(ctx context.Context) string {
	if userId, ok := ctx.Value(USER_DI_IDENTITY).(string); ok {
		return userId
	}
	return ""
}

func OrderIDFromContext(ctx context.Context) string {
	if orderId, ok := ctx.Value(ORDER_DI_IDENTITY).(string); ok {
		return orderId
	}
	return ""
}

func UserNameFromContext(ctx context.Context) string {
	if username, ok := ctx.Value(USER_NAME_IDENTITY).(string); ok {
		return username
	}
	return ""
}

func InstanceFromContext(ctx context.Context) *v1alpha1.CCR {
	return ctx.Value(INSTANCE_OBJECT_IDENTITY).(*v1alpha1.CCR)
}

func HarborClientFromContext(ctx context.Context) *harbor.HarborClient {
	return ctx.Value(HARBOR_CLIENT_IDENTITY).(*harbor.HarborClient)
}

func HarborClientSimulateFromContext(ctx context.Context) *harbor.HarborClient {
	return ctx.Value(HARBOR_CLIENT_SIMULATE_IDENTITY).(*harbor.HarborClient)
}

func HarborClientByUserFromContext(ctx context.Context, useSim bool) *harbor.HarborClient {
	if useSim {
		return ctx.Value(HARBOR_CLIENT_SIMULATE_IDENTITY).(*harbor.HarborClient)
	} else {
		return ctx.Value(HARBOR_CLIENT_IDENTITY).(*harbor.HarborClient)
	}
}
