package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	bcesdkiam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	testbilling "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/billing"
	testingiam "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/iam"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-service/clientset"
)

type GinResponseWriter struct {
	http.ResponseWriter
}

func (g *GinResponseWriter) CloseNotify() <-chan bool {
	return make(chan bool)
}

func newGinResponseWriter() http.ResponseWriter {
	return &GinResponseWriter{httptest.NewRecorder()}
}

func TestIAMMiddleware(t *testing.T) {
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	clis := &testingclientset.ClientSet{}
	iamMiddleware := IAMMiddleware(clis)

	iamClient := &testingiam.Client{}
	mock.OnAnything(clis, "InternalIam").Return(iamClient, nil)

	mock.OnAnything(clis.InternalIam(), "ValidatorRequest").Return(&iam.Token{
		Domain: iam.Domain{
			ID:   "xxxx",
			Name: "xxxx",
		},
		User: iam.User{
			ID:   "xxxx",
			Name: "xxxx",
			Domain: iam.UserDomain{
				ID:   "xxxx",
				Name: "xxxx",
			},
		},
	}, nil) //TODO  getConsoleTokenID faild err

	iamMiddleware(ctx)

	assert.Equal(t, "xxxx", UserIDFromContext(ctx))
	assert.Equal(t, "xxxx", UserNameFromContext(ctx))
	assert.Equal(t, "xxxx", AccountIDFromContext(ctx))
	assert.Equal(t, "xxxx", AccountNameFromContext(ctx))

}

func TestOrderCheckMiddleware(t *testing.T) {

	// case1
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	clis := &testingclientset.ClientSet{}

	iamClient := &testingiam.Client{}
	mock.OnAnything(clis, "InternalIam").Return(iamClient, nil)
	mock.OnAnything(clis.InternalIam(), "ValidatorRequest").Return(&iam.Token{
		Domain: iam.Domain{
			ID:   "xxxx",
			Name: "xxxx",
		},
		User: iam.User{
			ID:   "xxxx",
			Name: "xxxx",
			Domain: iam.UserDomain{
				ID:   "xxxx",
				Name: "xxxx",
			},
		},
	}, nil)
	iamMiddleware := IAMMiddleware(clis)
	iamMiddleware(ctx)

	orderCheckMiddleware := OrderCheckMiddleware(clis)
	orderCheckMiddleware(ctx)
	assert.Equal(t, 401, ctx.Writer.Status())

	// case2
	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	clis = &testingclientset.ClientSet{}
	iamClient = &testingiam.Client{}
	mock.OnAnything(clis, "InternalIam").Return(iamClient, nil)
	mock.OnAnything(clis.InternalIam(), "ValidatorRequest").Return(&iam.Token{
		Domain: iam.Domain{
			ID:   "xxxx",
			Name: "order_execute",
		},
		User: iam.User{
			ID:   "xxxx",
			Name: "xxxx",
			Domain: iam.UserDomain{
				ID:   "xxxx",
				Name: "order_execute",
			},
		},
	}, nil)
	iamMiddleware = IAMMiddleware(clis)
	iamMiddleware(ctx)

	orderCheckMiddleware = OrderCheckMiddleware(clis)

	req, _ := http.NewRequest("GET", "/", nil)
	q := req.URL.Query()
	q.Add("orderId", "xxx")
	req.URL.RawQuery = q.Encode()
	ctx.Request = req

	orderClient := &testbilling.OrderClient{}
	mock.OnAnything(clis, "OrderClient").Return(orderClient, nil)
	mock.OnAnything(clis.OrderClient(), "GetOrderDetail").Return(&billing.GetOrderDetail{AccountId: "xxxxx", UserId: "xxxxx"}, nil)

	orderCheckMiddleware(ctx)

	assert.Equal(t, "xxxxx", UserIDFromContext(ctx))
	assert.Equal(t, "xxxxx", AccountIDFromContext(ctx))
	assert.Equal(t, 200, ctx.Writer.Status())
}

func TestOrderExecuteMiddleware(t *testing.T) {
	// case1
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	clis := &testingclientset.ClientSet{}

	iamClient := &testingiam.Client{}
	mock.OnAnything(clis, "InternalIam").Return(iamClient, nil)
	mock.OnAnything(clis.InternalIam(), "ValidatorRequest").Return(&iam.Token{
		Domain: iam.Domain{
			ID:   "xxxx",
			Name: "xxxx",
		},
		User: iam.User{
			ID:   "xxxx",
			Name: "xxxx",
			Domain: iam.UserDomain{
				ID:   "xxxx",
				Name: "xxxx",
			},
		},
	}, nil)
	iamMiddleware := IAMMiddleware(clis)
	iamMiddleware(ctx)

	orderExecuteMiddleware := OrderExecuteMiddleware(clis)

	orderExecuteMiddleware(ctx)
	assert.Equal(t, 401, ctx.Writer.Status())

	// case2
	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	clis = &testingclientset.ClientSet{}
	iamClient = &testingiam.Client{}
	mock.OnAnything(clis, "InternalIam").Return(iamClient, nil)
	mock.OnAnything(clis.InternalIam(), "ValidatorRequest").Return(&iam.Token{
		Domain: iam.Domain{
			ID:   "xxxx",
			Name: "order_execute",
		},
		User: iam.User{
			ID:   "xxxx",
			Name: "xxxx",
			Domain: iam.UserDomain{
				ID:   "xxxx",
				Name: "order_execute",
			},
		},
	}, nil)
	iamMiddleware = IAMMiddleware(clis)
	iamMiddleware(ctx)

	orderExecuteMiddleware = OrderExecuteMiddleware(clis)

	eo := model.ExecuteOrderRequest{
		OrderId: "xxxx",
	}

	data, err := json.Marshal(eo)
	if err != nil {
		t.Errorf("json marshal failed: %s", err)
	}

	req, _ := http.NewRequest("POST", "/", bytes.NewReader(data))
	req.Header.Add("Content-Type", gin.MIMEJSON)
	ctx.Request = req

	orderClient := &testbilling.OrderClient{}
	mock.OnAnything(clis, "OrderClient").Return(orderClient, nil)
	mock.OnAnything(clis.OrderClient(), "GetOrderDetail").Return(&billing.GetOrderDetail{AccountId: "xxxxx", UserId: "xxxxx"}, nil)

	orderExecuteMiddleware(ctx)

	assert.Equal(t, "xxxxx", UserIDFromContext(ctx))
	assert.Equal(t, "xxxxx", AccountIDFromContext(ctx))
	assert.Equal(t, 200, ctx.Writer.Status())
}

func TestResourceMiddleware(t *testing.T) {
	// case1
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	clis := &testingclientset.ClientSet{}
	iamClient := &testingiam.Client{}
	mock.OnAnything(clis, "InternalIam").Return(iamClient, nil)
	mock.OnAnything(clis.InternalIam(), "ValidatorRequest").Return(&iam.Token{
		Domain: iam.Domain{
			ID:   "xxxx",
			Name: "xxxx",
		},
		User: iam.User{
			ID:   "xxxx",
			Name: "xxxx",
			Domain: iam.UserDomain{
				ID:   "xxxx",
				Name: "xxxx",
			},
		},
	}, nil)
	iamMiddleware := IAMMiddleware(clis)
	iamMiddleware(ctx)
	resourceMiddleware := ResourceMiddleware(clis)

	resourceMiddleware(ctx)
	assert.Equal(t, 401, ctx.Writer.Status())

	// case2
	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	clis = &testingclientset.ClientSet{}
	iamClient = &testingiam.Client{}
	mock.OnAnything(clis, "InternalIam").Return(iamClient, nil)
	mock.OnAnything(clis.InternalIam(), "ValidatorRequest").Return(&iam.Token{
		User: iam.User{
			ID:   "xxxx",
			Name: "xxxx",
			Domain: iam.UserDomain{
				ID:   "xxxx",
				Name: "billing",
			},
		},
	}, nil)
	iamMiddleware = IAMMiddleware(clis)
	iamMiddleware(ctx)

	resourceMiddleware = ResourceMiddleware(clis)
	resourceMiddleware(ctx)

	assert.Equal(t, 200, ctx.Writer.Status())
}

func TestOrderIDFromContext(t *testing.T) {
	c, _ := gin.CreateTestContext(newGinResponseWriter())
	c.Set("ORDER_DI_IDENTITY", "xxx")

	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "case1",
			args: args{
				ctx: c,
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, OrderIDFromContext(tt.args.ctx), "OrderIDFromContext(%v)", tt.args.ctx)
		})
	}
}

func Test_UserNameMiddleware(t *testing.T) {
	iamInterface := &testingiam.Client{}
	iamInterface.On("GetUser", mock.Anything, "test").Times(2).Return(&bcesdkiam.UserInfo{
		Name: "test",
	}, nil)
	iamInterface.On("GetUser", mock.Anything, "none").Return(nil, nil)
	iamInterface.On("GetUser", mock.Anything, "error").Return(nil, fmt.Errorf("error"))

	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	RealUserNameMiddleware(iamInterface)(ctx)

	var err error
	httpRespRecorder := httptest.NewRecorder()
	ctx, _ = gin.CreateTestContext(&GinResponseWriter{httpRespRecorder})
	ctx.Request, err = http.NewRequest(http.MethodGet, "http://xxx/test?userId=test", nil)
	ctx.Set(ACCOUNT_ID_IDENTITY, "account-id")
	ctx.Set(USER_DI_IDENTITY, "account-id")
	assert.NoError(t, err)
	RealUserNameMiddleware(iamInterface)(ctx)
	assert.Equal(t, http.StatusOK, httpRespRecorder.Code)
	assert.Equal(t, UserNameFromContext(ctx), "test")

	httpRespRecorder = httptest.NewRecorder()
	ctx, _ = gin.CreateTestContext(&GinResponseWriter{httpRespRecorder})
	ctx.Request, err = http.NewRequest(http.MethodGet, "http://xxx/test?userId=none", nil)
	ctx.Set(ACCOUNT_ID_IDENTITY, "account-id")
	ctx.Set(USER_DI_IDENTITY, "account-id")
	assert.NoError(t, err)
	RealUserNameMiddleware(iamInterface)(ctx)
	assert.Equal(t, http.StatusInternalServerError, httpRespRecorder.Code)

	httpRespRecorder = httptest.NewRecorder()
	ctx, _ = gin.CreateTestContext(&GinResponseWriter{httpRespRecorder})
	ctx.Request, err = http.NewRequest(http.MethodGet, "http://xxx/test?userId=error", nil)
	ctx.Set(ACCOUNT_ID_IDENTITY, "account-id")
	ctx.Set(USER_DI_IDENTITY, "account-id")
	assert.NoError(t, err)
	RealUserNameMiddleware(iamInterface)(ctx)
	assert.Equal(t, http.StatusInternalServerError, httpRespRecorder.Code)

	httpRespRecorder = httptest.NewRecorder()
	ctx, _ = gin.CreateTestContext(&GinResponseWriter{httpRespRecorder})
	ctx.Request, err = http.NewRequest(http.MethodGet, "http://xxx/test", nil)
	ctx.Set(ACCOUNT_ID_IDENTITY, "account-id")
	ctx.Set(USER_DI_IDENTITY, "account-id")
	assert.NoError(t, err)
	RealUserNameMiddleware(iamInterface)(ctx)
	assert.Equal(t, http.StatusOK, httpRespRecorder.Code)
	assert.Equal(t, "account-id", UserIDFromContext(ctx))
}
