package middleware

import "github.com/gin-gonic/gin"

type ResourceResolver interface {
	Resource(c *gin.Context) ([]string, error)
}

const (
	Read        = "READ"
	Operate     = "OPERATE"
	FullControl = "FULL_CONTROL"
)

var (
	DefaultResourceResolver ResourceResolver = &defaultResourceResolver{}
	InstanceResolver        ResourceResolver = &instanceResolver{}
	NamespaceResolver       ResourceResolver = &namespaceResolver{}
)

type defaultResourceResolver struct{}

func (r *defaultResourceResolver) Resource(c *gin.Context) ([]string, error) {
	return []string{"*"}, nil
}

type instanceResolver struct{}

func (ins *instanceResolver) Resource(c *gin.Context) ([]string, error) {
	return []string{"instance/" + c.Param("instanceId")}, nil
}

type namespaceResolver struct{}

// Resource 返回与给定参数相关的资源路径。
// c是当前请求的gin上下文。
// 返回值是一个字符串数组和一个错误对象。
// 数组中包含两个资源路径，分别为实例ID和实例ID/项目名称。
// 如果成功，则错误对象为nil。
func (*namespaceResolver) Resource(c *gin.Context) ([]string, error) {
	return []string{
		"instance/" + c.Param("instanceId"),
		"instance/" + c.Param("instanceId") + "/project/" + c.Param("projectName"),
	}, nil
}
