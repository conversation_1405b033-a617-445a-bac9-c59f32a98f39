package router

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
	gs "github.com/swaggo/gin-swagger"
	"github.com/swaggo/gin-swagger/swaggerFiles"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/ginprom"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/config"
	_ "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/docs"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler/accelerator"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler/chart"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler/domain"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler/immutable"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler/replication"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler/sync"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/handler/trigger"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
)

type ApiFunc func(c *gin.Context) *handler.Response

// NewGin 函数新建一个gin引擎，并返回该引擎指针
// 参数conf：*config.ServiceConfig，服务配置信息指针
// 参数clientset：clientset.ClientSetInterface，客户端集合接口
// 返回值*gin.Engine，gin引擎指针
func NewGin(conf *config.ServiceConfig, clientset clientset.ClientSetInterface) *gin.Engine {
	route := gin.Default()
	route.Use(gin_context.LoggerMiddleware())
	route.UseRawPath = true

	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		if err := v.RegisterValidation("password", model.PasswordValidate); err != nil {
			return nil
		}
	}

	route.GET("/healthz", func(c *gin.Context) {
		c.String(http.StatusOK, "ok")
	})

	route.GET("/metrics", ginprom.PromHanlder)
	if gin.Mode() == gin.DebugMode {
		route.GET("/swagger/*any", gs.WrapHandler(swaggerFiles.Handler))
	}

	instr := func(f ApiFunc) gin.HandlerFunc {
		return func(c *gin.Context) {
			response := f(c)
			response.ParseResponse()
		}
	}

	// resource 启停相关
	resourceHandler := billing.NewResource(clientset, conf.Region)
	route.PUT("/v1/billing/account/:accountId/:service/resources/:resourceId", middleware.IAMMiddleware(clientset), middleware.ResourceMiddleware(clientset), resourceHandler.Action)
	route.GET("/v1/billing/account/:accountId/:service/resources/:resourceId", middleware.IAMMiddleware(clientset), middleware.ResourceMiddleware(clientset), resourceHandler.Get)

	pc := middleware.NewPermissionCheck(clientset, conf.Region, conf.Env, common.IamServiceName)

	// 订单执行器
	orderHandler := billing.NewOrder(clientset, conf.Region)
	route.POST("/v1/order_executor/execute", middleware.IAMMiddleware(clientset), middleware.OrderExecuteMiddleware(clientset), pc.Verify(), orderHandler.Execute)
	route.GET("/v1/order_executor/check", middleware.IAMMiddleware(clientset), middleware.OrderCheckMiddleware(clientset), pc.Verify(), orderHandler.Check)

	// instance
	instanceHandler := handler.NewInstance(clientset, conf.Region, conf.Endpoints)
	route.GET("/v1/instances", ginprom.PromMiddleware(pc.GetOperation), middleware.IAMMiddleware(clientset), instanceHandler.List)

	route.POST("/v1/tag/resources", ginprom.PromMiddleware(pc.GetOperation), middleware.IAMMiddleware(clientset), instanceHandler.ListForLogicTag)
	route.GET("/v1/iam/resources", ginprom.PromMiddleware(pc.GetOperation), middleware.IAMMiddleware(clientset), instanceHandler.ListForLogicIam)

	registryTypeHandler := handler.NewRegistryTypeHandler()
	route.GET("/v1/registrytypes", ginprom.PromMiddleware(pc.GetOperation), middleware.IAMMiddleware(clientset), instr(registryTypeHandler.ListRegistryTypes)) // 列举所有远程仓库类型

	// user
	userHandler := handler.NewUserHandler(clientset)
	route.GET("/v1/users/profile", ginprom.PromMiddleware(pc.GetOperation), middleware.IAMMiddleware(clientset), middleware.RealUserNameMiddleware(clientset.InternalIam()), userHandler.GetUserProfile)
	route.GET("/v1/users/cert", ginprom.PromMiddleware(pc.GetOperation), middleware.IAMMiddleware(clientset), middleware.RealUserNameMiddleware(clientset.InternalIam()), userHandler.ListUserCert)

	policyFilterHandler := accelerator.NewPolicyFilterHandler()
	route.POST("/v1/accelerators/policies/filters", ginprom.PromMiddleware(pc.GetOperation), middleware.IAMMiddleware(clientset), policyFilterHandler.TestPolicyFilter) // 测试加速器策略规则

	// 为prometheus打上额外的label
	v1 := route.Group("/v1", ginprom.PromMiddleware(pc.GetOperation), middleware.IAMMiddleware(clientset), pc.Verify())
	{

		instanceGroup := v1.Group("instances")
		{
			instanceGroup.POST("", instanceHandler.Create)
			instanceGroup.POST("/renew", instanceHandler.Renew) // 续费

			instanceIdGroup := instanceGroup.Group("/:instanceId", middleware.InstanceMiddleware(clientset),
				middleware.HarborClientMiddleware(clientset))
			{
				instanceIdGroup.GET("", middleware.InstanceMiddleware(clientset), instanceHandler.Get)
				instanceIdGroup.PUT("", middleware.InstanceMiddleware(clientset), instanceHandler.Update)
				instanceIdGroup.PUT("/tags", middleware.InstanceMiddleware(clientset), instanceHandler.AssignTag)
				instanceIdGroup.DELETE("", middleware.InstanceMiddleware(clientset), instanceHandler.Delete)
				instanceIdGroup.PUT("/upgrade", middleware.InstanceMiddleware(clientset), instanceHandler.Upgrade)
				instanceIdGroup.PUT("/resgroups", middleware.InstanceMiddleware(clientset), instanceHandler.BindResGroup)

				// repository
				repositoryHandler := handler.NewRepositoryHandler(clientset)
				instanceIdGroup.GET("/repositories", instr(repositoryHandler.ListAllRepository))

				// network
				networkGroup := instanceIdGroup.Group("", middleware.NetworkItemMiddleware(clientset))
				{
					networkHandler := handler.NewNetworkHandler(clientset)
					networkGroup.GET("/privatelinks", networkHandler.ListPrivateNetwork)
					networkGroup.POST("/privatelinks", networkHandler.CreatePrivateNetwork)
					networkGroup.DELETE("/privatelinks", networkHandler.DeletePrivateLink)
					networkGroup.GET("/publiclinks", networkHandler.GetPublicLink)
					networkGroup.PUT("/publiclinks", networkHandler.UpdatePublicLink)
					networkGroup.DELETE("/publiclinks/whitelist", networkHandler.DeleteWhitelist)
					networkGroup.POST("/publiclinks/whitelist", networkHandler.AddWhiteList)
				}

				// credential
				credentialGroup := instanceIdGroup.Group("/credential", middleware.RealUserNameMiddleware(clientset.InternalIam()))
				{
					credentialHandler := handler.NewCredentialHandler(clientset)
					credentialGroup.PUT("", credentialHandler.RestPassword)
					credentialGroup.POST("", credentialHandler.CreateTemporaryToken)
				}

				// 远程仓库
				registryGroup := instanceIdGroup.Group("/registries")
				{
					registryHandler := handler.NewRegistryHandler(conf.RegistryAllowlist)
					registryGroup.POST("", instr(registryHandler.CreateRegistry))               // 创建远程仓库
					registryGroup.GET("/:registryId", instr(registryHandler.GetRegistry))       // 查询远程仓库详情
					registryGroup.GET("", instr(registryHandler.ListRegistries))                // 查询远程仓库列表
					registryGroup.POST("/ping", instr(registryHandler.CheckHealth))             // 检查健康
					registryGroup.PUT("/:registryId", instr(registryHandler.UpdateRegistry))    // 更新远程仓库
					registryGroup.DELETE("/:registryId", instr(registryHandler.DeleteRegistry)) // 删除远程仓库
				}

				// 镜像迁移或者实例同步共用
				executionGroup := instanceIdGroup.Group("/executions")
				{
					executionHandler := handler.NewExecutionHandler()
					executionGroup.GET("", instr(executionHandler.ListExecutions))                            // 查询镜像迁移或者实例同步执行记录列表
					executionGroup.POST("", instr(executionHandler.StartExecution))                           // 执行镜像迁移或者实例同步
					executionGroup.PUT("/:executionId", instr(executionHandler.StopExecution))                // 停止镜像迁移或者实例同步
					executionGroup.GET("/:executionId", instr(executionHandler.GetExecution))                 // 查询镜像迁移或者实例同步执行记录
					executionGroup.GET("/:executionId/tasks", instr(executionHandler.ListTasks))              // 查询镜像迁移或者实例同步执行任务记录
					executionGroup.GET("/:executionId/tasks/:taskId/log", instr(executionHandler.GetTaskLog)) // 查询镜像迁移或者实例同步执行任务日志
				}

				// 镜像迁移
				replicationGroup := instanceIdGroup.Group("/replications")
				{
					policyHandler := replication.NewPolicyHandler()
					replicationGroup.POST("", instr(policyHandler.CreatePolicy))             // 创建镜像迁移策略
					replicationGroup.GET("", instr(policyHandler.ListPolicies))              // 查询镜像迁移策略列表
					replicationGroup.GET("/:policyId", instr(policyHandler.GetPolicy))       // 查询镜像迁移策略
					replicationGroup.DELETE("/:policyId", instr(policyHandler.DeletePolicy)) // 删除镜像迁移策略
					replicationGroup.PUT("/:policyId", instr(policyHandler.UpdatePolicy))    // 更新镜像迁移策略
				}

				// 镜像迁移或者实例同步共用
				syncGroup := instanceIdGroup.Group("/syncs")
				{
					instanceSync := sync.NewPolicyHandler(clientset, conf.Region, conf.Endpoints)
					syncGroup.POST("", middleware.RealUserNameMiddleware(clientset.InternalIam()), instr(instanceSync.CreatePolicy))          // 创建实例同步策略
					syncGroup.GET("", instr(instanceSync.ListPolicies))                                                                       // 获取实例同步策略列表
					syncGroup.GET("/:policyId", instr(instanceSync.GetPolicy))                                                                // 查询实例同步策略
					syncGroup.DELETE("/:policyId", instr(instanceSync.DeletePolicy))                                                          // 删除实例同步策略
					syncGroup.PUT("/:policyId", middleware.RealUserNameMiddleware(clientset.InternalIam()), instr(instanceSync.UpdatePolicy)) // 更新实例同步策略
				}

				// gc
				gcGroup := instanceIdGroup.Group("/gcs")
				{
					gcHandler := handler.NewGcHandler()
					gcGroup.POST("", gcHandler.CreateGCSchedule)  // 执行垃圾回收调度任务
					gcGroup.GET("", gcHandler.ListGCHistory)      // 查询垃圾回收调度任务历史记录
					gcGroup.GET("/:gcId/log", gcHandler.GetGCLog) // 查询垃圾回收调度任务日志
				}

				// 触发器
				triggerGourp := instanceIdGroup.Group("/triggers")
				{
					policyHandle := trigger.NewPolicyHandler(clientset)
					triggerGourp.POST("/policies", policyHandle.CreatePolicy)
					triggerGourp.GET("/policies", policyHandle.ListPolicies)
					triggerGourp.GET("/policies/:policyId", policyHandle.GetPolicy)
					triggerGourp.PUT("/policies/:policyId", policyHandle.UpdatePolicy)
					triggerGourp.DELETE("/policies/:policyId", policyHandle.DeletePolicy)
					triggerGourp.DELETE("/policies", policyHandle.BatchDeletePolicy)
					triggerGourp.POST("/policies/targets", policyHandle.TestPolicyTarget)
					triggerGourp.PUT("/policies/:policyId/enable", policyHandle.EnablePolicy)

					jobHandler := trigger.NewJobHandler(clientset)
					triggerGourp.GET("/policies/:policyId/jobs", jobHandler.ListJobs)
					triggerGourp.PUT("/policies/:policyId/jobs/:jobId/retry", jobHandler.RetryExecuteJob)
				}

				acceleratorGourp := instanceIdGroup.Group("/accelerators")
				{
					policyHandle := accelerator.NewPolicyHandler(clientset)
					acceleratorGourp.POST("/policies", policyHandle.CreatePolicy)
					acceleratorGourp.GET("/policies", policyHandle.ListPolicies)
					acceleratorGourp.GET("/policies/:policyId", policyHandle.GetPolicy)
					acceleratorGourp.PUT("/policies/:policyId", policyHandle.UpdatePolicy)
					acceleratorGourp.DELETE("/policies/:policyId", policyHandle.DeletePolicy)
					acceleratorGourp.DELETE("/policies", policyHandle.BatchDeletePolicy)
					acceleratorGourp.PUT("/policies/:policyId/enable", policyHandle.EnablePolicy)
				}

				// 版本不可变
				immutableGroup := instanceIdGroup.Group("/immutable")
				{
					immutableHandler := immutable.NewImmutableHandler(clientset)
					immutableGroup.POST("", instr(immutableHandler.CreateImmutableRule))
					immutableGroup.GET("", instr(immutableHandler.ListImmutableRule))
					immutableGroup.GET("/:immutableId", instr(immutableHandler.GetImmutableRule))
					immutableGroup.PUT("/:immutableId", instr(immutableHandler.UpdateImmutableRule))
					immutableGroup.DELETE("/:immutableId", instr(immutableHandler.DeleteImmutableRule))
					immutableGroup.DELETE("", instr(immutableHandler.BatchDeleteImmutableRule))
					immutableGroup.PUT("/:immutableId/enable", instr(immutableHandler.EnableImmutableRule))
					immutableGroup.GET("/project", instr(immutableHandler.GetImmutableProjectList))
				}

				//自定义域名
				domainGroup := instanceIdGroup.Group("/domain", middleware.NetworkItemMiddleware(clientset))
				{
					domainHandler := domain.NewDomainHandler(clientset)
					domainGroup.POST("", domainHandler.CreateDomain)
					domainGroup.GET("", domainHandler.ListDomain)
					domainGroup.GET("/:domainName", domainHandler.GetDomain)
					domainGroup.GET("/:domainName/check", domainHandler.CheckDomainICP)
					domainGroup.PUT("/:domainName", domainHandler.UpdateDomain)
					domainGroup.DELETE("/:domainName", domainHandler.DeleteDomain)
				}

				// 命名空间
				projectGroup := instanceIdGroup.Group("/projects")
				{
					projectHandler := handler.NewProjectHandler(clientset)
					projectGroup.POST("", instr(projectHandler.CreateProject))        // 创建命名空间
					projectGroup.DELETE("", instr(projectHandler.BatchDeleteProject)) // 批量删除命名空间
					projectGroup.GET("", instr(projectHandler.ListProject))           // 获取当前用户的命名空间列表

					projectNameGroup := projectGroup.Group("/:projectName", middleware.ProjectMiddleware())
					{
						projectNameGroup.PUT("", instr(projectHandler.UpdateProject))    // 更新命名空间
						projectNameGroup.GET("", instr(projectHandler.GetProject))       // 通过通过项目名称获取项目详情
						projectNameGroup.DELETE("", instr(projectHandler.DeleteProject)) // 删除命名空间

						chartGroup := projectNameGroup.Group("/charts")
						{
							downloadHandler := chart.NewDownloadHandler(conf.CCRDomainEndpoint, clientset.Lister())
							chartGroup.GET("/download/:filename", downloadHandler.DownloadChart) // 下载 helm chart 文件

							chartHandler := chart.NewChartHandler(clientset)
							chartGroup.POST("", instr(chartHandler.PostCharts))                                             // 上传chart
							chartGroup.GET("", instr(chartHandler.ListCharts))                                              // 查询chart列表
							chartGroup.DELETE("", instr(chartHandler.BatchDeleteChart))                                     // 批量删除chart
							chartGroup.DELETE("/:chartName", instr(chartHandler.DeleteChart))                               // 删除chart
							chartGroup.GET("/:chartName/versions", instr(chartHandler.ListChartVersions))                   // 查询chart version列表
							chartGroup.DELETE("/:chartName/versions", instr(chartHandler.BatchDeleteChartVersion))          // 批量删除chart版本
							chartGroup.DELETE("/:chartName/versions/:chartVersion", instr(chartHandler.DeleteChartVersion)) // 删除chart版本
						}

						repositoryGroup := projectNameGroup.Group("/repositories", gin_context.RepositoryNameResolveMiddleware())
						{
							repositoryGroup.GET("", instr(repositoryHandler.ListRepository))                      // 查询镜像仓库列表
							repositoryGroup.DELETE("", instr(repositoryHandler.BatchDeleteRepository))            // 批量删除镜像仓库
							repositoryGroup.DELETE("/:repositoryName", instr(repositoryHandler.DeleteRepository)) // 删除镜像仓库
							repositoryGroup.PUT("/:repositoryName", instr(repositoryHandler.UpdateRepository))    // 更新镜像仓库
							repositoryGroup.GET("/:repositoryName", instr(repositoryHandler.GetRepository))       // 查询镜像仓库

							imageBuildGroup := repositoryGroup.Group("/:repositoryName/imagebuilds", middleware.ImageBuildIDCheck(clientset.SqlClient()))
							{
								imageBuildGroup.POST("", instr(repositoryHandler.BuildRepository))                       // 创建镜像构建任务
								imageBuildGroup.GET("", instr(repositoryHandler.ListBuildRepositoryTask))                // 获取镜像构建任务
								imageBuildGroup.GET("/:buildId", instr(repositoryHandler.GetBuildRepositoryTask))        // 获取镜像构建任务详情
								imageBuildGroup.GET("/:buildId/log", instr(repositoryHandler.GetBuildRepositoryTaskLog)) // 获取镜像构建任务日志
								imageBuildGroup.DELETE("", instr(repositoryHandler.BatchDeleteBuildRepositoryTask))      // 批量删除镜像构建任务
								imageBuildGroup.DELETE("/:buildId", instr(repositoryHandler.DeleteBuildRepositoryTask))  // 删除镜像构建任务
							}

							tagGroup := repositoryGroup.Group("/:repositoryName/tags")
							{
								tagHandler := handler.NewTagHandler()
								tagGroup.GET("", instr(tagHandler.ListTag))                                // 查询tag列表
								tagGroup.GET("/:tagName", instr(tagHandler.GetTag))                        // 查询tag详情
								tagGroup.DELETE("", instr(tagHandler.BatchDeleteTag))                      // 批量删除tag
								tagGroup.DELETE("/:tagName", instr(tagHandler.DeleteTag))                  // 删除tag
								tagGroup.POST("/:tagName/scan", instr(tagHandler.TagScan))                 // 扫描漏洞
								tagGroup.GET("/:tagName/scan/:reportId/log", instr(tagHandler.TagScanLog)) // 扫描漏洞日志
								tagGroup.GET("/:tagName/scanoverview", instr(tagHandler.ScanOverview))     // 查询tag漏洞
								tagGroup.GET("/:tagName/buildhistory", instr(tagHandler.BuildHistory))     // 查询tag构建历史
							}
						}
					}
				}
			}
		}

	}

	return route
}
