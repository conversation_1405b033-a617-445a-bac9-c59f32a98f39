package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/baidubce/bce-sdk-go/services/cce"
	"github.com/baidubce/bce-sdk-go/util/log"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"k8s.io/apimachinery/pkg/util/uuid"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bus"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	_ "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/log"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/router"
)

var (
	serviceConfigFile string // ccr-service 配置文件
	logFile           string // 日志打印文件
	runMode           string // gin 模式: debug release test
	version           string // 版本
)

var cmd = &cobra.Command{
	Use:     "ccr-service",
	Short:   "CCR enterprise ccr service.",
	Long:    `CCR enterprise directly communicate with the front end and call the business interface of area a according to the requirement.`,
	Example: "go run main.go api --config=config.yaml",
	Run:     run,
	Version: version,
}

func init() {
	flags := cmd.Flags()

	flags.StringVar(&serviceConfigFile, "config", "", "ccr-service service 配置文件.")
	flags.StringVar(&logFile, "log-file", "", "ccr-service 日志路径, 不设置则打印到 STDOUT.")
	flags.StringVar(&runMode, "run-mode", "debug", "run 模式: debug release test.")
}

// @title CCR Service API
// @version 0.0.1
// @description CCR Service 提供 RESTFUL 风格 API, 对接 Console 及 OpenAPI 请求

// @contact.name duzhanwei,wenmanxiang
// @contact.email <EMAIL>

// @host ccr.baidubce.com
// @BasePath /v1
func main() {
	if err := cmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "error: %v\n", err)
		os.Exit(1)
	}
}

func run(*cobra.Command, []string) {

	signalChan := make(chan os.Signal, 0)
	signal.Notify(signalChan, syscall.SIGTERM, syscall.SIGINT)

	stopChan := make(chan struct{})

	go func() {
		<-signalChan
		close(stopChan)
	}()

	if serviceConfigFile == "" {
		panic("config file is empty")
	}

	conf, err := config.NewConfig(serviceConfigFile)
	if err != nil {
		panic(fmt.Sprintf("read config file failed: %s", err))
	}

	var clients clientset.ClientSetInterface
	if conf.Env != common.EnvSANDBOX {
		cceCli, err := cce.NewClient(conf.AccessKey, conf.SecretKey, conf.CCEEndpoint)
		if err != nil {
			panic(fmt.Sprintf("new cce client failed: %s", err))
		}

		kubeResult, err := cceCli.GetKubeConfig(&cce.GetKubeConfigArgs{
			ClusterUuid: conf.ClusterID,
			Type:        cce.KubeConfigTypeInternal,
		})
		if err != nil {
			panic(fmt.Sprintf("get kube config failed: %s", err))
		}

		k8sCli, err := utils.NewK8sClient(kubeResult.Data, scheme)
		if err != nil {
			panic(fmt.Sprintf("new k8s clinet failed: %s", err))
		}

		k8sClientset, err := utils.NewK8sClientSetFromKubeconfig(kubeResult.Data)
		if err != nil {
			panic(fmt.Errorf("new k8s legacy client failed: %s", err))
		}

		cachedClient, err := utils.NewK8sClientWithCache(context.Background(), kubeResult.Data, scheme, "", 30*time.Minute)
		if err != nil {
			panic(fmt.Errorf("new cached k8s client failed: %s", err))
		}

		clients, err = clientset.NewClientSet(*conf, cceCli, k8sCli, k8sClientset, listers.NewLister(cachedClient))
		if err != nil {
			panic(fmt.Sprintf("create client set failed: %s", err))
		}
	} else {
		clients, err = clientset.NewClientSet(*conf, nil, nil, nil, nil)
		if err != nil {
			panic(fmt.Sprintf("create client set failed: %s", err))
		}
	}
	// registry bus service
	if conf.Env != common.EnvSANDBOX && conf.Region != common.RegionGZTEST {
		if err := registerBusService(clients); err != nil {
			logrus.Warnf("register bus service failed: %s, skipped!", err)
		}
	}

	NewServer(conf, clients).Start(stopChan)
}

func registerBusService(clients clientset.ClientSetInterface) error {
	if err := registerBillingOrderExecutor(clients); err != nil {
		logrus.Warnf("register order executor service failed: %s ", err)
		return err
	}
	logrus.Infof("register billing order executor success")
	if err := registerLogicTag(clients); err != nil {
		return err
	}
	logrus.Infof("register billing logic tag success")

	return nil
}

// registerLogicTag 函数用于向注册中心注册逻辑标签
// 参数：
//
//	clients: clientset.ClientSetInterface 类型变量，客户端集合接口对象
//
// 返回值：
//
//	error: 错误信息
func registerLogicTag(clients clientset.ClientSetInterface) error {
	reqId := string(uuid.NewUUID())

	// gz灰度插件地址，注册中心注册tag服务地址，需要注册真实地址
	ccrServiceEndpoint := clients.Conf().CCRServiceEndpoint
	if clients.Conf().Region == common.RegionGZ {
		ccrServiceEndpoint = "10.169.25.216:8500"
	}

	rs := &bus.RegisterServiceRequest{
		Endpoint:  ccrServiceEndpoint,
		Region:    clients.Conf().Region,
		Type:      "tag",
		ServiceID: "CCR",
		Configuration: bus.RegisterConfig{
			TagPath: "/v1/tag/resources", // TODO 替换为配置文件和标签的路径
		},
	}
	if err := clients.BusClient().RegisterService(reqId, rs); err != nil {
		return err
	}
	return nil
}

func registerBillingOrderExecutor(clients clientset.ClientSetInterface) error {
	reqId := string(uuid.NewUUID())
	rs := &bus.RegisterServiceRequest{
		Endpoint:  clients.Conf().CCRServiceEndpoint,
		Region:    clients.Conf().Region,
		Type:      "console",
		ServiceID: "CCR",
	}
	if err := clients.BusClient().RegisterService(reqId, rs); err != nil {
		return err
	}
	return nil
}

type Server struct {
	httpSrv *http.Server

	conf      *config.ServiceConfig
	clientset clientset.ClientSetInterface
}

func NewServer(conf *config.ServiceConfig, clientset clientset.ClientSetInterface) *Server {

	return &Server{
		conf:      conf,
		clientset: clientset,
	}
}

func (server *Server) Start(stopChan <-chan struct{}) {
	// set gin mode
	gin.SetMode(runMode)
	if runMode == "debug" {
		log.SetLogHandler(log.STDERR)
		log.SetLogLevel(log.DEBUG)
		logrus.SetLevel(logrus.DebugLevel)
	}
	logrus.SetFormatter(&logrus.TextFormatter{DisableColors: true})

	r := router.NewGin(server.conf, server.clientset)
	server.httpSrv = &http.Server{
		Addr:           server.conf.ListenAddress,
		Handler:        r,
		ReadTimeout:    10 * time.Second,
		WriteTimeout:   10 * time.Second,
		MaxHeaderBytes: 1 << 20,
	}

	go func() {
		<-stopChan
		timeoutCtx, cancelCtx := context.WithTimeout(context.TODO(), 10*time.Second)
		defer cancelCtx()
		server.httpSrv.Shutdown(timeoutCtx)
	}()

	if err := server.httpSrv.ListenAndServe(); err != nil {
		fmt.Println("server was shutdown gracefully")
		panic(err)
	}
}
