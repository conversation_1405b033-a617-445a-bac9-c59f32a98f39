package clientset

import (
	"reflect"
	"testing"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/cce"
	"github.com/baidubce/bce-sdk-go/services/sts/api"
	"github.com/goharbor/harbor/src/testing/mock"
	sdkiam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	lister "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/config"
	testingsts "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/sts"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func TestClientSet_VpcClientForAccount(t *testing.T) {

	stsClient := &testingsts.Client{}

	type fields struct {
		stsClient      sts.ClientInterface
		bosClient      *bos.Client
		sqlClient      *models.Client
		cceClient      *cce.Client
		iamClient      iam.ClientInterface
		userSetting    usersetting.ClientInterface
		k8sClient      client.Client
		harborClient   *harbor.Client
		conf           config.ServiceConfig
		lister         listers.ListerInterface
		orderClient    billing.OrderClientInterface
		resourceClient billing.ResourceClientInterface
	}
	type args struct {
		accountId string
		userId    string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    vpc.ClientInterface
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				stsClient: stsClient,
			},
			args: args{
				accountId: "xxx",
				userId:    "aaa",
			},
			want:    &vpc.Client{},
			wantErr: false,
		},
	}
	for _, tt := range tests {

		cred := &sts.Credential{
			Credential: &api.Credential{
				AccessKeyId:     "aaa",
				SecretAccessKey: "xxx",
				SessionToken:    "sss",
			},
			Token: &sdkiam.Token{},
		}
		mock.OnAnything(tt.fields.stsClient, "AssumeRole").Return(cred, nil)
		t.Run(tt.name, func(t *testing.T) {
			c := &ClientSet{
				stsClient:      tt.fields.stsClient,
				bosClient:      tt.fields.bosClient,
				sqlClient:      tt.fields.sqlClient,
				cceClient:      tt.fields.cceClient,
				iamClient:      tt.fields.iamClient,
				userSetting:    tt.fields.userSetting,
				k8sClient:      tt.fields.k8sClient,
				harborClient:   tt.fields.harborClient,
				conf:           tt.fields.conf,
				lister:         tt.fields.lister,
				orderClient:    tt.fields.orderClient,
				resourceClient: tt.fields.resourceClient,
			}
			got, err := c.VpcClientForAccount(tt.args.accountId, tt.args.userId)
			if (err != nil) != tt.wantErr {
				t.Errorf("VpcClientForAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, got) {
				t.Errorf("VpcClientForAccount() got = %v, want %v", got, got)
			}
		})
	}
}

func TestNewClientSet(t *testing.T) {
	type args struct {
		conf            config.ServiceConfig
		cceCli          *cce.Client
		k8sCli          client.Client
		k8sLegacyClient kubernetes.Interface
		lister          lister.ListerInterface
	}
	tests := []struct {
		name    string
		args    args
		want    ClientSetInterface
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				conf: config.ServiceConfig{
					STSEndpoint:     "test.com",
					IAMEndpoint:     "test.com",
					ServiceName:     "test",
					ServicePassword: "test",
					RoleName:        "test",
					MysqlConnection: "test.com",
				},
				cceCli:          nil,
				k8sCli:          nil,
				k8sLegacyClient: nil,
				lister:          nil,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewClientSet(tt.args.conf, tt.args.cceCli, tt.args.k8sCli, tt.args.k8sLegacyClient, tt.args.lister)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewClientSet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestClientSet_LegacyKubernetesClientset(t *testing.T) {
	type fields struct {
		stsClient       sts.ClientInterface
		bosClient       *bos.Client
		sqlClient       *models.Client
		cceClient       *cce.Client
		iamClient       iam.ClientInterface
		userSetting     usersetting.ClientInterface
		k8sClient       client.Client
		k8sLegacyClient kubernetes.Interface
		harborClient    *harbor.Client
		conf            config.ServiceConfig
		lister          lister.ListerInterface
		orderClient     billing.OrderClientInterface
		resourceClient  billing.ResourceClientInterface
	}
	tests := []struct {
		name   string
		fields fields
		want   kubernetes.Interface
	}{
		// TODO: Add test cases.
		{
			name:   "normal",
			fields: fields{},
			want:   (kubernetes.Interface)(nil),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ClientSet{
				stsClient:       tt.fields.stsClient,
				bosClient:       tt.fields.bosClient,
				sqlClient:       tt.fields.sqlClient,
				cceClient:       tt.fields.cceClient,
				iamClient:       tt.fields.iamClient,
				userSetting:     tt.fields.userSetting,
				k8sClient:       tt.fields.k8sClient,
				k8sLegacyClient: tt.fields.k8sLegacyClient,
				harborClient:    tt.fields.harborClient,
				conf:            tt.fields.conf,
				lister:          tt.fields.lister,
				orderClient:     tt.fields.orderClient,
				resourceClient:  tt.fields.resourceClient,
			}
			if got := c.LegacyKubernetesClientset(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ClientSet.LegacyKubernetesClientset() = %v, want %v", got, tt.want)
			}
		})
	}
}
