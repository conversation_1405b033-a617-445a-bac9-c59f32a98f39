package clientset

import (
	"context"
	"fmt"

	"github.com/baidubce/bce-sdk-go/auth"
	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/cce"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"

	sdkiam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcd"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bus"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/ccr/personal"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/certificate"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/resourcegroup"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	lister "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/config"
)

type ClientSetInterface interface {
	SqlClient() models.Interface
	CceClient() *cce.Client
	K8sClient() client.Client
	InternalIam() iam.ClientInterface
	Usersetting() usersetting.ClientInterface
	Conf() config.ServiceConfig
	StsClient() sts.ClientInterface
	HarborClient(instanceID string) (*harbor.HarborClient, error)
	HarborClientSimulate(instanceID string, sid string) (*harbor.HarborClient, error)
	Lister() lister.ListerInterface
	OrderClient() billing.OrderClientInterface
	ResourceClient() billing.ResourceClientInterface
	BusClient() bus.Client
	LogicTagClient() logictag.Client
	ResourceGroupClient() resourcegroup.Client
	VpcClientForAccount(accountId, userId string) (vpc.ClientInterface, error)
	GetBceCredential(accountId, userId string) (*auth.BceCredentials, error)
	OrderClientForAccount(accountId, userId string) (billing.OrderClientInterface, error)
	FacadeClientForAccount(accountId, userId string) (billing.FacadeClientInterface, error)
	ResourceClientForAccount(accountId, userId string) (billing.ResourceClientInterface, error)
	AutoRuleClientForAccount(accountId, userId string) (billing.AutoRenewClientInterface, error)
	CCRClientForAccount(accountId, userId string, endpoint string) (ccr.ClientInterface, error)
	PersonalClientForAccount(accountId, userId string) (personal.Interface, error)
	CCRClientForAkSkSessionToken(ak, sk, sessionToken string, endpoint string) (ccr.ClientInterface, error)

	LegacyKubernetesClientset() kubernetes.Interface

	LogicTagClientForAccount(accountId, userId string) (logictag.Client, error)
    ResourceGroupClientForAccount(accountId, userId string) (resourcegroup.Client, error)

	CertClientForAccount(accountId, userId string) (certificate.Interface, error)
	BcdClientForAccount(accountId, userId string) (bcd.Interface, error)
}

type ClientSet struct {
	stsClient   sts.ClientInterface
	bosClient   *bos.Client
	sqlClient   *models.Client
	cceClient   *cce.Client
	iamClient   iam.ClientInterface
	userSetting usersetting.ClientInterface

	k8sClient client.Client

	k8sLegacyClient kubernetes.Interface

	harborClient *harbor.Client

	conf   config.ServiceConfig
	lister lister.ListerInterface

	orderClient    billing.OrderClientInterface
	resourceClient billing.ResourceClientInterface

	busClient      bus.Client
	logicTagClient logictag.Client
	resGroupClient resourcegroup.Client
}

func NewClientSet(conf config.ServiceConfig, cceCli *cce.Client, k8sCli client.Client,
	k8sLegacyClient kubernetes.Interface, lister lister.ListerInterface) (ClientSetInterface, error) {
	stsCli, err := sts.NewClient(conf.STSEndpoint, conf.IAMEndpoint, conf.ServiceName, conf.ServicePassword, conf.RoleName)
	if err != nil {
		return nil, err
	}

	sqlCli, err := models.NewClient(context.Background(), conf.MysqlConnection)
	if err != nil {
		return nil, err
	}

	userSettingCli, err := usersetting.NewClient(conf.AccessKey, conf.SecretKey, conf.UserSettingEndpoint)
	if err != nil {
		return nil, err
	}

	iamCli := iam.NewClient(sdkiam.NewBceClient(&sdkiam.BceClientConfiguration{
		Endpoint: conf.IAMEndpoint,
		UserName: conf.ServiceName,
		Password: conf.ServicePassword,
		Retry:    sdkiam.DEFAULT_RETRY_POLICY,
		Domain:   "Default",
		Version:  "/v3",
	}))

	resourceClient, err := billing.NewResourceClient(conf.Billing.AccessKey, conf.Billing.SecretKey, conf.ResourceManageEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create resource client failed: %w", err)
	}

	orderClient, err := billing.NewOrderClient(conf.Billing.AccessKey, conf.Billing.SecretKey, conf.OrderManagerEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create order client failed: %w", err)
	}

	harborCli := harbor.NewClient(conf.CCRDomainEndpoint)

	busClient, err := bus.NewClient(conf.Billing.AccessKey, conf.Billing.SecretKey, conf.BusEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create bus client failed: %w", err)
	}

	logicTagClient, err := logictag.NewClient(conf.AccessKey, conf.SecretKey, conf.LogicTagEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create logic tag client failed: %w", err)
	}

	resGroupClient, err := resourcegroup.NewClient(conf.AccessKey, conf.SecretKey, conf.ResGroupEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create resourcegroup client failed: %w", err)
	}

	return &ClientSet{
		stsClient:      stsCli,
		sqlClient:      sqlCli,
		cceClient:      cceCli,
		k8sClient:      k8sCli,
		iamClient:      iamCli,
		userSetting:    userSettingCli,
		harborClient:   harborCli,
		lister:         lister,
		conf:           conf,
		resourceClient: resourceClient,
		orderClient:    orderClient,

		k8sLegacyClient: k8sLegacyClient,

		busClient:      busClient,
		logicTagClient: logicTagClient,
		resGroupClient: resGroupClient,
	}, nil
}

func (c *ClientSet) SqlClient() models.Interface {
	return c.sqlClient
}

func (c *ClientSet) CceClient() *cce.Client {
	return c.cceClient
}

func (c *ClientSet) K8sClient() client.Client {
	return c.k8sClient
}

func (c *ClientSet) InternalIam() iam.ClientInterface {
	return c.iamClient
}

func (c *ClientSet) Usersetting() usersetting.ClientInterface {
	return c.userSetting
}

func (c *ClientSet) Conf() config.ServiceConfig {
	return c.conf
}

func (c *ClientSet) StsClient() sts.ClientInterface {
	return c.stsClient
}

func (c *ClientSet) HarborClient(instanceID string) (*harbor.HarborClient, error) {
	insInfo, err := c.lister.GetInstanceInfo(instanceID)
	if err != nil {
		return nil, fmt.Errorf("get instance info failed: %w", err)
	}
	harborHost := insInfo.ClusterURL
	harborPasswd := insInfo.Password

	return c.harborClient.HarborClientFor(harborHost, "admin", harborPasswd)
}

func (c *ClientSet) HarborClientSimulate(instanceID string, sid string) (*harbor.HarborClient, error) {
	insInfo, err := c.lister.GetInstanceInfo(instanceID)
	if err != nil {
		return nil, fmt.Errorf("get instance info failed: %w", err)
	}
	harborHost := insInfo.ClusterURL
	harborCsrf := insInfo.HarborCsrf

	return c.harborClient.HarborClientForSimulate(harborHost, sid, harborCsrf)
}

func (c *ClientSet) Lister() lister.ListerInterface {
	return c.lister
}

func (c *ClientSet) OrderClient() billing.OrderClientInterface {
	return c.orderClient
}

func (c *ClientSet) BusClient() bus.Client {
	return c.busClient
}

func (c *ClientSet) LogicTagClient() logictag.Client {
	return c.logicTagClient
}

func (c *ClientSet) ResourceClient() billing.ResourceClientInterface {
	return c.resourceClient
}

func (c *ClientSet) ResourceGroupClient() resourcegroup.Client { return c.resGroupClient }

func (c *ClientSet) VpcClientForAccount(accountId, userId string) (vpc.ClientInterface, error) {
	stsCred, err := c.StsClient().AssumeRole(accountId, userId)
	if err != nil {
		return nil, err
	}

	vpcCli, err := vpc.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, c.conf.VPCEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create vpc client failed: %w", err)
	}
	vpcCli.Client.Config.Credentials = &auth.BceCredentials{
		AccessKeyId:     stsCred.AccessKeyId,
		SecretAccessKey: stsCred.SecretAccessKey,
		SessionToken:    stsCred.SessionToken,
	}

	return vpcCli, nil
}

func (c *ClientSet) GetBceCredential(accountId, userId string) (*auth.BceCredentials, error) {
	stsCred, err := c.StsClient().AssumeRole(accountId, userId)
	if err != nil {
		return nil, err
	}

	return &auth.BceCredentials{
		AccessKeyId:     stsCred.AccessKeyId,
		SecretAccessKey: stsCred.SecretAccessKey,
		SessionToken:    stsCred.SessionToken,
	}, nil
}

func (c *ClientSet) OrderClientForAccount(accountId, userId string) (billing.OrderClientInterface, error) {
	stsCred, err := c.StsClient().AssumeRole(accountId, userId)
	if err != nil {
		return nil, err
	}

	orderCli, err := billing.NewOrderClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, c.conf.OrderManagerEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create order client failed: %w", err)
	}
	orderCli.Client.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return orderCli, nil
}

func (c *ClientSet) FacadeClientForAccount(accountId, userId string) (billing.FacadeClientInterface, error) {
	stsCred, err := c.StsClient().GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	facadeCli, err := billing.NewFacadeClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, c.conf.OrderFacadeEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create facade client failed: %w", err)
	}
	facadeCli.Client.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return facadeCli, nil
}

func (c *ClientSet) ResourceClientForAccount(accountId, userId string) (billing.ResourceClientInterface, error) {
	stsCred, err := c.StsClient().GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	resourceClient, err := billing.NewResourceClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, c.conf.ResourceManageEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create resource client for account failed: %w", err)
	}
	resourceClient.Client.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return resourceClient, nil
}

func (c *ClientSet) AutoRuleClientForAccount(accountId, userId string) (billing.AutoRenewClientInterface, error) {
	stsCred, err := c.StsClient().GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	autoRenewClient, err := billing.NewAutoRenewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, c.conf.AutoRenewEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create auto rule client failed: %w", err)
	}
	autoRenewClient.Client.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return autoRenewClient, nil
}

func (c *ClientSet) CCRClientForAccount(accountId, userId string, endpoint string) (ccr.ClientInterface, error) {
	stsCred, err := c.StsClient().GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	ccrClient, err := ccr.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create ccr client failed: %w", err)
	}
	ccrClient.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return ccrClient, nil
}

func (c *ClientSet) LegacyKubernetesClientset() kubernetes.Interface {
	return c.k8sLegacyClient
}

func (c *ClientSet) PersonalClientForAccount(accountId, userId string) (personal.Interface, error) {
	stsCred, err := c.StsClient().GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	ccrClient, err := personal.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey)
	if err != nil {
		return nil, fmt.Errorf("create ccr client failed: %w", err)
	}
	ccrClient.Client.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return ccrClient, nil
}

func (c *ClientSet) CCRClientForAkSkSessionToken(ak, sk, sessionToken string, endpoint string) (ccr.ClientInterface, error) {
	cli, err := ccr.NewClient(ak, sk, endpoint)
	if err != nil {
		return nil, err
	}
	cli.GetBceClientConfig().Credentials.SessionToken = sessionToken
	return cli, nil
}

func (c *ClientSet) LogicTagClientForAccount(accountId, userId string) (logictag.Client, error) {
	stsCred, err := c.StsClient().GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	tagClient, err := logictag.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, c.conf.LogicTagEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create tag client failed: %w", err)
	}
	tagClient.Client.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return tagClient, nil
}

func (c *ClientSet) ResourceGroupClientForAccount(accountId, userId string) (resourcegroup.Client, error) {
	stsCred, err := c.StsClient().GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	resGroupClient, err := resourcegroup.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, c.conf.ResGroupEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create resource group client failed: %w", err)
	}
	resGroupClient.Client.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return resGroupClient, nil
}

// CertClientForAccount 为指定账户和用户获取证书客户端。
func (c *ClientSet) CertClientForAccount(accountID, userID string) (certificate.Interface, error) {
	stsCred, err := c.StsClient().AssumeRole(accountID, userID)
	if err != nil {
		return nil, err
	}

	certCli, err := certificate.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, stsCred.SessionToken, c.conf.CertificateEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create certificate client failed: %w", err)
	}

	return certCli, nil
}

// BcdClientForAccount 为指定账户和用户获取BCD客户端。
func (c *ClientSet) BcdClientForAccount(accountID, userID string) (bcd.Interface, error) {
	stsCred, err := c.StsClient().AssumeRole(accountID, userID)
	if err != nil {
		return nil, err
	}

	bcdClient, err := bcd.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, stsCred.SessionToken, c.conf.DomainCheckEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create bcd client failed: %w", err)
	}

	return bcdClient, nil
}
