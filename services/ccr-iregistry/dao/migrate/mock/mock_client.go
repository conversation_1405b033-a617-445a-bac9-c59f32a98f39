// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao/migrate (interfaces: MigrateInterface)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	migrate "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao/migrate"
)

// MockMigrateInterface is a mock of MigrateInterface interface.
type MockMigrateInterface struct {
	ctrl     *gomock.Controller
	recorder *MockMigrateInterfaceMockRecorder
}

// MockMigrateInterfaceMockRecorder is the mock recorder for MockMigrateInterface.
type MockMigrateInterfaceMockRecorder struct {
	mock *MockMigrateInterface
}

// NewMockMigrateInterface creates a new mock instance.
func NewMockMigrateInterface(ctrl *gomock.Controller) *MockMigrateInterface {
	mock := &MockMigrateInterface{ctrl: ctrl}
	mock.recorder = &MockMigrateInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMigrateInterface) EXPECT() *MockMigrateInterfaceMockRecorder {
	return m.recorder
}

// BatchDelete mocks base method.
func (m *MockMigrateInterface) BatchDelete(arg0 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelete", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelete indicates an expected call of BatchDelete.
func (mr *MockMigrateInterfaceMockRecorder) BatchDelete(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelete", reflect.TypeOf((*MockMigrateInterface)(nil).BatchDelete), arg0)
}

// BatchInsert mocks base method.
func (m *MockMigrateInterface) BatchInsert(arg0 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchInsert", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchInsert indicates an expected call of BatchInsert.
func (mr *MockMigrateInterfaceMockRecorder) BatchInsert(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchInsert", reflect.TypeOf((*MockMigrateInterface)(nil).BatchInsert), arg0)
}

// DeleteRecord mocks base method.
func (m *MockMigrateInterface) DeleteRecord(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRecord", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRecord indicates an expected call of DeleteRecord.
func (mr *MockMigrateInterfaceMockRecorder) DeleteRecord(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRecord", reflect.TypeOf((*MockMigrateInterface)(nil).DeleteRecord), arg0)
}

// Exist mocks base method.
func (m *MockMigrateInterface) Exist(arg0 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exist", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Exist indicates an expected call of Exist.
func (mr *MockMigrateInterfaceMockRecorder) Exist(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exist", reflect.TypeOf((*MockMigrateInterface)(nil).Exist), arg0)
}

// InsertRecord mocks base method.
func (m *MockMigrateInterface) InsertRecord(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertRecord", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertRecord indicates an expected call of InsertRecord.
func (mr *MockMigrateInterfaceMockRecorder) InsertRecord(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertRecord", reflect.TypeOf((*MockMigrateInterface)(nil).InsertRecord), arg0)
}

// ListRecords mocks base method.
func (m *MockMigrateInterface) ListRecords() ([]*migrate.NamespaceMigration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRecords")
	ret0, _ := ret[0].([]*migrate.NamespaceMigration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRecords indicates an expected call of ListRecords.
func (mr *MockMigrateInterfaceMockRecorder) ListRecords() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRecords", reflect.TypeOf((*MockMigrateInterface)(nil).ListRecords))
}
