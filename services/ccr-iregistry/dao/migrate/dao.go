package migrate

import (
	"fmt"
	"time"

	_ "github.com/golang/mock/mockgen/model"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao/migrate MigrateInterface
type MigrateInterface interface {
	InsertRecord(ns string) error
	DeleteRecord(ns string) error
	ListRecords() ([]*NamespaceMigration, error)
	Exist(ns string) (bool, error)
	BatchInsert(ns []string) error
	BatchDelete(ns []string) error
}

var _ MigrateInterface = &Dao{}

type Dao struct {
	db *gorm.DB
}

func NewDao(db *gorm.DB) *Dao {
	return &Dao{
		db: db,
	}
}

func (d *Dao) InsertRecord(ns string) error {
	return d.db.Table(NamespaceMigrationTableName).Create(&NamespaceMigration{
		Name:       ns,
		CreateTime: utils.TimePtr(time.Now()),
	}).Error
}

func (d *Dao) DeleteRecord(ns string) error {
	return d.db.Table(NamespaceMigrationTableName).Delete(&NamespaceMigration{
		Name: ns,
	}).Error
}

func (d *Dao) ListRecords() ([]*NamespaceMigration, error) {
	var nm []*NamespaceMigration
	result := d.db.Table(NamespaceMigrationTableName).Find(&nm)
	return nm, result.Error
}

func (d *Dao) Exist(ns string) (bool, error) {
	var nm NamespaceMigration
	result := d.db.Table(NamespaceMigrationTableName).Where("name=?", ns).First(&nm)
	if gorm.ErrRecordNotFound == result.Error {
		return false, nil
	}
	return result.RowsAffected == 1, result.Error
}

func (d *Dao) BatchInsert(ns []string) error {
	return d.db.Table(NamespaceMigrationTableName).Transaction(func(tx *gorm.DB) error {
		for _, v := range ns {
			if err := tx.Create(&NamespaceMigration{
				Name:       v,
				CreateTime: utils.TimePtr(time.Now()),
			}).Error; err != nil {
				return fmt.Errorf("create %s failed: %s", v, err)
			}
		}
		return nil
	})
}

func (d *Dao) BatchDelete(ns []string) error {
	return d.db.Table(NamespaceMigrationTableName).Where("name in ?", ns).Delete(&NamespaceMigration{}).Error
}
