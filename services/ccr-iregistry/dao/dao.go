package dao

import (
	"fmt"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao/migrate"
)

func InitDatabase(dbConfig *config.HarborDatabase) (*gorm.DB, error) {
	db, err := gorm.Open(postgres.Open(dbConfig.DSN()), &gorm.Config{SkipDefaultTransaction: true})
	if err != nil {
		return nil, fmt.Errorf("open postgres database failed: %w", err)
	}

	if err = db.AutoMigrate(&migrate.NamespaceMigration{}); err != nil {
		return nil, fmt.Errorf("migration of database failed: %w", err)
	}

	return db, nil
}
