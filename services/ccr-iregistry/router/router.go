package router

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	govalidator "github.com/go-playground/validator/v10"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/uic"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/session"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/validator"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao/migrate"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/handler"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/service"
)

func NewRouter(conf *config.Config, harborAuthCli *harbor.HarborClient, db *gorm.DB) *gin.Engine {
	r := gin.New()

	csrfService, err := utils.NewHarborCsrf(conf.CsrfKey)
	if err != nil {
		panic(fmt.Sprintf("generate csrf token failed %s", err))
	}

	proxyHandler := handler.NewProxyHandler(
		conf.HarborIP,
		conf.HarborDomain,
		conf.OriginRegistryAddr,
		conf.ServiceTokenName,
		conf.TokenPath,
		conf.Migrating,
		migrate.NewDao(db),
		csrfService,
		&conf.P2p,
	)

	r.GET("/ping", func(c *gin.Context) {
		c.JSON(http.StatusOK, "pong")
	})

	if v, ok := binding.Validator.Engine().(*govalidator.Validate); ok {
		if err := v.RegisterValidation("password", validator.HarborPasswordValidate); err != nil {
			return nil
		}
	}

	r.Use(gin_context.LoggerMiddleware())

	// iregistry specific
	{
		tagHandler := handler.NewTagHandler()
		repoHandler := handler.NewRepositoryHandler()

		iregistryGroup := r.Group("/rest/v1")
		{
			projectHandler := handler.NewProjectHandler() // TODO 根据instanceId 获取harbor地址
			projectGroup := iregistryGroup.Group("/project", middleware.OrmMiddleware())
			{
				projectGroup.GET("/:project_name_or_id", projectHandler.GetProject)
				projectGroup.GET("", projectHandler.GetProject)

				projectGroup.GET("/:project_name_or_id/repositories", repoHandler.ListRepository)

				projectGroup.GET("/:project_name_or_id/repositories/:repository_name/tags", tagHandler.ListTagsByProjectNameAndRepoName)
				projectGroup.GET("/:project_name_or_id/repositories/:repository_name/tags/:tag_name", tagHandler.GetTag)
			}

			imageGroup := iregistryGroup.Group("/images", middleware.OrmMiddleware())
			{
				imageGroup.GET("/getAllTagsByRepoName", tagHandler.ListAllTags)
				imageGroup.GET("/tags/:tag_name", tagHandler.GetTagInfo)
				imageGroup.GET("/repositories", repoHandler.ListRepositories)
				imageGroup.GET("/tags", tagHandler.ListPagedTags)
				imageGroup.DELETE("/tags/:tag_name", tagHandler.DeleteImage)
				imageGroup.POST("/tags/:tag_name/retag", tagHandler.Retag)
			}
		}

		harborV1Group := r.Group("/api/repositories", middleware.OrmMiddleware())
		{
			harborV1Group.GET("/*repoName", tagHandler.ListAllTagsV1)
		}
	}

	// migrate specific
	migrateHandler := handler.NewMigrateHandler(db)
	migrateGroup := r.Group("/migrate/v1", middleware.AdminMiddleware(conf.HarborAdminPassword))
	{
		migrateGroup.POST("/projects", migrateHandler.Create)
		migrateGroup.DELETE("/projects", migrateHandler.Delete)
		migrateGroup.GET("/projects", migrateHandler.List)
	}

	// harbor 重写接口
	sessionTokenService := session.NewSessionToken(harborAuthCli)
	uicCli := uic.NewClient(conf.Uic.Address, conf.Uic.Username, conf.Uic.Secret)
	uuapService := service.NewBaiduUUAP(uicCli)
	harborGroup := r.Group("/api/v2.0", middleware.OrmMiddleware(), middleware.SessionMiddleware(uuapService, sessionTokenService))
	{
		userHandler := &handler.UserHandler{}
		harborGroup.PUT("/users/:user_id/password", userHandler.ChangePassword)

		systemHandler := handler.NewSystemHandler(conf.HarborDomain, harborAuthCli)
		harborGroup.GET("/systeminfo", systemHandler.GetSystemInfo)

		harborProject := handler.NewHarborProjectHandler(uuapService, uicCli, proxyHandler)
		harborGroup.POST("/projects/:project_id/members", harborProject.AddMember)
		harborGroup.POST("/projects/", harborProject.CreateProject)
		harborGroup.GET("/projects/", harborProject.ListProject)
		harborGroup.GET("/projects/:project_id", harborProject.GetProject)
	}

	casGroup := r.Group("/c")
	{
		casHandler := handler.NewCasHandler(conf.Cas.Endpoint, conf.Cas.LocalEndpoint, uuapService, sessionTokenService)
		casGroup.GET("/cas/login", casHandler.RedirectLogin())
		casGroup.GET("/cas/callback", casHandler.Callback())
		casGroup.GET("/log_out", casHandler.Logout())
	}

	// service token
	{
		tokenHandler := handler.NewTokenHandler(conf.ReadOnlyUsers,
			[]byte(conf.PrivateKey),
			token.NewTokenMaker(30, "harbor-registry"),
			proxyHandler,
		)
		r.GET(conf.TokenPath, middleware.OrmMiddleware(),
			tokenHandler.GetServiceToken)
	}

	//iregistryProxy := handler.NewIRegistryProxyHandler(conf.OriginRegistryAddr)
	//r.Any("/iregistry/v2/*wild", iregistryProxy.Proxy)

	r.Any("/v2/*wild", proxyHandler.RegistryProxy)

	r.NoRoute(gin_context.LoggerMiddleware(),
		middleware.OrmMiddleware(),
		middleware.SessionMiddleware(uuapService, sessionTokenService),
		proxyHandler.Proxy)

	return r
}
