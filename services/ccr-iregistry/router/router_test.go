package router

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/config"
)

func Test_NewRouter(t *testing.T) {
	engine := NewRouter(
		&config.Config{
			HarborIP:     "********",
			CsrfKey:      "this is 32 bit key used for test",
			ListenAddr:   ":1234",
			HarborDomain: "test.com",
		}, nil, nil)

	assert.Equal(t, 35, len(engine.Routes()))
}
