package cmd

import (
	"fmt"

	"github.com/goharbor/harbor/src/common/dao"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/config"
	ccriregistrydao "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/router"
)

var (
	configFile string // ccr-iregistry 配置文件
	runMode    string // gin 模式: debug release test
	version    string
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "ccr-iregistry",
	Short: "harbor iregistry",
	Long:  `ccr-iregistry is used to supplement the harbor`,
	// Uncomment the following line if your bare application
	// has an action associated with it:
	Example: "ccr-iregistry --config config.yaml",
	Run: func(cmd *cobra.Command, args []string) {
		rootRun()
	},
	Version: version,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	cobra.CheckErr(rootCmd.Execute())
}

func init() {
	//cobra.OnInitialize(initConfig)

	flags := rootCmd.Flags()

	flags.StringVar(&configFile, "config", "", "ccr-iregistry service 配置文件.")
	flags.StringVar(&runMode, "run-mode", "debug", "run 模式: debug release test.")

}

func rootRun() error {

	conf, err := config.NewConfig(configFile)
	if err != nil {
		panic(fmt.Sprintf("read config file failed: %s", err))
	}
	logrus.Infof("config %#v", conf)

	harborDB, err := config.NewHarborDatabaseConfig(conf.HarborDatabase)
	if err != nil {
		logrus.Errorf("generate database config failed: %s", err)
		return err
	}

	if err = dao.InitDatabase(harborDB); err != nil {
		logrus.Errorf("init database failed: %s", err)
		return err
	}

	harborAuthCli, err := harbor.NewHarborClientForHost(conf.HarborIP, conf.HarborDomain, "admin", conf.HarborAdminPassword)
	if err != nil {
		logrus.Errorf("create harbor client with auth failed: %v", err)
		return err
	}

	migrateDB, err := ccriregistrydao.InitDatabase(&conf.HarborDatabase)
	if err != nil {
		logrus.Errorf("init ccr iregsitry dao failed: %s", err)
		return err
	}

	// Initialize internal Mgr/Dao ...
	route := router.NewRouter(conf, harborAuthCli, migrateDB)
	route.Run(conf.ListenAddr)

	return nil
}
