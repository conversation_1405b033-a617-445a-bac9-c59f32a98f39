basePath: /iregistry/v1
definitions:
  models.Project:
    type: object
  models.Repository:
    properties:
      creation_time:
        type: string
      id:
        type: integer
      name:
        type: string
      project_id:
        type: integer
      pull_count:
        type: integer
      tags_count:
        type: integer
      update_time:
        type: string
    type: object
  models.Tag:
    properties:
      artifact_id:
        description: the artifact ID that the tag attaches to, it changes when pushing
          a same name but different digest artifact
        type: integer
      digest:
        type: string
      id:
        type: integer
      name:
        type: string
      pull_time:
        type: string
      push_time:
        type: string
      repository_id:
        description: tags are the resources of repository, one repository only contains
          one same name tag
        type: integer
      size:
        type: integer
    type: object
host: ccr.baidubce.com
info:
  contact:
    email: <EMAIL>
    name: du<PERSON><PERSON><PERSON>,wenmanxiang
  description: CCR IRegistry 提供 RESTFUL 风格 API, 提供harbor的补充功能
  title: CCR IRegistry
  version: 0.0.1
paths:
  /project/{project_name_or_id}/repositories/{repository_name}/tags:
    get:
      consumes:
      - application/json
      description: 通过project和repository查询出该repository下所有tag
      operationId: listTagsByProjectAndRepo
      parameters:
      - description: An unique ID for the request
        in: header
        name: x-bce-request-id
        type: string
      - description: 项目名字
        in: path
        name: project_name_or_id
        required: true
        type: string
      - description: 仓库名字
        in: path
        name: repository_name
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        name: page_no
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            x-total-count:
              description: The total count of tag
              type: integer
          schema:
            items:
              $ref: '#/definitions/models.Tag'
            type: array
        "400":
          description: Bad Request
          headers:
            x-bce-request-id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            x-bce-request-id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: 列举一个repo下的tag
      tags:
      - tag
  /projects/{project_id}/members:
    post: {}
  /projects/{project_name_or_id}:
    get:
      consumes:
      - application/json
      description: This endpoint returns specific project information by project ID.
      operationId: getProjectByName
      parameters:
      - description: An unique ID for the request
        in: header
        name: x-bce-request-id
        type: string
      - description: The name or id of the project
        in: path
        name: project_name_or_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Return matched project information.
          schema:
            $ref: '#/definitions/models.Project'
        "500":
          description: Internal Server Error
          headers:
            x-bce-request-id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Return specific project detail information
      tags:
      - project
  /projects/{project_name_or_id}/repositories:
    get:
      consumes:
      - application/json
      description: 查询镜像仓库列表
      parameters:
      - description: An unique ID for the request
        in: header
        name: x-bce-request-id
        type: string
      - description: 命名空间名称或者ID
        in: path
        name: project_name_or_id
        required: true
        type: string
      - description: 镜像仓库名称
        in: query
        name: repository_name
        type: string
      - default: 1
        description: 当前页
        in: query
        name: page_no
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          headers:
            x-total-count:
              description: The total count of repository
              type: integer
          schema:
            items:
              $ref: '#/definitions/models.Repository'
            type: array
        "400":
          description: Bad Request
          headers:
            x-bce-request-id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            x-bce-request-id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      summary: 查询镜像仓库列表
      tags:
      - repository
  /projects/{project_name_or_id}/repositories/{repository_name}/tags/{tag_name}:
    get:
      consumes:
      - application/json
      description: 通过project和repository及tag查询出该tag
      operationId: getTag
      parameters:
      - description: An unique ID for the request
        in: header
        name: X-Request-Id
        type: string
      - description: 项目名字
        in: path
        name: project_name_or_id
        required: true
        type: string
      - description: 仓库名字
        in: path
        name: repository_name
        required: true
        type: string
      - description: 标签名字
        in: path
        name: tag_name
        required: true
        type: string
      - description: request id
        in: header
        name: x-bce-request-id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Tag'
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: 通过project和repository及tag查询出该tag
      tags:
      - tag
securityDefinitions:
  BasicAuth:
    type: basic
swagger: "2.0"
