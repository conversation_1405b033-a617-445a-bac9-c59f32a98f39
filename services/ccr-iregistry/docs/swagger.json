{"swagger": "2.0", "info": {"description": "CCR IRegistry 提供 RESTFUL 风格 API, 提供harbor的补充功能", "title": "CCR IRegistry", "contact": {"name": "duzhanwei,wenmanxiang", "email": "<EMAIL>"}, "version": "0.0.1"}, "host": "ccr.baidubce.com", "basePath": "/iregistry/v1", "paths": {"/project/{project_name_or_id}/repositories/{repository_name}/tags": {"get": {"security": [{"BasicAuth": []}], "description": "通过project和repository查询出该repository下所有tag", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tag"], "summary": "列举一个repo下的tag", "operationId": "listTagsByProjectAndRepo", "parameters": [{"type": "string", "description": "An unique ID for the request", "name": "x-bce-request-id", "in": "header"}, {"type": "string", "description": "项目名字", "name": "project_name_or_id", "in": "path", "required": true}, {"type": "string", "description": "仓库名字", "name": "repository_name", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "当前页", "name": "page_no", "in": "query"}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Tag"}}, "headers": {"x-total-count": {"type": "integer", "description": "The total count of tag"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}, "headers": {"x-bce-request-id": {"type": "string", "description": "The ID of the corresponding request for the response"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}, "headers": {"x-bce-request-id": {"type": "string", "description": "The ID of the corresponding request for the response"}}}}}}, "/projects/{project_id}/members": {"post": {}}, "/projects/{project_name_or_id}": {"get": {"security": [{"BasicAuth": []}], "description": "This endpoint returns specific project information by project ID.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["project"], "summary": "Return specific project detail information", "operationId": "getProjectByName", "parameters": [{"type": "string", "description": "An unique ID for the request", "name": "x-bce-request-id", "in": "header"}, {"type": "string", "description": "The name or id of the project", "name": "project_name_or_id", "in": "path", "required": true}], "responses": {"200": {"description": "Return matched project information.", "schema": {"$ref": "#/definitions/models.Project"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}, "headers": {"x-bce-request-id": {"type": "string", "description": "The ID of the corresponding request for the response"}}}}}}, "/projects/{project_name_or_id}/repositories": {"get": {"description": "查询镜像仓库列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["repository"], "summary": "查询镜像仓库列表", "parameters": [{"type": "string", "description": "An unique ID for the request", "name": "x-bce-request-id", "in": "header"}, {"type": "string", "description": "命名空间名称或者ID", "name": "project_name_or_id", "in": "path", "required": true}, {"type": "string", "description": "镜像仓库名称", "name": "repository_name", "in": "query"}, {"type": "integer", "default": 1, "description": "当前页", "name": "page_no", "in": "query"}, {"type": "integer", "default": 10, "description": "每页记录数", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Repository"}}, "headers": {"x-total-count": {"type": "integer", "description": "The total count of repository"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}, "headers": {"x-bce-request-id": {"type": "string", "description": "The ID of the corresponding request for the response"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}, "headers": {"x-bce-request-id": {"type": "string", "description": "The ID of the corresponding request for the response"}}}}}}, "/projects/{project_name_or_id}/repositories/{repository_name}/tags/{tag_name}": {"get": {"security": [{"BasicAuth": []}], "description": "通过project和repository及tag查询出该tag", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tag"], "summary": "通过project和repository及tag查询出该tag", "operationId": "getTag", "parameters": [{"type": "string", "description": "An unique ID for the request", "name": "X-Request-Id", "in": "header"}, {"type": "string", "description": "项目名字", "name": "project_name_or_id", "in": "path", "required": true}, {"type": "string", "description": "仓库名字", "name": "repository_name", "in": "path", "required": true}, {"type": "string", "description": "标签名字", "name": "tag_name", "in": "path", "required": true}, {"type": "string", "description": "request id", "name": "x-bce-request-id", "in": "header"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Tag"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}}, "definitions": {"models.Project": {"type": "object"}, "models.Repository": {"type": "object", "properties": {"creation_time": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "project_id": {"type": "integer"}, "pull_count": {"type": "integer"}, "tags_count": {"type": "integer"}, "update_time": {"type": "string"}}}, "models.Tag": {"type": "object", "properties": {"artifact_id": {"description": "the artifact ID that the tag attaches to, it changes when pushing a same name but different digest artifact", "type": "integer"}, "digest": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "pull_time": {"type": "string"}, "push_time": {"type": "string"}, "repository_id": {"description": "tags are the resources of repository, one repository only contains one same name tag", "type": "integer"}, "size": {"type": "integer"}}}}, "securityDefinitions": {"BasicAuth": {"type": "basic"}}}