package config

import (
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"strconv"

	"k8s.io/apimachinery/pkg/util/yaml"

	"github.com/goharbor/harbor/src/common/models"
)

type HarborDatabase struct {
	Host     string `yaml:"host,omitempty"`
	Port     string `yaml:"port,omitempty"`
	Username string `yaml:"username,omitempty"`
	Password string `yaml:"password,omitempty"`
	Database string `yaml:"database,omitempty"`
	SSLMode  string `yaml:"sslMode,omitempty"`
}

type UicConfig struct {
	Address  string `yaml:"address,omitempty"`
	Username string `yaml:"username,omitempty"`
	Secret   string `yaml:"secret,omitempty"`
}

type CasConfig struct {
	Endpoint      string `yaml:"endpoint,omitempty"`
	LocalEndpoint string `yaml:"localEndpoint,omitempty"`
}

type P2pConfig struct {
	PreheatServerAddr string   `yaml:"preheatServerAddr,omitempty"`
	AuthUser          string   `yaml:"authUser,omitempty"`
	AuthPassword      string   `yaml:"authPassword,omitempty"`
	ProjectList       []string `yaml:"projectList,flow,omitempty"`
}

type Config struct {
	HarborDatabase      HarborDatabase `yaml:"harborDatabase,omitempty"`
	Uic                 UicConfig      `yaml:"uic,omitempty"`
	Cas                 CasConfig      `yaml:"cas,omitempty"`
	ListenAddr          string         `yaml:"listenAddr,omitempty"`
	HarborIP            string         `yaml:"harborIP,omitempty"`
	HarborDomain        string         `yaml:"harborDomain,omitempty"`
	HarborAdminPassword string         `yaml:"harborAdminPassword,omitempty"`
	TokenPath           string         `yaml:"tokenPath,omitempty"`
	CsrfKey             string         `yaml:"csrfKey,omitemtpy"`
	ServiceTokenName    string         `yaml:"serviceTokenName,omitempty"`
	OriginRegistryAddr  string         `yaml:"originRegistryAddr,omitempty"`
	P2p                 P2pConfig      `yaml:"p2p,omitempty"`
	Migrating           bool           `yaml:"migrating,omitempty"`
	ReadOnlyUsers       []string       `yaml:"readOnlyUsers,omitempty"`
	PrivateKey          string         `yaml:"privateKey,omitempty"`
}

func IRegistryDatabaseDSNFromConfig(dbConf HarborDatabase) string {
	return fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		dbConf.Host, dbConf.Username, dbConf.Password, dbConf.Database, dbConf.Port, dbConf.SSLMode,
	)
}

func NewHarborDatabaseConfig(dbConf HarborDatabase) (*models.Database, error) {
	port, err := strconv.Atoi(dbConf.Port)
	if err != nil {
		return nil, err
	}

	return &models.Database{
		Type: "postgresql",
		PostGreSQL: &models.PostGreSQL{
			Host:         dbConf.Host,
			Port:         port,
			Username:     dbConf.Username,
			Password:     dbConf.Password,
			Database:     dbConf.Database,
			SSLMode:      dbConf.SSLMode,
			MaxIdleConns: 2,
			MaxOpenConns: 0,
		},
	}, nil
}

func NewConfig(filepath string) (*Config, error) {
	content, err := ioutil.ReadFile(filepath)
	if err != nil {
		return nil, err
	}

	return configFromString(string(content))
}

func configFromString(content string) (*Config, error) {
	var config Config
	err := yaml.Unmarshal([]byte(content), &config)
	if err != nil {
		return nil, err
	}

	if config.TokenPath == "" {
		config.TokenPath = "/service/token"
	}

	// base64 decode
	if config.PrivateKey != "" {
		content, err := base64.StdEncoding.DecodeString(config.PrivateKey)
		if err != nil {
			return nil, fmt.Errorf("invalid private key: %s", err)
		}
		config.PrivateKey = string(content)
	}

	return &config, nil
}

func (h HarborDatabase) DSN() string {
	sslMode := "false"
	if h.SSLMode != "" {
		sslMode = h.SSLMode
	}
	return fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		h.Host, h.Username, h.Password, h.Database, h.Port, sslMode)
}
