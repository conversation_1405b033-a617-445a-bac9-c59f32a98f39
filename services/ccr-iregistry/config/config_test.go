package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_configFromString(t *testing.T) {
	src := `xyz: test
  12: xx`
	cf, err := configFromString(src)
	assert.Error(t, err)
	assert.Nil(t, cf)

	src = `listenAddr: test`
	cf, err = configFromString(src)
	assert.NoError(t, err)
	assert.NotNil(t, cf)
	assert.Equal(t, "/service/token", cf.TokenPath)

	src = `listenAddr: test
privateKey: xxxx=_)(*&&)`
	cf, err = configFromString(src)
	assert.Error(t, err)
	assert.Nil(t, cf)
}
