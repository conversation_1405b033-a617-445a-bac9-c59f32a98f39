package models

import (
	"time"

	"github.com/goharbor/harbor/src/pkg/artifact"

	modeltag "github.com/goharbor/harbor/src/pkg/tag/model/tag"
)

type GetTagByProjectNameAndRepoNameAndTagNameParam struct {
	RequestId   string
	ProjectName string
	RepoName    string
	TagName     string
}

type ListTagsByProjectNameAndRepoNameParam struct {
	RequestId   string
	ProjectName string
	RepoName    string
}

type Tag struct {
	ID           int64     `json:"id"`
	RepositoryID int64     `json:"repository_id"` // tags are the resources of repository, one repository only contains one same name tag
	ArtifactID   int64     `json:"artifact_id"`   // the artifact ID that the tag attaches to, it changes when pushing a same name but different digest artifact
	Name         string    `json:"name"`
	PushTime     time.Time `json:"push_time"`
	PullTime     time.Time `json:"pull_time"`

	Digest string `json:"digest"`
	Size   int64  `json:"size"`
}

// TagSwagger model
type TagSwagger struct {
	t *modeltag.Tag
	a *artifact.Artifact
}

// ToSwagger converts the project to the swagger model
func (t *TagSwagger) ToSwagger() *Tag {

	return &Tag{
		ID:           t.t.ID,
		RepositoryID: t.t.RepositoryID,
		ArtifactID:   t.t.ArtifactID,
		Name:         t.t.Name,
		PushTime:     t.t.PushTime,
		PullTime:     t.t.PullTime,
		Digest:       t.a.Digest,
		Size:         t.a.Size,
	}

}

// NewTagSwagger ...
func NewTagSwagger(t *modeltag.Tag, a *artifact.Artifact) *TagSwagger {
	return &TagSwagger{t, a}
}
