package models

import (
	"github.com/go-openapi/strfmt"
	"github.com/goharbor/harbor/src/controller/project"
)

// Project project
// swagger:model Project
type Project struct {

	// Project ID
	ProjectID int32 `json:"project_id" format:"int32"`

	// The owner ID of the project always means the creator of the project.
	OwnerID int32 `json:"owner_id"`

	// The name of the project.
	Name string `json:"name"`

	// The creation time of the project.
	// Format: date-time
	CreationTime strfmt.DateTime `json:"creation_time" format:"date-time"`

	// The update time of the project.
	// Format: date-time
	UpdateTime strfmt.DateTime `json:"update_time" format:"date-time"`

	// A deletion mark of the project.
	Deleted bool `json:"deleted"`
}

// ProjectSwagger model
type ProjectSwagger struct {
	*project.Project
}

// ToSwagger converts the project to the swagger model
func (p *ProjectSwagger) ToSwagger() *Project {

	return &Project{
		CreationTime: strfmt.DateTime(p.CreationTime),
		Name:         p.Name,
		OwnerID:      int32(p.OwnerID),
		ProjectID:    int32(p.ProjectID),
		UpdateTime:   strfmt.DateTime(p.UpdateTime),
		Deleted:      p.Deleted,
	}
}

// NewProjectSwagger ...
func NewProjectSwagger(p *project.Project) *ProjectSwagger {
	return &ProjectSwagger{p}
}

type ProjectMetadata struct {
	ProjectName string            `json:"project_name,omitempty"`
	Metadata    map[string]string `json:"metadata,omitempty"`
}
