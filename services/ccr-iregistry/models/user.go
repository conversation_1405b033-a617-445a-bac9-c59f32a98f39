package models

type PasswordReq struct {
	OldPassword string `json:"old_password,omitempty"`
	NewPassword string `json:"new_password" binding:"required,password"`
}

type DevOpsUser struct {
	Username       string `json:"username,omitempty"`
	Email          string `json:"email,omitempty"`
	DisplayName    string `json:"displayName,omitempty"`
	DepartmentId   string `json:"departmentId,omitempty"`
	DepartmentName string `json:"departmentName,omitempty"`
	EmployeeType   string `json:"employeeType,omitempty"`
	SyncVersion    string `json:"syncVersion,omitempty"`
	Origin         string `json:"origin,omitempty"`
}
