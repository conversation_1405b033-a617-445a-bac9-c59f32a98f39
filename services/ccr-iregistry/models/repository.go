package models

import (
	"time"

	"github.com/goharbor/harbor/src/pkg/repository/model"
)

// ListRepositoryResponse list repository response
type ListRepositoryResponse struct {
	PageInfo `json:",inline"`
	Items    []*model.RepoRecord `json:"items"`
}

type PageInfo struct {
	Total    int `json:"total"`
	PageNo   int `json:"pageNo"`
	PageSize int `json:"pageSize"`
}

type Repository struct {
	ID           int64     `json:"id"`
	Name         string    `json:"name"`
	ProjectID    int64     `json:"project_id"`
	PullCount    int64     `json:"pull_count"`
	CreationTime time.Time `json:"creation_time"`
	UpdateTime   time.Time `json:"update_time"`
	TagsCount    int64     `json:"tags_count"`
}

// RepositorySwagger model
type RepositorySwagger struct {
	*model.RepoRecord
}

// ToSwagger converts the project to the swagger model
func (r *RepositorySwagger) ToSwagger() *Repository {

	return &Repository{
		ID:           r.RepositoryID,
		Name:         r.Name,
		ProjectID:    r.ProjectID,
		PullCount:    r.PullCount,
		CreationTime: r.CreationTime,
		UpdateTime:   r.UpdateTime,
	}

}

// NewRepositorySwagger ...
func NewRepositorySwagger(r *model.RepoRecord) *RepositorySwagger {
	return &RepositorySwagger{r}
}
