package middleware

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	harbormodels "github.com/goharbor/harbor/src/common/models"
	"github.com/goharbor/harbor/src/pkg/user"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/session"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/service"
)

const (
	CCR_SECURITY_IDENTITY      = "ccr-security"
	CCR_SESSION_TOKEN_IDENTITY = "ccr-session-token"
)

func AdminMiddleware(harborAdminPassword string) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		username, passwd, ok := c.Request.BasicAuth()
		if !ok {
			logger.Errorf("no basic auth provided")
			c.AbortWithError(http.StatusUnauthorized, fmt.Errorf("no basic auth provided"))
			return
		}
		if username != "admin" || passwd != harborAdminPassword {
			logger.Errorf("basic auth is wrong, user %s password %s", username, passwd)
			c.AbortWithError(http.StatusUnauthorized, fmt.Errorf("no basic auth provided"))
			return
		}
	}
}

func BasicAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		if SecurityFromContext(c) != nil {
			return
		}

		username, passwd, ok := c.Request.BasicAuth()
		if !ok {
			logger.Warn("no basic auth provided")
			return
		}

		user, err := user.Mgr.MatchLocalPassword(HarborContext(c), username, passwd)

		if err != nil {
			logger.Errorf("failed to authenticate %s: %v", username, err)
			c.AbortWithError(http.StatusUnauthorized, fmt.Errorf("failed to authenticate %s: %v", username, err))
			return
		}
		if user == nil {
			c.AbortWithError(http.StatusUnauthorized, fmt.Errorf("invalid credentials"))
			return
		}
		c.Set(CCR_SECURITY_IDENTITY, user)
	}
}

func SecurityFromContext(c context.Context) *harbormodels.User {
	if ctx, ok := c.Value(CCR_SECURITY_IDENTITY).(*harbormodels.User); ok {
		return ctx
	}
	return nil
}

func DevopsAuthMiddleware(bduuap *service.BaiduUUAP) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)

		xdu, err := DecodeDevOpsUser(c.GetHeader("X-DevOps-User"))
		if err != nil {
			logger.Errorf("get devops user from X-DevOps-Use failed: %s", err)
			c.AbortWithStatusJSON(http.StatusBadRequest, "invalid user")
			return
		}

		user, err := bduuap.AuthenticateDevOps(HarborContext(c), xdu.Username)
		if err != nil {
			logger.Errorf("get user failed: %v", err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}

		c.Set(CCR_SECURITY_IDENTITY, user)
	}
}

func DecodeDevOpsUser(src string) (*models.DevOpsUser, error) {
	if src == "" {
		return nil, fmt.Errorf("length is 0")
	}

	decodeBytes, err := base64.StdEncoding.DecodeString(src)
	if err != nil {
		return nil, fmt.Errorf("invalid base64 string: %w", err)
	}

	xdu := models.DevOpsUser{}
	err = json.Unmarshal(decodeBytes, &xdu)
	if err != nil {
		return nil, fmt.Errorf("invalid devops user format: %w", err)
	}

	if len(xdu.Username) == 0 {
		return nil, fmt.Errorf("invalid user")
	}

	return &xdu, nil
}

func SessionMiddleware(bduuap *service.BaiduUUAP, sessionToken *session.SessionToken) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)

		// basic auth provided
		if _, _, ok := c.Request.BasicAuth(); ok {
			// has authorization, sid is lower priority
			logger.Infof("authorization header provided")

			return
		}

		// session 三件套: sid, _gorilla_csrf, x-harbor-csrf-token
		sessionExist := false
		for _, v := range c.Request.Cookies() {
			if v.Name == "sid" {
				sessionExist = true
			}
		}

		if sessionExist {
			// session id already exist
			logger.Info("session id exist")
			return
		}

		user := SecurityFromContext(c)

		// no auth provided, skip
		if user == nil && c.GetHeader("X-Devops-User") == "" {
			logger.Warningf("no auth provided, maybe public access")
			return
		}

		if user == nil {
			// try to get user from header
			xdu, err := DecodeDevOpsUser(c.GetHeader("X-DevOps-User"))
			if err != nil {
				logger.Errorf("get devops user from X-DevOps-Use failed: %s", err)
				c.AbortWithStatusJSON(http.StatusBadRequest, "no user provided")
				return
			}

			user, err = bduuap.AuthenticateDevOps(HarborContext(c), xdu.Username)
			if err != nil {
				logger.Errorf("get user failed: %v", err)
				c.AbortWithError(http.StatusInternalServerError, err)
				return
			}
		}

		sid, err := sessionToken.GetSessionToken(c, user)
		if err != nil {
			logger.Errorf("get session token failed: %s", err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}

		logger.Infof("with session id: %s", sid)
		c.Request.AddCookie(&http.Cookie{
			Name:     "sid",
			Value:    sid,
			Path:     "/",
			Secure:   true,
			HttpOnly: true,
		})

		c.Set(CCR_SESSION_TOKEN_IDENTITY, sid)
		c.Set(CCR_SECURITY_IDENTITY, user)
	}
}
