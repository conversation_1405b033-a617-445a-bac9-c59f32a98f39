package middleware

import (
	"context"

	o "github.com/astaxie/beego/orm"
	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/lib/orm"
)

const (
	ORM_CONTEXT_IDENTITY    = "orm"
	HARBOR_CONTEXT_IDENTITY = "harbor_context"
)

func OrmMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		SetHarborContext(c, orm.NewContext(HarborContext(c), o.NewOrm()))
	}
}

func HarborContext(c *gin.Context) context.Context {
	if ctx, ok := c.Value(HARBOR_CONTEXT_IDENTITY).(context.Context); ok {
		return ctx
	}

	return c.Request.Context()
}

func SetHarborContext(c *gin.Context, ctx context.Context) {
	c.Set(HARBOR_CONTEXT_IDENTITY, ctx)
}
