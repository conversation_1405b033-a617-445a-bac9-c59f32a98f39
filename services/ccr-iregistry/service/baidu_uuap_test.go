package service

import (
	"context"
	"fmt"
	"testing"

	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/goharbor/harbor/src/testing/pkg/user"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	mockuic "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/uic"
)

func Test_uuap_SearchUser(t *testing.T) {
	mockUic := mockuic.NewMockInterface(gomock.NewController(t))
	mockUic.EXPECT().GetUicUser("nonexist").Return(nil, nil)
	mockUic.EXPECT().GetUicUser("notfound").Return(nil, fmt.Errorf("not found"))

	usrMgr := &user.Manager{}
	usrMgr.On("GetByName", mock.Anything, "nonexist").Return(nil, nil)
	usrMgr.On("GetByName", mock.Anything, "notfound").Return(nil, nil)

	bd := &BaiduUUAP{
		uicCli:  mockUic,
		userMgr: usrMgr,
	}

	_, err := bd.SearchUser(context.Background(), "nonexist")
	assert.Error(t, err)

	_, err = bd.SearchUser(context.Background(), "notfound")
	assert.Error(t, err)
}
