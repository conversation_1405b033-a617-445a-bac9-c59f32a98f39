package service

import (
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"

	httputil "github.com/goharbor/harbor/src/common/http"
)

func ValidateTicketAndGerUsername(validateAddr, loginCallbackAddr, ticket string) (string, error) {
	serviceValidateUrl, err := url.Parse(validateAddr)
	if err != nil {
		return "", err
	}

	query := serviceValidateUrl.Query()
	query.Set("ticket", ticket)
	query.Set("service", loginCallbackAddr)
	serviceValidateUrl.RawQuery = query.Encode()

	req, err := http.NewRequest(http.MethodGet, serviceValidateUrl.String(), nil)
	if err != nil {
		return "", err
	}

	client := httputil.NewClient(nil)
	response, err := client.Do(req)
	if err != nil {
		return "", err
	}

	body, err := ioutil.ReadAll(response.Body)

	if err != nil {
		return "", err
	}

	if response.StatusCode != http.StatusOK {
		return "", fmt.Errorf("cas: validate ticket: %v", string(body))
	}

	var x xmlServiceResponse

	if err := xml.Unmarshal(body, &x); err != nil {
		return "", err
	}

	if x.Failure != nil {
		return "", fmt.Errorf("%s: %s", x.Failure.Code, x.Failure.Message)
	}

	return x.Success.User, nil
}

type xmlServiceResponse struct {
	XMLName xml.Name `xml:"http://www.yale.edu/tp/cas serviceResponse"`

	Failure *xmlAuthenticationFailure
	Success *xmlAuthenticationSuccess
}

type xmlAuthenticationFailure struct {
	XMLName xml.Name `xml:"authenticationFailure"`
	Code    string   `xml:"code,attr"`
	Message string   `xml:",innerxml"`
}

type xmlAuthenticationSuccess struct {
	XMLName             xml.Name           `xml:"authenticationSuccess"`
	User                string             `xml:"user"`
	ProxyGrantingTicket string             `xml:"proxyGrantingTicket,omitempty"`
	Proxies             *xmlProxies        `xml:"proxies"`
	Attributes          *xmlAttributes     `xml:"attributes"`
	ExtraAttributes     []*xmlAnyAttribute `xml:",any"`
}

type xmlProxies struct {
	XMLName xml.Name `xml:"proxies"`
	Proxies []string `xml:"proxy"`
}

func (p *xmlProxies) AddProxy(proxy string) {
	p.Proxies = append(p.Proxies, proxy)
}

type xmlAttributes struct {
	XMLName                                xml.Name  `xml:"attributes"`
	AuthenticationDate                     time.Time `xml:"authenticationDate"`
	LongTermAuthenticationRequestTokenUsed bool      `xml:"longTermAuthenticationRequestTokenUsed"`
	IsFromNewLogin                         bool      `xml:"isFromNewLogin"`
	MemberOf                               []string  `xml:"memberOf"`
	UserAttributes                         *xmlUserAttributes
	ExtraAttributes                        []*xmlAnyAttribute `xml:",any"`
}

type xmlUserAttributes struct {
	XMLName       xml.Name             `xml:"userAttributes"`
	Attributes    []*xmlNamedAttribute `xml:"attribute"`
	AnyAttributes []*xmlAnyAttribute   `xml:",any"`
}

type xmlNamedAttribute struct {
	XMLName xml.Name `xml:"attribute"`
	Name    string   `xml:"name,attr,omitempty"`
	Value   string   `xml:",innerxml"`
}

type xmlAnyAttribute struct {
	XMLName xml.Name
	Value   string `xml:",chardata"`
}
