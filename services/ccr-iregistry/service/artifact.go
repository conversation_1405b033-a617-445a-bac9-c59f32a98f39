package service

import (
	"context"
	"fmt"

	"github.com/goharbor/harbor/src/pkg/artifact"
)

type ArtifactService struct {
	artifactMgr artifact.Manager
}

func NewArtifactService() *ArtifactService {
	return &ArtifactService{
		artifactMgr: artifact.NewManager(),
	}
}

func (a *ArtifactService) FindArtifactByID(ctx context.Context, artifactID int64) (*artifact.Artifact, error) {

	arti, err := a.artifactMgr.Get(ctx, artifactID)
	if err != nil {
		return nil, fmt.Errorf("get artifact failed: %w", err)
	}

	return arti, nil
}
