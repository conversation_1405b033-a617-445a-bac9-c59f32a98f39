package service

import (
	"context"
	"fmt"

	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/artifact"
	mgrproject "github.com/goharbor/harbor/src/pkg/project"
	"github.com/goharbor/harbor/src/pkg/repository"
	"github.com/goharbor/harbor/src/pkg/repository/model"
	"github.com/goharbor/harbor/src/pkg/tag"
)

type RepositoryService struct {
	repoMgr     repository.Manager
	tagMgr      tag.Manager
	artifactMgr artifact.Manager
	projectMgr  mgrproject.Manager
}

func NewRepositoryService() *RepositoryService {
	return &RepositoryService{
		projectMgr:  mgrproject.Mgr,
		repoMgr:     repository.New(),
		tagMgr:      tag.NewManager(),
		artifactMgr: artifact.NewManager(),
	}
}

// ListRepositories List repositories of the specified project
func (r *RepositoryService) ListRepositories(ctx context.Context, projectId int64, repositoryName string, pageNo, pageSize int64) (int64, []*model.RepoRecord, error) {

	query, err := q.Build("", "", pageNo, pageSize)
	if err != nil {
		return 0, nil, err
	}

	query.Keywords["ProjectID"] = projectId
	query.Keywords["Name__contains"] = repositoryName

	repos, err := r.repoMgr.List(ctx, query)
	if err != nil {
		return 0, nil, fmt.Errorf("list repo failed: %w", err)
	}
	total, err := r.repoMgr.Count(ctx, query)
	if err != nil {
		return 0, nil, fmt.Errorf("count repo failed: %w", err)
	}

	return total, repos, nil
}
