package service

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/jarcoal/httpmock"
)

func Test_PreheatImage(t *testing.T) {

	preheatServerAddr := "http://10.11.144.35:8088"
	p := NewPreheatHandler(preheatServerAddr, "", "", []string{})

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	ctx.Request, _ = http.NewRequest("POST", "/v2/test/t1/manifests/v1", nil)

	// projectList is empty
	httpmock.RegisterResponder("POST", preheatServerAddr+"/preheats",
		httpmock.NewStringResponder(200, string("success")))
	image, tag := "eks_test/test", "1001"
	p.PreheatImage(ctx, image, tag)

	// image is empty
	p.PreheatImage(ctx, "", tag)

	//set projectList don't need to preheat
	p.projectList = []string{"eks_test", "eks_dev"}
	p.PreheatImage(ctx, image, tag)
	p.PreheatImage(ctx, "eks/test", tag)

	//response is not ok
	httpmock.RegisterResponder("POST", preheatServerAddr+"/preheats",
		httpmock.NewStringResponder(500, string("success")))
	p.PreheatImage(ctx, image, tag)

}
