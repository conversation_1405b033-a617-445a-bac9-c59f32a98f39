package service

import (
	"context"
	"errors"
	"fmt"

	"github.com/goharbor/harbor/src/common"
	"github.com/goharbor/harbor/src/common/models"
	"github.com/goharbor/harbor/src/common/utils"
	liberr "github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/pkg/user"
	group "github.com/goharbor/harbor/src/pkg/usergroup"
	groupmodel "github.com/goharbor/harbor/src/pkg/usergroup/model"
	"github.com/sirupsen/logrus"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/uic"
)

const (
	BaiduEmailGroupType = common.HTTPGroupType
)

type BaiduUUAP struct {
	uicCli  uic.Interface
	userMgr user.Manager
}

func NewBaiduUUAP(uicCli *uic.Client) *BaiduUUAP {
	return &BaiduUUAP{
		uicCli:  uicCli,
		userMgr: user.New(),
	}
}

func (a *BaiduUUAP) AuthenticateDevOps(ctx context.Context, username string) (*models.User, error) {
	if len(username) == 0 {
		return nil, fmt.Errorf("failed to get user: username is empty")
	}
	u, err := a.SearchUser(ctx, username)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %s, error: %v", username, err)
	}

	// onboard user if it's not yet onboarded
	if u.UserID <= 0 {
		err = a.OnBoardUser(ctx, u)
		if err != nil {
			return nil, fmt.Errorf("on board user failed: %w", err)
		}
	}

	if u.UserID == 0 {
		return nil, fmt.Errorf("no user id return after onboard")
	}

	return u, nil
}

// OnBoard user with random password, set uid
func (a *BaiduUUAP) OnBoardUser(ctx context.Context, u *models.User) error {

	if u.Username == "" {
		return fmt.Errorf("username is blank")
	}

	_, err := a.userMgr.GetByName(ctx, u.Username)
	// user is in db, return directly
	if err == nil {
		return nil
	}

	if !liberr.IsNotFoundErr(err) {
		return err
	}

	uicUser, err := a.uicCli.GetUicUser(u.Username)
	if err != nil {
		return err
	}

	u.Username = uicUser.Username
	u.Email = uicUser.Email
	u.Realname = uicUser.ZhName

	u.Salt = utils.GenerateRandomString()
	password := utils.GenerateRandomString()
	u.Password = utils.Encrypt(password, u.Salt, utils.SHA256)
	u.PasswordVersion = utils.SHA256

	if err := a.userMgr.Onboard(ctx, u); err != nil {
		return err
	}

	if err := a.uicCli.PatchUicUser(uicUser, u.UserID); err != nil {
		// try to delete user, no gurantee
		a.userMgr.Delete(ctx, u.UserID)
		logrus.Errorf("on board uic user error, %v", err)
		return fmt.Errorf("on board uic user error, %v", err)
	}

	return nil
}

// search user will return user with group info
func (a *BaiduUUAP) SearchUser(ctx context.Context, username string) (*models.User, error) {
	u, err := a.userMgr.GetByName(ctx, username)
	if err != nil && !liberr.IsNotFoundErr(err) {
		return nil, err
	}

	if u == nil {
		if uicUser, err := a.uicCli.GetUicUser(username); err == nil && uicUser != nil {
			u = &models.User{
				Username: uicUser.Username,
			}
		} else {
			if err != nil {
				return nil, fmt.Errorf("get uic user failed: %s", err)
			}
			return nil, fmt.Errorf("no uic user found")
		}
	}

	u.Password = ""
	u.Salt = ""

	ugNameList, err := a.uicCli.GetUserEmailGroup(username)
	if err != nil {
		logrus.Errorf("get user email group error, %v", err)
		return u, nil
	}

	logrus.Debugf("user groups %+v", ugNameList)
	if len(ugNameList) > 0 {
		groups := groupmodel.UserGroupsFromName(ugNameList, BaiduEmailGroupType)
		groupIDList, err := group.Mgr.Populate(ctx, groups)
		if err != nil {
			return nil, err
		}
		logrus.Debugf("current user's group ID list is %+v", groupIDList)
		u.GroupIDs = groupIDList
	}

	return u, nil
}

func (a *BaiduUUAP) SearchGroup(groupName string) (*groupmodel.UserGroup, error) {
	uicEmailGroup, err := a.uicCli.GetEmailGroupInfo(groupName)
	if err != nil || uicEmailGroup == nil {
		logrus.Errorf("get uic email group error, %v", err)
		return nil, errors.New("Group does not exist")
	}

	userGroup := &groupmodel.UserGroup{
		GroupName: uicEmailGroup.Email,
		GroupType: BaiduEmailGroupType,
	}

	return userGroup, nil
}

func (a *BaiduUUAP) OnBoardGroup(ctx context.Context, g *groupmodel.UserGroup, altGroupName string) error {
	if len(altGroupName) > 0 {
		g.GroupName = altGroupName
	}
	g.GroupType = BaiduEmailGroupType
	userGroupList, err := group.Mgr.List(ctx, groupmodel.UserGroup{
		GroupName: g.GroupName,
		GroupType: g.GroupType,
	})
	if err != nil {
		return err
	}

	// group already exist
	if len(userGroupList) > 0 {
		return nil
	}

	return group.Mgr.Onboard(ctx, g)
}
