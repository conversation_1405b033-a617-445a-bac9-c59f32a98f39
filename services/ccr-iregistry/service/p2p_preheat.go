package service

import (
	"bytes"
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/wxnacy/wgo/arrays"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
)

type PreheatHandler struct {
	preheatServerAddr string
	authUser          string
	authPassword      string
	projectList       []string
}

func NewPreheatHandler(preheatServerAddr string, authUser string, authPassword string, projectList []string) *PreheatHandler {
	return &PreheatHandler{
		preheatServerAddr: preheatServerAddr,
		authUser:          authUser,
		authPassword:      authPassword,
		projectList:       projectList,
	}
}

func (p *PreheatHandler) PreheatImage(c *gin.Context, image string, tag string) {
	logger := gin_context.LoggerFromContext(c)
	if image == "" || tag == "" {
		logger.Infof("image or tag is empty.")
		return
	}

	if len(p.projectList) > 0 {
		// 根据配置的命名空间列表判断是否需要预热做种
		ns := strings.Split(image, "/")[0]
		if arrays.Contains(p.projectList, ns) == -1 {
			logger.Infof("namespace [%s] not in seed list,not need to preheat", ns)
			return
		}
	}

	logger.Infof("begin p2p preheat, image [%s:%s].", image, tag)

	status, err := p.postData(image, tag)
	if status == http.StatusOK {
		logger.Infof("p2p preheat success, image [%s:%s].", image, tag)
		return
	}
	logger.Errorf("image [%s:%s] preheat failed: [%s]", image, tag, err)

	//失败重试周期
	/*var backoffSchedule = []time.Duration{
		1 * time.Second,
	}
	for _, backoff := range backoffSchedule {
		status, err := p.postData(image, tag)
		if status == http.StatusOK {
			logger.Infof("p2p preheat success, image [%s:%s].", image, tag)
			break
		}
		logger.Errorf("image [%s:%s] preheat failed: [%s],try again", image, tag, err)
		//time.Sleep(backoff)
	}*/

}

func (p *PreheatHandler) postData(image, tag string) (int, error) {

	url := p.preheatServerAddr + "/preheats"
	var requestBody bytes.Buffer
	param := make(map[string]string)
	param["image"] = image
	param["tag"] = tag
	_ = json.NewEncoder(&requestBody).Encode(param)

	req, er := http.NewRequest(http.MethodPost, url, &requestBody)
	if er != nil {
		return 500, er
	}
	req.SetBasicAuth(p.authUser, p.authPassword)
	req.Header.Set("Content-Type", "application/json")

	cli := &http.Client{}
	resp, err := cli.Do(req)
	if err != nil {
		return 500, err
	}

	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return resp.StatusCode, err
	}
	return 200, nil
}
