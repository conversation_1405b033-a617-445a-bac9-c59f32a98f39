package service

import (
	"context"
	"fmt"

	"github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/artifact"
	"github.com/goharbor/harbor/src/pkg/repository"
	"github.com/goharbor/harbor/src/pkg/tag"
	modeltag "github.com/goharbor/harbor/src/pkg/tag/model/tag"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/models"
)

type TagInterface interface {
	ListTags(ctx context.Context, projectName, repoName, tagName string, pageNo, pageSize int64) (int64, []*modeltag.Tag, error)
	CountTags(ctx context.Context, repositoryID int64) (int64, error)
	GetTagByProjectNameAndRepoNameAndTagName(ctx context.Context, params *models.GetTagByProjectNameAndRepoNameAndTagNameParam) (*modeltag.Tag, error)
}

var _ TagInterface = &TagService{}

type TagService struct {
	repoMgr     repository.Manager
	tagMgr      tag.Manager
	artifactMgr artifact.Manager
}

func NewTagService() *TagService {
	return &TagService{
		repoMgr:     repository.New(),
		tagMgr:      tag.NewManager(),
		artifactMgr: artifact.NewManager(),
	}
}

// ListTags 查询tag
func (t *TagService) ListTags(ctx context.Context, projectName, repoName, tagName string, pageNo, pageSize int64) (int64, []*modeltag.Tag, error) {
	fullRepoName := fmt.Sprintf("%s/%s", projectName, repoName)
	repo, err := t.repoMgr.GetByName(ctx, fullRepoName)
	if err != nil {
		return 0, nil, fmt.Errorf("get repo failed: %w", err)
	}

	query, err := q.Build("", "", pageNo, pageSize)
	if err != nil {
		return 0, nil, err
	}
	query.Keywords["RepositoryID"] = repo.RepositoryID
	query.Keywords["Name__contains"] = tagName

	tags, err := t.tagMgr.List(ctx, query)
	if err != nil {
		return 0, nil, fmt.Errorf("list tag failed: %w", err)
	}

	total, err := t.tagMgr.Count(ctx, query)
	if err != nil {
		return 0, nil, fmt.Errorf("count tag failed: %w", err)
	}

	return total, tags, nil

}

func (t *TagService) CountTags(ctx context.Context, repositoryID int64) (int64, error) {

	query := q.New(map[string]interface{}{"RepositoryID": repositoryID})

	total, err := t.tagMgr.Count(ctx, query)
	if err != nil {
		return 0, fmt.Errorf("count tag failed: %w", err)
	}

	return total, nil

}

// GetTagByProjectNameAndRepoNameAndTagName 通过project和repository及tag查询出该tag
func (t *TagService) GetTagByProjectNameAndRepoNameAndTagName(ctx context.Context, params *models.GetTagByProjectNameAndRepoNameAndTagNameParam) (*modeltag.Tag, error) {
	fullRepoName := fmt.Sprintf("%s/%s", params.ProjectName, params.RepoName)
	repo, err := t.repoMgr.GetByName(ctx, fullRepoName)
	if err != nil {
		return nil, fmt.Errorf("cannot get repo info: %w", err)
	}

	query := q.New(map[string]interface{}{"RepositoryID": repo.RepositoryID, "Name": params.TagName})

	tags, err := t.tagMgr.List(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("list tag failed: %w", err)
	}

	if len(tags) == 0 {
		return nil, errors.NotFoundError(fmt.Errorf("no tag found"))
	}

	if len(tags) > 1 {
		return nil, fmt.Errorf("tag is not only one")
	}

	return tags[0], nil

}
