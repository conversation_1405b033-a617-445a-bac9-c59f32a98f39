package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	mgrproject "github.com/goharbor/harbor/src/pkg/project"
	repomodel "github.com/goharbor/harbor/src/pkg/repository/model"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/service"
)

type RepositoryHandler struct {
	repositoryService service.RepositoryService
	tagService        service.TagService
	projectMgr        mgrproject.Manager
}

func NewRepositoryHandler() *RepositoryHandler {
	return &RepositoryHandler{
		repositoryService: *service.NewRepositoryService(),
		tagService:        *service.NewTagService(),
		projectMgr:        mgrproject.Mgr,
	}
}

func (h RepositoryHandler) ListRepositories(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	projectIdStr := c.Query("project_id")
	pageNoStr, pageSizeStr := c.DefaultQuery("page", "1"), c.DefaultQuery("page_size", "10")

	projectId, err := strconv.ParseInt(projectIdStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid project id %s: %s", projectIdStr, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page no %s: %s", pageNoStr, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page size %s: %s", pageSizeStr, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	filter := c.Query("q")

	total, repoRecords, err := h.repositoryService.ListRepositories(middleware.HarborContext(c), projectId, filter, pageNo, pageSize)
	if err != nil {
		logger.Errorf("list repository failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	// assemble tags count
	payload, err := h.assembleTagsCount(c, repoRecords)
	if err != nil {
		logger.Errorf("assemble tags count failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	c.Header("x-total-count", strconv.FormatInt(total, 10))
	c.JSON(http.StatusOK, payload)
}

// ListRepository 查询镜像仓库列表
// @Summary 查询镜像仓库列表
// @Description 查询镜像仓库列表
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param x-bce-request-id header string false "An unique ID for the request"
// @Param project_name_or_id path string true "命名空间名称或者ID"
// @Param repository_name query string false "镜像仓库名称"
// @Param page_no query int false "当前页" default(1)
// @Param page_size query int false "每页记录数" default(10)
// @Success 200 {array} models.Repository "Success"
// @Failure 400 {string} Bad request
// @Failure 500 {string} Internal server error
// @Header 200 {integer} x-total-count "The total count of repository"
// @Header 400 {string} x-bce-request-id "The ID of the corresponding request for the response"
// @Header 500 {string} x-bce-request-id "The ID of the corresponding request for the response"
// @Router /projects/{project_name_or_id}/repositories [get]
func (h RepositoryHandler) ListRepository(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	projectNameOrId := parseProjectNameOrID(c.Param("project_name_or_id"))
	repositoryName := c.Query("repository_name")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("page_no", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("page_size", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	p, err := h.projectMgr.Get(middleware.HarborContext(c), projectNameOrId)
	if err != nil {
		logger.Errorf("get project failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	total, repoRecords, err := h.repositoryService.ListRepositories(middleware.HarborContext(c), p.ProjectID, repositoryName, pageNo, pageSize)
	if err != nil {
		logger.Errorf("list repository failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	// assemble tags count
	payload, err := h.assembleTagsCount(c, repoRecords)
	if err != nil {
		logger.Errorf("assemble tags count failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	c.Header("x-total-count", strconv.FormatInt(total, 10))
	c.JSON(http.StatusOK, payload)
}

func (h RepositoryHandler) assembleTagsCount(c *gin.Context, repoRecords []*repomodel.RepoRecord) ([]*models.Repository, error) {
	logger := gin_context.LoggerFromContext(c)

	repositories := make([]*models.Repository, 0)
	for _, repoRecord := range repoRecords {
		tagsCount, err := h.tagService.CountTags(middleware.HarborContext(c), repoRecord.RepositoryID)
		if err != nil {
			logger.Errorf("count tags failed: %s", err)
			return nil, err
		}
		repository := models.NewRepositorySwagger(repoRecord).ToSwagger()
		repository.TagsCount = tagsCount
		repositories = append(repositories, repository)
	}
	return repositories, nil
}
