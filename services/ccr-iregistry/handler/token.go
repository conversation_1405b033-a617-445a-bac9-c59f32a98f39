package handler

import (
	"encoding/json"
	"io/ioutil"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/pkg/user"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
)

type TokenHandler struct {
	proxy   *ProxyHandler
	userMgr user.Manager

	readOnlyUsers map[string]interface{}
	tokenMaker    *token.TokenMaker
	privateKey    []byte
}

func NewTokenHandler(roUsers []string, privateKey []byte, tokenMaker *token.TokenMaker, proxy *ProxyHandler) *TokenHandler {
	readOnlyUserSet := make(map[string]interface{})
	for _, v := range roUsers {
		readOnlyUserSet[v] = nil
	}

	return &TokenHandler{
		readOnlyUsers: readOnlyUserSet,
		tokenMaker:    tokenMaker,
		privateKey:    privateKey,
		proxy:         proxy,
		userMgr:       user.Mgr,
	}
}

func (th *TokenHandler) GetServiceToken(c *gin.Context) {
	// proxy to iregistry
	th.getOriginToken(c)
	if c.IsAborted() {
		return
	}

	th.getReadOnlyToken(c)
	if c.IsAborted() {
		return
	}

	// proxy to ccr
	c.Writer.Header().Add("X-Back-Source", "ccr")
	th.proxy.Proxy(c)
}

func (th *TokenHandler) getOriginToken(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	if !th.proxy.SupportOrigin() {
		return
	}

	encoding := c.Request.Header.Get("Accept-Encoding")
	defer func() {
		if encoding != "" {
			c.Request.Header.Set("Accept-Encoding", encoding)
		}
	}()

	c.Writer.Header().Add("X-Back-Source", "iregistry")
	c.Request.Header.Del("Accept-Encoding")
	resp, err := th.proxy.ForwardRequestToOrigin(c.Request)
	if err != nil {
		logger.Errorf("Forward to iregistry failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logger.Errorf("get token from iregistry failed with status code :%d", resp.StatusCode)
		return
	}

	content, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("read from response failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	var tk token.Token
	if err = json.Unmarshal(content, &tk); err != nil {
		logger.Errorf("invalid token: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	claims, err := token.Parse(tk.Token, th.privateKey)
	if err != nil {
		logger.Errorf("parse token failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	// 只要subject不为空，则认为源能够处理，则直接返回token
	if claims.Subject != "" {
		c.AbortWithStatusJSON(http.StatusOK, tk)
		return
	}

	ras := token.GetResourceActions(c.QueryArray("scope"))

	srcAccessMap := make(map[string]*token.ResourceActions)
	for _, v := range claims.Access {
		srcAccessMap[v.Name] = v
	}

	for _, v := range ras {
		// 不满足，交给下一步处理
		if srcAccessMap[v.Name] == nil || !srcAccessMap[v.Name].Can(v) {
			return
		}
	}

	c.AbortWithStatusJSON(http.StatusOK, tk)
}

func (th *TokenHandler) getReadOnlyToken(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	username, password, isBasic := c.Request.BasicAuth()

	if !isBasic {
		return
	}

	_, isReadOnly := th.readOnlyUsers[username]
	if !isReadOnly {
		return
	}

	ras := token.GetResourceActions(c.QueryArray("scope"))
	for _, rs := range ras {
		for _, action := range rs.Actions {
			if action != "pull" {
				return
			}
		}
	}

	c.Writer.Header().Add("X-Back-Source", "readonly")
	if _, err := th.userMgr.MatchLocalPassword(middleware.HarborContext(c), username, password); err != nil {
		logger.Errorf("[MatchLocalPassword] invalid password for user %s: %s", username, err)
		c.AbortWithError(http.StatusUnauthorized, err)
		return
	}

	tk, err := th.tokenMaker.MakeToken(username, th.privateKey, ras)
	if err != nil {
		logger.Errorf("[GetServiceToken] failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.AbortWithStatusJSON(http.StatusOK, tk)
}
