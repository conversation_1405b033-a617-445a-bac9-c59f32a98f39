package handler

import (
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	harbormodels "github.com/goharbor/harbor/src/common/models"
	"github.com/golang/mock/gomock"
	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao/migrate/mock"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
)

type GinResponseWriter struct {
	http.ResponseWriter
}

func (g *GinResponseWriter) CloseNotify() <-chan bool {
	return make(chan bool)
}

func newGinResponseWriter() http.ResponseWriter {
	return &GinResponseWriter{httptest.NewRecorder()}
}

type TestTransport struct {
	resp *http.Response
}

func (t *TestTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	return t.resp, nil
}

type FuncTransport struct {
	Do func(*http.Request) (*http.Response, error)
}

func (f *FuncTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	return f.Do(req)
}

func Test_RegistryProxy(t *testing.T) {
	ctSvc, err := utils.NewHarborCsrf("this is 32 bit key used for test")
	assert.NoError(t, err)
	assert.NotNil(t, ctSvc)

	ctx, engine := gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, err = http.NewRequest(http.MethodGet, "/v2/test/t1/manifests/v1", nil)
	assert.Nil(t, err)
	assert.NotNil(t, engine)
	assert.NotNil(t, ctx)

	mockMigrate := mock.NewMockMigrateInterface(gomock.NewController(t))
	mockMigrate.EXPECT().Exist(gomock.Eq("test")).Return(true, nil)
	mockMigrate.EXPECT().Exist(gomock.Eq("notexist")).AnyTimes().Return(false, nil)
	mockMigrate.EXPECT().Exist(gomock.Eq("err")).Return(false, fmt.Errorf("wrong"))

	ph := NewProxyHandler("", "", "", "", "/test/test", true, mockMigrate, ctSvc, &config.P2pConfig{})

	ph.transport = &TestTransport{
		resp: &http.Response{
			StatusCode: 400,
			Body:       io.NopCloser(strings.NewReader("")),
		},
	}
	ph.RegistryProxy(ctx)
	assert.Equal(t, 400, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, err = http.NewRequest(http.MethodGet, "/v2/notexist/t1/manifests/v1", nil)
	ph.RegistryProxy(ctx)
	assert.Equal(t, 500, ctx.Writer.Status())

	ph.originRegistryAddr = "another"
	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, err = http.NewRequest(http.MethodGet, "/v2/notexist/t1/manifests/v1", nil)
	ph.RegistryProxy(ctx)
	assert.Equal(t, 400, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, err = http.NewRequest(http.MethodGet, "/v2/err/t1/manifests/v1", nil)
	ph.RegistryProxy(ctx)
	assert.Equal(t, 500, ctx.Writer.Status())

	originRegistryAddr := "http://127.0.0.1"
	reqPath := "/v2/eks_test/test/manifests/1001"
	ph.originRegistryAddr = originRegistryAddr
	httpmock.RegisterResponder("PUT", originRegistryAddr+reqPath, httpmock.NewStringResponder(200, string("")))
	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, err = http.NewRequest(http.MethodPut, reqPath, nil)
	ph.RegistryProxy(ctx)

	//
	ph.migrating = false
	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, err = http.NewRequest(http.MethodGet, "/v2/test/notexist/manifest/v1", nil)
	ph.RegistryProxy(ctx)
	assert.Equal(t, 400, ctx.Writer.Status())

	// header
	header := http.Header{}
	header.Set("www-authenticate", `Bearer realm="https://iregistry.baidu-int.com/service/token",service="harbor-registry"`)
	ph.transport = &TestTransport{
		resp: &http.Response{
			StatusCode: 401,
			Body:       io.NopCloser(strings.NewReader("")),
			Header:     header,
		},
	}
	ph.domainName = "test.com"
	ph.serviceName = "tt"
	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, err = http.NewRequest(http.MethodGet, "/v2/test/notexist/manifest/v1", nil)
	ph.RegistryProxy(ctx)
	assert.Equal(t, 401, ctx.Writer.Status())
	assert.Equal(t, `Bearer realm="https://test.com/test/test",service="tt"`, ctx.Writer.Header().Get("www-authenticate"))
}

func Test_Proxy(t *testing.T) {
	ctSvc, err := utils.NewHarborCsrf("this is 32 bit key used for test")
	assert.NoError(t, err)
	assert.NotNil(t, ctSvc)

	ctx, engine := gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, err = http.NewRequest(http.MethodGet, "/proxy-no-header", nil)
	assert.Nil(t, err)
	assert.NotNil(t, engine)
	assert.NotNil(t, ctx)

	ph := NewProxyHandler("", "", "", "", "", true, nil, ctSvc, &config.P2pConfig{})

	ph.transport = &FuncTransport{
		Do: func(req *http.Request) (*http.Response, error) {
			assert.Equal(t, "", req.Header.Get("x-harbor-csrf-token"))
			return &http.Response{
				StatusCode: 400,
				Body:       io.NopCloser(strings.NewReader("")),
			}, nil
		},
	}
	ph.Proxy(ctx)
	assert.Equal(t, 400, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, err = http.NewRequest(http.MethodGet, "/proxy-with-header", nil)
	ctx.Set(middleware.CCR_SECURITY_IDENTITY, &harbormodels.User{})
	ph.transport = &FuncTransport{
		Do: func(req *http.Request) (*http.Response, error) {
			assert.NotEqual(t, "", req.Header.Get("x-harbor-csrf-token"))
			return &http.Response{
				StatusCode: 400,
				Body:       io.NopCloser(strings.NewReader("")),
			}, nil
		},
	}
	ph.Proxy(ctx)
	assert.Equal(t, 400, ctx.Writer.Status())
}
