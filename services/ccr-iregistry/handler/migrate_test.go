package handler

import (
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao/migrate"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao/migrate/mock"
)

func Test_MigrateHandler(t *testing.T) {
	mh := &MigrateHandler{}

	migrateInterface := mock.NewMockMigrateInterface(gomock.NewController(t))
	migrateInterface.EXPECT().ListRecords().Return([]*migrate.NamespaceMigration{
		{ID: 1, Name: "test"},
	}, nil)
	migrateInterface.EXPECT().BatchInsert(gomock.Len(2)).Return(fmt.Errorf("wrong"))
	migrateInterface.EXPECT().BatchInsert(gomock.Len(1)).Return(nil)
	migrateInterface.EXPECT().BatchDelete(gomock.Len(2)).Return(fmt.Errorf("wrong"))
	migrateInterface.EXPECT().BatchDelete(gomock.Len(1)).Return(nil)

	mh.dao = migrateInterface

	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	mh.List(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())

	// create
	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, _ = http.NewRequest("POST", "test", io.NopCloser(strings.NewReader(`""`)))
	mh.Create(ctx)
	assert.Equal(t, 400, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, _ = http.NewRequest("POST", "test", io.NopCloser(strings.NewReader(`[]`)))
	mh.Create(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, _ = http.NewRequest("POST", "test", io.NopCloser(strings.NewReader(`["t1", "t2"]`)))
	mh.Create(ctx)
	assert.Equal(t, 500, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, _ = http.NewRequest("POST", "test", io.NopCloser(strings.NewReader(`["t1"]`)))
	mh.Create(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())

	// delete
	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, _ = http.NewRequest("DELETE", "test", io.NopCloser(strings.NewReader(`""`)))
	mh.Delete(ctx)
	assert.Equal(t, 400, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, _ = http.NewRequest("DELETE", "test", io.NopCloser(strings.NewReader(`[]`)))
	mh.Delete(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, _ = http.NewRequest("DELETE", "test", io.NopCloser(strings.NewReader(`["t1", "t2"]`)))
	mh.Delete(ctx)
	assert.Equal(t, 500, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Request, _ = http.NewRequest("DELETE", "test", io.NopCloser(strings.NewReader(`["t1"]`)))
	mh.Delete(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())
}
