package handler

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/pkg/user"
	testinguser "github.com/goharbor/harbor/src/testing/pkg/user"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
)

const (
	privateKey = `*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
)

func TestTokenHandler_GetServiceToken(t *testing.T) {
	type fields struct {
		proxy         *ProxyHandler
		userMgr       user.Manager
		readOnlyUsers []string
		tokenMaker    *token.TokenMaker
		privateKey    []byte
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		expected int
	}{
		{
			name: "proxy to origin success",
			fields: func() fields {
				tk, err := token.NewTokenMaker(30, "harbor-registry").MakeToken("test", []byte(privateKey), token.GetResourceActions([]string{
					"repository:test/test:pull",
				}))
				require.NoError(t, err)
				tkStr, err := json.Marshal(tk)
				require.NoError(t, err)
				p := NewProxyHandler("", "", "t.t", "", "/test/test", false, nil, nil, nil)
				p.transport = &TestTransport{
					resp: &http.Response{
						StatusCode: 200,
						Body:       io.NopCloser(strings.NewReader(string(tkStr))),
					},
				}
				return fields{
					proxy:         p,
					readOnlyUsers: nil,
					tokenMaker:    token.NewTokenMaker(30, "harbor-registry"),
					privateKey:    []byte(privateKey),
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test.com", nil)
				return args{
					c: ctx,
				}
			}(),
			expected: 200,
		},
		{
			name: "proxy to origin failed without username",
			fields: func() fields {
				tk, err := token.NewTokenMaker(30, "harbor-registry").MakeToken("", []byte(privateKey), token.GetResourceActions([]string{
					"repository:test/test:pull",
				}))
				require.NoError(t, err)
				tkStr, err := json.Marshal(tk)
				require.NoError(t, err)
				p := NewProxyHandler("", "", "t.t", "", "/test/test", false, nil, nil, nil)
				p.transport = &TestTransport{
					resp: &http.Response{
						StatusCode: 200,
						Body:       io.NopCloser(strings.NewReader(string(tkStr))),
					},
				}
				return fields{
					proxy:         p,
					readOnlyUsers: nil,
					tokenMaker:    token.NewTokenMaker(30, "harbor-registry"),
					privateKey:    []byte(privateKey),
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test.com?scope=repository:test/test:push", nil)
				return args{
					c: ctx,
				}
			}(),
			expected: 200,
		},
		{
			name: "user not exist",
			fields: func() fields {
				p := NewProxyHandler("", "", "", "", "/test/test", false, nil, nil, nil)
				p.transport = &TestTransport{
					resp: &http.Response{
						StatusCode: 400,
						Body:       io.NopCloser(strings.NewReader("")),
					},
				}
				return fields{
					proxy:         p,
					readOnlyUsers: nil,
					tokenMaker:    token.NewTokenMaker(30, "harbor-registry"),
					privateKey:    []byte("test"),
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test.com", nil)
				return args{
					c: ctx,
				}
			}(),
			expected: 400,
		},
		{
			name: "user exist, not in whitelist",
			fields: func() fields {
				p := NewProxyHandler("", "", "", "", "/test/test", false, nil, nil, nil)
				p.transport = &TestTransport{
					resp: &http.Response{
						StatusCode: 400,
						Body:       io.NopCloser(strings.NewReader("")),
					},
				}
				return fields{
					proxy:         p,
					readOnlyUsers: nil,
					tokenMaker:    token.NewTokenMaker(30, "harbor-registry"),
					privateKey:    []byte("test"),
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test.com", nil)
				ctx.Request.SetBasicAuth("test", "xxxx")
				return args{
					c: ctx,
				}
			}(),
			expected: 400,
		},
		{
			name: "user exist, in whitelist, but with push",
			fields: func() fields {
				p := NewProxyHandler("", "", "", "", "/test/test", false, nil, nil, nil)
				p.transport = &TestTransport{
					resp: &http.Response{
						StatusCode: 400,
						Body:       io.NopCloser(strings.NewReader("")),
					},
				}
				return fields{
					proxy:         p,
					readOnlyUsers: []string{"test"},
					tokenMaker:    token.NewTokenMaker(30, "harbor-registry"),
					privateKey:    []byte("test"),
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?service=harbor-registry&scope=repository:test/test:push,pull", nil)
				ctx.Request.SetBasicAuth("test", "xxxx")
				return args{
					c: ctx,
				}
			}(),
			expected: 400,
		},
		{
			name: "user exist, in whitelist, but password not match",
			fields: func() fields {
				p := NewProxyHandler("", "", "", "", "/test/test", false, nil, nil, nil)
				p.transport = &TestTransport{
					resp: &http.Response{
						StatusCode: 400,
						Body:       io.NopCloser(strings.NewReader("")),
					},
				}
				userMgr := &testinguser.Manager{}
				userMgr.On("MatchLocalPassword", mock.Anything, mock.Anything, mock.Anything).Return(nil, fmt.Errorf("error"))
				return fields{
					proxy:         p,
					readOnlyUsers: []string{"test"},
					tokenMaker:    token.NewTokenMaker(30, "harbor-registry"),
					privateKey:    []byte("test"),
					userMgr:       userMgr,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?service=harbor-registry&scope=repository:test/test:pull", nil)
				ctx.Request.SetBasicAuth("test", "xxxx")
				return args{
					c: ctx,
				}
			}(),
			expected: 401,
		},
		{
			name: "user exist, in whitelist, pull with invalid private key",
			fields: func() fields {
				p := NewProxyHandler("", "", "", "", "/test/test", false, nil, nil, nil)
				p.transport = &TestTransport{
					resp: &http.Response{
						StatusCode: 400,
						Body:       io.NopCloser(strings.NewReader("")),
					},
				}
				userMgr := &testinguser.Manager{}
				userMgr.On("MatchLocalPassword", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
				return fields{
					proxy:         p,
					readOnlyUsers: []string{"test"},
					tokenMaker:    token.NewTokenMaker(30, "harbor-registry"),
					privateKey:    []byte("test"),
					userMgr:       userMgr,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?service=harbor-registry&scope=repository:test/test:pull", nil)
				ctx.Request.SetBasicAuth("test", "xxxx")
				return args{
					c: ctx,
				}
			}(),
			expected: 500,
		},
		{
			name: "user exist, in whitelist, pull with success",
			fields: func() fields {
				p := NewProxyHandler("", "", "", "", "/test/test", false, nil, nil, nil)
				p.transport = &TestTransport{
					resp: &http.Response{
						StatusCode: 400,
						Body:       io.NopCloser(strings.NewReader("")),
					},
				}
				userMgr := &testinguser.Manager{}
				userMgr.On("MatchLocalPassword", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
				return fields{
					proxy:         p,
					readOnlyUsers: []string{"test"},
					tokenMaker:    token.NewTokenMaker(30, "harbor-registry"),
					privateKey:    []byte(privateKey),
					userMgr:       userMgr,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?service=harbor-registry&scope=repository:test/test:pull", nil)
				ctx.Request.SetBasicAuth("test", "xxxx")
				return args{
					c: ctx,
				}
			}(),
			expected: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			th := NewTokenHandler(tt.fields.readOnlyUsers, tt.fields.privateKey, tt.fields.tokenMaker, tt.fields.proxy)
			th.userMgr = tt.fields.userMgr
			th.GetServiceToken(tt.args.c)
			if tt.args.c.Writer.Status() != tt.expected {
				t.Errorf("expected %d but with %d", tt.expected, tt.args.c.Writer.Status())
			}
		})
	}
}
