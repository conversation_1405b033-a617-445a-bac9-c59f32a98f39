package handler

import (
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/uic"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/service"
)

func Test_HarborProject_AddMember(t *testing.T) {
	type field struct {
		ph *HarborProjectHandler
	}

	type args struct {
		ctx *gin.Context
	}

	tests := []struct {
		name   string
		filed  field
		args   args
		expect int
	}{
		{
			name: "normal",
			filed: func() field {
				ctSvc, err := utils.NewHarborCsrf("this is 32 bit key used for test")
				require.NoError(t, err)
				ph := NewProxyHandler("", "", "", "", "", true, nil, ctSvc, &config.P2pConfig{})
				ph.transport = &TestTransport{
					resp: &http.Response{
						StatusCode: 200,
						Body:       io.NopCloser(strings.NewReader("")),
					},
				}
				return field{
					ph: NewHarborProjectHandler(nil, nil, ph),
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "/v2/test/t1/manifests/v1", nil)
				return args{
					ctx: ctx,
				}
			}(),
			expect: 200,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.filed.ph.AddMember(tt.args.ctx)
			assert.Equal(t, 200, tt.args.ctx.Writer.Status())
		})
	}
}

func Test_HarborProject_GetProject(t *testing.T) {
	type field struct {
		ph *HarborProjectHandler
	}

	type args struct {
		ctx *gin.Context
	}

	tests := []struct {
		name   string
		filed  field
		args   args
		expect int
	}{
		{
			name: "normal",
			filed: func() field {
				ctSvc, err := utils.NewHarborCsrf("this is 32 bit key used for test")
				require.NoError(t, err)
				ph := NewProxyHandler("", "", "", "", "", true, nil, ctSvc, &config.P2pConfig{})
				ph.transport = &TestTransport{
					resp: &http.Response{
						StatusCode: 200,
						Body:       io.NopCloser(strings.NewReader("")),
					},
				}
				return field{
					ph: NewHarborProjectHandler(nil, nil, ph),
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "/api/v2.0/projects/10", nil)
				ctx.Params = append(ctx.Params, gin.Param{Key: "project_id", Value: "10"})
				return args{
					ctx: ctx,
				}
			}(),
			expect: 200,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.filed.ph.GetProject(tt.args.ctx)
			assert.Equal(t, 200, tt.args.ctx.Writer.Status())
		})
	}
}

func TestHarborProjectHandler_CreateProject(t *testing.T) {
	type fields struct {
		uuapService *service.BaiduUUAP
		uicCli      *uic.Client
		proxy       *ProxyHandler
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		status int
	}{
		// TODO: Add test cases.
		{
			name: "no user",
			fields: fields{
				uuapService: nil,
				uicCli:      nil,
				proxy:       nil,
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "/api/v2.0/projects/10", nil)
				return args{
					c: ctx,
				}
			}(),
			status: 401,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &HarborProjectHandler{
				uuapService: tt.fields.uuapService,
				uicCli:      tt.fields.uicCli,
				proxy:       tt.fields.proxy,
			}
			a.CreateProject(tt.args.c)
			if tt.args.c.Writer.Status() != tt.status {
				t.Errorf("CreateProject expect %d but got %d", tt.status, tt.args.c.Writer.Status())
			}
		})
	}
}
