package handler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/controller/member"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/uic"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/service"
)

const (
	LuoShuAccountId   = "luoshu_account_id"
	LuoShuAccountName = "luoshu_account_name"
)

type HarborProjectHandler struct {
	uuapService *service.BaiduUUAP
	uicCli      *uic.Client
	proxy       *ProxyHandler
}

func NewHarborProjectHandler(uuapService *service.BaiduUUAP, uicCli *uic.Client, proxy *ProxyHandler) *HarborProjectHandler {
	return &HarborProjectHandler{
		uuapService: uuapService,
		uicCli:      uicCli,
		proxy:       proxy,
	}
}

// @Router /projects/{project_id}/members [post]
func (a *HarborProjectHandler) AddMember(c *gin.Context) {
	var req member.Request

	logger := gin_context.LoggerFromContext(c)
	harborCtx := middleware.HarborContext(c)

	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid member request")
		return
	}

	if req.MemberUser.Username != "" {
		user, err := a.uuapService.SearchUser(harborCtx, req.MemberUser.Username)
		if err != nil {
			logger.Errorf("search user %s failed: %s", req.MemberUser.Username, err)
			c.AbortWithError(http.StatusNotFound, err)
			return
		}

		if err = a.uuapService.OnBoardUser(harborCtx, user); err != nil {
			logger.Errorf("save user %s to db failed: %s", req.MemberUser.Username, err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
	}

	if req.MemberGroup.GroupName != "" {
		// need to set goup type to baidu
		req.MemberGroup.GroupType = service.BaiduEmailGroupType
		grp, err := a.uuapService.SearchGroup(req.MemberGroup.GroupName)
		if err != nil {
			logger.Errorf("search group %s failed: %s", req.MemberGroup.GroupName, err)
			c.AbortWithError(http.StatusNotFound, err)
			return
		}

		if err = a.uuapService.OnBoardGroup(harborCtx, grp, ""); err != nil {
			logger.Errorf("save groupp %s to db failed: %s", req.MemberGroup.GroupName, err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
	}

	// rewrite request body, prepare to proxy to harbor
	content, err := json.Marshal(req)
	if err != nil {
		logger.Errorf("marshal member request failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.Request.Body = io.NopCloser(bytes.NewReader(content))
	c.Request.Header.Del("Content-Length")
	c.Request.Header.Set("Content-Length", fmt.Sprintf("%d", len(content)))
	c.Request.ContentLength = int64(len(content))

	a.proxy.Proxy(c)
}

func (a *HarborProjectHandler) getProjectMetadata(c *gin.Context) (*models.ProjectMetadata, error) {
	content, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		return nil, fmt.Errorf("get project metadata failed: %w", err)
	}

	var metadata models.ProjectMetadata
	err = json.Unmarshal(content, &metadata)
	if err != nil {
		return nil, fmt.Errorf("invalid metadata format: %w", err)
	}

	c.Request.Body = io.NopCloser(bytes.NewReader(content))

	return &metadata, nil
}

// /api/v2.0/projects
func (a *HarborProjectHandler) CreateProject(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	user := middleware.SecurityFromContext(c)

	username, _, isBasic := c.Request.BasicAuth()
	if !isBasic && user == nil {
		logger.Errorf("create project failed, no user provided")
		c.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	if user != nil {
		username = user.Username
	}

	metadata, err := a.getProjectMetadata(c)
	if err != nil {
		logger.Errorf("get project metadata failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid request body")
		return
	}

	resp, err := a.proxy.Forward(c)
	if err != nil {
		logger.Errorf("create project failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}
	defer resp.Body.Close()

	content, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("read response from body failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	// failed, return directly
	if resp.StatusCode < 200 || resp.StatusCode > 300 {
		logger.Errorf("create project failed with status code %d, content: %s", resp.StatusCode, string(content))
		c.Status(resp.StatusCode)
		c.Writer.Write(content)
		return
	}

	location := resp.Header.Get("location")
	if location == "" {
		logger.Errorf("no location return by harbor")
		c.AbortWithStatusJSON(http.StatusInternalServerError, "create project failed")
		return
	}

	idx := strings.LastIndex(location, "/")
	if idx == -1 {
		logger.Errorf("project id is not found in locatoin: %s", location)
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}

	projectID, err := strconv.Atoi(location[idx+1:])
	if err != nil {
		logger.Errorf("project id is not interger in locatoin: %s", location)
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}

	if err = a.uicCli.CreateOrUpdateProjectAccount(&uic.ProjectAccountRequest{
		ProjectId:   int64(projectID),
		ProjectName: metadata.ProjectName,
		AccountId:   metadata.Metadata[LuoShuAccountId],
		ModifiedBy:  username,
	}); err != nil {
		logger.Errorf("create or update project luoshu relation failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusCreated, "")
}

// /api/v2.0/projects
func (a *HarborProjectHandler) ListProject(c *gin.Context) {
	c.Request.URL.Path = "/addon/v1/projects"

	a.proxy.Proxy(c)
}

// /projects/{project_id}
func (a *HarborProjectHandler) GetProject(c *gin.Context) {
	projectID := c.Param("project_id")
	c.Request.URL.Path = fmt.Sprintf("/addon/v1/projects/%s", projectID)

	a.proxy.Proxy(c)
}
