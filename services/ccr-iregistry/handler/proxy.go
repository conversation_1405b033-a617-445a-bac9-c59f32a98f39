package handler

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"net/http/httputil"
	"strings"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao/migrate"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/service"
)

type ProxyHandler struct {
	domainName  string
	ip          string
	serviceName string
	tokenPath   string
	csrfService *utils.HarborCsrf
	migrateDao  migrate.MigrateInterface
	// iregistry harbor ip
	originRegistryAddr string

	// is in migrating
	migrating bool

	transport http.RoundTripper
	p2pConfig *config.P2pConfig
}

func NewProxyHandler(ip, domain, originAddr, serviceName, tokenPath string, migrating bool, migrateDao migrate.MigrateInterface,
	csrfService *utils.HarborCsrf, p2pConfig *config.P2pConfig) *ProxyHandler {
	if serviceName == "" {
		serviceName = "harbor-registry"
	}

	return &ProxyHandler{
		domainName:         domain,
		ip:                 ip,
		serviceName:        serviceName,
		tokenPath:          tokenPath,
		csrfService:        csrfService,
		migrateDao:         migrateDao,
		originRegistryAddr: originAddr,
		migrating:          migrating,

		transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
		p2pConfig: p2pConfig,
	}
}

func (p *ProxyHandler) SupportOrigin() bool {
	return p.originRegistryAddr != ""
}

func (p *ProxyHandler) Proxy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	var csrfToken, csrfCookie string
	var err error

	// if no user provided, proxy directly
	if middleware.SecurityFromContext(c) != nil {
		csrfToken, csrfCookie, err = p.csrfService.GetCsrfToken()
		if err != nil {
			logger.Errorf("get csrf token failed: %s", err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
	}

	reverseProxy := httputil.ReverseProxy{
		Director: func(req *http.Request) {
			p.modifyRequest(req, csrfToken, csrfCookie)
		},
		Transport: p.transport,
	}

	logger.Infof("proxy to %s", c.Request.URL.String())
	reverseProxy.ServeHTTP(c.Writer, c.Request)
}

func (p *ProxyHandler) modifyRequestWithHarbor(req *http.Request) {
	req.URL.Scheme = "https"
	req.URL.Host = p.ip
	req.Host = p.domainName
	req.Header.Set("host", p.domainName)
}

func (p *ProxyHandler) modifyRequest(req *http.Request, csrfToken, csrfCookie string) {
	p.modifyRequestWithHarbor(req)

	if csrfToken != "" {
		req.Header.Set("x-harbor-csrf-token", csrfToken)
	}

	// set _gorilla_csrf
	if csrfCookie != "" {
		req.AddCookie(&http.Cookie{
			Name:  "_gorilla_csrf",
			Value: csrfCookie,
		})
	}
}

func (p *ProxyHandler) Forward(c *gin.Context) (*http.Response, error) {
	logger := gin_context.LoggerFromContext(c)

	cli := http.Client{
		Transport: p.transport,
	}

	csrfToken, csrfCookie, err := p.csrfService.GetCsrfToken()
	if err != nil {
		logger.Errorf("get csrf token failed: %s", err)
		return nil, fmt.Errorf("get csrf token failed: %w", err)
	}

	c.Request.RequestURI = ""

	p.modifyRequest(c.Request, csrfToken, csrfCookie)
	return cli.Do(c.Request)
}

func (p *ProxyHandler) ForwardRequestToOrigin(req *http.Request) (*http.Response, error) {
	cli := http.Client{
		Transport: p.transport,
	}

	req.RequestURI = ""
	req.URL.Scheme = "http"
	req.URL.Host = p.originRegistryAddr
	return cli.Do(req)
}

func (p *ProxyHandler) RegistryProxy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	logger.Infof("pure proxy to %s", c.Request.URL.String())

	method, path := c.Request.Method, c.Request.URL.Path
	ns := p.getNamespace(c.Request.URL.Path)

	var err error
	migrated := true
	// 处于迁移中的namespace，会被设置上转发
	if p.migrating && ns != "" && p.isPullOperation(method, path) {
		migrated, err = p.migrateDao.Exist(ns)
		if err != nil {
			logger.Errorf("get migration info failed: %s", err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}

		if !migrated && p.originRegistryAddr == "" {
			logger.Errorf("no origin address provided")
			c.AbortWithStatus(http.StatusInternalServerError)
			return
		}
	}

	reverseProxy := httputil.ReverseProxy{
		Director: func(r *http.Request) {
			if migrated {
				p.modifyRequestWithHarbor(r)
			} else {
				r.URL.Host = p.originRegistryAddr
				r.URL.Scheme = "http"
			}
		},
		ModifyResponse: func(resp *http.Response) error {
			p.modifyAuthenticate(resp)
			return nil
		},
		Transport: p.transport,
	}
	reverseProxy.ServeHTTP(c.Writer, c.Request)

	if p.isPutManifests(method, path) && c.Writer.Status() == http.StatusCreated {
		// push镜像后，上传manifest成功，触发p2p预热做种
		image, tag := p.getImage(path)
		logger.Infof("put manifests success,to preheat image[%s:%s]", image, tag)
		p2p := service.NewPreheatHandler(p.p2pConfig.PreheatServerAddr, p.p2pConfig.AuthUser, p.p2pConfig.AuthPassword, p.p2pConfig.ProjectList)
		p2p.PreheatImage(c, image, tag)
	}

}

func (p *ProxyHandler) modifyAuthenticate(resp *http.Response) error {
	// Www-Authenticate: Bearer realm="http://iregistry-test.baidu-int.com/service/token",service="registry.baidu.com",scope="repository:library/test:pull"
	authStr := resp.Header.Get("www-authenticate")
	if authStr != "" {
		authStr = strings.TrimPrefix(authStr, "Bearer ")
		parts := strings.Split(authStr, ",")
		a := "Bearer "
		if len(parts) > 0 {
			a += fmt.Sprintf(`realm="https://%s%s"`, p.domainName, p.tokenPath)
		}

		if len(parts) > 1 {
			a += fmt.Sprintf(`,service="%s"`, p.serviceName)
		}

		if len(parts) > 2 {
			a += "," + parts[2]
		}
		resp.Header.Set("www-authenticate", a)
	}

	return nil
}

func (p *ProxyHandler) getNamespace(path string) string {
	// /v2/namespace/xxx or v2/namespace/xxx
	if strings.HasPrefix(path, "/") {
		path = path[1:]
	}

	parts := strings.Split(path, "/")
	if len(parts) < 2 {
		return ""
	}

	return parts[1]
}

func (p *ProxyHandler) isPullOperation(method, path string) bool {
	// GET /v2/<name>/manifests/<reference>
	// HEAD /v2/<name>/manifests/<reference>
	// GET /v2/<name>/blobs/<digest>
	idx := strings.LastIndex(path, "/")
	if idx == -1 {
		return false
	}

	subPath := path[:idx]
	idx = strings.LastIndex(subPath, "/")
	if idx == -1 {
		return false
	}

	t := subPath[idx+1:]

	method = strings.ToLower(method)
	switch method + t {
	case "getmanifests", "getblobs", "headmanifests":
		return true
	}

	return false
}

func (p *ProxyHandler) isPutManifests(method, path string) bool {
	// PUT /v2/<ns>/<repo>/manifests/<tag>
	idx := strings.LastIndex(path, "/")
	if idx == -1 {
		return false
	}

	subPath := path[:idx]
	idx = strings.LastIndex(subPath, "/")
	if idx == -1 {
		return false
	}

	t := subPath[idx+1:]
	method = strings.ToLower(method)
	switch method + t {
	case "putmanifests":
		return true
	}

	return false
}

func (p *ProxyHandler) getImage(path string) (string, string) {
	if strings.HasPrefix(path, "/") {
		path = path[1:]
	}

	// path like v2/eks_test/test/manifests/v1.0.1
	parts := strings.Split(path, "/manifests/")
	tag := parts[1]
	image := strings.Split(parts[0], "v2/")[1]

	return image, tag
}
