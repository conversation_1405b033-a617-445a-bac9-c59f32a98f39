package handler

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/pkg/project/models"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/goharbor/harbor/src/testing/pkg/project"
	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-iregistry/service"
)

func TestTagHandler_ListAllTagsV1(t *testing.T) {
	type fields struct {
		tagHandler *TagHandler
	}

	type args struct {
		ctx *gin.Context
	}

	tests := []struct {
		name       string
		fields     fields
		args       args
		statusCode int
	}{
		{
			name: "repo not found",
			fields: func() fields {
				tagServcie := service.NewMockTagInterface(gomock.NewController(t))
				tagServcie.EXPECT().ListTags(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(int64(0), nil, errors.NotFoundError(fmt.Errorf("failed")))
				return fields{
					tagHandler: &TagHandler{
						tagService: tagServcie,
					},
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{
					Key:   "repoName",
					Value: "/xxxx/xxxx/tags",
				})
				middleware.SetHarborContext(ctx, context.TODO())
				return args{
					ctx: ctx,
				}
			}(),
			statusCode: 404,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.fields.tagHandler.ListAllTagsV1(tt.args.ctx)
			if tt.args.ctx.Writer.Status() != tt.statusCode {
				t.Errorf("ListAllTagsV1 return %v, expected: %v", tt.args.ctx.Writer.Status(), tt.statusCode)
			}
		})
	}
}

func TestTagHandler_ListAllTags(t *testing.T) {
	type fields struct {
		tagHandler *TagHandler
	}

	type args struct {
		ctx *gin.Context
	}

	tests := []struct {
		name       string
		fields     fields
		args       args
		statusCode int
	}{
		{
			name: "repo not found",
			fields: func() fields {
				tagServcie := service.NewMockTagInterface(gomock.NewController(t))
				tagServcie.EXPECT().ListTags(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(int64(0), nil, errors.NotFoundError(fmt.Errorf("failed")))
				return fields{
					tagHandler: &TagHandler{
						tagService: tagServcie,
					},
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test.com?repoName=test/test", nil)
				middleware.SetHarborContext(ctx, context.TODO())
				return args{
					ctx: ctx,
				}
			}(),
			statusCode: 404,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.fields.tagHandler.ListAllTags(tt.args.ctx)
			if tt.args.ctx.Writer.Status() != tt.statusCode {
				t.Errorf("ListAllTags return %v, expected: %v", tt.args.ctx.Writer.Status(), tt.statusCode)
			}
		})
	}
}

func TestTagHandler_GetTagInfo(t *testing.T) {
	type fields struct {
		tagHandler *TagHandler
	}

	type args struct {
		ctx *gin.Context
	}

	tests := []struct {
		name       string
		fields     fields
		args       args
		statusCode int
	}{
		{
			name: "repo not found",
			fields: func() fields {
				tagServcie := service.NewMockTagInterface(gomock.NewController(t))
				tagServcie.EXPECT().GetTagByProjectNameAndRepoNameAndTagName(gomock.Any(), gomock.Any()).Return(
					nil, errors.NotFoundError(fmt.Errorf("failed")))
				return fields{
					tagHandler: &TagHandler{
						tagService: tagServcie,
					},
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{
					Key:   "tag_name",
					Value: "latest",
				})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test.com?repositoryName=test/test", nil)
				middleware.SetHarborContext(ctx, context.TODO())
				return args{
					ctx: ctx,
				}
			}(),
			statusCode: 404,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.fields.tagHandler.GetTagInfo(tt.args.ctx)
			if tt.args.ctx.Writer.Status() != tt.statusCode {
				t.Errorf("GetTagInfo return %v, expected: %v", tt.args.ctx.Writer.Status(), tt.statusCode)
			}
		})
	}
}

func TestTagHandler_ListPagedTags(t *testing.T) {
	type fields struct {
		tagHandler *TagHandler
	}

	type args struct {
		ctx *gin.Context
	}

	tests := []struct {
		name       string
		fields     fields
		args       args
		statusCode int
	}{
		{
			name: "repo not found",
			fields: func() fields {
				tagServcie := service.NewMockTagInterface(gomock.NewController(t))
				tagServcie.EXPECT().ListTags(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(int64(0), nil, errors.NotFoundError(fmt.Errorf("failed")))
				return fields{
					tagHandler: &TagHandler{
						tagService: tagServcie,
					},
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{
					Key:   "tag_name",
					Value: "latest",
				})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test.com?repositoryName=test/test", nil)
				middleware.SetHarborContext(ctx, context.TODO())
				return args{
					ctx: ctx,
				}
			}(),
			statusCode: 404,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.fields.tagHandler.ListPagedTags(tt.args.ctx)
			if tt.args.ctx.Writer.Status() != tt.statusCode {
				t.Errorf("GetTagInfo return %v, expected: %v", tt.args.ctx.Writer.Status(), tt.statusCode)
			}
		})
	}
}

func TestTagHandler_ListTagsByProjectNameAndRepoName(t *testing.T) {
	type fields struct {
		tagHandler *TagHandler
	}

	type args struct {
		ctx *gin.Context
	}

	tests := []struct {
		name       string
		fields     fields
		args       args
		statusCode int
	}{
		{
			name: "project not found",
			fields: func() fields {
				prjMgr := &project.Manager{}
				prjMgr.On("Get", mock.Anything, mock.Anything).Return(nil, errors.NotFoundError(fmt.Errorf("failed")))
				return fields{
					tagHandler: &TagHandler{
						projectMgr: prjMgr,
					},
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{
					Key:   "project_name_or_id",
					Value: "test",
				}, gin.Param{
					Key:   "repository_name",
					Value: "test/test",
				})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test.com?tag_name=latest", nil)
				middleware.SetHarborContext(ctx, context.TODO())
				return args{
					ctx: ctx,
				}
			}(),
			statusCode: 404,
		},
		{
			name: "repo not found",
			fields: func() fields {
				tagServcie := service.NewMockTagInterface(gomock.NewController(t))
				tagServcie.EXPECT().ListTags(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(int64(0), nil, errors.NotFoundError(fmt.Errorf("failed")))

				prjMgr := &project.Manager{}
				prjMgr.On("Get", mock.Anything, mock.Anything).Return(&models.Project{Name: "test"}, nil)
				return fields{
					tagHandler: &TagHandler{
						tagService: tagServcie,
						projectMgr: prjMgr,
					},
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{
					Key:   "project_name_or_id",
					Value: "test",
				}, gin.Param{
					Key:   "repository_name",
					Value: "test/test",
				})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test.com?tag_name=latest", nil)
				middleware.SetHarborContext(ctx, context.TODO())
				return args{
					ctx: ctx,
				}
			}(),
			statusCode: 404,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.fields.tagHandler.ListTagsByProjectNameAndRepoName(tt.args.ctx)
			if tt.args.ctx.Writer.Status() != tt.statusCode {
				t.Errorf("GetTagInfo return %v, expected: %v", tt.args.ctx.Writer.Status(), tt.statusCode)
			}
		})
	}
}
