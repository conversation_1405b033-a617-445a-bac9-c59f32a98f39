package handler

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/controller/project"
	mgrproject "github.com/goharbor/harbor/src/pkg/project"
	"github.com/goharbor/harbor/src/pkg/project/metadata"
	"github.com/goharbor/harbor/src/pkg/repository"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/models"
)

type ProjectHandler struct {
	repoMgr    repository.Manager
	projectMgr mgrproject.Manager
	metaMgr    metadata.Manager
}

func NewProjectHandler() *ProjectHandler {
	return &ProjectHandler{
		projectMgr: mgrproject.Mgr,
		metaMgr:    metadata.Mgr,
		repoMgr:    repository.Mgr,
	}
}

// GetProject Return specific project detail information
// @Summary Return specific project detail information
// @Description This endpoint returns specific project information by project ID.
// @Id getProjectByName
// @Tags project
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param x-bce-request-id header string false "An unique ID for the request"
// @Param project_name_or_id path string true "The name or id of the project"
// @Success 200 {object} models.Project "Return matched project information."
// @Failure 500 {string} Internal server error
// @Header 500 {string} x-bce-request-id "The ID of the corresponding request for the response"
// @Router /projects/{project_name_or_id} [get]
func (a *ProjectHandler) GetProject(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	var (
		projectNameOrID interface{}
		err             error
	)
	if c.Param("project_name_or_id") != "" {
		projectNameOrID = c.Param("project_name_or_id")
	}

	if projectNameOrID == nil && c.Query("projectId") != "" {
		projectNameOrID, err = strconv.ParseInt(c.Query("projectId"), 10, 64)
		if err != nil {
			logger.Errorf("project id is provided, but invalid: %v", err)
			c.AbortWithStatusJSON(http.StatusBadRequest, "invalid project id")
			return
		}
	}

	if projectNameOrID == nil {
		logger.Errorf("no project provided")
		c.AbortWithStatusJSON(http.StatusBadRequest, "no project info provided")
		return
	}

	p, err := a.getProject(c, projectNameOrID)
	if err != nil {
		logger.Errorf("get project failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, models.NewProjectSwagger(p).ToSwagger())
}

func (a *ProjectHandler) getProject(c *gin.Context, projectNameOrID interface{}) (*project.Project, error) {

	p, err := a.projectMgr.Get(middleware.HarborContext(c), projectNameOrID)
	if err != nil {
		return nil, fmt.Errorf("get project: %v", err)
	}

	return p, nil
}

func parseProjectNameOrID(str string) interface{} {

	v, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		// it's projectName
		return str
	}

	return v // projectID
}
