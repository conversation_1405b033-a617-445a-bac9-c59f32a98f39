package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/common/utils"
	"github.com/goharbor/harbor/src/pkg/user"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/models"
)

type UserHandler struct {
	harborCli *harbor.HarborClient
}

func (u *UserHandler) ChangePassword(c *gin.Context) {
	var (
		err error
		req models.PasswordReq
	)

	logger := gin_context.LoggerFromContext(c)

	usr := middleware.SecurityFromContext(c)

	if err = c.Bind(&req); err != nil {
		logger.Errorf("bind request failed: %s", err)
		c.AbortWithError(http.StatusBadRequest, err)
		return
	}

	if usr.Password == utils.Encrypt(req.NewPassword, usr.Salt, usr.PasswordVersion) {
		logger.Errorf("same password provided")
		c.AbortWithStatusJSON(http.StatusBadRequest, "same password")
		return
	}

	if err = user.Mgr.UpdatePassword(middleware.HarborContext(c), usr.UserID, req.NewPassword); err != nil {
		logger.Errorf("change password failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.Status(http.StatusOK)
}
