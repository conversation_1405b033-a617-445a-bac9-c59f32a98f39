package handler

import (
	"fmt"
	"io"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	liberrors "github.com/goharbor/harbor/src/lib/errors"
	mgrproject "github.com/goharbor/harbor/src/pkg/project"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/service"
)

type TagHandler struct {
	tagService      service.TagInterface
	artifactService *service.ArtifactService
	projectMgr      mgrproject.Manager
}

func NewTagHandler() *TagHandler {
	return &TagHandler{
		tagService:      service.NewTagService(),
		artifactService: service.NewArtifactService(),
		projectMgr:      mgrproject.Mgr,
	}
}

func (t *TagHandler) ListAllTagsV1(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	repoName := c.Param("repoName")
	if !strings.HasSuffix(repoName, "/tags") {
		logger.Errorf("request path is :%s", repoName)
		c.AbortWithStatus(http.StatusNotFound)
		return
	}

	repoName = strings.TrimSuffix(repoName, "/tags")
	repoName = strings.TrimPrefix(repoName, "/")

	parts := strings.SplitN(repoName, "/", 2)
	if len(parts) < 2 {
		logger.Errorf("repoName %s is invalid", repoName)
		c.AbortWithStatusJSON(http.StatusBadRequest, "repoName is invalid")
		return
	}

	// TODO: 权限
	_, payload, err := t.listAllTags(c, parts[0], parts[1], "", 0, 0)
	if err != nil {
		if liberrors.IsNotFoundErr(err) {
			c.AbortWithError(http.StatusNotFound, err)
			return
		}
		logger.Errorf("list all tags for %s failed: %s", repoName, err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.Header("x-total-count", fmt.Sprintf("%d", len(payload)))
	c.JSON(http.StatusOK, payload)
}

func (t *TagHandler) ListAllTags(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	repoName := c.Query("repoName")
	if repoName == "" {
		logger.Errorf("repoName is not set")
		c.AbortWithStatusJSON(http.StatusBadRequest, "repoName is missing")
		return
	}

	parts := strings.SplitN(repoName, "/", 2)
	if len(parts) < 2 {
		logger.Errorf("repoName %s is invalid", repoName)
		c.AbortWithStatusJSON(http.StatusBadRequest, "repoName is invalid")
		return
	}

	// TODO: 权限
	_, payload, err := t.listAllTags(c, parts[0], parts[1], "", 0, 0)
	if err != nil {
		if liberrors.IsNotFoundErr(err) {
			c.AbortWithError(http.StatusNotFound, err)
			return
		}
		logger.Errorf("list all tags for %s failed: %s", repoName, err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.Header("x-total-count", fmt.Sprintf("%d", len(payload)))
	c.JSON(http.StatusOK, payload)
}

func (t *TagHandler) listAllTags(c *gin.Context, projectName, repoName, filterTag string, pageNo, pageSize int64) (int64, []*models.Tag, error) {
	logger := gin_context.LoggerFromContext(c)
	total, tags, err := t.tagService.ListTags(middleware.HarborContext(c), projectName, repoName, filterTag, pageNo, pageSize)
	if err != nil {
		logger.Errorf("list tags by repo name and project name failed: %v", err)
		return total, nil, err
	}

	var payload []*models.Tag
	for _, tag := range tags {
		artifact, err := t.artifactService.FindArtifactByID(middleware.HarborContext(c), tag.ArtifactID)
		if err != nil {
			logger.Errorf("get artifact failed: %v", err)
			return total, nil, err
		}
		payload = append(payload, models.NewTagSwagger(tag, artifact).ToSwagger())
	}

	return total, payload, nil
}

func (t *TagHandler) GetTagInfo(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	tagName := c.Param("tag_name")
	repoName := c.Query("repositoryName")

	if tagName == "" || repoName == "" {
		logger.Errorf("tag name is %s, repo name is %s", tagName, repoName)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	parts := strings.SplitN(repoName, "/", 2)
	if len(parts) < 2 {
		logger.Errorf("repoName %s is invalid", repoName)
		c.AbortWithStatusJSON(http.StatusBadRequest, "repoName is invalid")
		return
	}

	tag, err := t.tagService.GetTagByProjectNameAndRepoNameAndTagName(
		middleware.HarborContext(c),
		&models.GetTagByProjectNameAndRepoNameAndTagNameParam{
			RequestId:   gin_context.RequestIdFromContext(c),
			ProjectName: parts[0],
			RepoName:    parts[1],
			TagName:     tagName,
		})
	if err != nil {
		if liberrors.IsNotFoundErr(err) {
			c.AbortWithError(http.StatusNotFound, err)
			return
		}
		logger.Errorf("list tags by repo name and project name failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	artifact, err := t.artifactService.FindArtifactByID(middleware.HarborContext(c), tag.ArtifactID)
	if err != nil {
		logger.Errorf("get artifact failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, models.NewTagSwagger(tag, artifact).ToSwagger())
}

func (t *TagHandler) ListPagedTags(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	repoName, filterTag := c.Query("repositoryName"), c.Query("filter_tag")
	pageNoStr, pageSizeStr := c.DefaultQuery("page", "1"), c.DefaultQuery("page_size", "10")

	if repoName == "" {
		logger.Errorf("repo name is empty")
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	parts := strings.SplitN(repoName, "/", 2)
	if len(parts) < 2 {
		logger.Errorf("repoName %s is invalid", repoName)
		c.AbortWithStatusJSON(http.StatusBadRequest, "repoName is invalid")
		return
	}

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page no %s: %s", pageNoStr, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page size %s: %s", pageSizeStr, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	// TODO: 权限
	total, payload, err := t.listAllTags(c, parts[0], parts[1], filterTag, pageNo, pageSize)
	if err != nil {
		if liberrors.IsNotFoundErr(err) {
			c.AbortWithError(http.StatusNotFound, err)
			return
		}
		logger.Errorf("list all tags for %s failed: %s", repoName, err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.Header("x-total-count", fmt.Sprintf("%d", total))
	c.JSON(http.StatusOK, payload)
}

func (t *TagHandler) DeleteImage(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	tagName := c.Param("tag_name")
	repoName := c.Query("repositoryName")
	if repoName == "" {
		logger.Errorf("repo name is empty")
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	parts := strings.SplitN(repoName, "/", 2)
	if len(parts) < 2 {
		logger.Errorf("repoName %s is invalid", repoName)
		c.AbortWithStatusJSON(http.StatusBadRequest, "repoName is invalid")
		return
	}

	deletePath := fmt.Sprintf("/api/v2.0/projects/%s/repositories/%s/artifacts/%s", parts[0], url.QueryEscape(parts[1]), tagName)

	(&httputil.ReverseProxy{
		Director: func(r *http.Request) {
			r.URL.Path = deletePath
		},
	}).ServeHTTP(c.Writer, c.Request)
}

func (t *TagHandler) Retag(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	tagName := c.Param("tag_name")
	repoName, sourceTag := c.Query("repositoryName"), c.Query("sourceTag")
	if repoName == "" {
		logger.Errorf("repo name is empty")
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	parts := strings.SplitN(repoName, "/", 2)
	if len(parts) < 2 {
		logger.Errorf("repoName %s is invalid", repoName)
		c.AbortWithStatusJSON(http.StatusBadRequest, "repoName is invalid")
		return
	}

	if sourceTag == "" {
		logger.Errorf("source tag is empty")
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid source tag")
		return
	}

	retagPath := fmt.Sprintf("/api/v2.0/projects/%s/repositories/%s/artifacts/%s/tags", parts[0], url.QueryEscape(parts[1]), sourceTag)

	(&httputil.ReverseProxy{
		Director: func(r *http.Request) {
			r.URL.Path = retagPath
			r.Header.Set("Content-Type", "application/json")
			if r.Body != nil {
				r.Body.Close()
			}
			r.Body = io.NopCloser(strings.NewReader(fmt.Sprintf(`{"name":"%s"}`, tagName)))
		},
	}).ServeHTTP(c.Writer, c.Request)
}

// ListTagsByProjectNameAndRepoName 通过project和repository查询出该repository下所有tag
// @Summary 列举一个repo下的tag
// @Description 通过project和repository查询出该repository下所有tag
// @Id listTagsByProjectAndRepo
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param x-bce-request-id header string false "An unique ID for the request"
// @Param project_name_or_id path string true "项目名字"
// @Param repository_name path string true "仓库名字"
// @Param page_no query int false "当前页" default(1)
// @Param page_size query int false "每页记录数" default(10)
// @Success 200 {array} models.Tag
// @Failure 400 {string} Bad request
// @Failure 500 {string} Internal server error
// @Header 200 {integer} x-total-count "The total count of tag"
// @Header 400 {string} x-bce-request-id "The ID of the corresponding request for the response"
// @Header 500 {string} x-bce-request-id "The ID of the corresponding request for the response"
// @Router /project/{project_name_or_id}/repositories/{repository_name}/tags [get]
func (t *TagHandler) ListTagsByProjectNameAndRepoName(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	// TODO: 通过header来判断是projectname或者id
	projectNameOrId := parseProjectNameOrID(c.Param("project_name_or_id"))
	repoName := c.Param("repository_name")

	tagName := c.Query("tag_name")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("page_no", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("page_size", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	p, err := t.projectMgr.Get(middleware.HarborContext(c), projectNameOrId)
	if err != nil {
		if liberrors.IsNotFoundErr(err) {
			c.AbortWithError(http.StatusNotFound, err)
			return
		}
		logger.Errorf("get project failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	total, tags, err := t.tagService.ListTags(middleware.HarborContext(c), p.Name, repoName, tagName, pageNo, pageSize)
	if err != nil {
		if liberrors.IsNotFoundErr(err) {
			c.AbortWithError(http.StatusNotFound, err)
			return
		}
		logger.Errorf("list tags by repo name and project name failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	var payload []*models.Tag
	for _, tag := range tags {
		artifact, err := t.artifactService.FindArtifactByID(middleware.HarborContext(c), tag.ArtifactID)
		if err != nil {
			logger.Errorf("get artifact failed: %v", err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, err)
			return
		}
		payload = append(payload, models.NewTagSwagger(tag, artifact).ToSwagger())
	}

	c.Header("x-total-count", strconv.FormatInt(total, 10))
	c.JSON(http.StatusOK, payload)
}

// GetTag 通过project和repository及tag查询出该tag
// @Summary 通过project和repository及tag查询出该tag
// @Description 通过project和repository及tag查询出该tag
// @Id getTag
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "An unique ID for the request"
// @Param project_name_or_id path string true "项目名字"
// @Param repository_name path string true "仓库名字"
// @Param tag_name path string true "标签名字"
// @Param x-bce-request-id header string false "request id"
// @Success 200 {object} models.Tag
// @Failure 400 {string} string
// @Failure 500 {string} string
// @Router /projects/{project_name_or_id}/repositories/{repository_name}/tags/{tag_name} [get]
func (t *TagHandler) GetTag(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	projectNameOrId := parseProjectNameOrID(c.Param("project_name_or_id"))
	p, err := t.projectMgr.Get(middleware.HarborContext(c), projectNameOrId)
	if err != nil {
		logger.Errorf("get project failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	repoName, tagName := c.Param("repository_name"), c.Param("tag_name")
	tag, err := t.tagService.GetTagByProjectNameAndRepoNameAndTagName(middleware.HarborContext(c), &models.GetTagByProjectNameAndRepoNameAndTagNameParam{
		RequestId:   gin_context.RequestIdFromContext(c),
		ProjectName: p.Name,
		RepoName:    repoName,
		TagName:     tagName,
	})
	if err != nil {
		logger.Errorf("list tags by repo name and project name failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	artifact, err := t.artifactService.FindArtifactByID(middleware.HarborContext(c), tag.ArtifactID)
	if err != nil {
		logger.Errorf("get artifact failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, models.NewTagSwagger(tag, artifact).ToSwagger())
}
