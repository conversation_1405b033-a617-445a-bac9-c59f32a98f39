package handler

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/systeminfo"
)

type SystemHandler struct {
	extURL    string
	harborCli *harbor.HarborClient
}

// NewSystemHandler current only has exturl
func NewSystemHandler(extURL string, harborCli *harbor.HarborClient) *SystemHandler {
	if strings.HasPrefix(extURL, "http") {
		if idx := strings.Index(extURL, "//"); idx != -1 {
			extURL = extURL[idx+2:]
		}
	}

	return &SystemHandler{
		extURL:    extURL,
		harborCli: harborCli,
	}
}

func (s *SystemHandler) GetSystemInfo(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	resp, err := s.harborCli.V2Client.Systeminfo.GetSystemInfo(systeminfo.NewGetSystemInfoParamsWithContext(c), s.harborCli.AuthInfo)
	if err != nil {
		logger.Errorf("get system info from harbor failed: %#v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	payload := resp.GetPayload()
	if payload != nil {
		payload.ExternalURL = &s.extURL
		payload.RegistryURL = &s.extURL
	}

	c.JSON(http.StatusOK, payload)
}
