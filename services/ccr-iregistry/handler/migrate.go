package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/dao/migrate"
)

type MigrateHandler struct {
	dao migrate.MigrateInterface
}

func NewMigrateHandler(db *gorm.DB) *MigrateHandler {
	return &MigrateHandler{
		dao: migrate.NewDao(db),
	}
}

func (m *MigrateHandler) List(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	nms, err := m.dao.ListRecords()
	if err != nil {
		logger.Errorf("list migrate failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	ns := make([]string, 0)
	for _, v := range nms {
		ns = append(ns, v.Name)
	}

	c.JSON(http.StatusOK, ns)
}

func (m *MigrateHandler) Create(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	var namespaces []string
	if err := c.BindJSON(&namespaces); err != nil {
		logger.Errorf("bind namespace json failed: %s", err)
		c.AbortWithError(http.StatusBadRequest, err)
		return
	}

	if len(namespaces) == 0 {
		logger.Warnf("no namespace provided")
		c.JSON(http.StatusOK, "")
		return
	}

	if err := m.dao.BatchInsert(namespaces); err != nil {
		logger.Errorf("batch insert namespace record failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, "")
}

func (m *MigrateHandler) Delete(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	var namespaces []string
	if err := c.BindJSON(&namespaces); err != nil {
		logger.Errorf("bind namespace json failed: %s", err)
		c.AbortWithError(http.StatusBadRequest, err)
		return
	}

	if len(namespaces) == 0 {
		logger.Warnf("no namespace provided")
		c.JSON(http.StatusOK, "")
		return
	}

	if err := m.dao.BatchDelete(namespaces); err != nil {
		logger.Errorf("batch delete namespace record failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, "")
}
