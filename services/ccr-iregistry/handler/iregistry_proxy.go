package handler

import (
	"crypto/tls"
	"net/http"
	"net/http/httputil"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
)

type IRegistryProxyHandler struct {
	originIregistryAddr string
}

func NewIRegistryProxyHandler(originRegistryAddr string) *IRegistryProxyHandler {
	return &IRegistryProxyHandler{
		originIregistryAddr: originRegistryAddr,
	}
}

// Proxy: send api to ip
// /iregistry/v2/.....
func (r *IRegistryProxyHandler) Proxy(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	if r.originIregistryAddr == "" {
		logger.Errorf("origin iregistry address is empty, return 404 directly")
		c.AbortWithStatus(http.StatusNotFound)
		return
	}

	reverseProxy := httputil.ReverseProxy{
		Director: func(req *http.Request) {
			req.URL.Host = r.originIregistryAddr
			// remove iregistry, use /v2/xxxx
			path := req.URL.Path[len("/iregistry"):]
			req.URL.Path = path
		},
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	logger.Infof("proxy to %s", c.Request.URL.String())
	reverseProxy.ServeHTTP(c.Writer, c.Request)
}
