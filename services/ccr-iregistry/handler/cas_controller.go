package handler

import (
	"fmt"
	"net/http"
	"net/url"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/session"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-iregistry/service"
)

type CasHandler struct {
	casEndpoint   string
	localEndpoint string
	uuapService   *service.BaiduUUAP
	sessionToken  *session.SessionToken
}

func NewCasHandler(casEnd, localEnd string, uuap *service.BaiduUUAP, sessTok *session.SessionToken) *CasHandler {
	return &CasHandler{
		casEndpoint:   casEnd,
		localEndpoint: localEnd,
		uuapService:   uuap,
		sessionToken:  sessTok,
	}
}

func (cas *CasHandler) RedirectLogin() gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)

		loginUrl, err := url.Parse(fmt.Sprintf("%s/login", cas.casEndpoint))
		if err != nil {
			logger.Errorf("invalid url %s provided: %s", cas.casEndpoint, err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}

		query := loginUrl.Query()
		query.Set("service", cas.localEndpoint+"/c/cas/callback")
		loginUrl.RawQuery = query.Encode()

		c.Redirect(http.StatusFound, loginUrl.String())
	}
}

func (cas *CasHandler) Callback() gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)

		ticket := c.Query("ticket")
		if ticket == "" {
			logger.Warnf("no ticket found")
			c.Redirect(http.StatusFound, "/")
			return
		}

		username, err := service.ValidateTicketAndGerUsername(
			cas.casEndpoint+"/serviceValidate",
			cas.localEndpoint+"/c/cas/callback",
			ticket)
		if err != nil {
			logger.Errorf("validate cas ticket and get username failed, ticket: %s, %v", ticket, err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}

		user, err := cas.uuapService.AuthenticateDevOps(middleware.HarborContext(c), username)
		if err != nil {
			logger.Errorf("search and onboard user %s failed: %s", username, err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}

		sid, err := cas.sessionToken.GetSessionToken(c, user)
		if err != nil {
			logger.Errorf("get session token failed: %s", err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}

		c.SetCookie("sid", sid, 1500, "/", cas.localEndpoint, true, true)
		c.Redirect(http.StatusFound, "/")
	}
}

func (cas *CasHandler) Logout() gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)

		logoutUrl, err := url.Parse(cas.casEndpoint + "/logout")
		if err != nil {
			logger.Errorf("invalid logout url: %s", cas.casEndpoint+"/logout")
		}
		query := logoutUrl.Query()
		query.Set("service", cas.localEndpoint)
		logoutUrl.RawQuery = query.Encode()

		c.Redirect(http.StatusFound, logoutUrl.String())
	}
}
