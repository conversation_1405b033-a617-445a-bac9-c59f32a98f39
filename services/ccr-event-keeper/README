# 编译&推送镜像仓库
参考Makefile代码，设置环境变量
export CCR_COMPONENT=ccr-event-keeper
export GO_1_18_HOME=/xxx/src/go18/go
export GO_1_18_BIN=/xxx/src/go18/go/bin/
make docker-build-and-push

# ccr调试环境：
ssh <EMAIL>
cd /home/<USER>/duzhanwei
sh ccr-login.sh  / ssh root@100.72.202.170
bdccr@123

# helm 导出yaml
helm template -f values.yaml -f values-sandbox.yaml -n ccr-system  ccr-event-keeper ./ > cek
kubectl -n ccr-event-keeper apply -f cek

# 模拟参数，调试效果
curl --location '172.16.9.200:8500/pushevent' --header 'Content-Type: text/plain' --data '{
    "resourceId":"ccr-1xxxxwmx",
    "eventType":"ImagePushSucceeded",
    "timestamp":"2023-10-22T15:03:03Z"
}'

# 调试账号
沙盒账号登录方式
https://login.bcetest.baidu.com/?redirect=https%3A%2F%2Fqasandbox.bcetest.baidu.com%2F
若报错：依次点击以下两个链接
https://wappass.qatest.baidu.com/static/touch/js/lib/mkd.js?cdnversion=1555640269771
https://passport.qatest.baidu.com/v6/ucenter?_t=1603683175

百度账号登录
用户名：<EMAIL>
密码：123qwe

沙盒所有验证码一律为「111111」（包括充值），可新建账号进行充值测试

# 效果验证
进入bcm时间中心查看推送信息