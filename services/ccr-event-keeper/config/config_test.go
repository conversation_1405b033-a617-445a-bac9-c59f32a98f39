package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewConfigFromString(t *testing.T) {
	src := `
accessKey: 095327a0b0eb42979a403cac2dcd9b60
secretKey: bf771ef9f2aa4b14b872c9f2101623fb
listenAddress: :8500
clusterId: ccr
bcmEndpoint: *************:8869
region: bj
`
	sc, err := NewConfigFromString(src)
	assert.NoError(t, err)
	assert.Equal(t, "bj", sc.Region)
}

func TestNewConfig(t *testing.T) {
	_, err := NewConfig("")
	assert.Error(t, err)
}
