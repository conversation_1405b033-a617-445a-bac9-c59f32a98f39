package config

import (
	"io/ioutil"

	"k8s.io/apimachinery/pkg/util/yaml"
)

type ServiceConfig struct {
	AccessKey     string `yaml:"accessKey,omitempty"`
	SecretKey     string `yaml:"secretKey,omitempty"`
	ListenAddress string `yaml:"listenAddress,omitempty"`
	Region        string `yaml:"region,omitempty"`
	BcmEndpoint   string `yaml:"bcmEndpoint,omitempty"`
	RunMode       string `yaml:"runMode,omitempty"`
}

func NewConfig(filepath string) (*ServiceConfig, error) {
	content, err := ioutil.ReadFile(filepath)
	if err != nil {
		return nil, err
	}

	return NewConfigFromString(string(content))
}

func NewConfigFromString(content string) (*ServiceConfig, error) {
	var serviceConfig ServiceConfig
	err := yaml.Unmarshal([]byte(content), &serviceConfig)

	if err != nil {
		return nil, err
	}

	return &serviceConfig, nil
}
