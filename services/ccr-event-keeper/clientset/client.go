package clientset

import (
	"context"
	"fmt"
	"time"

	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcm"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/listers"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-event-keeper/config"
)

type ClientSet struct {
	Conf         *config.ServiceConfig
	BcmClient    bcm.ClientInterface
	CachedClient client.Client
	ListerClient listers.ListerInterface
}

func NewClientSet(conf *config.ServiceConfig) (*ClientSet, error) {
	bcmClient, err := bcm.NewClient(conf.AccessKey, conf.Secret<PERSON>ey, conf.BcmEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create bcm client failed: %w", err)
	}

	cachedClient, err := utils.NewK8sClientWithCache(context.Background(), "", scheme, "", 30*time.Minute)
	if err != nil {
		return nil, fmt.Errorf("new cached k8s client failed: %s", err)
	}
	lister := listers.NewLister(cachedClient)

	return &ClientSet{
		Conf:         conf,
		BcmClient:    bcmClient,
		CachedClient: cachedClient,
		ListerClient: lister,
	}, nil
}
