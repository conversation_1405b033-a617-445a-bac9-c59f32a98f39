package cmd

import (
	"testing"

	"github.com/sirupsen/logrus"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-event-keeper/config"
)

func TestSetLogLevel(t *testing.T) {
	c := &config.ServiceConfig{
		RunMode: "test",
	}
	setLogLevel(c)
	v := logrus.GetLevel().String()
	if v != "info" {
		t.Errorf("setLogLevel error")
	}
}

func TestRootRun(t *testing.T) {
	defer func() {
		err := recover()
		if err == nil {
			t.Error("rootRun error")
		}
	}()
	rootRun()
}
