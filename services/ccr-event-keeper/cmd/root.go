package cmd

import (
	"fmt"

	"github.com/gin-contrib/pprof"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-event-keeper/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-event-keeper/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-event-keeper/router"
)

var (
	configFile string // ccr-event-keeper 配置文件
	runMode    string // gin 模式: debug release test
	version    string
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "ccr-event-keeper",
	Short: "ccr-event-keeper",
	Long:  `ccr-event-keeper is used to transmit harbor events to BCM`,
	// Uncomment the following line if your bare application
	// has an action associated with it:
	Example: "ccr-event-keeper --config config.yaml",
	Run: func(cmd *cobra.Command, args []string) {
		rootRun()
	},
	Version: version,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	cobra.CheckErr(rootCmd.Execute())
}

func init() {
	//cobra.OnInitialize(initConfig)

	flags := rootCmd.Flags()

	flags.StringVar(&configFile, "config", "", "ccr-event-keeper 配置文件.")
	flags.StringVar(&runMode, "run-mode", "", "run 模式: debug release.")

}

func rootRun() error {

	conf, err := config.NewConfig(configFile)
	if err != nil {
		panic(fmt.Sprintf("read config file failed: %s", err))
	}
	logrus.SetFormatter(&logrus.TextFormatter{DisableColors: true})
	logrus.Infof("config %#v", conf)
	setLogLevel(conf)

	clientSet, err := clientset.NewClientSet(conf)
	if err != nil {
		panic(err)
	}
	route := router.NewRouter(conf, clientSet)
	if conf.RunMode == "debug" {
		pprof.Register(route)
	}
	err = route.Run(conf.ListenAddress)
	if err != nil {
		panic(err)
	}
	return nil
}

func setLogLevel(conf *config.ServiceConfig) {
	if runMode != "debug" && runMode != "release" {
		runMode = conf.RunMode
	}

	if runMode == "debug" {
		logrus.SetLevel(logrus.DebugLevel)
	} else {
		logrus.SetLevel(logrus.InfoLevel)
	}
}
