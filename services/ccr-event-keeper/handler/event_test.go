package handler

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcm"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-event-keeper/config"
)

type mockBcmClient struct {
	bcm.ClientInterface
}

func (m *mockBcmClient) PushEvent(accountID string, event []bcm.Event) ([]*bcm.PushEventResponse, error) {
	ret := make([]*bcm.PushEventResponse, 0)
	ret = append(ret, &bcm.PushEventResponse{
		Code:    200,
		Message: "mes",
		EventID: "eventID",
	})
	return ret, nil
}

func mockNewEventHandler() *EventHandler {
	conf := &config.ServiceConfig{
		Region: "bj",
	}
	mockBcmClient := &mockBcmClient{}

	scheme := runtime.NewScheme()
	if err := v1alpha1.AddToScheme(scheme); err != nil {
		return nil
	}

	cli := fake.NewFakeClientWithScheme(scheme, &v1alpha1.CCR{
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{
				"name": "test",
			},
			Name: "1",
		},
		Spec: v1alpha1.CCRSpec{
			AccountID: "account-test",
		},
	}, &v1alpha1.CCR{
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{
				"name": "test",
			},
			Name: "ccr-xxxrsid",
		},
		Spec: v1alpha1.CCRSpec{
			AccountID: "account-test",
		},
	})

	return NewEventHandler(conf, mockBcmClient, cli)
}

func TestDoPushEvent(t *testing.T) {
	handler := mockNewEventHandler()
	ctx := context.WithValue(context.Background(), gin_context.REQUEST_ID_IDENTITY, "reqid")
	logger := logrus.New().WithField("k", "v")
	req := &Request{
		ResourceID:     "1",
		EventType:      "ImagePushFailed",
		Timestamp:      "1",
		InstanceID:     "1",
		ProjectName:    "1",
		RepositoryName: "",
		TagName:        "",
		Sha256:         "",
		RetCode:        "",
		PolicyName:     "",
	}
	_, err := handler.doPushEvent(ctx, logger, req)
	if err != nil {
		t.Errorf(err.Error())
	}
}

func TestNewEventId(t *testing.T) {
	os.Setenv("POD_NAME", "ccr-pod_name-xxx")
	r := NewEventId("bj", "aa")
	fmt.Println(r)
	if len(r) < 10 {
		t.Errorf("NewEventId length error")
	}
}

type GinResponseWriter struct {
	http.ResponseWriter
}

func (g *GinResponseWriter) CloseNotify() <-chan bool {
	return make(chan bool)
}

func newGinResponseWriter() http.ResponseWriter {
	return &GinResponseWriter{httptest.NewRecorder()}
}

func TestPushEvent(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "PushEvent_wrong_eventType",
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest("POST", "/pushevent", io.NopCloser(strings.NewReader(`{
					"resourceId":"ccr-xxxrsid",
					"eventType":"xxxxx"
				}`)))
				ctx.Set(gin_context.REQUEST_ID_IDENTITY, "reqid")
				ctx.Set(gin_context.LOGGER_IDENTITY, logrus.New().WithField("k", "v"))
				return args{
					c: ctx,
				}
			}(),
			want: http.StatusBadRequest,
		},
		{
			name: "PushEvent",
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Request, _ = http.NewRequest("POST", "/pushevent", io.NopCloser(strings.NewReader(`{
					"resourceId":"ccr-xxxrsid",
					"eventType":"ImagePushSucceeded"
				}`)))
				ctx.Set(gin_context.REQUEST_ID_IDENTITY, "reqid")
				ctx.Set(gin_context.LOGGER_IDENTITY, logrus.New().WithField("k", "v"))
				return args{
					c: ctx,
				}
			}(),
			want: http.StatusOK,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			handler := mockNewEventHandler()
			handler.PushEvent(tt.args.c)
			if tt.args.c.Writer.Status() != tt.want {
				t.Errorf("PushEvent() test:%s, want: %d, res_statuscode:%d", tt.name, tt.want, tt.args.c.Writer.Status())
			}
		})
	}
}
