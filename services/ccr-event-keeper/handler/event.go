package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bcm"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-event-keeper/config"
)

type EventHandler struct {
	config    *config.ServiceConfig
	bcmClient bcm.ClientInterface
	k8sCli    client.Client
}

func NewEventHandler(conf *config.ServiceConfig, bcmClient bcm.ClientInterface, cli client.Client) *EventHandler {
	handler := &EventHandler{
		config:    conf,
		bcmClient: bcmClient,
		k8sCli:    cli,
	}
	return handler
}

type Request struct {
	ResourceID     string `json:"resourceId"`
	EventType      string `json:"eventType"`
	Timestamp      string `json:"timestamp"`
	InstanceID     string `json:"instanceID"`
	ProjectName    string `json:"projectName"`
	RepositoryName string `json:"repositoryName"`
	TagName        string `json:"tagName"`
	Sha256         string `json:"sha256"`
	RetCode        string `json:"retCode"`
	PolicyName     string `json:"policyName"`
}

func (e *EventHandler) doPushEvent(ctx context.Context, logger *logrus.Entry, req *Request) ([]*bcm.PushEventResponse, error) {
	resourceID := req.ResourceID
	timestamp := req.Timestamp
	eventType := req.EventType
	// get acountID

	var ccrObj v1alpha1.CCR
	if err := e.k8sCli.Get(ctx, client.ObjectKey{
		Name: resourceID,
	}, &ccrObj); err != nil {
		return nil, fmt.Errorf("get ccr resource %s error: %v", resourceID, err)
	}

	// get eventID
	eventID := NewEventId(e.config.Region, ctx.Value(gin_context.REQUEST_ID_IDENTITY).(string))

	bcmEventType := bcm.EventType(eventType)
	eventDetail, err := bcm.GetEventTypeInfo(bcmEventType, req.InstanceID, req.ProjectName,
		req.RepositoryName, req.TagName, req.Sha256, req.PolicyName, req.RetCode)
	if err != nil {
		return nil, fmt.Errorf("GetEventTypeInfo error: %v", err)
	}
	// get content
	strEventContent, err := json.Marshal(eventDetail.EventContent)
	if err != nil {
		return nil, fmt.Errorf("EventContent json.Marshal error: %v", err)
	}
	event := bcm.Event{
		Region:       e.config.Region,
		ResourceID:   resourceID,
		ResourceType: bcm.ResourceTypeInstance,
		ResourceName: ccrObj.Labels["name"],
		Timestamp:    timestamp,
		EventID:      eventID,
		EventType:    bcmEventType,
		EventLevel:   eventDetail.EventLevel,
		EventAlias:   eventDetail.EventAlias,
		EventAliasEn: eventDetail.EventAliasEn,
		Content:      string(strEventContent),
	}
	byteJsonEvent, err := json.Marshal(event)
	strJsonEvent := string(byteJsonEvent)
	if err != nil {
		return nil, fmt.Errorf("marshal event error: %s", err)
	}
	logger.WithFields(logrus.Fields{
		"eventdata": strJsonEvent,
		"acountid":  ccrObj.Spec.AccountID,
	}).Info()
	events := []bcm.Event{event}
	res, err := e.bcmClient.PushEvent(ccrObj.Spec.AccountID, events)
	if err != nil {
		return nil, fmt.Errorf("bcm pushevent err: %v", err)
	}
	return res, nil
}

func (e *EventHandler) PushEvent(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)

	var req Request
	err := c.BindJSON(&req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"bind_request_error:": err,
		}).Error()
		c.JSON(http.StatusBadRequest, fmt.Errorf("bind request error: %v", err))
		return
	}
	res, err := e.doPushEvent(c, logger, &req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"doPushEvent_error": err,
		}).Error()
		c.JSON(http.StatusBadRequest, err)
		return
	}
	byteJsonRes, err := json.Marshal(res)
	strJsonRes := string(byteJsonRes)
	logger.WithFields(logrus.Fields{
		"bcm_pushevent_res": strJsonRes,
	}).Info()
	if err != nil {
		logger.WithFields(logrus.Fields{
			"marshal_event_error": err,
		}).Error()
		c.JSON(http.StatusBadRequest, fmt.Errorf("marshal event error: %v", err))
		return
	}
	c.JSON(http.StatusOK, res)
	return
}

func NewEventId(region string, reqID string) string {
	currentTime := time.Now().Unix()
	podName := os.Getenv("POD_NAME")
	podNameArr := strings.Split(podName, "-")
	podNameArrLen := len(podNameArr)
	podNameSuffix := ""
	if podNameArrLen > 0 {
		podNameSuffix = podNameArr[podNameArrLen-1]
	}
	return fmt.Sprintf("%s%d%s-%s", region, currentTime, podNameSuffix, reqID)
}
