package router

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/ginprom"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-auth/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-event-keeper/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-event-keeper/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-event-keeper/handler"
)

func NewRouter(conf *config.ServiceConfig, clientset *clientset.ClientSet) *gin.Engine {
	r := gin.Default()
	r.Use(middleware.LoggerMiddleware())

	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		if err := v.RegisterValidation("password", model.PasswordValidate); err != nil {
			return nil
		}
	}

	r.GET("/ping", func(c *gin.Context) {
		c.JSON(http.StatusOK, "pong")
	})
	eventHandler := handler.NewEventHandler(conf, clientset.BcmClient, clientset.CachedClient)
	r.POST("/pushevent", eventHandler.PushEvent)
	r.GET("/healthz", func(c *gin.Context) {
		c.String(http.StatusOK, "ok")
	})

	r.GET("/metrics", ginprom.PromHanlder)

	return r
}
