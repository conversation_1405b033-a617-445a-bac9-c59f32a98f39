package middleware

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	common_http "github.com/goharbor/harbor/src/common/http"
	"github.com/goharbor/harbor/src/common/models"
	harborerrros "github.com/goharbor/harbor/src/lib/errors"
	mgrproject "github.com/goharbor/harbor/src/pkg/project"
	"github.com/goharbor/harbor/src/pkg/project/metadata"

	pkg_token "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/auth"
)

func ChartMuseumAuthMiddleware(authorizer auth.Interface, privateKey []byte,
	projectMgr mgrproject.Manager, metaMgr metadata.Manager, authPasson bool, isPublic bool) gin.HandlerFunc {
	if !isPublic {
		return func(c *gin.Context) {}
	}

	return func(c *gin.Context) {
		logger := LoggerFromContext(c)

		// check project exists
		repo := c.Param("repo")

		// public namespaces allow all get operator
		if repo != "" && c.Request.Method == http.MethodGet {

			// 替换成调用包
			p, err := projectMgr.Get(HarborContext(c), repo)
			if err != nil {
				logger.Errorf("get project : %v", err)
				t, ok := err.(*harborerrros.Error)
				if ok {
					switch t.Code {
					case harborerrros.NotFoundCode:
						c.AbortWithError(http.StatusNotFound, err)
						return
					}
				}
				c.AbortWithError(http.StatusInternalServerError, err)
				return
			}

			meta, err := metaMgr.Get(HarborContext(c), p.ProjectID)
			if err != nil {
				logger.Errorf("get project : %v", err)
				c.AbortWithError(http.StatusInternalServerError, err)
				return
			}
			p.Metadata = meta
			if p.IsPublic() {
				logger.Debugf("the project: %s is public", p.Name)
				return
			}
		}

		username, password, ok := c.Request.BasicAuth()
		if !ok {
			return
		}

		// if repo is not provided, required admin permission
		if repo == "" {
			repo = "catalog"
		}
		// repo in chartmuseum corresponds to the project in harbor and the namespace in the frontend
		var action = "pull"
		if c.Request.Method == http.MethodPost {
			action = "push"
		}

		// the resource name should be in "namespace/xxx" format
		reqResourceName := repo + "/*"
		resActions := &pkg_token.ResourceActions{
			Type:    "chartmuseum",
			Name:    reqResourceName,
			Actions: []string{action},
		}

		authPass, err := authorizer.Validate(username, password, []*pkg_token.ResourceActions{resActions}, privateKey, true)
		if (err != nil || !authPass) && authPasson {
			logger.Warnf("get token from authorizer failed: pass %v, err %v, will pass request down", authPass, err)
			return
		}

		if err != nil {
			logger.Errorf("get token failed: %s", err)
			commonError, ok := err.(*common_http.Error)
			if !ok {
				c.AbortWithError(http.StatusInternalServerError, err)
				return
			}
			c.AbortWithError(commonError.Code, err)
			return
		}

		if !authPass {
			logger.Error("failed to match access on")
			c.AbortWithError(http.StatusUnauthorized, fmt.Errorf("no push/pull chart permission"))
			return
		}

		c.Request.Header.Del("Authorization")
		// x-harbor-csrf-token is auth header
		c.Request.Header.Del("x-harbor-csrf-token")
		c.Request.Header.Del("cookie")

		c.Set(CCR_SECURITY_IDENTITY, &models.User{
			Username:        username,
			SysAdminFlag:    true,
			AdminRoleInAuth: true,
		})
	}

}
