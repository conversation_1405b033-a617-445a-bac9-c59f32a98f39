package middleware

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"k8s.io/apimachinery/pkg/util/uuid"
)

const (
	REQID_HEADER        = "X-Request-Id"
	LOGGER_IDENTITY     = "LOGGER"
	REQUEST_ID_IDENTITY = "REQUEST_ID"
)

func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		reqid := c.Request.Header.Get(REQID_HEADER)
		if reqid == "" {
			reqid = string(uuid.NewUUID())
			c.Request.Header.Set(REQID_HEADER, reqid)
		}
		c.Set(REQUEST_ID_IDENTITY, reqid)
		// Set request id into response header
		c.Writer.Header().Set(REQID_HEADER, reqid)

		logger := logrus.WithFields(logrus.Fields{
			"request-id": reqid,
		})
		c.Set(LOGGER_IDENTITY, logger)

		entry := logger.WithFields(logrus.Fields{
			"path":       c.Request.URL.String(),
			"method":     c.Request.Method,
			"ip":         c.ClientIP(),
			"user-agent": c.Request.UserAgent(),
		})
		entry.Info()

		c.Next()

		end := time.Now()
		latency := end.Sub(start)

		entry = entry.WithFields(logrus.Fields{
			"status":  c.Writer.Status(),
			"size":    c.Writer.Size(),
			"latency": latency,
		})

		if len(c.Errors) > 0 {
			entry.Error(c.Errors.String())
		} else {
			entry.Info()
		}
	}
}

func LoggerFromContext(ctx context.Context) *logrus.Entry {
	logger, ok := ctx.Value(LOGGER_IDENTITY).(*logrus.Entry)
	if !ok || logger == nil {
		return logrus.WithContext(ctx)
	}

	return logger
}

func RequestIdFromContext(ctx context.Context) string {
	if requestId, ok := ctx.Value(REQUEST_ID_IDENTITY).(string); ok {
		return requestId
	}
	return ""
}
