package middleware

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/common/models"
	"github.com/goharbor/harbor/src/common/rbac"
	rbac_project "github.com/goharbor/harbor/src/common/rbac/project"
	"github.com/goharbor/harbor/src/common/security"
	"github.com/goharbor/harbor/src/common/security/local"
	"github.com/goharbor/harbor/src/common/utils"
	"github.com/goharbor/harbor/src/controller/project"
)

const (
	CCR_SECURITY_IDENTITY = "ccr-user"
)

func SecurityMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := LoggerFromContext(c)

		user := UserFromContext(c)
		if user == nil {
			logger.Errorf("no user provided")
			c.AbortWithStatusJSON(http.StatusUnauthorized, "no user")
			return
		}

		SetHarborContext(c, security.NewContext(HarborContext(c), local.NewSecurityContext(user)))
	}
}

func UserFromContext(c context.Context) *models.User {
	if ctx, ok := c.Value(CCR_SECURITY_IDENTITY).(*models.User); ok {
		return ctx
	}
	return nil
}

func hasPermission(c *gin.Context, action rbac.Action, resource rbac.Resource) bool {
	logger := LoggerFromContext(c)
	harborCtx := HarborContext(c)

	s, ok := security.FromContext(harborCtx)
	if !ok {
		logger.Warningf("security not found in the context")
		return false
	}

	return s.Can(harborCtx, action, resource)
}

func HasProjectPermission(c *gin.Context, projectIDOrName interface{}, action rbac.Action, subresource ...rbac.Resource) bool {
	logger := LoggerFromContext(c)
	harborCtx := HarborContext(c)

	projectID, projectName, err := utils.ParseProjectIDOrName(projectIDOrName)
	if err != nil {
		return false
	}

	if projectName != "" {
		p, err := project.Ctl.GetByName(harborCtx, projectName)
		if err != nil {
			logger.Errorf("failed to get project %s: %v", projectName, err)
			return false
		}
		if p == nil {
			logger.Warningf("project %s not found", projectName)
			return false
		}

		projectID = p.ProjectID
	}

	resource := rbac_project.NewNamespace(projectID).Resource(subresource...)

	return hasPermission(c, action, resource)
}
