package middleware

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/pkg/user"
)

func BasicAuthMiddleware(mgr user.Manager) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := LoggerFromContext(c)
		username, passwd, ok := c.Request.BasicAuth()
		if !ok {
			return
		}

		// not only admin
		// if username != "admin" || passwd != os.Getenv("HARBOR_ADMIN_PASSWORD") {
		// 	c.AbortWithError(http.StatusUnauthorized, fmt.Errorf("no basic auth provided"))
		// 	return
		// }

		user, err := mgr.MatchLocalPassword(HarborContext(c), username, passwd)

		if err != nil {
			logger.Errorf("failed to authenticate %s: %v", username, err)
			c.AbortWithError(http.StatusUnauthorized, fmt.Errorf("failed to authenticate %s: %v", username, err))
			return
		}
		if user == nil {
			c.AbortWithError(http.StatusUnauthorized, fmt.Errorf("invalid credentials"))
			return
		}
		c.Set(CCR_SECURITY_IDENTITY, user)
	}
}

func RequireAdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		usr := UserFromContext(c)
		if usr == nil || !usr.SysAdminFlag {
			c.AbortWithStatus(http.StatusUnauthorized)
		}
	}
}
