package middleware

import (
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	mgrproject "github.com/goharbor/harbor/src/pkg/project"
	"github.com/goharbor/harbor/src/pkg/project/metadata"
	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/auth"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
	mockauth "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/harbor-addon/auth"
)

func TestChartMuseumAuthMiddleware(t *testing.T) {
	type args struct {
		authorizer auth.Interface
		privateKey []byte
		projectMgr mgrproject.Manager
		metaMgr    metadata.Manager
		authPasson bool
		method     string
		username   string
		isPublic   bool
	}
	tests := []struct {
		name string
		args args
		want gin.HandlerFunc
	}{
		// TODO: Add test cases.
		{
			name: "auth passon with validate failed and post method",
			args: func() args {
				authInterface := mockauth.NewMockInterface(gomock.NewController(t))
				authInterface.EXPECT().Validate(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				return args{
					authorizer: authInterface,
					privateKey: []byte("privateKey"),
					authPasson: true,
					method:     "POST",
					username:   "username",
					isPublic:   true,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ChartMuseumAuthMiddleware(tt.args.authorizer, tt.args.privateKey, tt.args.projectMgr,
				tt.args.metaMgr, tt.args.authPasson, tt.args.isPublic)
			ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
			ctx.Request, _ = http.NewRequest(tt.args.method, "http://test.com", nil)
			if tt.args.username != "" {
				ctx.Request.SetBasicAuth(tt.args.username, "password")
			}
			got(ctx)
			t.Log(ctx.Writer.Status())
		})
	}
}
