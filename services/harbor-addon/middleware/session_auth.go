package middleware

import (
	"encoding/gob"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/goharbor/harbor/src/common/models"
)

// Handle ...
func SessionAuth(redisCli redis.UniversalClient) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := LoggerFromContext(c)
		// if user is set, return directly
		if UserFromContext(c) != nil {
			return
		}

		sid := ""
		for _, v := range c.Request.Cookies() {
			if v.Name == "sid" {
				sid = v.Value
			}
		}

		// TODO: validate csrf token
		if sid == "" {
			return
		}

		realToken, err := redisCli.Get(c, sid).Result()
		if err != nil {
			logger.Errorf("get token from redis failed: %s", err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}

		var kv map[interface{}]interface{}
		if err = gob.NewDecoder(strings.NewReader(realToken)).Decode(&kv); err != nil {
			logger.Errorf("session is not in user format")
			c.AbortWithStatusJSON(http.StatusUnauthorized, "invalid user")
			return
		}

		if user, ok := kv["user"].(models.User); ok {
			c.Set(CCR_SECURITY_IDENTITY, &user)
		}
	}
}
