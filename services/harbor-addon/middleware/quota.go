package middleware

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"sigs.k8s.io/controller-runtime/pkg/client"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
)

const (
	CCR_QUOTA_IDENTITY = "ccr-quota"
)

func QuotaMiddleware(namespace string, cli client.Client, isPublic bool) gin.HandlerFunc {
	if isPublic {
		return func(c *gin.Context) {
			logger := LoggerFromContext(c)
			var quotaObj ccrv1alpha1.CCRQuota
			err := cli.Get(c, client.ObjectKey{Namespace: namespace, Name: namespace}, &quotaObj)
			if apierrors.IsNotFound(err) {
				return
			}
			if err != nil {
				logger.Errorf("get quota object failed: %s", err)
				c.AbortWithError(http.StatusInternalServerError, err)
				return
			}

			c.Set(CCR_QUOTA_IDENTITY, &quotaObj)
		}
	}

	return func(c *gin.Context) {}
}

func QuotaFromContext(c context.Context) *ccrv1alpha1.CCRQuota {
	if ctx, ok := c.Value(CCR_QUOTA_IDENTITY).(*ccrv1alpha1.CCRQuota); ok {
		return ctx
	}

	return nil
}
