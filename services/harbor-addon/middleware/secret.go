package middleware

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	CCR_SECRET_KEY_IDENTITY = "ccr-secret-key"
)

func SecretMiddleware(namespace string, cli client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := LoggerFromContext(c)

		var secret v1.Secret
		err := cli.Get(c, client.ObjectKey{Namespace: namespace, Name: fmt.Sprintf("%s%s", namespace, "-harbor-core")}, &secret)
		if apierrors.IsNotFound(err) {
			return
		}
		if err != nil {
			logger.Errorf("get secret failed: %s", err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}

		c.Set(CCR_SECRET_KEY_IDENTITY, &secret)
	}
}

func SecretFromContext(c context.Context) *v1.Secret {
	if ctx, ok := c.Value(CCR_SECRET_KEY_IDENTITY).(*v1.Secret); ok {
		return ctx
	}
	return nil
}
