package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/common/models"
	"gotest.tools/assert"
)

type GinResponseWriter struct {
	http.ResponseWriter
}

func (g *GinResponseWriter) CloseNotify() <-chan bool {
	return make(chan bool)
}

func newGinResponseWriter() http.ResponseWriter {
	return &GinResponseWriter{httptest.NewRecorder()}
}

func TestRequireAdminMiddleware(t *testing.T) {

	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	ctx.Set(CCR_SECURITY_IDENTITY, &models.User{
		SysAdminFlag: true,
	})

	RequireAdminMiddleware()
	assert.Equal(t, 200, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Set(CCR_SECURITY_IDENTITY, &models.User{
		SysAdminFlag: false,
	})

	RequireAdminMiddleware()

	assert.Equal(t, 200, ctx.Writer.Status())
}
