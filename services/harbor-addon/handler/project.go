package handler

import (
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/common"
	"github.com/goharbor/harbor/src/common/rbac"
	"github.com/goharbor/harbor/src/common/security"
	"github.com/goharbor/harbor/src/common/security/local"
	"github.com/goharbor/harbor/src/controller/project"
	"github.com/goharbor/harbor/src/controller/repository"
	"github.com/goharbor/harbor/src/lib"
	"github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/lib/q"
	mgrproject "github.com/goharbor/harbor/src/pkg/project"
	"github.com/goharbor/harbor/src/pkg/project/metadata"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/service"
)

type ProjectHandler struct {
	chartService  *service.ChartService
	projectCtl    project.Controller
	repositoryCtl repository.Controller
	projectMgr    mgrproject.Manager
	metaMgr       metadata.Manager
}

func NewProjectHandler(harborAddr string, db *gorm.DB) *ProjectHandler {
	return &ProjectHandler{
		projectMgr:    mgrproject.Mgr,
		metaMgr:       metadata.Mgr,
		repositoryCtl: repository.Ctl,
		projectCtl:    project.Ctl,
		chartService:  service.NewChartService(db, harborAddr),
	}
}

// GetProjectOverview Return specific project overview information
// @Summary Return specific project overview information
// @Description This endpoint returns overview project information by project ID.
// @Id getProjectOverview
// @Tags project
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "An unique ID for the request"
// @Param X-Is-Resource-Name header boolean false "If the resource name is set, the resource name will be used"
// @Param project_name path string true "The name or id of the project"
// @Success 200 {object} models.ProjectOverview "Return matched project information."
// @Failure 401 {string} Unauthorized
// @Failure 500 {string} Internal server error
// @Header 401 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /projects/{project_name}/overview [get]
func (a *ProjectHandler) GetProjectOverview(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	projectIdOrName := a.getProjectIDOrName(c)
	if !middleware.HasProjectPermission(c, projectIdOrName, rbac.ActionRead) {
		logger.Errorf("user has no permission to read project: %s", projectIdOrName)
		c.AbortWithStatusJSON(http.StatusUnauthorized, "has no permission")
		return
	}

	p, err := a.getProjectOverview(c, projectIdOrName)
	if err != nil {
		logger.Errorf("get project overview failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, models.NewProjectOverviewSwagger(p).ToSwagger())
}

func (a *ProjectHandler) getProjectOverview(c *gin.Context, projectNameOrID interface{}) (*project.Project, error) {
	p, err := a.projectMgr.Get(middleware.HarborContext(c), projectNameOrID)
	if err != nil {
		return nil, fmt.Errorf("get project: %v", err)
	}

	meta, err := a.metaMgr.Get(middleware.HarborContext(c), p.ProjectID)
	if err != nil {
		return nil, fmt.Errorf("get project meta: %v", err)
	}
	p.Metadata = meta

	return p, nil
}

// GetProject Return specific project detail information
// @Summary Return specific project detail information
// @Description This endpoint returns specific project information by project ID.
// @Id getProject
// @Tags project
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "An unique ID for the request"
// @Param X-Is-Resource-Name header boolean false "If the resource name is set, the resource name will be used"
// @Param project_name path string true "The name or id of the project"
// @Success 200 {object} models.ProjectDetail "Return matched project information."
// @Failure 401 {string} Unauthorized
// @Failure 500 {string} Internal server error
// @Header 401 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /projects/{project_name} [get]
func (a *ProjectHandler) GetProject(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	projectIdOrName := a.getProjectIDOrName(c)
	if !middleware.HasProjectPermission(c, projectIdOrName, rbac.ActionRead) {
		logger.Errorf("user has no permission to read project: %s", projectIdOrName)
		c.AbortWithStatusJSON(http.StatusUnauthorized, "has no permission")
		return
	}

	p, err := a.getProject(c, projectIdOrName, project.WithCVEAllowlist(), project.WithOwner())
	if err != nil {
		logger.Errorf("get project failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, models.NewProjectSwagger(p).ToSwagger())
}

func (a *ProjectHandler) getProject(c *gin.Context, projectNameOrID interface{}, options ...project.Option) (*project.Project, error) {

	p, err := a.projectCtl.Get(middleware.HarborContext(c), projectNameOrID, options...)
	if err != nil {
		return nil, fmt.Errorf("get project: %v", err)
	}

	if err := a.populateProperties(c, p); err != nil {
		return nil, fmt.Errorf("populate project properties %v", err)
	}
	return p, nil
}

func (a *ProjectHandler) getProjectIDOrName(c *gin.Context) interface{} {
	idOrName := c.Param("project_name")
	if c.GetHeader("X-Is-Resource-Name") != "true" {
		if id, err := strconv.ParseInt(idOrName, 10, 64); err == nil {
			return id
		}
	}

	return idOrName
}

func (a *ProjectHandler) populateProperties(c *gin.Context, p *project.Project) error {
	if user := middleware.UserFromContext(c); user != nil {
		roles, err := a.projectCtl.ListRoles(middleware.HarborContext(c), p.ProjectID, user)
		if err != nil {
			return err
		}
		p.RoleList = roles
		p.Role = highestRole(roles)
	}

	total, err := a.repositoryCtl.Count(middleware.HarborContext(c), q.New(q.KeyWords{"project_id": p.ProjectID}))
	if err != nil {
		return err
	}
	p.RepoCount = total

	// Populate chart count property
	// 替换成从数据库中查询
	count, err := a.chartService.Count(middleware.HarborContext(c), p.Name)
	if err != nil {
		err = errors.Wrap(err, fmt.Sprintf("get chart count of project %d failed", p.ProjectID))
		return err
	}

	p.ChartCount = uint64(count)

	return nil
}

// ListProjects List projects
// @Summary List projects
// @Description This endpoint returns projects created by Harbor.
// @Id listProjects
// @Tags project
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "An unique ID for the request"
// @Param page query int false "The page number" default(1)
// @Param page_size query int false "The size of per page" default(10)
// @Param name query string false "The name of project"
// @Param public query boolean false "The project is public or private"  Format(int32)
// @Param owner query string false "The name of project owner"
// @Param with_detail query boolean false "Bool value indicating whether return detailed information of the project" default(true)
// @Success 200 {array} models.ProjectDetail "Return all matched projects."
// @Failure 401 {string} Unauthorized
// @Failure 500 {string} Internal server error
// @Header 200 {integer} X-Total-Count "The total count of projects"
// @Header 200 {string} Link "Link refers to the previous page and next page"
// @Header 401 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /projects [get]
func (a *ProjectHandler) ListProjects(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	pageNoStr, pageSizeStr := c.DefaultQuery("page", "1"), c.DefaultQuery("page_size", "10")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page number: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page size : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	query, err := q.Build(c.Query("q"), "name", pageNo, pageSize)
	if err != nil {
		logger.Errorf("build query failed : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if c.Query("name") != "" {
		query.Keywords["name"] = &q.FuzzyMatchValue{Value: c.Query("name")}
	}
	if c.Query("owner") != "" {
		query.Keywords["owner"] = c.Query("owner")
	}
	if c.Query("public") != "" {
		query.Keywords["public"] = lib.ToBool(c.Query("public"))
	}

	harborCtx := middleware.HarborContext(c)
	secCtx, ok := security.FromContext(harborCtx)

	// anonymous has been blocked outside
	if ok && secCtx.IsAuthenticated() {
		if !secCtx.IsSysAdmin() {
			// authenticated but not system admin or solution user,
			// return public projects and projects that the user is member of
			if l, ok := secCtx.(*local.SecurityContext); ok {
				currentUser := l.User()
				member := &project.MemberQuery{
					UserID:   currentUser.UserID,
					GroupIDs: currentUser.GroupIDs,
				}

				// not filter by public or filter by the public with true,
				// so also return public projects for the member
				if public, ok := query.Keywords["public"]; !ok || lib.ToBool(public) {
					member.WithPublic = true
				}

				query.Keywords["member"] = member
			} else {
				// can't get the user info, force to return public projects
				query.Keywords["public"] = true
			}
		}
	}

	total, err := a.projectCtl.Count(middleware.HarborContext(c), query)
	if err != nil {
		logger.Errorf("count project size failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	if total == 0 {
		// no projects found for the query return directly
		c.Header("x-total-count", "0")
		c.JSON(http.StatusOK, []*models.ProjectDetail{})
		return
	}
	withDetail := lib.ToBool(c.DefaultQuery("with_detail", "true"))

	projects, err := a.projectCtl.List(middleware.HarborContext(c), query, project.Detail(withDetail), project.WithCVEAllowlist(), project.WithOwner())
	if err != nil {
		logger.Errorf("list project failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	var wg sync.WaitGroup
	for _, p := range projects {
		wg.Add(1)
		go func(p *project.Project) {
			defer wg.Done()
			// due to the issue https://github.com/lib/pq/issues/81 of lib/pg or postgres,
			// simultaneous queries in transaction may failed, so clone a ctx with new ormer here
			if err := a.populateProperties(c, p); err != nil {
				logger.Warningf("failed to populate propertites for project %s, error: %v", p.Name, err)
			}
		}(p)
	}
	wg.Wait()

	var payload []*models.ProjectDetail
	for _, p := range projects {
		payload = append(payload, models.NewProjectSwagger(p).ToSwagger())
	}

	c.Header("x-total-count", strconv.FormatInt(total, 10))
	c.Header("Link", a.Links(c, c.Request.URL, total, query.PageNumber, query.PageSize).String())
	c.JSON(http.StatusOK, payload)
}

// Returns the highest role in the role list.
// This func should be removed once we deprecate the "current_user_role_id" in project API
// A user can have multiple roles and they may not have a strict ranking relationship
func highestRole(roles []int) int {
	if roles == nil {
		return 0
	}
	rolePower := map[int]int{
		common.RoleProjectAdmin: 50,
		common.RoleMaintainer:   40,
		common.RoleDeveloper:    30,
		common.RoleGuest:        20,
		common.RoleLimitedGuest: 10,
	}
	var highest, highestPower int
	for _, role := range roles {
		if p, ok := rolePower[role]; ok && p > highestPower {
			highest = role
			highestPower = p
		}
	}
	return highest
}

// Links return Links based on the provided pagination information
func (b *ProjectHandler) Links(c *gin.Context, u *url.URL, total, pageNumber, pageSize int64) lib.Links {
	logger := middleware.LoggerFromContext(c)
	var links lib.Links
	if pageSize == 0 {
		return links
	}
	ul := *u
	// prev
	if pageNumber > 1 && (pageNumber-1)*pageSize < total {
		q := ul.Query()
		q.Set("page", strconv.FormatInt(pageNumber-1, 10))
		// the URL may contain no "page_size", in this case the pageSize in the query is set by
		// the go-swagger automatically
		q.Set("page_size", strconv.FormatInt(pageSize, 10))
		ul.RawQuery = q.Encode()
		// try to unescape the query
		if escapedQuery, err := url.QueryUnescape(ul.RawQuery); err == nil {
			ul.RawQuery = escapedQuery
		} else {
			logger.Errorf("failed to unescape the query %s: %v", ul.RawQuery, err)
		}
		link := &lib.Link{
			URL: ul.String(),
			Rel: "prev",
		}
		links = append(links, link)
	}
	// next
	if pageSize*pageNumber < total {
		q := ul.Query()
		q.Set("page", strconv.FormatInt(pageNumber+1, 10))
		q.Set("page_size", strconv.FormatInt(pageSize, 10))
		ul.RawQuery = q.Encode()
		// try to unescape the query
		if escapedQuery, err := url.QueryUnescape(ul.RawQuery); err == nil {
			ul.RawQuery = escapedQuery
		} else {
			logger.Errorf("failed to unescape the query %s: %v", ul.RawQuery, err)
		}
		link := &lib.Link{
			URL: ul.String(),
			Rel: "next",
		}
		links = append(links, link)
	}
	return links
}
