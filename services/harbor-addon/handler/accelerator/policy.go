package accelerator

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/common/security"
	"github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/lib/q"

	daopolicy "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/dao/policy"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/manager/policy"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
)

type PolicyHandler struct {
	policyMgr policy.Manager
}

func NewPolicyHandler() *PolicyHandler {
	return &PolicyHandler{
		policyMgr: policy.NewPolicyManager(),
	}
}

// List accelerator policies.
// @Summary List accelerator policies.
// @Description This endpoint returns accelerator policies.
// @Id listAcceleratorPolicy
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param page query int false "page number" default(1)
// @Param page_size query int false "page size" default(10)
// @Param q query string false "Query string to query resources. Supported query patterns are "exact match(k=v)", "fuzzy match(k=~v)", "range(k=[min~max])", "list with union releationship(k={v1 v2 v3})" and "list with intersetion relationship(k=(v1 v2 v3))". The value of range and list can be string(enclosed by " or '), integer or time(in format "2020-04-09 02:36:00"). All of these query patterns should be put in the query string "q=xxx" and splitted by ",". e.g. q=k1=v1,k2=~v2,k3=[min~max]"
// @Param X-Request-Id header string false "The request id"
// @Success 200 {array} policy.AcceleratorPolicy AcceleratorPolicy
// @Failure 400 {string} Bad request
// @Failure 500 {string} Internal server error
// @Header 200 {integer} X-Total-Count "The total of accelerator policy"
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /accelerators/policies [get]
func (rh *PolicyHandler) List(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)
	queryStr := c.Query("q")

	pageNoStr, pageSizeStr := c.DefaultQuery("page", "1"), c.DefaultQuery("page_size", "10")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page number: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page size : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	query, err := q.Build(queryStr, "", pageNo, pageSize)
	if err != nil {
		logger.Errorf("build query failed: %v", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	total, policies, err := rh.policyMgr.List(ctx, query)
	if err != nil {
		logger.Errorf("list accelerator policy failed: %v", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.Header("X-Total-Count", strconv.FormatInt(total, 10))
	c.JSON(http.StatusOK, policies)
}

// Create accelerator policy.
// @Summary Create accelerator policy.
// @Description This endpoint creates an accelerator policy
// @Id createAcceleratorPolicy
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param policy body policy.AcceleratorPolicy true "Properties filters needed."
// @Success 200 {integer} The ID of accelerator policy create successfully.
// @Failure 400 {string} Bad request
// @Failure 409 {string} Conflict
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /accelerators/policies [post]
func (rh *PolicyHandler) Create(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	ap := &daopolicy.AcceleratorPolicy{}
	if err := c.Bind(&ap); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if ap.ID != 0 {
		c.AbortWithError(http.StatusBadRequest, fmt.Errorf("cannot accept accelerator policy creating request with ID: %d", ap.ID))
		return
	}

	var username string
	secCtx, ok := security.FromContext(ctx)
	if ok {
		username = secCtx.GetUsername()
	}

	ap.Creator = username

	id, err := rh.policyMgr.Create(ctx, ap)
	if err != nil {
		if errors.IsConflictErr(err) {
			logger.Errorf("Conflict, the accelerator policy :%s is exist", ap.Name)
			c.AbortWithStatusJSON(http.StatusConflict, fmt.Sprintf("Conflict, the accelerator policy name: %s is exist", ap.Name))
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, id)
}

// Get accelerator policy.
// @Summary get accelerator policy.
// @Description This endpoint returns accelerator policy.
// @Id getAcceleratorPolicy
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param policy_id path string true "The ID of the policy"
// @Success 200 {object} policy.AcceleratorPolicy AcceleratorPolicy
// @Failure 400 {string} Bad request
// @Failure 404 {string} Not found
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /accelerators/policies/{policy_id} [get]
func (rh *PolicyHandler) Get(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	policyId, err := strconv.ParseInt(c.Param("policy_id"), 10, 64)
	if err != nil {
		logger.Errorf("Failed to get accelerator policy ID, error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	acceleratorPolicy, err := rh.policyMgr.Get(ctx, policyId)
	if err != nil {
		logger.Errorf("Failed to get acceleratorPolicy, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, acceleratorPolicy)
}

// Update accelerator policy.
// @Summary Update accelerator policy.
// @Description This endpoint update accelerator policy.
// @Id updateAcceleratorPolicy
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param policy_id path string true "The ID of the policy"
// @Param policy body policy.AcceleratorPolicy true "Properties "targets" and "event_types" needed."
// @Success 200 {string} Accelerator policy update successfully.
// @Failure 400 {string} Bad request
// @Failure 404 {string} Not found
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /accelerators/policies/{policy_id} [put]
func (rh *PolicyHandler) Update(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	policyId, err := strconv.ParseInt(c.Param("policy_id"), 10, 64)
	if err != nil {
		logger.Errorf("Failed to get accelerator policy ID, error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	_, err = rh.policyMgr.Get(ctx, policyId)
	if err != nil {
		logger.Errorf("Failed to get accelerator policy, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	ap := &daopolicy.AcceleratorPolicy{}
	if err := c.Bind(&ap); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	ap.ID = policyId

	if err = rh.policyMgr.Update(ctx, ap); err != nil {
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, "success")
}

// Delete accelerator policy.
// @Summary Delete accelerator policy.
// @Description This endpoint delete accelerator policy.
// @Id deleteAcceleratorPolicy
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param policy_id path string true "The ID of the policy"
// @Success 200 {string} Accelerator policy delete successfully.
// @Failure 400 {string} Bad request
// @Failure 404 {string} Not found
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /accelerators/policies/{policy_id} [delete]
func (rh *PolicyHandler) Delete(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	policyId, err := strconv.ParseInt(c.Param("policy_id"), 10, 64)
	if err != nil {
		logger.Errorf("Failed to get policy ID, error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	_, err = rh.policyMgr.Get(ctx, policyId)
	if err != nil {
		logger.Errorf("Failed to get policy, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if err := rh.policyMgr.Delete(ctx, policyId); err != nil {
		logger.Errorf("Failed to detele policy, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, "success")
}

// BatchDelete accelerator policy.
// @Summary BatchDelete accelerator policy.
// @Description This endpoint batch delete accelerator policy.
// @Id batchDeleteAcceleratorPolicy
// @Tags accelerator
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param request body models.BatchDeleteRequest true "The accelerator policy ids"
// @Success 200 {string} Accelerator policy delete successfully.
// @Failure 400 {string} Bad request
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /accelerators/policies [delete]
func (rh *PolicyHandler) BatchDelete(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	var deleteRequest models.BatchDeleteRequest
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("Bind batch delete request item failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if err := rh.policyMgr.BatchDelete(ctx, deleteRequest.Items); err != nil {
		logger.Errorf("Batch delete accelerator policy failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, "success")
}
