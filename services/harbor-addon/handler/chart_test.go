package handler

import (
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"k8s.io/apimachinery/pkg/api/resource"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
)

func Test_UploadChart(t *testing.T) {
	rw := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(&GinResponseWriter{rw})
	assert.NotNil(t, ctx)

	ch := NewChartHandler("test.com", nil, nil, nil, true)
	ch.proxy = newChartProxy("test.com", &TestTransport{
		resp: &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(strings.NewReader("")),
		},
	})

	ctx.Set(middleware.CCR_QUOTA_IDENTITY, &ccrv1alpha1.CCRQuota{
		Status: ccrv1alpha1.CCRQuotaStatus{
			ChartRepo: resource.NewQuantity(0, resource.DecimalSI),
		},
	})
	ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com", nil)
	ctx.Request.Header.Add("Authorization", "test")

	ch.UploadChart(ctx)

	assert.Equal(t, 200, rw.Code)
}
