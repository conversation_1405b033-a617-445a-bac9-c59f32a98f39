package handler

import (
	"net/http"
	"net/http/httputil"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/service"
)

const (
	csrfHeaderKey     = "x-harbor-csrf-token"
	csrfCookieName    = "_gorilla_csrf"
	sessionCookieName = "sid"
)

type ChartHandler struct {
	chartService   *service.ChartService
	simUserService *service.SimUserService
	harborCsrf     *utils.HarborCsrf
	proxy          http.Handler
	isPublic       bool
}

func NewChartHandler(harborAddr string, db *gorm.DB, sus *service.SimUserService, csrfUtil *utils.HarborCsrf, isPublic bool) *ChartHandler {
	return &ChartHandler{
		chartService:   service.NewChartService(db, harborAddr),
		simUserService: sus,
		harborCsrf:     csrfUtil,
		proxy:          newChartProxy(harborAddr, nil),

		isPublic: isPublic,
	}
}

func newChartProxy(harborAddr string, trans http.RoundTripper) http.Handler {
	director := func(req *http.Request) {
		req.URL.Scheme = "http"
		req.URL.Host = harborAddr
		req.Host = harborAddr

		var path string
		if strings.HasPrefix(req.URL.Path, "/") {
			path = strings.TrimPrefix(req.URL.Path, "/addon/v1")
		} else {
			path = strings.TrimPrefix(req.URL.Path, "addon/v1/")
		}
		req.URL.Path = path
		req.URL.RawPath = ""

		if _, ok := req.Header["User-Agent"]; !ok {
			// explicitly disable User-Agent so it's not set to default value
			req.Header.Set("User-Agent", "")
		}
	}

	return &httputil.ReverseProxy{Director: director, Transport: trans}
}

func (ch *ChartHandler) UploadChart(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	if ch.isPublic {
		quota := middleware.QuotaFromContext(c)
		if quota != nil && quota.Status.ChartRepo != nil && quota.Status.ChartRepo.Value() > 0 {
			count, err := ch.chartService.Count(middleware.HarborContext(c), "")
			if err != nil {
				logger.Errorf("get chart repo count failed: %v", err)
				c.AbortWithError(http.StatusInternalServerError, err)
				return
			}

			if count >= quota.Status.ChartRepo.Value() {
				logger.Errorf("quota exceed, now chart count is %d but quota is: %d", count, quota.Status.ChartRepo.Value())
				c.AbortWithStatusJSON(http.StatusPreconditionFailed, "quota exceed")
				return
			}
		}
	}

	if err := ch.modifyRequestWithAuth(c); err != nil {
		logger.Errorf("modify request failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	ch.proxy.ServeHTTP(c.Writer, c.Request)
}

func (ch *ChartHandler) modifyRequestWithAuth(c *gin.Context) error {
	logger := middleware.LoggerFromContext(c)

	if c.GetHeader("Authorization") == "" &&
		c.GetHeader(csrfHeaderKey) == "" {
		u := middleware.UserFromContext(c)
		if u != nil {
			sid, err := ch.simUserService.GetAdminSession(c, u.Username)
			if err != nil {
				logger.Errorf("get session id failed: %s", err)
				return err
			}

			csrfToken, csrfCookie, err := ch.harborCsrf.GetCsrfToken()
			if err != nil {
				logger.Errorf("get csrf token failed: %s", err)
				return err
			}

			c.Request.Header.Set(csrfHeaderKey, csrfToken)
			c.Request.AddCookie(&http.Cookie{
				Name:  sessionCookieName,
				Value: sid,
			})
			c.Request.AddCookie(&http.Cookie{
				Name:  csrfCookieName,
				Value: csrfCookie,
			})
		}
	}

	return nil
}

func (ch *ChartHandler) Proxy(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	if err := ch.modifyRequestWithAuth(c); err != nil {
		logger.Errorf("modify request failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	ch.proxy.ServeHTTP(c.Writer, c.Request)
}
