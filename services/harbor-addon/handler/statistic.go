package handler

import (
	"net/http"
	"os"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/controller/project"
	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/blob"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
	_ "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/service"
)

type StatisticHandler struct {
	statisticService *service.StatisticService
	projectCtl       project.Controller
	blobMgr          blob.Manager
}

func NewStatisticHandler(clients *harbor.HarborClient) *StatisticHandler {
	return &StatisticHandler{
		statisticService: service.NewStatisticService(clients),
		projectCtl:       project.Ctl,
		blobMgr:          blob.Mgr,
	}
}

// GetStatistic 获取统计信息
// @Summary 获取统计信息
// @Description 获取统计信息
// @Tags product
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "request id"
// @Success 200 {object} models.Statistic
// @Failure 500 {string} string
// @Router /products/statistic [get]
func (t *StatisticHandler) GetStatistic(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	username, password, ok := c.Request.BasicAuth()
	if !ok {
		username = "admin"
		password = os.Getenv("HARBOR_ADMIN_PASSWORD")
	}

	result, err := t.statisticService.GetStatistic(middleware.HarborContext(c), username, password)
	if err != nil {
		logger.Errorf("get static failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

func (t *StatisticHandler) GetStorage(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	pageNoStr, pageSizeStr := c.DefaultQuery("page", "1"), c.DefaultQuery("page_size", "50")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page no %s: %s", pageNoStr, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page size %s: %s", pageSizeStr, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid parameter")
		return
	}

	total, err := t.projectCtl.Count(middleware.HarborContext(c), &q.Query{})
	if err != nil {
		logger.Errorf("get total project failed: %s", err)
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}

	projs, err := t.projectCtl.List(middleware.HarborContext(c), &q.Query{
		PageNumber: pageNo,
		PageSize:   pageSize,
	}, project.Detail(false))
	if err != nil {
		logger.Errorf("list project failed: %s", err)
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}

	storageDetail := &models.StorageDetail{
		Total:        total,
		PageNo:       pageNo,
		ProjectItems: make([]models.ProjectStorage, len(projs)),
	}

	for i, v := range projs {
		size, err := t.blobMgr.CalculateTotalSizeByProject(middleware.HarborContext(c), v.ProjectID, false)
		if err != nil {
			logger.Errorf("get image size of project %s failed: %s", v.Name, err)
			c.AbortWithStatus(http.StatusInternalServerError)
			return
		}

		storageDetail.ProjectItems[i].Image = size
		storageDetail.ProjectItems[i].Chart = 0
		storageDetail.ProjectItems[i].ProjectID = v.ProjectID
		storageDetail.ProjectItems[i].Total = storageDetail.ProjectItems[i].Image + storageDetail.ProjectItems[i].Chart
	}

	c.JSON(http.StatusOK, storageDetail)
}
