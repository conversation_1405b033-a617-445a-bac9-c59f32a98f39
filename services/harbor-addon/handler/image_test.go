package handler

import (
	"context"
	_ "crypto/sha256"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/lib/errors"
	art "github.com/goharbor/harbor/src/pkg/artifact"
	pkgblob "github.com/goharbor/harbor/src/pkg/blob"
	pkgrepository "github.com/goharbor/harbor/src/pkg/repository"
	"github.com/goharbor/harbor/src/pkg/repository/model"
	pkgtag "github.com/goharbor/harbor/src/pkg/tag"
	modeltag "github.com/goharbor/harbor/src/pkg/tag/model/tag"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/goharbor/harbor/src/testing/pkg/artifact"
	"github.com/goharbor/harbor/src/testing/pkg/blob"
	"github.com/goharbor/harbor/src/testing/pkg/repository"
	"github.com/goharbor/harbor/src/testing/pkg/tag"
	"github.com/stretchr/testify/assert"
	"k8s.io/apimachinery/pkg/api/resource"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

func newMockImageHandler() *ImageHandler {
	return &ImageHandler{
		repoMgr:       &repository.Manager{},
		tagMgr:        &tag.FakeManager{},
		artifactMgr:   &artifact.Manager{},
		blobMgr:       &blob.Manager{},
		manifestCache: utils.NewCache(10 * time.Minute),
		isPublic:      true,
	}
}

type GinResponseWriter struct {
	http.ResponseWriter
}

func (g *GinResponseWriter) CloseNotify() <-chan bool {
	return make(chan bool)
}

func newGinResponseWriter() http.ResponseWriter {
	return &GinResponseWriter{httptest.NewRecorder()}
}

type TestTransport struct {
	resp *http.Response
}

func (t *TestTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	if t.resp == nil {
		return nil, fmt.Errorf("error generated")
	}
	return t.resp, nil
}

func Test_HeadBlobs(t *testing.T) {
	ih := newMockImageHandler()

	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	assert.NotNil(t, ctx)
	ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test"})
	ih.HeadBlobs(ctx)
	assert.Equal(t, 400, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test@test"})
	ctx.Set("harbor_context", context.Background())
	mock.OnAnything(ih.blobMgr, "Get").Return(nil, errors.NotFoundError(fmt.Errorf("test")))
	ih.HeadBlobs(ctx)
	assert.Equal(t, 404, ctx.Writer.Status())

	ih.blobMgr = &blob.Manager{}
	mock.OnAnything(ih.blobMgr, "Get").Return(nil, fmt.Errorf("test"))
	ih.HeadBlobs(ctx)
	assert.Equal(t, 500, ctx.Writer.Status())

	ih.blobMgr = &blob.Manager{}
	mock.OnAnything(ih.blobMgr, "Get").Return(&pkgblob.Blob{Size: 100}, nil)

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test@test"})
	ctx.Request, _ = http.NewRequest(http.MethodHead, "/test", nil)
	ih.transport = &TestTransport{
		resp: &http.Response{
			StatusCode: 500,
			Body:       io.NopCloser(strings.NewReader("")),
		},
	}
	ih.HeadBlobs(ctx)
	assert.Equal(t, 500, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test@test"})
	ctx.Request, _ = http.NewRequest(http.MethodHead, "/test", nil)
	ih.transport = &TestTransport{
		resp: &http.Response{
			StatusCode: 307,
			Body:       io.NopCloser(strings.NewReader("")),
		},
	}
	ih.HeadBlobs(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())
}

func Test_PutManifests(t *testing.T) {
	ih := newMockImageHandler()

	quantity, err := resource.ParseQuantity("10")
	assert.NoError(t, err)

	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	assert.NotNil(t, ctx)
	ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test@test"})
	ctx.Set("ccr-quota", &ccrv1alpha1.CCRQuota{
		Status: ccrv1alpha1.CCRQuotaStatus{
			ImageRepo: &quantity,
		},
	})
	ctx.Set("harbor_context", context.Background())
	mock.OnAnything(ih.repoMgr, "Count").Return(int64(11), nil)
	ih.PutManifests(ctx)
	assert.Equal(t, 412, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	assert.NotNil(t, ctx)
	ctx.Request, _ = http.NewRequest(http.MethodHead, "/test", nil)
	ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test@test"})
	ctx.Set("ccr-quota", &ccrv1alpha1.CCRQuota{
		Status: ccrv1alpha1.CCRQuotaStatus{
			ImageRepo: &quantity,
		},
	})
	ctx.Set("harbor_context", context.Background())
	ih.repoMgr = &repository.Manager{}
	mock.OnAnything(ih.repoMgr, "Count").Return(int64(9), nil)
	ih.transport = &TestTransport{
		resp: &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(strings.NewReader("")),
		},
	}
	ih.PutManifests(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())
}

func Test_HeadManifests(t *testing.T) {
	privateKey := `*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	tm := token.NewTokenMaker(1800, "xxx")
	tk, err := tm.MakeToken("test", []byte(privateKey), []*token.ResourceActions{
		{
			Type:    "repository",
			Name:    "test",
			Actions: []string{"pull", "push"},
		},
	})
	assert.NoError(t, err)

	ih := newMockImageHandler()
	ih.privateKey = []byte(privateKey)

	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	assert.NotNil(t, ctx)
	ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test@test"})
	ctx.Request, _ = http.NewRequest(http.MethodGet, "test", nil)
	ctx.Set("harbor_context", context.Background())

	ih.HeadManifest(ctx)
	assert.Equal(t, 401, ctx.Writer.Status())

	ctx.Request.Header.Set("Authorization", "Bearer "+tk.Token)

	mock.OnAnything(ih.repoMgr, "List").Return(nil, nil)
	ih.HeadManifest(ctx)
	assert.Equal(t, 404, ctx.Writer.Status())

	ih.repoMgr = &repository.Manager{}
	mock.OnAnything(ih.repoMgr, "List").Return([]*model.RepoRecord{{}}, nil)
	mock.OnAnything(ih.tagMgr, "List").Return(nil, nil)
	ih.HeadManifest(ctx)
	assert.Equal(t, 404, ctx.Writer.Status())

	ih.tagMgr = &tag.FakeManager{}
	mock.OnAnything(ih.tagMgr, "List").Return([]*modeltag.Tag{{}}, nil)
	mock.OnAnything(ih.artifactMgr, "Get").Return(nil, errors.NotFoundError(fmt.Errorf("test")))
	ih.HeadManifest(ctx)
	assert.Equal(t, 404, ctx.Writer.Status())

	ih.artifactMgr = &artifact.Manager{}
	mock.OnAnything(ih.artifactMgr, "Get").Return(&art.Artifact{Digest: "sha256:test", ManifestMediaType: "application/test"}, nil)
	ih.HeadManifest(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())
	assert.Equal(t, "sha256:test", ctx.Writer.Header().Get("Docker-Content-Digest"))
	assert.Equal(t, "application/test", ctx.Writer.Header().Get("Content-Type"))
	assert.Equal(t, "0", ctx.Writer.Header().Get("Content-Length"))

	ctx.Params = []gin.Param{{Key: "resource", Value: "test@sha256:c5d7abd44e7b292eb6a976c12f2a1952c2a414deabefa5412e05c89b259ca201"}}
	mock.OnAnything(ih.artifactMgr, "GetByDigest").Return(&art.Artifact{Digest: "sha256:test"}, nil)
	ih.HeadManifest(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())
	assert.Equal(t, "sha256:test", ctx.Writer.Header().Get("Docker-Content-Digest"))

	ih.artifactMgr = &artifact.Manager{}
	mock.OnAnything(ih.artifactMgr, "GetByDigest").Return(&art.Artifact{}, errors.NotFoundError(fmt.Errorf("test")))
	ih.HeadManifest(ctx)
	assert.Equal(t, 404, ctx.Writer.Status())
}

type errorReader struct{}

func (e *errorReader) Read([]byte) (int, error) {
	return 0, fmt.Errorf("error reader")
}

func TestImageHandler_GetManifest(t *testing.T) {
	privateKey := `*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	type fields struct {
		harborAddr       string
		authDomain       string
		privateKey       []byte
		repoMgr          pkgrepository.Manager
		tagMgr           pkgtag.Manager
		artifactMgr      art.Manager
		blobMgr          pkgblob.Manager
		transport        http.RoundTripper
		manifestCache    *utils.Cache
		cachedNamespaces map[string]bool
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		expectCode int
	}{
		// TODO: Add test cases.
		{
			name: "资源名字不合法,没有tag",
			fields: func() fields {
				return fields{}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "testtest"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test", nil)
				return args{
					c: ctx,
				}
			}(),
			expectCode: 400,
		},
		{
			name: "资源名字不合法,没有命名空间",
			fields: func() fields {
				return fields{}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test@test"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test", nil)
				return args{
					c: ctx,
				}
			}(),
			expectCode: 400,
		},
		{
			name: "不在缓存名单中",
			fields: func() fields {
				return fields{
					transport: &TestTransport{
						resp: &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(strings.NewReader("")),
						},
					},
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test/t1@test"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test", nil)
				return args{
					c: ctx,
				}
			}(),
			expectCode: 200,
		},
		{
			name: "在缓存名单中,但是没有权限",
			fields: func() fields {
				return fields{
					cachedNamespaces: map[string]bool{"test": true},
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test/t1@test"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test", nil)
				return args{
					c: ctx,
				}
			}(),
			expectCode: 401,
		},
		{
			name: "在缓存名单中,有权限,请求报错",
			fields: func() fields {
				return fields{
					cachedNamespaces: map[string]bool{"test": true},
					transport: &TestTransport{
						resp: nil,
					},
					privateKey:    []byte(privateKey),
					manifestCache: utils.NewCache(10 * time.Minute),
				}
			}(),
			args: func() args {
				tm := token.NewTokenMaker(1800, "xxx")
				tk, err := tm.MakeToken("test", []byte(privateKey), []*token.ResourceActions{
					{
						Type:    "repository",
						Name:    "test/t1",
						Actions: []string{"pull", "push"},
					},
				})
				assert.NoError(t, err)
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test/t1@test"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test", nil)
				ctx.Request.Header.Set("Authorization", "Bearer "+tk.Token)
				return args{
					c: ctx,
				}
			}(),
			expectCode: 500,
		},
		{
			name: "在缓存名单中,有权限,读取body报错",
			fields: func() fields {
				return fields{
					cachedNamespaces: map[string]bool{"test": true},
					transport: &TestTransport{
						resp: &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(&errorReader{}),
						},
					},
					privateKey:    []byte(privateKey),
					manifestCache: utils.NewCache(10 * time.Minute),
				}
			}(),
			args: func() args {
				tm := token.NewTokenMaker(1800, "xxx")
				tk, err := tm.MakeToken("test", []byte(privateKey), []*token.ResourceActions{
					{
						Type:    "repository",
						Name:    "test/t1",
						Actions: []string{"pull", "push"},
					},
				})
				assert.NoError(t, err)
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test/t1@test"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test", nil)
				ctx.Request.Header.Set("Authorization", "Bearer "+tk.Token)
				return args{
					c: ctx,
				}
			}(),
			expectCode: 500,
		},
		{
			name: "在缓存名单中,有权限,返回码不是2xx",
			fields: func() fields {
				return fields{
					cachedNamespaces: map[string]bool{"test": true},
					transport: &TestTransport{
						resp: &http.Response{
							StatusCode: 400,
							Body:       io.NopCloser(strings.NewReader("xxx")),
						},
					},
					privateKey:    []byte(privateKey),
					manifestCache: utils.NewCache(10 * time.Minute),
				}
			}(),
			args: func() args {
				tm := token.NewTokenMaker(1800, "xxx")
				tk, err := tm.MakeToken("test", []byte(privateKey), []*token.ResourceActions{
					{
						Type:    "repository",
						Name:    "test/t1",
						Actions: []string{"pull", "push"},
					},
				})
				assert.NoError(t, err)
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test/t1@test"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test", nil)
				ctx.Request.Header.Set("Authorization", "Bearer "+tk.Token)
				return args{
					c: ctx,
				}
			}(),
			expectCode: 400,
		},
		{
			name: "在缓存名单中,有权限,成功",
			fields: func() fields {
				hd := make(http.Header)
				hd.Add("Content-Length", "3")
				hd.Add("Content-Type", "text")
				return fields{
					cachedNamespaces: map[string]bool{"test": true},
					transport: &TestTransport{
						resp: &http.Response{
							StatusCode: 200,
							Body:       io.NopCloser(strings.NewReader("xxx")),
							Header:     hd,
						},
					},
					privateKey:    []byte(privateKey),
					manifestCache: utils.NewCache(10 * time.Minute),
				}
			}(),
			args: func() args {
				tm := token.NewTokenMaker(1800, "xxx")
				tk, err := tm.MakeToken("test", []byte(privateKey), []*token.ResourceActions{
					{
						Type:    "repository",
						Name:    "test/t1",
						Actions: []string{"pull", "push"},
					},
				})
				assert.NoError(t, err)
				ctx, _ := gin.CreateTestContext(newGinResponseWriter())
				ctx.Params = append(ctx.Params, gin.Param{Key: "resource", Value: "test/t1@test"})
				ctx.Request, _ = http.NewRequest(http.MethodGet, "test", nil)
				ctx.Request.Header.Set("Authorization", "Bearer "+tk.Token)
				return args{
					c: ctx,
				}
			}(),
			expectCode: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			image := &ImageHandler{
				harborAddr:       tt.fields.harborAddr,
				authDomain:       tt.fields.authDomain,
				privateKey:       tt.fields.privateKey,
				repoMgr:          tt.fields.repoMgr,
				tagMgr:           tt.fields.tagMgr,
				artifactMgr:      tt.fields.artifactMgr,
				blobMgr:          tt.fields.blobMgr,
				transport:        tt.fields.transport,
				manifestCache:    tt.fields.manifestCache,
				cachedNamespaces: tt.fields.cachedNamespaces,
			}
			image.GetManifest(tt.args.c)
			if tt.args.c.Writer.Status() != tt.expectCode {
				t.Errorf("actual status code is %d but actual code is %d", tt.args.c.Writer.Status(), tt.expectCode)
			}
		})
	}
}
