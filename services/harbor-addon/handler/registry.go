package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/reg/model"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/service"
)

type RegistryHandler struct {
	registry *service.RegistryService
}

func NewRegistryHandler() *RegistryHandler {
	return &RegistryHandler{
		registry: service.NewRegistryService(),
	}
}

// List 查询仓库列表（支持分页）
// @Summary 查询仓库列表（支持分页）
// @Description 查询仓库列表（支持分页）
// @Id listRegistries
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "页大小" default(10)
// @Param q query string false "搜索关键信息 Query string to query resources. Supported query patterns are "exact match(k=v)",
// "fuzzy match(k=~v)", "range(k=[min~max])", "list with union releationship(k={v1 v2 v3})" and "list with intersetion relationship(k=(v1 v2 v3))".
// The value of range and list can be string(enclosed by " or '), integer or time(in format "2020-04-09 02:36:00").
// All of these query patterns should be put in the query string "q=xxx" and splitted by ",". e.g. q=k1=v1,k2=~v2,k3=[min~max]"
// @Param X-Request-Id header string false "request id"
// @Success 200 {array} model.Registry Registry
// @Failure 400 {string} string
// @Failure 500 {string} string
// @Header 200 {integer} X-Total-Count "返回registries总条数"
// @Router /registries [get]
func (rh *RegistryHandler) List(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	queryStr := c.Query("q")

	pageNoStr, pageSizeStr := c.DefaultQuery("page", "1"), c.DefaultQuery("page_size", "10")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page number: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page size : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	query, err := q.Build(queryStr, "", pageNo, pageSize)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}
	total, registries, err := rh.registry.List(query, c)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	// Hide passwords
	for _, r := range registries {
		hideAccessSecret(r.Credential)
	}
	c.Header("x-total-count", strconv.FormatInt(total, 10))
	c.JSON(http.StatusOK, registries)
}

func hideAccessSecret(credential *model.Credential) {
	if credential == nil {
		return
	}
	if len(credential.AccessSecret) == 0 {
		return
	}
	credential.AccessSecret = "*****"
}
