package handler

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/controller/project"
	"github.com/goharbor/harbor/src/controller/repository"
	mgrproject "github.com/goharbor/harbor/src/pkg/project"
	"github.com/goharbor/harbor/src/pkg/project/metadata"
	mockproject "github.com/goharbor/harbor/src/testing/controller/project"
	"github.com/stretchr/testify/mock"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/service"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
)

func TestProjectHandler_ListProjects(t *testing.T) {
	type fields struct {
		chartService  *service.ChartService
		projectCtl    project.Controller
		repositoryCtl repository.Controller
		projectMgr    mgrproject.Manager
		metaMgr       metadata.Manager
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		// TODO: Add test cases.
		{
			name: "list project failed",
			fields: func() fields {
				projCtl := &mockproject.Controller{}
				projCtl.On("List", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, fmt.Errorf("failed"))
				projCtl.On("Count", mock.Anything, mock.Anything).Return(int64(10), nil)
				return fields{
					projectCtl: projCtl,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 500,
		},
		{
			name: "bad request",
			fields: func() fields {
				return fields{}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?q=xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &ProjectHandler{
				chartService:  tt.fields.chartService,
				projectCtl:    tt.fields.projectCtl,
				repositoryCtl: tt.fields.repositoryCtl,
				projectMgr:    tt.fields.projectMgr,
				metaMgr:       tt.fields.metaMgr,
			}
			a.ListProjects(tt.args.c)
			if tt.args.c.Writer.Status() != tt.code {
				t.Errorf("t.args.c.Writer.Status() %d != tt.code %d", tt.args.c.Writer.Status(), tt.code)
			}
		})
	}
}
