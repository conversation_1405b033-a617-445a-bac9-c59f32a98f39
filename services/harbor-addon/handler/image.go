package handler

import (
	_ "crypto/sha256"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"time"

	"github.com/docker/distribution/registry/api/errcode"
	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/artifact"
	"github.com/goharbor/harbor/src/pkg/blob"
	"github.com/goharbor/harbor/src/pkg/repository"
	"github.com/goharbor/harbor/src/pkg/tag"
	"github.com/opencontainers/go-digest"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
)

type ImageHandler struct {
	harborAddr  string
	authDomain  string
	privateKey  []byte
	repoMgr     repository.Manager
	tagMgr      tag.Manager
	artifactMgr artifact.Manager
	blobMgr     blob.Manager
	transport   http.RoundTripper

	manifestCache    *utils.Cache
	cachedNamespaces map[string]bool

	isPublic bool
}

type CachedManifest struct {
	canonical []byte
	mediaType string
	digest    string
	length    string
}

type UpstreamError struct {
	Status int
	Header http.Header
	Body   []byte
}

func (u *UpstreamError) Error() string {
	return fmt.Sprintf("upstream return %d, header: %s, body lenght: %d", u.Status, u.Header, len(u.Body))
}

func NewImageHandler(harborAddr, authDomain string, privateKey []byte, cachedNamespaces []string, isPublic bool) *ImageHandler {
	cachedMap := make(map[string]bool)
	for _, v := range cachedNamespaces {
		cachedMap[v] = true
	}

	return &ImageHandler{
		harborAddr:       harborAddr,
		authDomain:       authDomain,
		privateKey:       privateKey,
		repoMgr:          repository.New(),
		tagMgr:           tag.NewManager(),
		artifactMgr:      artifact.NewManager(),
		blobMgr:          blob.NewManager(),
		transport:        http.DefaultTransport,
		manifestCache:    utils.NewCache(1 * time.Minute),
		cachedNamespaces: cachedMap,
		isPublic:         isPublic,
	}
}

// HeadBlobs
// return 200 OK always
func (image *ImageHandler) HeadBlobs(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	// resource is name@reference
	name, ref, ok := image.parseNameReference(c.Param("resource"))
	if !ok {
		logger.Errorf("invalid param %s, cut resource failed: '@' does not appear in resource", c.Param("resource"))
		c.AbortWithError(http.StatusBadRequest, fmt.Errorf("invalid repository"))
		return
	}

	// get size
	bb, err := image.blobMgr.Get(middleware.HarborContext(c), ref)
	if err != nil {
		logger.Errorf("get blob failed: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithError(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	targetUrl, err := url.Parse(fmt.Sprintf("http://%s/v2/%s/blobs/%s", image.harborAddr, name, ref))
	if err != nil {
		logger.Errorf("parse url failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	(&httputil.ReverseProxy{
		Director: func(req *http.Request) {
			req.URL = targetUrl
			if req.Header.Get("User-Agent") != "" {
				// explicitly disable User-Agent so it's not set to default value
				req.Header.Set("User-Agent", req.Header.Get("User-Agent"))
			}
		},
		ModifyResponse: func(r *http.Response) error {
			if r.StatusCode < http.StatusBadRequest {
				r.StatusCode = http.StatusOK
				r.Status = http.StatusText(r.StatusCode)
			}
			if r.Header == nil {
				r.Header = make(http.Header)
			}
			// content length is necessary since client will check the length
			r.Header.Set("Content-Length", fmt.Sprintf("%d", bb.Size))
			r.Header.Set("Docker-Content-Digest", ref)
			return nil
		},
		Transport: image.transport,
	}).ServeHTTP(c.Writer, c.Request)
}

// PutManifests Proxy put the manifest identified by name and reference where reference can be a tag or digest.
// /addon/v1/manifests/*resource resource is name@reference
func (image *ImageHandler) PutManifests(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	// resource is name@reference
	name, ref, ok := image.parseNameReference(c.Param("resource"))
	if !ok {
		logger.Errorf("invalid param %s, cut resource failed: '@' does not appear in resource", c.Param("resource"))
		c.AbortWithError(http.StatusBadRequest, fmt.Errorf("invalid repository"))
		return
	}

	targetUrl, err := url.Parse(fmt.Sprintf("http://%s/v2/%s/manifests/%s", image.harborAddr, name, ref))
	if err != nil {
		logger.Errorf("parse url failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if image.isPublic {
		quota := middleware.QuotaFromContext(c)
		if quota != nil && quota.Status.ImageRepo != nil {
			count, err := image.repoMgr.Count(middleware.HarborContext(c), nil)
			if err != nil {
				logger.Errorf("get repo count failed: %s", err)
				c.AbortWithError(http.StatusInternalServerError, err)
				return
			}

			if count > quota.Status.ImageRepo.Value() {
				logger.Errorf("quota exceed, now image count is %d but quota is: %d", count,
					quota.Status.ImageRepo.Value())
				errs := errcode.Errors{errcode.Error{
					Message: fmt.Sprintf("quota exceed, now image count is %d but quota is: %d", count,
						quota.Status.ImageRepo.Value()),
				}}
				c.AbortWithStatusJSON(http.StatusPreconditionFailed, errs)
				return
			}
		}
	}

	(&httputil.ReverseProxy{
		Director: func(req *http.Request) {
			req.URL = targetUrl
			if req.Header.Get("User-Agent") != "" {
				// explicitly disable User-Agent so it's not set to default value
				req.Header.Set("User-Agent", req.Header.Get("User-Agent"))
			}
		},
		Transport: image.transport,
	}).ServeHTTP(c.Writer, c.Request)
}

// HeadManifest put the manifest identified by name and reference where reference can be a tag or digest.
// /addon/v1/manifests/*resource resource is name@reference
func (image *ImageHandler) HeadManifest(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	// resource is name@reference
	name, ref, ok := image.parseNameReference(c.Param("resource"))
	if !ok {
		logger.Errorf("invalid param %s, cut resource failed: '@' does not appear in resource", c.Param("resource"))
		c.AbortWithError(http.StatusBadRequest, fmt.Errorf("invalid repository"))
		return
	}

	// auth check
	err := image.checkResourceAction(c, name, "pull")
	if err != nil {
		logger.Errorf("check resource action failed: %s", err)
		c.Header("Www-Authenticate", fmt.Sprintf(`Bearer realm="%s/service/token",service="harbor-registry",scope="repository:%s:pull"`, image.authDomain, name))
		c.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	var art *artifact.Artifact
	// tag found
	if _, err := digest.Parse(ref); err != nil {
		repos, err := image.repoMgr.List(middleware.HarborContext(c), &q.Query{
			Keywords: map[string]interface{}{
				"Name": name,
			},
		})
		if err != nil {
			logger.Errorf("get repo %s record failed: %s", name, err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}

		if len(repos) == 0 {
			logger.Errorf("get repo %s record failed: not found", name)
			c.AbortWithStatus(http.StatusNotFound)
			return
		}

		tags, err := image.tagMgr.List(middleware.HarborContext(c), &q.Query{
			Keywords: map[string]interface{}{
				"RepositoryID": repos[0].RepositoryID,
				"Name":         ref,
			},
		})
		if err != nil {
			logger.Errorf("get tag %d/%s record failed: %s", repos[0].RepositoryID, ref, err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
		if len(tags) == 0 {
			logger.Errorf("get tag %d/%s record failed: not found", repos[0].RepositoryID, ref)
			c.AbortWithStatus(http.StatusNotFound)
			return
		}

		art, err = image.artifactMgr.Get(middleware.HarborContext(c), tags[0].ArtifactID)
		if err != nil {
			logger.Errorf("get artifact by id %d failed: %s", tags[0].ArtifactID, err)
			if errors.IsNotFoundErr(err) {
				c.AbortWithStatus(http.StatusNotFound)
				return
			}
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
	} else {
		art, err = image.artifactMgr.GetByDigest(middleware.HarborContext(c), name, ref)
		if err != nil {
			logger.Errorf("get artifact %s:%s failed: %s", name, ref, err)
			if errors.IsNotFoundErr(err) {
				c.AbortWithStatus(http.StatusNotFound)
				return
			}

			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
	}

	// content-length is not required since manifest has different format.
	// mask Docker-Content-Digest as none，make sure client will use tag to get, then trigger will has tag info
	c.Writer.Header().Set("Docker-Content-Digest", art.Digest)
	c.Writer.Header().Set("Content-Type", art.ManifestMediaType)
	c.Writer.Header().Set("Content-Length", "0")
	c.Writer.Header().Set("X-Bce-Request-Id", middleware.RequestIdFromContext(c))
	c.Status(http.StatusOK)
}

// GetManifest get from cache first
// sha256 ==> content
// tag ==> content
func (image *ImageHandler) GetManifest(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	resource := c.Param("resource")
	// resource is name@reference
	name, ref, ok := image.parseNameReference(resource)
	if !ok {
		logger.Errorf("invalid param %s, cut resource failed: '@' does not appear in resource", c.Param("resource"))
		c.AbortWithError(http.StatusBadRequest, fmt.Errorf("invalid repository"))
		return
	}

	// checkout cached map
	parts := strings.SplitN(name, "/", 2)
	if len(parts) < 2 {
		logger.Errorf("invalid repository %s", name)
		c.AbortWithStatusJSON(http.StatusBadRequest, "invalid repository")
		return
	}

	targetUrl, err := url.Parse(fmt.Sprintf("http://%s/v2/%s/manifests/%s", image.harborAddr, name, ref))
	if err != nil {
		logger.Errorf("parse url failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	// if not cached, proxy directly
	if !image.cachedNamespaces[parts[0]] {
		(&httputil.ReverseProxy{
			Director: func(req *http.Request) {
				req.URL = targetUrl
				if req.Header.Get("User-Agent") != "" {
					// explicitly disable User-Agent so it's not set to default value
					req.Header.Set("User-Agent", req.Header.Get("User-Agent"))
				}
			},
			Transport: image.transport,
		}).ServeHTTP(c.Writer, c.Request)
		return
	}

	// auth check
	err = image.checkResourceAction(c, name, "pull")
	if err != nil {
		logger.Errorf("check resource action failed: %s", err)
		c.Header("Www-Authenticate", fmt.Sprintf(`Bearer realm="%s/service/token",service="harbor-registry",scope="repository:%s:pull"`, image.authDomain, name))
		c.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	manifest, err := image.manifestCache.Get(resource, func(key string) (interface{}, error) {
		cli := &http.Client{
			Transport: image.transport,
		}

		req := c.Request
		req.RequestURI = ""
		req.Method = http.MethodGet
		req.URL = targetUrl

		resp, err := cli.Do(req)
		if err != nil {
			logger.Errorf("get manifest from harbor failed: %s", err)
			return nil, err
		}
		defer resp.Body.Close()

		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			logger.Errorf("read body return error: %s", err)
			return nil, err
		}

		if resp.StatusCode/100 != 2 {
			err = &UpstreamError{
				Status: resp.StatusCode,
				Header: resp.Header,
				Body:   body,
			}
			logger.Errorf("get manifest failed, return error is: %s", err)
			return nil, err
		}

		mft := CachedManifest{
			digest:    resp.Header.Get("Docker-Content-Digest"),
			mediaType: resp.Header.Get("Content-Type"),
			length:    resp.Header.Get("Content-Length"),
			canonical: body,
		}

		return &mft, nil
	})

	if err != nil {
		logger.Errorf("get manifest from cache failed: %s", err)
		var upstreamErr *UpstreamError
		if errors.As(err, &upstreamErr) {
			c.Writer.WriteHeader(upstreamErr.Status)
			for k, v := range upstreamErr.Header {
				c.Writer.Header()[k] = v
			}
			_, err = c.Writer.Write(upstreamErr.Body)
			if err != nil {
				logger.Errorf("write body failed: %s", err)
			}
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	cachedManifest, ok := manifest.(*CachedManifest)
	if !ok {
		logger.Errorf("get manifest from cache failed, manifest is not CachedManifest format")
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.Status(http.StatusOK)
	c.Writer.Header().Set("Docker-Content-Digest", cachedManifest.digest)
	c.Writer.Header().Set("Content-Type", cachedManifest.mediaType)
	c.Writer.Header().Set("Content-Length", cachedManifest.length)
	c.Writer.Header().Set("X-Bce-Request-Id", middleware.RequestIdFromContext(c))
	c.Writer.Write(cachedManifest.canonical)
}

func (image *ImageHandler) parseNameReference(resource string) (name, ref string, ok bool) {
	if resource == "" {
		return
	}

	if strings.HasPrefix(resource, "/") {
		resource = resource[1:]
	}

	// resource is name@reference
	return strings.Cut(resource, "@")
}

func (image *ImageHandler) checkResourceAction(c *gin.Context, name, action string) error {
	// auth check
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return fmt.Errorf("no auth header provider")
	}

	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || (parts[0] != "bearer" && parts[0] != "Bearer") {
		return fmt.Errorf("invalid auth header format")
	}

	claim, err := token.Parse(parts[1], image.privateKey)
	if err != nil {
		return fmt.Errorf("decode auth header failed: %s", err)
	}

	nw := time.Now()
	if time.Unix(claim.ExpiresAt, 0).Before(nw) || time.Unix(claim.NotBefore, 0).After(nw) {
		return fmt.Errorf("invalid time range")
	}

	if !claim.Can("repository", name, "pull", false) {
		return fmt.Errorf("has no auth to access %s", name)
	}

	return nil
}
