package handler

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	harbormodels "github.com/goharbor/harbor/src/common/models"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/service"
)

type AuthHandler struct {
	maxGCDuration  time.Duration
	simUserService *service.SimUserService
}

func NewAuthHandler(sus *service.SimUserService) *AuthHandler {
	return &AuthHandler{
		simUserService: sus,
		maxGCDuration:  30 * time.Minute,
	}
}

// GetSessionToken Return sessin token
// @Summary Return sessin token include user info
// @Description used by admin to acquire user's credential
// @Id getUserSessionToken
// @Tags user
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "An unique ID for the request"
// @Param body body models.SessionTokenArgs true "user's info"
// @Success 200 {object} models.SessionToken "user's token with expired time"
// @Failure 401 {string} Unauthorized
// @Failure 500 {string} Internal server error
// @Header 401 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /sessiontoken [post]
func (a *AuthHandler) GetSessionToken(c *gin.Context) {
	var (
		err error
		req models.SessionTokenArgs
	)

	logger := middleware.LoggerFromContext(c)

	if err = c.Bind(&req); err != nil {
		logger.Errorf("bind request failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	u := harbormodels.User{
		UserID:          req.UserID,
		Username:        req.Username,
		Email:           req.Email,
		Password:        req.Password,
		PasswordVersion: req.PasswordVersion,
		Realname:        req.Realname,
		Deleted:         req.Deleted,
		Rolename:        req.Rolename,
		Role:            req.Role,
		SysAdminFlag:    req.SysAdminFlag,
		AdminRoleInAuth: req.AdminRoleInAuth,
		ResetUUID:       req.ResetUUID,
		Salt:            req.Salt,
		CreationTime:    req.CreationTime,
		UpdateTime:      req.UpdateTime,
		GroupIDs:        req.GroupIDs,
	}

	key, err := a.simUserService.SimulateUserSession(c, &u, a.maxGCDuration)
	if err != nil {
		logger.Errorf("get simulation user session failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	auth := models.SessionToken{
		Token:     key,
		ExpiredAt: time.Now().Add(a.maxGCDuration),
	}

	c.JSON(http.StatusOK, auth)
}
