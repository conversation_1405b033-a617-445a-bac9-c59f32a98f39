package handler

import (
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	projectmodels "github.com/goharbor/harbor/src/pkg/project/models"
	projectcontroller "github.com/goharbor/harbor/src/testing/controller/project"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/goharbor/harbor/src/testing/pkg/blob"
	"github.com/stretchr/testify/assert"
)

func Test_GetStorage(t *testing.T) {
	sh := &StatisticHandler{}

	blbMgr := &blob.Manager{}
	blbMgr.On("CalculateTotalSizeByProject", mock.Anything, mock.Anything, mock.Anything).Return(int64(100), nil)

	sh.blobMgr = blbMgr

	prjCtl := &projectcontroller.Controller{}

	prjCtl.On("Count", mock.Anything, mock.Anything).Return(int64(1), nil)
	prjCtl.On("List", mock.Anything, mock.Anything, mock.Anything).Return([]*projectmodels.Project{
		{
			ProjectID: int64(1),
			Name:      "test",
		},
	}, nil)

	sh.projectCtl = prjCtl

	var err error
	ctx, _ := gin.CreateTestContext(newGinResponseWriter())
	assert.NotNil(t, ctx)
	ctx.Request, err = http.NewRequest(http.MethodGet, "http://test.com/storage?page=1&page_size=10", nil)
	assert.NoError(t, err)

	sh.GetStorage(ctx)
	assert.Equal(t, 200, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	assert.NotNil(t, ctx)
	ctx.Request, err = http.NewRequest(http.MethodGet, "http://test.com/storage?page=1&page_size=10x", nil)
	assert.NoError(t, err)

	sh.GetStorage(ctx)
	assert.Equal(t, 400, ctx.Writer.Status())

	ctx, _ = gin.CreateTestContext(newGinResponseWriter())
	assert.NotNil(t, ctx)
	ctx.Request, err = http.NewRequest(http.MethodGet, "http://test.com/storage?page=1x&page_size=10", nil)
	assert.NoError(t, err)

	sh.GetStorage(ctx)
	assert.Equal(t, 400, ctx.Writer.Status())
}
