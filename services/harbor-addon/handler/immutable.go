package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/controller/immutable"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/service"
)

type ImmutableHandler struct {
	immutableCtl     immutable.Controller
	immutableService *service.ImmutableService
}

func NewImmutableHandler() *ImmutableHandler {
	return &ImmutableHandler{
		immutableCtl:     immutable.Ctr,
		immutableService: service.NewImmutableService(),
	}
}

// GetImmutableRule Return specific immutable rule detail information
// @Summary Return specific immutable rule detail information
// @Description This endpoint returns specific immutable rule information by immutable ID.
// @Id GetImmutableRule
// @Tags immutable
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "An unique ID for the request"
// @Param immutableId path string true "The id of the immutable rule"
// @Success 200 {object} models.ImmutableRule "Return matched immutable information."
// @Failure 401 {string} Unauthorized
// @Failure 500 {string} Internal server error
// @Header 401 {string} string
// @Header 500 {string} string
// @Router /immutable/rule/{immutableId} [get]
func (h *ImmutableHandler) GetImmutableRule(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	immutableId := c.Param("immutableId")
	id, err := strconv.ParseInt(immutableId, 10, 64)
	if err != nil {
		logger.Errorf("invalid immutable id: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	result, err := h.immutableCtl.GetImmutableRule(middleware.HarborContext(c), id)
	if err != nil {
		logger.Errorf("get immutable error: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	scopeSelectors := make(map[string][]*models.Selector)
	for key, val := range result.ScopeSelectors {
		scopeSelector := make([]*models.Selector, 0)
		for _, t := range val {
			sc := &models.Selector{
				Decoration: t.Decoration,
				Kind:       t.Kind,
				Pattern:    t.Pattern,
			}
			scopeSelector = append(scopeSelector, sc)
		}
		scopeSelectors[key] = scopeSelector
	}

	tagSelectors := make([]*models.Selector, 0)
	for _, t := range result.TagSelectors {
		tagSelector := &models.Selector{
			Decoration: t.Decoration,
			Kind:       t.Kind,
			Pattern:    t.Pattern,
		}
		tagSelectors = append(tagSelectors, tagSelector)
	}

	immutableRule := &models.ImmutableRule{
		ID:             result.ID,
		ProjectID:      result.ProjectID,
		Disabled:       result.Disabled,
		Priority:       result.Priority,
		Action:         result.Action,
		Template:       result.Template,
		TagSelectors:   tagSelectors,
		ScopeSelectors: scopeSelectors,
	}

	if err != nil {
		logger.Errorf("get immutable failed: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, immutableRule)
}

// ListImmutableRule Return immutable rule list
// @Summary Return immutable rule list
// @Description This endpoint returns immutable rule list
// @Id ListImmutableRule
// @Tags immutable
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "An unique ID for the request"
// @Param page query int false "The page number" default(1)
// @Param page_size query int false "The size of per page" default(10)
// @Success 200 {array} models.ImmutableRule "Return all  matched immutable information."
// @Failure 401 {string} Unauthorized
// @Failure 500 {string} Internal server error
// @Header 200 {integer} X-Total-Count "The total count of projects"
// @Header 200 {string} Link "Link refers to the previous page and next page"
// @Header 401 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /immutable/rule [get]
func (h *ImmutableHandler) ListImmutableRule(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	pageNoStr, pageSizeStr := c.DefaultQuery("page", "1"), c.DefaultQuery("page_size", "10")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page number: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page size : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	total, rules, err := h.immutableService.ListImmutableRules(middleware.HarborContext(c), pageNo, pageSize)
	c.Header("x-total-count", strconv.FormatInt(total, 10))
	c.JSON(http.StatusOK, rules)

}
