package handler

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/service"
)

type TagHandler struct {
	tagService service.TagService
}

func NewTagHandler(clients *harbor.HarborClient) *TagHandler {
	return &TagHandler{
		tagService: *service.NewTagService(clients),
	}
}

// ListTags 列举一个repo下的tag
// @Summary 列举一个repo下的tag
// @Description 列举一个repo下的tag
// @Id listTags
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param project_name path string true "空间名字"
// @Param repository_name path string true "仓库名字"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "页大小" default(10)
// @Param key query string false "搜索关键信息"
// @Param X-Request-Id header string false "request id"
// @Success 200 {array} models.ArtifactTag
// @Failure 400 {string} string
// @Failure 500 {string} string
// @Header 200 {integer} X-Total-Count "返回tag总条数"
// @Router /projects/{project_name}/repositories/{repository_name}/tags [get]
func (t *TagHandler) ListTags(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	projectName, repoName := c.Param("project_name"), c.Param("repository_name")
	pageNoStr, pageSizeStr, key := c.DefaultQuery("page", "1"), c.DefaultQuery("page_size", "10"), c.Query("key")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page number: %v", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page size : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	queryCondition := ""
	if key != "" {
		queryCondition = fmt.Sprintf("name=~%s", key)
	}

	username, password, ok := c.Request.BasicAuth()
	if !ok {
		username = "admin"
		password = os.Getenv("HARBOR_ADMIN_PASSWORD")
	}

	total, result, err := t.tagService.ListTags(middleware.HarborContext(c),
		&models.ListTagsParam{
			RequestId:   middleware.RequestIdFromContext(c),
			ProjectName: projectName,
			RepoName:    repoName,
			Keyword:     queryCondition,
			PageNo:      pageNo,
			PageSize:    pageSize,
		},
		username,
		password,
	)
	if err != nil {
		logger.Errorf("list tag failed: %v", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.Header("x-total-count", strconv.FormatInt(total, 10))
	c.JSON(http.StatusOK, result)
}

// GetTag 获取tag版本详情
// @Summary 获取tag版本详情
// @Description 获取tag版本详情
// @Id getTag
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param project_name path string true "空间名字"
// @Param repository_name path string true "仓库名字"
// @Param tag_name path string true "版本名字"
// @Param X-Request-Id header string false "request id"
// @Success 200 {object} models.Tag
// @Failure 400 {string} string
// @Failure 500 {string} string
// @Router /projects/{project_name}/repositories/{repository_name}/tags/{tag_name} [get]
func (t *TagHandler) GetTag(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	projectName, repoName, tagName := c.Param("project_name"), c.Param("repository_name"), c.Param("tag_name")

	tag, err := t.tagService.GetTag(middleware.HarborContext(c),
		&models.GetTagParam{
			RequestId:   middleware.RequestIdFromContext(c),
			ProjectName: projectName,
			RepoName:    repoName,
			TagName:     tagName,
		},
	)
	if err != nil {
		logger.Errorf("get tag failed: %v", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, tag)
}

// CountTags 计算一个repo下的tag的数量
// @Summary 计算一个repo下的tag的数量
// @Description 计算一个repo下的tag的数量
// @Id countTags
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param project_name path string true "空间名字"
// @Param repository_name path string true "仓库名字"
// @Param X-Request-Id header string false "request id"
// @Success 200 {integer} integer "tag的数量"
// @Failure 400 {string} string
// @Failure 500 {string} string
// @Router /projects/{project_name}/repositories/{repository_name}/count_tags [get]
func (t *TagHandler) CountTags(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	projectName, repoName := c.Param("project_name"), c.Param("repository_name")

	total, err := t.tagService.CountTags(middleware.HarborContext(c),
		&models.CountTagsParam{
			RequestId:   middleware.RequestIdFromContext(c),
			ProjectName: projectName,
			RepoName:    repoName,
		},
	)
	if err != nil {
		logger.Errorf("count tag failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, total)
}

// CountRepoTags 计算一组repo下的tag的数量
// @Summary 计算一组repo下的tag的数量
// @Description 计算一组repo下的tag的数量
// @Id countRepoTags
// @Tags tag
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param project_name path string true "空间名字"
// @Param repository_names query string true "仓库名字数组"
// @Param X-Request-Id header string false "request id"
// @Success 200 {object} map[string]integer "repo对应tag数量"
// @Failure 400 {string} string
// @Failure 500 {string} string
// @Router /projects/{project_name}/count_repositories_tags [get]
func (t *TagHandler) CountRepoTags(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	projectName, repoNameStrs := c.Param("project_name"), c.Query("repository_names")

	if len(repoNameStrs) == 0 {
		c.JSON(http.StatusOK, 0)
		return
	}
	repoNames := strings.Split(repoNameStrs, ",")

	total, err := t.tagService.CountRepoTagsMap(middleware.HarborContext(c),
		&models.CountRepoTagsParam{
			RequestId:   middleware.RequestIdFromContext(c),
			ProjectName: projectName,
			RepoNames:   repoNames,
		},
	)
	if err != nil {
		logger.Errorf("count tag failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, total)
}
