package trigger

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/common/job"
	jobmodels "github.com/goharbor/harbor/src/common/job/models"
	"github.com/goharbor/harbor/src/common/models"
	jjob "github.com/goharbor/harbor/src/jobservice/job"
	"github.com/goharbor/harbor/src/lib/errors"
	"github.com/goharbor/harbor/src/lib/q"

	ccrjob "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/jobservice/job"
	daojob "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/dao/job"
	managerjob "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/manager/job"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/manager/policy"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
)

var statusMap = map[string]string{
	job.JobServiceStatusPending:   models.JobPending,
	job.JobServiceStatusScheduled: models.JobScheduled,
	job.JobServiceStatusRunning:   models.JobRunning,
	job.JobServiceStatusStopped:   models.JobStopped,
	job.JobServiceStatusError:     models.JobError,
	job.JobServiceStatusSuccess:   models.JobFinished,
}

type JobHandler struct {
	jobMgr    managerjob.Manager
	policyMgr policy.Manager
	client    job.Client
}

func NewJobHandler() *JobHandler {
	return &JobHandler{
		policyMgr: policy.NewPolicyManager(""),
		jobMgr:    managerjob.NewJobManager(),
		client:    job.NewDefaultClient(os.Getenv("JOBSERVICE_URL"), os.Getenv("ADDON_SECRET")),
	}
}

// JobServiceCallbackUpdate handles the hook of trigger job
func (j *JobHandler) JobServiceCallbackUpdate(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		logger.Errorf("Failed to get job ID, error: %v", err)
		c.AbortWithStatus(http.StatusOK)
		return
	}

	var data jjob.StatusChange
	if err := c.BindJSON(&data); err != nil {
		logger.Errorf("Failed to bind job status change with error: %v", err)
		c.AbortWithStatus(200)
		return
	}

	rawStatus := data.Status
	status, ok := statusMap[rawStatus]
	if !ok {
		logger.Debugf("drop the job status update event: job id-%d, status-%s", id, status)
		c.AbortWithStatus(200)
		return
	}

	logger.Debugf("received trigger job status update event: job-%d, status-%s", id, status)

	if err = j.jobMgr.Update(ctx, &daojob.TriggerJob{
		ID:         id,
		Status:     status,
		UpdateTime: time.Now(),
	}, "Status", "UpdateTime"); err != nil {
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		logger.Errorf("Failed to update trigger job status, id: %d, status: %s", id, status)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}
	c.AbortWithStatus(http.StatusOK)
}

// List List trigger jobs.
// @Summary List trigger jobs.
// @Description This endpoint returns trigger jobs.
// @Id listJob
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param policy_id path string true "The ID of the policy"
// @Param page query int false "page number" default(1)
// @Param page_size query int false "page size" default(10)
// @Param q query string false "Query string to query resources. Supported query patterns are "exact match(k=v)", "fuzzy match(k=~v)", "range(k=[min~max])", "list with union releationship(k={v1 v2 v3})" and "list with intersetion relationship(k=(v1 v2 v3))". The value of range and list can be string(enclosed by " or '), integer or time(in format "2020-04-09 02:36:00"). All of these query patterns should be put in the query string "q=xxx" and splitted by ",". e.g. q=k1=v1,k2=~v2,k3=[min~max]"
// @Param X-Request-Id header string false "The request id"
// @Success 200 {array} job.TriggerJob TriggerJob
// @Failure 400 {string} Bad request
// @Failure 404 {string} Not found
// @Failure 500 {string} Internal server error
// @Header 200 {integer} X-Total-Count "The total of trigger job"
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /triggers/policies/{policy_id}/jobs [get]
func (j *JobHandler) List(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	policyId, err := strconv.ParseInt(c.Param("policy_id"), 10, 64)
	if err != nil {
		logger.Errorf("Failed to get policy ID, error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if _, err = j.policyMgr.Get(ctx, policyId); err != nil {
		logger.Errorf("Failed to get policy, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	queryStr := c.Query("q")
	pageNoStr, pageSizeStr := c.DefaultQuery("page", "1"), c.DefaultQuery("page_size", "10")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page number: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page size : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	query, err := q.Build(queryStr, "", pageNo, pageSize)
	if err != nil {
		logger.Errorf("build query failed: %v", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	query.Keywords["PolicyID"] = policyId

	total, jobs, err := j.jobMgr.List(ctx, query)
	if err != nil {
		logger.Errorf("list trigger job failed: %v", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.Header("X-Total-Count", strconv.FormatInt(total, 10))
	c.JSON(http.StatusOK, jobs)
}

//RetryExecuteJob trigger job.
//@Summary RetryExecuteJob trigger job.
//@Description This endpoint retry execute trigger job.
//@Id retryExecuteJob
//@Tags trigger
//@Accept application/json
//@Produce application/json
//@Security BasicAuth
//@Param X-Request-Id header string false "The request id"
//@Param policy_id path string true "The ID of the policy"
//@Param job_id path string true "The ID of the job"
//@Success 200 {string} Trigger job retry execute successfully.
//@Failure 400 {string} Bad request
// @Failure 404 {string} Not found
//@Failure 500 {string} Internal server error
//@Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
//@Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
//@Router /triggers/policies/{policy_id}/jobs/{job_id}/retry [patch]
func (j *JobHandler) RetryExecuteJob(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	policyId, err := strconv.ParseInt(c.Param("policy_id"), 10, 64)
	if err != nil {
		logger.Errorf("Failed to get policy ID, error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	policy, err := j.policyMgr.Get(ctx, policyId)
	if err != nil {
		logger.Errorf("Failed to get policy, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	jobId, err := strconv.ParseInt(c.Param("job_id"), 10, 64)
	if err != nil {
		logger.Errorf("Failed to get job ID, error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	job, err := j.jobMgr.Get(ctx, jobId)
	if err != nil {
		logger.Errorf("Failed to get job, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if job.PolicyID != policyId {
		logger.Errorf("job policy ID: %d not equal path policy ID: %d", job.PolicyID, policyId)
		c.AbortWithStatusJSON(http.StatusBadRequest,
			fmt.Errorf("job policy ID: %d not equal path policy ID: %d", job.PolicyID, policyId))
		return
	}

	if job.Status != models.JobError {
		logger.Errorf("retry execute job status %s ,job status must be error", job.Status)
		c.AbortWithStatusJSON(http.StatusBadRequest,
			fmt.Errorf("retry execute job %s ,job status must be error", job.Status))
		return
	}

	if err = j.jobMgr.Update(ctx, &daojob.TriggerJob{
		ID:     jobId,
		Status: models.JobPending,
		UUID:   "",
	}, "Status", "UUID"); err != nil {
		logger.Errorf("Failed to update job, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	data := &jobmodels.JobData{
		Metadata: &jobmodels.JobMetadata{
			JobKind: jjob.KindGeneric,
		},
		Name: ccrjob.TriggerJob,
		Parameters: map[string]interface{}{
			"payload": job.JobDetailDB,
			"address": policy.Targets[0].Address, // 默认只处理第一条
			// Users can define a auth header in http statement in trigger policy.
			// So it will be sent in header in http request.
			"headers":          policy.Targets[0].Headers,
			"skip_cert_verify": policy.Targets[0].SkipCertVerify,
		},
		StatusHook: fmt.Sprintf("%s/%d", policy.JobStatusHookURL, jobId),
	}

	// submit hook job to jobservice
	jobUUID, err := j.client.SubmitJob(data)
	if err != nil {
		logger.Errorf("failed to submit job with trigger event: %v", err)
		e := j.jobMgr.Update(ctx, &daojob.TriggerJob{
			ID:     jobId,
			Status: models.JobError,
		}, "Status")
		if e != nil {
			logger.Errorf("failed to update the trigger job status %d: %v", jobId, e)
			if errors.IsNotFoundErr(e) {
				c.AbortWithStatusJSON(http.StatusNotFound, e)
				return
			}
			c.AbortWithError(http.StatusInternalServerError, e)
			return
		}
		logger.Errorf("failed to submit to jobservice job ID %d: data: %v", jobId, data)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if err := j.jobMgr.Update(ctx, &daojob.TriggerJob{
		ID:   jobId,
		UUID: jobUUID,
	}, "UUID"); err != nil {
		logger.Errorf("failed to update the trigger job %d: %v", jobId, err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, "success")
}
