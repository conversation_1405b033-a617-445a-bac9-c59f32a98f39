package trigger

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/goharbor/harbor/src/lib/errors"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/common/security"
	"github.com/goharbor/harbor/src/common/utils"
	"github.com/goharbor/harbor/src/controller/event"
	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/project"

	daopolicy "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/dao/policy"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/manager/policy"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/trigger/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
)

type PolicyHandler struct {
	policyMgr       policy.Manager
	projectMgr      project.Manager
	supportedEvents map[string]struct{}
	// SupportedNotifyTypes is a map to store trigger type, eg. trigger etc
	supportedNotifyTypes map[string]struct{}
	jobStatusHookURL     string
}

func NewPolicyHandler(addonAddr, whiteList string) *PolicyHandler {

	if !strings.HasPrefix(addonAddr, "http") {
		addonAddr = fmt.Sprintf("%s%s", "http://", addonAddr)
	}
	jobStatusHookURL := fmt.Sprintf("%s%s", addonAddr, "/service/trigger/job")

	return &PolicyHandler{
		policyMgr:            policy.NewPolicyManager(whiteList),
		projectMgr:           project.Mgr,
		supportedEvents:      initSupportedEvents(),
		supportedNotifyTypes: initSupportedNotifyType(),
		jobStatusHookURL:     jobStatusHookURL,
	}
}

// List trigger policies.
// @Summary List trigger policies.
// @Description This endpoint returns trigger policies.
// @Id listPolicy
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param page query int false "page number" default(1)
// @Param page_size query int false "page size" default(10)
// @Param q query string false "Query string to query resources. Supported query patterns are "exact match(k=v)", "fuzzy match(k=~v)", "range(k=[min~max])", "list with union releationship(k={v1 v2 v3})" and "list with intersetion relationship(k=(v1 v2 v3))". The value of range and list can be string(enclosed by " or '), integer or time(in format "2020-04-09 02:36:00"). All of these query patterns should be put in the query string "q=xxx" and splitted by ",". e.g. q=k1=v1,k2=~v2,k3=[min~max]"
// @Param X-Request-Id header string false "The request id"
// @Success 200 {array} policy.TriggerPolicy TriggerPolicy
// @Failure 400 {string} Bad request
// @Failure 500 {string} Internal server error
// @Header 200 {integer} X-Total-Count "The total of trigger policy"
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /triggers/policies [get]
func (rh *PolicyHandler) List(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)
	queryStr := c.Query("q")

	pageNoStr, pageSizeStr := c.DefaultQuery("page", "1"), c.DefaultQuery("page_size", "10")

	pageNo, err := strconv.ParseInt(pageNoStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page number: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64)
	if err != nil {
		logger.Errorf("invalid page size : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	query, err := q.Build(queryStr, "", pageNo, pageSize)
	if err != nil {
		logger.Errorf("build query failed: %v", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	total, policies, err := rh.policyMgr.List(ctx, query)
	if err != nil {
		logger.Errorf("list trigger policy failed: %v", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.Header("X-Total-Count", strconv.FormatInt(total, 10))
	c.JSON(http.StatusOK, policies)
}

// Create trigger policy.
// @Summary Create trigger policy.
// @Description This endpoint create a trigger policy
// @Id createPolicy
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param policy body policy.TriggerPolicy true "Properties "targets" and "event_types" needed."
// @Success 200 {integer} The ID of trigger policy create successfully.
// @Failure 400 {string} Bad request
// @Failure 409 {string} Conflict
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /triggers/policies [post]
func (rh *PolicyHandler) Create(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	policy := &daopolicy.TriggerPolicy{}
	if err := c.Bind(&policy); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if err := rh.validateTargets(policy.Targets); err != nil {
		logger.Errorf("validate targets failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if err := rh.policyMgr.Test(policy); err != nil {
		logger.Errorf("test policy targets failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	if err := rh.validateEventTypes(policy); err != nil {
		logger.Errorf("validate event types failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if policy.ID != 0 {
		c.AbortWithError(http.StatusBadRequest, fmt.Errorf("cannot accept policy creating request with ID: %d", policy.ID))
		return
	}

	var username string
	secCtx, ok := security.FromContext(ctx)
	if ok {
		username = secCtx.GetUsername()
	}

	policy.Creator = username
	policy.JobStatusHookURL = rh.jobStatusHookURL

	id, err := rh.policyMgr.Create(ctx, policy)
	if err != nil {
		if errors.IsConflictErr(err) {
			logger.Errorf("Conflict, the policy :%s is exist", policy.Name)
			c.AbortWithStatusJSON(http.StatusConflict, fmt.Sprintf("Conflict, the policy name: %s is exist", policy.Name))
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, id)
}

// Test trigger policy targets.
// @Summary Test trigger policy targets.
// @Description This endpoint test a trigger policy targets
// @Id testPolicyTargets
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param target body policy.TriggerTarget true "trigger policy targets"
// @Success 200 {string} Trigger policy targets test successfully.
// @Failure 400 {string} Bad request
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /triggers/policies/targets [post]
func (rh *PolicyHandler) Test(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)

	var target daopolicy.TriggerTarget
	if err := c.Bind(&target); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if err := rh.validateTargets([]daopolicy.TriggerTarget{target}); err != nil {
		logger.Errorf("validate targets failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}
	policy := &daopolicy.TriggerPolicy{
		Targets: []daopolicy.TriggerTarget{target},
	}

	err := rh.policyMgr.Test(policy)
	if err != nil {
		logger.Errorf("test policy targets failed: %s", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, "success")
}

// Get trigger policy.
// @Summary get trigger policy.
// @Description This endpoint returns trigger policy.
// @Id getPolicy
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param policy_id path string true "The ID of the policy"
// @Success 200 {object} policy.TriggerPolicy TriggerPolicy
// @Failure 400 {string} Bad request
// @Failure 404 {string} Not found
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /triggers/policies/{policy_id} [get]
func (rh *PolicyHandler) Get(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	policyId, err := strconv.ParseInt(c.Param("policy_id"), 10, 64)
	if err != nil {
		logger.Errorf("Failed to get policy ID, error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	policy, err := rh.policyMgr.Get(ctx, policyId)
	if err != nil {
		logger.Errorf("Failed to get policy, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, policy)
}

// Update trigger policy.
// @Summary Update trigger policy.
// @Description This endpoint update trigger policy.
// @Id updatePolicy
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param policy_id path string true "The ID of the policy"
// @Param policy body policy.TriggerPolicy true "Properties "targets" and "event_types" needed."
// @Success 200 {string} Trigger policy update successfully.
// @Failure 400 {string} Bad request
// @Failure 404 {string} Not found
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /triggers/policies/{policy_id} [put]
func (rh *PolicyHandler) Update(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	policyId, err := strconv.ParseInt(c.Param("policy_id"), 10, 64)
	if err != nil {
		logger.Errorf("Failed to get policy ID, error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	_, err = rh.policyMgr.Get(ctx, policyId)
	if err != nil {
		logger.Errorf("Failed to get policy, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	policy := &daopolicy.TriggerPolicy{}
	if err := c.Bind(&policy); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if err := rh.validateTargets(policy.Targets); err != nil {
		logger.Errorf("validate targets failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if err := rh.policyMgr.Test(policy); err != nil {
		logger.Errorf("test policy targets failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if err := rh.validateEventTypes(policy); err != nil {
		logger.Errorf("validate event types failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	policy.ID = policyId
	policy.JobStatusHookURL = rh.jobStatusHookURL

	if err = rh.policyMgr.Update(ctx, policy); err != nil {
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, "success")
}

// Delete trigger policy.
// @Summary Delete trigger policy.
// @Description This endpoint delete trigger policy.
// @Id deletePolicy
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param policy_id path string true "The ID of the policy"
// @Success 200 {string} Trigger policy delete successfully.
// @Failure 400 {string} Bad request
// @Failure 404 {string} Not found
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /triggers/policies/{policy_id} [delete]
func (rh *PolicyHandler) Delete(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	policyId, err := strconv.ParseInt(c.Param("policy_id"), 10, 64)
	if err != nil {
		logger.Errorf("Failed to get policy ID, error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	_, err = rh.policyMgr.Get(ctx, policyId)
	if err != nil {
		logger.Errorf("Failed to get policy, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if err := rh.policyMgr.Delete(ctx, policyId); err != nil {
		logger.Errorf("Failed to detele policy, error: %v", err)
		if errors.IsNotFoundErr(err) {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, "success")
}

// BatchDelete trigger policy.
// @Summary BatchDelete trigger policy.
// @Description This endpoint batch delete trigger policy.
// @Id batchDeletePolicy
// @Tags trigger
// @Accept application/json
// @Produce application/json
// @Security BasicAuth
// @Param X-Request-Id header string false "The request id"
// @Param request body models.BatchDeleteRequest true "The trigger policy ids"
// @Success 200 {string} Trigger policy delete successfully.
// @Failure 400 {string} Bad request
// @Failure 500 {string} Internal server error
// @Header 400 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Header 500 {string} X-Request-Id "The ID of the corresponding request for the response"
// @Router /triggers/policies [delete]
func (rh *PolicyHandler) BatchDelete(c *gin.Context) {
	logger := middleware.LoggerFromContext(c)
	ctx := middleware.HarborContext(c)

	var deleteRequest models.BatchDeleteRequest
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("Bind batch delete request item failed: %s", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, err)
		return
	}

	if err := rh.policyMgr.BatchDelete(ctx, deleteRequest.Items); err != nil {
		logger.Errorf("Batch delete trigger policy failed: %s", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	c.JSON(http.StatusOK, "success")
}

func (rh *PolicyHandler) validateTargets(targets []daopolicy.TriggerTarget) error {

	for _, target := range targets {
		url, err := utils.ParseEndpoint(target.Address)
		if err != nil {
			return err
		}
		// Prevent SSRF security issue #3755
		target.Address = url.Scheme + "://" + url.Host + url.Path

		_, ok := rh.supportedNotifyTypes[target.Type]
		if !ok {
			return fmt.Errorf("unsupport target type %s with policy", target.Type)
		}
	}

	return nil
}

func (rh *PolicyHandler) validateEventTypes(policy *daopolicy.TriggerPolicy) error {
	if len(policy.EventTypes) == 0 {
		return fmt.Errorf("empty trigger event types with policy %s", policy.Name)
	}

	for _, eventType := range policy.EventTypes {
		_, ok := rh.supportedEvents[eventType]
		if !ok {
			return fmt.Errorf("unsupport event type %s", eventType)
		}
	}

	return nil
}

func initSupportedEvents() map[string]struct{} {
	eventTypes := []string{
		event.TopicPushArtifact,
		event.TopicPullArtifact,
		event.TopicDeleteArtifact,
		event.TopicUploadChart,
		event.TopicDeleteChart,
		event.TopicDownloadChart,
	}

	var supportedEventTypes = make(map[string]struct{})
	for _, eventType := range eventTypes {
		supportedEventTypes[eventType] = struct{}{}
	}

	return supportedEventTypes
}

func initSupportedNotifyType() map[string]struct{} {
	notifyTypes := []string{model.NotifierTypeTrigger}

	var supportedNotifyTypes = make(map[string]struct{})
	for _, notifyType := range notifyTypes {
		supportedNotifyTypes[notifyType] = struct{}{}
	}

	return supportedNotifyTypes
}
