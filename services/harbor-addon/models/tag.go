package models

import (
	"time"

	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
)

type ListTagsByProjectNameAndRepoNameParam struct {
	RequestId   string
	ProjectName string
	RepoName    string
}

type ListTagsParam struct {
	RequestId   string
	ProjectName string
	RepoName    string
	Keyword     string
	PageNo      int64
	PageSize    int64
}

type CountTagsParam struct {
	RequestId   string
	ProjectName string
	RepoName    string
}

type CountRepoTagsParam struct {
	RequestId   string
	ProjectName string
	RepoNames   []string
}

type GetTagParam struct {
	RequestId   string
	ProjectName string
	RepoName    string
	TagName     string
}

// Tag model in database
type Tag struct {
	ID                int64     `json:"id"`
	ProjectID         int64     `json:"project_id"`
	RepositoryID      int64     `json:"repository_id"`
	ArtifactID        int64     `json:"artifact_id"`
	Name              string    `json:"name"`
	PushTime          time.Time `json:"push_time"`
	PullTime          time.Time `json:"pull_time"`
	Type              string    `json:"type"`                // image, chart, etc
	MediaType         string    `json:"media_type"`          // the media type of artifact. Mostly, it's the value of `manifest.config.mediatype`
	ManifestMediaType string    `json:"manifest_media_type"` // the media type of manifest/index
	RepositoryName    string    `json:"repository_name"`
	Digest            string    `json:"digest"`
	Size              int64     `json:"size"`
	AcceleratorStatus string    `json:"accelerator_status"`
}

// ArtifactTag Artifact and Tag
//
// swagger:model ArtifactTag
type ArtifactTag struct {
	Art *harbormodel.Artifact `json:"art,omitempty"`
	Tag *Tag                  `json:"tag,omitempty"`
}
