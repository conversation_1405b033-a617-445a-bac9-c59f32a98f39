package models

import "time"

type SessionToken struct {
	Token     string    `json:"token,omitempty"`
	ExpiredAt time.Time `json:"expiredAt,omitempty"`
}

type SessionTokenArgs struct {
	UserID          int    `json:"userID,omitempty"`
	Username        string `json:"username,omitempty"`
	Email           string `json:"email,omitempty"`
	Password        string `json:"password,omitempty"`
	PasswordVersion string `json:"passwordVersion,omitempty"`
	Realname        string `json:"realname,omitempty"`
	Comment         string `json:"comment,omitempty"`
	Deleted         bool   `json:"deleted,omitempty"`
	Rolename        string `json:"rolename,omitempty"`
	// if this field is named as "RoleID", beego orm can not map role_id
	// to it.
	Role         int  `json:"role,omitempty"`
	SysAdminFlag bool `json:"sysAdminFlag,omitempty"`
	// AdminRoleInAuth to store the admin privilege granted by external authentication provider
	AdminRoleInAuth bool      `json:"adminRoleInAuth,omitempty"`
	ResetUUID       string    `json:"resetUUID,omitempty"`
	Salt            string    `json:"salt,omitempty"`
	CreationTime    time.Time `json:"creationTime,omitempty"`
	UpdateTime      time.Time `json:"updateTime,omitempty"`
	GroupIDs        []int     `json:"groupIDs,omitempty"`
}
