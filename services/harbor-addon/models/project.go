// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"strings"

	"github.com/go-openapi/errors"
	strfmt "github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
	"github.com/goharbor/harbor/src/controller/project"
	"github.com/goharbor/harbor/src/lib"
	"github.com/goharbor/harbor/src/pkg/scan/vuln"
)

// ProjectDetail project detail
// swagger:model Project
type ProjectDetail struct {

	// The total number of charts under this project.
	ChartCount int64 `json:"chart_count,omitempty"`

	// The creation time of the project.
	// Format: date-time
	CreationTime strfmt.DateTime `json:"creation_time,omitempty" format:"date-time"`

	// The role ID with highest permission of the current user who triggered the API (for UI).  This attribute is deprecated and will be removed in future versions.
	CurrentUserRoleID int64 `json:"current_user_role_id,omitempty"`

	// The list of role ID of the current user who triggered the API (for UI)
	CurrentUserRoleIds []int32 `json:"current_user_role_ids"`

	// The CVE allowlist of this project.
	CVEAllowlist *CVEAllowlist `json:"cve_allowlist,omitempty"`

	// A deletion mark of the project.
	Deleted bool `json:"deleted,omitempty"`

	// The metadata of the project.
	Metadata *ProjectMetadata `json:"metadata,omitempty"`

	// The name of the project.
	Name string `json:"name,omitempty"`

	// The owner ID of the project always means the creator of the project.
	OwnerID int32 `json:"owner_id,omitempty"`

	// The owner name of the project.
	OwnerName string `json:"owner_name,omitempty"`

	// Project ID
	ProjectID int32 `json:"project_id,omitempty" format:"int32"`

	// The ID of referenced registry when the project is a proxy cache project.
	RegistryID int64 `json:"registry_id,omitempty"`

	// The number of the repositories under this project.
	RepoCount int64 `json:"repo_count,omitempty"`

	// Correspond to the UI about whether the project's publicity is  updatable (for UI)
	Togglable bool `json:"togglable,omitempty"`

	// The update time of the project.
	// Format: date-time
	UpdateTime strfmt.DateTime `json:"update_time,omitempty" format:"date-time"`
}

// Validate validates this project
func (m *ProjectDetail) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateCreationTime(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateCVEAllowlist(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateMetadata(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateUpdateTime(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ProjectDetail) validateCreationTime(formats strfmt.Registry) error {

	if swag.IsZero(m.CreationTime) { // not required
		return nil
	}

	if err := validate.FormatOf("creation_time", "body", "date-time", m.CreationTime.String(), formats); err != nil {
		return err
	}

	return nil
}

func (m *ProjectDetail) validateCVEAllowlist(formats strfmt.Registry) error {

	if swag.IsZero(m.CVEAllowlist) { // not required
		return nil
	}

	if m.CVEAllowlist != nil {
		if err := m.CVEAllowlist.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("cve_allowlist")
			}
			return err
		}
	}

	return nil
}

func (m *ProjectDetail) validateMetadata(formats strfmt.Registry) error {

	if swag.IsZero(m.Metadata) { // not required
		return nil
	}

	if m.Metadata != nil {
		if err := m.Metadata.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("metadata")
			}
			return err
		}
	}

	return nil
}

func (m *ProjectDetail) validateUpdateTime(formats strfmt.Registry) error {

	if swag.IsZero(m.UpdateTime) { // not required
		return nil
	}

	if err := validate.FormatOf("update_time", "body", "date-time", m.UpdateTime.String(), formats); err != nil {
		return err
	}

	return nil
}

// MarshalBinary interface implementation
func (m *ProjectDetail) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ProjectDetail) UnmarshalBinary(b []byte) error {
	var res ProjectDetail
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}

// Project model
type ProjectSwagger struct {
	*project.Project
}

// ToSwagger converts the project to the swagger model
func (p *ProjectSwagger) ToSwagger() *ProjectDetail {
	var currentUserRoleIds []int32
	for _, role := range p.RoleList {
		currentUserRoleIds = append(currentUserRoleIds, int32(role))
	}

	var md *ProjectMetadata
	if p.Metadata != nil {
		var m ProjectMetadata
		lib.JSONCopy(&m, p.Metadata)

		// Transform the severity to severity of CVSS v3.0 Ratings
		if m.Severity != nil {
			severity := strings.ToLower(vuln.ParseSeverityVersion3(*m.Severity).String())
			m.Severity = &severity
		}

		md = &m
	}

	var allowlist CVEAllowlist
	if err := lib.JSONCopy(&allowlist, p.CVEAllowlist); err != nil {
		//log.Warningf("failed to copy CVEAllowlist form %T", p.CVEAllowlist)
	}

	return &ProjectDetail{
		ChartCount:         int64(p.ChartCount),
		CreationTime:       strfmt.DateTime(p.CreationTime),
		CurrentUserRoleID:  int64(p.Role),
		CurrentUserRoleIds: currentUserRoleIds,
		CVEAllowlist:       &allowlist,
		Metadata:           md,
		Name:               p.Name,
		OwnerID:            int32(p.OwnerID),
		OwnerName:          p.OwnerName,
		ProjectID:          int32(p.ProjectID),
		RegistryID:         p.RegistryID,
		RepoCount:          p.RepoCount,
		UpdateTime:         strfmt.DateTime(p.UpdateTime),
	}
}

// NewProjectSwagger ...
func NewProjectSwagger(p *project.Project) *ProjectSwagger {
	return &ProjectSwagger{p}
}
