// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"strconv"

	"github.com/go-openapi/errors"
	strfmt "github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
	"github.com/goharbor/harbor/src/controller/project"
)

// ProjectOverview project overview
// swagger:model ProjectOverview
type ProjectOverview struct {

	// The creation time of the project.
	// Format: date-time
	CreationTime strfmt.DateTime `json:"creation_time,omitempty" format:"date-time"`

	// A deletion mark of the project.
	Deleted bool `json:"deleted,omitempty"`

	// Whether scan images automatically when pushing. The valid values are "true", "false".
	AutoScan string `json:"auto_scan,omitempty"`

	// The public status of the project. The valid values are "true", "false".
	Public string `json:"public,omitempty"`

	// The name of the project.
	Name string `json:"name,omitempty"`

	// The owner ID of the project always means the creator of the project.
	OwnerID int32 `json:"owner_id,omitempty"`

	// The owner name of the project.
	OwnerName string `json:"owner_name,omitempty"`

	// Project ID
	ProjectID int32 `json:"project_id,omitempty" format:"int32"`

	// The ID of referenced registry when the project is a proxy cache project.
	RegistryID int64 `json:"registry_id,omitempty"`

	// The update time of the project.
	// Format: date-time
	UpdateTime strfmt.DateTime `json:"update_time,omitempty"  format:"date-time"`
}

// Validate validates this project
func (m *ProjectOverview) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateCreationTime(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateUpdateTime(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ProjectOverview) validateCreationTime(formats strfmt.Registry) error {

	if swag.IsZero(m.CreationTime) { // not required
		return nil
	}

	if err := validate.FormatOf("creation_time", "body", "date-time", m.CreationTime.String(), formats); err != nil {
		return err
	}

	return nil
}

func (m *ProjectOverview) validateUpdateTime(formats strfmt.Registry) error {

	if swag.IsZero(m.UpdateTime) { // not required
		return nil
	}

	if err := validate.FormatOf("update_time", "body", "date-time", m.UpdateTime.String(), formats); err != nil {
		return err
	}

	return nil
}

// MarshalBinary interface implementation
func (m *ProjectOverview) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ProjectOverview) UnmarshalBinary(b []byte) error {
	var res ProjectOverview
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}

// ProjectOverview model
type ProjectOverviewSwagger struct {
	*project.Project
}

// ToSwagger converts the project to the swagger model
func (p *ProjectOverviewSwagger) ToSwagger() *ProjectOverview {

	return &ProjectOverview{
		CreationTime: strfmt.DateTime(p.CreationTime),
		Name:         p.Name,
		OwnerID:      int32(p.OwnerID),
		OwnerName:    p.OwnerName,
		ProjectID:    int32(p.ProjectID),
		RegistryID:   p.RegistryID,
		UpdateTime:   strfmt.DateTime(p.UpdateTime),
		Public:       strconv.FormatBool(p.IsPublic()),
		AutoScan:     strconv.FormatBool(p.AutoScan()),
	}
}

// newProject ...
func NewProjectOverviewSwagger(p *project.Project) *ProjectOverviewSwagger {
	return &ProjectOverviewSwagger{p}
}
