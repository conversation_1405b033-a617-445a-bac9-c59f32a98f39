package models

import "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"

type Statistic struct {
	model.Statistic `json:",inline"`
	TotalStorage    int64 `json:"total_storage,omitempty"`
	ChartCount      int64 `json:"total_chart_count,omitempty"`
}

type ProjectStorage struct {
	ProjectID int64 `json:"project_id,omitempty"`
	Image     int64 `json:"image,omitempty"`
	Chart     int64 `json:"chart,omitempty"`
	Total     int64 `json:"total,omitempty"`
}

type StorageDetail struct {
	Total        int64            `json:"total,omitempty"`
	PageNo       int64            `json:"pageNo,omitempty"`
	ProjectItems []ProjectStorage `json:"projectItems,omitempty"`
}
