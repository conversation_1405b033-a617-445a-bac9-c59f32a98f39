package router

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/goharbor/harbor/src/pkg/project"
	"github.com/goharbor/harbor/src/pkg/project/metadata"
	"github.com/goharbor/harbor/src/pkg/user"
	"gorm.io/gorm"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/ginprom"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/auth"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/handler"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/handler/accelerator"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/handler/trigger"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/service"
)

func NewRouter(conf *config.Config,
	db *gorm.DB,
	k8scli client.Client,
	authorizer *auth.Authorizer,
	authDomain string,
	privateKey []byte,
	projectMgr project.Manager,
	metaMgr metadata.Manager,
	redisCli redis.UniversalClient,
	csrfService *utils.HarborCsrf) *gin.Engine {
	r := gin.New()
	r.UseRawPath = true

	r.GET("/ping", func(c *gin.Context) {
		c.JSON(http.StatusOK, "pong")
	})

	r.GET("/metrics", ginprom.PromHanlder)

	r.Use(middleware.LoggerMiddleware(), ginprom.PromMiddleware(nil))

	serviceGroup := r.Group("/service/", middleware.OrmMiddleware())
	{
		jobHandler := trigger.NewJobHandler()
		serviceGroup.POST("/trigger/job/:id", jobHandler.JobServiceCallbackUpdate)
	}

	harborCli := harbor.NewHarborClientWithoutAuth(conf.HarborHost, "http")
	v1Group := r.Group("/addon/v1",
		middleware.OrmMiddleware(),
		middleware.BasicAuthMiddleware(user.New()),
		middleware.SessionAuth(redisCli),
		middleware.SecurityMiddleware(),
	)

	simUserService := service.NewSimUserService(redisCli)

	{

		statisticGroup := v1Group.Group("/products/statistic")

		statisticHandler := handler.NewStatisticHandler(harborCli)
		statisticGroup.GET("", statisticHandler.GetStatistic)

		storageGroup := v1Group.Group("/storage")
		storageGroup.GET("", middleware.RequireAdminMiddleware(), statisticHandler.GetStorage)

		triggerGroup := v1Group.Group("/triggers")
		{
			triggerHandler := trigger.NewPolicyHandler(conf.AddonAddr, conf.WhiteList)
			triggerGroup.POST("/policies", triggerHandler.Create)
			triggerGroup.GET("/policies", triggerHandler.List)
			triggerGroup.GET("/policies/:policy_id", triggerHandler.Get)
			triggerGroup.PUT("/policies/:policy_id", triggerHandler.Update)
			triggerGroup.DELETE("/policies/:policy_id", triggerHandler.Delete)
			triggerGroup.DELETE("/policies", triggerHandler.BatchDelete)
			triggerGroup.POST("/policies/targets", triggerHandler.Test)

			jobHandler := trigger.NewJobHandler()
			triggerGroup.GET("/policies/:policy_id/jobs", jobHandler.List)
			triggerGroup.PATCH("/policies/:policy_id/jobs/:job_id/retry", jobHandler.RetryExecuteJob)
		}

		acceleratorGroup := v1Group.Group("/accelerators")
		{
			acceleratorHandler := accelerator.NewPolicyHandler()
			acceleratorGroup.POST("/policies", acceleratorHandler.Create)
			acceleratorGroup.GET("/policies", acceleratorHandler.List)
			acceleratorGroup.GET("/policies/:policy_id", acceleratorHandler.Get)
			acceleratorGroup.PUT("/policies/:policy_id", acceleratorHandler.Update)
			acceleratorGroup.DELETE("/policies/:policy_id", acceleratorHandler.Delete)
			acceleratorGroup.DELETE("/policies", acceleratorHandler.BatchDelete)
		}

		projectGroup := v1Group.Group("/projects")
		{
			projectHandler := handler.NewProjectHandler(conf.HarborHost, db)
			projectGroup.GET("", projectHandler.ListProjects)
			projectGroup.GET("/:project_name", projectHandler.GetProject)
			projectGroup.GET("/:project_name/overview", projectHandler.GetProjectOverview)

			tagHandler := handler.NewTagHandler(harborCli)

			projectGroup.GET("/:project_name/repositories/:repository_name/tags", tagHandler.ListTags)
			projectGroup.GET("/:project_name/repositories/:repository_name/count_tags", tagHandler.CountTags)
			projectGroup.GET("/:project_name/repositories/:repository_name/tags/:tag_name", tagHandler.GetTag)
			projectGroup.GET("/:project_name/count_repositories_tags", tagHandler.CountRepoTags)
		}

		chartGroup := v1Group.Group("/api/chartrepo")
		{
			chartHandler := handler.NewChartHandler(conf.HarborHost, db, simUserService, csrfService, conf.IsPublic)

			chartGroup.POST("/:repo/charts", middleware.QuotaMiddleware(conf.Namespace, k8scli, conf.IsPublic), chartHandler.UploadChart)

			chartGroup.DELETE("/:repo/charts/:name", chartHandler.Proxy)
			chartGroup.DELETE("/:repo/charts/:name/:version", chartHandler.Proxy)

			chartGroup.GET("/:repo/charts", chartHandler.Proxy)
			chartGroup.GET("/:repo/charts/:name", chartHandler.Proxy)
			chartGroup.GET("/:repo/charts/:name/:version", chartHandler.Proxy)

			chartGroup.POST("/:repo/prov", chartHandler.Proxy)
			chartGroup.POST("/charts", chartHandler.Proxy)
		}

		registryGroup := v1Group.Group("/registries", middleware.SecretMiddleware(conf.Namespace, k8scli))

		registryHandler := handler.NewRegistryHandler()
		registryGroup.GET("", registryHandler.List)

		authGroup := v1Group.Group("")
		{
			authHandler := handler.NewAuthHandler(simUserService)
			authGroup.POST("/sessiontoken", authHandler.GetSessionToken)
		}

		immutableGroup := v1Group.Group("/immutable")
		{
			immutableHandler := handler.NewImmutableHandler()
			immutableGroup.GET("/rule", immutableHandler.ListImmutableRule)
			immutableGroup.GET("/rule/:immutableId", immutableHandler.GetImmutableRule)
		}
	}

	{
		imageHandler := handler.NewImageHandler(conf.HarborHost, authDomain, privateKey, conf.CachedNamespaces, conf.IsPublic)
		r.HEAD("/addon/v1/blobs/*resource", middleware.OrmMiddleware(), imageHandler.HeadBlobs)
		r.HEAD("/addon/v1/manifests/*resource", imageHandler.GetManifest)
		r.GET("/addon/v1/manifests/*resource", imageHandler.GetManifest)
		r.PUT("/addon/v1/manifests/*resource", middleware.OrmMiddleware(), middleware.QuotaMiddleware(conf.Namespace, k8scli, conf.IsPublic), imageHandler.PutManifests)
	}

	chartGroup := r.Group("", middleware.OrmMiddleware(), middleware.ChartMuseumAuthMiddleware(authorizer, privateKey, projectMgr, metaMgr, conf.AuthPasson, conf.IsPublic))
	{
		chartHandler := handler.NewChartHandler(conf.HarborHost, db, simUserService, csrfService, conf.IsPublic)

		chartGroup.POST("/api/chartrepo/:repo/charts", middleware.QuotaMiddleware(conf.Namespace, k8scli, conf.IsPublic), chartHandler.UploadChart)

		chartGroup.GET("/api/chartrepo/:repo/charts", chartHandler.Proxy)
		chartGroup.GET("/api/chartrepo/:repo/charts/:name", chartHandler.Proxy)
		chartGroup.GET("/api/chartrepo/:repo/charts/:name/:version", chartHandler.Proxy)
		chartGroup.DELETE("/api/chartrepo/:repo/charts/:name/:version", chartHandler.Proxy)

		chartGroup.POST("/api/chartrepo/:repo/prov", chartHandler.Proxy)

		chartGroup.GET("/chartrepo/:repo/index.yaml", chartHandler.Proxy)
		chartGroup.GET("/chartrepo/index.yaml", chartHandler.Proxy)
		chartGroup.GET("/chartrepo/:repo/charts/:filename", chartHandler.Proxy)
	}

	return r
}
