basePath: /addon/v1
definitions:
  job.EventData:
    properties:
      custom_attributes:
        additionalProperties:
          type: string
        type: object
      repository:
        $ref: '#/definitions/job.Repository'
      resources:
        items:
          $ref: '#/definitions/job.Resource'
        type: array
    type: object
  job.JobDetail:
    properties:
      event_data:
        $ref: '#/definitions/job.EventData'
      occur_at:
        type: integer
      operator:
        type: string
      type:
        type: string
    type: object
  job.Repository:
    properties:
      date_created:
        type: integer
      name:
        type: string
      namespace:
        type: string
      repo_full_name:
        type: string
      repo_type:
        type: string
    type: object
  job.Resource:
    properties:
      digest:
        type: string
      resource_url:
        type: string
      scan_overview:
        additionalProperties: true
        type: object
      tag:
        type: string
    type: object
  job.TriggerJob:
    properties:
      creation_time:
        type: string
      event_type:
        type: string
      id:
        type: integer
      job_detail:
        $ref: '#/definitions/job.JobDetail'
      notify_type:
        type: string
      policy_id:
        type: integer
      status:
        type: string
      update_time:
        type: string
    type: object
  model.AdditionLink:
    properties:
      absolute:
        description: Determine whether the link is an absolute URL or not
        type: boolean
      href:
        description: The link of the addition
        type: string
    type: object
  model.AdditionLinks:
    additionalProperties:
      $ref: '#/definitions/model.AdditionLink'
    type: object
  model.Annotations:
    additionalProperties:
      type: string
    type: object
  model.Artifact:
    properties:
      addition_links:
        $ref: '#/definitions/model.AdditionLinks'
        description: addition links
      annotations:
        $ref: '#/definitions/model.Annotations'
        description: annotations
      digest:
        description: The digest of the artifact
        type: string
      extra_attrs:
        $ref: '#/definitions/model.ExtraAttrs'
        description: extra attrs
      icon:
        description: The digest of the icon
        type: string
      id:
        description: The ID of the artifact
        type: integer
      labels:
        description: labels
        items:
          $ref: '#/definitions/model.Label'
        type: array
      manifest_media_type:
        description: The manifest media type of the artifact
        type: string
      media_type:
        description: The media type of the artifact
        type: string
      project_id:
        description: The ID of the project that the artifact belongs to
        type: integer
      pull_time:
        description: |-
          The latest pull time of the artifact
          Format: date-time
        type: string
      push_time:
        description: |-
          The push time of the artifact
          Format: date-time
        type: string
      references:
        description: references
        items:
          $ref: '#/definitions/model.Reference'
        type: array
      repository_id:
        description: The ID of the repository that the artifact belongs to
        type: integer
      scan_overview:
        $ref: '#/definitions/model.ScanOverview'
        description: The overview of the scan result.
      size:
        description: The size of the artifact
        type: integer
      tags:
        description: tags
        items:
          $ref: '#/definitions/model.Tag'
        type: array
      type:
        description: The type of the artifact, e.g. image, chart, etc
        type: string
    type: object
  model.Credential:
    properties:
      access_key:
        description: The key of the access account, for OAuth token, it can be empty
        type: string
      access_secret:
        description: The secret or password for the key
        type: string
      type:
        description: Type of the credential
        type: string
    type: object
  model.ExtraAttrs:
    additionalProperties: true
    type: object
  model.Label:
    properties:
      color:
        description: The color of label.
        type: string
      creation_time:
        description: The creation time of label.
        type: string
      deleted:
        description: The label is deleted or not.
        type: boolean
      description:
        description: The description of label.
        type: string
      id:
        description: The ID of label.
        type: integer
      name:
        description: The name of label.
        type: string
      project_id:
        description: The project ID if the label is a project label.
        type: integer
      scope:
        description: The scope of label, g for global labels and p for project labels.
        type: string
      update_time:
        description: The update time of label.
        type: string
    type: object
  model.NativeReportSummary:
    properties:
      complete_percent:
        description: |-
          The complete percent of the scanning which value is between 0 and 100
          Example: 100
        type: integer
      duration:
        description: |-
          The seconds spent for generating the report
          Example: 300
        type: integer
      end_time:
        description: |-
          The end time of the scan process that generating report
          Example: 2006-01-02T15:04:05Z
          Format: date-time
        type: string
      report_id:
        description: |-
          id of the native scan report
          Example: 5f62c830-f996-11e9-957f-0242c0a89008
        type: string
      scan_status:
        description: |-
          The status of the report generating process
          Example: Success
        type: string
      scanner:
        $ref: '#/definitions/model.Scanner'
        description: scanner
      severity:
        description: |-
          The overall severity
          Example: High
        type: string
      start_time:
        description: |-
          The start time of the scan process that generating report
          Example: 2006-01-02T14:04:05Z
          Format: date-time
        type: string
      summary:
        $ref: '#/definitions/model.VulnerabilitySummary'
        description: summary
    type: object
  model.Platform:
    properties:
      '''os.features''':
        description: The features of the OS that the artifact applys to
        items:
          type: string
        type: array
      '''os.version''':
        description: The version of the OS that the artifact applys to
        type: string
      architecture:
        description: The architecture that the artifact applys to
        type: string
      os:
        description: The OS that the artifact applys to
        type: string
      variant:
        description: The variant of the CPU
        type: string
    type: object
  model.Reference:
    properties:
      annotations:
        $ref: '#/definitions/model.Annotations'
        description: annotations
      child_digest:
        description: The digest of the child artifact
        type: string
      child_id:
        description: The child ID of the reference
        type: integer
      parent_id:
        description: The parent ID of the reference
        type: integer
      platform:
        $ref: '#/definitions/model.Platform'
        description: platform
      urls:
        description: The download URLs
        items:
          type: string
        type: array
    type: object
  model.Registry:
    properties:
      creation_time:
        type: string
      credential:
        $ref: '#/definitions/model.Credential'
      description:
        type: string
      id:
        type: integer
      insecure:
        type: boolean
      name:
        type: string
      status:
        type: string
      token_service_url:
        description: |-
          TokenServiceURL is only used for local harbor instance to
          avoid the requests passing through the external proxy for now
        type: string
      type:
        type: string
      update_time:
        type: string
      url:
        type: string
    type: object
  model.ScanOverview:
    additionalProperties:
      $ref: '#/definitions/model.NativeReportSummary'
    type: object
  model.Scanner:
    properties:
      name:
        description: |-
          Name of the scanner
          Example: Trivy
        type: string
      vendor:
        description: |-
          Name of the scanner provider
          Example: Aqua Security
        type: string
      version:
        description: |-
          Version of the scanner adapter
          Example: v0.9.1
        type: string
    type: object
  model.Tag:
    properties:
      artifact_id:
        description: The ID of the artifact that the tag attached to
        type: integer
      id:
        description: The ID of the tag
        type: integer
      immutable:
        description: The immutable status of the tag
        type: boolean
      name:
        description: The name of the tag
        type: string
      pull_time:
        description: |-
          The latest pull time of the tag
          Format: date-time
        type: string
      push_time:
        description: |-
          The push time of the tag
          Format: date-time
        type: string
      repository_id:
        description: The ID of the repository that the tag belongs to
        type: integer
      signed:
        description: The attribute indicates whether the tag is signed or not
        type: boolean
    type: object
  model.VulnerabilitySummary:
    properties:
      fixable:
        description: |-
          The number of the fixable vulnerabilities
          Example: 100
        type: integer
      summary:
        additionalProperties:
          type: integer
        description: |-
          Numbers of the vulnerabilities with different severity
          Example: {"Critical":5,"High":5}
        type: object
      total:
        description: |-
          The total number of the found vulnerabilities
          Example: 500
        type: integer
    type: object
  models.ArtifactTag:
    properties:
      art:
        $ref: '#/definitions/model.Artifact'
      tag:
        $ref: '#/definitions/models.Tag'
    type: object
  models.BatchDeleteRequest:
    properties:
      items:
        items:
          type: integer
        type: array
    required:
    - items
    type: object
  models.CVEAllowlist:
    properties:
      creation_time:
        description: |-
          The creation time of the allowlist.
          Format: date-time
        type: string
      expires_at:
        description: the time for expiration of the allowlist, in the form of seconds
          since epoch.  This is an optional attribute, if it's not set the CVE allowlist
          does not expire.
        type: integer
      id:
        description: ID of the allowlist
        type: integer
      items:
        description: items
        items:
          $ref: '#/definitions/models.CVEAllowlistItem'
        type: array
      project_id:
        description: ID of the project which the allowlist belongs to.  For system
          level allowlist this attribute is zero.
        type: integer
      update_time:
        description: |-
          The update time of the allowlist.
          Format: date-time
        type: string
    type: object
  models.CVEAllowlistItem:
    properties:
      cve_id:
        description: The ID of the CVE, such as "CVE-2019-10164"
        type: string
    type: object
  models.ImmutableRule:
    properties:
      action:
        type: string
      disabled:
        type: boolean
      id:
        type: integer
      priority:
        type: integer
      projectID:
        type: integer
      scopeSelectors:
        additionalProperties:
          items:
            $ref: '#/definitions/models.Selector'
          type: array
        type: object
      tagSelectors:
        items:
          $ref: '#/definitions/models.Selector'
        type: array
      template:
        type: string
    type: object
  models.ProjectDetail:
    properties:
      chart_count:
        description: The total number of charts under this project.
        type: integer
      creation_time:
        description: |-
          The creation time of the project.
          Format: date-time
        format: date-time
        type: string
      current_user_role_id:
        description: The role ID with highest permission of the current user who triggered
          the API (for UI).  This attribute is deprecated and will be removed in future
          versions.
        type: integer
      current_user_role_ids:
        description: The list of role ID of the current user who triggered the API
          (for UI)
        items:
          type: integer
        type: array
      cve_allowlist:
        $ref: '#/definitions/models.CVEAllowlist'
        description: The CVE allowlist of this project.
      deleted:
        description: A deletion mark of the project.
        type: boolean
      metadata:
        $ref: '#/definitions/models.ProjectMetadata'
        description: The metadata of the project.
      name:
        description: The name of the project.
        type: string
      owner_id:
        description: The owner ID of the project always means the creator of the project.
        type: integer
      owner_name:
        description: The owner name of the project.
        type: string
      project_id:
        description: Project ID
        format: int32
        type: integer
      registry_id:
        description: The ID of referenced registry when the project is a proxy cache
          project.
        type: integer
      repo_count:
        description: The number of the repositories under this project.
        type: integer
      togglable:
        description: Correspond to the UI about whether the project's publicity is  updatable
          (for UI)
        type: boolean
      update_time:
        description: |-
          The update time of the project.
          Format: date-time
        format: date-time
        type: string
    type: object
  models.ProjectMetadata:
    properties:
      auto_scan:
        description: Whether scan images automatically when pushing. The valid values
          are "true", "false".
        type: string
      enable_content_trust:
        description: Whether content trust is enabled or not. If it is enabled, user
          can't pull unsigned images from this project. The valid values are "true",
          "false".
        type: string
      prevent_vul:
        description: Whether prevent the vulnerable images from running. The valid
          values are "true", "false".
        type: string
      public:
        description: The public status of the project. The valid values are "true",
          "false".
        type: string
      retention_id:
        description: The ID of the tag retention policy for the project
        type: string
      reuse_sys_cve_allowlist:
        description: Whether this project reuse the system level CVE allowlist as
          the allowlist of its own.  The valid values are "true", "false". If it is
          set to "true" the actual allowlist associate with this project, if any,
          will be ignored.
        type: string
      severity:
        description: If the vulnerability is high than severity defined here, the
          images can't be pulled. The valid values are "none", "low", "medium", "high",
          "critical".
        type: string
    type: object
  models.ProjectOverview:
    properties:
      auto_scan:
        description: Whether scan images automatically when pushing. The valid values
          are "true", "false".
        type: string
      creation_time:
        description: |-
          The creation time of the project.
          Format: date-time
        format: date-time
        type: string
      deleted:
        description: A deletion mark of the project.
        type: boolean
      name:
        description: The name of the project.
        type: string
      owner_id:
        description: The owner ID of the project always means the creator of the project.
        type: integer
      owner_name:
        description: The owner name of the project.
        type: string
      project_id:
        description: Project ID
        format: int32
        type: integer
      public:
        description: The public status of the project. The valid values are "true",
          "false".
        type: string
      registry_id:
        description: The ID of referenced registry when the project is a proxy cache
          project.
        type: integer
      update_time:
        description: |-
          The update time of the project.
          Format: date-time
        format: date-time
        type: string
    type: object
  models.Selector:
    properties:
      decoration:
        type: string
      kind:
        type: string
      pattern:
        type: string
    type: object
  models.SessionToken:
    properties:
      expiredAt:
        type: string
      token:
        type: string
    type: object
  models.SessionTokenArgs:
    properties:
      adminRoleInAuth:
        description: AdminRoleInAuth to store the admin privilege granted by external
          authentication provider
        type: boolean
      comment:
        type: string
      creationTime:
        type: string
      deleted:
        type: boolean
      email:
        type: string
      groupIDs:
        items:
          type: integer
        type: array
      password:
        type: string
      passwordVersion:
        type: string
      realname:
        type: string
      resetUUID:
        type: string
      role:
        description: |-
          if this field is named as "RoleID", beego orm can not map role_id
          to it.
        type: integer
      rolename:
        type: string
      salt:
        type: string
      sysAdminFlag:
        type: boolean
      updateTime:
        type: string
      userID:
        type: integer
      username:
        type: string
    type: object
  models.Statistic:
    properties:
      private_project_count:
        description: The count of the private projects
        type: integer
      private_repo_count:
        description: The count of the private repositories
        type: integer
      public_project_count:
        description: The count of the public projects
        type: integer
      public_repo_count:
        description: The count of the public repositories
        type: integer
      total_chart_count:
        type: integer
      total_project_count:
        description: The count of the total projects, only be seen by the system admin
        type: integer
      total_repo_count:
        description: The count of the total repositories, only be seen by the system
          admin
        type: integer
      total_storage:
        type: integer
      total_storage_consumption:
        description: The total storage consumption of blobs, only be seen by the system
          admin
        type: integer
    type: object
  models.Tag:
    properties:
      accelerator_status:
        type: string
      artifact_id:
        type: integer
      digest:
        type: string
      id:
        type: integer
      manifest_media_type:
        description: the media type of manifest/index
        type: string
      media_type:
        description: the media type of artifact. Mostly, it's the value of `manifest.config.mediatype`
        type: string
      name:
        type: string
      project_id:
        type: integer
      pull_time:
        type: string
      push_time:
        type: string
      repository_id:
        type: integer
      repository_name:
        type: string
      size:
        type: integer
      type:
        description: image, chart, etc
        type: string
    type: object
  policy.AcceleratorFilter:
    properties:
      type:
        description: The accelerator policy filter type.
        type: string
      value:
        description: The value of accelerator policy filter.
        type: object
    type: object
  policy.AcceleratorPolicy:
    properties:
      creation_time:
        type: string
      creator:
        type: string
      description:
        type: string
      enabled:
        type: boolean
      filters:
        items:
          $ref: '#/definitions/policy.AcceleratorFilter'
        type: array
      id:
        type: integer
      name:
        type: string
      update_time:
        type: string
    type: object
  policy.TriggerFilter:
    properties:
      type:
        description: The trigger policy filter type.
        type: string
      value:
        description: The value of trigger policy filter.
        type: object
    type: object
  policy.TriggerPolicy:
    properties:
      creation_time:
        type: string
      creator:
        type: string
      description:
        type: string
      enabled:
        type: boolean
      event_types:
        items:
          type: string
        type: array
      filters:
        items:
          $ref: '#/definitions/policy.TriggerFilter'
        type: array
      id:
        type: integer
      name:
        type: string
      targets:
        items:
          $ref: '#/definitions/policy.TriggerTarget'
        type: array
      update_time:
        type: string
    type: object
  policy.TriggerTarget:
    properties:
      address:
        type: string
      headers:
        additionalProperties:
          type: string
        type: object
      skip_cert_verify:
        type: boolean
      type:
        type: string
    type: object
host: ccr.baidubce.com
info:
  contact:
    email: <EMAIL>
    name: duzhanwei,wenmanxiang
  description: Harbor Addon 提供 RESTFUL 风格 API, 提供harbor的补充功能
  title: Harbor Addon
  version: 0.0.1
paths:
  /accelerators/policies:
    delete:
      consumes:
      - application/json
      description: This endpoint batch delete accelerator policy.
      operationId: batchDeleteAcceleratorPolicy
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: The accelerator policy ids
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: BatchDelete accelerator policy.
      tags:
      - accelerator
    get:
      consumes:
      - application/json
      description: This endpoint returns accelerator policies.
      operationId: listAcceleratorPolicy
      parameters:
      - default: 1
        description: page number
        in: query
        name: page
        type: integer
      - default: 10
        description: page size
        in: query
        name: page_size
        type: integer
      - description: 'Query string to query resources. Supported query patterns are '
        in: query
        name: q
        type: string
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            X-Total-Count:
              description: The total of accelerator policy
              type: integer
          schema:
            items:
              $ref: '#/definitions/policy.AcceleratorPolicy'
            type: array
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: List accelerator policies.
      tags:
      - accelerator
    post:
      consumes:
      - application/json
      description: This endpoint creates an accelerator policy
      operationId: createAcceleratorPolicy
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: Properties filters needed.
        in: body
        name: policy
        required: true
        schema:
          $ref: '#/definitions/policy.AcceleratorPolicy'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: integer
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "409":
          description: Conflict
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Create accelerator policy.
      tags:
      - accelerator
  /accelerators/policies/{policy_id}:
    delete:
      consumes:
      - application/json
      description: This endpoint delete accelerator policy.
      operationId: deleteAcceleratorPolicy
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: The ID of the policy
        in: path
        name: policy_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Delete accelerator policy.
      tags:
      - accelerator
    get:
      consumes:
      - application/json
      description: This endpoint returns accelerator policy.
      operationId: getAcceleratorPolicy
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: The ID of the policy
        in: path
        name: policy_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/policy.AcceleratorPolicy'
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: get accelerator policy.
      tags:
      - accelerator
    put:
      consumes:
      - application/json
      description: This endpoint update accelerator policy.
      operationId: updateAcceleratorPolicy
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: The ID of the policy
        in: path
        name: policy_id
        required: true
        type: string
      - description: 'Properties '
        in: body
        name: policy
        required: true
        schema:
          $ref: '#/definitions/policy.AcceleratorPolicy'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Update accelerator policy.
      tags:
      - accelerator
  /immutable/rule:
    get:
      consumes:
      - application/json
      description: This endpoint returns immutable rule list
      operationId: ListImmutableRule
      parameters:
      - description: An unique ID for the request
        in: header
        name: X-Request-Id
        type: string
      - default: 1
        description: The page number
        in: query
        name: page
        type: integer
      - default: 10
        description: The size of per page
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Return all  matched immutable information.
          headers:
            Link:
              description: Link refers to the previous page and next page
              type: string
            X-Total-Count:
              description: The total count of projects
              type: integer
          schema:
            items:
              $ref: '#/definitions/models.ImmutableRule'
            type: array
        "401":
          description: Unauthorized
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Return immutable rule list
      tags:
      - immutable
  /immutable/rule/{immutableId}:
    get:
      consumes:
      - application/json
      description: This endpoint returns specific immutable rule information by immutable
        ID.
      operationId: GetImmutableRule
      parameters:
      - description: An unique ID for the request
        in: header
        name: X-Request-Id
        type: string
      - description: The id of the immutable rule
        in: path
        name: immutableId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Return matched immutable information.
          schema:
            $ref: '#/definitions/models.ImmutableRule'
        "401":
          description: Unauthorized
          headers:
            string:
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            string:
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Return specific immutable rule detail information
      tags:
      - immutable
  /products/statistic:
    get:
      consumes:
      - application/json
      description: 获取统计信息
      parameters:
      - description: request id
        in: header
        name: X-Request-Id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Statistic'
        "500":
          description: Internal Server Error
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: 获取统计信息
      tags:
      - product
  /projects:
    get:
      consumes:
      - application/json
      description: This endpoint returns projects created by Harbor.
      operationId: listProjects
      parameters:
      - description: An unique ID for the request
        in: header
        name: X-Request-Id
        type: string
      - default: 1
        description: The page number
        in: query
        name: page
        type: integer
      - default: 10
        description: The size of per page
        in: query
        name: page_size
        type: integer
      - description: The name of project
        in: query
        name: name
        type: string
      - description: The project is public or private
        format: int32
        in: query
        name: public
        type: boolean
      - description: The name of project owner
        in: query
        name: owner
        type: string
      - default: true
        description: Bool value indicating whether return detailed information of
          the project
        in: query
        name: with_detail
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: Return all matched projects.
          headers:
            Link:
              description: Link refers to the previous page and next page
              type: string
            X-Total-Count:
              description: The total count of projects
              type: integer
          schema:
            items:
              $ref: '#/definitions/models.ProjectDetail'
            type: array
        "401":
          description: Unauthorized
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: List projects
      tags:
      - project
  /projects/{project_name}:
    get:
      consumes:
      - application/json
      description: This endpoint returns specific project information by project ID.
      operationId: getProject
      parameters:
      - description: An unique ID for the request
        in: header
        name: X-Request-Id
        type: string
      - description: If the resource name is set, the resource name will be used
        in: header
        name: X-Is-Resource-Name
        type: boolean
      - description: The name or id of the project
        in: path
        name: project_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Return matched project information.
          schema:
            $ref: '#/definitions/models.ProjectDetail'
        "401":
          description: Unauthorized
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Return specific project detail information
      tags:
      - project
  /projects/{project_name}/count_repositories_tags:
    get:
      consumes:
      - application/json
      description: 计算一组repo下的tag的数量
      operationId: countRepoTags
      parameters:
      - description: 空间名字
        in: path
        name: project_name
        required: true
        type: string
      - description: 仓库名字数组
        in: query
        name: repository_names
        required: true
        type: string
      - description: request id
        in: header
        name: X-Request-Id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: repo对应tag数量
          schema:
            additionalProperties:
              type: integer
            type: object
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: 计算一组repo下的tag的数量
      tags:
      - tag
  /projects/{project_name}/overview:
    get:
      consumes:
      - application/json
      description: This endpoint returns overview project information by project ID.
      operationId: getProjectOverview
      parameters:
      - description: An unique ID for the request
        in: header
        name: X-Request-Id
        type: string
      - description: If the resource name is set, the resource name will be used
        in: header
        name: X-Is-Resource-Name
        type: boolean
      - description: The name or id of the project
        in: path
        name: project_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Return matched project information.
          schema:
            $ref: '#/definitions/models.ProjectOverview'
        "401":
          description: Unauthorized
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Return specific project overview information
      tags:
      - project
  /projects/{project_name}/repositories/{repository_name}/count_tags:
    get:
      consumes:
      - application/json
      description: 计算一个repo下的tag的数量
      operationId: countTags
      parameters:
      - description: 空间名字
        in: path
        name: project_name
        required: true
        type: string
      - description: 仓库名字
        in: path
        name: repository_name
        required: true
        type: string
      - description: request id
        in: header
        name: X-Request-Id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: tag的数量
          schema:
            type: integer
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: 计算一个repo下的tag的数量
      tags:
      - tag
  /projects/{project_name}/repositories/{repository_name}/tags:
    get:
      consumes:
      - application/json
      description: 列举一个repo下的tag
      operationId: listTags
      parameters:
      - description: 空间名字
        in: path
        name: project_name
        required: true
        type: string
      - description: 仓库名字
        in: path
        name: repository_name
        required: true
        type: string
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 页大小
        in: query
        name: page_size
        type: integer
      - description: 搜索关键信息
        in: query
        name: key
        type: string
      - description: request id
        in: header
        name: X-Request-Id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            X-Total-Count:
              description: 返回tag总条数
              type: integer
          schema:
            items:
              $ref: '#/definitions/models.ArtifactTag'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: 列举一个repo下的tag
      tags:
      - tag
  /projects/{project_name}/repositories/{repository_name}/tags/{tag_name}:
    get:
      consumes:
      - application/json
      description: 获取tag版本详情
      operationId: getTag
      parameters:
      - description: 空间名字
        in: path
        name: project_name
        required: true
        type: string
      - description: 仓库名字
        in: path
        name: repository_name
        required: true
        type: string
      - description: 版本名字
        in: path
        name: tag_name
        required: true
        type: string
      - description: request id
        in: header
        name: X-Request-Id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Tag'
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: 获取tag版本详情
      tags:
      - tag
  /registries:
    get:
      consumes:
      - application/json
      description: 查询仓库列表（支持分页）
      operationId: listRegistries
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 页大小
        in: query
        name: page_size
        type: integer
      - description: '搜索关键信息 Query string to query resources. Supported query patterns
          are '
        in: query
        name: q
        type: string
      - description: request id
        in: header
        name: X-Request-Id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            X-Total-Count:
              description: 返回registries总条数
              type: integer
          schema:
            items:
              $ref: '#/definitions/model.Registry'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: 查询仓库列表（支持分页）
      tags:
      - registry
  /sessiontoken:
    post:
      consumes:
      - application/json
      description: used by admin to acquire user's credential
      operationId: getUserSessionToken
      parameters:
      - description: An unique ID for the request
        in: header
        name: X-Request-Id
        type: string
      - description: user's info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.SessionTokenArgs'
      produces:
      - application/json
      responses:
        "200":
          description: user's token with expired time
          schema:
            $ref: '#/definitions/models.SessionToken'
        "401":
          description: Unauthorized
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Return sessin token include user info
      tags:
      - user
  /triggers/policies:
    delete:
      consumes:
      - application/json
      description: This endpoint batch delete trigger policy.
      operationId: batchDeletePolicy
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: The trigger policy ids
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: BatchDelete trigger policy.
      tags:
      - trigger
    get:
      consumes:
      - application/json
      description: This endpoint returns trigger policies.
      operationId: listPolicy
      parameters:
      - default: 1
        description: page number
        in: query
        name: page
        type: integer
      - default: 10
        description: page size
        in: query
        name: page_size
        type: integer
      - description: 'Query string to query resources. Supported query patterns are '
        in: query
        name: q
        type: string
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            X-Total-Count:
              description: The total of trigger policy
              type: integer
          schema:
            items:
              $ref: '#/definitions/policy.TriggerPolicy'
            type: array
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: List trigger policies.
      tags:
      - trigger
    post:
      consumes:
      - application/json
      description: This endpoint create a trigger policy
      operationId: createPolicy
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: 'Properties '
        in: body
        name: policy
        required: true
        schema:
          $ref: '#/definitions/policy.TriggerPolicy'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: integer
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "409":
          description: Conflict
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Create trigger policy.
      tags:
      - trigger
  /triggers/policies/{policy_id}:
    delete:
      consumes:
      - application/json
      description: This endpoint delete trigger policy.
      operationId: deletePolicy
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: The ID of the policy
        in: path
        name: policy_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Delete trigger policy.
      tags:
      - trigger
    get:
      consumes:
      - application/json
      description: This endpoint returns trigger policy.
      operationId: getPolicy
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: The ID of the policy
        in: path
        name: policy_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/policy.TriggerPolicy'
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: get trigger policy.
      tags:
      - trigger
    put:
      consumes:
      - application/json
      description: This endpoint update trigger policy.
      operationId: updatePolicy
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: The ID of the policy
        in: path
        name: policy_id
        required: true
        type: string
      - description: 'Properties '
        in: body
        name: policy
        required: true
        schema:
          $ref: '#/definitions/policy.TriggerPolicy'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Update trigger policy.
      tags:
      - trigger
  /triggers/policies/{policy_id}/jobs:
    get:
      consumes:
      - application/json
      description: This endpoint returns trigger jobs.
      operationId: listJob
      parameters:
      - description: The ID of the policy
        in: path
        name: policy_id
        required: true
        type: string
      - default: 1
        description: page number
        in: query
        name: page
        type: integer
      - default: 10
        description: page size
        in: query
        name: page_size
        type: integer
      - description: 'Query string to query resources. Supported query patterns are '
        in: query
        name: q
        type: string
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          headers:
            X-Total-Count:
              description: The total of trigger job
              type: integer
          schema:
            items:
              $ref: '#/definitions/job.TriggerJob'
            type: array
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: List trigger jobs.
      tags:
      - trigger
  /triggers/policies/{policy_id}/jobs/{job_id}/retry:
    patch:
      consumes:
      - application/json
      description: This endpoint retry execute trigger job.
      operationId: retryExecuteJob
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: The ID of the policy
        in: path
        name: policy_id
        required: true
        type: string
      - description: The ID of the job
        in: path
        name: job_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: RetryExecuteJob trigger job.
      tags:
      - trigger
  /triggers/policies/targets:
    post:
      consumes:
      - application/json
      description: This endpoint test a trigger policy targets
      operationId: testPolicyTargets
      parameters:
      - description: The request id
        in: header
        name: X-Request-Id
        type: string
      - description: trigger policy targets
        in: body
        name: target
        required: true
        schema:
          $ref: '#/definitions/policy.TriggerTarget'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
        "500":
          description: Internal Server Error
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
          schema:
            type: string
      security:
      - BasicAuth: []
      summary: Test trigger policy targets.
      tags:
      - trigger
securityDefinitions:
  BasicAuth:
    type: basic
swagger: "2.0"
