// Package docs GENERATED BY THE COMMAND ABOVE; DO NOT EDIT
// This file was generated by swaggo/swag
package docs

import (
	"bytes"
	"encoding/json"
	"strings"
	"text/template"

	"github.com/swaggo/swag"
)

var doc = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {
            "name": "duzhan<PERSON>,wenmanxiang",
            "email": "<EMAIL>"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/accelerators/policies": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint returns accelerator policies.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "accelerator"
                ],
                "summary": "List accelerator policies.",
                "operationId": "listAcceleratorPolicy",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "page size",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Query string to query resources. Supported query patterns are ",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/policy.AcceleratorPolicy"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "integer",
                                "description": "The total of accelerator policy"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint creates an accelerator policy",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "accelerator"
                ],
                "summary": "Create accelerator policy.",
                "operationId": "createAcceleratorPolicy",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "description": "Properties filters needed.",
                        "name": "policy",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/policy.AcceleratorPolicy"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "integer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint batch delete accelerator policy.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "accelerator"
                ],
                "summary": "BatchDelete accelerator policy.",
                "operationId": "batchDeleteAcceleratorPolicy",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "description": "The accelerator policy ids",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.BatchDeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/accelerators/policies/{policy_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint returns accelerator policy.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "accelerator"
                ],
                "summary": "get accelerator policy.",
                "operationId": "getAcceleratorPolicy",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "The ID of the policy",
                        "name": "policy_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/policy.AcceleratorPolicy"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint update accelerator policy.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "accelerator"
                ],
                "summary": "Update accelerator policy.",
                "operationId": "updateAcceleratorPolicy",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "The ID of the policy",
                        "name": "policy_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Properties ",
                        "name": "policy",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/policy.AcceleratorPolicy"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint delete accelerator policy.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "accelerator"
                ],
                "summary": "Delete accelerator policy.",
                "operationId": "deleteAcceleratorPolicy",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "The ID of the policy",
                        "name": "policy_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/immutable/rule": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint returns immutable rule list",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "immutable"
                ],
                "summary": "Return immutable rule list",
                "operationId": "ListImmutableRule",
                "parameters": [
                    {
                        "type": "string",
                        "description": "An unique ID for the request",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "The page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "The size of per page",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return all  matched immutable information.",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.ImmutableRule"
                            }
                        },
                        "headers": {
                            "Link": {
                                "type": "string",
                                "description": "Link refers to the previous page and next page"
                            },
                            "X-Total-Count": {
                                "type": "integer",
                                "description": "The total count of projects"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/immutable/rule/{immutableId}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint returns specific immutable rule information by immutable ID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "immutable"
                ],
                "summary": "Return specific immutable rule detail information",
                "operationId": "GetImmutableRule",
                "parameters": [
                    {
                        "type": "string",
                        "description": "An unique ID for the request",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "The id of the immutable rule",
                        "name": "immutableId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return matched immutable information.",
                        "schema": {
                            "$ref": "#/definitions/models.ImmutableRule"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "string": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "string": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/products/statistic": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "获取统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "product"
                ],
                "summary": "获取统计信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Statistic"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/projects": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint returns projects created by Harbor.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "project"
                ],
                "summary": "List projects",
                "operationId": "listProjects",
                "parameters": [
                    {
                        "type": "string",
                        "description": "An unique ID for the request",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "The page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "The size of per page",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "The name of project",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "format": "int32",
                        "description": "The project is public or private",
                        "name": "public",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "The name of project owner",
                        "name": "owner",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "default": true,
                        "description": "Bool value indicating whether return detailed information of the project",
                        "name": "with_detail",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return all matched projects.",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.ProjectDetail"
                            }
                        },
                        "headers": {
                            "Link": {
                                "type": "string",
                                "description": "Link refers to the previous page and next page"
                            },
                            "X-Total-Count": {
                                "type": "integer",
                                "description": "The total count of projects"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/projects/{project_name}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint returns specific project information by project ID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "project"
                ],
                "summary": "Return specific project detail information",
                "operationId": "getProject",
                "parameters": [
                    {
                        "type": "string",
                        "description": "An unique ID for the request",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "boolean",
                        "description": "If the resource name is set, the resource name will be used",
                        "name": "X-Is-Resource-Name",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "The name or id of the project",
                        "name": "project_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return matched project information.",
                        "schema": {
                            "$ref": "#/definitions/models.ProjectDetail"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/projects/{project_name}/count_repositories_tags": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "计算一组repo下的tag的数量",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "计算一组repo下的tag的数量",
                "operationId": "countRepoTags",
                "parameters": [
                    {
                        "type": "string",
                        "description": "空间名字",
                        "name": "project_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "仓库名字数组",
                        "name": "repository_names",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "repo对应tag数量",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "integer"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/projects/{project_name}/overview": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint returns overview project information by project ID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "project"
                ],
                "summary": "Return specific project overview information",
                "operationId": "getProjectOverview",
                "parameters": [
                    {
                        "type": "string",
                        "description": "An unique ID for the request",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "boolean",
                        "description": "If the resource name is set, the resource name will be used",
                        "name": "X-Is-Resource-Name",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "The name or id of the project",
                        "name": "project_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Return matched project information.",
                        "schema": {
                            "$ref": "#/definitions/models.ProjectOverview"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/projects/{project_name}/repositories/{repository_name}/count_tags": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "计算一个repo下的tag的数量",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "计算一个repo下的tag的数量",
                "operationId": "countTags",
                "parameters": [
                    {
                        "type": "string",
                        "description": "空间名字",
                        "name": "project_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "仓库名字",
                        "name": "repository_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "tag的数量",
                        "schema": {
                            "type": "integer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/projects/{project_name}/repositories/{repository_name}/tags": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "列举一个repo下的tag",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "列举一个repo下的tag",
                "operationId": "listTags",
                "parameters": [
                    {
                        "type": "string",
                        "description": "空间名字",
                        "name": "project_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "仓库名字",
                        "name": "repository_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "页大小",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键信息",
                        "name": "key",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.ArtifactTag"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "integer",
                                "description": "返回tag总条数"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/projects/{project_name}/repositories/{repository_name}/tags/{tag_name}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "获取tag版本详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "获取tag版本详情",
                "operationId": "getTag",
                "parameters": [
                    {
                        "type": "string",
                        "description": "空间名字",
                        "name": "project_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "仓库名字",
                        "name": "repository_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "版本名字",
                        "name": "tag_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Tag"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/registries": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "查询仓库列表（支持分页）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "registry"
                ],
                "summary": "查询仓库列表（支持分页）",
                "operationId": "listRegistries",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "页大小",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键信息 Query string to query resources. Supported query patterns are ",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/model.Registry"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "integer",
                                "description": "返回registries总条数"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/sessiontoken": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "used by admin to acquire user's credential",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "Return sessin token include user info",
                "operationId": "getUserSessionToken",
                "parameters": [
                    {
                        "type": "string",
                        "description": "An unique ID for the request",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "description": "user's info",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.SessionTokenArgs"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "user's token with expired time",
                        "schema": {
                            "$ref": "#/definitions/models.SessionToken"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/triggers/policies": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint returns trigger policies.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "trigger"
                ],
                "summary": "List trigger policies.",
                "operationId": "listPolicy",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "page size",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Query string to query resources. Supported query patterns are ",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/policy.TriggerPolicy"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "integer",
                                "description": "The total of trigger policy"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint create a trigger policy",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "trigger"
                ],
                "summary": "Create trigger policy.",
                "operationId": "createPolicy",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "description": "Properties ",
                        "name": "policy",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/policy.TriggerPolicy"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "integer"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint batch delete trigger policy.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "trigger"
                ],
                "summary": "BatchDelete trigger policy.",
                "operationId": "batchDeletePolicy",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "description": "The trigger policy ids",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.BatchDeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/triggers/policies/targets": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint test a trigger policy targets",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "trigger"
                ],
                "summary": "Test trigger policy targets.",
                "operationId": "testPolicyTargets",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "description": "trigger policy targets",
                        "name": "target",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/policy.TriggerTarget"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/triggers/policies/{policy_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint returns trigger policy.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "trigger"
                ],
                "summary": "get trigger policy.",
                "operationId": "getPolicy",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "The ID of the policy",
                        "name": "policy_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/policy.TriggerPolicy"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint update trigger policy.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "trigger"
                ],
                "summary": "Update trigger policy.",
                "operationId": "updatePolicy",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "The ID of the policy",
                        "name": "policy_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Properties ",
                        "name": "policy",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/policy.TriggerPolicy"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint delete trigger policy.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "trigger"
                ],
                "summary": "Delete trigger policy.",
                "operationId": "deletePolicy",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "The ID of the policy",
                        "name": "policy_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/triggers/policies/{policy_id}/jobs": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint returns trigger jobs.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "trigger"
                ],
                "summary": "List trigger jobs.",
                "operationId": "listJob",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The ID of the policy",
                        "name": "policy_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "page size",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Query string to query resources. Supported query patterns are ",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/job.TriggerJob"
                            }
                        },
                        "headers": {
                            "X-Total-Count": {
                                "type": "integer",
                                "description": "The total of trigger job"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        },
        "/triggers/policies/{policy_id}/jobs/{job_id}/retry": {
            "patch": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "description": "This endpoint retry execute trigger job.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "trigger"
                ],
                "summary": "RetryExecuteJob trigger job.",
                "operationId": "retryExecuteJob",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The request id",
                        "name": "X-Request-Id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "The ID of the policy",
                        "name": "policy_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "The ID of the job",
                        "name": "job_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "string"
                        },
                        "headers": {
                            "X-Request-Id": {
                                "type": "string",
                                "description": "The ID of the corresponding request for the response"
                            }
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "job.EventData": {
            "type": "object",
            "properties": {
                "custom_attributes": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "repository": {
                    "$ref": "#/definitions/job.Repository"
                },
                "resources": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/job.Resource"
                    }
                }
            }
        },
        "job.JobDetail": {
            "type": "object",
            "properties": {
                "event_data": {
                    "$ref": "#/definitions/job.EventData"
                },
                "occur_at": {
                    "type": "integer"
                },
                "operator": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "job.Repository": {
            "type": "object",
            "properties": {
                "date_created": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "repo_full_name": {
                    "type": "string"
                },
                "repo_type": {
                    "type": "string"
                }
            }
        },
        "job.Resource": {
            "type": "object",
            "properties": {
                "digest": {
                    "type": "string"
                },
                "resource_url": {
                    "type": "string"
                },
                "scan_overview": {
                    "type": "object",
                    "additionalProperties": true
                },
                "tag": {
                    "type": "string"
                }
            }
        },
        "job.TriggerJob": {
            "type": "object",
            "properties": {
                "creation_time": {
                    "type": "string"
                },
                "event_type": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "job_detail": {
                    "$ref": "#/definitions/job.JobDetail"
                },
                "notify_type": {
                    "type": "string"
                },
                "policy_id": {
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                },
                "update_time": {
                    "type": "string"
                }
            }
        },
        "model.AdditionLink": {
            "type": "object",
            "properties": {
                "absolute": {
                    "description": "Determine whether the link is an absolute URL or not",
                    "type": "boolean"
                },
                "href": {
                    "description": "The link of the addition",
                    "type": "string"
                }
            }
        },
        "model.AdditionLinks": {
            "type": "object",
            "additionalProperties": {
                "$ref": "#/definitions/model.AdditionLink"
            }
        },
        "model.Annotations": {
            "type": "object",
            "additionalProperties": {
                "type": "string"
            }
        },
        "model.Artifact": {
            "type": "object",
            "properties": {
                "addition_links": {
                    "description": "addition links",
                    "$ref": "#/definitions/model.AdditionLinks"
                },
                "annotations": {
                    "description": "annotations",
                    "$ref": "#/definitions/model.Annotations"
                },
                "digest": {
                    "description": "The digest of the artifact",
                    "type": "string"
                },
                "extra_attrs": {
                    "description": "extra attrs",
                    "$ref": "#/definitions/model.ExtraAttrs"
                },
                "icon": {
                    "description": "The digest of the icon",
                    "type": "string"
                },
                "id": {
                    "description": "The ID of the artifact",
                    "type": "integer"
                },
                "labels": {
                    "description": "labels",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.Label"
                    }
                },
                "manifest_media_type": {
                    "description": "The manifest media type of the artifact",
                    "type": "string"
                },
                "media_type": {
                    "description": "The media type of the artifact",
                    "type": "string"
                },
                "project_id": {
                    "description": "The ID of the project that the artifact belongs to",
                    "type": "integer"
                },
                "pull_time": {
                    "description": "The latest pull time of the artifact\nFormat: date-time",
                    "type": "string"
                },
                "push_time": {
                    "description": "The push time of the artifact\nFormat: date-time",
                    "type": "string"
                },
                "references": {
                    "description": "references",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.Reference"
                    }
                },
                "repository_id": {
                    "description": "The ID of the repository that the artifact belongs to",
                    "type": "integer"
                },
                "scan_overview": {
                    "description": "The overview of the scan result.",
                    "$ref": "#/definitions/model.ScanOverview"
                },
                "size": {
                    "description": "The size of the artifact",
                    "type": "integer"
                },
                "tags": {
                    "description": "tags",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.Tag"
                    }
                },
                "type": {
                    "description": "The type of the artifact, e.g. image, chart, etc",
                    "type": "string"
                }
            }
        },
        "model.Credential": {
            "type": "object",
            "properties": {
                "access_key": {
                    "description": "The key of the access account, for OAuth token, it can be empty",
                    "type": "string"
                },
                "access_secret": {
                    "description": "The secret or password for the key",
                    "type": "string"
                },
                "type": {
                    "description": "Type of the credential",
                    "type": "string"
                }
            }
        },
        "model.ExtraAttrs": {
            "type": "object",
            "additionalProperties": true
        },
        "model.Label": {
            "type": "object",
            "properties": {
                "color": {
                    "description": "The color of label.",
                    "type": "string"
                },
                "creation_time": {
                    "description": "The creation time of label.",
                    "type": "string"
                },
                "deleted": {
                    "description": "The label is deleted or not.",
                    "type": "boolean"
                },
                "description": {
                    "description": "The description of label.",
                    "type": "string"
                },
                "id": {
                    "description": "The ID of label.",
                    "type": "integer"
                },
                "name": {
                    "description": "The name of label.",
                    "type": "string"
                },
                "project_id": {
                    "description": "The project ID if the label is a project label.",
                    "type": "integer"
                },
                "scope": {
                    "description": "The scope of label, g for global labels and p for project labels.",
                    "type": "string"
                },
                "update_time": {
                    "description": "The update time of label.",
                    "type": "string"
                }
            }
        },
        "model.NativeReportSummary": {
            "type": "object",
            "properties": {
                "complete_percent": {
                    "description": "The complete percent of the scanning which value is between 0 and 100\nExample: 100",
                    "type": "integer"
                },
                "duration": {
                    "description": "The seconds spent for generating the report\nExample: 300",
                    "type": "integer"
                },
                "end_time": {
                    "description": "The end time of the scan process that generating report\nExample: 2006-01-02T15:04:05Z\nFormat: date-time",
                    "type": "string"
                },
                "report_id": {
                    "description": "id of the native scan report\nExample: 5f62c830-f996-11e9-957f-0242c0a89008",
                    "type": "string"
                },
                "scan_status": {
                    "description": "The status of the report generating process\nExample: Success",
                    "type": "string"
                },
                "scanner": {
                    "description": "scanner",
                    "$ref": "#/definitions/model.Scanner"
                },
                "severity": {
                    "description": "The overall severity\nExample: High",
                    "type": "string"
                },
                "start_time": {
                    "description": "The start time of the scan process that generating report\nExample: 2006-01-02T14:04:05Z\nFormat: date-time",
                    "type": "string"
                },
                "summary": {
                    "description": "summary",
                    "$ref": "#/definitions/model.VulnerabilitySummary"
                }
            }
        },
        "model.Platform": {
            "type": "object",
            "properties": {
                "'os.features'": {
                    "description": "The features of the OS that the artifact applys to",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "'os.version'": {
                    "description": "The version of the OS that the artifact applys to",
                    "type": "string"
                },
                "architecture": {
                    "description": "The architecture that the artifact applys to",
                    "type": "string"
                },
                "os": {
                    "description": "The OS that the artifact applys to",
                    "type": "string"
                },
                "variant": {
                    "description": "The variant of the CPU",
                    "type": "string"
                }
            }
        },
        "model.Reference": {
            "type": "object",
            "properties": {
                "annotations": {
                    "description": "annotations",
                    "$ref": "#/definitions/model.Annotations"
                },
                "child_digest": {
                    "description": "The digest of the child artifact",
                    "type": "string"
                },
                "child_id": {
                    "description": "The child ID of the reference",
                    "type": "integer"
                },
                "parent_id": {
                    "description": "The parent ID of the reference",
                    "type": "integer"
                },
                "platform": {
                    "description": "platform",
                    "$ref": "#/definitions/model.Platform"
                },
                "urls": {
                    "description": "The download URLs",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "model.Registry": {
            "type": "object",
            "properties": {
                "creation_time": {
                    "type": "string"
                },
                "credential": {
                    "$ref": "#/definitions/model.Credential"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "insecure": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "token_service_url": {
                    "description": "TokenServiceURL is only used for local harbor instance to\navoid the requests passing through the external proxy for now",
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "update_time": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "model.ScanOverview": {
            "type": "object",
            "additionalProperties": {
                "$ref": "#/definitions/model.NativeReportSummary"
            }
        },
        "model.Scanner": {
            "type": "object",
            "properties": {
                "name": {
                    "description": "Name of the scanner\nExample: Trivy",
                    "type": "string"
                },
                "vendor": {
                    "description": "Name of the scanner provider\nExample: Aqua Security",
                    "type": "string"
                },
                "version": {
                    "description": "Version of the scanner adapter\nExample: v0.9.1",
                    "type": "string"
                }
            }
        },
        "model.Tag": {
            "type": "object",
            "properties": {
                "artifact_id": {
                    "description": "The ID of the artifact that the tag attached to",
                    "type": "integer"
                },
                "id": {
                    "description": "The ID of the tag",
                    "type": "integer"
                },
                "immutable": {
                    "description": "The immutable status of the tag",
                    "type": "boolean"
                },
                "name": {
                    "description": "The name of the tag",
                    "type": "string"
                },
                "pull_time": {
                    "description": "The latest pull time of the tag\nFormat: date-time",
                    "type": "string"
                },
                "push_time": {
                    "description": "The push time of the tag\nFormat: date-time",
                    "type": "string"
                },
                "repository_id": {
                    "description": "The ID of the repository that the tag belongs to",
                    "type": "integer"
                },
                "signed": {
                    "description": "The attribute indicates whether the tag is signed or not",
                    "type": "boolean"
                }
            }
        },
        "model.VulnerabilitySummary": {
            "type": "object",
            "properties": {
                "fixable": {
                    "description": "The number of the fixable vulnerabilities\nExample: 100",
                    "type": "integer"
                },
                "summary": {
                    "description": "Numbers of the vulnerabilities with different severity\nExample: {\"Critical\":5,\"High\":5}",
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "total": {
                    "description": "The total number of the found vulnerabilities\nExample: 500",
                    "type": "integer"
                }
            }
        },
        "models.ArtifactTag": {
            "type": "object",
            "properties": {
                "art": {
                    "$ref": "#/definitions/model.Artifact"
                },
                "tag": {
                    "$ref": "#/definitions/models.Tag"
                }
            }
        },
        "models.BatchDeleteRequest": {
            "type": "object",
            "required": [
                "items"
            ],
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "models.CVEAllowlist": {
            "type": "object",
            "properties": {
                "creation_time": {
                    "description": "The creation time of the allowlist.\nFormat: date-time",
                    "type": "string"
                },
                "expires_at": {
                    "description": "the time for expiration of the allowlist, in the form of seconds since epoch.  This is an optional attribute, if it's not set the CVE allowlist does not expire.",
                    "type": "integer"
                },
                "id": {
                    "description": "ID of the allowlist",
                    "type": "integer"
                },
                "items": {
                    "description": "items",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CVEAllowlistItem"
                    }
                },
                "project_id": {
                    "description": "ID of the project which the allowlist belongs to.  For system level allowlist this attribute is zero.",
                    "type": "integer"
                },
                "update_time": {
                    "description": "The update time of the allowlist.\nFormat: date-time",
                    "type": "string"
                }
            }
        },
        "models.CVEAllowlistItem": {
            "type": "object",
            "properties": {
                "cve_id": {
                    "description": "The ID of the CVE, such as \"CVE-2019-10164\"",
                    "type": "string"
                }
            }
        },
        "models.ImmutableRule": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string"
                },
                "disabled": {
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                },
                "priority": {
                    "type": "integer"
                },
                "projectID": {
                    "type": "integer"
                },
                "scopeSelectors": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "array",
                        "items": {
                            "$ref": "#/definitions/models.Selector"
                        }
                    }
                },
                "tagSelectors": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Selector"
                    }
                },
                "template": {
                    "type": "string"
                }
            }
        },
        "models.ProjectDetail": {
            "type": "object",
            "properties": {
                "chart_count": {
                    "description": "The total number of charts under this project.",
                    "type": "integer"
                },
                "creation_time": {
                    "description": "The creation time of the project.\nFormat: date-time",
                    "type": "string",
                    "format": "date-time"
                },
                "current_user_role_id": {
                    "description": "The role ID with highest permission of the current user who triggered the API (for UI).  This attribute is deprecated and will be removed in future versions.",
                    "type": "integer"
                },
                "current_user_role_ids": {
                    "description": "The list of role ID of the current user who triggered the API (for UI)",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "cve_allowlist": {
                    "description": "The CVE allowlist of this project.",
                    "$ref": "#/definitions/models.CVEAllowlist"
                },
                "deleted": {
                    "description": "A deletion mark of the project.",
                    "type": "boolean"
                },
                "metadata": {
                    "description": "The metadata of the project.",
                    "$ref": "#/definitions/models.ProjectMetadata"
                },
                "name": {
                    "description": "The name of the project.",
                    "type": "string"
                },
                "owner_id": {
                    "description": "The owner ID of the project always means the creator of the project.",
                    "type": "integer"
                },
                "owner_name": {
                    "description": "The owner name of the project.",
                    "type": "string"
                },
                "project_id": {
                    "description": "Project ID",
                    "type": "integer",
                    "format": "int32"
                },
                "registry_id": {
                    "description": "The ID of referenced registry when the project is a proxy cache project.",
                    "type": "integer"
                },
                "repo_count": {
                    "description": "The number of the repositories under this project.",
                    "type": "integer"
                },
                "togglable": {
                    "description": "Correspond to the UI about whether the project's publicity is  updatable (for UI)",
                    "type": "boolean"
                },
                "update_time": {
                    "description": "The update time of the project.\nFormat: date-time",
                    "type": "string",
                    "format": "date-time"
                }
            }
        },
        "models.ProjectMetadata": {
            "type": "object",
            "properties": {
                "auto_scan": {
                    "description": "Whether scan images automatically when pushing. The valid values are \"true\", \"false\".",
                    "type": "string"
                },
                "enable_content_trust": {
                    "description": "Whether content trust is enabled or not. If it is enabled, user can't pull unsigned images from this project. The valid values are \"true\", \"false\".",
                    "type": "string"
                },
                "prevent_vul": {
                    "description": "Whether prevent the vulnerable images from running. The valid values are \"true\", \"false\".",
                    "type": "string"
                },
                "public": {
                    "description": "The public status of the project. The valid values are \"true\", \"false\".",
                    "type": "string"
                },
                "retention_id": {
                    "description": "The ID of the tag retention policy for the project",
                    "type": "string"
                },
                "reuse_sys_cve_allowlist": {
                    "description": "Whether this project reuse the system level CVE allowlist as the allowlist of its own.  The valid values are \"true\", \"false\". If it is set to \"true\" the actual allowlist associate with this project, if any, will be ignored.",
                    "type": "string"
                },
                "severity": {
                    "description": "If the vulnerability is high than severity defined here, the images can't be pulled. The valid values are \"none\", \"low\", \"medium\", \"high\", \"critical\".",
                    "type": "string"
                }
            }
        },
        "models.ProjectOverview": {
            "type": "object",
            "properties": {
                "auto_scan": {
                    "description": "Whether scan images automatically when pushing. The valid values are \"true\", \"false\".",
                    "type": "string"
                },
                "creation_time": {
                    "description": "The creation time of the project.\nFormat: date-time",
                    "type": "string",
                    "format": "date-time"
                },
                "deleted": {
                    "description": "A deletion mark of the project.",
                    "type": "boolean"
                },
                "name": {
                    "description": "The name of the project.",
                    "type": "string"
                },
                "owner_id": {
                    "description": "The owner ID of the project always means the creator of the project.",
                    "type": "integer"
                },
                "owner_name": {
                    "description": "The owner name of the project.",
                    "type": "string"
                },
                "project_id": {
                    "description": "Project ID",
                    "type": "integer",
                    "format": "int32"
                },
                "public": {
                    "description": "The public status of the project. The valid values are \"true\", \"false\".",
                    "type": "string"
                },
                "registry_id": {
                    "description": "The ID of referenced registry when the project is a proxy cache project.",
                    "type": "integer"
                },
                "update_time": {
                    "description": "The update time of the project.\nFormat: date-time",
                    "type": "string",
                    "format": "date-time"
                }
            }
        },
        "models.Selector": {
            "type": "object",
            "properties": {
                "decoration": {
                    "type": "string"
                },
                "kind": {
                    "type": "string"
                },
                "pattern": {
                    "type": "string"
                }
            }
        },
        "models.SessionToken": {
            "type": "object",
            "properties": {
                "expiredAt": {
                    "type": "string"
                },
                "token": {
                    "type": "string"
                }
            }
        },
        "models.SessionTokenArgs": {
            "type": "object",
            "properties": {
                "adminRoleInAuth": {
                    "description": "AdminRoleInAuth to store the admin privilege granted by external authentication provider",
                    "type": "boolean"
                },
                "comment": {
                    "type": "string"
                },
                "creationTime": {
                    "type": "string"
                },
                "deleted": {
                    "type": "boolean"
                },
                "email": {
                    "type": "string"
                },
                "groupIDs": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "password": {
                    "type": "string"
                },
                "passwordVersion": {
                    "type": "string"
                },
                "realname": {
                    "type": "string"
                },
                "resetUUID": {
                    "type": "string"
                },
                "role": {
                    "description": "if this field is named as \"RoleID\", beego orm can not map role_id\nto it.",
                    "type": "integer"
                },
                "rolename": {
                    "type": "string"
                },
                "salt": {
                    "type": "string"
                },
                "sysAdminFlag": {
                    "type": "boolean"
                },
                "updateTime": {
                    "type": "string"
                },
                "userID": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.Statistic": {
            "type": "object",
            "properties": {
                "private_project_count": {
                    "description": "The count of the private projects",
                    "type": "integer"
                },
                "private_repo_count": {
                    "description": "The count of the private repositories",
                    "type": "integer"
                },
                "public_project_count": {
                    "description": "The count of the public projects",
                    "type": "integer"
                },
                "public_repo_count": {
                    "description": "The count of the public repositories",
                    "type": "integer"
                },
                "total_chart_count": {
                    "type": "integer"
                },
                "total_project_count": {
                    "description": "The count of the total projects, only be seen by the system admin",
                    "type": "integer"
                },
                "total_repo_count": {
                    "description": "The count of the total repositories, only be seen by the system admin",
                    "type": "integer"
                },
                "total_storage": {
                    "type": "integer"
                },
                "total_storage_consumption": {
                    "description": "The total storage consumption of blobs, only be seen by the system admin",
                    "type": "integer"
                }
            }
        },
        "models.Tag": {
            "type": "object",
            "properties": {
                "accelerator_status": {
                    "type": "string"
                },
                "artifact_id": {
                    "type": "integer"
                },
                "digest": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "manifest_media_type": {
                    "description": "the media type of manifest/index",
                    "type": "string"
                },
                "media_type": {
                    "description": "the media type of artifact. Mostly, it's the value of ` + "`" + `manifest.config.mediatype` + "`" + `",
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "project_id": {
                    "type": "integer"
                },
                "pull_time": {
                    "type": "string"
                },
                "push_time": {
                    "type": "string"
                },
                "repository_id": {
                    "type": "integer"
                },
                "repository_name": {
                    "type": "string"
                },
                "size": {
                    "type": "integer"
                },
                "type": {
                    "description": "image, chart, etc",
                    "type": "string"
                }
            }
        },
        "policy.AcceleratorFilter": {
            "type": "object",
            "properties": {
                "type": {
                    "description": "The accelerator policy filter type.",
                    "type": "string"
                },
                "value": {
                    "description": "The value of accelerator policy filter.",
                    "type": "object"
                }
            }
        },
        "policy.AcceleratorPolicy": {
            "type": "object",
            "properties": {
                "creation_time": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "enabled": {
                    "type": "boolean"
                },
                "filters": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/policy.AcceleratorFilter"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "update_time": {
                    "type": "string"
                }
            }
        },
        "policy.TriggerFilter": {
            "type": "object",
            "properties": {
                "type": {
                    "description": "The trigger policy filter type.",
                    "type": "string"
                },
                "value": {
                    "description": "The value of trigger policy filter.",
                    "type": "object"
                }
            }
        },
        "policy.TriggerPolicy": {
            "type": "object",
            "properties": {
                "creation_time": {
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "enabled": {
                    "type": "boolean"
                },
                "event_types": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "filters": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/policy.TriggerFilter"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "targets": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/policy.TriggerTarget"
                    }
                },
                "update_time": {
                    "type": "string"
                }
            }
        },
        "policy.TriggerTarget": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "string"
                },
                "headers": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "skip_cert_verify": {
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BasicAuth": {
            "type": "basic"
        }
    }
}`

type swaggerInfo struct {
	Version     string
	Host        string
	BasePath    string
	Schemes     []string
	Title       string
	Description string
}

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = swaggerInfo{
	Version:     "0.0.1",
	Host:        "ccr.baidubce.com",
	BasePath:    "/addon/v1",
	Schemes:     []string{},
	Title:       "Harbor Addon",
	Description: "Harbor Addon 提供 RESTFUL 风格 API, 提供harbor的补充功能",
}

type s struct{}

func (s *s) ReadDoc() string {
	sInfo := SwaggerInfo
	sInfo.Description = strings.Replace(sInfo.Description, "\n", "\\n", -1)

	t, err := template.New("swagger_info").Funcs(template.FuncMap{
		"marshal": func(v interface{}) string {
			a, _ := json.Marshal(v)
			return string(a)
		},
		"escape": func(v interface{}) string {
			// escape tabs
			str := strings.Replace(v.(string), "\t", "\\t", -1)
			// replace " with \", and if that results in \\", replace that with \\\"
			str = strings.Replace(str, "\"", "\\\"", -1)
			return strings.Replace(str, "\\\\\"", "\\\\\\\"", -1)
		},
	}).Parse(doc)
	if err != nil {
		return doc
	}

	var tpl bytes.Buffer
	if err := t.Execute(&tpl, sInfo); err != nil {
		return doc
	}

	return tpl.String()
}

func init() {
	swag.Register(swag.Name, &s{})
}
