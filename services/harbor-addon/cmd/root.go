package cmd

import (
	"context"
	"fmt"
	"io/ioutil"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/goharbor/harbor/src/common/dao"
	mgrproject "github.com/goharbor/harbor/src/pkg/project"
	"github.com/goharbor/harbor/src/pkg/project/metadata"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/auth"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/dao/chart"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/router"
)

var (
	listenPort int
	harborAddr string
	namespace  string
	authDomain string
	version    string // 版本
	whiteList  string
	addonAddr  string

	insecureEndpoint bool
	cachedNamespaces []string

	isPublic bool

	authPassOn bool
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "harbor-addon",
	Short: "harbor addon",
	Long:  `harbor-addon is used to supplement the harbor`,
	// Uncomment the following line if your bare application
	// has an action associated with it:
	Run: func(cmd *cobra.Command, args []string) {
		rootRun(cmd.Context())
	},
	Version: version,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	cobra.CheckErr(rootCmd.Execute())
}

func init() {
	//cobra.OnInitialize(initConfig)

	// Here you will define your flags and configuration settings.
	// Cobra supports persistent flags, which, if defined here,
	// will be global for your application.

	viper.BindEnv("postgresql_host")
	viper.BindEnv("postgresql_port")
	viper.BindEnv("postgresql_username")
	viper.BindEnv("postgresql_password")
	viper.BindEnv("postgresql_database")
	viper.BindEnv("postgresql_sslmode")

	viper.BindEnv("_redis_url_core")
	viper.BindEnv("csrf_key")

	rootCmd.Flags().IntVar(&listenPort, "port", 9300, "listened port")
	rootCmd.Flags().StringVar(&harborAddr, "harbor-addr", "", "harbor address")
	rootCmd.Flags().StringVar(&namespace, "namespace", "", "namespace the pod in")
	rootCmd.Flags().StringVar(&authDomain, "auth-domain", "", "ccr auth service url")
	rootCmd.Flags().StringVar(&whiteList, "white-list", "", "trigger hook white list")
	rootCmd.Flags().StringVar(&addonAddr, "addon-addr", "", "harbor addon address")
	rootCmd.Flags().StringSliceVar(&cachedNamespaces, "cached-namespaces", []string{}, "namespaces will be cached")
	rootCmd.Flags().BoolVar(&insecureEndpoint, "insecure", false, "insecure endpoint")
	rootCmd.Flags().BoolVar(&isPublic, "is-public", true, "distinguish between public cloud and industry cloud")
	rootCmd.Flags().BoolVar(&authPassOn, "auth-passon", false, "pass auth to downstream if failed")
	// Cobra also supports local flags, which will only run
	// when this action is called directly.
	// rootCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}

func initConfig() *config.Config {
	redisDsn := viper.GetString("_redis_url_core")
	u, err := url.Parse(redisDsn)
	if err != nil {
		panic("invalid redis url pattern")
	}

	var masterName, password string
	var db int
	if u.Scheme != "redis+sentinel" && u.Scheme != "redis" {
		panic("unsupport redis scheme")
	}

	ps := strings.Split(u.Path, "/")
	if len(ps) < 2 {
		panic("bad redis url")
	}
	if u.User != nil {
		if passwd, ok := u.User.Password(); ok {
			password = passwd
		}
	}
	if len(ps) == 3 {
		masterName = ps[1]

		db, err = strconv.Atoi(ps[2])
		if err != nil {
			panic("invalid db index")
		}
	} else {
		db, err = strconv.Atoi(ps[1])
		if err != nil {
			panic("invalid db index")
		}
	}
	redisAddrs := strings.Split(u.Host, ",")

	return &config.Config{
		Database: config.DatabaseConfig{
			Host:           viper.GetString("postgresql_host"),
			Port:           viper.GetString("postgresql_port"),
			Username:       viper.GetString("postgresql_username"),
			Password:       viper.GetString("postgresql_password"),
			HarborDatabase: viper.GetString("postgresql_database"),
			SSLMode:        viper.GetString("postgresql_sslmode"),
		},
		Redis: config.RedisConfig{
			HarborDBIndex:      db,
			Addrs:              redisAddrs,
			SentinelMasterName: masterName,
			RedisPassword:      password,
		},
		ListenAddr: fmt.Sprintf(":%v", listenPort),
		HarborHost: harborAddr,
		Namespace:  namespace,
		AuthDomain: authDomain,
		WhiteList:  whiteList,
		AddonAddr:  addonAddr,
		CsrfKey:    viper.GetString("csrf_key"),

		CachedNamespaces: cachedNamespaces,

		IsPublic:   isPublic,
		AuthPasson: authPassOn,
	}
}

func rootRun(ctx context.Context) error {
	conf := initConfig()

	harborDB, err := config.NewHarborDatabaseConfig(&conf.Database)
	if err != nil {
		logrus.Errorf("generate database config failed: %s", err)
		return err
	}

	if err = dao.InitDatabase(harborDB); err != nil {
		logrus.Errorf("init database failed: %s", err)
		return err
	}

	addonDB, err := newAddonDB(conf)
	if err != nil {
		logrus.Errorf("create addon db failed: %v", err)
		return err
	}

	k8sClient, err := utils.NewK8sClientWithCache(ctx, "", scheme, namespace, 10*time.Minute)
	if err != nil {
		logrus.Errorf("create k8s client failed: %s", err)
		return err
	}

	privateKey, err := ioutil.ReadFile("/etc/addon/private_key.pem")
	if err != nil {
		return fmt.Errorf("fail to read %s: %w", "private_key.pem", err)
	}

	csrfService, err := utils.NewHarborCsrf(conf.CsrfKey)
	if err != nil {
		return fmt.Errorf("init harbor csrf failed: %w", err)
	}

	authorizer := auth.NewAuthorizer(namespace, fmt.Sprintf("%s%s", authDomain, "/service/token"), insecureEndpoint)

	redisCli, err := NewRedisClient(&conf.Redis)
	if err != nil {
		logrus.Errorf("create redis  client failed: %s", err)
		return err
	}
	// Initialize internal Mgr/Dao ...
	route := router.NewRouter(conf,
		addonDB,
		k8sClient,
		authorizer,
		authDomain,
		privateKey,
		mgrproject.Mgr,
		metadata.Mgr,
		redisCli,
		csrfService,
	)
	route.Run(fmt.Sprintf(":%v", listenPort))

	return nil
}

func newAddonDB(conf *config.Config) (*gorm.DB, error) {
	addonDB, err := gorm.Open(
		postgres.Open(config.AddonDatabaseDSNFromConfig(&conf.Database)),
		&gorm.Config{
			NowFunc: time.Now().UTC,
		})
	if err != nil {
		logrus.Errorf("open addon database failed: %v", err)
		return nil, err
	}

	if err = addonDB.AutoMigrate(&chart.Chart{}); err != nil {
		return nil, err
	}

	return addonDB, nil
}

func NewRedisClient(conf *config.RedisConfig) (redis.UniversalClient, error) {
	client := redis.NewUniversalClient(&redis.UniversalOptions{
		Addrs:      conf.Addrs,
		DB:         conf.HarborDBIndex,
		MasterName: conf.SentinelMasterName,
		Password:   conf.RedisPassword,
	})

	if err := client.Ping(context.Background()).Err(); err != nil {
		return nil, err
	}

	return client, nil
}
