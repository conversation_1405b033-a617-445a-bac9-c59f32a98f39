package cmd

import (
	"os"
	"strings"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/config"
)

func Test_NewRedisClient(t *testing.T) {
	addrs := []string{"localhost:6379"}
	conf := &config.RedisConfig{Addrs: addrs, RedisPassword: "test11", HarborDBIndex: 0}

	_, err := NewRedisClient(conf)

	actual := err.Error()
	expected := "connection refused"

	assert.Equal(t, true, strings.Contains(actual, expected))
}

func Test_initConfig(t *testing.T) {
	// TODO: add redis url test
	os.Setenv("_REDIS_URL_CORE",
		"redis+sentinel://redis:<EMAIL>-headless:26379/ccr/2?idle_timeout_seconds=30")
	viper.BindEnv("_REDIS_URL_CORE")

	got := initConfig()
	assert.False(t, got.AuthPasson)
}
