package config

import (
	"fmt"
	"strconv"

	"github.com/goharbor/harbor/src/common/models"
)

type DatabaseConfig struct {
	Host           string `yaml:"host,omitempty"`
	Port           string `yaml:"port,omitempty"`
	Username       string `yaml:"username,omitempty"`
	Password       string `yaml:"password,omitempty"`
	HarborDatabase string `yaml:"database,omitempty"`
	SSLMode        string `yaml:"sslMode,omitempty"`
}

type RedisConfig struct {
	Addrs              []string `yaml:"addrs,omitempty"`
	SentinelMasterName string   `yaml:"sentinelMasterName,omitempty"`
	RedisPassword      string   `yaml:"redisPassword,omitempty"`
	HarborDBIndex      int      `yaml:"harborDBIndex,omitempty"`
}

type Config struct {
	Database   DatabaseConfig `yaml:"database,omitempty"`
	Redis      RedisConfig    `yaml:"redis,omitempty"`
	ListenAddr string         `yaml:"listenAddr,omitempty"`
	HarborHost string         `yaml:"harborHost,omitempty"`
	Namespace  string         `yaml:"namespace,omitempty"`
	AuthDomain string         `yaml:"authDomain,omitempty"`
	WhiteList  string         `yaml:"whiteList,omitempty"`
	AddonAddr  string         `yaml:"addonAddr,omitempty"`
	CsrfKey    string         `yaml:"csrfKey,omitempty"`

	CachedNamespaces []string `yaml:"cachedNamespace,omitempty"`

	IsPublic   bool `yaml:"isPublic,omitempty"`
	AuthPasson bool `yaml:"authPasson,omitempty"`
}

func AddonDatabaseDSNFromConfig(dbConf *DatabaseConfig) string {
	return fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		dbConf.Host, dbConf.Username, dbConf.Password, dbConf.HarborDatabase, dbConf.Port, dbConf.SSLMode,
	)
}

func NewHarborDatabaseConfig(dbConf *DatabaseConfig) (*models.Database, error) {
	port, err := strconv.Atoi(dbConf.Port)
	if err != nil {
		return nil, err
	}

	return &models.Database{
		Type: "postgresql",
		PostGreSQL: &models.PostGreSQL{
			Host:         dbConf.Host,
			Port:         port,
			Username:     dbConf.Username,
			Password:     dbConf.Password,
			Database:     dbConf.HarborDatabase,
			SSLMode:      dbConf.SSLMode,
			MaxIdleConns: 2,
			MaxOpenConns: 0,
		},
	}, nil
}
