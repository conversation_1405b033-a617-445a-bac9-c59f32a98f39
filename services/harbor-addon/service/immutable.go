package service

import (
	"context"
	"fmt"

	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/immutable"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
)

type ImmutableService struct {
	immutableMgr immutable.Manager
}

func NewImmutableService() *ImmutableService {
	return &ImmutableService{
		immutableMgr: immutable.NewDefaultRuleManager(),
	}
}

func (i *ImmutableService) ListImmutableRules(ctx context.Context, pageNo, pageSize int64) (int64, []*models.ImmutableRule, error) {

	query, err := q.Build("", "", pageNo, pageSize)
	if err != nil {
		return 0, nil, fmt.Errorf("build query string failed: %w", err)
	}

	total, err := i.immutableMgr.Count(ctx, query)
	if err != nil {
		return 0, nil, fmt.Errorf("count the immutable failed: %w", err)
	}
	result, err := i.immutableMgr.ListImmutableRules(ctx, query)
	if err != nil {
		return 0, nil, fmt.Errorf("list the immutable failed: %w", err)
	}

	rules := make([]*models.ImmutableRule, 0)

	for i := range result {
		r := result[i]

		scopeSelectors := make(map[string][]*models.Selector)
		for key, val := range r.ScopeSelectors {
			scopeSelector := make([]*models.Selector, 0)
			for _, t := range val {
				sc := &models.Selector{
					Decoration: t.Decoration,
					Kind:       t.Kind,
					Pattern:    t.Pattern,
				}
				scopeSelector = append(scopeSelector, sc)
			}
			scopeSelectors[key] = scopeSelector
		}

		tagSelectors := make([]*models.Selector, 0)
		for _, t := range r.TagSelectors {
			tagSelector := &models.Selector{
				Decoration: t.Decoration,
				Kind:       t.Kind,
				Pattern:    t.Pattern,
			}
			tagSelectors = append(tagSelectors, tagSelector)
		}

		rule := &models.ImmutableRule{
			ID:             r.ID,
			ProjectID:      r.ProjectID,
			Disabled:       r.Disabled,
			Action:         r.Action,
			Priority:       r.Priority,
			Template:       r.Template,
			TagSelectors:   tagSelectors,
			ScopeSelectors: scopeSelectors,
		}
		rules = append(rules, rule)
	}

	return total, rules, nil

}
