package service

import (
	"context"
	"testing"

	"github.com/goharbor/harbor/src/lib/orm"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/statistic"
	mockstatistic "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/statistic/mocks"
	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	testingorm "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/ormer"
)

func Test_GetStatistic(t *testing.T) {
	ss := NewStatisticService(nil)

	_, err := ss.GetStatistic(context.Background(), "test", "test")
	assert.Error(t, err)

	testOrm := testingorm.NewMockOrm(gomock.NewController(t))
	ctx := orm.NewContext(context.Background(), testOrm)
	testseter := testingorm.NewMockRawSeter(gomock.NewController(t))
	testseter.EXPECT().QueryRow(gomock.Any()).AnyTimes().Return(nil)
	testOrm.EXPECT().Raw(gomock.Any(), gomock.Any()).AnyTimes().Return(testseter)

	productService := &mockstatistic.MockStatisticClientService{}
	productService.On("GetStatistic", mock.Anything, mock.Anything).Return(&statistic.GetStatisticOK{
		Payload: &harbormodel.Statistic{},
	}, nil)

	ss.clients = &harbor.HarborClient{
		V2Client: &client.Harbor{
			Statistic: productService,
		},
	}

	st, err := ss.GetStatistic(ctx, "test", "test")
	assert.NoError(t, err)
	assert.NotNil(t, st)
}
