package service

import (
	"context"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/dao"
)

type ChartService struct {
	dao        dao.Dao
	harborAddr string

	expiration time.Duration
}

func NewChartService(db *gorm.DB, harborAddr string) *ChartService {
	return &ChartService{
		dao:        dao.New(),
		harborAddr: harborAddr,
		expiration: 5 * time.Minute,
	}
}

func (cs *ChartService) Count(ctx context.Context, projectName string) (int64, error) {
	return cs.dao.ChartRepoCount(ctx, projectName)
}
