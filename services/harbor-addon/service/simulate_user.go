package service

import (
	"bytes"
	"context"
	"encoding/gob"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	harbormodels "github.com/goharbor/harbor/src/common/models"
	"github.com/karlseguin/ccache/v2"
	"github.com/sirupsen/logrus"
)

// SimUserService simulation user
type SimUserService struct {
	redisCli   redis.UniversalClient
	gcDuration time.Duration
	cache      *ccache.Cache
}

func NewSimUserService(cli redis.UniversalClient) *SimUserService {
	gob.Register([]interface{}{})
	gob.Register(map[int]interface{}{})
	gob.Register(map[string]interface{}{})
	gob.Register(map[interface{}]interface{}{})
	gob.Register(map[string]string{})
	gob.Register(map[int]string{})
	gob.Register(map[int]int{})
	gob.Register(map[int]int64{})
	gob.Register(harbormodels.User{})

	return &SimUserService{
		redisCli:   cli,
		gcDuration: 30 * time.Minute,
		cache:      ccache.New(ccache.Configure().MaxSize(5000).ItemsToPrune(500).GetsPerPromote(10)),
	}
}

func (s *SimUserService) GetAdminSession(ctx context.Context, username string) (string, error) {
	key := "admin-" + username
	item := s.cache.Get(key)
	if item == nil || item.Expired() {
		sid, err := s.SimulateUserSession(ctx, &harbormodels.User{
			Username:        username,
			SysAdminFlag:    true,
			AdminRoleInAuth: true,
		}, s.gcDuration)
		if err != nil {
			return "", err
		}

		s.cache.Set(key, sid, s.gcDuration-1*time.Minute)

		return sid, nil
	}

	if st, ok := item.Value().(string); ok {
		return st, nil
	}

	return "", nil
}

// SimulateUserSession will generate session id for specific user
func (s *SimUserService) SimulateUserSession(ctx context.Context, u *harbormodels.User, maxGCDuration time.Duration) (string, error) {
	kv := make(map[interface{}]interface{})
	kv["user"] = u

	buf := bytes.NewBuffer(nil)
	encoder := gob.NewEncoder(buf)
	err := encoder.Encode(kv)
	if err != nil {
		return "", fmt.Errorf("encode user with gob failed: %w", err)
	}

	// generate key
	key := fmt.Sprintf("session:%d:%s:%d", u.UserID, u.Username, time.Now().UnixNano())
	logrus.Debugf("generate session key: %s", key)

	cmd := s.redisCli.SetNX(ctx, key, buf.String(), maxGCDuration)
	existed, err := cmd.Result()
	if err != nil {
		return "", fmt.Errorf("redis set nx failed: %w", err)
	}

	if existed {
		logrus.Debugf("key %s already existed", key)
	}

	return key, nil
}
