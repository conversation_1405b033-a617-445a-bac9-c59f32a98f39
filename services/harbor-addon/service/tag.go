package service

import (
	"context"
	"fmt"
	"strings"

	runtimeclient "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/artifact"
	pkgartifact "github.com/goharbor/harbor/src/pkg/artifact"
	"github.com/goharbor/harbor/src/pkg/repository"
	"github.com/goharbor/harbor/src/pkg/tag"
	"github.com/goharbor/harbor/src/pkg/task"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	apiclient "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client"
	artifactclient "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/artifact"
	harbormodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/manager/tag_execution"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	artifactdao "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/dao/artifact"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
)

type TagService struct {
	harborCli   *apiclient.Harbor
	repoMgr     repository.Manager
	tagMgr      tag.Manager
	teMgr       tag_execution.Manager
	artifactMgr artifact.Manager
	artifactDao artifactdao.DAO
	execMgr     task.ExecutionManager
}

func NewTagService(clients *harbor.HarborClient) *TagService {
	return &TagService{
		harborCli:   clients.V2Client,
		repoMgr:     repository.New(),
		tagMgr:      tag.NewManager(),
		artifactMgr: artifact.NewManager(),
		teMgr:       tag_execution.NewTagExecutionManager(),
		execMgr:     task.ExecMgr,
		artifactDao: &artifactdao.Dao{},
	}
}

func (t *TagService) ListTags(ctx context.Context, params *models.ListTagsParam, username, password string) (int64, []*models.ArtifactTag, error) {
	fullRepoName := fmt.Sprintf("%s/%s", params.ProjectName, params.RepoName)
	repo, err := t.repoMgr.GetByName(ctx, fullRepoName)
	if err != nil {
		return 0, nil, fmt.Errorf("cannot get repo info: %w", err)
	}

	query, err := q.Build(params.Keyword, "", params.PageNo, params.PageSize)
	if err != nil {
		return 0, nil, fmt.Errorf("build query string failed: %w", err)
	}
	query.Keywords["RepositoryID"] = repo.RepositoryID

	total, err := t.tagMgr.Count(ctx, query)
	if err != nil {
		return 0, nil, fmt.Errorf("count the tag failed: %w", err)
	}

	tags, err := t.tagMgr.List(ctx, query)
	if err != nil {
		return total, nil, fmt.Errorf("list tag failed: %w", err)
	}

	var artIds []string
	for _, v := range tags {
		artIds = append(artIds, fmt.Sprintf("%d", v.ArtifactID))
	}

	// TODO harbor中入到需要获取harbor数据时，不在推荐使用接口方式调用，直接使用数据库获取接口
	arts, err := t.queryArtifactFromIds(ctx, params.RequestId, params.ProjectName, params.RepoName, artIds, username, password)
	if err != nil {
		return total, nil, fmt.Errorf("ListTags queryArtifactFromIds failed: %w", err)
	}

	artSet := map[int64]*harbormodel.Artifact{}

	subArtIds := []int64{}

	for _, art := range arts {
		artSet[art.ID] = art
		if art.References != nil {
			for _, v := range art.References {
				subArtIds = append(subArtIds, v.ChildID)
			}
		}
	}

	subArts, err := t.queryArtifactFromIdsPurely(ctx, subArtIds)
	if err != nil {
		return total, nil, fmt.Errorf("ListTags querySubArtifactFromIds failed: %w", err)
	}
	subArtSet := map[int64]*harbormodel.Artifact{}
	for _, subArt := range subArts {
		subArtSet[subArt.ID] = subArt
	}

	// fill acceleration information
	teTagIdAndAcceleratorStatus := make(map[int64]string)
	teExecutionIdsAndTagId := make(map[int64]int64)

	// get execution id
	var teExecutionIds []int64

	var tagIds []int64
	for _, tag := range tags {
		tagIds = append(tagIds, tag.ID)
	}
	if len(tagIds) > 0 {

		teQuery, err := q.Build("", "", 1, 50)
		if err != nil {
			return 0, nil, fmt.Errorf("build query string failed: %s", err)
		}
		teQuery.Keywords["TagID__in"] = tagIds

		_, tes, err := t.teMgr.List(ctx, teQuery)
		for _, te := range tes {
			teExecutionIdsAndTagId[te.ExecutionID] = te.TagID
			teExecutionIds = append(teExecutionIds, te.ExecutionID)
		}
	}

	// get the #size execution record
	if len(teExecutionIds) > 0 {
		eQuery, err := q.Build("", "", 1, 50)
		if err != nil {
			return 0, nil, fmt.Errorf("build query string failed: %s", err)
		}
		eQuery.Keywords["ID__in"] = teExecutionIds

		executions, err := t.execMgr.List(ctx, eQuery)
		if err != nil {
			return 0, nil, err
		}
		// list is null means that the execution count < size, return directly
		if len(executions) > 0 {
			for _, execution := range executions {
				tagId, ok := teExecutionIdsAndTagId[execution.ID]
				if ok {
					teTagIdAndAcceleratorStatus[tagId] = execution.Status
				}
			}
		}
	}

	result := make([]*models.ArtifactTag, 0)
	for _, tag := range tags {
		art := artSet[tag.ArtifactID]
		if art == nil {
			continue
		}

		modelsTag := &models.Tag{
			ID:           tag.ID,
			RepositoryID: tag.RepositoryID,
			ArtifactID:   tag.ArtifactID,
			Name:         tag.Name,
			PushTime:     tag.PushTime,
			PullTime:     tag.PullTime,
		}

		acceleratorStatus, ok := teTagIdAndAcceleratorStatus[modelsTag.ID]
		if ok {
			modelsTag.AcceleratorStatus = acceleratorStatus
		}

		// expand if media type is manifest
		if len(art.References) != 0 {
			for _, ref := range art.References {
				if val, ok := subArtSet[ref.ChildID]; ok {
					artTag := &models.ArtifactTag{
						Art: val,
						Tag: modelsTag,
					}

					artTag.Art.ScanOverview = art.ScanOverview
					result = append(result, artTag)
				}
			}
			continue
		}

		result = append(result, &models.ArtifactTag{
			Art: art,
			Tag: modelsTag,
		})
	}

	return total, result, nil
}

func (t *TagService) queryArtifactFromIds(ctx context.Context, reqId, projectName, repoName string, ids []string, username, password string) ([]*harbormodel.Artifact, error) {
	queryKey := fmt.Sprintf("id={%s}", strings.Join(ids, " "))
	trueFlag := true
	falseFlag := false
	arts, err := t.harborCli.Artifact.ListArtifacts(
		artifactclient.NewListArtifactsParams().
			WithDefaults().
			WithQ(&queryKey).
			WithProjectName(projectName).
			WithRepositoryName(repoName).
			WithWithScanOverview(&trueFlag).
			WithWithTag(&falseFlag).
			WithPage(utils.Int64Ptr(1)).
			WithPageSize(utils.Int64Ptr(int64(len(ids)))).
			WithXRequestID(&reqId),
		runtimeclient.BasicAuth(username, password))

	if err != nil {
		return nil, fmt.Errorf("list artifacts failed: %w", err)
	}

	return arts.Payload, nil
}

func (t *TagService) queryArtifactFromIdsPurely(ctx context.Context, ids []int64) ([]*harbormodel.Artifact, error) {
	if len(ids) == 0 {
		return []*harbormodel.Artifact{}, nil
	}

	allIds := make([]interface{}, len(ids))
	for i, v := range ids {
		allIds[i] = v
	}

	arts, err := t.artifactDao.FindArtifactByIDs(ctx, allIds...)
	if err != nil {
		return nil, fmt.Errorf("query artifact from database failed: %w", err)
	}

	result := make([]*harbormodel.Artifact, len(arts))
	for i, v := range arts {
		pkgArt := &pkgartifact.Artifact{}
		pkgArt.From(v)
		result[i] = &harbormodel.Artifact{
			Annotations:       pkgArt.Annotations,
			Digest:            pkgArt.Digest,
			ExtraAttrs:        pkgArt.ExtraAttrs,
			Icon:              pkgArt.Icon,
			ID:                pkgArt.ID,
			ManifestMediaType: pkgArt.ManifestMediaType,
			MediaType:         pkgArt.MediaType,
			ProjectID:         pkgArt.ProjectID,
			PullTime:          strfmt.DateTime(pkgArt.PullTime),
			PushTime:          strfmt.DateTime(pkgArt.PushTime),
			RepositoryID:      pkgArt.RepositoryID,
			Size:              pkgArt.Size,
			Type:              pkgArt.Type,
		}
	}

	return result, nil
}

func (t *TagService) GetTag(ctx context.Context, params *models.GetTagParam) (*models.Tag, error) {
	fullRepoName := fmt.Sprintf("%s/%s", params.ProjectName, params.RepoName)
	repo, err := t.repoMgr.GetByName(ctx, fullRepoName)
	if err != nil {
		return nil, fmt.Errorf("cannot get repo info: %w", err)
	}

	tags, err := t.tagMgr.List(ctx, &q.Query{
		Keywords: map[string]interface{}{
			"RepositoryID": repo.RepositoryID,
			"Name":         params.TagName,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("list tags failed: %w", err)
	}
	if len(tags) == 0 {
		return nil, fmt.Errorf("tag %s:%s not found", fullRepoName, params.TagName)
	}
	modelTag := tags[0]

	artifact, err := t.artifactMgr.Get(ctx, modelTag.ArtifactID)

	if err != nil {
		return nil, fmt.Errorf("get artifact failed: %w", err)
	}

	tag := &models.Tag{
		ID:                modelTag.ID,
		ProjectID:         artifact.ProjectID,
		RepositoryID:      modelTag.RepositoryID,
		ArtifactID:        modelTag.ArtifactID,
		Name:              modelTag.Name,
		PushTime:          modelTag.PushTime,
		PullTime:          modelTag.PullTime,
		Type:              artifact.Type,
		ManifestMediaType: artifact.ManifestMediaType,
		MediaType:         artifact.MediaType,
		RepositoryName:    artifact.RepositoryName,
		Digest:            artifact.Digest,
		Size:              artifact.Size,
	}

	return tag, nil

}

func (t *TagService) CountTags(ctx context.Context, params *models.CountTagsParam) (int64, error) {
	fullRepoName := fmt.Sprintf("%s/%s", params.ProjectName, params.RepoName)
	repo, err := t.repoMgr.GetByName(ctx, fullRepoName)
	if err != nil {
		return 0, fmt.Errorf("cannot get repo info: %w", err)
	}

	query := q.New(map[string]interface{}{"RepositoryID": repo.RepositoryID})

	total, err := t.tagMgr.Count(ctx, query)
	if err != nil {
		return 0, fmt.Errorf("count the tag failed: %w", err)
	}

	return total, nil
}

// CountRepoTagsMap 查询一组repo下的tag数量
// 返回repo对应tag数量的map
// TODO 查询镜像仓库下tag数量使用 in 和 group by 优化性能
// select repository_id, count(*) as cnt from tag where repository_id in (1,2,3) group by repository;
func (t *TagService) CountRepoTagsMap(ctx context.Context, params *models.CountRepoTagsParam) (map[string]int64, error) {

	repoNames := params.RepoNames
	repositoryTagMap := make(map[string]int64)

	for i := range repoNames {
		repoName := repoNames[i]
		repo, err := t.repoMgr.GetByName(ctx, repoName)
		if err != nil {
			return nil, fmt.Errorf("cannot get repo info: %w", err)
		}
		query := q.New(map[string]interface{}{"RepositoryID": repo.RepositoryID})
		total, err := t.tagMgr.Count(ctx, query)
		if err != nil {
			return nil, fmt.Errorf("count the tag failed: %w", err)
		}
		repositoryTagMap[repoName] = total
	}

	return repositoryTagMap, nil
}
