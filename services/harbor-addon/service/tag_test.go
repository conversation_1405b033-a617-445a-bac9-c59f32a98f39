package service

import (
	"fmt"
	"testing"

	"github.com/goharbor/harbor/src/pkg/artifact"

	"github.com/goharbor/harbor/src/pkg/repository/model"
	"github.com/goharbor/harbor/src/pkg/tag/model/tag"
	testingartifact "github.com/goharbor/harbor/src/testing/pkg/artifact"
	testingrepository "github.com/goharbor/harbor/src/testing/pkg/repository"
	testingtag "github.com/goharbor/harbor/src/testing/pkg/tag"
	testingtask "github.com/goharbor/harbor/src/testing/pkg/task"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	apiclient "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/acceleration/manager/tag_execution/mocks"
	testingartifactdao "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/dao/artifact"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
)

type TagServiceTestSuite struct {
	suite.Suite

	harborCli   *apiclient.Harbor
	repoMgr     *testingrepository.Manager
	tagMgr      *testingtag.FakeManager
	teMgr       *mocks.Manager
	artifactMgr *testingartifact.Manager
	artifactDao *testingartifactdao.Dao
	execMgr     *testingtask.ExecutionManager

	TagService *TagService
}

func (m *TagServiceTestSuite) SetupTest() {

	m.repoMgr = &testingrepository.Manager{}
	m.tagMgr = &testingtag.FakeManager{}
	m.teMgr = &mocks.Manager{}
	m.artifactMgr = &testingartifact.Manager{}
	m.artifactDao = &testingartifactdao.Dao{}
	m.execMgr = &testingtask.ExecutionManager{}

	m.TagService = &TagService{
		harborCli:   nil,
		repoMgr:     m.repoMgr,
		tagMgr:      m.tagMgr,
		teMgr:       m.teMgr,
		artifactMgr: m.artifactMgr,
		artifactDao: m.artifactDao,
		execMgr:     m.execMgr,
	}

}

func (m *TagServiceTestSuite) TestCountRepoTagsMap() {

	m.repoMgr.On("GetByName", mock.Anything, mock.Anything).Return(&model.RepoRecord{}, nil)
	_, err := m.TagService.CountRepoTagsMap(nil, &models.CountRepoTagsParam{})
	m.Require().Nil(err)
}

func (m *TagServiceTestSuite) TestCountTags() {

	m.repoMgr.On("GetByName", mock.Anything, mock.Anything).Return(&model.RepoRecord{}, nil)
	m.tagMgr.On("Count", mock.Anything, mock.Anything).Return(1, nil)

	total, err := m.TagService.CountTags(nil, &models.CountTagsParam{})
	m.Require().Nil(err)
	m.Equal(int64(1), total)
}

func (m *TagServiceTestSuite) TestListTags() {

	m.repoMgr.On("GetByName", mock.Anything, mock.Anything).Return(&model.RepoRecord{}, nil)
	m.tagMgr.On("Count", mock.Anything, mock.Anything).Return(1, nil)

	m.tagMgr.On("List", mock.Anything, mock.Anything).Return([]*tag.Tag{}, fmt.Errorf(""))

	total, ats, _ := m.TagService.ListTags(nil, &models.ListTagsParam{}, "", "")

	m.Equal(int64(1), total)
	m.Equal(0, len(ats))
}

func (m *TagServiceTestSuite) TestGetTag() {
	m.repoMgr.On("GetByName", mock.Anything, mock.Anything).Return(&model.RepoRecord{}, nil)

	m.tagMgr.On("List", mock.Anything, mock.Anything).Return([]*tag.Tag{}, fmt.Errorf(""))
	m.artifactMgr.On("Get", mock.Anything, mock.Anything).Return(&artifact.Artifact{}, nil)

	_, err := m.TagService.GetTag(nil, &models.GetTagParam{})
	m.Require().NotNil(err)
}

func TestTagServiceTestSuite(t *testing.T) {
	suite.Run(t, &TagServiceTestSuite{})
}
