package service

import (
	"context"
	"fmt"

	runtimeclient "github.com/go-openapi/runtime/client"
	"github.com/goharbor/harbor/src/lib/orm"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/statistic"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/pkg/chart/dao"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/models"
)

type StatisticService struct {
	clients  *harbor.HarborClient
	chartDao dao.Dao
}

func NewStatisticService(clients *harbor.HarborClient) *StatisticService {
	return &StatisticService{
		clients:  clients,
		chartDao: dao.New(),
	}
}

func (s *StatisticService) GetStatistic(ctx context.Context, username, password string) (*models.Statistic, error) {
	// get total storage
	o, err := orm.FromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("cannot get orm context: %w", err)
	}

	sql := `SELECT SUM(size) FROM blob`

	var totalSize int64
	if err := o.Raw(sql).QueryRow(&totalSize); err != nil {
		return nil, fmt.Errorf("query total image size failed: %w", err)
	}

	chartSize, err := s.chartDao.ChartSize(ctx, "")
	if err != nil {
		return nil, fmt.Errorf("query total chart size failed: %w", err)
	}

	totalSize += chartSize

	chartCount, err := s.chartDao.ChartRepoCount(ctx, "")
	if err != nil {
		return nil, fmt.Errorf("get chart count failed: %w", err)
	}

	//
	statistic, err := s.clients.V2Client.Statistic.GetStatistic(statistic.NewGetStatisticParamsWithContext(ctx), runtimeclient.BasicAuth(username, password))
	if err != nil {
		return nil, fmt.Errorf("get statistic failed: %w", err)
	}

	return &models.Statistic{
		Statistic:    *statistic.Payload,
		TotalStorage: totalSize,
		ChartCount:   chartCount,
	}, nil
}
