package service

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/common/utils"
	"github.com/goharbor/harbor/src/lib/q"
	"github.com/goharbor/harbor/src/pkg/reg/dao"
	models "github.com/goharbor/harbor/src/pkg/reg/dao"
	"github.com/goharbor/harbor/src/pkg/reg/model"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/harbor-addon/middleware"
)

type RegistryService struct {
	dao dao.DAO
}

func NewRegistryService() *RegistryService {
	return &RegistryService{
		dao: dao.NewDAO(),
	}
}

// List lists registries according to query provided.
func (m *RegistryService) List(query *q.Query, c *gin.Context) (int64, []*model.Registry, error) {
	secret := middleware.SecretFromContext(c)
	logger := middleware.LoggerFromContext(c)

	var secretKey string
	if secret == nil || secret.Data == nil {
		logger.Infof("secret or secret is nil,use default vaule")
		secretKey = "not-a-secure-key"
	}
	secretKeyByte, ok := secret.Data["secretKey"]
	if !ok {
		logger.Infof("secretKey not found,use default vaule")
		secretKey = "not-a-secure-key"
	}
	secretKey = string(secretKeyByte)

	count, err := m.dao.Count(middleware.HarborContext(c), query)
	if err != nil {
		logger.Errorf("get reg count failed: %s", err)
		return 0, nil, err
	}

	registries, err := m.dao.List(middleware.HarborContext(c), query)
	if err != nil {
		logger.Errorf("list registries failed: %s", err)
		return 0, nil, err
	}

	var results []*model.Registry
	for _, r := range registries {
		registry, err := fromDaoModel(r, secretKey)
		if err != nil {
			logger.Errorf("from dao model failed: %s", err)
			return 0, nil, err
		}
		results = append(results, registry)
	}

	return count, results, nil
}

// fromDaoModel converts DAO layer registry model to replication model.
// Also, if access secret is provided, decrypt it.
func fromDaoModel(registry *models.Registry, secretKey string) (*model.Registry, error) {
	r := &model.Registry{
		ID:           registry.ID,
		Name:         registry.Name,
		Description:  registry.Description,
		Type:         registry.Type,
		Credential:   &model.Credential{},
		URL:          registry.URL,
		Insecure:     registry.Insecure,
		Status:       registry.Status,
		CreationTime: registry.CreationTime,
		UpdateTime:   registry.UpdateTime,
	}

	if len(registry.AccessKey) != 0 {
		credentialType := registry.CredentialType
		if len(credentialType) == 0 {
			credentialType = model.CredentialTypeBasic
		}
		decrypted, err := decrypt(registry.AccessSecret, secretKey)
		if err != nil {
			return nil, fmt.Errorf("harbor addon decrypt accessSecret failed: %s", err)
		}
		r.Credential = &model.Credential{
			Type:         credentialType,
			AccessKey:    registry.AccessKey,
			AccessSecret: decrypted,
		}
	}

	return r, nil
}

// decrypt checks whether access secret is set in the registry, if so, decrypt it.
func decrypt(secret, secretKey string) (string, error) {
	if len(secret) == 0 {
		return "", nil
	}

	decrypted, err := utils.ReversibleDecrypt(secret, secretKey)
	if err != nil {
		return "", err
	}

	return decrypted, nil
}
