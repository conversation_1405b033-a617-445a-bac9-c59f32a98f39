package auth

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	commonhttp "github.com/goharbor/harbor/src/common/http"
	"github.com/karlseguin/ccache/v2"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
)

type Interface interface {
	Validate(username, password string, scopes []*token.ResourceActions, privateKey []byte, ignoreResourceType bool) (bool, error)
	GetClaims(username, password string, scopes []*token.ResourceActions, privateKey []byte) (*token.Claims, error)
}

type Authorizer struct {
	authDomain string
	service    string

	cache  *ccache.Cache
	client *commonhttp.Client
}

func NewAuthorizer(service, authDomain string, insecure bool) *Authorizer {
	trans := &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
			DualStack: true,
		}).DialContext,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: insecure,
		},
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	return &Authorizer{
		authDomain: authDomain,
		service:    service,
		cache:      ccache.New(ccache.Configure().MaxSize(5000).ItemsToPrune(500).GetsPerPromote(10)),
		client:     commonhttp.NewClient(&http.Client{Transport: trans}),
	}
}

func (a *Authorizer) Validate(username, password string, scopes []*token.ResourceActions, privateKey []byte, ignoreResourceType bool) (bool, error) {
	item := a.cache.Get(genKey(username, scopes))
	if item != nil && !item.Expired() {
		return true, nil
	}

	t, err := a.fetchToken(username, password, scopes)
	if err != nil {
		return false, err
	}

	claims, err := token.Parse(t.Token, privateKey)
	if err != nil {
		return false, fmt.Errorf("parse token failed: %s", err)
	}

	matched := true
	for _, scope := range scopes {
		if matched {
			for _, act := range scope.Actions {
				if !claims.Can(scope.Type, scope.Name, act, ignoreResourceType) {
					matched = false
					break
				}
			}
		}
	}

	if matched {
		a.cache.Set(genKey(username, scopes), claims, time.Unix(claims.ExpiresAt, 0).Sub(time.Now()))
	}

	return matched, nil
}

func (a *Authorizer) GetClaims(username, password string, scopes []*token.ResourceActions, privateKey []byte) (*token.Claims, error) {
	// get token from cache first
	item := a.cache.Get(genKey(username, scopes))
	if item != nil && !item.Expired() {
		if claims, ok := item.Value().(*token.Claims); ok {
			return claims, nil
		}
	}

	// get no token from cache, fetch it from the token service
	t, err := a.fetchToken(username, password, scopes)
	if err != nil {
		return nil, err
	}

	claims, err := token.Parse(t.Token, privateKey)
	if err != nil {
		return nil, fmt.Errorf("parse token failed: %s", err)
	}

	// set the claims into the cache
	a.cache.Set(genKey(username, scopes), claims, time.Unix(claims.ExpiresAt, 0).Sub(time.Now()))
	return claims, nil
}

func (a *Authorizer) fetchToken(username, password string, scopes []*token.ResourceActions) (*token.Token, error) {
	url, err := url.Parse(a.authDomain)
	if err != nil {
		return nil, err
	}
	query := url.Query()
	query.Add("service", a.service)
	for _, scope := range scopes {
		query.Add("scope", scope.String())
	}
	url.RawQuery = query.Encode()

	req, err := http.NewRequest(http.MethodGet, url.String(), nil)
	if err != nil {
		return nil, err
	}

	req.SetBasicAuth(username, password)

	resp, err := a.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		return nil, &commonhttp.Error{
			Code:    resp.StatusCode,
			Message: string(body),
		}
	}

	token := &token.Token{}
	if err = json.Unmarshal(body, &token); err != nil {
		return nil, err
	}
	if len(token.Token) == 0 && len(token.AccessToken) > 0 {
		token.Token = token.AccessToken
	}

	return token, nil
}

func genKey(username string, scopes []*token.ResourceActions) string {
	stringSlice := sort.StringSlice{username}
	for _, scope := range scopes {
		stringSlice = append(stringSlice, scope.String())
	}

	sort.Sort(stringSlice)

	return strings.Join(stringSlice, "#")
}
