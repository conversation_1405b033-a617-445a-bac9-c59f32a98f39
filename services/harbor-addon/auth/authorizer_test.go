package auth

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	commonhttp "github.com/goharbor/harbor/src/common/http"
	"github.com/karlseguin/ccache/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/token"
)

// repository:ccr-image/harbor-addon:pull,push&scope=repository:ccr-image/harbor-addon:pull&scope=ormb:ccr:push

func Test_genKey(t *testing.T) {
	type args struct {
		username string
		scopes   []*token.ResourceActions
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "case1",
			args: args{
				username: "wenmanxiang1",
				scopes: []*token.ResourceActions{
					{
						Type:    "repository",
						Name:    "ccr-image/harbor-addon",
						Actions: []string{"pull,push"},
					},
					{
						Type:    "repository",
						Name:    "ccr-image/harbor-core",
						Actions: []string{"pull"},
					},
					{
						Type:    "ormb",
						Name:    "ccr",
						Actions: []string{"push"},
					},
				},
			},
			want: "ormb:ccr:push#repository:ccr-image/harbor-addon:pull,push#repository:ccr-image/harbor-core:pull#wenmanxiang1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := genKey(tt.args.username, tt.args.scopes); got != tt.want {
				t.Errorf("genKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_NewAuthorizer(t *testing.T) {
	au := NewAuthorizer("harbor-registry", "test.com", true)
	assert.NotNil(t, au)
}

type TestTransport struct {
	resp *http.Response
}

func (t *TestTransport) RoundTrip(*http.Request) (*http.Response, error) {
	return t.resp, nil
}

const (
	privateKey = `*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
)

func Test_Authorizer_Validate(t *testing.T) {
	type fields struct {
		autho Authorizer
	}

	type args struct {
		username           string
		password           string
		scopes             []*token.ResourceActions
		privateKey         string
		ignoreResourceType bool
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		result  bool
		wantErr bool
	}{
		{
			name: "cache命中",
			fields: func() fields {
				c := ccache.New(ccache.Configure().MaxSize(100).ItemsToPrune(10).GetsPerPromote(10))
				sp := []*token.ResourceActions{
					{
						Type:    "test",
						Name:    "test",
						Actions: []string{"test"},
					},
				}
				c.Set(genKey("user", sp), "exist", time.Minute)
				return fields{
					autho: Authorizer{
						cache: c,
					},
				}
			}(),
			args: func() args {
				return args{
					username: "user",
					password: "xxxx",
					scopes: []*token.ResourceActions{
						{
							Type:    "test",
							Name:    "test",
							Actions: []string{"test"},
						},
					},
					privateKey:         "test",
					ignoreResourceType: true,
				}
			}(),
			result:  true,
			wantErr: false,
		},
		{
			name: "cache没命中,fetch token失败",
			fields: func() fields {
				c := ccache.New(ccache.Configure().MaxSize(100).ItemsToPrune(10).GetsPerPromote(10))
				cli := commonhttp.NewClient(&http.Client{
					Transport: &TestTransport{resp: &http.Response{
						Status:     http.StatusText(500),
						StatusCode: http.StatusInternalServerError,
					}},
				})
				return fields{
					autho: Authorizer{
						authDomain: "http://test.com",
						service:    "test",
						cache:      c,
						client:     cli,
					},
				}
			}(),
			args: func() args {
				return args{
					username: "user",
					password: "xxxx",
					scopes: []*token.ResourceActions{
						{
							Type:    "test",
							Name:    "test",
							Actions: []string{"test"},
						},
					},
					privateKey:         "test",
					ignoreResourceType: true,
				}
			}(),
			result:  false,
			wantErr: true,
		},
		{
			name: "cache没命中,parse token失败",
			fields: func() fields {
				c := ccache.New(ccache.Configure().MaxSize(100).ItemsToPrune(10).GetsPerPromote(10))

				cli := commonhttp.NewClient(&http.Client{
					Transport: &TestTransport{resp: &http.Response{
						Status:     http.StatusText(200),
						StatusCode: http.StatusOK,
						Body:       io.NopCloser(strings.NewReader("test")),
					}},
				})
				return fields{
					autho: Authorizer{
						authDomain: "http://test.com",
						service:    "test",
						cache:      c,
						client:     cli,
					},
				}
			}(),
			args: func() args {
				return args{
					username: "user",
					password: "xxxx",
					scopes: []*token.ResourceActions{
						{
							Type:    "test",
							Name:    "test",
							Actions: []string{"test"},
						},
					},
					privateKey:         privateKey,
					ignoreResourceType: true,
				}
			}(),
			result:  false,
			wantErr: true,
		},
		{
			name: "cache没命中,token不match",
			fields: func() fields {
				c := ccache.New(ccache.Configure().MaxSize(100).ItemsToPrune(10).GetsPerPromote(10))
				tokenMaker := token.NewTokenMaker(30, "")
				tk, err := tokenMaker.MakeToken("user", []byte(privateKey), []*token.ResourceActions{
					{
						Type:    "test",
						Name:    "test",
						Actions: []string{"test"},
					},
				})
				require.NoError(t, err)
				tkBody, err := json.Marshal(tk)
				require.NoError(t, err)

				cli := commonhttp.NewClient(&http.Client{
					Transport: &TestTransport{resp: &http.Response{
						Status:     http.StatusText(200),
						StatusCode: http.StatusOK,
						Body:       io.NopCloser(bytes.NewReader(tkBody)),
					}},
				})
				return fields{
					autho: Authorizer{
						authDomain: "http://test.com",
						service:    "test",
						cache:      c,
						client:     cli,
					},
				}
			}(),
			args: func() args {
				return args{
					username: "user",
					password: "xxxx",
					scopes: []*token.ResourceActions{
						{
							Type:    "test",
							Name:    "test",
							Actions: []string{"noaccess"},
						},
					},
					privateKey:         privateKey,
					ignoreResourceType: true,
				}
			}(),
			result:  false,
			wantErr: false,
		},
		{
			name: "cache没命中,resource type mismatch",
			fields: func() fields {
				c := ccache.New(ccache.Configure().MaxSize(100).ItemsToPrune(10).GetsPerPromote(10))
				tokenMaker := token.NewTokenMaker(30, "")
				tk, err := tokenMaker.MakeToken("user", []byte(privateKey), []*token.ResourceActions{
					{
						Type:    "test",
						Name:    "test",
						Actions: []string{"test"},
					},
				})
				require.NoError(t, err)
				tkBody, err := json.Marshal(tk)
				require.NoError(t, err)

				cli := commonhttp.NewClient(&http.Client{
					Transport: &TestTransport{resp: &http.Response{
						Status:     http.StatusText(200),
						StatusCode: http.StatusOK,
						Body:       io.NopCloser(bytes.NewReader(tkBody)),
					}},
				})
				return fields{
					autho: Authorizer{
						authDomain: "http://test.com",
						service:    "test",
						cache:      c,
						client:     cli,
					},
				}
			}(),
			args: func() args {
				return args{
					username: "user",
					password: "xxxx",
					scopes: []*token.ResourceActions{
						{
							Type:    "wrong",
							Name:    "test",
							Actions: []string{"test"},
						},
					},
					privateKey:         privateKey,
					ignoreResourceType: false,
				}
			}(),
			result:  false,
			wantErr: false,
		},
		{
			name: "cache没命中,token正常",
			fields: func() fields {
				c := ccache.New(ccache.Configure().MaxSize(100).ItemsToPrune(10).GetsPerPromote(10))
				tokenMaker := token.NewTokenMaker(30, "")
				tk, err := tokenMaker.MakeToken("user", []byte(privateKey), []*token.ResourceActions{
					{
						Type:    "test",
						Name:    "test",
						Actions: []string{"test"},
					},
				})
				require.NoError(t, err)
				tkBody, err := json.Marshal(tk)
				require.NoError(t, err)

				cli := commonhttp.NewClient(&http.Client{
					Transport: &TestTransport{resp: &http.Response{
						Status:     http.StatusText(200),
						StatusCode: http.StatusOK,
						Body:       io.NopCloser(bytes.NewReader(tkBody)),
					}},
				})
				return fields{
					autho: Authorizer{
						authDomain: "http://test.com",
						service:    "test",
						cache:      c,
						client:     cli,
					},
				}
			}(),
			args: func() args {
				return args{
					username: "user",
					password: "xxxx",
					scopes: []*token.ResourceActions{
						{
							Type:    "test",
							Name:    "test",
							Actions: []string{"test"},
						},
					},
					privateKey:         privateKey,
					ignoreResourceType: true,
				}
			}(),
			result:  true,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tt.fields.autho.Validate(tt.args.username, tt.args.password, tt.args.scopes, []byte(tt.args.privateKey), tt.args.ignoreResourceType)
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if result != tt.result {
				t.Errorf("Validate() error, expect %t, actual is %t", tt.result, result)
				return
			}
		})
	}
}
