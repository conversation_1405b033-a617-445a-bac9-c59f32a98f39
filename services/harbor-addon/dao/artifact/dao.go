package artifact

import (
	"context"
	"fmt"

	beegoorm "github.com/astaxie/beego/orm"
	"github.com/goharbor/harbor/src/lib/orm"
	"github.com/goharbor/harbor/src/pkg/artifact/dao"
)

type DAO interface {
	FindArtifactByIDs(ctx context.Context, ids ...interface{}) ([]*dao.Artifact, error)
}

type Dao struct {
}

func (d *Dao) FindArtifactByIDs(ctx context.Context, ids ...interface{}) ([]*dao.Artifact, error) {
	ormer, err := orm.FromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("get orm failed: %w", err)
	}

	qs := ormer.QueryTable(&dao.Artifact{})
	qs.SetCond(beegoorm.NewCondition().And("id__in", ids...))

	var arts []*dao.Artifact
	_, err = qs.All(&arts)
	return arts, err
}
