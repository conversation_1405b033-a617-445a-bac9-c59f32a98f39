// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	context "context"

	dao "github.com/goharbor/harbor/src/pkg/artifact/dao"
	mock "github.com/stretchr/testify/mock"
)

// DAO is an autogenerated mock type for the DAO type
type DAO struct {
	mock.Mock
}

// FindArtifactByIDs provides a mock function with given fields: ctx, ids
func (_m *DAO) FindArtifactByIDs(ctx context.Context, ids ...interface{}) ([]*dao.Artifact, error) {
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, ids...)
	ret := _m.Called(_ca...)

	var r0 []*dao.Artifact
	if rf, ok := ret.Get(0).(func(context.Context, ...interface{}) []*dao.Artifact); ok {
		r0 = rf(ctx, ids...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*dao.Artifact)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...interface{}) error); ok {
		r1 = rf(ctx, ids...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewDAO interface {
	mock.TestingT
	Cleanup(func())
}

// NewDAO creates a new instance of DAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewDAO(t mockConstructorTestingTNewDAO) *DAO {
	mock := &DAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
