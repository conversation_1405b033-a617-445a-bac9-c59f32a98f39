package chart

import "time"

type Chart struct {
	ID          int64      `gorm:"primaryKey"`
	ProjectName string     `gorm:"column:project_name;not null;uniqueIndex:chart_project_name_version"`
	Name        string     `gorm:"column:name;not null;uniqueIndex:chart_project_name_version"`
	Version     string     `gorm:"column:version;not null;uniqueIndex:chart_project_name_version"`
	Size        int64      `gorm:"column:size;default:0"`
	DeleteTime  *time.Time `gorm:"column:delete_time"`
	CreateTime  *time.Time `gorm:"column:create_time"`
}

func (c *Chart) TableName() string {
	return "chart"
}
