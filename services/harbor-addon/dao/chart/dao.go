package chart

import (
	"errors"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Dao struct {
	db *gorm.DB
}

func New<PERSON>ao(db *gorm.DB) *Dao {
	return &Dao{
		db: db,
	}
}

func (d *Dao) RepoCount(projectName string) (int64, error) {
	var total int64
	query := d.db.Table("chart").Select("count(distinct(project_name, name))")
	if projectName != "" {
		query = query.Where("project_name = ?", projectName)
	}

	result := query.Count(&total)

	return total, result.Error
}

func (d *Dao) SumSize() (int64, error) {
	var totalSize int64

	result := d.db.Table("chart").Select("sum(size)").Scan(&totalSize)

	return totalSize, result.Error
}

func (d *Dao) QueryUnreadyCharts() ([]*Chart, error) {
	charts := make([]*Chart, 0)
	result := d.db.Table("chart").Where("size=0 or delete_time is not null").Find(&charts)

	return charts, result.Error
}

func (d *Dao) Create(chart *Chart) (int64, error) {
	result := d.db.Table("chart").Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "project_name"}, {Name: "name"}, {Name: "version"}},
		DoUpdates: clause.AssignmentColumns([]string{"create_time"}),
	}).Create(chart)

	return chart.ID, result.Error
}

func (d *Dao) Get(project, name, version string) (*Chart, error) {
	var chrt Chart
	result := d.db.Table("chart").Where("project_name=? and name=? and version=?", project, name, version).First(&chrt)

	return &chrt, result.Error
}

func (d *Dao) Update(chrt *Chart) error {
	result := d.db.Table("chart").Where("id=?", chrt.ID).Updates(&Chart{
		Size:       chrt.Size,
		DeleteTime: chrt.DeleteTime,
	})

	return result.Error
}

func (d *Dao) UpdateDeleteTimeByProjectAndName(project, name string, deleteTime time.Time) error {
	return d.db.Table("chart").Where("project_name=? and name=?", project, name).Update("delete_time", deleteTime).Error
}

func (d *Dao) DeleteByProjectAndName(project, name string) error {
	err := d.db.Table("chart").Where("project_name=? and name=?", project, name).Delete(&Chart{}).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil
	}

	return err
}

func (d *Dao) Delete(id int64, createTime *time.Time) error {
	err := d.db.Table("chart").Where("id=? and create_time=?", id, createTime).Delete(&Chart{}).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil
	}

	return err
}
