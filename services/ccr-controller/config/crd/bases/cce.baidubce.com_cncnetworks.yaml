
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: ""
  creationTimestamp: null
  name: cncnetworks.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    kind: CNCNetwork
    listKind: CNCNetworkList
    plural: cncnetworks
    singular: cncnetwork
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: PHASE
      type: string
    - jsonPath: .status.publicStatus.address
      name: ADDR
      type: string
    - jsonPath: .status.reason
      name: REASON
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: CNCNetwork is the Schema for the cncnetworks API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: CNCNetworkSpec defines the desired state of CNCNetwork
            properties:
              UserID:
                type: string
              accountID:
                type: string
              blbID:
                type: string
              customDomains:
                items:
                  description: CustomDomain customDomain definition
                  properties:
                    certID:
                      type: string
                    certName:
                      type: string
                    domainName:
                      type: string
                  type: object
                type: array
              domain:
                properties:
                  privateDomain:
                    type: string
                  publicDomain:
                    type: string
                type: object
              privateLinks:
                items:
                  properties:
                    autoDNS:
                      type: boolean
                    createdBy:
                      type: string
                    ipAddr:
                      type: string
                    subnetID:
                      type: string
                    vpcID:
                      type: string
                  type: object
                type: array
              publicLink:
                properties:
                  eipOn:
                    type: boolean
                  whiteList:
                    items:
                      properties:
                        description:
                          type: string
                        ipCidr:
                          type: string
                      type: object
                    type: array
                type: object
            type: object
          status:
            description: CNCNetworkStatus defines the observed state of CNCNetwork
            properties:
              blbId:
                type: string
              customDomains:
                items:
                  properties:
                    certExpireTime:
                      format: date-time
                      type: string
                    certID:
                      type: string
                    certName:
                      type: string
                    domainICPed:
                      type: boolean
                    domainName:
                      type: string
                    domainStatus:
                      type: string
                    domainStatusDesc:
                      type: string
                  type: object
                type: array
              lastProbeTime:
                format: date-time
                type: string
              linkStatus:
                items:
                  properties:
                    createdBy:
                      type: string
                    domain:
                      type: string
                    ip:
                      type: string
                    reason:
                      type: string
                    recordID:
                      type: string
                    resourceSource:
                      type: string
                    serviceID:
                      type: string
                    startTime:
                      format: date-time
                      type: string
                    status:
                      type: string
                    subnetID:
                      type: string
                    vpcID:
                      type: string
                  type: object
                type: array
              phase:
                description: 'INSERT ADDITIONAL STATUS FIELD - define observed state
                  of cluster Important: Run "make" to regenerate code after modifying
                  this file'
                type: string
              publicStatus:
                properties:
                  address:
                    type: string
                  domain:
                    type: string
                  reason:
                    type: string
                  status:
                    type: string
                  whiteList:
                    items:
                      properties:
                        description:
                          type: string
                        ipCidr:
                          type: string
                      type: object
                    type: array
                type: object
              publishPoint:
                type: string
              reason:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
