package controllers

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-controller/ccr"
)

func TestCCRReconciler_Reconcile(t *testing.T) {

	scheme := runtime.NewScheme()
	v1alpha1.AddToScheme(scheme)

	reconciler := &CCRReconciler{
		Client:  fake.NewFakeClientWithScheme(scheme),
		Scheme:  scheme,
		Handler: &ccr.CCRHandler{},
	}

	result, err := reconciler.Reconcile(context.Background(), reconcile.Request{NamespacedName: types.NamespacedName{Name: "non-exist"}})
	assert.Nil(t, err)
	assert.Equal(t, ctrl.Result{}, result)

	reconciler.Client = fake.NewFakeClient()
	result, err = reconciler.Reconcile(context.Background(), reconcile.Request{NamespacedName: types.NamespacedName{Name: "test"}})
	assert.NotNil(t, err)
	assert.Equal(t, ctrl.Result{RequeueAfter: 1 * time.Second}, result)
}
