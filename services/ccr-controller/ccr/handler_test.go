package ccr

import (
	"context"
	"fmt"
	"sync"
	"testing"

	"sigs.k8s.io/controller-runtime/pkg/manager"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-controller/ccr/config"
	testingutils "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
)

func TestCCRHandler_Handle(t *testing.T) {

	scheme := runtime.NewScheme()
	v1alpha1.AddToScheme(scheme)

	containerSpec := "./config/container_spec.yaml"

	componentConfig := config.NewComponentPodSpecOrDie(containerSpec)
	handler := NewCCRHandlerOrDie(&config.Config{}, componentConfig, nil, nil)

	handler.client = fake.NewFakeClientWithScheme(scheme, &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "test"}, Status: v1alpha1.CCRStatus{Phase: v1alpha1.CCRStarting}})
	assert.Error(t, handler.Handle(context.Background(), &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "test"}, Status: v1alpha1.CCRStatus{Phase: v1alpha1.CCRStarting}}))

	handler.client = fake.NewFakeClientWithScheme(scheme, &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "test"}, Status: v1alpha1.CCRStatus{Phase: v1alpha1.CCRStarting}})
	assert.Error(t, handler.handleStarting(context.Background(), &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "test"}, Status: v1alpha1.CCRStatus{Phase: v1alpha1.CCRStarting}}))

}

func TestCCRHandler_handleStarting(t *testing.T) {

	scheme := runtime.NewScheme()
	v1alpha1.AddToScheme(scheme)

	containerSpec := "./config/container_spec.yaml"

	componentConfig := config.NewComponentPodSpecOrDie(containerSpec)
	handler := NewCCRHandlerOrDie(&config.Config{}, componentConfig, nil, nil)

	handler.client = fake.NewFakeClientWithScheme(scheme, &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "test"}, Status: v1alpha1.CCRStatus{Phase: v1alpha1.CCRStarting}})
	assert.Error(t, handler.handleStarting(context.Background(), &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "test"}, Status: v1alpha1.CCRStatus{Phase: v1alpha1.CCRStarting}}))

}

func TestCCRHandler_handleStopping(t *testing.T) {

	scheme := runtime.NewScheme()
	v1alpha1.AddToScheme(scheme)

	containerSpec := "./config/container_spec.yaml"

	componentConfig := config.NewComponentPodSpecOrDie(containerSpec)
	handler := NewCCRHandlerOrDie(&config.Config{}, componentConfig, nil, nil)

	handler.client = fake.NewFakeClientWithScheme(scheme, &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "test"}, Status: v1alpha1.CCRStatus{Phase: v1alpha1.CCRStarting}})
	assert.Error(t, handler.handleStopping(context.Background(), &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "test"}, Status: v1alpha1.CCRStatus{Phase: v1alpha1.CCRStarting}}))

}

func TestCCRHandler_handleCreateBos(t *testing.T) {
	scheme := runtime.NewScheme()
	v1alpha1.AddToScheme(scheme)

	type fields struct {
		cli client.Client
	}

	type args struct {
		ctx context.Context
		obj *v1alpha1.CCR
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "bucket字段已经设置",
			fields: fields{
				cli: nil,
			},
			args: args{
				ctx: context.Background(),
				obj: &v1alpha1.CCR{
					Status: v1alpha1.CCRStatus{
						Bucket: "test",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "spec的storage字段不为空",
			fields: fields{
				cli: nil,
			},
			args: args{
				ctx: context.Background(),
				obj: &v1alpha1.CCR{
					Spec: v1alpha1.CCRSpec{
						Storage: &v1alpha1.CCRStorage{
							Bucket: "test",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "获取bos资源失败",
			fields: func() fields {
				cli := testingutils.NewMockK8sClient(gomock.NewController(t))
				cli.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("error"))
				cli.EXPECT().Status().Return(cli)
				cli.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				return fields{
					cli: cli,
				}
			}(),
			args: args{
				ctx: context.Background(),
				obj: &v1alpha1.CCR{},
			},
			wantErr: true,
		},
		{
			name: "bos不存在,且创建失败",
			fields: func() fields {
				cli := testingutils.NewMockK8sClient(gomock.NewController(t))

				cli.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(apierrors.NewNotFound(schema.GroupResource{}, "error"))
				cli.EXPECT().Create(gomock.Any(), gomock.Any()).Return(fmt.Errorf("failed"))
				cli.EXPECT().Status().Return(cli)
				cli.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				return fields{
					cli: cli,
				}
			}(),
			args: args{
				ctx: context.Background(),
				obj: &v1alpha1.CCR{},
			},
			wantErr: true,
		},
		{
			name: "bos不存在,且创建成功",
			fields: func() fields {
				cli := testingutils.NewMockK8sClient(gomock.NewController(t))

				cli.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(apierrors.NewNotFound(schema.GroupResource{}, "error"))
				cli.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
				return fields{
					cli: cli,
				}
			}(),
			args: args{
				ctx: context.Background(),
				obj: &v1alpha1.CCR{},
			},
			wantErr: true,
		},
		{
			name: "bos存在,且ready",
			fields: func() fields {
				cli := fake.NewFakeClientWithScheme(scheme, &v1alpha1.Bos{Status: v1alpha1.BosStatus{Phase: v1alpha1.BosStatusPhaseReady}})
				return fields{
					cli: cli,
				}
			}(),
			args: args{
				ctx: context.Background(),
				obj: &v1alpha1.CCR{},
			},
			wantErr: false,
		},
		{
			name: "bos存在,且failed",
			fields: func() fields {
				cli := fake.NewFakeClientWithScheme(scheme, &v1alpha1.Bos{Status: v1alpha1.BosStatus{Phase: v1alpha1.BosStatusPhaseFailed}})
				return fields{
					cli: cli,
				}
			}(),
			args: args{
				ctx: context.Background(),
				obj: &v1alpha1.CCR{},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ccrHandler := &CCRHandler{
				config: &config.Config{
					UserID:            "xxxxx",
					DisableBosEncrypt: true,
				},
				client: tt.fields.cli,
			}
			err := ccrHandler.handleCreateBos(tt.args.ctx, tt.args.obj)
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

// TestCCRHandler_handlePendingPhase ensures the CCR Handler handles Pending Phase correctly
func TestCCRHandler_handlePendingPhase(t *testing.T) {
	type fields struct {
		config        *config.Config
		containerSpec *config.CCRTypePodSpec
		client        client.Client
		mgr           manager.Manager
		dbs           *sync.Map
	}
	type args struct {
		ctx    context.Context
		object *v1alpha1.CCR
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "normal",
			fields: func() fields {
				scheme := runtime.NewScheme()
				v1alpha1.AddToScheme(scheme)

				containerSpec := "./config/container_spec.yaml"

				componentConfig := config.NewComponentPodSpecOrDie(containerSpec)

				return fields{
					config:        &config.Config{},
					containerSpec: componentConfig,
					client: fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects(&v1alpha1.CCR{
						ObjectMeta: v1.ObjectMeta{Name: "test"},
						Spec:       v1alpha1.CCRSpec{AccountID: "xxx"}}).Build(),
					mgr: nil,
					dbs: nil,
				}
			}(),
			args: func() args {

				return args{
					ctx: context.Background(),
					object: &v1alpha1.CCR{
						ObjectMeta: v1.ObjectMeta{Name: "test"},
						Spec:       v1alpha1.CCRSpec{AccountID: "xxx"},
					},
				}
			}(),
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return false
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CCRHandler{
				config:        tt.fields.config,
				containerSpec: tt.fields.containerSpec,
				client:        tt.fields.client,
				mgr:           tt.fields.mgr,
				dbs:           tt.fields.dbs,
			}
			tt.wantErr(t, c.handlePendingPhase(tt.args.ctx, tt.args.object), fmt.Sprintf("handlePendingPhase(%v, %v)", tt.args.ctx, tt.args.object))
		})
	}
}
