package ccr

import (
	"context"
	"encoding/base64"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-controller/ccr/config"
)

func ValuesFromCCR(ctx context.Context, conf *config.Config, containerSpec *config.CCRTypePodSpec, cli client.Client, obj *ccrv1alpha1.CCR, zeroReplicas bool) (map[string]interface{}, error) {
	logger := log.FromContext(ctx)
	var databaseConfig *config.DatabaseConfig

	if obj.Spec.Database != nil {
		password, err := base64.StdEncoding.DecodeString(obj.Spec.Database.Password)
		if err != nil {
			logger.Error(err, "invalid password provided in spec")
			return nil, err
		}

		databaseConfig = &config.DatabaseConfig{
			Host:     obj.Spec.Database.Host,
			Port:     fmt.Sprintf("%d", obj.Spec.Database.Port),
			Username: obj.Spec.Database.Username,
			Password: string(password),
		}
	} else {
		var pgObj ccrv1alpha1.Postgres

		if err := cli.Get(ctx, client.ObjectKey{Namespace: obj.GetName(), Name: obj.GetName()}, &pgObj); err != nil {
			logger.Error(err, "get postgres failed")
			return nil, err
		}

		passwd, err := base64.StdEncoding.DecodeString(pgObj.Spec.Password)
		if err != nil {
			logger.Error(err, "invalid password format")
			return nil, err
		}

		databaseConfig = &config.DatabaseConfig{
			Host:     pgObj.Status.Host,
			Port:     pgObj.Status.Port,
			Username: pgObj.Status.UserName,
			Password: string(passwd),
		}
	}

	// get bos config
	bosConfig := &config.BosConfig{
		Ak:       conf.AccessKey,
		Sk:       conf.SecretKey,
		Bucket:   obj.GetName(),
		Endpoint: conf.BosEndpoint,
	}

	// get admin password
	secretName := fmt.Sprintf("%s-harbor-core", obj.GetName())
	var coreSecret corev1.Secret
	err := cli.Get(ctx, client.ObjectKey{Namespace: obj.GetName(), Name: secretName}, &coreSecret)
	if err != nil {
		logger.V(2).Error(err, "get harbor core secret failed")
		return nil, err
	}

	// get job service secret
	secretName = fmt.Sprintf("%s-harbor-jobservice", obj.GetName())
	var jobsvcSecret corev1.Secret
	err = cli.Get(ctx, client.ObjectKey{Namespace: obj.GetName(), Name: secretName}, &jobsvcSecret)
	if err != nil {
		logger.V(2).Error(err, "get harbor jobservice secret failed")
		return nil, err
	}

	// get registry secret
	secretName = fmt.Sprintf("%s-harbor-registry", obj.GetName())
	var registrySecret corev1.Secret
	err = cli.Get(ctx, client.ObjectKey{Namespace: obj.GetName(), Name: secretName}, &registrySecret)
	if err != nil {
		logger.V(2).Error(err, "get harbor jobservice secret failed")
		return nil, err
	}

	var ccrCiperSecret corev1.Secret
	err = cli.Get(ctx, client.ObjectKey{Namespace: obj.GetName(), Name: "ccr-cipher"}, &ccrCiperSecret)
	if err != nil {
		logger.V(2).Error(err, "get ccr cipher secret failed")
		return nil, err
	}

	overwriteConfig, err := config.GenerateOverwriteConfig(&config.OverwriteConfig{
		CoreSecret:       string(coreSecret.Data["secret"]),
		CsrfKey:          string(coreSecret.Data["CSRF_KEY"]),
		JobserviceSecret: string(jobsvcSecret.Data["JOBSERVICE_SECRET"]),
		RegistrySecret:   string(registrySecret.Data["REGISTRY_HTTP_SECRET"]),
		AdminPassword:    string(coreSecret.Data["HARBOR_ADMIN_PASSWORD"]),
		JwtTlsCrt:        base64.StdEncoding.EncodeToString(ccrCiperSecret.Data["tls.crt"]),
		JwtTlsKey:        base64.StdEncoding.EncodeToString(ccrCiperSecret.Data["tls.key"]),
	})

	if err != nil {
		logger.V(2).Error(err, "generate overwrite config failed")
		return nil, err
	}

	return config.GenerateBasicValue(
		conf,
		(*containerSpec)[obj.Status.Type],
		bosConfig,
		databaseConfig,
		config.NewInstanceConfig(obj.GetName(),
			obj.GetName(),
			"",
			"",
			""),
		overwriteConfig,
		zeroReplicas)
}
