package ccr

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/manager"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/helm"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-controller/ccr/config"
)

const (
	DefaultInterval time.Duration = time.Minute
)

type CCRMonitor struct {
	client        client.Client
	mgr           manager.Manager
	globalConfig  *config.Config
	containerSpec *config.CCRTypePodSpec
	interval      time.Duration
}

func NewCCRMonitor(mgr manager.Manager, cli client.Client, cfg *config.Config, containerSpec *config.CCRTypePodSpec) *CCRMonitor {
	return &CCRMonitor{
		client:        cli,
		mgr:           mgr,
		globalConfig:  cfg,
		containerSpec: containerSpec,
		interval:      DefaultInterval,
	}
}

func (m *CCRMonitor) Start(ctx context.Context) error {
	synced := m.mgr.GetCache().WaitForCacheSync(ctx)
	if !synced {
		panic("cache could not be synced")
	}
	// set index

	wait.Until(func() {
		m.run(ctx)
	}, m.interval, ctx.Done())

	return nil
}

func (m *CCRMonitor) NeedLeaderElection() bool {
	return true
}

func (m *CCRMonitor) run(ctx context.Context) error {
	logger := log.FromContext(ctx)
	ccrList := ccrv1alpha1.CCRList{}

	err := m.mgr.GetCache().List(ctx, &ccrList)
	if err != nil {
		logger.V(2).Error(err, "list all ccr failed")
		return err
	}

	for _, v := range ccrList.Items {
		obj := v.DeepCopy()
		switch obj.Status.Phase {
		case ccrv1alpha1.CCRCreated:
			m.monitorPhase(ctx, obj, func(c context.Context, ccr *ccrv1alpha1.CCR) error {
				err := m.postDeleteLibraryProject(c, ccr)
				if err != nil {
					return fmt.Errorf("delete library failed: %w", err)
				}

				ccr.Status.Phase = ccrv1alpha1.CCRRunning
				current := metav1.Now()
				ccr.Status.StartTime = &current
				return nil
			})

		// 升级失败不一定会导致实例失败，可以继续尝试重新升级
		// TODO: 对于upgrade failed的情况，修改其annotation，关闭自动校准功能
		case ccrv1alpha1.CCRUpgraded, ccrv1alpha1.CCRUpgradeFailed:
			m.monitorPhase(ctx, obj, func(c context.Context, ccr *ccrv1alpha1.CCR) error {
				ccr.Status.Phase = ccrv1alpha1.CCRRunning
				return nil
			})
		case ccrv1alpha1.CCRRunning:
			m.monitorPhase(ctx, obj, func(c context.Context, obj *ccrv1alpha1.CCR) error {
				return m.monitorCalibrating(c, obj)
			})
		}
	}

	//cache.List(ctx, &ccrv1alpha1.CCRList{}, &client.ListOptions{FieldSelector: })
	return nil
}

func (m *CCRMonitor) monitorCalibrating(ctx context.Context, obj *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)

	if obj.Annotations[ccrv1alpha1.CCRCalibratePolicyAnnotation] != ccrv1alpha1.CCRCalibratePolicyAnnotationAuto {
		return nil
	}

	if obj.Status.Phase == ccrv1alpha1.CCRStopped {
		logger.V(2).Info("ccr status phase is stop, skip!")
		return nil
	}

	values, err := ValuesFromCCR(ctx, m.globalConfig, m.containerSpec, m.client, obj, false)
	if err != nil {
		logger.Error(err, "get values failed")
		return err
	}

	expected, err := helm.Template(ctx, obj.GetName(), obj.GetName(), m.globalConfig.ChartPath, values, m.mgr)
	if err != nil {
		logger.Error(err, "get expected manifest failed")
		return err
	}

	actual, err := helm.ManifestFromRelease(ctx, obj.GetName(), obj.GetName(), m.mgr)
	if err != nil {
		logger.Error(err, "get manifest failed")
		return err
	}

	if actual.Manifest != expected.Manifest {
		logger.Info("manifest is not equal, need to calibrating")
		obj.Status.Phase = ccrv1alpha1.CCRCalibrating
	}

	return nil
}

func (m *CCRMonitor) monitorPhase(ctx context.Context, obj *ccrv1alpha1.CCR, healthFunc func(c context.Context, obj *ccrv1alpha1.CCR) error) error {
	logger := log.FromContext(ctx)

	if m.check(ctx, obj) && healthFunc != nil {
		if err := healthFunc(ctx, obj); err != nil {
			logger.V(2).Error(err, "execute health check failed")
			return err
		}
	}

	err := m.client.Status().Update(ctx, obj)
	if err != nil {
		logger.V(6).Error(err, fmt.Sprintf("update status of object %s error", obj.GetName()))
		return err
	}

	return nil
}

func (m *CCRMonitor) check(ctx context.Context, obj *ccrv1alpha1.CCR) bool {
	//logger := log.FromContext(ctx)
	actualHealth, expectedHealth := 0, 3

	if obj.Status.Phase == ccrv1alpha1.CCRStopped {
		return true
	}

	err := m.harborCheck(ctx, obj)
	if err != nil {
		//logger.V(6).Error(err, "harbor check failed")
		m.updateCondition(ctx, obj, &ccrv1alpha1.ComponentCondition{
			Name:    "harbor",
			Health:  ccrv1alpha1.ComponentRed,
			Reason:  ccrv1alpha1.ReasonHarborNotReady,
			Message: err.Error(),
		})
	} else {
		actualHealth++
		m.updateCondition(ctx, obj, &ccrv1alpha1.ComponentCondition{
			Name:    "harbor",
			Health:  ccrv1alpha1.ComponentGreen,
			Reason:  "",
			Message: "",
		})
	}

	err = m.ingressControllerCheck(ctx, obj)
	if err != nil {
		//logger.V(6).Error(err, "ingress check failed")
		m.updateCondition(ctx, obj, &ccrv1alpha1.ComponentCondition{
			Name:    "ingressController",
			Health:  ccrv1alpha1.ComponentRed,
			Reason:  ccrv1alpha1.ReasonIngressControllerNotReady,
			Message: err.Error(),
		})
	} else {
		actualHealth++
		m.updateCondition(ctx, obj, &ccrv1alpha1.ComponentCondition{
			Name:    "ingressController",
			Health:  ccrv1alpha1.ComponentGreen,
			Reason:  "",
			Message: "",
		})
	}

	err = m.databaseCheck(ctx, obj)
	if err != nil {
		//logger.V(6).Error(err, "database check failed")
		m.updateCondition(ctx, obj, &ccrv1alpha1.ComponentCondition{
			Name:    "database",
			Health:  ccrv1alpha1.ComponentRed,
			Reason:  ccrv1alpha1.ReasonDatabaseNotReady,
			Message: err.Error(),
		})
	} else {
		actualHealth++
		m.updateCondition(ctx, obj, &ccrv1alpha1.ComponentCondition{
			Name:    "database",
			Health:  ccrv1alpha1.ComponentGreen,
			Reason:  "",
			Message: "",
		})
	}

	return actualHealth == expectedHealth
}

func (m *CCRMonitor) harborCheck(ctx context.Context, object *ccrv1alpha1.CCR) error {
	harborSvc := fmt.Sprintf("%s-harbor-core", object.GetName())

	// charts 需要
	harborSvc = strings.TrimSuffix(utils.Truncate(harborSvc, 63), "-")
	err := httpCheck(fmt.Sprintf("%s.%s:80/api/v2.0/ping", harborSvc, object.GetName()), 200)
	if err != nil {
		return err
	}

	jobSvc := fmt.Sprintf("%s-harbor-jobservice", object.GetName())
	jobSvc = strings.TrimSuffix(utils.Truncate(jobSvc, 63), "-")
	err = httpCheck(fmt.Sprintf("%s.%s:80/api/v1/stats", jobSvc, object.GetName()), 200)
	if err != nil {
		return err
	}

	/*
		chartMuseumSvc := fmt.Sprintf("%s-harbor-chartmuseum", object.GetName())
		chartMuseumSvc = strings.TrimSuffix(utils.Truncate(chartMuseumSvc, 63), "-")
		err = httpCheck(fmt.Sprintf("%s.%s:80/health", chartMuseumSvc, object.GetName()), 200)
		if err != nil {
			return err
		}
	*/

	registrySvc := fmt.Sprintf("%s-harbor-registry", object.GetName())
	registrySvc = strings.TrimSuffix(utils.Truncate(registrySvc, 63), "-")
	err = httpCheck(fmt.Sprintf("%s.%s:8080/api/health", registrySvc, object.GetName()), 200)
	if err != nil {
		return err
	}

	trivySvc := fmt.Sprintf("%s-harbor-trivy", object.GetName())
	trivySvc = strings.TrimSuffix(utils.Truncate(trivySvc, 63), "-")
	return httpCheck(fmt.Sprintf("%s.%s:8080/probe/ready", trivySvc, object.GetName()), 200)
}

func (m *CCRMonitor) databaseCheck(ctx context.Context, object *ccrv1alpha1.CCR) error {
	return nil
}

func (m *CCRMonitor) ingressControllerCheck(ctx context.Context, object *ccrv1alpha1.CCR) error {
	controllerMetrics := fmt.Sprintf("%s-ingress-controller-metrics.%s:10254/healthz", object.GetName(), object.GetName())
	return httpCheck(controllerMetrics, 200)
}

func httpCheck(uri string, expectedCode int) error {
	cli := http.Client{
		Transport: http.DefaultTransport,
		Timeout:   time.Second * 2,
	}

	if !strings.HasPrefix(uri, "http") {
		uri = "http://" + uri
	}

	resp, err := cli.Get(uri)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if expectedCode != resp.StatusCode {
		return fmt.Errorf("url %s proble failed, expect %d, actually %d", uri, expectedCode, resp.StatusCode)
	}

	return nil
}

func (m *CCRMonitor) updateCondition(ctx context.Context, object *ccrv1alpha1.CCR, condition *ccrv1alpha1.ComponentCondition) {
	if object.Status.Conditions == nil {
		object.Status.Conditions = make([]*ccrv1alpha1.ComponentCondition, 0)
	}
	current := metav1.Now()

	var lastCondition *ccrv1alpha1.ComponentCondition
	for _, v := range object.Status.Conditions {
		if v.Name == condition.Name {
			lastCondition = v
			break
		}
	}

	if lastCondition == nil {
		condition.LastProbeTime = &current
		condition.LastTransitionTime = &current
		object.Status.Conditions = append(object.Status.Conditions, condition)
	} else {
		lastCondition.LastProbeTime = &current
		if lastCondition.Health != condition.Health || condition.Health != ccrv1alpha1.ComponentGreen {
			lastCondition.LastTransitionTime = &current
			lastCondition.Message = condition.Message
			lastCondition.Reason = condition.Reason
			lastCondition.Health = condition.Health
		}
	}
}

func (m *CCRMonitor) postDeleteLibraryProject(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)
	// get password
	secretName := fmt.Sprintf("%s-harbor-core", object.GetName())
	var coreSecret corev1.Secret
	err := m.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: secretName}, &coreSecret)
	if err != nil {
		logger.V(2).Error(err, "get core secret failed")
		return err
	}

	coreUrl := fmt.Sprintf("http://%s-harbor-core.%s/api/v2.0/projects/1", object.GetName(), object.GetName())
	req, err := http.NewRequest(http.MethodDelete, coreUrl, nil)
	if err != nil {
		logger.V(2).Error(err, "build delete project url failed")
		return err
	}
	req.SetBasicAuth("admin", string(coreSecret.Data["HARBOR_ADMIN_PASSWORD"]))

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		logger.V(2).Error(err, "delete project library failed")
		return err
	}
	defer func() {
		resp.Body.Close()
	}()

	if resp.StatusCode != http.StatusOK {
		logger.V(2).Error(fmt.Errorf("delete project get %v, expected 200", resp.StatusCode), "delete project failed")
		return fmt.Errorf("delete project get %v, expected 200", resp.StatusCode)
	}

	return nil
}
