---
BASIC:
  chartMuseum:
    replicas: "2"
    container:
      chartMuseum:
        registry: registry.baidubce.com
        repository: ccr-image/chartmuseum-photon
        tag: v2.3.5
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  core:
    replicas: "2"
    container:
      core:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-core
        tag: 1.0.137.1
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  harborAddon:
    replicas: "2"
    container:
      harborAddon:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-addon
        tag: 1.0.137.1
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  harborExporter:
    replicas: "1"
    container:
      harborExporter:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-exporter
        tag: v2.3.5
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 100Mi
  ingressCtl:
    replicas: "3"
    container:
      ingressCtl:
        registry: registry.baidubce.com
        repository: ccr-image/ingress-nginx
        tag: v0.49.0
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  jobService:
    replicas: "2"
    container:
      jobService:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-jobservice
        tag: 1.0.137.1
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  redis:
    replicas: "3"
    container:
      redis:
        registry: registry.baidubce.com
        repository: ccr-image/redis
        tag: 6.2.4-debian-10-r14
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 500m
          memory: 1Gi
      exporter:
        registry: registry.baidubce.com
        repository: ccr-image/redis-exporter
        tag: 1.24.0-debian-10-r9
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 100Mi
      sentinel:
        registry: registry.baidubce.com
        repository: ccr-image/redis-sentinel
        tag: 6.2.4-debian-10-r14
        requests:
          cpu: 50m
          memory: 100Mi
        limits:
          cpu: 100m
          memory: 100Mi
      sentinelExporter:
        registry: registry.baidubce.com
        repository: ccr-image/redis-sentinel-exporter
        tag: 1.7.1-debian-10-r161
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 100Mi
  registry:
    replicas: "2"
    container:
      registry:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-registry
        tag: 7ce2909-202305091149
        requests:
          cpu: 250m
          memory: 500Mi
        limits:
          cpu: '1'
          memory: 2Gi
      registryCtl:
        registry: registry.baidubce.com
        repository: ccr-image/ccr-registryctl
        tag: 1.0.58.1
        requests:
          cpu: 50m
          memory: 125Mi
        limits:
          cpu: 100m
          memory: 250Mi
  trivy:
    replicas: "2"
    container:
      trivy:
        registry: registry.baidubce.com
        repository: ccr-image/trivy-onfly
        tag: v1-2021122312
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  portal:
    replicas: "1"
    container:
      portal:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-portal
        tag: v2.3.5
        requests:
          cpu: 100m
          memory: 100Mi
        limits:
          cpu: 200m
          memory: 200Mi
  scheduler:
    replicas: "0"
    container:
      scheduler:
        registry: registry.baidubce.com
        repository: ccr-image/dragonfly-scheduler
        tag: v2.1.0-0.2
        requests:
          cpu: "2"
          memory: "4Gi"
        limits:
          cpu: "4"
          memory: "8Gi"
STANDARD:
  chartMuseum:
    replicas: "2"
    container:
      chartMuseum:
        registry: registry.baidubce.com
        repository: ccr-image/chartmuseum-photon
        tag: v2.3.5
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  core:
    replicas: "2"
    container:
      core:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-core
        tag: 1.0.137.1
        requests:
          cpu: 250m
          memory: 500Mi
        limits:
          cpu: 500m
          memory: 1Gi
  harborAddon:
    replicas: "2"
    container:
      harborAddon:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-addon
        tag: 1.0.137.1
        requests:
          cpu: 250m
          memory: 500Mi
        limits:
          cpu: 500m
          memory: 1Gi
  harborExporter:
    replicas: "1"
    container:
      harborExporter:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-exporter
        tag: v2.3.5
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 100Mi
  ingressCtl:
    replicas: "3"
    container:
      ingressCtl:
        registry: registry.baidubce.com
        repository: ccr-image/ingress-nginx
        tag: v0.49.0
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  jobService:
    replicas: "2"
    container:
      jobService:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-jobservice
        tag: 1.0.137.1
        requests:
          cpu: 250m
          memory: 500Mi
        limits:
          cpu: 500m
          memory: 1Gi
  redis:
    replicas: "3"
    container:
      redis:
        registry: registry.baidubce.com
        repository: ccr-image/redis
        tag: 6.2.4-debian-10-r14
        requests:
          cpu: 250m
          memory: 500Mi
        limits:
          cpu: 750m
          memory: 1.5Gi
      exporter:
        registry: registry.baidubce.com
        repository: ccr-image/redis-exporter
        tag: 1.24.0-debian-10-r9
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 100Mi
      sentinel:
        registry: registry.baidubce.com
        repository: ccr-image/redis-sentinel
        tag: 6.2.4-debian-10-r14
        requests:
          cpu: 50m
          memory: 100Mi
        limits:
          cpu: 100m
          memory: 100Mi
      sentinelExporter:
        registry: registry.baidubce.com
        repository: ccr-image/redis-sentinel-exporter
        tag: 1.7.1-debian-10-r161
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 100Mi
  registry:
    replicas: "2"
    container:
      registry:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-registry
        tag: 7ce2909-202305091149
        requests:
          cpu: 500m
          memory: 1Gi
        limits:
          cpu: '1'
          memory: 2Gi
      registryCtl:
        registry: registry.baidubce.com
        repository: ccr-image/ccr-registryctl
        tag: 1.0.58.1
        requests:
          cpu: 50m
          memory: 125Mi
        limits:
          cpu: 100m
          memory: 250Mi
  trivy:
    replicas: "2"
    container:
      trivy:
        registry: registry.baidubce.com
        repository: ccr-image/trivy-onfly
        tag: v1-2021122312
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  portal:
    replicas: "1"
    container:
      portal:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-portal
        tag: v2.3.5
        requests:
          cpu: 100m
          memory: 100Mi
        limits:
          cpu: 200m
          memory: 200Mi
  scheduler:
    replicas: "0"
    container:
      scheduler:
        registry: registry.baidubce.com
        repository: ccr-image/dragonfly-scheduler
        tag: v2.1.0-0.2
        requests:
          cpu: "2"
          memory: "4Gi"
        limits:
          cpu: "4"
          memory: "8Gi"
ADVANCED:
  chartMuseum:
    replicas: "3"
    container:
      chartMuseum:
        registry: registry.baidubce.com
        repository: ccr-image/chartmuseum-photon
        tag: v2.3.5
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  core:
    replicas: "2"
    container:
      core:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-core
        tag: 1.0.137.1
        requests:
          cpu: 500m
          memory: 1Gi
        limits:
          cpu: '1'
          memory: 2Gi
  harborAddon:
    replicas: "2"
    container:
      harborAddon:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-addon
        tag: 1.0.137.1
        requests:
          cpu: 500m
          memory: 1Gi
        limits:
          cpu: '1'
          memory: 2Gi
  harborExporter:
    replicas: "1"
    container:
      harborExporter:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-exporter
        tag: v2.3.5
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 100Mi
  ingressCtl:
    replicas: "3"
    container:
      ingressCtl:
        registry: registry.baidubce.com
        repository: ccr-image/ingress-nginx
        tag: v0.49.0
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  jobService:
    replicas: "2"
    container:
      jobService:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-jobservice
        tag: 1.0.137.1
        requests:
          cpu: 500m
          memory: 1Gi
        limits:
          cpu: '1'
          memory: 2Gi
  redis:
    replicas: "3"
    container:
      redis:
        registry: registry.baidubce.com
        repository: ccr-image/redis
        tag: 6.2.4-debian-10-r14
        requests:
          cpu: 500m
          memory: 1Gi
        limits:
          cpu: '1'
          memory: 2Gi
      exporter:
        registry: registry.baidubce.com
        repository: ccr-image/redis-exporter
        tag: 1.24.0-debian-10-r9
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 100Mi
      sentinel:
        registry: registry.baidubce.com
        repository: ccr-image/redis-sentinel
        tag: 6.2.4-debian-10-r14
        requests:
          cpu: 50m
          memory: 100Mi
        limits:
          cpu: 100m
          memory: 100Mi
      sentinelExporter:
        registry: registry.baidubce.com
        repository: ccr-image/redis-sentinel-exporter
        tag: 1.7.1-debian-10-r161
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 100Mi
  registry:
    replicas: "2"
    container:
      registry:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-registry
        tag: 7ce2909-202305091149
        requests:
          cpu: '1'
          memory: 2Gi
        limits:
          cpu: '2'
          memory: 4Gi
      registryCtl:
        registry: registry.baidubce.com
        repository: ccr-image/ccr-registryctl
        tag: 1.0.58.1
        requests:
          cpu: 50m
          memory: 125Mi
        limits:
          cpu: 100m
          memory: 250Mi
  trivy:
    replicas: "2"
    container:
      trivy:
        registry: registry.baidubce.com
        repository: ccr-image/trivy-onfly
        tag: v1-2021122312
        requests:
          cpu: 125m
          memory: 250Mi
        limits:
          cpu: 250m
          memory: 500Mi
  portal:
    replicas: "1"
    container:
      portal:
        registry: registry.baidubce.com
        repository: ccr-image/harbor-portal
        tag: v2.3.5
        requests:
          cpu: 100m
          memory: 100Mi
        limits:
          cpu: 200m
          memory: 200Mi
  scheduler:
    replicas: "3"
    container:
      scheduler:
        registry: registry.baidubce.com
        repository: ccr-image/dragonfly-scheduler
        tag: v2.1.0-0.2
        requests:
          cpu: "2"
          memory: "4Gi"
        limits:
          cpu: "4"
          memory: "8Gi"