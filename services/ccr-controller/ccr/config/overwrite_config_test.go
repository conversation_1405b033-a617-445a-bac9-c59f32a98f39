package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

/*
harbor:
  core:
    secret: {{ .CoreSecret }}
    xsrfKey: {{ .CsrfKey }}
  jobservice:
    secret: {{ .JobserviceSecret }}
  registry:
    secret: {{ .RegistrySecret }}
  harborAdminPassword: {{ .AdminPassword }}
ccr:
  jwtTlsCrt: {{ .JwtTlsCrt }}
  jwtTlsKey: {{ .JwtTlsKey }}
*/

func Test_GenerateOverwriteConfig(t *testing.T) {
	oc := OverwriteConfig{
		CoreSecret:       "coresecret",
		CsrfKey:          "csrfkey",
		JobserviceSecret: "jobservicesecret",
		RegistrySecret:   "registrysecret",
		AdminPassword:    "adminpassword",
		JwtTlsCrt:        "jwttlscrt",
		JwtTlsKey:        "jwttlskey",
	}

	m, err := GenerateOverwriteConfig(&oc)

	assert.Nil(t, err)
	assert.NotNil(t, m)
	assert.Equal(t, map[string]interface{}{
		"harbor": map[string]interface{}{
			"core": map[string]interface{}{
				"secret":  "coresecret",
				"xsrfKey": "csrfkey",
			},
			"jobservice": map[string]interface{}{
				"secret": "jobservicesecret",
			},
			"registry": map[string]interface{}{
				"secret": "registrysecret",
			},
			"harborAdminPassword": "adminpassword",
		},
		"ccr": map[string]interface{}{
			"jwtTlsCrt": "jwttlscrt",
			"jwtTlsKey": "jwttlskey",
		},
	}, m)
}
