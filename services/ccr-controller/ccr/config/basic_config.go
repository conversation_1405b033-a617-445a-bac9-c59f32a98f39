package config

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"text/template"

	jsonpatch "github.com/evanphx/json-patch"
	"github.com/fatih/structs"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/util/yaml"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

var (
	defaultBasicComponentContainerSpec = map[Component]PodSpec{
		ComponentCore: {
			Replicas: CoreReplicas,
			Container: map[Container]ContainerSpec{
				ContainerCore: {
					Registry:   RegistryDefault,
					Repository: CoreRepositoryDefault,
					Tag:        CoreTagDefault,
					Requests: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
					Limits: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
				},
			},
		},
		ComponentJobService: {
			Replicas: JobServiceReplicas,
			Container: map[Container]ContainerSpec{
				ContainerJobService: {
					Registry:   RegistryDefault,
					Repository: JobServiceRepositoryDefault,
					Tag:        JobServiceTagDefault,
					Requests: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
					Limits: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
				},
			},
		},
		ComponentRegistry: {
			Replicas: RegistryReplicas,
			Container: map[Container]ContainerSpec{
				ContainerRegistry: {
					Registry:   RegistryDefault,
					Repository: RegistryRepositoryDefault,
					Tag:        RegistryTagDefault,
					Requests: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
					Limits: Resource{
						Memory: "4Gi",
						CPU:    "2",
					},
				},
				ContainerRegistryCtl: {
					Registry:   RegistryDefault,
					Repository: RegistryCtlRepositoryDefault,
					Tag:        RegistryCtlTagDefault,
					Requests: Resource{
						Memory: "125Mi",
						CPU:    "50m",
					},
					Limits: Resource{
						Memory: "250Mi",
						CPU:    "100m",
					},
				},
			},
		},
		ComponentChartMuseum: {
			Replicas: ChartMuseumReplicas,
			Container: map[Container]ContainerSpec{
				ContainerChartMuseum: {
					Registry:   RegistryDefault,
					Repository: ChartMuseumRepositoryDefault,
					Tag:        ChartMuseumTagDefault,
					Requests: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
					Limits: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
				},
			},
		},
		ComponentTrivy: {
			Replicas: TrivyReplicas,
			Container: map[Container]ContainerSpec{
				ContainerTrivy: {
					Registry:   RegistryDefault,
					Repository: TrivyRepositoryDefault,
					Tag:        TrivyTagDefault,
					Requests: Resource{
						Memory: "250Mi",
						CPU:    "125m",
					},
					Limits: Resource{
						Memory: "500Mi",
						CPU:    "250m",
					},
				},
			},
		},
		ComponentHarborExporter: {
			Replicas: HarborExporterReplicas,
			Container: map[Container]ContainerSpec{
				ContainerHarborExporter: {
					Registry:   RegistryDefault,
					Repository: HarborExporterRepositoryDefault,
					Tag:        HarborExporterTagDefault,
					Requests: Resource{
						Memory: "512Mi",
						CPU:    "300m",
					},
					Limits: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
				},
			},
		},
		ComponentPortal: {
			Replicas: PortalReplicas,
			Container: map[Container]ContainerSpec{
				ContainerPortal: {
					Registry:   RegistryDefault,
					Repository: PortalRepositoryDefault,
					Tag:        PortalTagDefault,
					Requests: Resource{
						Memory: "100Mi",
						CPU:    "100m",
					},
					Limits: Resource{
						Memory: "200Mi",
						CPU:    "200m",
					},
				},
			},
		},
		ComponentIngressCtl: {
			Replicas: IngressCtlReplicas,
			Container: map[Container]ContainerSpec{
				ContainerIngressCtl: {
					Registry:   RegistryDefault,
					Repository: IngressCtlRepositoryDefault,
					Tag:        IngressCtlTagDefault,
					Requests: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
					Limits: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
				},
			},
		},
		ComponentRedis: {
			Replicas: RedisReplicas,
			Container: map[Container]ContainerSpec{
				ContainerRedis: {
					Registry:   RegistryDefault,
					Repository: RedisRepositoryDefault,
					Tag:        RedisTagDefault,
					Requests: Resource{
						Memory: "1Gi",
						CPU:    "500m",
					},
					Limits: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
				},
				ContainerRedisSentinel: {
					Registry:   RegistryDefault,
					Repository: RedisSentinelRepositoryDefault,
					Tag:        RedisSentinelTagDefault,
					Requests: Resource{
						Memory: "50Mi",
						CPU:    "50m",
					},
					Limits: Resource{
						Memory: "100Mi",
						CPU:    "100m",
					},
				},
				ContainerRedisExporter: {
					Registry:   RegistryDefault,
					Repository: RedisExporterRepositoryDefault,
					Tag:        RedisExporterTagDefault,
					Requests: Resource{
						Memory: "50Mi",
						CPU:    "50m",
					},
					Limits: Resource{
						Memory: "100Mi",
						CPU:    "100m",
					},
				},
				ContainerRedisSentinelExporter: {
					Registry:   RegistryDefault,
					Repository: RedisSentinelExporterRepositoryDefault,
					Tag:        RedisSentinelExporterTagDefault,
					Requests: Resource{
						Memory: "50Mi",
						CPU:    "50m",
					},
					Limits: Resource{
						Memory: "100Mi",
						CPU:    "100m",
					},
				},
			},
		},
		ComponentHarborAddon: {
			Replicas: HarborAddonReplicas,
			Container: map[Container]ContainerSpec{
				ContainerHarborAddon: {
					Registry:   RegistryDefault,
					Repository: HarborAddonRepositoryDefault,
					Tag:        HarborAddonTagDefault,
					Requests: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
					Limits: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
				},
			},
		},
		ComponentScheduler: {
			Replicas: SchedulerReplicas,
			Container: map[Container]ContainerSpec{
				ContainerScheduler: {
					Registry:   RegistryDefault,
					Repository: SchedulerRepositoryDefault,
					Tag:        SchedulerTagDefault,
					Requests: Resource{
						Memory: "2Gi",
						CPU:    "1",
					},
					Limits: Resource{
						Memory: "4Gi",
						CPU:    "2",
					},
				},
			},
		},
	}
	basicValue string = `
harbor:
  expose:
    ingress:
      hosts:
        core: '{{ .clusterDomain }}'
  externalURL: '{{ .authURL }}'
  harborAdminPassword: '{{ .adminPassword }}'
  persistence:
    imageChartStorage:
      bos:
        region: '{{ .region }}'
        bucket: '{{ .bos.bucket }}'
        accesskeyid: '{{ .bos.ak }}'
        secretaccesskey: '{{ .bos.sk }}'
        endpoint: '{{ .bos.endpoint }}'
  core:
    replicas: {{ .core.replicas }}
    image:
      repository: '{{ .core.container.core.registry }}/{{ .core.container.core.repository }}'
      tag: '{{ .core.container.core.tag }}'
    resources:
      requests:
        memory: '{{ .core.container.core.requests.memory }}'
        cpu: '{{ .core.container.core.requests.cpu }}'
      limits:
        memory: '{{ .core.container.core.limits.memory }}'
        cpu: '{{ .core.container.core.limits.cpu }}'
  portal:
    replicas: {{ .portal.replicas }}
    image:
      repository: '{{ .portal.container.portal.registry }}/{{ .portal.container.portal.repository }}'
      tag: '{{ .portal.container.portal.tag }}'
    resources:
      requests:
        memory: '{{ .portal.container.portal.requests.memory }}'
        cpu: '{{ .portal.container.portal.requests.cpu }}'
      limits:
        memory: '{{ .portal.container.portal.limits.memory }}'
        cpu: '{{ .portal.container.portal.limits.cpu }}'
  jobservice:
    replicas: {{ .jobService.replicas }}
    image:
      repository: '{{ .jobService.container.jobService.registry }}/{{ .jobService.container.jobService.repository }}'
      tag: '{{ .jobService.container.jobService.tag }}'
    resources:
      requests:
        memory: '{{ .jobService.container.jobService.requests.memory }}'
        cpu: '{{ .jobService.container.jobService.requests.cpu }}'
      limits:
        memory: '{{ .jobService.container.jobService.limits.memory }}'
        cpu: '{{ .jobService.container.jobService.limits.cpu }}'
  registry:
    replicas: {{ .registry.replicas }}
    registry:
      image:
        repository: '{{ .registry.container.registry.registry }}/{{ .registry.container.registry.repository }}'
        tag: '{{ .registry.container.registry.tag }}'
      resources:
        requests:
          memory: '{{ .registry.container.registry.requests.memory }}'
          cpu: '{{ .registry.container.registry.requests.cpu }}'
        limits:
          memory: '{{ .registry.container.registry.limits.memory }}'
          cpu: '{{ .registry.container.registry.limits.cpu }}'
    controller:
      image:
        repository: '{{ .registry.container.registryCtl.registry }}/{{ .registry.container.registryCtl.repository }}'
        tag: '{{ .registry.container.registryCtl.tag }}'
      resources:
        requests:
          memory: '{{ .registry.container.registryCtl.requests.memory }}'
          cpu: '{{ .registry.container.registryCtl.requests.cpu }}'
        limits:
          memory: '{{ .registry.container.registryCtl.limits.memory }}'
          cpu: '{{ .registry.container.registryCtl.limits.cpu }}'
  chartmuseum:
    replicas: {{ .chartMuseum.replicas }}
    image:
      repository: {{ .chartMuseum.container.chartMuseum.registry }}/{{ .chartMuseum.container.chartMuseum.repository }}
      tag: {{ .chartMuseum.container.chartMuseum.tag }}
    resources:
      requests:
        memory: '{{ .chartMuseum.container.chartMuseum.requests.memory }}'
        cpu: '{{ .chartMuseum.container.chartMuseum.requests.cpu }}'
      limits:
        memory: '{{ .chartMuseum.container.chartMuseum.limits.memory }}'
        cpu: '{{ .chartMuseum.container.chartMuseum.limits.cpu }}'
  trivy:
    replicas: {{ .trivy.replicas }}
    image:
      repository: {{ .trivy.container.trivy.registry }}/{{ .trivy.container.trivy.repository }}
      tag: {{ .trivy.container.trivy.tag }}
    resources:
      requests:
        memory: '{{ .trivy.container.trivy.requests.memory }}'
        cpu: '{{ .trivy.container.trivy.requests.cpu }}'
      limits:
        memory: '{{ .trivy.container.trivy.limits.memory }}'
        cpu: '{{ .trivy.container.trivy.limits.cpu }}'
  database:
    external:
      host: {{ .database.host }}
      port: {{ .database.port }}
      username: {{ .database.username }}
      password: {{ .database.password }}
  exporter:
    replicas: {{ .harborExporter.replicas }}
    image:
      repository: {{ .harborExporter.container.harborExporter.registry }}/{{ .harborExporter.container.harborExporter.repository }}
      tag: {{ .harborExporter.container.harborExporter.tag }}
    resources:
      requests:
        memory: '{{ .harborExporter.container.harborExporter.requests.memory }}'
        cpu: '{{ .harborExporter.container.harborExporter.requests.cpu }}'
      limits:
        memory: '{{ .harborExporter.container.harborExporter.limits.memory }}'
        cpu: '{{ .harborExporter.container.harborExporter.limits.cpu }}'
ingress-nginx:
  fullnameOverride: {{ .instanceName }}-ingress
  controller:
    replicaCount: {{ .ingressCtl.replicas }}
    image:
      registry: '{{ .ingressCtl.container.ingressCtl.registry }}'
      image: '{{ .ingressCtl.container.ingressCtl.repository }}'
      tag: '{{ .ingressCtl.container.ingressCtl.tag }}'
    ingressClass: '{{ .namespace }}'
    resources:
      requests:
        cpu: '{{ .ingressCtl.container.ingressCtl.requests.cpu }}'
        memory: '{{ .ingressCtl.container.ingressCtl.requests.memory }}'
      limits:
        cpu: '{{ .ingressCtl.container.ingressCtl.limits.cpu }}'
        memory: '{{ .ingressCtl.container.ingressCtl.limits.memory }}'
    service:
      annotations:
        service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
        service.beta.kubernetes.io/cce-load-balancer-lb-name: {{ .instanceName }}
redis:
  commonConfiguration: |-
    appendonly yes
    save ""
    maxmemory {{ resourcePercent .redis.container.redis.limits.memory 90 }}
    maxmemory-policy allkeys-lru
  image:
    registry: {{ .redis.container.redis.registry }}
    repository: {{ .redis.container.redis.repository }}
    tag: '{{ .redis.container.redis.tag }}'
  replica:
    replicaCount: {{ .redis.replicas }}
    resources:
      requests:
        memory: '{{ .redis.container.redis.requests.memory }}'
        cpu: '{{ .redis.container.redis.requests.cpu }}'
      limits:
        memory: '{{ .redis.container.redis.limits.memory }}'
        cpu: '{{ .redis.container.redis.limits.cpu }}'
  sentinel:
    image:
      registry: {{ .redis.container.sentinel.registry }}
      repository: {{ .redis.container.sentinel.repository }}
      tag: {{ .redis.container.sentinel.tag }}
    resources:
      requests:
        memory: '{{ .redis.container.sentinel.requests.memory }}'
        cpu: '{{ .redis.container.sentinel.requests.cpu }}'
      limits:
        memory: '{{ .redis.container.sentinel.limits.memory }}'
        cpu: '{{ .redis.container.sentinel.limits.cpu }}'
  metrics:
    image:
      registry: {{ .redis.container.exporter.registry }}
      repository: {{ .redis.container.exporter.repository }}
      tag: '{{ .redis.container.exporter.tag }}'
    resources:
      requests:
        memory: '{{ .redis.container.exporter.requests.memory }}'
        cpu: '{{ .redis.container.exporter.requests.cpu }}'
      limits:
        memory: '{{ .redis.container.exporter.limits.memory }}'
        cpu: '{{ .redis.container.exporter.limits.cpu }}'
    sentinel:
      image:
        registry: {{ .redis.container.sentinelExporter.registry }}
        repository: {{ .redis.container.sentinelExporter.repository }}
        tag: '{{ .redis.container.sentinelExporter.tag }}'
      resources:
        requests:
          memory: '{{ .redis.container.sentinelExporter.requests.memory }}'
          cpu: '{{ .redis.container.sentinelExporter.requests.cpu }}'
        limits:
          memory: '{{ .redis.container.sentinelExporter.limits.memory }}'
          cpu: '{{ .redis.container.sentinelExporter.limits.cpu }}'
ccr:
  jwtTlsCrt: "{{.jwtTlsCrt}}"
  jwtTlsKey: "{{.jwtTlsKey}}"
  region: "{{ .region }}"
  domainCert:
    key: {{ .domainKey }}
    cert: {{ .domainCert }}
  privateDomain:
    name: {{ .privateDomain }}
  publicDomain:
    name: {{ .publicDomain }}
  authDomain:
    name: {{ .authURL }}
  harborAddon:
    replicas: {{ .harborAddon.replicas }}
    image:
      repository: {{ .harborAddon.container.harborAddon.registry }}/{{ .harborAddon.container.harborAddon.repository }}
      tag: '{{ .harborAddon.container.harborAddon.tag }}'
    resources:
      limits:
        memory: '{{ .harborAddon.container.harborAddon.limits.memory }}'
        cpu: '{{ .harborAddon.container.harborAddon.limits.cpu }}'
      requests:
        memory: '{{ .harborAddon.container.harborAddon.requests.memory }}'
        cpu: '{{ .harborAddon.container.harborAddon.requests.cpu }}'
dragonfly:
  scheduler:
    image: {{ .scheduler.container.scheduler.registry }}/{{ .scheduler.container.scheduler.repository }}
    tag: '{{ .scheduler.container.scheduler.tag }}'
    replicas: {{ .scheduler.replicas }}
    config:
      server:
        advertiseHostSuffix: {{ .dragonflyAdvertiseHostSuffix }}
      host:
        idc: '{{ .namespace }}'
    resources:
      requests:
        cpu: '{{ .scheduler.container.scheduler.requests.cpu }}'
        memory: '{{ .scheduler.container.scheduler.requests.memory }}'
      limits:
        cpu: '{{ .scheduler.container.scheduler.limits.cpu }}'
        memory: '{{ .scheduler.container.scheduler.limits.memory }}'
`
)

var valueTemplateEngine = newValueTemplateOrPanic("ccr-value", basicValue)

type valueTemplate struct {
	valueTpl *template.Template
}

func newValueTemplateOrPanic(name, t string) valueTemplate {
	tpl, err := template.New(name).Funcs(template.FuncMap{
		"resourcePercent": ResourceLimitPercent,
	}).Parse(t)
	if err != nil {
		panic(err)
	}

	return valueTemplate{
		valueTpl: tpl,
	}
}

func fillBasicComponentSpec(spec CCRTypePodSpec, t v1alpha1.CCRType) {
	if _, ok := spec[t]; !ok {
		spec[t] = make(ComponentPodSpec)
	}

	ccs := spec[t]
	for k, v := range defaultBasicComponentContainerSpec {
		if _, ok := ccs[k]; !ok {
			ccs[k] = v
		}
	}
}

type BosConfig struct {
	Ak       string `structs:"ak,omitempty"`
	Sk       string `structs:"sk,omitempty"`
	Bucket   string `structs:"bucket,omitempty"`
	Endpoint string `structs:"endpoint,omitempty"`
}

type DatabaseConfig struct {
	Host     string `structs:"host,omitempty"`
	Port     string `structs:"port,omitempty"`
	Username string `structs:"username,omitempty"`
	Password string `structs:"password,omitempty"`
}

type BasicConfig struct {
	ClusterDomain                string          `structs:"clusterDomain,omitempty"`
	PrivateDomain                string          `structs:"privateDomain,omitempty"`
	PublicDomain                 string          `structs:"publicDomain,omitempty"`
	AuthURL                      string          `structs:"authURL,omitempty"`
	AdminPassword                string          `structs:"adminPassword,omitempty"`
	Region                       string          `structs:"region,omitempty"`
	Namespace                    string          `structs:"namespace,omitempty"`
	Bos                          *BosConfig      `structs:"bos,omitempty"`
	Database                     *DatabaseConfig `structs:"database,omitempty"`
	InstanceName                 string          `structs:"instanceName,omitempty"`
	JwtTLSCrt                    string          `structs:"jwtTlsCrt"`
	JwtTlsKey                    string          `structs:"jwtTlsKey"`
	DomainKey                    string          `structs:"domainKey"`
	DomainCert                   string          `structs:"domainCert"`
	DragonflyAdvertiseHostSuffix string          `structs:"dragonflyAdvertiseHostSuffix"`
}

type InstanceConfig struct {
	Namespace     string
	InstanceName  string
	AdminPassword string
	JwtTlsCrt     string
	JwtTlsKey     string
}

func NewInstanceConfig(namespace, instanceName, adminPassword, jwtTlsKey, jwtTlsCrt string) *InstanceConfig {
	if adminPassword == "" {
		adminPassword = utils.SimpleRandomPassword(16)
	}

	return &InstanceConfig{
		Namespace:     namespace,
		InstanceName:  instanceName,
		AdminPassword: adminPassword,
		JwtTlsCrt:     base64.StdEncoding.EncodeToString([]byte(jwtTlsCrt)),
		JwtTlsKey:     base64.StdEncoding.EncodeToString([]byte(jwtTlsKey)),
	}
}

func newBasicConfig(globalConfig *Config, bosConfig *BosConfig, dbConfig *DatabaseConfig, ins *InstanceConfig) *BasicConfig {
	return &BasicConfig{
		ClusterDomain:                ins.InstanceName + "-" + globalConfig.ClusterDomainSuffix,
		PrivateDomain:                ins.InstanceName + "-" + globalConfig.PrivateDomainSuffix,
		PublicDomain:                 ins.InstanceName + "-" + globalConfig.PublicDomainSuffix,
		AuthURL:                      globalConfig.AuthDomain,
		AdminPassword:                ins.AdminPassword,
		Region:                       globalConfig.Region,
		Namespace:                    ins.Namespace,
		Bos:                          bosConfig,
		Database:                     dbConfig,
		InstanceName:                 ins.InstanceName,
		JwtTLSCrt:                    ins.JwtTlsCrt,
		JwtTlsKey:                    ins.JwtTlsKey,
		DomainKey:                    globalConfig.DomainKey,
		DomainCert:                   globalConfig.DomainCert,
		DragonflyAdvertiseHostSuffix: globalConfig.PrivateDomainSuffix,
	}
}

// GenerateBasicValue 方法生成一个基本配置值
// globalConfig：全局配置参数指针
// componentConfig：组件 PodSpec 参数
// bosConfig：Bos 配置参数指针
// dbConfig：数据库配置参数指针
// ins：实例配置参数指针
// overwrite：覆盖参数字典
// zeroReplicas：是否将副本数量设为零
// 返回值：返回生成的基本配置值和错误信息
func GenerateBasicValue(globalConfig *Config, componentConfig ComponentPodSpec, bosConfig *BosConfig, dbConfig *DatabaseConfig,
	ins *InstanceConfig, overwrite map[string]interface{}, zeroReplicas bool) (map[string]interface{}, error) {

	conf := newBasicConfig(globalConfig, bosConfig, dbConfig, ins)

	var valueWriter bytes.Buffer

	forkComponentConfig := componentConfig.Clone()

	if zeroReplicas {
		for component, spec := range forkComponentConfig {
			spec.Replicas = "0"
			forkComponentConfig[component] = spec
		}
	}

	configData := forkComponentConfig.mapStruct2MapInterface()

	structs.FillMap(conf, configData)

	err := valueTemplateEngine.valueTpl.Execute(&valueWriter, configData)
	if err != nil {
		return nil, fmt.Errorf("value template engine exeute failed: %w", err)
	}

	values := make(map[string]interface{})

	err = yaml.Unmarshal(valueWriter.Bytes(), &values)
	if err != nil {
		return nil, fmt.Errorf("yaml unmarshal values failed: %w", err)
	}

	if overwrite == nil || len(overwrite) == 0 {
		return values, nil
	}

	// merge with overwrite
	srcDoc, err := json.Marshal(values)
	if err != nil {
		return nil, fmt.Errorf("yaml unmarshal values failed: %w", err)
	}

	overwriteDoc, err := json.Marshal(overwrite)
	if err != nil {
		return nil, fmt.Errorf("yaml marshal overwrite failed: %w", err)
	}

	srcDoc, err = jsonpatch.MergePatch(srcDoc, overwriteDoc)
	if err != nil {
		return nil, fmt.Errorf("merge patch to values failed: %w", err)
	}

	err = json.Unmarshal(srcDoc, &values)

	return values, err
}

func ResourceLimitPercent(res string, per int64) (string, error) {
	number, err := resource.ParseQuantity(res)
	if err != nil {
		return "", fmt.Errorf("parse quantity %s failed: %s", res, err)
	}

	return fmt.Sprintf("%d", number.Value()*per/100), nil
}
