package config

import (
	"bytes"

	"k8s.io/apimachinery/pkg/util/yaml"
)

const (
	keepedValue = `
harbor:
  core:
    secret: {{ .CoreSecret }}
    xsrfKey: {{ .CsrfKey }}
  jobservice:
    secret: {{ .JobserviceSecret }}
  registry:
    secret: {{ .RegistrySecret }}
  harborAdminPassword: {{ .AdminPassword }}
ccr:
  jwtTlsCrt: {{ .JwtTlsCrt }}
  jwtTlsKey: {{ .JwtTlsKey }}
`
)

var keepedTemplateEngine = newValueTemplateOrPanic("ccr-keeped", keepedValue)

type OverwriteConfig struct {
	CoreSecret       string
	CsrfKey          string
	JobserviceSecret string
	RegistrySecret   string
	AdminPassword    string
	JwtTlsCrt        string
	JwtTlsKey        string
}

func GenerateOverwriteConfig(oc *OverwriteConfig) (map[string]interface{}, error) {
	var buf bytes.Buffer

	err := keepedTemplateEngine.valueTpl.Execute(&buf, oc)
	if err != nil {
		return nil, err
	}

	values := make(map[string]interface{})
	err = yaml.Unmarshal(buf.Bytes(), &values)
	if err != nil {
		return nil, err
	}

	return values, nil
}
