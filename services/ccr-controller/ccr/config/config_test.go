package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewComponentPodSpecOrDie(t *testing.T) {

	filename := "container_spec.yaml"
	componentConfig := NewComponentPodSpecOrDie(filename)

	assert.NotNil(t, componentConfig, "should have some values")

	componentPodSpec, ok := (*componentConfig)["BASIC"]

	assert.Equal(t, ok, true)

	podSpec, ok := componentPodSpec[ComponentCore]

	assert.Equal(t, ok, true)
	assert.Equal(t, podSpec.Replicas, "2")

}
