package config

import (
	"bytes"
	"testing"
	"text/template"

	"github.com/stretchr/testify/assert"
)

func Test_GenerateBasicValue(t *testing.T) {
	globalConf := &Config{
		AccessKey:           "xxx",
		SecretKey:           "yyy",
		Region:              "gztest",
		BosEndpoint:         "bos.baidubce.com",
		ChartPath:           "/test",
		PublicDomainSuffix:  "xxx.ccr.baidubce.com",
		PrivateDomainSuffix: "vpc.ccr.baidubce.com",
		ClusterDomainSuffix: "cluster.ccr.baidubce.com",
		AuthDomain:          "xxxx.auth.ccr.baidubce.com",
	}

	bosConfig := &BosConfig{
		Ak:       "bosak",
		Sk:       "bossk",
		Bucket:   "bosbucket",
		Endpoint: "bosendpoint",
	}

	dbConfig := &DatabaseConfig{
		Host:     "db.test.com",
		Port:     "22",
		Username: "iamatest",
		Password: "atestpassword",
	}

	values, err := GenerateBasicValue(globalConf, defaultBasicComponentContainerSpec, bosConfig, dbConfig, NewInstanceConfig("test", "", "", "", ""), nil, false)

	t.Logf("generate basic values: %s", values)

	assert.Nil(t, err, "should have no error")
	assert.NotNil(t, values, "should have some values")

	oc := OverwriteConfig{
		CoreSecret:       "coresecret",
		CsrfKey:          "csrfkey",
		JobserviceSecret: "jobservicesecret",
		RegistrySecret:   "registrysecret",
		AdminPassword:    "adminpassword",
		JwtTlsCrt:        "jwttlscrt",
		JwtTlsKey:        "jwttlskey",
	}

	om, err := GenerateOverwriteConfig(&oc)
	assert.Nil(t, err)
	assert.NotNil(t, om)

	valueWithOverwrite, err := GenerateBasicValue(globalConf, defaultBasicComponentContainerSpec, bosConfig, dbConfig, NewInstanceConfig("test", "", "", "", ""), om, false)
	assert.Nil(t, err)
	assert.NotNil(t, valueWithOverwrite)
}

func Test_ResourceLimitPercent(t *testing.T) {
	tpl, err := template.New("test").Funcs(template.FuncMap{
		"resourcePercent": ResourceLimitPercent,
	}).Parse("{{ resourcePercent .val 90 }}")
	assert.NoError(t, err)

	var valueWriter bytes.Buffer

	err = tpl.Execute(&valueWriter, map[string]interface{}{"val": "4Gi"})
	assert.NoError(t, err)
	assert.Equal(t, "3865470566", valueWriter.String())

	err = tpl.Execute(&valueWriter, map[string]interface{}{"val": "test"})
	assert.Error(t, err)
}
