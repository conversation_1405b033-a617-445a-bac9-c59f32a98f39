package config

import (
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"os"

	"github.com/fatih/structs"
	"k8s.io/apimachinery/pkg/util/yaml"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

type Config struct {
	AccessKey           string `yaml:"accessKey,omitempty"`
	SecretKey           string `yaml:"secretKey,omitempty"`
	UserID              string `yaml:"userID,omitempty"`
	Region              string `yaml:"region,omitempty"`
	BosEndpoint         string `yaml:"bosEndpoint,omitempty"`
	ChartPath           string `yaml:"chartPath,omitempty"`
	DisableBosEncrypt   bool   `yaml:"disableBosEncrypt,omitempty"`
	PublicDomainSuffix  string `yaml:"publicDomainSuffix,omitempty"`
	PrivateDomainSuffix string `yaml:"privateDomainSuffix,omitempty"`
	ClusterDomainSuffix string `yaml:"clusterDomainSuffix,omitempty"`
	AuthDomain          string `yaml:"authDomain,omitempty"`
	DomainKey           string `yaml:"domainKey,omitempty"`
	DomainCert          string `yaml:"domainCert,omitempty"`
}

const (
	RegistryDefault = "registry.baidubce.com"

	CoreReplicas          = "2"
	CoreRepositoryDefault = "ccr-image/harbor-core"
	CoreTagDefault        = "v2.2.3"

	JobServiceReplicas          = "2"
	JobServiceRepositoryDefault = "ccr-image/harbor-jobservice"
	JobServiceTagDefault        = "v2.2.3"

	RegistryReplicas             = "2"
	RegistryRepositoryDefault    = "ccr-image/registry-photon"
	RegistryTagDefault           = "v2.7.1-bos"
	RegistryCtlRepositoryDefault = "ccr-image/ccr-registryctl"
	RegistryCtlTagDefault        = "v2.2.3-bos-1"

	ChartMuseumReplicas          = "2"
	ChartMuseumRepositoryDefault = "ccr-image/chartmuseum-photon"
	ChartMuseumTagDefault        = "v2.2.3"

	TrivyReplicas          = "2"
	TrivyRepositoryDefault = "ccr-image/trivy-onfly"
	TrivyTagDefault        = "v1-2021080200"

	HarborExporterReplicas          = "2"
	HarborExporterRepositoryDefault = "ccr-image/harbor-exporter"
	HarborExporterTagDefault        = "v2.2.3"

	IngressCtlReplicas          = "3"
	IngressCtlRepositoryDefault = "ccr-image/ingress-nginx"
	IngressCtlTagDefault        = "v0.47.0"

	RedisReplicas                          = "3"
	RedisRepositoryDefault                 = "ccr-image/redis"
	RedisTagDefault                        = "6.2.4-debian-10-r14"
	RedisSentinelRepositoryDefault         = "ccr-image/redis-sentinel"
	RedisSentinelTagDefault                = "6.2.4-debian-10-r14"
	RedisExporterRepositoryDefault         = "ccr-image/redis-exporter"
	RedisExporterTagDefault                = "1.24.0-debian-10-r9"
	RedisSentinelExporterRepositoryDefault = "ccr-image/redis-sentinel-exporter"
	RedisSentinelExporterTagDefault        = "1.7.1-debian-10-r161"

	HarborAddonReplicas          = "2"
	HarborAddonRepositoryDefault = "ccr-image/harbor-addon"
	HarborAddonTagDefault        = "v0.0.1"

	PortalReplicas          = "1"
	PortalRepositoryDefault = "ccr-image/harbor-portal"
	PortalTagDefault        = "v2.3.5"

	SchedulerReplicas          = "3"
	SchedulerRepositoryDefault = "ccr-image/dragonfly-scheduler"
	SchedulerTagDefault        = "v2.1.0-0.2"

	DatabaseUser     = "ccr"
	DatabasePassword = "enterprise123"
)

type Component string

const (
	ComponentCore           Component = "core"
	ComponentJobService     Component = "jobService"
	ComponentPortal         Component = "portal"
	ComponentTrivy          Component = "trivy"
	ComponentChartMuseum    Component = "chartMuseum"
	ComponentHarborExporter Component = "harborExporter"

	ComponentRegistry Component = "registry"

	ComponentIngressCtl Component = "ingressCtl"

	ComponentRedis Component = "redis"

	ComponentHarborAddon Component = "harborAddon"

	ComponentScheduler Component = "scheduler"
)

type Container string

const (
	ContainerCore           Container = "core"
	ContainerJobService     Container = "jobService"
	ContainerPortal         Container = "portal"
	ContainerTrivy          Container = "trivy"
	ContainerChartMuseum    Container = "chartMuseum"
	ContainerHarborExporter Container = "harborExporter"

	ContainerRegistry    Container = "registry"
	ContainerRegistryCtl Container = "registryCtl"

	ContainerIngressCtl Container = "ingressCtl"

	ContainerRedis                 Container = "redis"
	ContainerRedisExporter         Container = "exporter"
	ContainerRedisSentinel         Container = "sentinel"
	ContainerRedisSentinelExporter Container = "sentinelExporter"

	ContainerHarborAddon Container = "harborAddon"

	ContainerScheduler Container = "scheduler"
)

type PodSpec struct {
	Replicas  string                      `yaml:"replicas,omitempty" structs:"replicas,omitempty"`
	Container map[Container]ContainerSpec `yaml:"container,omitempty" structs:"container,omitempty"`
}

type ContainerSpec struct {
	Registry   string   `yaml:"registry,omitempty" structs:"registry,omitempty"`
	Repository string   `yaml:"repository,omitempty" structs:"repository,omitempty"`
	Tag        string   `yaml:"tag,omitempty" structs:"tag,omitempty"`
	Requests   Resource `yaml:"requests,omitempty" structs:"requests,omitempty"`
	Limits     Resource `yaml:"limits,omitempty" structs:"limits,omitempty"`
}

type Resource struct {
	Memory string `yaml:"memory,omitempty" structs:"memory,omitempty"`
	CPU    string `yaml:"cpu,omitempty" structs:"cpu,omitempty"`
}

type ComponentPodSpec map[Component]PodSpec

func (c ComponentPodSpec) Clone() ComponentPodSpec {
	ccs := make(ComponentPodSpec)
	for k, v := range c {
		ccs[k] = v
	}

	return ccs
}

func (c ComponentPodSpec) mapStruct2MapInterface() map[string]interface{} {
	result := make(map[string]interface{})

	for k, v := range c {
		result[string(k)] = structs.Map(v)
	}

	return result
}

type CCRTypePodSpec map[v1alpha1.CCRType]ComponentPodSpec

func NewComponentPodSpecOrDie(filename string) *CCRTypePodSpec {
	var ccrSpec CCRTypePodSpec = nil
	if len(filename) != 0 {
		if _, err := os.Stat(filename); err != nil {
			panic(fmt.Sprintf("%s is not existed", filename))
		}

		content, err := ioutil.ReadFile(filename)
		if err != nil {
			panic(fmt.Sprintf("read file %v failed: %v", filename, err))
		}

		if err = yaml.Unmarshal(content, &ccrSpec); err != nil {
			panic(fmt.Sprintf("content in file %s is invalid", filename))
		}
	}

	// 没有外部的配置文件，直接使用默认的
	if ccrSpec == nil {
		ccrSpec = make(CCRTypePodSpec)
		ccrSpec[v1alpha1.BasicCCRType] = make(ComponentPodSpec)
		ccrSpec[v1alpha1.StandardCCRType] = make(ComponentPodSpec)
		ccrSpec[v1alpha1.AdvancedCCRType] = make(ComponentPodSpec)
	}

	fillBasicComponentSpec(ccrSpec, v1alpha1.BasicCCRType)
	fillBasicComponentSpec(ccrSpec, v1alpha1.StandardCCRType)
	fillBasicComponentSpec(ccrSpec, v1alpha1.AdvancedCCRType)

	return &ccrSpec
}

func NewConfigOrDie(filename, keyFile, certFile string) *Config {
	var conf Config
	if filename == "" {
		panic("no config provided")
	}

	if _, err := os.Stat(filename); err != nil {
		panic(fmt.Sprintf("%v is not existed", filename))
	}

	content, err := ioutil.ReadFile(filename)
	if err != nil {
		panic(fmt.Sprintf("cannot read config file: %v", filename))
	}

	err = yaml.Unmarshal(content, &conf)
	if err != nil {
		panic(fmt.Sprintf("file format is invalid: %s", err))
	}

	// keyfile
	if keyFile != "" {
		conf.DomainKey, err = readAndDecrypt(keyFile)
		if err != nil {
			panic(fmt.Sprintf("key file is invalid: %s", err))
		}
	}

	if certFile != "" {
		conf.DomainCert, err = readAndDecrypt(certFile)
		if err != nil {
			panic(fmt.Sprintf("cert file is invalid: %s", err))
		}
	}

	return &conf
}

func readAndDecrypt(filename string) (string, error) {
	if _, err := os.Stat(filename); err != nil {
		return "", err
	}

	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return "", err
	}

	decrypted, err := utils.AESCFBDecrypt(string(content))
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString([]byte(decrypted)), nil
}
