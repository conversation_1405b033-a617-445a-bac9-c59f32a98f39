package ccr

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"strconv"
	"sync"

	pkgrelease "helm.sh/helm/v3/pkg/release"
	"helm.sh/helm/v3/pkg/storage/driver"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/manager"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/blb"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/database/postgres"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/helm"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-controller/ccr/config"
)

type CCRHandler struct {
	config        *config.Config
	containerSpec *config.CCRTypePodSpec
	client        client.Client
	mgr           manager.Manager
	// name to db connection
	dbs *sync.Map
}

func NewCCRHandlerOrDie(conf *config.Config, containerSpec *config.CCRTypePodSpec, mgr manager.Manager, cli client.Client) *CCRHandler {
	return &CCRHandler{
		config:        conf,
		containerSpec: containerSpec,
		client:        cli,
		mgr:           mgr,
		dbs:           new(sync.Map),
	}
}

func (c *CCRHandler) Handle(ctx context.Context, object *ccrv1alpha1.CCR) error {
	obj := object.DeepCopy()

	// need to create and update
	switch {
	// delete
	case obj.DeletionTimestamp != nil:
		return c.handleDeleting(ctx, obj)
	case obj.Status.Phase == ccrv1alpha1.CCRFailed,
		obj.Status.Phase == ccrv1alpha1.CCRUpgradeFailed,
		obj.Status.Phase == ccrv1alpha1.CCRStartingFailed,
		obj.Status.Phase == ccrv1alpha1.CCRStoppingFailed:
		return nil
	case obj.Status.Phase == ccrv1alpha1.CCRPending:
		return c.handlePendingPhase(ctx, obj)
	case obj.Status.Phase == ccrv1alpha1.CCRCreating:
		return c.handleCreating(ctx, obj)

	case obj.Status.Phase == ccrv1alpha1.CCRUpgrading:
		return c.handleUpgrading(ctx, obj)
	case obj.Status.Phase == ccrv1alpha1.CCRCalibrating:
		return c.handleCalibrating(ctx, obj)

	case obj.Status.Phase == ccrv1alpha1.CCRStarting:
		return c.handleStarting(ctx, obj)
	case obj.Status.Phase == ccrv1alpha1.CCRStopping:
		return c.handleStopping(ctx, obj)

	case obj.Status.Type != obj.Spec.Type:
		return c.handleUpdateType(ctx, obj)

	case obj.Status.Action != obj.Spec.Action:
		return c.handleActioning(ctx, obj)
	}

	return nil
}

func (c *CCRHandler) handleCreating(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)
	// create namespace first
	var ns corev1.Namespace
	err := c.client.Get(ctx, types.NamespacedName{Namespace: metav1.NamespaceNone, Name: object.GetName()}, &ns)
	if err != nil && !apierrors.IsNotFound(err) {
		c.updateReason(ctx, object, ccrv1alpha1.ReasonNamespaceNotReady, err.Error())
		return fmt.Errorf("list namespace failed: %w", err)
	}

	if apierrors.IsNotFound(err) {
		err = c.client.Create(ctx, &corev1.Namespace{
			TypeMeta: metav1.TypeMeta{
				APIVersion: "v1",
				Kind:       "Namespace",
			},
			ObjectMeta: metav1.ObjectMeta{
				Name: object.GetName(),
			},
		})
		if err != nil {
			c.updateReason(ctx, object, ccrv1alpha1.ReasonNamespaceNotReady, err.Error())
			return fmt.Errorf("create namespace failed: %w", err)
		}
	}
	// create quota
	err = c.handleCreateQuota(ctx, object)
	if err != nil {
		return err
	}

	// create bos first
	err = c.handleCreateBos(ctx, object)
	if err != nil {
		return err
	}
	// check database first
	dbConfig, err := c.handleCreateDatabase(ctx, object)
	if err != nil {
		logger.V(2).Error(err, "database is not ready now")
		c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseNotReady, err.Error())
		return err
	}

	// check release
	st, err := helm.Status(ctx, object.Name, object.Name, c.mgr)
	if err != nil {
		if !errors.Is(err, driver.ErrReleaseNotFound) {
			logger.V(2).Error(err, fmt.Sprintf("find release %v failed", object.Name))
			c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseStatusFailed, err.Error())
			return err
		}

		//bos config
		bosConfig := &config.BosConfig{
			Ak:       c.config.AccessKey,
			Sk:       c.config.SecretKey,
			Bucket:   object.Status.Bucket,
			Endpoint: c.config.BosEndpoint,
		}
		// create release
		values, err := config.GenerateBasicValue(
			c.config,
			(*c.containerSpec)[object.Spec.Type],
			bosConfig,
			dbConfig,
			config.NewInstanceConfig(object.GetName(),
				object.GetName(),
				"",
				"",
				""),
			nil, false)
		if err != nil {
			logger.V(2).Error(err, "generate value field failed")
			c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseInstallFailed, "generate value failed")
			return err
		}

		err = helm.Install(ctx, object.GetName(), object.Name, c.config.ChartPath, values, c.mgr)
		if err != nil {
			logger.V(2).Error(err, "install chart failed")
			c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseInstallFailed, err.Error())
			return err
		}

		return fmt.Errorf("after release install, wait for release ready")
	}

	if st == pkgrelease.StatusFailed {
		logger.V(2).Error(fmt.Errorf("install release failed"), "install relase failed")
		object.Status.Phase = ccrv1alpha1.CCRFailed
		c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseInstallFailed, "install release failed")
		return fmt.Errorf("install release failed")
	}

	// wait for loadbalancer created
	err = c.createServicePublishPoint(ctx, object)
	if err != nil {
		logger.V(2).Error(err, "create service publish point failed")
		return err
	}

	object.Status.Phase = ccrv1alpha1.CCRCreated
	object.Status.Type = object.Spec.Type
	return c.updateReason(ctx, object, "", "")
}

func (c *CCRHandler) handleUpdateType(ctx context.Context, object *ccrv1alpha1.CCR) error {

	if object.Status.Phase != ccrv1alpha1.CCRRunning && object.Status.Phase != ccrv1alpha1.CCRUpgrading {
		return fmt.Errorf("can not upgrading under status: %s", string(object.Status.Phase))
	}

	if object.Status.Phase == ccrv1alpha1.CCRRunning {
		object.Status.Type = object.Spec.Type
		object.Status.Phase = ccrv1alpha1.CCRUpgrading

	}

	return c.updateReason(ctx, object, "", "")
}

func (c *CCRHandler) handleUpgrading(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)

	// quota upgrade
	var ccrQuota ccrv1alpha1.CCRQuota
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &ccrQuota)
	if err != nil {
		logger.V(2).Error(err, "get ccr quota failed when upgrade")
		c.updateReason(ctx, object, ccrv1alpha1.ReasonQuotaNotReady, err.Error())
		return err
	}

	if ccrQuota.Spec.Type != object.Spec.Type {
		ccrQuota.Spec.Type = object.Spec.Type
		err = c.client.Update(ctx, &ccrQuota)
		if err != nil {
			logger.V(2).Error(err, "upgrade ccr quota failed")
			c.updateReason(ctx, object, ccrv1alpha1.ReasonQuotaNotReady, err.Error())
			return err
		}
	}

	// postgres upgrade
	if object.Spec.Database == nil {
		var ccrPostgres ccrv1alpha1.Postgres
		err = c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &ccrPostgres)
		if err != nil {
			logger.V(2).Error(err, "get ccr postgres failed")
			c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseNotReady, err.Error())
			return err
		}

		if ccrPostgres.Status.Type != string(object.Spec.Type) {
			if ccrPostgres.Spec.InstanceType != string(object.Spec.Type) {
				ccrPostgres.Spec.InstanceType = string(object.Spec.Type)
				err = c.client.Update(ctx, &ccrPostgres)
				if err != nil {
					logger.V(2).Error(err, "update postgres failed")
					c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseNotReady, err.Error())
					return err
				}
			}

			return fmt.Errorf("wait for postgres ready")
		}
	}

	if err = c.releaseUpgrade(ctx, object); err != nil {
		logger.Error(err, "upgrade release failed")
		object.Status.Phase = ccrv1alpha1.CCRUpgradeFailed
		return c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseUpgradeFailed, err.Error())
	}

	object.Status.Phase = ccrv1alpha1.CCRUpgraded
	return c.updateReason(ctx, object, "", "")
}

// calibration: should not exposed to user
func (c *CCRHandler) handleCalibrating(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)

	if err := c.releaseUpgrade(ctx, object); err != nil {
		logger.Error(err, "upgrade release failed when calibrating")
		object.Status.Phase = ccrv1alpha1.CCRUpgradeFailed
		return c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseUpgradeFailed, err.Error())
	}

	object.Status.Phase = ccrv1alpha1.CCRRunning
	return c.updateReason(ctx, object, "", "")
}

func (c *CCRHandler) handleActioning(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)
	logger.V(2).Info("start billing action")
	if object.Spec.Action == ccrv1alpha1.StartAction && object.Status.Action == ccrv1alpha1.StopAction {
		object.Status.Action = object.Spec.Action
		object.Status.Phase = ccrv1alpha1.CCRStarting
	}

	if object.Spec.Action == ccrv1alpha1.StopAction &&
		(object.Status.Action == "" || object.Status.Action == ccrv1alpha1.StartAction) {
		object.Status.Action = object.Spec.Action
		object.Status.Phase = ccrv1alpha1.CCRStopping
	}

	return c.updateReason(ctx, object, "", "")
}

func (c *CCRHandler) handleStarting(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)
	logger.V(2).Info("start release start")
	if err := c.releaseStart(ctx, object); err != nil {
		logger.Error(err, "start release failed when starting")
		object.Status.Phase = ccrv1alpha1.CCRStartingFailed
		return c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseStartFailed, err.Error())
	}

	object.Status.Phase = ccrv1alpha1.CCRRunning
	return c.updateReason(ctx, object, "", "")
}

func (c *CCRHandler) handleStopping(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)

	if err := c.releaseStop(ctx, object); err != nil {
		logger.Error(err, "upgrade release failed when stopping")
		object.Status.Phase = ccrv1alpha1.CCRStoppingFailed
		return c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseStopFailed, err.Error())
	}

	object.Status.Phase = ccrv1alpha1.CCRStopped
	return c.updateReason(ctx, object, "", "")
}

func (c *CCRHandler) releaseUpgrade(ctx context.Context, object *ccrv1alpha1.CCR) error {
	values, err := ValuesFromCCR(ctx, c.config, c.containerSpec, c.client, object, false)
	if err != nil {
		return err
	}

	return helm.Upgrade(ctx, object.GetName(), object.GetName(), c.config.ChartPath, values, c.mgr)
}

func (c *CCRHandler) releaseStop(ctx context.Context, object *ccrv1alpha1.CCR) error {
	values, err := ValuesFromCCR(ctx, c.config, c.containerSpec, c.client, object, true)
	if err != nil {
		return err
	}

	return helm.Upgrade(ctx, object.GetName(), object.GetName(), c.config.ChartPath, values, c.mgr)
}

func (c *CCRHandler) releaseStart(ctx context.Context, object *ccrv1alpha1.CCR) error {
	values, err := ValuesFromCCR(ctx, c.config, c.containerSpec, c.client, object, false)
	if err != nil {
		return err
	}

	return helm.Upgrade(ctx, object.GetName(), object.GetName(), c.config.ChartPath, values, c.mgr)
}

func (c *CCRHandler) handleCreateQuota(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)
	var err error

	var ccrQuota ccrv1alpha1.CCRQuota
	err = c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &ccrQuota)
	if err != nil && !apierrors.IsNotFound(err) {
		logger.V(2).Error(err, "get ccr quota failed")
		c.updateReason(ctx, object, ccrv1alpha1.ReasonQuotaNotReady, err.Error())
		return err
	}

	if apierrors.IsNotFound(err) {
		ccrQuota = ccrv1alpha1.CCRQuota{
			TypeMeta: metav1.TypeMeta{
				Kind:       "CCRQuota",
				APIVersion: "cce.baidubce.com/v1alpha1",
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      object.GetName(),
				Namespace: object.GetName(),
				Labels:    object.GetLabels(),
				OwnerReferences: []metav1.OwnerReference{
					*ownerReferenceFor(object),
				},
			},
			Spec: ccrv1alpha1.CCRQuotaSpec{
				Type:      object.Spec.Type,
				AccountID: object.Spec.AccountID,
			},
		}

		err = c.client.Create(ctx, &ccrQuota)
		if err != nil {
			logger.V(2).Error(err, "create ccr quota failed")
			c.updateReason(ctx, object, ccrv1alpha1.ReasonBosNotReady, err.Error())
			return err
		}
	}

	if ccrQuota.Status.Phase != ccrv1alpha1.CCRQuotaPhaseReady {
		return fmt.Errorf("ccr quota is not ready, wait for next round")
	}

	return nil
}

func (c *CCRHandler) handleDeleteQuota(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)
	var err error

	var ccrQuota ccrv1alpha1.CCRQuota
	err = c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &ccrQuota)
	if err != nil && !apierrors.IsNotFound(err) {
		logger.V(2).Error(err, "get ccr quota failed")
		c.updateReason(ctx, object, ccrv1alpha1.ReasonQuotaNotReady, err.Error())
		return err
	}

	if apierrors.IsNotFound(err) {
		return nil
	}

	err = c.client.Delete(ctx, &ccrQuota)
	if err != nil {
		logger.V(2).Error(err, "delete ccr quota failed")
		c.updateReason(ctx, object, ccrv1alpha1.ReasonQuotaNotReady, err.Error())
		return err
	}

	return nil
}

func (c *CCRHandler) handleCreateBos(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)
	var err error

	if object.Status.Bucket != "" {
		return nil
	}

	if object.Spec.Storage != nil {
		object.Status.Bucket = object.Spec.Storage.Bucket
		return nil
	}

	var bosObj ccrv1alpha1.Bos
	err = c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &bosObj)
	if err != nil && !apierrors.IsNotFound(err) {
		logger.V(2).Error(err, "get bos failed")
		c.updateReason(ctx, object, ccrv1alpha1.ReasonBosNotReady, err.Error())
		return err
	}

	if apierrors.IsNotFound(err) {
		bosObj = ccrv1alpha1.Bos{
			TypeMeta: metav1.TypeMeta{
				Kind:       "Bos",
				APIVersion: "cce.baidubce.com/v1alpha1",
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      object.GetName(),
				Namespace: object.GetName(),
				Labels:    object.GetLabels(),
				OwnerReferences: []metav1.OwnerReference{
					*ownerReferenceFor(object),
				},
			},
			Spec: ccrv1alpha1.BosSpec{
				ExternalAccountID: object.Spec.AccountID,
				ExternalUserID:    object.Spec.UserID,
				UserID:            c.config.UserID,
				NeedEncrypt:       !c.config.DisableBosEncrypt,
			},
		}

		err = c.client.Create(ctx, &bosObj)
		if err != nil {
			logger.Error(err, "create bos failed")
			c.updateReason(ctx, object, ccrv1alpha1.ReasonBosNotReady, err.Error())
			return err
		}
	}

	switch bosObj.Status.Phase {
	case ccrv1alpha1.BosStatusPhaseReady:
		object.Status.Bucket = bosObj.Status.Bucket
		return nil
	case ccrv1alpha1.BosStatusPhaseFailed:
		object.Status.Phase = ccrv1alpha1.CCRFailed
		c.updateReason(ctx, object, ccrv1alpha1.ReasonBosNotReady, bosObj.Status.Reason)
	}

	return fmt.Errorf("bos is not ready")
}

func (c *CCRHandler) handleDeleteBos(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)
	if object.Spec.Storage != nil {
		return nil
	}

	var bosObj ccrv1alpha1.Bos
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &bosObj)
	if err != nil && !apierrors.IsNotFound(err) {
		logger.V(2).Error(err, "get bos object failed")
		c.updateReason(ctx, object, ccrv1alpha1.ReasonBosDeleteFailed, err.Error())
		return err
	}

	if apierrors.IsNotFound(err) {
		return nil
	}

	logger.V(2).Info("begin to delete bos object")
	err = c.client.Delete(ctx, &bosObj)
	if err != nil && !apierrors.IsNotFound(err) {
		logger.V(2).Error(err, "delete bos object failed")
		c.updateReason(ctx, object, ccrv1alpha1.ReasonBosDeleteFailed, err.Error())
		return err
	}

	return nil
}

// handleCreateDatabase 处理创建数据库的逻辑，如果指定了数据库则使用指定的数据库，否则创建一个新的数据库。
// 返回值：*config.DatabaseConfig，error，分别为数据库配置和错误信息
func (c *CCRHandler) handleCreateDatabase(ctx context.Context, object *ccrv1alpha1.CCR) (*config.DatabaseConfig, error) {
	logger := log.FromContext(ctx)
	var err error
	var dbConfig config.DatabaseConfig

	if object.Spec.Database != nil {
		// extract password
		passwdBytes, err := base64.StdEncoding.DecodeString(object.Spec.Database.Password)
		if err != nil {
			object.Status.Phase = ccrv1alpha1.CCRFailed
			c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseNotReady, "password is in wrong format")
			return nil, err
		}

		dbConfig = config.DatabaseConfig{
			Host:     object.Spec.Database.Host,
			Port:     fmt.Sprintf("%v", object.Spec.Database.Port),
			Username: object.Spec.Database.Username,
			Password: string(passwdBytes),
		}
	} else {
		pgObj := ccrv1alpha1.Postgres{}
		err = c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &pgObj)
		if err != nil && !apierrors.IsNotFound(err) {
			logger.V(2).Error(err, "get object failed")
			c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseNotReady, err.Error())
			return nil, err
		}

		if apierrors.IsNotFound(err) {
			pgObj = ccrv1alpha1.Postgres{
				TypeMeta: metav1.TypeMeta{
					Kind:       "Postgres",
					APIVersion: "cce.baidubce.com/v1alpha1",
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:      object.GetName(),
					Namespace: object.GetName(),
					Labels:    object.GetLabels(),
					OwnerReferences: []metav1.OwnerReference{
						*ownerReferenceFor(object),
					},
				},
				Spec: ccrv1alpha1.PostgresSpec{
					InstanceType: string(object.Spec.Type),
					Username:     config.DatabaseUser,
					Password:     base64.StdEncoding.EncodeToString([]byte(config.DatabasePassword)),
				},
			}

			err = c.client.Create(ctx, &pgObj)
			if err != nil {
				logger.V(2).Error(err, "create database failed")
				c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseNotReady, err.Error())
				return nil, err
			}
		}

		switch pgObj.Status.Phase {
		case ccrv1alpha1.PostgresStatusRunning:
			// skip
		case ccrv1alpha1.PostgresStatusFailed:
			object.Status.Phase = ccrv1alpha1.CCRFailed
			return nil, c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseNotReady, object.Status.Reason)
		default:
			c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseNotReady, "postgres is not ready")
			return nil, fmt.Errorf("waiting for postgres ready")
		}

		dbConfig = config.DatabaseConfig{
			Host:     pgObj.Status.Host,
			Port:     pgObj.Status.Port,
			Username: pgObj.Status.UserName,
			Password: config.DatabasePassword,
		}
	}

	port, err := strconv.Atoi(dbConfig.Port)
	if err != nil {
		logger.V(2).Error(err, fmt.Sprintf("port %v is not an number", dbConfig.Port))
		c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseNotReady, err.Error())
		return nil, err
	}

	if len(object.Status.Database.Databases) == 0 {
		// create database harbor
		db, dbExisted := c.dbs.Load(object.GetName())
		if !dbExisted {
			db, err = postgres.NewPostgres(
				dbConfig.Host,
				port,
				dbConfig.Username,
				dbConfig.Password,
				"postgres")
			if err != nil {
				logger.Error(err, fmt.Sprintf("open postgres failed: %v", dbConfig.Host))
				c.updateReason(ctx, object, ccrv1alpha1.ReasonPostgresNotReady, err.Error())
				return nil, err
			}

			c.dbs.Store(object.GetName(), db)
		}

		pg := db.(*postgres.Postgres)
		dbExisted, err = pg.ExistDB("harbor")
		if err != nil {
			logger.Error(err, "get database failed")
			c.updateReason(ctx, object, ccrv1alpha1.ReasonPostgresNotReady, err.Error())
			return nil, err
		}

		if !dbExisted {
			err = pg.CreateDB("harbor")
			if err != nil {
				logger.Error(err, "create database failed")
				c.updateReason(ctx, object, ccrv1alpha1.ReasonPostgresNotReady, err.Error())
				return nil, err
			}
		}

		object.Status.Database.Databases = []string{"harbor"}
	}

	return &dbConfig, nil
}

func (c *CCRHandler) handleDeleteDatabase(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)
	// database is create external, skip delete
	if object.Spec.Database != nil {
		return nil
	}

	pgObj := ccrv1alpha1.Postgres{}
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &pgObj)
	if err != nil && !apierrors.IsNotFound(err) {
		logger.V(2).Error(err, "get postgres failed")
		c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseDeleteFailed, err.Error())
		return err
	}

	if apierrors.IsNotFound(err) {
		return nil
	}

	err = c.client.Delete(ctx, &pgObj)
	if err != nil {
		logger.V(2).Error(err, "delete postgres failed")
		c.updateReason(ctx, object, ccrv1alpha1.ReasonDatabaseDeleteFailed, err.Error())
		return err
	}

	return nil
}

func (c *CCRHandler) createServicePublishPoint(ctx context.Context, object *ccrv1alpha1.CCR) error {
	var netObj ccrv1alpha1.CNCNetwork
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &netObj)
	if err != nil && !apierrors.IsNotFound(err) {
		c.updateReason(ctx, object, "getCNCNetworkFailed", err.Error())
		return fmt.Errorf("get cncnetwork %v failed: %w", object.GetName(), err)
	}

	if apierrors.IsNotFound(err) {
		err = c.client.Create(ctx, &ccrv1alpha1.CNCNetwork{
			TypeMeta: metav1.TypeMeta{
				Kind:       "CNCNetwork",
				APIVersion: "cce.baidubce.com/v1alpha1",
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      object.GetName(),
				Namespace: object.GetName(),
				OwnerReferences: []metav1.OwnerReference{
					*ownerReferenceFor(object),
				},
				Labels: object.GetLabels(),
			},
			Spec: ccrv1alpha1.CNCNetworkSpec{
				AccountID: object.Spec.AccountID,
				UserID:    object.Spec.UserID,
				Domain: ccrv1alpha1.DomainSpec{
					PrivateDomain: object.Status.PrivateDomain,
					PublicDomain:  object.Status.PublicDomain,
				},
			},
		})
		if err != nil {
			c.updateReason(ctx, object, "createCNCNetworkFailed", err.Error())
			return fmt.Errorf("create cnc network failed: %w", err)
		}
	}

	if netObj.Status.PublishPoint == "" {
		c.updateReason(ctx, object, "PublishPointNotReady", "waiting for publish point ready")
		return fmt.Errorf("waiting publish point ready, for next round")
	}

	return nil
}

func (c *CCRHandler) handleDeleting(ctx context.Context, object *ccrv1alpha1.CCR) error {
	logger := log.FromContext(ctx)
	object.Status.Phase = ccrv1alpha1.CCRTerminating

	var netObj ccrv1alpha1.CNCNetwork
	err := c.client.Get(ctx, client.ObjectKey{Namespace: object.GetName(), Name: object.GetName()}, &netObj)
	if err != nil && !apierrors.IsNotFound(err) {
		c.updateReason(ctx, object, "getCNCNetworkFailed", err.Error())
		return fmt.Errorf("get cncnetwork %v failed: %w", object.GetName(), err)
	}

	if err == nil && netObj.DeletionTimestamp == nil {
		err = c.client.Delete(ctx, &netObj)
		if err != nil {
			c.updateReason(ctx, object, "deleteCNCNetworkFailed", err.Error())
			return fmt.Errorf("delete cncnetwork failed: %w", err)
		}
	}

	_, err = helm.Status(ctx, object.GetName(), object.Name, c.mgr)
	switch {
	case err == nil:
		err = helm.Delete(ctx, object.GetName(), object.Name, c.mgr)
		if err != nil {
			logger.Error(err, "delete release %v failed", object.Name)
			c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseUninstallFailed, err.Error())
			return err
		}
	case err != nil && !errors.Is(err, driver.ErrReleaseNotFound):
		logger.Error(err, "cannot status release %v", object.Name)
		c.updateReason(ctx, object, ccrv1alpha1.ReasonReleaseStatusFailed, err.Error())
		return err
	}

	err = c.handleDeleteBos(ctx, object)
	if err != nil {
		return fmt.Errorf("delete bos failed: %w", err)
	}

	err = c.handleDeleteQuota(ctx, object)
	if err != nil {
		return fmt.Errorf("delete ccr quota failed: %w", err)
	}

	err = c.handleDeleteDatabase(ctx, object)
	if err != nil {
		return fmt.Errorf("delete database failed: %w", err)
	}

	var ns corev1.Namespace
	err = c.client.Get(ctx, client.ObjectKey{Namespace: metav1.NamespaceNone, Name: object.GetName()}, &ns)
	if err != nil && !apierrors.IsNotFound(err) {
		return fmt.Errorf("get namespace failed: %w", err)
	}

	if err == nil {
		if ns.DeletionTimestamp == nil {
			err = c.client.Delete(ctx, &ns)
			if err != nil {
				return fmt.Errorf("delete namespace failed: %w", err)
			}
		}

		return fmt.Errorf("deleteing namespace, wait for next round")
	}

	return c.removeFinalizer(ctx, object)
}

// handlePendingPhase handles the pending phase of a CCR
// It updates the status phase and private/public domain names of CCR to be creating
// If any errors occur in updating, it will print an error and return
func (c *CCRHandler) handlePendingPhase(ctx context.Context, object *ccrv1alpha1.CCR) error {
	// set finalizer first
	logger := log.FromContext(ctx)
	finalizerSetted, err := c.setFinalizer(ctx, object)
	if err != nil {
		logger.V(2).Error(err, "update finalizer failed")
		return err
	}

	// finalizer setted, waitfor next round
	if !finalizerSetted {
		return fmt.Errorf("need next round")
	}

	object.Status.Phase = ccrv1alpha1.CCRCreating
	object.Status.PrivateDomain = object.Name + "-" + c.config.PrivateDomainSuffix
	object.Status.PublicDomain = object.Name + "-" + c.config.PublicDomainSuffix
	object.Status.CCRDomain = object.Name + "-" + c.config.ClusterDomainSuffix

	object.Status.P2PManagerDomain = object.Name + "-dragonfly-manager-" + c.config.PrivateDomainSuffix

	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime

	return c.client.Status().Update(ctx, object)
}

func (c *CCRHandler) setFinalizer(ctx context.Context, object *ccrv1alpha1.CCR) (bool, error) {
	finalizers := object.GetFinalizers()
	if finalizers == nil {
		finalizers = make([]string, 0)
	}

	for _, v := range finalizers {
		if v == ccrv1alpha1.CCRFinalizer {
			return true, nil
		}
	}

	finalizers = append(finalizers, ccrv1alpha1.CCRFinalizer)
	object.SetFinalizers(finalizers)

	return false, c.client.Update(ctx, object)
}

func (c *CCRHandler) removeFinalizer(ctx context.Context, object *ccrv1alpha1.CCR) error {
	finalizers := object.GetFinalizers()
	if finalizers == nil {
		return nil
	}

	reservedFinalizer := make([]string, 0)
	for _, v := range finalizers {
		if v != ccrv1alpha1.CCRFinalizer {
			reservedFinalizer = append(reservedFinalizer, v)
		}
	}

	object.SetFinalizers(reservedFinalizer)
	return c.client.Update(ctx, object)
}

func (c *CCRHandler) updateReason(ctx context.Context, object *ccrv1alpha1.CCR, reason string, message string) error {
	logger := log.FromContext(ctx)
	object.Status.Reason = reason
	object.Status.Message = message
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime
	err := c.client.Status().Update(ctx, object)
	if err != nil {
		logger.Error(err, "update status failed")
	}

	return err
}

func containService(svcPoints []blb.GetServiceResult, service string) bool {
	for _, v := range svcPoints {
		if v.Service == service {
			return true
		}
	}

	return false
}

func ownerReferenceFor(obj client.Object) *metav1.OwnerReference {
	trueFlag := true
	return &metav1.OwnerReference{
		APIVersion:         obj.GetObjectKind().GroupVersionKind().GroupVersion().Identifier(),
		Kind:               obj.GetObjectKind().GroupVersionKind().Kind,
		Name:               obj.GetName(),
		UID:                obj.GetUID(),
		Controller:         &trueFlag,
		BlockOwnerDeletion: &trueFlag,
	}
}

type CCRHandlerInterface interface {
	Handle(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleCreating(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleUpdateType(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleUpgrading(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleCalibrating(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleActioning(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleStarting(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleStopping(ctx context.Context, object *ccrv1alpha1.CCR) error
	//releaseUpgrade(ctx context.Context, object *ccrv1alpha1.CCR) error
	//releaseStop(ctx context.Context, object *ccrv1alpha1.CCR) error
	//releaseStart(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleCreateQuota(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleDeleteQuota(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleCreateBos(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleDeleteBos(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleCreateDatabase(ctx context.Context, object *ccrv1alpha1.CCR) (*config.DatabaseConfig, error)
	//handleDeleteDatabase(ctx context.Context, object *ccrv1alpha1.CCR) error
	//createServicePublishPoint(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handleDeleting(ctx context.Context, object *ccrv1alpha1.CCR) error
	//handlePendingPhase(ctx context.Context, object *ccrv1alpha1.CCR) error
	//setFinalizer(ctx context.Context, object *ccrv1alpha1.CCR) (bool, error)
	//removeFinalizer(ctx context.Context, object *ccrv1alpha1.CCR) error
	//updateReason(ctx context.Context, object *ccrv1alpha1.CCR, reason string, message string) error
}
