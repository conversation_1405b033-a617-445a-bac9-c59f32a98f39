package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

var (
	kubeconfig string
	region     string

	scheme = runtime.NewScheme()
)

// init 函数用于在程序启动时初始化一些全局变量
func init() {

	flag.StringVar(&kubeconfig, "kubeconfig", "~/.kube/config", "kubeconfig file path")
	flag.StringVar(&region, "region", "", "instance region")

	utilruntime.Must(clientgoscheme.AddToScheme(scheme))

	utilruntime.Must(v1alpha1.AddToScheme(scheme))
}

// main 函数为应用程序入口
func main() {
	flag.Parse()

	kubedata, err := os.ReadFile(kubeconfig)
	if err != nil {
		fmt.Printf("read file failed: %s \n", err)
		return
	}

	client, err := utils.NewK8sClient(string(kubedata), scheme)
	if err != nil {
		fmt.Printf("unable create cluster client: %s \n", err)
		return
	}

	var ccrList v1alpha1.CCRList
	if err := client.List(context.Background(), &ccrList); err != nil {
		fmt.Printf("list ccr instance failed: %s \n", err)
		return
	}
	for _, item := range ccrList.Items {
		fmt.Printf("patching p2p manager domain info for ccr %s ,p2p domain %v\n", item.Name, item.Status.P2PManagerDomain)
		item.Status.P2PManagerDomain = fmt.Sprintf("%s-dragonfly-manager-vpc.cnc.%s.baidubce.com", item.Name, region)
		if err := client.Status().Update(context.Background(), &item); err != nil {
			fmt.Printf("patch to p2p manager domain info failed: %s \n", err)
			return
		}
		fmt.Printf("end patching p2p manager domain info for ccr %s ,p2p domain %v\n", item.Name, item.Status.P2PManagerDomain)
	}

}
