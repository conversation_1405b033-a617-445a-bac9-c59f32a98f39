package main

import (
	"database/sql"
	"errors"
	"flag"
	"fmt"
	"net/http"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/rds"
	"github.com/baidubce/bce-sdk-go/services/scs"
	"github.com/baidubce/bce-sdk-go/services/vpc"
	"github.com/baidubce/bce-sdk-go/util/log"
	"github.com/google/uuid"
	_ "github.com/lib/pq"
)

var (
	ak          string
	sk          string
	scsEndpoint string
	vpcEndpoint string
	bosEndpoint string
	rdsEndpoint string
	pgHost      string
	pgPort      int
	logEnabled  bool
)

type scsClient struct {
	*scs.Client
}

type CreateInstanceArgs struct {
	Billing           scs.Billing       `json:"billing"`
	PurchaseCount     int               `json:"purchaseCount"`
	InstanceName      string            `json:"instanceName"`
	NodeType          string            `json:"nodeType"`
	ProxyNum          int               `json:"proxyNum"`
	ClusterType       string            `json:"clusterType"`
	ReplicationNum    int               `json:"replicationNum"`
	ReplicationInfo   []scs.Replication `json:"replicationInfo"`
	Port              int               `json:"port"`
	Engine            int               `json:"engine,omitempty"`
	EngineVersion     string            `json:"engineVersion"`
	DiskFlavor        int               `json:"diskFlavor,omitempty"`
	DiskType          string            `json:"diskType,omitempty"`
	VpcID             string            `json:"vpcId"`
	Subnets           []scs.Subnet      `json:"subnets,omitempty"`
	AutoRenewTimeUnit string            `json:"autoRenewTimeUnit,omitempty"`
	AutoRenewTime     int               `json:"autoRenewTime,omitempty"`
	BgwGroupId        string            `json:"bgwGroupId,omitempty"`
	ClientToken       string            `json:"-"`
	ClientAuth        string            `json:"clientAuth"`
	StoreType         int               `json:"storeType"`
	EnableReadOnly    int               `json:"enableReadOnly,omitempty"`
	AllocateVIP       string            `json:"allocateVip"`
}

func (c *scsClient) CreateInstanceWithVIP(args *CreateInstanceArgs) (interface{}, error) {
	if args == nil {
		return nil, fmt.Errorf("please set create scs argments")
	}

	result := &scs.CreateInstanceResult{}
	err := bce.NewRequestBuilder(c).
		WithMethod(http.MethodPost).
		WithURL(scs.INSTANCE_URL_V2).
		WithQueryParamFilter("clientToken", args.ClientToken).
		WithQueryParam("allocateVip", "1").
		WithBody(args).
		WithResult(result).
		Do()

	return result, err
}

func init() {
	flag.StringVar(&ak, "ak", "", "access key")
	flag.StringVar(&sk, "sk", "", "secret key")
	flag.StringVar(&scsEndpoint, "scs", "", "scs endpoint")
	flag.StringVar(&vpcEndpoint, "vpc", "", "vpc endpoint")
	flag.StringVar(&bosEndpoint, "bos", "", "bos endpoint")
	flag.StringVar(&rdsEndpoint, "rds", "", "rds endpoint")
	flag.StringVar(&pgHost, "pg-host", "", "pg host")
	flag.IntVar(&pgPort, "pg-port", 3306, "pg port")
	flag.BoolVar(&logEnabled, "log", false, "log enabled")
}

func main() {
	flag.Parse()

	if logEnabled {
		log.SetLogHandler(log.STDERR)
		log.SetLogLevel(log.Level(log.DEBUG))
	}

	vpcCli, err := vpc.NewClient(ak, sk, vpcEndpoint)
	if err != nil {
		panic("create vpc client failed: " + err.Error())
	}

	vpcList, err := vpcCli.ListVPC(&vpc.ListVPCArgs{})
	if err != nil {
		panic("create vpc client failed: " + err.Error())
	}

	fmt.Println(vpcList)

	if len(vpcList.VPCs) < 1 {
		panic("no vpc found")
	}

	subnetList, err := vpcCli.ListSubnets(&vpc.ListSubnetArgs{
		SubnetType: vpc.SUBNET_TYPE_BCC,
		VpcId:      vpcList.VPCs[0].VPCID,
	})
	if len(subnetList.Subnets) < 1 {
		panic("no subnet found for vpc: " + vpcList.VPCs[0].VPCID)
	}

	fmt.Println(subnetList)

	if err = createScs(); err != nil {
		panic("create scs client failed: " + err.Error())
	}

	if err = createBosOrSkip(); err != nil {
		panic("create bos failed: " + err.Error())
	}

	if err = initDatabase(); err != nil {
		panic("init database failed: " + err.Error())
	}

}

func createScs() error {
	if scsEndpoint == "" {
		fmt.Println("no scs endpoint provided, skipped")
		return nil
	}

	baseScs, err := scs.NewClient(ak, sk, scsEndpoint)
	if err != nil {
		panic(err)
	}

	scsCli := &scsClient{baseScs}

	instances, err := scsCli.ListInstances(&scs.ListInstancesArgs{})
	if err != nil {
		fmt.Println("list scs instances failed:", err)
		return err
	}

	for _, v := range instances.Instances {
		if v.InstanceName == "ccr-online" {
			fmt.Println("scs instance ccr-online already exist, skipped")
			return nil
		}
	}

	result, err := scsCli.CreateInstanceWithVIP(&CreateInstanceArgs{
		Billing: scs.Billing{
			PaymentTiming: "Postpaid",
		},
		ReplicationNum: 2,
		PurchaseCount:  1,
		InstanceName:   "ccr-online",
		NodeType:       "cache.n1.medium",
		ClusterType:    "master_slave",
		Port:           6379,
		ClientAuth:     "bdccr_123",
		Engine:         2,
		EngineVersion:  "4.0",
		AllocateVIP:    "1",
	})
	if err != nil {
		fmt.Println("create instace failed:", err)
		return err
	}

	fmt.Println(result)

	return nil
}

func createBosOrSkip() error {
	if bosEndpoint == "" {
		fmt.Println("no bos endpoint provided, skipped")
		return nil
	}

	bosCli, err := bos.NewClient(ak, sk, bosEndpoint)
	if err != nil {
		fmt.Println("create bos client failed: " + err.Error())
		return err
	}

	_, err = bosCli.PutBucket("registry-data")
	if err != nil {
		var bceErr *bce.BceServiceError
		if errors.As(err, &bceErr) && bceErr.StatusCode == http.StatusConflict {
			fmt.Println("bucket already exist, skipped")
			return nil
		}

		fmt.Println("put bucket failed: " + err.Error())
		return err
	}

	return err
}

func initDatabase() error {
	var (
		defUser     = "ccr"
		defPassword = "bdccr@123"
	)

	rdsCli, err := rds.NewClient(ak, sk, rdsEndpoint)
	if err != nil {
		fmt.Println("create rds client failed: ", err)
		return err
	}

	rdsInstances, err := rdsCli.ListRds(&rds.ListRdsArgs{})
	if err != nil {
		fmt.Println("list rds instance failed: ", err)
		return err
	}

	instanceID := ""
	for _, v := range rdsInstances.Instances {
		if v.InstanceName == "ccr-online" {
			instanceID = v.InstanceId
		}
	}

	if instanceID == "" {
		ins, err := rdsCli.CreateRds(&rds.CreateRdsArgs{
			ClientToken: uuid.New().String(),
			Billing: rds.Billing{
				PaymentTiming: "Postpaid",
			},
			PurchaseCount:  1,
			InstanceName:   "ccr-online",
			Engine:         "PostgreSQL",
			EngineVersion:  "10",
			Category:       "Standard",
			CpuCount:       2,
			MemoryCapacity: 4,
			VolumeCapacity: 100,
			IsDirectPay:    true,
			DiskIoType:     "normal_io", //磁盘类型, normal_io:本地盘ssd磁盘, cloud_high:高性能云磁盘, cloud_nor:通用型SSD, cloud_enha:增强型SSD, 必选
		})

		if err != nil {
			fmt.Println("create rds instance failed: ", err)
			return err
		}

		instanceID = ins.InstanceIds[0]
	}

	if pgHost == "" {
		fmt.Println("no postgres host provided, please create vip for rds ", instanceID)
		return fmt.Errorf("no postgres host provided, please create vip for rds %s", instanceID)
	}

	rdsAccountList, err := rdsCli.ListAccount(instanceID)
	if err != nil {
		fmt.Println("list instance account failed: ", err)
		return err
	}

	needCreateAccount := true
	for _, v := range rdsAccountList.Accounts {
		if v.AccountName == defUser {
			needCreateAccount = false
			fmt.Println("user already exist, skipped: ", v.AccountName)
			break
		}
	}

	if needCreateAccount {
		err = rdsCli.CreateAccount(instanceID, &rds.CreateAccountArgs{
			ClientToken: uuid.New().String(),
			AccountName: defUser,
			Password:    defPassword,
			AccountType: "Super",
			Type:        "OnlyMaster",
			Desc:        "created by ccr user",
		})

		if err != nil {
			fmt.Println("create rds account failed: ", err)
			return err
		}
	}

	connectStr := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		pgHost, pgPort, defUser, defPassword, "postgres")

	db, err := sql.Open("postgres", connectStr)
	if err != nil {
		fmt.Println("open database failed: " + err.Error())
		return err
	}
	defer func() {
		db.Close()
	}()

	createDbIfNotExist := func(dbname string) error {
		rows, err := db.Query("select datname from pg_database where datistemplate=false and datname=$1", dbname)
		if err != nil {
			fmt.Println("query db failed: ", err)
			return err
		}

		if rows.Next() {
			fmt.Println(dbname, " already exist, skipped")
			return nil
		}

		_, err = db.Exec("CREATE DATABASE " + dbname)
		if err != nil {
			fmt.Println("create db failed: ", err)
			return err
		}

		return nil
	}

	err = createDbIfNotExist("ccr")
	if err != nil {
		fmt.Println("create database ccr failed: ", err)
		return err
	}

	err = createDbIfNotExist("clair")
	if err != nil {
		fmt.Println("create database clair failed: ", err)
		return err
	}

	err = createDbIfNotExist("registry")
	if err != nil {
		fmt.Println("create database registry failed: ", err)
		return err
	}

	db.Close()
	// switch database
	connectStr = fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		pgHost, pgPort, defUser, defPassword, "ccr")

	db, err = sql.Open("postgres", connectStr)
	if err != nil {
		fmt.Println("open database failed: " + err.Error())
		return err
	}

	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS t_ccr_user_project (
		id serial NOT NULL,
		username varchar(255) NULL,
		owner bool NOT NULL DEFAULT false,
		creator bool NOT NULL DEFAULT false,
		project_id int4 NOT NULL,
		role int4 NOT NULL,
		creation_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		update_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		CONSTRAINT t_ccr_user_project_pkey PRIMARY KEY (id)
	);
	
	CREATE OR REPLACE FUNCTION update_update_time_at_column() RETURNS trigger
		LANGUAGE plpgsql
		AS $$
	  BEGIN
		NEW.update_time = NOW();
		RETURN NEW;
	  END;
	$$;
	
	DROP TRIGGER IF EXISTS t_ccr_user_project_at_update_time ON t_ccr_user_project;

	CREATE TRIGGER t_ccr_user_project_at_update_time BEFORE
	UPDATE
		ON
		t_ccr_user_project FOR EACH ROW EXECUTE PROCEDURE update_update_time_at_column();
		
	CREATE TABLE IF NOT EXISTS t_registry (
		id serial NOT NULL,
		username varchar(255) NULL,
		registry_id int4 NOT NULL,
		deleted bool NOT NULL DEFAULT false,
		creation_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		update_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		CONSTRAINT t_registry_pkey PRIMARY KEY (id)
	);
	
	DROP TRIGGER IF EXISTS t_registry_at_update_time ON t_registry;

	CREATE TRIGGER t_registry_at_update_time BEFORE
	UPDATE
		ON
		t_registry FOR EACH ROW EXECUTE PROCEDURE update_update_time_at_column();
		
	 CREATE TABLE IF NOT EXISTS t_policy (
		id serial NOT NULL,
		username varchar(255) NULL,
		policy_id int4 NOT NULL,
		deleted bool NOT NULL DEFAULT false,
		creation_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		update_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		CONSTRAINT t_policy_pkey PRIMARY KEY (id)
	);
	
	DROP TRIGGER IF EXISTS t_policy_at_update_time ON t_policy;
	CREATE TRIGGER t_policy_at_update_time BEFORE
	UPDATE
		ON
		t_policy FOR EACH ROW EXECUTE PROCEDURE update_update_time_at_column();
		
	CREATE TABLE IF NOT EXISTS t_user_activation (
		id serial NOT NULL,
		username varchar(255) NULL,
		creation_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		update_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		CONSTRAINT t_user_activation_pkey PRIMARY KEY (id)
	);
	
	DROP TRIGGER IF EXISTS t_user_activation_at_update_time ON t_user_activation;
	CREATE TRIGGER t_user_activation_at_update_time BEFORE
		UPDATE
		ON
			t_user_activation FOR EACH ROW EXECUTE PROCEDURE update_update_time_at_column();
	`)

	if err != nil {
		fmt.Println("init database ccr failed: ", err)
		return err
	}

	// switch database
	db.Close()
	// switch database
	connectStr = fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		pgHost, pgPort, defUser, defPassword, "registry")

	db, err = sql.Open("postgres", connectStr)
	if err != nil {
		fmt.Println("open database failed: " + err.Error())
		return err
	}

	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS t_temporary_token (
		id serial NOT NULL,
		username varchar(255) NULL,
		token varchar(40) NOT NULL,
		salt varchar(40) NULL DEFAULT NULL::character varying,
		expire_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		begin_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		update_time timestamp NULL DEFAULT CURRENT_TIMESTAMP,
		CONSTRAINT t_temporary_token_pkey PRIMARY KEY (id)
	);`)

	if err != nil {
		fmt.Println("init database registry failed: ", err)
		return err
	}

	return nil
}
