package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"path"
	"strings"

	"github.com/chartmuseum/storage"
	"github.com/ghodss/yaml"
	helm_repo "helm.sh/helm/v3/pkg/repo"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/src/core/api"
)

var (
	host string

	ak          string
	sk          string
	bucket      string
	bosEndpoint string
	bosPrefix   string

	namespacefile string
	username      string
	password      string
	region        string
)

func init() {
	flag.StringVar(&ak, "ak", "", "access key")
	flag.StringVar(&sk, "sk", "", "secret key")
	flag.StringVar(&bucket, "bucket", "", "bucket")
	flag.StringVar(&bosEndpoint, "bos-endpoint", "", "bos endpoint, like bj.bcebos.com")
	flag.StringVar(&bosPrefix, "bos-prefix", "", "bos prefix, like chartmuseum")

	flag.StringVar(&namespacefile, "namespace", "", "namespace need to go")
	flag.StringVar(&username, "username", "admin", "harbor username")
	flag.StringVar(&password, "password", "", "harbor password")
	flag.StringVar(&host, "host", "", "the host for harbor")
}

func main() {
	flag.Parse()

	nameStr, err := os.ReadFile(namespacefile)
	if err != nil {
		fmt.Printf("read file failed: %s\n", err)
		return
	}

	names := strings.Split(string(nameStr), ",")

	storage := api.NewStorageWithConfig(ak, sk, bosEndpoint, bucket, bosPrefix)
	chartCli := &ChartClient{
		host:     host,
		username: username,
		password: password,
	}

	for _, v := range names {
		err := migrateChart(storage, chartCli, v)
		if err != nil {
			fmt.Printf("migrate chart failed: %s\n", err)
		}
	}
}

func migrateChart(store storage.Backend, chartCli *ChartClient, namespace string) error {
	indexFile, err := chartCli.ListCharts(namespace)
	if err != nil {
		return err
	}

	fileMap := map[string]interface{}{}
	cnt := 0
	for _, versions := range indexFile.Entries {
		for _, ver := range versions {
			fileName := ver.Name + "-" + ver.Version + ".tgz"
			if _, ok := fileMap[fileName]; ok {
				fmt.Println(fileName, " already exist")
			}
			fileMap[fileName] = nil
			cnt++
		}
	}

	fmt.Printf("total %d/%d charts exists in %s\n", len(fileMap), cnt, namespace)

	objs, err := store.ListObjects(namespace + "/")
	if err != nil {
		fmt.Printf("list objects from bos failed: %s\n", err)
		return err
	}
	fmt.Printf("total %d charts need to migrate in %s\n", len(objs), namespace)

	for _, v := range objs {
		if !strings.HasSuffix(v.Path, "tgz") {
			continue
		}

		if _, ok := fileMap[v.Path]; ok {
			continue
		}

		fmt.Printf("migrate %s in %s\n", v.Path, namespace)
		o, err := store.GetObject(path.Join(namespace, v.Path))
		if err != nil {
			fmt.Printf("get object content failed: %s\n", err)
			return err
		}

		err = chartCli.PostCharts(namespace, o.Content)
		if err != nil {
			fmt.Printf("post chart failed: %s, skipping\n", err)
		}
	}

	return nil
}

type ChartClient struct {
	host     string
	username string
	password string
}

func (c *ChartClient) doReq(method, path string, body []byte, result interface{}) error {
	var (
		req *http.Request
		err error
	)

	if body != nil {
		req, err = http.NewRequest(method, fmt.Sprintf("http://%s%s", c.host, path), bytes.NewReader(body))
	} else {
		req, err = http.NewRequest(method, fmt.Sprintf("http://%s%s", c.host, path), nil)
	}

	if err != nil {
		fmt.Printf("create chart request failed: %s\n", err)
		return err
	}
	req.SetBasicAuth(username, password)
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		fmt.Printf("request %s failed: %s\n", path, err)
		return err
	}
	defer resp.Body.Close()

	content, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("read from body failed\n")
		return err
	}

	if resp.StatusCode > 300 {
		fmt.Printf("get chart response failed: %s, body: %s\n", resp.Status, string(content))
		return fmt.Errorf("status code is %s", resp.Status)
	}

	if result != nil {
		if resp.Header.Get("Content-Type") == "application/x-yaml" {
			return yaml.Unmarshal(content, result)
		}
		return json.Unmarshal(content, result)
	}
	return nil
}

func (c *ChartClient) ListCharts(namespace string) (*helm_repo.IndexFile, error) {
	indexFile := helm_repo.NewIndexFile()
	err := c.doReq(http.MethodGet, fmt.Sprintf("/chartrepo/%s/index.yaml", namespace), nil, indexFile)
	if err != nil {
		fmt.Printf("list charts failed: %s\n", err)
		return nil, err
	}

	return indexFile, nil
}

func (c *ChartClient) PostCharts(namespace string, body []byte) error {
	err := c.doReq(http.MethodPost, fmt.Sprintf("/api/chartrepo/%s/charts", namespace), body, nil)
	if err != nil {
		fmt.Printf("post charts failed: %s\n", err)
		return err
	}

	return nil
}
