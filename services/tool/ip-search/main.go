package main

import (
	"flag"
	"fmt"

	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/apimachinery/pkg/util/uuid"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

var (
	domain   string
	subnetId string

	serviceName     string
	servicePassword string
	serviceRole     string
	ak              string
	sk              string
	resourceUserID  string
	resourceName    string
	resourceHex     string

	stsEndpoint string
	iamEndpoint string
	dnsEndpoint string
	bccEndpoint string

	userId string
	maxOut int
)

func init() {
	flag.StringVar(&domain, "domain", "", "The domain name")
	flag.StringVar(&subnetId, "subnet-id", "", "The subnet ID")
	flag.StringVar(&serviceName, "service-name", "", "The service name")
	flag.StringVar(&servicePassword, "service-password", "", "The service password")
	flag.StringVar(&serviceRole, "service-role", "", "The service role")
	flag.StringVar(&ak, "ak", "", "The access key")
	flag.StringVar(&sk, "sk", "", "The secret key")
	flag.StringVar(&resourceUserID, "resource-user-id", "", "The resource user id")
	flag.StringVar(&resourceName, "resource-name", "ccr", "The resource name")
	flag.StringVar(&resourceHex, "resource-hex", "", "The resource hex")
	flag.StringVar(&userId, "userId", "", "The user ID")
	flag.StringVar(&stsEndpoint, "sts-endpoint", "", "The sts endpoint")
	flag.StringVar(&iamEndpoint, "iam-endpoint", "", "The iam endpoint")
	flag.StringVar(&dnsEndpoint, "dns-endpoint", "", "The dns endpoint")
	flag.StringVar(&bccEndpoint, "bcc-endpoint", "", "The bcc endpoint")
	flag.IntVar(&maxOut, "max-out", 10, "The max out count")
}

func main() {
	flag.Parse()

	fmt.Println("start analyze")

	dnsIPs, err := getAvailableDNSIP()
	if err != nil {
		fmt.Println("get dns ips failed: ", err)
		return
	}

	dnsIpSet := sets.NewString(dnsIPs...)

	fmt.Println("dns ip has been occupied: ", dnsIPs)

	subnetIPs, err := getAvailableSubnetIP()
	if err != nil {
		fmt.Println("get available subnet ips failed: ", err)
		return
	}

	cnt := 0
	for _, v := range subnetIPs {
		if !dnsIpSet.Has(v) {
			fmt.Println("ip is available: ", v)
			cnt++
		}

		if cnt > maxOut {
			return
		}
	}

}

func getAvailableDNSIP() ([]string, error) {
	dnsCli, err := dns.NewVpcDnsClient(ak, sk, dnsEndpoint)
	if err != nil {
		return nil, fmt.Errorf("get dns client failed: %s", err)
	}

	resp, err := dnsCli.ListVpcRecord(string(uuid.NewUUID()), resourceName, resourceHex, resourceUserID, "", domain, "", "", "1000")
	if err != nil {
		return nil, fmt.Errorf("list vpc dns record failed: %s", err)
	}

	ips := []string{}

	for _, v := range resp.Records {
		if v.Type == "A" {
			ips = append(ips, v.Value)
		}
	}

	return ips, nil
}

func getAvailableSubnetIP() ([]string, error) {
	stsCli, err := sts.NewClient(stsEndpoint, iamEndpoint, serviceName, servicePassword, serviceRole)
	if err != nil {
		return nil, fmt.Errorf("create sts client failed: %s", err)
	}

	cred, err := stsCli.AssumeRole(userId, userId)
	if err != nil {
		return nil, fmt.Errorf("assume role failed: %s", err)
	}

	vpcCli, err := vpc.NewClient(cred.AccessKeyId, cred.SecretAccessKey, bccEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create subnet client failed: %s", err)
	}
	vpcCli.GetBceClientConfig().Credentials.SessionToken = cred.SessionToken

	subnetDetail, err := vpcCli.GetSubnetDetail(subnetId)
	if err != nil {
		return nil, fmt.Errorf("get subnet detail failed: %s", err)
	}

	if subnetDetail.Subnet.AvailableIp < 1 {
		return nil, fmt.Errorf("no available ip")
	}

	ipMin, ipMax, err := utils.IPRangeFromCIDR(subnetDetail.Subnet.Cidr)
	if err != nil {
		return nil, fmt.Errorf("invalid cidr: %s", err)
	}

	fmt.Println("start from: ", utils.Uint32ToIP(ipMin))
	fmt.Println("stop from: ", utils.Uint32ToIP(ipMax))

	occupiedIps, err := vpcCli.GetOccupiedIPs(subnetId)
	if err != nil {
		return nil, fmt.Errorf("get occupied ip failed: %s", err)
	}

	occupiedIPSet := sets.NewString(occupiedIps...)

	availableIps := sets.NewString()
	for i := ipMin + 1; i < ipMax; i++ {
		if !occupiedIPSet.Has(utils.Uint32ToIP(i)) {
			availableIps.Insert(utils.Uint32ToIP(i))
		}
	}

	return availableIps.List(), nil
}
