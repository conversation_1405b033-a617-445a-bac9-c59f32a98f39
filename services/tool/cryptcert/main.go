package main

import (
	"fmt"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

func main() {
	plaintext := ``
	ciphertext, err := utils.AESCFBEncrypt(plaintext)
	if err != nil {
		panic(err)
	}
	fmt.Printf("ciphertext: \n%s\n", ciphertext)

	outPlaintext, err := utils.AESCFBDecrypt(ciphertext)
	if err != nil {
		panic(err)
	}
	fmt.Printf("outplaintext: \n%s\n", outPlaintext)

	if outPlaintext != plaintext {
		panic("decrypt error")
	}

}
