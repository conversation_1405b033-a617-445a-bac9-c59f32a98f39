package main

import (
	"flag"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/tool/perf/client"
)

var (
	username       string
	password       string
	image          string
	parallel       int
	suspendTime    int
	authEndpoint   string
	service        string
	logLevel       string
	followRedirect bool
	enableRetry    bool
)

// init init 初始化函数，用于设置命令行参数的默认值和解释信息。
// 参数：无
// 返回值：无
func init() {
	flag.StringVar(&username, "username", "", "username for pull iamge")
	flag.StringVar(&password, "password", "", "password for pull image")
	flag.StringVar(&image, "image", "", "image need to pull")
	flag.StringVar(&authEndpoint, "auth", "", "auth endpoint need to do auth")
	flag.StringVar(&service, "service", "", "service name used for auth")
	flag.StringVar(&logLevel, "log-level", "", "logLevel level")
	flag.IntVar(&parallel, "parallel", 1, "the number of pull at the same time")
	flag.IntVar(&suspendTime, "suspend-time", 5, "the time to suspend the pull")
	flag.BoolVar(&followRedirect, "redirect", false, "whether redirect directly")
	flag.BoolVar(&enableRetry, "enable-retry", false, "whether redirect directly")
}

// main main 函数是程序的入口，负责解析命令行参数、获取服务token、生成客户端并下载镜像。
// 如果没有指定authEndpoint和service，则通过GetAuthEndpointAndService方法获取；
// 如果service为空且endpoint以"ccr"开头，则将endpoint设置为service；
// 创建一个client.Client实例，使用用户名、密码、authEndpoint和service来获取token；
// 创建一个client.TokenClient实例，使用token和followRedirect来下载镜像；
// 启动多个goroutine，每个goroutine使用TokenClient来下载镜像，最后等待所有goroutine完成。
func main() {
	flag.Parse()

	switch logLevel {
	case "debug":
		logrus.SetLevel(logrus.DebugLevel)
	case "info":
		logrus.SetLevel(logrus.InfoLevel)
	case "warn":
		logrus.SetLevel(logrus.WarnLevel)
	case "error":
		logrus.SetLevel(logrus.ErrorLevel)
	default:
		logrus.SetLevel(logrus.InfoLevel)
	}
	logrus.SetFormatter(&logrus.TextFormatter{DisableColors: true})

	img, err := client.ParseImage(image)
	if err != nil {
		panic("image " + image + " is not valid: " + err.Error())
	}
	// 获取service token 地址和service
	if authEndpoint == "" && service == "" {
		authEndpoint, service, err = GetAuthEndpointAndService(img.Endpoint)
		if err != nil {
			panic("get auth endpoint " + image + " is not valid: " + err.Error())
		}
	}

	if service == "" && strings.HasPrefix(img.Endpoint, "ccr") {
		service = img.Endpoint[:12]
	}

	cli := client.NewAuthClient(username, password, authEndpoint)

	tk, err := cli.GetToken(service, &img)
	if err != nil {
		panic("get token failed " + err.Error())
	}

	logrus.Debugf("Token: %s", ">>>>>>")
	logrus.Debugf("%s", tk.Token)
	logrus.Debugf("Token: %s", "<<<<<<")

	retryPolicy := client.RetryPolicy{
		MaxRetries:    3,
		InitialDelay:  5 * time.Second,
		BackoffFactor: 5,
		RetryEnabled:  enableRetry,
	}

	result := &client.Result{
		StatusCodeCount: make(map[int]int),
		Durations:       make([]time.Duration, 0),
	}

	imageClient := client.NewImageClient(tk.Token, followRedirect, retryPolicy, result)

	logrus.Debugf("start get image with redirect: %t", followRedirect)

	monitorInterval := 1 * time.Second
	go func() {
		for range time.Tick(monitorInterval) {
			imageClient.Result.RequestCounts = append(imageClient.Result.RequestCounts, imageClient.Result.Count)
		}
	}()

	wg := sync.WaitGroup{}
	wg.Add(parallel)
	for i := 0; i < parallel; i++ {
		go func() {
			defer wg.Done()
			e := imageClient.GetImage(&img)
			if e != nil {
				logrus.Errorf("get image failed: %s", e)
			}
		}()
		if i > 0 && i%500 == 0 {
			time.Sleep(time.Duration(suspendTime) * time.Second)
			logrus.Infof("suspend %d seconds", suspendTime)
		}
	}
	wg.Wait()

	printResults(result)

}

func printResults(result *client.Result) {
	sort.Slice(result.Durations, func(i, j int) bool {
		return result.Durations[i] < result.Durations[j]
	})

	p50 := percentile(result.Durations, 50)
	p70 := percentile(result.Durations, 70)
	p90 := percentile(result.Durations, 90)

	//fmt.Printf("Concurrent Users: %d\n", config.ConcurrentUsers)
	totalRequests := 0
	failCount := 0
	for statusCode, count := range result.StatusCodeCount {
		totalRequests += count
		if statusCode >= 500 {
			failCount += count
		}
		logrus.Infof("Status Code %d: %d", statusCode, count)
	}
	logrus.Infof("Total Requests: %d", totalRequests)
	logrus.Infof("Total Failures: %d", failCount)
	failureRate := calculateFailureRate(totalRequests, failCount)
	logrus.Infof("Failure Rate : %.2f%%", failureRate*100)

	logrus.Infof("P50 Latency: %s", p50)
	logrus.Infof("P70 Latency: %s", p70)
	logrus.Infof("P90 Latency: %s", p90)
	logrus.Infof("Fastest response time: %s", result.Durations[0])
	logrus.Infof("Slowest response time: %s", result.Durations[len(result.Durations)-1])
	logrus.Infof("Total number of requests per second interval: %#v", result.RequestCounts)
	logrus.Infof("QPS per second: %#v", calculateDifferences(result.RequestCounts))

	logrus.Infof("Max QPS per second: %d", findMax(calculateDifferences(result.RequestCounts)))

	logrus.Infof("P90 QPS per second: %.2f", float64(totalRequests)/p90.Seconds())
	logrus.Infof("Average QPS per second: %.2f", float64(totalRequests)/result.Durations[len(result.Durations)-1].Seconds())

}

// FindMax 返回数组中的最大值
func findMax(arr []int) int {
	if len(arr) == 0 {
		// 如果数组为空，则无最大值，这里可以返回一个错误或者特定的值，取决于你的业务逻辑
		panic("不能从空数组中查找最大值")
	}

	max := arr[0] // 假设第一个元素是最大的
	for _, value := range arr {
		if value > max {
			max = value // 如果发现更大的值，则更新最大值
		}
	}
	return max // 返回找到的最大值
}

// CalculateDifferences 计算给定整数切片中每对相邻元素的增长量。
func calculateDifferences(data []int) []int {
	if len(data) < 2 {
		// 如果数组中的元素少于两个，无法计算增长量，直接返回空切片
		return []int{}
	}

	// 创建一个新的切片来存储增长量，长度比输入切片少一个，因为它是基于相邻元素对计算的
	differences := make([]int, len(data)-1)

	// 遍历输入切片，计算相邻元素的差值
	for i := 1; i < len(data); i++ {
		differences[i-1] = data[i] - data[i-1]
	}

	return differences
}

// 计算失败率的函数
// totalCycles 表示总的洗衣次数
// failures 表示故障次数
// 返回值是故障率，它是一个 [0, 1] 范围内的浮点数
func calculateFailureRate(totalRequests, failCount int) float64 {
	if totalRequests == 0 {
		// 防止除以零
		return 0
	}
	// 计算故障率，并确保返回的是浮点数
	failureRate := float64(failCount) / float64(totalRequests)
	return failureRate
}

func percentile(durations []time.Duration, p int) time.Duration {
	index := (p * len(durations)) / 100
	if index == 0 {
		return durations[0]
	}
	if index >= len(durations) {
		return durations[len(durations)-1]
	}
	return durations[index-1]
}

func GetAuthEndpointAndService(imageEndpoint string) (string, string, error) {
	uri := fmt.Sprintf("https://%s/v2/", imageEndpoint)

	req, err := http.NewRequest(http.MethodGet, uri, nil)
	if err != nil {
		return "", "", err
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		wwwAuthenticate := resp.Header.Get("WWW-Authenticate")
		if wwwAuthenticate == "" {
			return "", "", fmt.Errorf("no WWW-Authenticate header")
		}
		realmStr, serviceStr, ok := strings.Cut(wwwAuthenticate, ",")
		if !ok {
			return "", "", fmt.Errorf("no Bearer realm and service in WWW-Authenticate header")
		}
		_, realmValue, ok := strings.Cut(realmStr, "=")
		if !ok {
			return "", "", fmt.Errorf("no Bearer realm WWW-Authenticate header")
		}

		_, serviceValue, ok := strings.Cut(serviceStr, "=")
		if !ok {
			return "", "", fmt.Errorf("no Service in WWW-Authenticate header")
		}

		return strings.Trim(realmValue, `"`), strings.Trim(serviceValue, `"`), nil
	}

	return "", "", fmt.Errorf("unexpected status code %d", resp.StatusCode)
}
