# 镜像推送 QPS 测试工具
1、测试镜像制作脚本，imagebuild.sh，实现自动制作镜像并推送至仓库

前置条件：docker login 登录仓库

使用方式，例：
```shell
# 制作10层镜像，每层5M
sh imagebuild.sh 5 10 ccr-1xxgzreg-pub.cnc.gz.baidubce.com/pressure/test:5m10l

# 制作20层镜像，每层50M
sh imagebuild.sh 50 20 ccr-1xxgzreg-pub.cnc.gz.baidubce.com/pressure/test:50m20l

# 制作10层镜像，每层大小呈斐波那契数列规律，首层大小为50M (层大小：50 50 100 150 250 400 650 1050 1700)
sh imagebuild.sh 50 10 ccr-1xxgzreg-pub.cnc.gz.baidubce.com/pressure/test:50m10l true
```


批量生成测试镜像，例：
```shell
#!/bin/bash

# Initialize starting value for x and y
start_value=5

# Initialize ending value for x and y
end_value=6

# Generate a random 3-letter string using /dev/urandom
random_str=$(tr -dc 'a-z' < /dev/urandom | fold -w 3 | head -n 1)

# Define the base of the image name, appending the random string to 'test'
image_base="ccr-1xgztest-vpc.cnc.gz.baidubce.com/library/test${random_str}"

# Loop over x values from start_value to end_value
for (( x=$start_value; x<=$end_value; x++ )); do
    # Nested loop over y values from start_value to end_value
    for (( y=$start_value; y<=$end_value; y++ )); do
        # Compose the image tag based on the current x and y values
        image_tag="${x}m${y}l"
        # Create the full image name by appending the tag to the base
        image_name="${image_base}:${image_tag}"

        # Call the image build script with the current x, y, and image name
        sh imagebuild.sh $x $y $image_name

        # The script is expected to build a Docker image with the provided name
        # and tag, using the x and y values for any custom logic within.
    done
done

# Ensure that the `imagebuild.sh` script is executable and available in the PATH.
```
2、测试镜像推送脚本，imagepush.sh，实现自动推送镜像至仓库
```shell
chmod +x imagebuild.sh
```
