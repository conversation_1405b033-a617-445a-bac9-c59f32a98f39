package client

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/sirupsen/logrus"
)

type AuthClient struct {
	Username string
	Password string
	Endpoint string
}

// GetToken 从 Docker Hub 获取 Token，用于验证和推送镜像。
//
// 参数：
//   - service (string) - 服务名称，例如 "registry.docker.io"。
//   - image (Image) - 包含镜像信息的结构体指针，包括 Repository（仓库名）、Name（镜像名）等。
//
// 返回值：
//   - (*Token, error) - Token 类型的指针和错误信息，当请求失败时会返回错误。
//   - Token 包含了 Docker Hub 返回的 Token 信息，可以使用该 Token 进行镜像的验证和推送操作。

func NewAuthClient(username, password, endpoint string) *AuthClient {
	return &AuthClient{Username: username, Password: password, Endpoint: endpoint}
}

func (c *AuthClient) GetToken(service string, image *Image) (*Token, error) {
	endpoint := strings.TrimSpace(c.Endpoint)

	endpointURL, err := url.Parse(endpoint)
	if err != nil {
		logrus.Errorf("Error parsing URL: %s", err)
		return nil, err
	}
	if endpointURL.Scheme != "https" {
		endpointURL.Scheme = "https"
	}

	queryParams := endpointURL.Query()                                                                // 获取现有的查询参数
	queryParams.Set("service", service)                                                               // 设置新的查询参数
	queryParams.Set("scope", fmt.Sprintf("repository:%s/%s:pull,push", image.Repository, image.Name)) // 再设置一个查询参数
	endpointURL.RawQuery = queryParams.Encode()

	logrus.Infof("auth endpoint: \n%s", endpointURL.String()) // 更新 URL 对象的 RawQuery 字段

	req, err := http.NewRequest(http.MethodGet, endpointURL.String(), nil)
	if err != nil {
		return nil, err
	}

	req.SetBasicAuth(c.Username, c.Password)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("return status code is: %v when get token", resp.StatusCode)
	}

	var tk Token
	err = json.NewDecoder(resp.Body).Decode(&tk)
	return &tk, err
}
