package client

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

type ImageClient struct {
	Token       string
	HttpCli     *http.Client
	RetryPolicy RetryPolicy
	Result      *Result
}

// Result 结构用于记录压测结果，包括每个状态码出现的次数和请求的延时
type Result struct {
	StatusCodeCount map[int]int
	//FailCount       int
	Durations     []time.Duration
	Mu            sync.Mutex
	RequestCounts []int
	Count         int
}

type RetryPolicy struct {
	MaxRetries    int           // 最大重试次数
	InitialDelay  time.Duration // 初始重试延迟
	BackoffFactor float64       // 退避因子，用于递增延迟时间
	RetryEnabled  bool
}

func NewImageClient(token string, enableRedirect bool, retryPolicy RetryPolicy, result *Result) *ImageClient {
	var httpCli *http.Client
	if enableRedirect {
		httpCli = http.DefaultClient
	} else {
		httpCli = &http.Client{
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				return http.ErrUseLastResponse
			},
		}
	}
	return &ImageClient{
		Token:       token,
		HttpCli:     httpCli,
		RetryPolicy: retryPolicy,
		Result:      result,
	}
}

func (c *ImageClient) GetManifest(image *Image) (*Manifest, error) {
	uri := fmt.Sprintf("https://%s/v2/%s/%s/manifests/%s", image.Endpoint, image.Repository, image.Name, image.Tag)
	logrus.Debugf("manifest uri: %s", uri)
	req, err := http.NewRequest(http.MethodGet, uri, nil)
	if err != nil {
		return nil, fmt.Errorf("new request failed: %v", err)
	}
	req.Header.Set("Authorization", "Bearer "+c.Token)
	var manifest Manifest
	err = c.doWithRetries(req, &manifest)
	return &manifest, err
}

func (c *ImageClient) GetLayer(image *Image, digest string) error {
	// 输出单层耗时
	startTime := time.Now()
	defer func() {
		duration := timeCost(startTime)
		//c.Result.Mu.Lock()
		c.Result.Durations = append(c.Result.Durations, duration)
		//c.Result.Mu.Unlock()
	}()

	uri := fmt.Sprintf("https://%s/v2/%s/%s/blobs/%s", image.Endpoint, image.Repository, image.Name, digest)
	logrus.Debugf("blob uri: %s", uri)

	req, err := http.NewRequest(http.MethodGet, uri, nil)
	if err != nil {
		return err
	}
	req.Header.Set("Authorization", "Bearer "+c.Token)

	return c.doWithRetries(req, nil)
}

func (c *ImageClient) doWithRetries(req *http.Request, body interface{}) error {

	maxRetries := 1
	if c.RetryPolicy.RetryEnabled {
		maxRetries = c.RetryPolicy.MaxRetries
	}

	for retryCount := 0; retryCount < maxRetries; retryCount++ {
		logrus.Debugf("retry count: %d", retryCount)

		resp, err := c.HttpCli.Do(req)
		defer func() {
			if resp != nil && resp.Body != nil {
				resp.Body.Close()
			}
		}()

		c.Result.Mu.Lock()
		if resp != nil && resp.StatusCode != 0 {
			c.Result.StatusCodeCount[resp.StatusCode]++
		}
		if err != nil && resp == nil {
			c.Result.StatusCodeCount[500]++
		}
		c.Result.Mu.Unlock()

		if err != nil {
			return fmt.Errorf("do request failed: %v", err)
		}

		if resp.StatusCode >= 200 && resp.StatusCode <= 299 {
			if body != nil {
				//bodyBytes, err := io.ReadAll(resp.Body)
				//if err != nil {
				//	return err
				//}
				//fmt.Printf("response body: %v\n", string(bodyBytes))

				return json.NewDecoder(resp.Body).Decode(body)
			}

			// 丢弃body中数据
			_, err = io.CopyBuffer(io.Discard, resp.Body, nil)
			if err != nil {
				return fmt.Errorf("io.copy failed: %s", err.Error())
			}
		}

		if !c.RetryPolicy.RetryEnabled {
			break
		}

		delay := c.RetryPolicy.InitialDelay + time.Duration(float64(retryCount)*c.RetryPolicy.BackoffFactor)
		time.Sleep(delay)
	}

	return nil
}

func timeCost(start time.Time) time.Duration {
	tc := time.Since(start)
	logrus.Debugf("layer time cost = %v", tc)
	return tc
}

func (c *ImageClient) GetImage(img *Image) error {
	manifest, err := c.GetManifest(img)
	if err != nil {
		return fmt.Errorf("get manifest failed: %v", err)
	}

	if manifest.SchemaVersion == 1 {
		wg := sync.WaitGroup{}
		wg.Add(len(manifest.FSLayers))

		for _, v := range manifest.FSLayers {

			go func(digest string) {
				defer wg.Done()
				e := c.GetLayer(img, digest)
				if e != nil {
					logrus.Errorf("get layer failed: %s", e)
				}
				c.Result.Count++

			}(v.BlobSum)
		}

		wg.Wait()
	}
	if manifest.SchemaVersion == 2 {
		wg := sync.WaitGroup{}
		wg.Add(len(manifest.Layers))
		for _, v := range manifest.Layers {
			go func(digest string) {
				defer wg.Done()
				e := c.GetLayer(img, digest)
				if e != nil {
					logrus.Errorf("get layer failed: %s", e)
				}
				c.Result.Count++
			}(v.Digest)
		}
		wg.Wait()
	}

	return nil
}
