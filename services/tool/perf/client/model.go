package client

import (
	"fmt"
	"strings"
)

type T struct {
	SchemaVersion int    `json:"schemaVersion"`
	Name          string `json:"name"`
	Tag           string `json:"tag"`
	Architecture  string `json:"architecture"`
	FsLayers      []struct {
		BlobSum string `json:"blobSum"`
	} `json:"fsLayers"`
	History []struct {
		V1Compatibility string `json:"v1Compatibility"`
	} `json:"history"`
	Signatures []struct {
		Header struct {
			Jwk struct {
				Crv string `json:"crv"`
				Kid string `json:"kid"`
				Kty string `json:"kty"`
				X   string `json:"x"`
				Y   string `json:"y"`
			} `json:"jwk"`
			Alg string `json:"alg"`
		} `json:"header"`
		Signature string `json:"signature"`
		Protected string `json:"protected"`
	} `json:"signatures"`
}

type FSLayer struct {
	BlobSum string `json:"blobSum"`
}

type Manifest struct {
	SchemaVersion int              `json:"schemaVersion"`
	MediaType     string           `json:"mediaType"`
	Config        ManifestConfig   `json:"config"`
	Layers        []ManifestLayers `json:"layers"`
	FSLayers      []FSLayer        `json:"fsLayers"`
}

type ManifestConfig struct {
	MediaType string `json:"mediaType"`
	Size      int    `json:"size"`
	Digest    string `json:"digest"`
}

type ManifestLayers struct {
	MediaType string `json:"mediaType"`
	Size      int    `json:"size"`
	Digest    string `json:"digest"`
}

type Token struct {
	Token       string `json:"token"`
	AccessToken string `json:"access_token"` // the token returned by azure container registry is called "access_token"
	ExpiresIn   int    `json:"expires_in"`
	IssuedAt    string `json:"issued_at"`
}

type Image struct {
	Endpoint   string
	Repository string
	Name       string
	Tag        string
}

// xxx.com/xxxx/xxxx:xxx
func ParseImage(image string) (Image, error) {
	parts := strings.SplitN(image, "/", 2)
	if len(parts) != 2 {
		return Image{}, fmt.Errorf("invalid image format")
	}

	ep := parts[0]

	repoNameTags := strings.Split(parts[1], ":")
	if len(repoNameTags) != 2 {
		return Image{}, fmt.Errorf("invalid image format")
	}

	tag := repoNameTags[1]

	repoName := strings.SplitN(repoNameTags[0], "/", 2)
	if len(repoName) != 2 {
		return Image{}, fmt.Errorf("invalid image format")
	}

	return Image{
		Endpoint:   ep,
		Repository: repoName[0],
		Name:       repoName[1],
		Tag:        tag,
	}, nil
}
