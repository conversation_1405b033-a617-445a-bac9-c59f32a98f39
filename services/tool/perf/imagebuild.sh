#! /bin/sh

#prework docker login

size=$1
layer=$2
imageID=$3
fib=$4

touch Dockerfile
echo 'From alpine:3' > ./Dockerfile

if [ $layer -lt 2 ]
then
  echo 'there is no other layer'
elif [ $fib ]; then
    first=$size
    second=$first
    if [ $layer -lt 4 ]; then
      for i in $(seq 2 $layer)
      do
        echo 'RUN dd if=/dev/urandom of=test'$i' bs=1M count='$size >> ./Dockerfile;
      done
    else
      echo 'RUN dd if=/dev/urandom of=test'$i' bs=1M count='$size >> ./Dockerfile;
      echo 'RUN dd if=/dev/urandom of=test'$i' bs=1M count='$size >> ./Dockerfile;
      for i in $(seq 4 $layer)
      do
        last=$(($first+$second))
        echo 'RUN dd if=/dev/urandom of=test'$i' bs=1M count='$last >> ./Dockerfile;
        first=$second
        second=$last
      done
    fi
else
  for i in $(seq 2 $layer)
  do
     echo 'RUN dd if=/dev/urandom of=test'$i' bs=1M count='$size >> ./Dockerfile;
  done
fi

docker build -f Dockerfile --platform amd64 -t $imageID .  --no-cache
docker push $imageID
docker rmi $imageID

rm -rf Dockerfile