
GITCOMMITLOG := $(shell git log --pretty='format:%h' -n 1)
DATE := $(shell date +%Y%m%d%H%M)

build:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o receiver .

docker: build
	docker build --platform=linux/amd64 --no-cache -t registry.baidubce.com/ccr-image/receiver-tools:$(GITCOMMITLOG)-$(DATE) .

push: docker
	docker push registry.baidubce.com/ccr-image/receiver-tools:$(GITCOMMITLOG)-$(DATE)



