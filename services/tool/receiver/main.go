package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"text/template"
)

const (
	// SlackBodyTemplate defines Slack request body template
	SlackBodyTemplate = `{
	"blocks": [
		{
            "type": "section",
			"text": {
				"type": "mrkdwn",
				"text": "*Harbor webhook events*"
			}
        },
        {
            "type": "section",
			"text": {
				"type": "mrkdwn",
				"text": "*event_type:* {{.Type}}"
			}
        },
        {
            "type": "section",
			"text": {
				"type": "mrkdwn",
				"text": "*occur_at:* <!date^{{.OccurAt}}^{date} at {time}|February 18th, 2014 at 6:39 AM PST>"
			}
        },
        {	"type": "section",
			"text": {
				"type": "mrkdwn",
				"text": "*operator:* {{.Operator}}"
			}
		},
        {	"type": "section",
			"text": {
				"type": "mrkdwn",
				"text": "*event_data:*"
			}
		},
		{	"type": "section",
			"text": {
				"type": "mrkdwn",
				"text": "{{.EventData}}"
			}
		}
    ]}`
)

// Payload of notification event
type Payload struct {
	Type      string     `json:"type"`
	OccurAt   int64      `json:"occur_at"`
	Operator  string     `json:"operator"`
	EventData *EventData `json:"event_data,omitempty"`
}

// EventData of notification event payload
type EventData struct {
	Resources  []*Resource       `json:"resources,omitempty"`
	Repository *Repository       `json:"repository,omitempty"`
	Custom     map[string]string `json:"custom_attributes,omitempty"`
}

// Resource describe infos of resource triggered notification
type Resource struct {
	Digest       string                 `json:"digest,omitempty"`
	Tag          string                 `json:"tag,omitempty"`
	ResourceURL  string                 `json:"resource_url,omitempty"`
	ScanOverview map[string]interface{} `json:"scan_overview,omitempty"`
}

// Repository info of notification event
type Repository struct {
	DateCreated  int64  `json:"date_created,omitempty"`
	Name         string `json:"name"`
	Namespace    string `json:"namespace"`
	RepoFullName string `json:"repo_full_name"`
	RepoType     string `json:"repo_type"`
}

func main() {
	log.Printf("Receiver Server started. Listening on 0.0.0.0:8080\n")

	http.HandleFunc("/receive", receiverHandler)
	http.HandleFunc("/ping", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "pong")
	})

	http.ListenAndServe(":8080", nil)
}

func receiverHandler(w http.ResponseWriter, r *http.Request) {
	log.Println("receiver message...")
	username, password, ok := r.BasicAuth()
	if !ok {

		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}
	if username != "anyone" || password != "password" {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	if err := sendSlackRobot(r); err != nil {
		log.Printf("send slack message failed: %s", err)
	}

	w.Header().Set("Content-Type", "application/json")
	result := make(map[string]string)
	result["code"] = "ok"
	result["message"] = "The result is OK"

	json.NewEncoder(w).Encode(result)
}

func sendSlackRobot(r *http.Request) error {

	payload := &Payload{}
	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Printf("decode payload failed: %s", err)
		return err
	}

	text, err := convert(payload)
	if err != nil {
		log.Printf("convert payload to slack body failed: %s", err)
		return err
	}

	if err := execute(text); err != nil {
		log.Printf("execute slace failed: %s", err)
		return err
	}
	return nil
}

func convert(payLoad *Payload) (string, error) {
	data := make(map[string]interface{})
	data["Type"] = payLoad.Type
	data["OccurAt"] = payLoad.OccurAt
	data["Operator"] = payLoad.Operator
	eventData, err := json.MarshalIndent(payLoad.EventData, "", "\t")
	if err != nil {
		return "", fmt.Errorf("marshal from eventData %v failed: %v", payLoad.EventData, err)
	}
	data["EventData"] = "```" + strings.Replace(string(eventData), `"`, `\"`, -1) + "```"

	st, _ := template.New("slack").Parse(SlackBodyTemplate)
	var slackBuf bytes.Buffer
	if err := st.Execute(&slackBuf, data); err != nil {
		return "", fmt.Errorf("%v", err)
	}
	return slackBuf.String(), nil
}

// execute slack job
func execute(body string) error {
	address := "*********************************************************************************"
	req, err := http.NewRequest(http.MethodPost, address, bytes.NewReader([]byte(body)))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("slack job(target: %s) response code is %d", address, resp.StatusCode)
	}
	return nil
}
