package main

import (
	"flag"
	"fmt"

	"k8s.io/apimachinery/pkg/util/uuid"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bus"
)

var (
	clear    bool
	register bool

	host string
	typ  string

	ak       string
	sk       string
	region   string
	endpoint string
)

func init() {
	flag.BoolVar(&clear, "clear", false, "clear exist register service")
	flag.BoolVar(&register, "register", false, "register service")

	flag.StringVar(&ak, "ak", "", "access key")
	flag.StringVar(&sk, "sk", "", "secret key")
	flag.StringVar(&region, "region", "", "region")
	flag.StringVar(&endpoint, "endpoint", "", "the endpoint for register server")
	flag.StringVar(&host, "host", "", "the host for register server")
	flag.StringVar(&typ, "type", "", "the type for register server")
}

// ./register-server --ak xxx --sk xxx --endpoint *************:28804 --host *************:28804  --clear  --register --region "gz"
// sandbox: http://register.internal-qasandbox.baidu-int.com:8985 online: http://register.bce-console.sdns.baidu.com canary: *************:28804
func main() {
	flag.Parse()
	reqId := string(uuid.NewUUID())
	cli, err := bus.NewClient(ak, sk, host)
	if err != nil {
		err = fmt.Errorf("create bus client failed: %w", err)
		return
	}

	// DELETE service
	if clear {
		ds := &bus.DeleteServiceRequest{
			Endpoint:  endpoint,
			Region:    region,
			Type:      typ,
			ServiceID: "CCR",
		}

		fmt.Printf("clear register service: endpoint:%s, region: %s\n", ds.Endpoint, ds.Region)
		if err := cli.DeleteService(reqId, ds); err != nil {
			fmt.Printf("delete service failed: %s \n", err)
			return
		}
	}

	if register {
		rs := &bus.RegisterServiceRequest{
			Endpoint:  endpoint,
			Region:    region,
			Type:      typ,
			ServiceID: "CCR",
		}

		if typ == "tag" {
			rs.Configuration = bus.RegisterConfig{
				TagPath: "/v1/tag/resources", // TODO 替换为配置文件和标签的路径
			}
		}

		fmt.Printf("register service: endpoint:%s, region: %s\n", rs.Endpoint, rs.Region)

		if err := cli.RegisterService(reqId, rs); err != nil {
			fmt.Printf("register  service failed: %s \n", err)
			return
		}
	}

}
