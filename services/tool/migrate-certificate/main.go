package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

var (
	kubeconfig string
	scheme     = runtime.NewScheme()

	// 替换证书信息
	certCrt string
	certKey string
)

// init 函数用于注册自定义资源组和其对应的 API 对象到 k8s Scheme 中
func init() {
	flag.StringVar(&kubeconfig, "kubeconfig", "~/.kube/config", "kubeconfig file path")
	flag.StringVar(&certCrt, "certCrt", "~/ccr/cert2024/tls.crt", "cert crt file path")
	flag.StringVar(&certKey, "certKey", "~/ccr/cert2024/tls.key", "cert key file path")

	utilruntime.Must(clientgoscheme.AddToScheme(scheme))

	utilruntime.Must(ccrv1alpha1.AddToScheme(scheme))
}

// main 函数是程序的入口函数
func main() {
	flag.Parse()

	kubedata, err := os.ReadFile(kubeconfig)
	if err != nil {
		fmt.Println("read file failed: ", err)
		return
	}

	cli, err := utils.NewK8sClient(string(kubedata), scheme)
	if err != nil {
		fmt.Println("new k8s client failed: ", err)
		return
	}

	var ccrList ccrv1alpha1.CCRList
	err = cli.List(context.Background(), &ccrList)
	if err != nil {
		fmt.Println("list ccr failed")
		return
	}

	for _, item := range ccrList.Items {
		if err = migrateDomainCertificate(cli, item.Name, "domain-cert"); err != nil {
			fmt.Println("migrate domain cert failed: ", err)
			return
		}
	}

	if err = migrateCertificate(cli, "kube-system", "ccr-controller"); err != nil {
		fmt.Println("migrate cert failed: ", err)
		return
	}

	if err = patchCCRController(cli, "kube-system", "ccr-controller"); err != nil {
		fmt.Println("patch ccr controller failed: ", err)
		return
	}
}

func patchCCRController(cli client.Client, namespace string, deployName string) error {
	fmt.Printf("patch ccr controller for %s/%s\n", namespace, deployName)
	var deploy v1.Deployment
	err := cli.Get(context.Background(), client.ObjectKey{Namespace: namespace, Name: deployName}, &deploy)
	if err != nil {
		fmt.Printf("get deploy failed: %s\n", err)
		return err
	}

	podAnnotations := deploy.Spec.Template.Annotations
	podAnnotations["kubernetes.io/cert"] = "2024"

	err = cli.Update(context.Background(), &deploy)
	if err != nil {
		fmt.Println("update deploy failed: ", err)
	}
	fmt.Printf("patch ccr controller for %s/%s success \n", namespace, deployName)

	return nil
}

// migrateDomainCertificate 更新指定命名空间下的secret的证书信息
// 参数：
//
//	cli client.Client - 客户端实例
//	namespace string - 目标命名空间名称
//	secretName string - 目标secret名
//
// 返回值：
//
//	error - 错误信息，如果更新成功则返回nil
func migrateDomainCertificate(cli client.Client, namespace, secretName string) error {
	fmt.Printf("migrate certificate for %s/%s\n", namespace, secretName)
	var sc corev1.Secret
	err := cli.Get(context.Background(), client.ObjectKey{Namespace: namespace, Name: secretName}, &sc)
	if err != nil {
		fmt.Printf("get secret failed: %s\n", err)
		return err
	}

	keyData, err := os.ReadFile(certKey)
	if err != nil {
		fmt.Println("read file failed: ", err)
		return err
	}
	key, err := utils.AESCFBDecrypt(string(keyData))
	if err != nil {
		fmt.Println("invalid key: ", err)
		return err
	}

	crtData, err := os.ReadFile(certCrt)
	if err != nil {
		fmt.Println("read file failed: ", err)
		return err
	}
	crt, err := utils.AESCFBDecrypt(string(crtData))
	if err != nil {
		fmt.Println("invalid cert: ", err)
		return err
	}

	if len(sc.Data["tls.crt"]) != 0 {
		sc.Data["tls.crt"] = []byte(crt)
	}

	if len(sc.Data["tls.key"]) != 0 {
		sc.Data["tls.key"] = []byte(key)
	}

	err = cli.Update(context.Background(), &sc)
	if err != nil {
		fmt.Println("update secret failed: ", err)
	}
	fmt.Printf("migrate certificate for %s/%s success \n", namespace, secretName)

	return nil
}

func migrateCertificate(cli client.Client, namespace, secretName string) error {
	fmt.Printf("migrate certificate for %s/%s\n", namespace, secretName)
	var sc corev1.Secret
	err := cli.Get(context.Background(), client.ObjectKey{Namespace: namespace, Name: secretName}, &sc)
	if err != nil {
		fmt.Printf("get secret failed: %s\n", err)
		return err
	}

	crtData, err := os.ReadFile(certCrt)
	if err != nil {
		fmt.Println("read file failed: ", err)
		return err
	}

	keyData, err := os.ReadFile(certKey)
	if err != nil {
		fmt.Println("read file failed: ", err)
		return err
	}

	if len(sc.Data["domain.crt"]) != 0 {
		sc.Data["domain.crt"] = crtData
	}

	if len(sc.Data["domain.key"]) != 0 {
		sc.Data["domain.key"] = keyData
	}

	err = cli.Update(context.Background(), &sc)
	if err != nil {
		fmt.Println("update secret failed: ", err)
	}
	fmt.Printf("migrate certificate for %s/%s success \n", namespace, secretName)

	return nil
}
