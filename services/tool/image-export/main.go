package main

import (
	"flag"
	"fmt"
	"strings"

	"github.com/baidubce/bce-sdk-go/services/bos"
)

var (
	image  string
	region string
	bucket string
	ak     string
	sk     string
)

// init 函数用于设置命令行参数
func init() {
	flag.StringVar(&image, "image", "", "required: image file absolute path.")
	flag.StringVar(&region, "region", "", "optional: region of bos,values is:bj,bd,gz,su,fwh,hkg,cd,yq")
	flag.StringVar(&bucket, "bucket", "", "optional: bos bucket name.")
	flag.StringVar(&ak, "ak", "", "optional: access key.")
	flag.StringVar(&sk, "sk", "", "optional: secret key.")
}

// 将用户镜像导出上传到bos
func main() {
	flag.Parse()

	if image == "" {
		panic("image is empty,please input image file absolute path")
	}

	if region == "" {
		//如果未指定地域，设置默认gz
		region = "gz"
	}

	if region != "gz" && bucket == "" {
		//未指定bucket，且不是gz上传会报错
		panic("region or bucket invalid,please input correct value.")
	}

	if region == "gz" && bucket == "" {
		//未指定bucket，使用gz默认的bucket
		bucket = "ccr-1axaxax1"
	}

	// 上传的文件在bos保存路径
	path := "image-load/"
	bosEndpoint := "gz.bcebos.com"

	switch region {
	case "bj":
		bosEndpoint = "bj.bcebos.com"
	case "bd":
		bosEndpoint = "bd.bcebos.com"
	case "su":
		bosEndpoint = "su.bcebos.com"
	case "gz":
		bosEndpoint = "gz.bcebos.com"
	case "fwh":
		bosEndpoint = "fwh.bcebos.com"
	case "hkg":
		bosEndpoint = "hkg.bcebos.com"
	case "cd":
		bosEndpoint = "cd.bcebos.com"
	case "yq":
		bosEndpoint = "yq.bcebos.com"
	}

	bosClient, err := bos.NewClient(ak, sk, bosEndpoint)

	arr := strings.Split(image, "/")
	fileName := arr[len(arr)-1]
	object := path + fileName

	fmt.Printf("will start upload image to %s:%s/%s\n", region, bucket, object)
	err = bosClient.UploadSuperFile(bucket, object, image, "")
	if err != nil {
		fmt.Printf("upload image file error:%s\n", err)
		return
	}
	fmt.Printf("upload image file success!\n")

}
