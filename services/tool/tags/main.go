package main

import (
	"encoding/json"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/logictag"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
)

func main() {
	tags := `{
    "tagAssociationFulls": [
        {
            "accountId": "2e1be1eb99e946c3a543ec5a4eaa7d39",
            "resourceId": "ccr-1xxxxxx5",
            "resourceUuid": "ccr-1xxxxxx5",
            "region": "gz",
            "serviceType": "CCR",
            "tagId": 8150642,
            "tagKey": "gztest-wmx1",
            "tagValue": "gztest-wmx1",
            "associationType": 0
        },
        {
            "accountId": "2e1be1eb99e946c3a543ec5a4eaa7d39",
            "resourceId": "ccr-1xxxxxx5",
            "resourceUuid": "ccr-1xxxxxx5",
            "region": "gz",
            "serviceType": "CCR",
            "tagId": 8150732,
            "tagKey": "gztest-wmx2",
            "tagValue": "gztest-wmx2",
            "associationType": 0
        },
        {
            "accountId": "2e1be1eb99e946c3a543ec5a4eaa7d39",
            "resourceId": "ccr-1l8bp7vd",
            "resourceUuid": "ccr-1l8bp7vd",
            "region": "gz",
            "serviceType": "CCR",
            "tagId": 8150642,
            "tagKey": "gztest-wmx1",
            "tagValue": "gztest-wmx1",
            "associationType": 0
        },
        {
            "accountId": "2e1be1eb99e946c3a543ec5a4eaa7d39",
            "resourceId": "ccr-1l8bp7vd",
            "resourceUuid": "ccr-1l8bp7vd",
            "region": "gz",
            "serviceType": "CCR",
            "tagId": 8150732,
            "tagKey": "gztest-wmx2",
            "tagValue": "gztest-wmx2",
            "associationType": 0
        }
    ]
}`
	ListTagsResult := &logictag.ListTagsResult{}
	err := json.Unmarshal([]byte(tags), &ListTagsResult)
	if err != nil {
		panic(err)
	}

	tagResourceMap := map[string][]model.Tag{}
	if ListTagsResult != nil && len(ListTagsResult.TagAssociationFulls) > 0 {

		for _, tagAssociation := range ListTagsResult.TagAssociationFulls {
			resourceID := tagAssociation.ResourceID
			if _, ok := tagResourceMap[resourceID]; ok {
				tagResourceMap[resourceID] = append(tagResourceMap[resourceID], model.Tag{
					TagKey:   tagAssociation.TagKey,
					TagValue: tagAssociation.TagValue,
				})
			} else {
				tagResourceMap[resourceID] = []model.Tag{model.Tag{
					TagKey:   tagAssociation.TagKey,
					TagValue: tagAssociation.TagValue,
				}}
			}
		}
	}

	items := []*model.InstanceInfo{
		&model.InstanceInfo{
			ID: "ccr-1l8bp7vd",
		},
		&model.InstanceInfo{
			ID: "ccr-1xxxxxx5",
		},
		&model.InstanceInfo{
			ID: "ccr-1xxxxwmx",
		},
	}

	tagBoundInstance := true

	filteredItems := make([]*model.InstanceInfo, 0, len(items))
	for i, instance := range items {
		if tags, ok := tagResourceMap[instance.ID]; ok {
			if ok == tagBoundInstance {
				items[i].Tags = tags
				filteredItems = append(filteredItems, items[i])
			}
		} else {
			filteredItems = append(filteredItems, items[i])
		}
	}
}
