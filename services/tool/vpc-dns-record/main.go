package main

import (
	"flag"
	"fmt"

	"k8s.io/apimachinery/pkg/util/uuid"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/tool/vpc-dns-record/clientset"
)

var (
	serviceName     string
	servicePassword string
	serviceRole     string

	stsEndpoint    string
	iamEndpoint    string
	vpcDnsEndpoint string
	vpcEndpoint    string

	accountID string
	vpcID     string

	domain           string
	serviceNetworkIP string

	resourceAccountID string
	resourceName      string
	resourceHex       string

	deleteRecord bool
)

func init() {
	flag.StringVar(&serviceName, "service-name", "", "The service name")
	flag.StringVar(&servicePassword, "service-password", "", "The service password")
	flag.StringVar(&serviceRole, "service-role", "", "The service role")

	flag.StringVar(&stsEndpoint, "sts-endpoint", "", "The sts endpoint")
	flag.StringVar(&iamEndpoint, "iam-endpoint", "", "The iam endpoint")
	flag.StringVar(&vpcDnsEndpoint, "vpc-dns-endpoint", "", "The bcc endpoint")
	flag.StringVar(&vpcEndpoint, "vpc-endpoint", "", "The vpc endpoint")

	flag.StringVar(&accountID, "account-id", "", "The account ID")
	flag.StringVar(&vpcID, "vpc-id", "", "The vpc ID")

	flag.StringVar(&domain, "domain", "", "The domain name")
	flag.StringVar(&serviceNetworkIP, "service-network-ip", "", "The service network IP")

	flag.StringVar(&resourceAccountID, "resource-account-id", "", "The resource account id")
	flag.StringVar(&resourceName, "resource-name", "ccr", "The resource name")
	flag.StringVar(&resourceHex, "resource-hex", "", "The resource hex")

	flag.BoolVar(&deleteRecord, "delete-record", false, "The delete dns record")

}

// main main 函数是程序的入口，负责解析命令行参数并进行相应操作
// 如果解析参数失败或创建客户端失败，则会panic出错误信息
func main() {
	flag.Parse()

	fmt.Println("start create vpc dns record")

	requestID := string(uuid.NewUUID())

	fmt.Printf("requestId: %s \n", requestID)

	clients, err := clientset.NewClientSet(stsEndpoint, iamEndpoint, serviceName, servicePassword, serviceRole)
	if err != nil {
		panic(err)
	}

	vpcDnsClient, err := clients.VpcDnsClientFromAccount(accountID, "", vpcDnsEndpoint)
	if err != nil {
		fmt.Printf("create vpc dns client failed: %s\n", err)
		return
	}

	vpcCli, err := clients.VpcClientFromAccount(accountID, "", vpcEndpoint)
	if err != nil {
		fmt.Printf("create vpc client failed: %s\n", err)
		return
	}

	longIdMap, err := vpcCli.MapByShortIds([]string{vpcID})
	if err != nil {
		fmt.Printf("get vpc long ID failed: %s\n", err)
		return
	}

	vpcLongID := longIdMap[vpcID]
	fmt.Printf("vpcLongID: %s \n", vpcLongID)

	resp, err := vpcDnsClient.ListVpcRecord(requestID, resourceName,
		resourceHex, resourceAccountID, vpcLongID, domain, "", "", "1000")
	if err != nil {
		fmt.Printf("list vpc record failed: %s\n", err)
		return
	}
	fmt.Printf("list vpc record success: %+v\n", resp)

	if len(resp.Records) > 0 && resp.Records[0] != nil && resp.Records[0].Id != "" {
		fmt.Printf("record exists: %s\n", resp.Records[0].Id)
		//	if deleteRecord {
		//		err = vpcDnsClient.DeleteVpcRecord(requestID, resourceName,
		//			resourceHex, resourceAccountID, resp.Records[0].Id)
		//		if err != nil {
		//			fmt.Printf("deleteRecord vpc record failed: %s\n", err)
		//			return
		//		}
		//		fmt.Printf("deleteRecord record success: %s\n", resp.Records[0].Id)
		//		return
		//	}
		//	fmt.Printf("record exists: %s\n", resp.Records[0].Id)
		//	return
		//}
		//
		//if !deleteRecord {
		//	records, err := vpcDnsClient.CreateVpcRecord(requestID, resourceName,
		//		resourceHex, resourceAccountID, &dns.CreateVPCDNSRecordRequest{
		//			VpcId:  vpcLongID,
		//			Name:   domain,
		//			Domain: "baidubce.com",
		//			Type:   "A",
		//			Value:  serviceNetworkIP,
		//			Ttl:    60,
		//		})
		//	if err != nil {
		//		fmt.Printf("created vpc record failed: %s\n", err)
		//		return
		//	}
		//
		//	fmt.Printf("create vpc record success, record ID: %s\n", records.Id)
	}

}
