package clientset

import (
	"fmt"

	"github.com/baidubce/bce-sdk-go/auth"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/vpc"
)

type ClientSet struct {
	stsClient    sts.ClientInterface
	iamClient    *iam.Client
	vpcDnsClient *dns.VpcDnsClient
}

func NewClientSet(stsEndpoint, iamEndpoint, serviceName, servicePassword, serviceRole string) (*ClientSet, error) {

	stsCli, err := sts.NewClient(stsEndpoint, iamEndpoint, serviceName, servicePassword, serviceRole)
	if err != nil {
		return nil, err
	}

	internalIamCli := &iam.Client{BceClient: stsCli.IAM()}

	return &ClientSet{
		stsClient: stsCli,
		iamClient: internalIamCli,
	}, nil
}

func (c *ClientSet) stsCredential(accountId, userId string) (*auth.BceCredentials, error) {
	cred, err := c.stsClient.GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	stsCredential, err := auth.NewSessionBceCredentials(
		cred.AccessKeyId,
		cred.SecretAccessKey,
		cred.SessionToken,
	)
	if err != nil {
		return nil, fmt.Errorf("create session credential failed: %w", err)
	}

	return stsCredential, nil
}

func (c *ClientSet) VpcDnsClientFromAccount(accountId, userId, endpoint string) (dns.VpcDnsClientInterface, error) {
	stsCred, err := c.stsCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	vpcDnsCli, err := dns.NewVpcDnsClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create vpc dns client failed: %w", err)
	}
	vpcDnsCli.Client.GetBceClientConfig().Credentials.SessionToken = stsCred.SessionToken

	return vpcDnsCli, nil
}

func (c *ClientSet) VpcClientFromAccount(accountId, userId, endpoint string) (*vpc.Client, error) {
	stsCred, err := c.stsCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	vpcCli, err := vpc.NewClient(stsCred.AccessKeyId, stsCred.SecretAccessKey, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create vpc client failed: %w", err)
	}

	vpcCli.Config.Credentials = stsCred
	return vpcCli, nil
}
