package clientset

import (
	"fmt"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/bosinterface"

	"github.com/baidubce/bce-sdk-go/auth"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"
)

type ClientSet struct {
	stsClient    sts.ClientInterface
	iamClient    *iam.Client
	vpcDnsClient *dns.VpcDnsClient
}

// NewClientSet 新建一个 ClientSet，包含了 STS 客户端和 IAM 客户端。
// stsEndpoint (string): STS 服务的地址。
// iamEndpoint (string): IAM 服务的地址。
// serviceName (string): 服务名称。
// servicePassword (string): 服务密码。
// serviceRole (string): 服务角色。
// 返回值 (ClientSet, error): 返回一个 ClientSet 类型的指针和错误信息（如果有）
func NewClientSet(stsEndpoint, iamEndpoint, serviceName, servicePassword, serviceRole string) (*ClientSet, error) {

	stsCli, err := sts.NewClient(stsEndpoint, iamEndpoint, serviceName, servicePassword, serviceRole)
	if err != nil {
		return nil, err
	}

	internalIamCli := &iam.Client{BceClient: stsCli.IAM()}

	return &ClientSet{
		stsClient: stsCli,
		iamClient: internalIamCli,
	}, nil
}

// stsCredential 获取STS凭证，返回值为*auth.BceCredentials类型和error类型的错误信息
// 参数：accountId - string类型，账号ID
//
//	userId - string类型，用户ID
//
// 返回值：stsCredential - *auth.BceCredentials类型，STS凭证
//
//	err - error类型，错误信息
func (c *ClientSet) stsCredential(accountId, userId string) (*auth.BceCredentials, error) {
	cred, err := c.stsClient.GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	stsCredential, err := auth.NewSessionBceCredentials(
		cred.AccessKeyId,
		cred.SecretAccessKey,
		cred.SessionToken,
	)
	if err != nil {
		return nil, fmt.Errorf("create session credential failed: %w", err)
	}

	return stsCredential, nil
}

// BosClientForAccount 获取指定账户、用户和endpoint的BOS客户端，返回bosinterface.BosInterface类型和error类型的错误信息
func (c *ClientSet) BosClientForAccount(accountId, userId, endpoint string) (bosinterface.BosInterface, error) {
	cred, err := c.stsClient.GetCredential(accountId, userId)
	if err != nil {
		return nil, err
	}

	stsCredential, err := auth.NewSessionBceCredentials(
		cred.AccessKeyId,
		cred.SecretAccessKey,
		cred.SessionToken,
	)
	if err != nil {
		return nil, fmt.Errorf("create session credential failed: %w", err)
	}

	bosCli, err := bos.NewClient(cred.AccessKeyId, cred.SecretAccessKey, endpoint)
	if err != nil {
		return nil, fmt.Errorf("create bos client failed: %w", err)
	}

	bosCli.Config.Credentials = stsCredential

	return bosCli, nil
}
