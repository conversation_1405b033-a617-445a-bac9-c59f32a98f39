package main

import (
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"net/http"
	"os"
	"reflect"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos/api"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/tool/bos-trash/clientset"
)

var (
	kubeconfig string
	scheme     = runtime.NewScheme()

	serviceName     string
	servicePassword string
	serviceRole     string

	stsEndpoint string
	iamEndpoint string
	bosEndpoint string
)

// init init 函数用于初始化程序，设置命令行参数的默认值和类型。
// 参数：无
// 返回值：无
func init() {

	flag.StringVar(&serviceName, "service-name", "", "The service name")
	flag.StringVar(&servicePassword, "service-password", "", "The service password")
	flag.StringVar(&serviceRole, "service-role", "", "The service role")

}

// main 函数是程序的入口函数
func main() {
	flag.Parse()

	fmt.Println("start create vpc dns record")

	requestID := string(uuid.NewUUID())

	fmt.Printf("requestId: %s \n", requestID)

	clients, err := clientset.NewClientSet(stsEndpoint, iamEndpoint, serviceName, servicePassword, serviceRole)
	if err != nil {
		panic(err)
	}

	kubedata, err := os.ReadFile(kubeconfig)
	if err != nil {
		fmt.Println("read file failed: ", err)
		return
	}

	k8sClient, err := utils.NewK8sClient(string(kubedata), scheme)
	if err != nil {
		fmt.Println("new k8s client failed: ", err)
		return
	}

	var ccrList ccrv1alpha1.CCRList
	err = k8sClient.List(context.Background(), &ccrList)
	if err != nil {
		fmt.Println("list ccr failed")
		return
	}

	for _, item := range ccrList.Items {
		if err = brushBosBucketConfig(context.Background(), clients, k8sClient, item.Name); err != nil {
			fmt.Println("migrate domain cert failed: ", err)
			return
		}
	}

}

// brushBosBucketConfig 函数用于配置BOS桶的桶级别，包括开启回收站、开启生命周期规则等操作。
// 参数：
//
//	ctx context.Context - 上下文信息，可以为nil
//	clients *clientset.ClientSet - BOS客户端集合，不能为nil
//	k8sClient client.Client - Kubernetes客户端，不能为nil
//	name string - BOS桶名称，不能为空字符串
//
// 返回值：
//
//	error - 错误信息，如果没有错误则为nil
func brushBosBucketConfig(ctx context.Context, clients *clientset.ClientSet, k8sClient client.Client, name string) error {
	var bosObj ccrv1alpha1.Bos
	err := k8sClient.Get(ctx, client.ObjectKey{Namespace: name, Name: name}, &bosObj)
	if err != nil && !apierrors.IsNotFound(err) {
		fmt.Println("get bos failed: ", err)
		return err
	}

	bosCli, err := clients.BosClientForAccount(bosObj.Spec.ExternalAccountID, bosObj.Spec.ExternalUserID, bosEndpoint)
	if err != nil {
		fmt.Printf("get bos client for %v failed \n", bosObj.Spec.ExternalAccountID)

		return err
	}

	if err := bosCli.HeadBucket(name); err != nil {
		fmt.Println("head bucket failed: ", err)
		return err
	}

	needOpenTrash := false
	bucketTrash, err := bosCli.GetBucketTrash(name)
	var bceError *bce.BceServiceError
	if err != nil {
		if errors.As(err, &bceError) {
			switch bceError.StatusCode {
			case http.StatusForbidden:
				fmt.Printf("has no permission to open the bucket %v", name)
				return fmt.Errorf("has no permission to open the bucket %v", name)
			case http.StatusNotFound:
				switch bceError.Code {
				case "NoSuchTrashDirectory":
					needOpenTrash = true
				default:
					fmt.Printf("get bucket trash failed: %v", err)
					return err
				}
			default:
				fmt.Printf("get bucket trash failed: %v", err)
				return err
			}
		} else {
			fmt.Printf("get bucket trash failed: %v", err)
			return err
		}
	}

	fmt.Printf("bucket is encrypted: %s", bucketTrash.TrashDir)
	if needOpenTrash {
		if err := bosCli.PutBucketTrash(name, api.PutBucketTrashReq{
			TrashDir: ".trash",
		}); err != nil {
			fmt.Printf("put bucket trash failed: %v", err)
			return err
		}
	}

	needOpenLifecycle := false
	lifecycleResult, err := bosCli.GetBucketLifecycle(name)
	if err != nil {
		if errors.As(err, &bceError) {
			switch bceError.StatusCode {
			case http.StatusForbidden:
				fmt.Printf("have no permission to get bucket lifecycle: %v", err)
				return fmt.Errorf("has no permission to get bucket lifecycle: %v", name)
			case http.StatusNotFound:
				switch bceError.Code {
				case "NoLifecycleConfiguration":
					needOpenLifecycle = true
				default:
					fmt.Printf("get bucket lifecycle failed: %v", err)
					return err
				}

			default:
				fmt.Printf("get bucket lifecycle failed: %v", err)
				return err
			}
		} else {
			fmt.Printf("get bucket lifecycle failed: %v", err)
			return err
		}
	} else {
		if !hasLifecycleRule(lifecycleResult, name) {
			needOpenLifecycle = true
		}
	}

	//fmt.Printf("bucket lifecycle: %v", lifecycleResult.Rule)
	if needOpenLifecycle {

		lifecycleArgs := api.PutBucketLifecycleArgs{}
		for _, ruleType := range lifecycleResult.Rule {
			lifecycleArgs.Rule = append(lifecycleArgs.Rule, ruleType)
		}

		lifecycleArgs.Rule = append(lifecycleArgs.Rule, api.LifecycleRuleType{
			Id:       fmt.Sprintf("%s/.trash/*-DeleteObject", name),
			Status:   "enabled",
			Resource: []string{fmt.Sprintf("%s/.trash/*", name)},
			Condition: api.LifecycleConditionType{
				Time: api.LifecycleConditionTimeType{
					DateGreaterThan: "$(lastModified)+P15D",
				},
			},
			Action: api.LifecycleActionType{
				Name: "DeleteObject",
			},
		})

		lifecycleString, err := json.Marshal(lifecycleArgs)
		if err != nil {
			fmt.Printf("marshal lifecycle failed: %v", err)
			return err
		}

		if err := bosCli.PutBucketLifecycleFromString(name, string(lifecycleString)); err != nil {
			fmt.Printf("put bucket lifecycle failed: %v", err)
			return err
		}
	}

	return nil
}

// hasLifecycleRule 判断给定的生命周期规则是否存在于指定的bucket中，返回bool值
// 参数：
//   - lifecycleResult *api.GetBucketLifecycleResult (*api.GetBucketLifecycleResult): bucket的生命周期规则结果
//   - name string: 需要查找的生命周期规则名称
//
// 返回值：
//   - bool: true表示存在，false表示不存在
func hasLifecycleRule(lifecycleResult *api.GetBucketLifecycleResult, name string) bool {

	want := api.LifecycleRuleType{
		Id:       fmt.Sprintf("%s/.trash/*-DeleteObject", name),
		Status:   "enabled",
		Resource: []string{fmt.Sprintf("%s/.trash/*", name)},
		Condition: api.LifecycleConditionType{
			Time: api.LifecycleConditionTimeType{
				DateGreaterThan: "$(lastModified)+P15D",
			},
		},
		Action: api.LifecycleActionType{
			Name: "DeleteObject",
		},
	}

	for _, rule := range lifecycleResult.Rule {
		if reflect.DeepEqual(rule, want) {

		}
	}
	return false
}
