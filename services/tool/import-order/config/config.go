package config

import (
	"io/ioutil"

	"k8s.io/apimachinery/pkg/util/yaml"
)

type ServiceConfig struct {
	AccessKey string `yaml:"accessKey,omitempty"`
	SecretKey string `yaml:"secretKey,omitempty"`

	ClusterID string `yaml:"clusterId,omitempty"`

	CCEEndpoint string `yaml:"cceEndpoint,omitempty"`

	Region string `yaml:"region,omitempty"`

	OrderManagerEndpoint   string `yaml:"orderManagerEndpoint,omitempty"`
	ResourceManageEndpoint string `yaml:"resourceManageEndpoint,omitempty"`

	EccrAccessKey string `yaml:"eccrAccessKey,omitempty"`
	EccrSecretKey string `yaml:"eccrSecretKey,omitempty"`
}

func NewConfig(filepath string) (*ServiceConfig, error) {
	content, err := ioutil.ReadFile(filepath)
	if err != nil {
		return nil, err
	}

	var serviceConfig ServiceConfig
	err = yaml.Unmarshal(content, &serviceConfig)
	if err != nil {
		return nil, err
	}

	return &serviceConfig, nil
}
