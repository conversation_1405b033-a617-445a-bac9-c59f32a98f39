package main

import (
	"context"
	"flag"
	"fmt"
	"time"

	"github.com/baidubce/bce-sdk-go/util/log"
	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v3"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing/orderimport"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/tool/import-order/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/tool/import-order/config"
)

var (
	serviceConfigFile string
	instanceId        string
	expire            string
	authorization     string
)

func init() {

	flag.StringVar(&serviceConfigFile, "config", "", "import-order config file path.")

	flag.StringVar(&instanceId, "instanceId", "", "import-order ccr instance id.")

	flag.StringVar(&expire, "expire", "", "import-order ccr instance expire time.")
	flag.StringVar(&authorization, "auth", "", "import-order with authorization.")
}

func main() {
	flag.Parse()

	log.SetLogHandler(log.STDERR)
	log.SetLogLevel(log.DEBUG)

	logrus.SetLevel(logrus.DebugLevel)
	logger := logrus.WithFields(logrus.Fields{
		"instanceId": instanceId,
	})

	if serviceConfigFile == "" {
		panic("config file is empty")
	}

	conf, err := config.NewConfig(serviceConfigFile)
	if err != nil {
		logger.Errorf("read config file failed: %s", err)
		return
	}

	expireTime, err := time.ParseDuration(expire)
	if err != nil {
		logger.Errorf("parse expire err: %s", err)
		return
	}

	cs, err := clientset.NewClientSet(conf, false)
	if err != nil {
		logger.Errorf("new client set failed: %s", err)
		return
	}

	importExecutor := ImportExecutor{
		clientSet: cs,
		conf:      conf,
		logger:    logger,
	}

	ccr := &ccrv1alpha1.CCR{}
	if err := cs.K8sClient().Get(context.Background(), client.ObjectKey{Name: instanceId}, ccr); err != nil {
		logger.Errorf("get ccr instance: %s failed: %s", instanceId, err)
		if apierrors.IsNotFound(err) {
			logger.Infof("get ccr instance not found")
			return
		}
		logger.Errorf("get ccr instance failed: %s", err)
		return
	}

	if ccr.Labels[ccrv1alpha1.LastOrderIdKey] != "" {
		logger.Infof("execute import order already exixt: %s", ccr.Labels[ccrv1alpha1.LastOrderIdKey])
		return
	}

	if err := importExecutor.execute(ccr, expireTime, authorization); err != nil {
		logger.Errorf("execute import order failed: %s", err)
		return
	}

}

type ImportExecutor struct {
	clientSet *clientset.ClientSet
	conf      *config.ServiceConfig
	logger    *logrus.Entry
}

func (ie *ImportExecutor) execute(ccr *ccrv1alpha1.CCR, expireTime time.Duration, authorization string) error {
	region := ie.conf.Region

	var canary bool
	if region == common.RegionGZTEST {
		canary = true
		region = common.RegionGZ
	}

	orderId, err := ie.importOrder(instanceId, ccr.GetLabels()["name"], string(ccr.Spec.Type), ccr.Spec.AccountID, region, expireTime, canary, authorization)
	if err != nil {
		ie.logger.Errorf("import orderfailed: %s", err)
		return err
	}

	// 查询billing资源
	resource, err := ie.clientSet.ResourceClient().GetResourceDetail(string(uuid.NewUUID()),
		&billing.GetResourceDetailRequest{
			ServiceType: billing.ServiceType,
			Region:      region,
			AccountId:   ccr.Spec.AccountID,
			Name:        instanceId,
		})

	if err != nil {
		ie.logger.Errorf("get billing resource %v failed: %s", ccr.GetName(), err)
		return err
	}
	ie.logger.Debugf("get resource detail: %#v", resource)

	updateObj := ccr.DeepCopy()
	billingExpireTime := metav1.NewTime(resource.ExpireTime)
	updateObj.Labels[ccrv1alpha1.BillingResourceUuidKey] = resource.Uuid
	updateObj.Labels[ccrv1alpha1.LastOrderIdKey] = orderId
	updateObj.Spec.ExpireTime = &billingExpireTime

	if err := ie.clientSet.K8sClient().Patch(context.Background(), updateObj, client.MergeFrom(ccr)); err != nil {
		ie.logger.Errorf("upgrade instance %v failed: %s", ccr.GetName(), err)
		return err
	}

	ie.logger.Debugf("==== stop import order, ccr:  %s ====", ccr.GetName())

	return nil
}

func (ie *ImportExecutor) importOrder(instanceId, instanceName, instanceType, accountId, region string, expireTime time.Duration, canary bool, authorization string) (string, error) {

	flavorItemList := []orderimport.FlavorItem{{
		Name:  "Specification_0",
		Value: instanceType,
		Scale: 1,
	}}

	extra := &billing.Extra{
		HistoryOrderImport: true,
		Canary:             canary,
		AutoRenew:          false,
		InstanceId:         instanceId,
		InstanceName:       instanceName,
		InstanceType:       instanceType,
	}

	content, err := yaml.Marshal(extra)
	if err != nil {
		ie.logger.Errorf("yaml marshal extra failed: %s", err)
		return "", err
	}

	createTime := time.Now().UTC()
	args := &orderimport.ImportOrderRequest{
		ServiceType: billing.ServiceType,
		Items: []orderimport.Item{
			{
				ResourceName: instanceId,
				CreateTime:   createTime.Format("2006-01-02T15:04:05Z"),
				Time:         int(expireTime.Hours()),
				TimeUnit:     "HOUR",
				ProductType:  "prepay",
				AccountId:    accountId,
				Region:       region,
				Extra:        string(content),
				Flavor:       flavorItemList,
			},
		},
	}

	resp, err := ie.clientSet.ImportOrderClient().ImportOrder(string(uuid.NewUUID()), authorization, args)
	if err != nil {
		ie.logger.Errorf("import order %v failed: %s", instanceId, err)
		return "", err
	}

	ie.logger.Infof("batchId: %d", resp.BatchId)
	for _, dri := range resp.DuplicatedResourceInfo {
		ie.logger.Infof("duplicated resource info: %s", dri)
	}

	if len(resp.ImportedOrderUuidList) == 0 {
		return "", fmt.Errorf("import order failed: %s is already exixt", instanceId)
	}
	ie.logger.Infof("orderId: %s", resp.ImportedOrderUuidList[0])

	return resp.ImportedOrderUuidList[0], nil
}
