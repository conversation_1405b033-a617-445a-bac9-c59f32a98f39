package clientset

import (
	"fmt"

	"github.com/baidubce/bce-sdk-go/services/cce"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing/orderimport"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/tool/import-order/config"
)

type ClientSet struct {
	k8sClient                 client.Client
	importOrderClient         orderimport.Client
	importRealTimeOrderClient orderimport.RealTimeOrderClient
	resourceClient            *billing.ResourceClient
}

func NewClientSet(conf *config.ServiceConfig, withAkSk bool) (*ClientSet, error) {

	importRealTimeOrderClient, err := orderimport.NewImportRealTimeOrderClient(conf.EccrAccessKey, conf.EccrSecretKey, conf.OrderManagerEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create order import client failed: %w", err)
	}

	importOrderClient, err := orderimport.NewClientWithoutAKSK(conf.OrderManagerEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create order import client failed: %w", err)
	}

	resourceClient, err := billing.NewResourceClient(conf.EccrAccessKey, conf.EccrSecretKey, conf.ResourceManageEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create resource client failed: %w", err)
	}

	cceCli, err := cce.NewClient(conf.AccessKey, conf.SecretKey, conf.CCEEndpoint)
	if err != nil {
		return nil, fmt.Errorf("new cce client failed: %s", err)
	}

	kubeResult, err := cceCli.GetKubeConfig(&cce.GetKubeConfigArgs{
		ClusterUuid: conf.ClusterID,
		Type:        cce.KubeConfigTypeInternal,
	})
	if err != nil {
		return nil, fmt.Errorf("get kube config failed: %s", err)
	}

	k8sCli, err := utils.NewK8sClient(kubeResult.Data, scheme)
	if err != nil {
		return nil, fmt.Errorf("new k8s clinet failed: %s", err)
	}

	return &ClientSet{
		k8sClient:                 k8sCli,
		importRealTimeOrderClient: importRealTimeOrderClient,
		importOrderClient:         importOrderClient,
		resourceClient:            resourceClient,
	}, nil
}

func (c *ClientSet) K8sClient() client.Client {
	return c.k8sClient
}

func (c *ClientSet) ImportOrderClient() orderimport.Client {
	return c.importOrderClient
}

func (c *ClientSet) ImportRealTimeOrderClient() orderimport.RealTimeOrderClient {
	return c.importRealTimeOrderClient
}

func (c *ClientSet) ResourceClient() *billing.ResourceClient {
	return c.resourceClient
}
