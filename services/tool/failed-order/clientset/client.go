package clientset

import (
	"fmt"

	"github.com/baidubce/bce-sdk-go/services/cce"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/tool/failed-order/config"
)

type ClientSet struct {
	k8sClient      client.Client
	orderClient    *billing.OrderClient
	resourceClient *billing.ResourceClient
}

func NewClientSet(conf *config.ServiceConfig) (*ClientSet, error) {

	orderClient, err := billing.NewOrderClient(conf.EccrAccessKey, conf.EccrSecretKey, conf.OrderManagerEndpoint)
	if err != nil {
		return nil, fmt.Errorf("create order import client failed: %w", err)
	}

	cceCli, err := cce.NewClient(conf.<PERSON><PERSON>ey, conf.<PERSON><PERSON>ey, conf.CCEEndpoint)
	if err != nil {
		return nil, fmt.Errorf("new cce client failed: %s", err)
	}

	kubeResult, err := cceCli.GetKubeConfig(&cce.GetKubeConfigArgs{
		ClusterUuid: conf.ClusterID,
		Type:        cce.KubeConfigTypeInternal,
	})
	if err != nil {
		return nil, fmt.Errorf("get kube config failed: %s", err)
	}

	k8sCli, err := utils.NewK8sClient(kubeResult.Data, scheme)
	if err != nil {
		return nil, fmt.Errorf("new k8s clinet failed: %s", err)
	}

	return &ClientSet{
		k8sClient:   k8sCli,
		orderClient: orderClient,
	}, nil
}

func (c *ClientSet) K8sClient() client.Client {
	return c.k8sClient
}

func (c *ClientSet) OrderClient() *billing.OrderClient {
	return c.orderClient
}

func (c *ClientSet) ResourceClient() *billing.ResourceClient {
	return c.resourceClient
}
