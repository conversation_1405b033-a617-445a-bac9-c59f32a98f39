package main

import (
	"context"
	"flag"

	"github.com/baidubce/bce-sdk-go/util/log"
	"github.com/sirupsen/logrus"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/tool/failed-order/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/tool/failed-order/config"
)

var (
	serviceConfigFile string
	instanceId        string
	orderId           string
	clear             bool
)

func init() {

	flag.BoolVar(&clear, "clear", false, "clear ccr instance")
	flag.StringVar(&serviceConfigFile, "config", "", "failed-order config file path.")
	flag.StringVar(&instanceId, "instanceId", "", "failed-order ccr instance id.")

	flag.StringVar(&orderId, "orderId", "", "failed-order ccr instance id.")
}

func main() {
	flag.Parse()
	log.SetLogHandler(log.STDERR)
	log.SetLogLevel(log.DEBUG)

	logrus.SetLevel(logrus.DebugLevel)
	logger := logrus.WithFields(logrus.Fields{
		"instanceId": instanceId,
	})

	if serviceConfigFile == "" {
		panic("config file is empty")
	}

	conf, err := config.NewConfig(serviceConfigFile)
	if err != nil {
		logger.Errorf("read config file failed: %s", err)
		return
	}

	cs, err := clientset.NewClientSet(conf)
	if err != nil {
		logger.Errorf("new client set failed: %s", err)
		return
	}

	failedExecutor := FailedExecutor{
		clientSet: cs,
		conf:      conf,
		logger:    logger,
	}

	if orderId != "" {
		if err := failedExecutor.executeByOrder(orderId); err != nil {
			logger.Errorf("execute failed order by order failed: %s", err)
			return
		}
	} else {
		ccr := &ccrv1alpha1.CCR{}
		if err := cs.K8sClient().Get(context.Background(), client.ObjectKey{Name: instanceId}, ccr); err != nil {
			logger.Errorf("get ccr instance: %s failed: %s", instanceId, err)
			if apierrors.IsNotFound(err) {
				logger.Infof("get ccr instance not found")
				return
			}
			logger.Errorf("get ccr instance failed: %s", err)
			return
		}

		if ccr.GetLabels()[ccrv1alpha1.LastOrderIdKey] == "" {
			logger.Infof("execute failed order: last order id is empty")
			return
		}

		if err := failedExecutor.executeByCCR(ccr); err != nil {
			logger.Errorf("execute failed order by ccr failed: %s", err)
			return
		}
	}

}

type FailedExecutor struct {
	clientSet *clientset.ClientSet
	conf      *config.ServiceConfig
	logger    *logrus.Entry
}

func (ie *FailedExecutor) executeByCCR(ccr *ccrv1alpha1.CCR) error {

	orderId := ccr.GetLabels()[ccrv1alpha1.LastOrderIdKey]

	// 查询billing订单
	resp, err := ie.clientSet.OrderClient().GetOrderDetail(string(uuid.NewUUID()), orderId)
	if err != nil {
		ie.logger.Errorf("[failed-order] get order detail failed: %s", err)
		return err
	}
	ie.logger.Infof("[failed-order] get orderId: %s, orderType: %s", orderId, resp.Type)

	uor := &billing.UpdateOrderRequest{
		Status: "CREATE_FAILED",
	}

	if err := ie.clientSet.OrderClient().UpdateOrder(string(uuid.NewUUID()), resp.Uuid, uor); err != nil {
		ie.logger.Errorf("[failed-order] update order status failed: %s", err)
		return err
	}

	if clear {
		if err := ie.clientSet.K8sClient().Delete(context.Background(), ccr); err != nil {
			ie.logger.Error("[failed-order] failed to delete instance", err)
			return err
		}
	}

	ie.logger.Debugf("==== stop failed order, ccr:  %s ====", ccr.GetName())

	return nil
}

func (ie *FailedExecutor) executeByOrder(orderId string) error {

	// 查询billing订单
	resp, err := ie.clientSet.OrderClient().GetOrderDetail(string(uuid.NewUUID()), orderId)
	if err != nil {
		ie.logger.Errorf("[failed-order] get order detail failed: %s", err)
		return err
	}
	ie.logger.Infof("[failed-order] get orderId: %s, orderType: %s", orderId, resp.Type)

	uor := &billing.UpdateOrderRequest{
		Status: "CREATE_FAILED",
	}

	if err := ie.clientSet.OrderClient().UpdateOrder(string(uuid.NewUUID()), resp.Uuid, uor); err != nil {
		ie.logger.Errorf("[failed-order] update order status failed: %s", err)
		return err
	}

	return nil
}
