apiVersion: batch/v1
kind: Job
metadata:
  name: image-import
spec:
  completions: 2 # 需要成功运行的pod个数，应该与待执行镜像导入的节点个数一致,其中待执行调度的节点需要打标签：ccr.image-import: true;通过反亲和性将job调度到不同的节点。
  parallelism: 10 # 提交pod的并发度
  backoffLimit: 3 # 失败重试次数
  ttlSecondsAfterFinished: 600 # 已完成的job自动清理时间
  template:
    metadata:
      name: image-import
      labels:
        app: image-import
    spec:
      containers:
      - name: image-import
        image: registry.baidubce.com/cce-public/ccr-image-import:202303301507
        # name参数值是前置步骤中导出的镜像文件名称；
        command: ["/home/<USER>/bin/image-import",  "--name=test-image.tar"]
        volumeMounts:
            - mountPath: /usr/bin/docker
              name: dockerpath
            - mountPath: /var/run/docker.sock
              name: dockersock
      restartPolicy: OnFailure
      volumes:
        - name: dockerpath
          hostPath:
            path: /usr/bin/docker
        - name: dockersock
          hostPath:
            path: /var/run/docker.sock
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - image-import
            topologyKey: "kubernetes.io/hostname"
      nodeSelector:
        ccr.image-import: "true"

 