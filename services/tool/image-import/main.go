package main

import (
	"flag"
	"fmt"
	"os/exec"
	"strings"

	"github.com/baidubce/bce-sdk-go/services/bos"
)

var (
	path    string
	name    string
	runtime string
	region  string
	bucket  string
	ak      string
	sk      string
)

func init() {

	flag.StringVar(&name, "name", "", "required: image file name.")
	flag.StringVar(&path, "path", "", "optional: image file save path,default /home/<USER>")
	flag.StringVar(&runtime, "runtime", "", "optional: container runtime,like docker or containerd，default docker.")
	flag.StringVar(&region, "region", "", "optional: region of bos,values is:bj,bd,gz,su,fwh,hkg")
	flag.StringVar(&bucket, "bucket", "", "optional: bos bucket name.")
	flag.StringVar(&ak, "ak", "", "optional: access key.")
	flag.StringVar(&sk, "sk", "", "optional: secret key.")
}

const ShellToUse = "bash"

func main() {
	flag.Parse()

	if name == "" {
		panic("image name is empty,please input image name")
	}
	if path == "" {
		path = "/home/<USER>"
	}
	if runtime == "" {
		runtime = "docker"
	}

	// 镜像文件下载到本地保存路径
	if !strings.HasSuffix(path, "/") {
		path = path + "/"
	}
	localPath := path + name

	if region == "" {
		//如果未指定地域，设置默认gz
		region = "gz"
	}

	if region != "gz" && bucket == "" {
		//未指定bucket，且不是gz会报错
		panic("region or bucket invalid,please input correct value.")
	}

	if region == "gz" && bucket == "" {
		//未指定bucket，使用gz默认的bucket
		bucket = "ccr-1axaxax1"
	}

	object := "image-load/" + name
	bosEndpoint := "gz.bcebos.com"
	switch region {
	case "bj":
		bosEndpoint = "bj.bcebos.com"
	case "bd":
		bosEndpoint = "bd.bcebos.com"
	case "su":
		bosEndpoint = "su.bcebos.com"
	case "gz":
		bosEndpoint = "gz.bcebos.com"
	case "fwh":
		bosEndpoint = "fwh.bcebos.com"
	case "hkg":
		bosEndpoint = "hkg.bcebos.com"
	}

	//1. download file from bos
	fmt.Printf("will start download image from %s:%s/%s\n", region, bucket, object)
	bosClient, err := bos.NewClient(ak, sk, bosEndpoint)
	err = bosClient.DownloadSuperFile(bucket, object, localPath)
	if err != nil {
		fmt.Printf("download file error:%s", err)
		return
	}
	fmt.Println("download file success!")

	//2.import image
	cmd := ""
	switch runtime {
	case "docker":
		cmd = "docker load -i " + localPath
	case "containerd":
		cmd = "ctr -n k8s.io image import " + localPath
	}
	if cmd == "" {
		fmt.Println("runtime not support.")
		return
	}
	fmt.Printf("begin execute cmd: %s\n", cmd)
	command := exec.Command(ShellToUse, "-c", cmd)
	result, err := command.CombinedOutput()
	if err != nil {
		fmt.Printf("call cmd failed:%s", err.Error())
	}
	fmt.Printf("call cmd result is: %s\n", string(result))
}
