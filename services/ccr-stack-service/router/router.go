package router

import (
	"net/http"

	"github.com/gin-gonic/gin"
	gs "github.com/swaggo/gin-swagger"
	"github.com/swaggo/gin-swagger/swaggerFiles"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/ginprom"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/config"
	_ "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/docs"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/handler"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/middleware"
)

type ApiFunc func(c *gin.Context) *handler.Response

func NewGin(conf *config.ServiceConfig, clientset clientset.ClientSetInterface) *gin.Engine {
	route := gin.Default()
	route.Use(gin_context.LoggerMiddleware())
	route.UseRawPath = true

	route.GET("/healthz", func(c *gin.Context) {
		c.String(http.StatusOK, "ok")
	})

	route.GET("/metrics", ginprom.PromHanlder)
	if gin.Mode() == gin.DebugMode {
		route.GET("/swagger/*any", gs.WrapHandler(swaggerFiles.Handler))
	}

	instr := func(f ApiFunc) gin.HandlerFunc {
		return func(c *gin.Context) {
			response := f(c)
			response.ParseResponse()
		}
	}

	v1 := route.Group("/v1", middleware.IAMMiddleware(clientset), middleware.HarborClientMiddleware(clientset,
		conf.Harbor.HarborHost, conf.Harbor.HarborUser, conf.Harbor.HarborPassword, conf.Harbor.CoreSecretNamespace, conf.Harbor.CoreSecretName))
	{
		// instance
		instanceIdGroup := v1.Group("instances/:instanceId")
		{
			instanceHandler := handler.NewInstance(conf)
			{
				instanceIdGroup.GET("", instanceHandler.Get)
			}

			// user
			userGroup := instanceIdGroup.Group("/users")
			{
				userHandler := handler.NewUserHandler(clientset)

				userGroup.GET("/current", userHandler.GetCurrentUser)
				userGroup.GET("/:username", userHandler.GetUser)
				userGroup.POST("", userHandler.CreateUser)
				userGroup.PUT("/:username/sysadmin", userHandler.UpdateUserSysadmin)
				userGroup.PUT("/:username/password", userHandler.UpdateUserPassword)
				userGroup.DELETE("/:username", userHandler.DeleteUser)
			}

			// 命名空间
			projectGroup := instanceIdGroup.Group("/projects")
			{
				projectHandler := handler.NewProjectHandler(clientset)
				projectGroup.POST("", projectHandler.CreateProject) // 创建命名空间

				projectNameGroup := projectGroup.Group("/:projectName")
				{
					projectNameGroup.GET("", instr(projectHandler.GetProject)) // 通过项目名称获取项目详情
					projectNameGroup.DELETE("", projectHandler.DeleteProject)  //  通过通过项目名称删除项目

					projectMemberGroup := projectNameGroup.Group("/members")
					{
						memberHandler := handler.NewMemberHandler(clientset)
						projectMemberGroup.POST("", memberHandler.CreateProjectMember)
						projectMemberGroup.GET("", memberHandler.ListProjectMember)
						projectMemberGroup.DELETE("/:username", memberHandler.DeleteProjectMember)
					}

					repositoryGroup := projectNameGroup.Group("/repositories", gin_context.RepositoryNameResolveMiddleware())
					{
						repositoryHandler := handler.NewRepositoryHandler(clientset, conf.Harbor.HarborDomain)
						repositoryGroup.GET("", instr(repositoryHandler.ListRepository))                      // 查询镜像仓库列表
						repositoryGroup.DELETE("", instr(repositoryHandler.BatchDeleteRepository))            // 批量删除镜像仓库
						repositoryGroup.DELETE("/:repositoryName", instr(repositoryHandler.DeleteRepository)) // 删除镜像仓库
						repositoryGroup.PUT("/:repositoryName", instr(repositoryHandler.UpdateRepository))    // 更新镜像仓库
						repositoryGroup.GET("/:repositoryName", instr(repositoryHandler.GetRepository))       // 查询镜像仓库

						tagGroup := repositoryGroup.Group("/:repositoryName/tags")
						{
							tagHandler := handler.NewTagHandler()
							tagGroup.GET("", instr(tagHandler.ListTag))                                // 查询tag列表
							tagGroup.GET("/:tagName", instr(tagHandler.GetTag))                        // 查询tag详情
							tagGroup.DELETE("", instr(tagHandler.BatchDeleteTag))                      // 批量删除tag
							tagGroup.DELETE("/:tagName", instr(tagHandler.DeleteTag))                  // 删除tag
							tagGroup.POST("/:tagName/scan", instr(tagHandler.TagScan))                 // 扫描漏洞
							tagGroup.GET("/:tagName/scan/:reportId/log", instr(tagHandler.TagScanLog)) // 扫描漏洞日志
							tagGroup.GET("/:tagName/scanoverview", instr(tagHandler.ScanOverview))     // 查询tag漏洞
							tagGroup.GET("/:tagName/buildhistory", instr(tagHandler.BuildHistory))     // 查询tag构建历史
						}
					}

					chartGroup := projectNameGroup.Group("/charts")
					{
						chartHandler := handler.NewChartHandler(clientset)
						chartGroup.POST("", instr(chartHandler.PostCharts))                                             // 上传chart
						chartGroup.GET("", instr(chartHandler.ListCharts))                                              // 查询chart列表
						chartGroup.DELETE("", instr(chartHandler.BatchDeleteChart))                                     // 批量删除chart
						chartGroup.DELETE("/:chartName", instr(chartHandler.DeleteChart))                               // 删除chart
						chartGroup.GET("/:chartName/versions/:chartVersion", instr(chartHandler.GetChartVersion))       // 查询chart version详情
						chartGroup.GET("/:chartName/versions", instr(chartHandler.ListChartVersions))                   // 查询chart version列表
						chartGroup.DELETE("/:chartName/versions", instr(chartHandler.BatchDeleteChartVersion))          // 批量删除chart版本
						chartGroup.DELETE("/:chartName/versions/:chartVersion", instr(chartHandler.DeleteChartVersion)) // 删除chart版本
					}
				}

			}
		}
	}

	return route
}
