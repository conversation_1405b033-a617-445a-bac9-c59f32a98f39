package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/baidubce/bce-sdk-go/util/log"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/config"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/router"
)

var (
	serviceConfigFile string // ccr-service 配置文件
	runMode           string // gin 模式: debug release test
	version           string // 版本
)

var cmd = &cobra.Command{
	Use:     "ccr-service",
	Short:   "CCR enterprise ccr service.",
	Long:    `CCR enterprise directly communicate with the front end and call the business interface of area a according to the requirement.`,
	Example: "go run main.go api --config=config.yaml",
	Run:     run,
	Version: version,
}

func init() {
	flags := cmd.Flags()

	flags.StringVar(&serviceConfigFile, "config", "", "ccr-service service 配置文件.")
	flags.StringVar(&version, "log-file", "", "ccr-service 日志路径, 不设置则打印到 STDOUT.")
	flags.StringVar(&runMode, "run-mode", "debug", "run 模式: debug release test.")
}

// @title CCR Service API
// @version 0.0.1
// @description CCR Service 提供 RESTFUL 风格 API, 对接 Console 及 OpenAPI 请求

// @contact.name duzhanwei,wenmanxiang
// @contact.email <EMAIL>

// @host ccr.baidubce.com
// @BasePath /v1
func main() {
	//log.SetLogHandler(log.STDERR)
	//log.SetLogLevel(log.DEBUG)

	if err := cmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "error: %v\n", err)
		os.Exit(1)
	}
}

func run(*cobra.Command, []string) {

	signalChan := make(chan os.Signal, 0)
	signal.Notify(signalChan, syscall.SIGTERM, syscall.SIGINT)

	stopChan := make(chan struct{})

	go func() {
		<-signalChan
		close(stopChan)
	}()

	if serviceConfigFile == "" {
		panic("config file is empty")
	}

	conf, err := config.NewConfig(serviceConfigFile)
	if err != nil {
		panic(fmt.Sprintf("read config file failed: %s", err))
	}

	var clients clientset.ClientSetInterface

	client, err := utils.NewK8sClient("", scheme)
	if err != nil {
		panic(fmt.Sprintf("create cluster client failed: %s", err))
	}

	clients, err = clientset.NewClientSet(*conf, client)
	if err != nil {
		panic(fmt.Sprintf("create client set failed: %s", err))
	}

	NewServer(conf, clients).Start(stopChan)
}

type Server struct {
	httpSrv *http.Server

	conf      *config.ServiceConfig
	clientset clientset.ClientSetInterface
}

func NewServer(conf *config.ServiceConfig, clientset clientset.ClientSetInterface) *Server {

	return &Server{
		conf:      conf,
		clientset: clientset,
	}
}

func (server *Server) Start(stopChan <-chan struct{}) {
	// set gin mode
	gin.SetMode(runMode)
	if runMode == "debug" {
		log.SetLogHandler(log.STDERR)
		log.SetLogLevel(log.DEBUG)
		logrus.SetLevel(logrus.DebugLevel)
	}
	logrus.SetFormatter(&logrus.TextFormatter{DisableColors: true})

	r := router.NewGin(server.conf, server.clientset)
	server.httpSrv = &http.Server{
		Addr:           server.conf.ListenAddress,
		Handler:        r,
		ReadTimeout:    10 * time.Second,
		WriteTimeout:   10 * time.Second,
		MaxHeaderBytes: 1 << 20,
	}

	go func() {
		<-stopChan
		timeoutCtx, cancelCtx := context.WithTimeout(context.TODO(), 10*time.Second)
		defer cancelCtx()
		_ = server.httpSrv.Shutdown(timeoutCtx)
	}()

	if err := server.httpSrv.ListenAndServe(); err != nil {
		fmt.Println("server was shutdown gracefully")
		panic(err)
	}
}
