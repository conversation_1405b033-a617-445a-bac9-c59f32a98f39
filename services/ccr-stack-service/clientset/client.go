package clientset

import (
	"sigs.k8s.io/controller-runtime/pkg/client"

	sdkiam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/config"
)

type ClientSetInterface interface {
	K8sClient() client.Client
	InternalIam() iam.ClientInterface
	Conf() config.ServiceConfig

	HarborClient(harborHost, harborUser, harborPassword string) (*harbor.HarborClient, error)
	HarborClientSimulate(harborHost, sid string, harborCsrf *utils.HarborCsrf) (*harbor.HarborClient, error)
}

type ClientSet struct {
	iamClient iam.ClientInterface

	k8sClient    client.Client
	harborClient *harbor.Client

	conf config.ServiceConfig
}

func NewClientSet(conf config.ServiceConfig, k8sCli client.Client) (ClientSetInterface, error) {

	iamCli := iam.NewClient(sdkiam.NewBceClient(&sdkiam.BceClientConfiguration{
		Endpoint: conf.IAMEndpoint,
		UserName: conf.ServiceName,
		Password: conf.ServicePassword,
		Retry:    sdkiam.DEFAULT_RETRY_POLICY,
		Domain:   "Default",
		Version:  "/v3",
	}))

	harborCli := harbor.NewClient(conf.Harbor.HarborIP)

	return &ClientSet{
		k8sClient:    k8sCli,
		iamClient:    iamCli,
		harborClient: harborCli,
		conf:         conf,
	}, nil
}

func (c *ClientSet) K8sClient() client.Client {
	return c.k8sClient
}

func (c *ClientSet) InternalIam() iam.ClientInterface {
	return c.iamClient
}

func (c *ClientSet) Conf() config.ServiceConfig {
	return c.conf
}

func (c *ClientSet) HarborClient(harborHost, harborUser, harborPassword string) (*harbor.HarborClient, error) {
	return c.harborClient.HarborClientFor(harborHost, harborUser, harborPassword)
}

func (c *ClientSet) HarborClientSimulate(harborHost, sid string, harborCsrf *utils.HarborCsrf) (*harbor.HarborClient, error) {
	return c.harborClient.HarborClientForSimulate(harborHost, sid, harborCsrf)
}
