package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type RegistryHandler struct {
}

func NewRegistryHandler(allowlist string) *RegistryHandler {
	return &RegistryHandler{}
}

// CreateRegistry 创建新的远程仓库
// @Summary 创建新的远程仓库
// @Description 创建新的远程仓库
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registry body model.RegistryRequest true "create registry body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries [post]
func (h *RegistryHandler) CreateRegistry(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}

// UpdateRegistry 修改单个远程仓库
// @Summary 修改单个远程仓库
// @Description 修改单个远程仓库
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registryId path string true "远程仓库ID"
// @Param registry body model.RegistryRequest true "update registry request"
// @Success 200 {object} model.RegistryResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries/{registryId} [put]
func (h *RegistryHandler) UpdateRegistry(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}

// ListRegistries 获取远程仓库列表
// @Summary 获取远程仓库列表
// @Description 获取远程仓库列表
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registryName query string false "远程仓库名称"
// @Param registryType query string false "远程仓库类型"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListRegistryResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries [get]
func (h *RegistryHandler) ListRegistries(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}

// GetRegistry 通过registryId查询远程仓库
// @Summary 通过registryId查询远程仓库
// @Description 通过registryId查询远程仓库
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registryId path string true "镜像远程仓库ID"
// @Success 200 {object} model.RegistryResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries/{registryId} [get]
func (h *RegistryHandler) GetRegistry(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}

// DeleteRegistry 删除远程仓库
// @Summary 删除远程仓库
// @Description 删除远程仓库
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registryId path string true "远程仓库ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries/{registryId} [delete]
func (h *RegistryHandler) DeleteRegistry(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}

// CheckHealth 检查远程仓库健康状态
// @Summary 检查远程仓库健康状态
// @Description 检查远程仓库健康状态
// @Tags registry
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param registryHealth body model.RegistryRequest true "check registry health request"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/registries/ping [post]
func (h *RegistryHandler) CheckHealth(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}
