package handler

import (
	"fmt"
	"github.com/go-openapi/runtime"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	repositoryapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/repository"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/repository"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset"
)

type RepositoryHandler struct {
	clientset         clientset.ClientSetInterface
	repositoryService repository.Interface
	harborDomain      string
}

func NewRepositoryHandler(clientset clientset.ClientSetInterface, harborDomain string) *RepositoryHandler {
	return &RepositoryHandler{
		clientset:         clientset,
		harborDomain:      harborDomain,
		repositoryService: repository.NewRepositoryService(),
	}
}

// ListRepository 查询镜像仓库列表
// @Summary 查询镜像仓库列表
// @Description 查询镜像仓库列表
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName query string false "镜像仓库名称"
// @Param pageNo query int false "当前页" default(1)
// @Param pageSize query int false "每页记录数" default(10)
// @Success 200 {object} model.ListRepositoryResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories [get]
func (h RepositoryHandler) ListRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	//requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	projectName, repositoryName := c.Param("projectName"), c.Query("repositoryName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		return NewResponse(http.StatusBadRequest, "page no is invalid", nil, c)
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		return NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	useSim := true
	if projectName == "ccr-public" || projectName == "ccr-ai" {
		useSim = false
	}
	repositoryResults, total, err := h.repositoryService.ListRepositories(c, projectName, repositoryName, &pageNo, &pageSize, useSim)
	if err != nil {
		logger.Errorf("list repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}
	for i := range repositoryResults {
		repositoryResults[i].RepositoryPath = fmt.Sprintf("%s/%s/%s", h.harborDomain, projectName, repositoryResults[i].RepositoryName)
	}

	lrr := &model.ListRepositoryResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: repositoryResults,
	}
	return NewResponse(http.StatusOK, "success", lrr, c)
}

// BatchDeleteRepository 批量删除镜像仓库
// @Summary 批量删除镜像仓库
// @Description 批量删除镜像仓库
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param body body model.BatchDeleteRequest true "镜像仓库名称数组"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories [delete]
func (h *RepositoryHandler) BatchDeleteRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	//requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	projectName := c.Param("projectName")

	var deleteRequest model.BatchDeleteRequest
	if err := c.BindJSON(&deleteRequest); err != nil || len(deleteRequest.Items) == 0 {
		logger.Errorf("bind project list item failed: %s", err)
		return NewResponse(http.StatusBadRequest, "page size is invalid", nil, c)
	}

	if err := h.repositoryService.BatchDeleteRepository(c, projectName, deleteRequest.Items); err != nil {
		logger.Errorf("delete repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}
	return NewResponse(http.StatusOK, "success", nil, c)
}

// DeleteRepository 删除单个镜像仓库
// @Summary 删除单个镜像仓库
// @Description 删除单个镜像仓库
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName} [delete]
func (h *RepositoryHandler) DeleteRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	//requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	projectName, repositoryName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c)

	if err := h.repositoryService.DeleteRepository(c, projectName, repositoryName); err != nil {
		logger.Errorf("delete repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}

	return NewResponse(http.StatusOK, "success", nil, c)
}

// GetRepository 通过镜像仓库名称查询镜像仓库
// @Summary 通过镜像仓库名称查询镜像仓库
// @Description 通过镜像仓库名称查询镜像仓库
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Success 200 {object} model.RepositoryResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName} [get]
func (h *RepositoryHandler) GetRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	//requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	projectName, repositoryName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c)

	useSim := true
	if projectName == "ccr-public" || projectName == "ccr-ai" {
		useSim = false
	}
	repositoryResult, err := h.repositoryService.GetRepository(c, projectName, repositoryName, useSim)
	if err != nil {
		logger.Errorf("get repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)

	}
	repositoryResult.RepositoryPath = fmt.Sprintf("%s/%s/%s", h.harborDomain, projectName, repositoryResult.RepositoryName)

	return NewResponse(http.StatusOK, "success", repositoryResult, c)

}

// UpdateRepository 修改单个镜像仓库
// @Summary 修改单个镜像仓库
// @Description 修改单个镜像仓库
// @Tags repository
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param repositoryName path string true "镜像仓库名称"
// @Param repository body model.UpdateRepositoryRequest true "update repository request"
// @Success 200 {object} model.RepositoryResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName} [put]
func (h *RepositoryHandler) UpdateRepository(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	//requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	projectName, repositoryName := c.Param("projectName"), gin_context.RepositoryNameFromContext(c)

	var urr *model.UpdateRepositoryRequest
	if err := c.Bind(&urr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		return NewResponse(http.StatusBadRequest, "bind request body failed", nil, c)
	}

	if err := h.repositoryService.UpdateRepository(c, projectName, repositoryName, urr.Description, true); err != nil {
		logger.Errorf("update repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}
	repositoryResult, err := h.repositoryService.GetRepository(c, projectName, repositoryName, true)
	if err != nil {
		logger.Errorf("get repository failed: %s", err)
		return HandleRepositorySwaggerErrors(c, err)
	}
	repositoryResult.RepositoryPath = fmt.Sprintf("%s/%s/%s", h.harborDomain, projectName, repositoryResult.RepositoryName)
	repositoryResult.PrivateRepositoryPath = fmt.Sprintf("%s/%s/%s", h.harborDomain, projectName, repositoryResult.RepositoryName)

	return NewResponse(http.StatusOK, "success", repositoryResult, c)
}

// HandleRepositorySwaggerErrors takes a swagger generated error as input,
// which usually does not contain any form of error message,
// and outputs a new error with a proper message.
func HandleRepositorySwaggerErrors(c *gin.Context, in error) *Response {
	t, ok := in.(*runtime.APIError)
	if ok {
		switch t.Code {
		case http.StatusBadRequest:
			return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
		case http.StatusUnauthorized:
			return NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
		case http.StatusForbidden:
			return NewResponse(http.StatusForbidden, "user does not have permission to the repository", nil, c)
		case http.StatusNotFound:
			return NewResponse(http.StatusNotFound, "repository not found", nil, c)
		case http.StatusConflict:
			return NewResponse(http.StatusConflict, "repository name already exists", nil, c)
		case http.StatusPreconditionFailed:
			return NewResponse(http.StatusPreconditionFailed, "repository precondition failed", nil, c)
		case http.StatusInternalServerError:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		default:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}

	switch in.(type) {
	case *repositoryapi.ListRepositoriesForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *repositoryapi.ListRepositoriesNotFound:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *repositoryapi.DeleteRepositoryNotFound:
		return NewResponse(http.StatusNotFound, "repository not found", nil, c)
	case *repositoryapi.DeleteRepositoryBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *repositoryapi.DeleteRepositoryForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *repositoryapi.UpdateRepositoryNotFound:
		return NewResponse(http.StatusNotFound, "repository not found", nil, c)
	case *repositoryapi.UpdateRepositoryBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *repositoryapi.UpdateRepositoryForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *repositoryapi.GetRepositoryNotFound:
		return NewResponse(http.StatusNotFound, "repository not found", nil, c)
	case *repositoryapi.GetRepositoryForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *repositoryapi.GetRepositoryUnauthorized:
		return NewResponse(http.StatusUnauthorized, "has no permission", nil, c)
	case *repositoryapi.ListRepositoriesUnauthorized:
		return NewResponse(http.StatusUnauthorized, "has no permission", nil, c)
	default:
		return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
	}
}
