package handler

import (
	"github.com/go-openapi/runtime"
	projectapi "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor/api/client/project"
	"net/http"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset"
)

type ProjectHandler struct {
	ClientSet      clientset.ClientSetInterface
	projectService project.ProjectServiceInterface
}

func NewProjectHandler(clientset clientset.ClientSetInterface) *ProjectHandler {
	return &ProjectHandler{
		ClientSet:      clientset,
		projectService: project.NewProjectService(),
	}
}

// CreateProject 创建命名空间
// @Summary 创建命名空间
// @Description 创建命名空间
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param project body model.CreateProjectRequest true "create project body"
// @Success 200 {object} model.ProjectResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects [post]
func (h *ProjectHandler) CreateProject(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var cpr model.CreateProjectRequest
	if err := c.Bind(&cpr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	pr := &model.ProjectReq{
		ProjectName: cpr.ProjectName,
		Public:      cpr.Public,
	}

	if err := h.projectService.NewProject(c, pr); err != nil {
		logger.Errorf("new project failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	projectObj, err := h.projectService.GetProject(c, cpr.ProjectName, false)
	if err != nil {
		logger.Errorf("get project failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, projectObj)
}

// GetProject 通过命名空间名称projectName查询命名空间
// @Summary 通过命名空间名称projectName查询命名空间
// @Description 通过命名空间名称projectName查询命名空间
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Success 200 {object} model.ProjectResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName} [get]
func (h *ProjectHandler) GetProject(c *gin.Context) *Response {
	logger := gin_context.LoggerFromContext(c)
	//requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	projectName := c.Param("projectName")

	useScan := true
	if projectName == "ccr-public" || projectName == "ccr-ai" {
		useScan = false
	}
	pr, err := h.projectService.GetProject(c, projectName, useScan)
	if err != nil {
		logger.Errorf("get project failed: %s", err)
		return HandleProjectSwaggerErrors(c, err)
	}
	return NewResponse(http.StatusOK, "success", pr, c)
}

// DeleteProject 删除命名空间
// @Summary 删除命名空间
// @Description 删除命名空间
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName} [delete]
func (h *ProjectHandler) DeleteProject(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	projectName := c.Param("projectName")

	if err := h.projectService.DeleteProject(c, projectName); err != nil {
		logger.Errorf("delete project failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
	}

	c.JSON(http.StatusOK, "delete project succeeded")
}

// HandleProjectSwaggerErrors takes a swagger generated error as input,
// which usually does not contain any form of error message,
// and outputs a new error with a proper message.
func HandleProjectSwaggerErrors(c *gin.Context, in error) *Response {
	t, ok := in.(*runtime.APIError)
	if ok {
		switch t.Code {
		case http.StatusBadRequest:
			return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
		case http.StatusUnauthorized:
			return NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
		case http.StatusForbidden:
			return NewResponse(http.StatusNotFound, "project not found", nil, c)
		case http.StatusNotFound:
			return NewResponse(http.StatusNotFound, "project not found", nil, c)
		case http.StatusConflict:
			return NewResponse(http.StatusConflict, "project name already exists", nil, c)
		case http.StatusPreconditionFailed:
			return NewResponse(http.StatusPreconditionFailed, "project precondition failed", nil, c)
		case http.StatusInternalServerError:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		default:
			return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
		}
	}

	switch in.(type) {
	case *projectapi.CreateProjectBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *projectapi.CreateProjectConflict:
		return NewResponse(http.StatusConflict, "project name already exists", nil, c)
	case *projectapi.DeleteProjectNotFound:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *projectapi.DeleteProjectBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *projectapi.DeleteProjectForbidden:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *projectapi.DeleteProjectPreconditionFailed:
		return NewResponse(http.StatusPreconditionFailed, "delete project precondition failed", nil, c)
	case *projectapi.UpdateProjectBadRequest:
		return NewResponse(http.StatusBadRequest, "invalid request", nil, c)
	case *projectapi.UpdateProjectNotFound:
		return NewResponse(http.StatusNotFound, "project not found", nil, c)
	case *projectapi.GetProjectUnauthorized:
		return NewResponse(http.StatusUnauthorized, "unauthorized", nil, c)
	default:
		return NewResponse(http.StatusInternalServerError, "unexpected internal errors", nil, c)
	}
}
