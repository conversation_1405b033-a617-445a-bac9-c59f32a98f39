package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/config"
)

type Instance struct {
	conf *config.ServiceConfig
}

func NewInstance(conf *config.ServiceConfig) *Instance {
	return &Instance{
		conf: conf,
	}
}

// Get 获取CCR实例
// @Summary 获取CCR实例
// @Description 获取CCR实例
// @Tags instance
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例id"
// @Success 200 {object} model.InstanceDetail "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId} [get]
func (cls *Instance) Get(c *gin.Context) {

	instanceDetail := model.InstanceDetail{
		Info: &model.InstanceInfo{
			PublicURL: cls.conf.Harbor.HarborDomain,
		},
	}

	c.JSON(http.StatusOK, instanceDetail)
}
