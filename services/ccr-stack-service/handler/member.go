package handler

import (
	"net/http"
	"strconv"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/middleware"

	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset"
)

type MemberHandler struct {
	ClientSet            clientset.ClientSetInterface
	ProjectMemberService project.MemberServiceInterface
}

func NewMemberHandler(clientset clientset.ClientSetInterface) *MemberHandler {
	return &MemberHandler{
		ClientSet:            clientset,
		ProjectMemberService: project.NewProjectMemberService(),
	}
}

// CreateProjectMember 创建命名空间成员
// @Summary 创建命名空间成员
// @Description 创建命名空间成员
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间ID"
// @Param member body model.CreateProjectMemberRequest true "create project body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/members [post]
func (h *MemberHandler) CreateProjectMember(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	projectName := c.Param("projectName")
	var cmr model.CreateProjectMemberRequest
	if err := c.Bind(&cmr); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	isResourceName := true
	projectMember, _, err := h.ProjectMemberService.ListProjectMember(c, projectName, isResourceName)
	if err != nil {
		logger.Errorf("List project member failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	isExists := false
	for _, member := range projectMember {
		if member.EntityName == cmr.MemberUser.Username {
			isExists = true
			break
		}
	}
	if !isExists {
		if err := h.ProjectMemberService.NewProjectMember(c, &cmr, projectName, isResourceName); err != nil {
			logger.Errorf("new project member failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}
	}

	c.JSON(http.StatusOK, "new project member succeeded")
}

// DeleteProjectMember 删除命名空间成员
// @Summary 创建命名空间成员
// @Description 创建命名空间成员
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Param username path string true "用户名"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/members/{username} [delete]
func (h *MemberHandler) DeleteProjectMember(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	projectNameOrID, username := c.Param("projectName"), c.Param("username")

	members, _, err := h.ProjectMemberService.ListProjectMember(c, projectNameOrID, true, 1, 100)
	if err != nil {
		logger.Errorf("list project members failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return

	}

	if len(members) > 0 {
		var memberId int64
		for _, m := range members {
			if m.EntityName == username {
				memberId = m.ID
				break
			}
		}
		if memberId != 0 {
			if err := h.ProjectMemberService.DeleteProjectMember(c, projectNameOrID, memberId, true); err != nil {
				logger.Errorf("update project member failed: %s", err)
				gin_context.E(c, gin_context.InternalServerError(requestID))
				return
			}
		}
	}

	c.JSON(http.StatusOK, "delete project member succeeded")
}

// ListProjectMember 查询命名空间下成员
// @Summary 查询命名空间下成员
// @Description 查询命名空间下成员
// @Tags project
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param projectName path string true "命名空间名称"
// @Success 200 {object} model.ListProjectMemberResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/projects/{projectName}/members [get]
func (h *MemberHandler) ListProjectMember(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	projectNameOrID := c.Param("projectName")

	pageNo, err := strconv.ParseInt(c.DefaultQuery("pageNo", "1"), 10, 64)
	if err != nil {
		logger.Errorf("page no is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}
	pageSize, err := strconv.ParseInt(c.DefaultQuery("pageSize", "10"), 10, 64)
	if err != nil {
		logger.Errorf("page size is invalid")
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}
	isResourceName := true
	resp, total, err := h.ProjectMemberService.ListProjectMember(c, projectNameOrID, isResourceName, pageNo, pageSize)
	if err != nil {
		logger.Errorf("list project members failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	mrr := &model.ListProjectMemberResponse{
		PageInfo: model.PageInfo{
			Total:    int(total),
			PageNo:   int(pageNo),
			PageSize: int(pageSize),
		},
		Items: resp,
	}

	c.JSON(http.StatusOK, mrr)
}
