package handler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/goharbor/harbor/src/testing/mock"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset"
	testingmember "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-stack-service/clientset"
	"net/http"
	"testing"
)

func TestMemberHandler_CreateProjectMember(t *testing.T) {
	type fields struct {
		ClientSet     clientset.ClientSetInterface
		MemberService project.MemberServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name: "success",
			fields: fields{
				ClientSet:     &testingclientset.ClientSet{},
				MemberService: &testingmember.MockMemberService{},
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				return args{
					c: ctx,
				}
			}(),
		},
		{
			name: "list project member failed",
			fields: fields{
				ClientSet:     &testingclientset.ClientSet{},
				MemberService: &testingmember.MockMemberService{},
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				return args{
					c: ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.name == "success" {
				req := model.CreateProjectMemberRequest{
					RoleID: 1,
					MemberUser: model.MemberUser{
						UserID:   1,
						Username: "testxxx",
					},
				}

				data, err := json.Marshal(req)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}
				tt.args.c.Params = append(tt.args.c.Params, gin.Param{Key: "projectName", Value: "testxxx"})

				tt.args.c.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
				tt.args.c.Request.Header.Add("Content-Type", gin.MIMEJSON)

				projectMemberResult := make([]*model.ProjectMemberResult, 0)
				projectMember := &model.ProjectMemberResult{
					ID:         1,
					ProjectID:  1,
					RoleID:     1,
					RoleName:   "testxxx",
					EntityID:   1,
					EntityName: "testxxx",
					EntityType: "test",
				}
				projectMemberResult = append(projectMemberResult, projectMember)
				mock.OnAnything(tt.fields.MemberService, "ListProjectMember").Return(projectMemberResult, int64(1), nil)
				mock.OnAnything(tt.fields.MemberService, "NewProjectMember").Return(nil)
			}

			if tt.name == "list project member failed" {
				req := model.CreateProjectMemberRequest{
					RoleID: 1,
					MemberUser: model.MemberUser{
						UserID:   1,
						Username: "testxxx",
					},
				}

				data, err := json.Marshal(req)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}
				tt.args.c.Params = append(tt.args.c.Params, gin.Param{Key: "projectName", Value: "testxxx"})

				tt.args.c.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
				tt.args.c.Request.Header.Add("Content-Type", gin.MIMEJSON)

				mock.OnAnything(tt.fields.MemberService, "ListProjectMember").Return(nil, int64(1), fmt.Errorf("list project member failed"))
			}

			h := &MemberHandler{
				ClientSet:            tt.fields.ClientSet,
				ProjectMemberService: tt.fields.MemberService,
			}
			h.CreateProjectMember(tt.args.c)
		})
	}
}

func TestMemberHandler_DeleteProjectMember(t *testing.T) {
	type fields struct {
		ClientSet     clientset.ClientSetInterface
		MemberService project.MemberServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "success",
			fields: fields{
				ClientSet:     &testingclientset.ClientSet{},
				MemberService: &testingmember.MockMemberService{},
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				return args{
					c: ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.name == "success" {

				tt.args.c.Params = append(tt.args.c.Params, gin.Param{Key: "projectName", Value: "testxxx"})
				tt.args.c.Params = append(tt.args.c.Params, gin.Param{Key: "username", Value: "testxxx"})

				tt.args.c.Request, _ = http.NewRequest("Delete", "/", nil)
				tt.args.c.Request.Header.Add("Content-Type", gin.MIMEJSON)

				projectMemberResult := make([]*model.ProjectMemberResult, 0)
				projectMember := &model.ProjectMemberResult{
					ID:         1,
					ProjectID:  1,
					RoleID:     1,
					RoleName:   "testxxx",
					EntityID:   1,
					EntityName: "testxxx",
					EntityType: "test",
				}
				projectMemberResult = append(projectMemberResult, projectMember)
				mock.OnAnything(tt.fields.MemberService, "ListProjectMember").Return(projectMemberResult, int64(1), nil)
				mock.OnAnything(tt.fields.MemberService, "DeleteProjectMember").Return(nil)
			}

			h := &MemberHandler{
				ClientSet:            tt.fields.ClientSet,
				ProjectMemberService: tt.fields.MemberService,
			}
			h.DeleteProjectMember(tt.args.c)
		})
	}
}
