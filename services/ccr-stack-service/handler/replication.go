package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type PolicyHandler struct {
}

func NewPolicyHandler() *PolicyHandler {
	return &PolicyHandler{}
}

// CreatePolicy 创建镜像迁移策略
// @Summary 创建镜像迁移策略
// @Description 创建镜像迁移策略
// @Tags replication
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policy body model.PolicyRequest true "create policy body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/replications [post]
func (h *PolicyHandler) CreatePolicy(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}

// UpdatePolicy 更新镜像迁移策略
// @Summary 更新镜像迁移策略
// @Description 更新镜像迁移策略
// @Tags replication
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "策略ID"
// @Param policy body model.PolicyRequest true "update policy body"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/replications/{policyId} [put]
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}

// DeletePolicy 删除镜像迁移策略
// @Summary 删除镜像迁移策略
// @Description 删除镜像迁移策略
// @Tags replication
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "策略ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/replications/{policyId} [delete]
func (h *PolicyHandler) DeletePolicy(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}

// ListPolicies 获取策略列表
// @Summary 获取策略列表
// @Description 获取策略列表
// @Tags replication
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyName query string false "镜像迁移策略名称"
// @Param pageNo query integer true "当前页" default(1)
// @Param pageSize query integer true "每页记录数" default(10)
// @Success 200 {object} model.ListReplicationResponse "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/replications [get]
func (h *PolicyHandler) ListPolicies(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}

// GetPolicy 通过policyId查询策略
// @Summary 通过policyId查询策略
// @Description 通过policyId查询策略
// @Tags replication
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param policyId path string true "镜像迁移策略名称ID"
// @Success 200 {object} model.PolicyResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/replications/{policyId} [get]
func (h *PolicyHandler) GetPolicy(c *gin.Context) {
	c.JSON(http.StatusOK, "")
}
