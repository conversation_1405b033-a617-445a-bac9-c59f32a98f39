package handler

import (
	"net/http"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/user"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-service/middleware"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset"

	"github.com/gin-gonic/gin"
)

type UserHandler struct {
	ClientSet   clientset.ClientSetInterface
	UserService user.UserServiceInterface
}

func NewUserHandler(clientset clientset.ClientSetInterface) *UserHandler {
	return &UserHandler{
		ClientSet:   clientset,
		UserService: user.NewUserService(),
	}
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建用户
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param user body model.CreateUserRequest true "create user body"
// @Success 201 {object} model.UserResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	var cur model.CreateUserRequest
	if err := c.Bind(&cur); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	var result *model.UserResult
	user, err := h.UserService.GetUser(c, cur.Username)
	if err != nil {
		logger.Errorf("get user failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}
	if user == nil {
		if err := h.UserService.NewUser(c, &cur); err != nil {
			logger.Errorf("new user failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}

		var err error
		result, err = h.UserService.GetUser(c, cur.Username)
		if err != nil {
			logger.Errorf("get user failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}
	}

	c.JSON(http.StatusOK, result)
}

// UpdateUserSysadmin 设置用户为管理员
// @Summary 设置用户为管理员
// @Description 设置用户为管理员
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param username path string true "用户ID"
// @Param user body model.UpdateUserSysadminRequest true "Toggle a user to admin or not"
// @Success 200 {object} model.UserResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/users/{username}/sysadmin [put]
func (h *UserHandler) UpdateUserSysadmin(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	username := c.Param("username")

	user, err := h.UserService.GetUser(c, username)
	if err != nil {
		logger.Errorf("get user failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return

	}

	var cur model.UpdateUserSysadminRequest
	if err := c.Bind(&cur); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	err = h.UserService.UpdateUserSysadmin(c, &cur, user.ID)
	if err != nil {
		logger.Errorf("update user sysadmin failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "Success")
}

// UpdateUserPassword 更新用户密码
// @Summary 更新用户密码
// @Description 更新用户密码
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param username path string true "用户名称"
// @Param password body model.UpdateUserPasswordRequest true "Password to be updated, the attribute 'OldPassword' is optional when the API is called by the system administrator."
// @Success 200 {object} model.UserResult "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/users/{username}/password [put]
func (h *UserHandler) UpdateUserPassword(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	username := c.Param("username")

	var req model.UpdateUserPasswordRequest
	if err := c.Bind(&req); err != nil {
		logger.Errorf("bind request body failed: %s", err)
		gin_context.E(c, gin_context.BadRequestError(requestID))
		return
	}

	user, err := h.UserService.GetUser(c, username)
	if err != nil {
		logger.Errorf("get user failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	err = h.UserService.UpdateUserPassword(c, &req, user.ID)
	if err != nil {
		logger.Errorf("update user password failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, "update user password success")
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 删除用户
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param username path string true "用户ID"
// @Success 200 {string} string "Success"
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/users/{username} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	username := c.Param("username")

	user, err := h.UserService.GetUser(c, username)
	if err != nil {
		logger.Errorf("get user failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	if user != nil && user.ID != 0 {
		if err = h.UserService.DeleteUser(c, user.ID); err != nil {
			logger.Errorf("delete user failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}
	}

	c.JSON(http.StatusOK, "delete user succeeded")
}

// GetUser 获取用户
// @Summary 获取用户
// @Description 获取用户
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Param username path string true "用户名称"
// @Success 200 {object} model.UserResult
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/users/{username} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	username := c.Param("username")

	userInfo, err := h.UserService.GetUser(c, username)
	if err != nil {
		logger.Errorf("get user info failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	c.JSON(http.StatusOK, userInfo)
}

// GetCurrentUser 获取当前用户
// @Summary 获取当前用户
// @Description 获取当前用户
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param instanceId path string true "实例ID"
// @Success 200 {success} model.UserResult
// @Failure 400 {object} bce.BceServiceError
// @Failure 500 {object} bce.BceServiceError
// @Router /instances/{instanceId}/users/current [get]
func (h *UserHandler) GetCurrentUser(c *gin.Context) {
	logger := gin_context.LoggerFromContext(c)
	requestID := c.Request.Header.Get(middleware.REQID_HEADER)

	userInfo, err := h.UserService.GetCurrentUser(c)
	if err != nil {
		logger.Errorf("get user info failed: %s", err)
		gin_context.E(c, gin_context.InternalServerError(requestID))
		return
	}

	if userInfo == nil {
		logger.Errorf("user not exists: %s", err)
		gin_context.E(c, gin_context.UnauthorizedError(requestID))
		return
	}
	c.JSON(http.StatusOK, userInfo)
}
