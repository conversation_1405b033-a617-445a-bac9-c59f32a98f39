package handler

import (
	"fmt"
	mockrepo "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service/harbor/repository"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/repository"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
	mockclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-stack-service/clientset"
)

func TestRepositoryHandler_ListRepository(t *testing.T) {
	type fields struct {
		repositoryService repository.Interface
		clientset         clientset.ClientSetInterface
		harborDomain      string
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name:   "invalid page number",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?repositoryName=xxx&pageNo=xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name:   "invalid page size",
			fields: fields{},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?repositoryName=xxx&pageNo=1&pageSize=xxx", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 400,
		},
		{
			name: "list repository failed",
			fields: func() fields {
				//lis := &mocklisters.MockLister{}
				//lis.On("GetInstanceInfo", mock.Anything).Return(&listers.InstanceInfo{}, nil)
				cli := &mockclientset.ClientSet{}
				//cli.On("Lister").Return(lis)

				repoSvc := mockrepo.NewMockInterface(gomock.NewController(t))
				repoSvc.EXPECT().ListRepositories(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, int64(0), fmt.Errorf("failed"))
				return fields{
					repositoryService: repoSvc,
					clientset:         cli,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?repositoryName=xxx&pageNo=1&pageSize=100", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 500,
		},
		{
			name: "success",
			fields: func() fields {
				//lis := &mocklisters.MockLister{}
				//lis.On("GetInstanceInfo", mock.Anything).Return(&listers.InstanceInfo{}, nil)
				cli := &mockclientset.ClientSet{}
				//cli.On("Lister").Return(lis)

				repoSvc := mockrepo.NewMockInterface(gomock.NewController(t))
				repoSvc.EXPECT().ListRepositories(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					[]*model.RepositoryResult{
						{
							RepositoryName: "test/test",
						},
					}, int64(1), nil)
				return fields{
					repositoryService: repoSvc,
					clientset:         cli,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com?repositoryName=xxx&pageNo=1&pageSize=100", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := RepositoryHandler{
				repositoryService: tt.fields.repositoryService,
				clientset:         tt.fields.clientset,
			}
			if got := h.ListRepository(tt.args.c); got.StatusCode() != tt.code {
				t.Errorf("RepositoryHandler.ListAllRepository() = %v, want %v", got.StatusCode(), tt.code)
			}
		})
	}
}

func TestRepositoryHandler_GetRepository(t *testing.T) {
	type fields struct {
		repositoryService repository.Interface
		clientset         clientset.ClientSetInterface
		harborDomain      string
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		code   int
	}{
		{
			name: "get repository failed",
			fields: func() fields {
				cli := &mockclientset.ClientSet{}

				repoSvc := mockrepo.NewMockInterface(gomock.NewController(t))
				repoSvc.EXPECT().GetRepository(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("failed"))
				return fields{
					repositoryService: repoSvc,
					clientset:         cli,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com/repositories/test", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 500,
		},
		{
			name: "success",
			fields: func() fields {
				//lis := &mocklisters.MockLister{}
				//lis.On("GetInstanceInfo", mock.Anything).Return(&listers.InstanceInfo{}, nil)
				cli := &mockclientset.ClientSet{}
				//cli.On("Lister").Return(lis)

				repoSvc := mockrepo.NewMockInterface(gomock.NewController(t))
				repoSvc.EXPECT().GetRepository(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&model.RepositoryResult{
						RepositoryName: "test",
					}, nil)
				return fields{
					repositoryService: repoSvc,
					clientset:         cli,
				}
			}(),
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				ctx.Request, _ = http.NewRequest(http.MethodGet, "http://test.com/repositories/test", nil)
				return args{
					c: ctx,
				}
			}(),
			code: 200,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := RepositoryHandler{
				repositoryService: tt.fields.repositoryService,
				clientset:         tt.fields.clientset,
			}
			if got := h.GetRepository(tt.args.c); got.StatusCode() != tt.code {
				t.Errorf("RepositoryHandler.GetRepository() = %v, want %v", got.StatusCode(), tt.code)
			}
		})
	}
}
