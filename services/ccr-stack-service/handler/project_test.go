package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/go-openapi/strfmt"
	"github.com/goharbor/harbor/src/testing/mock"

	ccrmodel "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset"
	testingproject "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/service/harbor/project"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/utils"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/ccr-stack-service/clientset"
)

func TestProjectHandler_CreateProject(t *testing.T) {
	type fields struct {
		ClientSet      clientset.ClientSetInterface
		projectService project.ProjectServiceInterface
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "success",
			fields: fields{
				ClientSet:      &testingclientset.ClientSet{},
				projectService: &testingproject.MockProjectService{},
			},
			args: func() args {
				ctx, _ := gin.CreateTestContext(utils.NewGinResponseWriter())
				return args{
					c: ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.name == "success" {
				req := ccrmodel.CreateProjectRequest{
					ProjectName: "test",
					Public:      "true",
				}

				data, err := json.Marshal(req)
				if err != nil {
					t.Errorf("json marshal failed: %s", err)
				}
				tt.args.c.Params = append(tt.args.c.Params, gin.Param{Key: "instanceId", Value: "xxxx"})

				tt.args.c.Request, _ = http.NewRequest("POST", "/", bytes.NewReader(data))
				tt.args.c.Request.Header.Add("Content-Type", gin.MIMEJSON)

				mock.OnAnything(tt.fields.projectService, "TotalProjects").Return(int64(8), nil)
				mock.OnAnything(tt.fields.projectService, "NewProject").Return(nil)
				mock.OnAnything(tt.fields.projectService, "GetProject").Return(&ccrmodel.ProjectResult{
					ProjectID:    int32(1),
					ProjectName:  "test",
					ChartCount:   int64(1),
					RepoCount:    int64(1),
					CreationTime: strfmt.NewDateTime(),
					UpdateTime:   strfmt.NewDateTime(),
					AutoScan:     "true",
					Public:       "true",
				}, nil)
			}

			h := &ProjectHandler{
				ClientSet:      tt.fields.ClientSet,
				projectService: tt.fields.projectService,
			}
			h.CreateProject(tt.args.c)

		})
	}
}
