package handler

import (
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/gin-gonic/gin"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/middleware"
	"net/http"
	"strings"
)

// Response defines the response structure for receiving BCE services response.
type Response struct {
	data       interface{}
	statusText string
	statusCode int
	c          *gin.Context
}

func NewResponse(statusCode int, statusText string, data interface{}, c *gin.Context) *Response {
	return &Response{statusCode: statusCode, statusText: statusText, data: data, c: c}
}

func (r *Response) IsFail() bool {
	return r.StatusCode() >= 400
}

func (r *Response) StatusText() string {
	return r.statusText
}

func (r *Response) StatusCode() int {
	return r.statusCode
}

func (r *Response) RequestId() string {
	return r.c.Request.Header.Get(middleware.REQID_HEADER)
}

func (r *Response) ParseResponse() {
	requestId := r.RequestId()
	if r.IsFail() {
		serviceError := bce.NewBceServiceError("", r.statusText, requestId, r.statusCode)

		switch r.statusCode {
		case http.StatusBadRequest:
			serviceError.Code = gin_context.EINVALID_HTTP_REQUEST
		case http.StatusUnauthorized:
			serviceError.Code = gin_context.UNAUTHORIZED
		case http.StatusForbidden:
			serviceError.Code = gin_context.EACCESS_DENIED
		case http.StatusNotFound:
			serviceError.Code = gin_context.NOT_FOUND
		case http.StatusConflict:
			serviceError.Code = gin_context.STATUS_CONFLICT
		case http.StatusPreconditionFailed:
			serviceError.Code = gin_context.EPRECONDITION_FAILED
		case http.StatusInternalServerError:
			serviceError.Code = gin_context.EINTERNAL_ERROR
		default:
			words := strings.Split(r.statusText, " ")
			serviceError.Code = strings.Join(words[1:], "")
		}
		r.c.JSON(serviceError.StatusCode, serviceError)
		return
	}
	if r.c.GetHeader("Content-Type") == "text/plain" {
		r.c.String(http.StatusOK, r.data.(string))
	}
	r.c.JSON(http.StatusOK, r.data)
}
