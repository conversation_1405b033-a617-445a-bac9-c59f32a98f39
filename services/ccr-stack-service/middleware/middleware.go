package middleware

import (
	"context"
	"errors"
	"net/http"
	"strings"

	harbormodels "github.com/goharbor/harbor/src/common/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/session"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/gin-gonic/gin"
	sdkiam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/gin_context"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/harbor"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/ccr-stack-service/clientset"
)

const (
	REQID_HEADER                    = "x-bce-request-id"
	ACCOUNT_ID_IDENTITY             = "ACCOUNT_ID"
	ACCOUNT_NAME_IDENTITY           = "ACCOUNT_NAME"
	USER_DI_IDENTITY                = "USER_ID"
	USER_NAME_IDENTITY              = "USER_NAME"
	NETWORK_OBJECT_IDENTIY          = "NETWORK_IDENTITY"
	INSTANCE_OBJECT_IDENTITY        = "INSTANCE_IDENTITY"
	HARBOR_CLIENT_IDENTITY          = "HARBOR_CLIENT_IDENTITY"
	HARBOR_CLIENT_REAL_IDENTITY     = "HARBOR_CLIENT_REAL_IDENTITY"
	HARBOR_CLIENT_SIMULATE_IDENTITY = "HARBOR_CLIENT_SIMULATE_IDENTITY"
	REPOSITORY_NAME_IDENTITY        = "REPOSITORY_NAME_IDENTITY"

	ORDER_DI_IDENTITY = "ORDER_ID"
)

func IAMMiddleware(clis clientset.ClientSetInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestId := gin_context.RequestIdFromContext(c)
		var accountId, accountName, userId, userName string

		token, err := clis.InternalIam().ValidatorRequest(c.Request)
		if err != nil {
			logger.Errorf("get user info failed: %s", err)

			var bceErr *sdkiam.BceServiceError
			if errors.As(err, &bceErr) {
				gin_context.E(c, bce.NewBceServiceError(bceErr.Code, bceErr.Message, bceErr.RequestId, bceErr.StatusCode))
				return
			}

			gin_context.E(c, gin_context.InternalServerError(requestId))
			return
		}

		if token.User.Domain.ID == "" || token.User.ID == "" {
			logger.Errorf("no user provided")
			gin_context.E(c, bce.NewBceServiceError(
				http.StatusText(http.StatusBadRequest),
				"cannot get user info",
				requestId,
				http.StatusBadRequest,
			))
			return
		}
		logger.Infof("account name: %s", token.User.Domain.Name)

		accountId = token.User.Domain.ID
		accountName = token.User.Domain.Name
		userId = token.User.ID
		userName = token.User.Name

		if strings.ToLower(accountId) == "default" {
			accountName = token.User.Name
		}

		logger.Infof("set context with accountId: %s,accountName: %s, userId: %s, userName: %s", accountId, accountName, userId, userName)
		c.Set(ACCOUNT_ID_IDENTITY, accountId)
		c.Set(ACCOUNT_NAME_IDENTITY, accountName)
		c.Set(USER_DI_IDENTITY, userId)
		c.Set(USER_NAME_IDENTITY, userName)
	}
}

func HarborClientMiddleware(clis clientset.ClientSetInterface, harborHost, harborUser, harborPassword, secretNamespace, secretName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger := gin_context.LoggerFromContext(c)
		requestID := c.Request.Header.Get(REQID_HEADER)
		//instanceId := c.Param("instanceId")
		username := UserNameFromContext(c)

		harborClient, err := clis.HarborClient(harborHost, harborUser, harborPassword)
		if err != nil {
			logger.Errorf("get harbor client failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}
		c.Set(HARBOR_CLIENT_IDENTITY, harborClient)

		sessionTokenService := session.NewSessionToken(harborClient)
		sid, err := sessionTokenService.GetSessionToken(c, &harbormodels.User{
			Username:        username,
			SysAdminFlag:    true,
			AdminRoleInAuth: true,
		})
		if err != nil {
			logger.Errorf("get session token failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}

		var harborCoreSecret corev1.Secret

		// TODO 命名空间从配置文件中获取
		if err := clis.K8sClient().Get(c, client.ObjectKey{Namespace: secretNamespace, Name: secretName}, &harborCoreSecret); err != nil {
			logger.Errorf("get harbor client failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}

		harborCsrf, err := utils.NewHarborCsrf(string(harborCoreSecret.Data["CSRF_KEY"]))
		if err != nil {
			logger.Errorf("generate csrf token failed %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}

		harborClientSimulate, err := clis.HarborClientSimulate(harborHost, sid, harborCsrf)
		if err != nil {
			logger.Errorf("get harbor client failed: %s", err)
			gin_context.E(c, gin_context.InternalServerError(requestID))
			return
		}
		c.Set(HARBOR_CLIENT_SIMULATE_IDENTITY, harborClientSimulate)

	}
}

func UserNameFromContext(ctx context.Context) string {
	if username, ok := ctx.Value(USER_NAME_IDENTITY).(string); ok {
		return username
	}
	return ""
}

func HarborClientFromContext(ctx context.Context) *harbor.HarborClient {
	return ctx.Value(HARBOR_CLIENT_IDENTITY).(*harbor.HarborClient)
}

func HarborClientSimulateFromContext(ctx context.Context) *harbor.HarborClient {
	return ctx.Value(HARBOR_CLIENT_SIMULATE_IDENTITY).(*harbor.HarborClient)
}
