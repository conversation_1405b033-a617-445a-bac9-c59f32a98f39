basePath: /v1
definitions:
  bce.BceServiceError:
    properties:
      code:
        type: string
      message:
        type: string
      requestId:
        type: string
      statusCode:
        type: integer
    type: object
  model.BatchDeleteRequest:
    properties:
      items:
        items:
          type: string
        type: array
    required:
    - items
    type: object
  model.BuildHistoryResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.BuildHistoryResult'
        type: array
    type: object
  model.BuildHistoryResult:
    properties:
      comment:
        type: string
      created:
        type: string
      createdBy:
        type: string
      emptyLayer:
        type: boolean
    type: object
  model.ChartInfoResult:
    properties:
      created:
        description: |-
          The created time of chart
          Required: true
        type: string
      deprecated:
        description: Flag to indicate if the chart is deprecated
        type: boolean
      home:
        description: The home website of chart
        type: string
      icon:
        description: The icon path of chart
        type: string
      latestVersion:
        description: latest version of chart
        type: string
      name:
        description: |-
          Name of chart
          Required: true
        type: string
      totalVersions:
        description: |-
          Total count of chart versions
          Required: true
        type: integer
      updated:
        description: The created time of chart
        type: string
    type: object
  model.ChartVersion:
    properties:
      apiVersion:
        description: |-
          The API version of this chart
          Required: true
        type: string
      appVersion:
        description: |-
          The version of the application enclosed in the chart
          Required: true
        type: string
      created:
        description: The created time of the chart entry
        type: string
      deprecated:
        description: Whether or not this chart is deprecated
        type: boolean
      description:
        description: A one-sentence description of chart
        type: string
      digest:
        description: The digest value of the chart entry
        type: string
      engine:
        description: |-
          The name of template engine
          Required: true
        type: string
      home:
        description: The URL to the relevant project page
        type: string
      icon:
        description: |-
          The URL to an icon file
          Required: true
        type: string
      keywords:
        description: A list of string keywords
        items:
          type: string
        type: array
      labels:
        description: labels
        items:
          $ref: '#/definitions/model.Label'
        type: array
      name:
        description: |-
          The name of the chart
          Required: true
        type: string
      removed:
        description: A flag to indicate if the chart entry is removed
        type: boolean
      sources:
        description: The URL to the source code of chart
        items:
          type: string
        type: array
      urls:
        description: The urls of the chart entry
        items:
          type: string
        type: array
      version:
        description: |-
          A SemVer 2 version of chart
          Required: true
        type: string
    type: object
  model.ChartVersionDetailsResult:
    properties:
      dependencies:
        description: dependencies
        items:
          $ref: '#/definitions/model.Dependency'
        type: array
      files:
        additionalProperties:
          type: string
        description: files
        type: object
      security:
        $ref: '#/definitions/model.SecurityReport'
        description: security
      values:
        additionalProperties: true
        description: values
        type: object
    type: object
  model.ChartVersionResult:
    properties:
      apiVersion:
        description: |-
          The API version of this chart
          Required: true
        type: string
      appVersion:
        description: |-
          The version of the application enclosed in the chart
          Required: true
        type: string
      created:
        description: The created time of the chart entry
        type: string
      deprecated:
        description: Whether or not this chart is deprecated
        type: boolean
      description:
        description: A one-sentence description of chart
        type: string
      digest:
        description: The digest value of the chart entry
        type: string
      engine:
        description: |-
          The name of template engine
          Required: true
        type: string
      home:
        description: The URL to the relevant project page
        type: string
      icon:
        description: |-
          The URL to an icon file
          Required: true
        type: string
      maintainers:
        description: maintainers
        items:
          type: string
        type: array
      name:
        description: |-
          The name of the chart
          Required: true
        type: string
      removed:
        description: A flag to indicate if the chart entry is removed
        type: boolean
      sources:
        description: The URL to the source code of chart
        items:
          type: string
        type: array
      urls:
        description: The urls of the chart entry
        items:
          type: string
        type: array
      version:
        description: |-
          A SemVer 2 version of chart
          Required: true
        type: string
    type: object
  model.CreateProjectMemberRequest:
    properties:
      memberUser:
        properties:
          userID:
            type: integer
          username:
            type: string
        type: object
      roleID:
        type: integer
    type: object
  model.CreateProjectRequest:
    properties:
      projectName:
        description: 命名空间名称.
        type: string
      public:
        description: The public status of the project. The valid values are "true",
          "false".
        type: string
    required:
    - projectName
    - public
    type: object
  model.CreateUserRequest:
    properties:
      comment:
        type: string
      email:
        type: string
      password:
        type: string
      realname:
        type: string
      username:
        type: string
    type: object
  model.Dependency:
    properties:
      name:
        description: |-
          The name of the chart denpendency
          Required: true
        type: string
      repository:
        description: The URL to the repository
        type: string
      version:
        description: |-
          The version of the chart dependency
          Required: true
        type: string
    type: object
  model.Filter:
    properties:
      type:
        description: The replication policy filter type.
        type: string
      value:
        description: The value of replication policy filter.
        type: string
    type: object
  model.InstanceDetail:
    properties:
      bucket:
        type: string
      info:
        $ref: '#/definitions/model.InstanceInfo'
      quota:
        $ref: '#/definitions/model.UserQuota'
      region:
        type: string
      statistic:
        $ref: '#/definitions/model.InstanceStatistic'
    type: object
  model.InstanceInfo:
    properties:
      createTime:
        type: string
      expireTime:
        type: string
      id:
        type: string
      instanceType:
        type: string
      name:
        type: string
      privateURL:
        type: string
      publicURL:
        type: string
      region:
        type: string
      status:
        type: string
    type: object
  model.InstanceStatistic:
    properties:
      chart:
        type: integer
      namespace:
        type: integer
      repo:
        type: integer
      storage:
        type: integer
    type: object
  model.Label:
    properties:
      color:
        description: The color of label.
        type: string
      creation_time:
        description: The creation time of label.
        type: string
      deleted:
        description: The label is deleted or not.
        type: boolean
      description:
        description: The description of label.
        type: string
      id:
        description: The ID of label.
        type: integer
      name:
        description: The name of label.
        type: string
      project_id:
        description: The project ID if the label is a project label.
        type: integer
      scope:
        description: The scope of label, g for global labels and p for project labels.
        type: string
      update_time:
        description: The update time of label.
        type: string
    type: object
  model.ListChartResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.ChartInfoResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListChartVersionResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.ChartVersionResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListProjectMemberResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.ProjectMemberResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListRegistryResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.RegistryResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListReplicationResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.PolicyResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListRepositoryResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.RepositoryResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.ListTagResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.TagResult'
        type: array
      pageNo:
        type: integer
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  model.PolicyRequest:
    properties:
      description:
        description: The description of the policy.
        type: string
      destProjectName:
        description: The destination namespace.
        type: string
      filters:
        description: |-
          The replication policy filter array.
          Example filters: [{"type": "name","value":"garenwen/virt-launcher"},{"type":"tag", "value":""}]
        items:
          $ref: '#/definitions/model.Filter'
        type: array
      name:
        description: The policy name.
        type: string
      override:
        description: Whether to override the resources on the destination registry.
        type: boolean
      srcRegistry:
        $ref: '#/definitions/model.SrcRegistry'
        description: The source registry.
      trigger:
        $ref: '#/definitions/model.TriggerReq'
        description: trigger
    type: object
  model.PolicyResult:
    properties:
      creationTime:
        description: The create time of the policy.
        type: string
      deletion:
        description: Whether to replicate the deletion operation.
        type: boolean
      description:
        description: The description of the policy.
        type: string
      destProjectName:
        description: The destination namespace.
        type: string
      destRegistry:
        $ref: '#/definitions/model.Registry'
        description: The destination registry.
      enabled:
        description: Whether the policy is enabled or not.
        type: boolean
      executionTimes:
        description: THe execution times of the policy
        type: integer
      filters:
        description: The replication policy filter array.
        items:
          $ref: '#/definitions/model.Filter'
        type: array
      id:
        description: The policy ID.
        type: integer
      name:
        description: The policy name.
        type: string
      override:
        description: Whether to override the resources on the destination registry.
        type: boolean
      srcRegistry:
        $ref: '#/definitions/model.Registry'
        description: The source registry.
      trigger:
        $ref: '#/definitions/model.Trigger'
        description: trigger
      updateTime:
        description: The update time of the policy.
        type: string
    type: object
  model.ProjectMemberResult:
    properties:
      entityID:
        type: integer
      entityName:
        type: string
      entityType:
        type: string
      id:
        type: integer
      projectID:
        type: integer
      roleID:
        type: integer
      roleName:
        type: string
    type: object
  model.ProjectResult:
    properties:
      autoScan:
        description: Whether scan images automatically when pushing. The valid values
          are "true", "false".
        type: string
      chartCount:
        description: The total number of charts under this project.
        type: integer
      creationTime:
        description: |-
          The creation time of the project.
          Format: date-time
        type: string
      projectId:
        description: Project ID
        type: integer
      projectName:
        description: The name of the project.
        type: string
      public:
        description: The public status of the project. The valid values are "true",
          "false".
        type: string
      repoCount:
        description: The number of the repositories under this project.
        type: integer
      updateTime:
        description: |-
          The update time of the project.
          Format: date-time
        type: string
    type: object
  model.Registry:
    properties:
      creationTime:
        description: The create time of the policy.
        type: string
      credential:
        $ref: '#/definitions/model.RegistryCredential'
        description: credential
      description:
        description: Description of the registry.
        type: string
      id:
        description: The registry ID.
        type: integer
      insecure:
        description: Whether or not the certificate will be verified when Harbor tries
          to access the server.
        type: boolean
      name:
        description: The registry name.
        type: string
      region:
        description: The region of the registry.
        type: string
      status:
        description: Health status of the registry.
        type: string
      type:
        description: Type of the registry, e.g. 'harbor'.
        type: string
      updateTime:
        description: The update time of the policy.
        type: string
      url:
        description: The registry URL string.
        type: string
    type: object
  model.RegistryCredential:
    properties:
      accessKey:
        description: Access key, e.g. user name when credential type is 'basic'.
        type: string
      accessSecret:
        description: Access secret, e.g. password when credential type is 'basic'.
        type: string
      type:
        description: Credential type, such as 'basic', 'oauth'.
        type: string
    type: object
  model.RegistryRequest:
    properties:
      credential:
        $ref: '#/definitions/model.RegistryCredential'
        description: credential
      description:
        description: Description of the registry.
        type: string
      insecure:
        description: Whether or not the certificate will be verified when Harbor tries
          to access the server.
        type: boolean
      name:
        description: The registry name.
        type: string
      type:
        description: Type of the registry, e.g. 'harbor'.
        type: string
      url:
        description: The registry URL string.
        type: string
    required:
    - type
    type: object
  model.RegistryResult:
    properties:
      creationTime:
        description: creation time
        type: string
      credential:
        $ref: '#/definitions/model.RegistryCredential'
        description: credential
      description:
        description: description
        type: string
      id:
        description: id
        type: integer
      insecure:
        description: insecure
        type: boolean
      name:
        description: name
        type: string
      status:
        description: status
        type: string
      type:
        description: type
        type: string
      updateTime:
        description: update time
        type: string
      url:
        description: url
        type: string
    type: object
  model.RepositoryResult:
    properties:
      creationTime:
        description: |-
          The creation time of the repository
          Format: date-time
        type: string
      description:
        description: The description of the repository
        type: string
      privateRepositoryPath:
        description: The path of private repository
        type: string
      projectName:
        description: The project name the repository
        type: string
      public:
        description: project is public or not
        type: string
      pullCount:
        description: The count that the artifact inside the repository pulled
        type: integer
      repositoryName:
        description: The name of the repository
        type: string
      repositoryPath:
        description: The path of the repository
        type: string
      tagCount:
        description: The count of the tags inside the repository
        type: integer
      updateTime:
        description: |-
          The update time of the repository
          Format: date-time
        type: string
    type: object
  model.ScanOverview:
    properties:
      description:
        type: string
      fixVersion:
        type: string
      id:
        type: string
      links:
        items:
          type: string
        type: array
      package:
        type: string
      severity:
        type: string
      version:
        type: string
    type: object
  model.ScanOverviewResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/model.ScanOverview'
        type: array
      lastScanTime:
        type: string
      pageNo:
        type: integer
      pageSize:
        type: integer
      summary:
        additionalProperties:
          type: integer
        type: object
      total:
        type: integer
    type: object
  model.SecurityReport:
    properties:
      prov_file:
        description: The URL of the provance file
        type: string
      signed:
        description: A flag to indicate if the chart is signed
        type: boolean
    type: object
  model.Settings:
    properties:
      cron:
        description: The cron string for scheduled trigger
        type: string
    type: object
  model.SrcRegistry:
    properties:
      id:
        description: The registry ID.
        type: integer
    type: object
  model.TagResult:
    properties:
      acceleratorStatus:
        description: The tag of repository accelerate status
        type: string
      architecture:
        description: Architecture The architecture of repository
        type: string
      author:
        description: Author
        type: string
      digest:
        description: The digest of the artifact
        type: string
      os:
        description: OS
        type: string
      projectId:
        description: The ID of the project that the artifact belongs to
        type: integer
      pullTime:
        description: |-
          The latest pull time of the tag
          Format: date-time
        type: string
      pushTime:
        description: |-
          The push time of the tag
          Format: date-time
        type: string
      repositoryId:
        description: The ID of the repository that the artifact belongs to
        type: integer
      scanOverview:
        $ref: '#/definitions/model.TagScanOverview'
        description: The scan overview
      size:
        description: The size of the artifact
        type: integer
      tagName:
        description: The name of the tag
        type: string
      type:
        description: The type of the artifact, e.g. image, chart, etc
        type: string
    type: object
  model.TagScanOverview:
    properties:
      endTime:
        description: |-
          The end time of the scan process that generating report
          Example: 2006-01-02T15:04:05
          Format: date-time
        type: string
      fixable:
        description: |-
          The number of the fixable vulnerabilities
          Example: 100
        type: integer
      reportId:
        description: |-
          id of the native scan report
          Example: 5f62c830-f996-11e9-957f-0242c0a89008
        type: string
      scanStatus:
        description: |-
          The status of the report generating process
          Example: Success
        type: string
      severity:
        description: 漏洞等级 Critical 危及 High 严重  Medium 中等 Low 较低
        type: string
      startTime:
        description: |-
          The start time of the scan process that generating report
          Example: 2006-01-02T14:04:05
          Format: date-time
        type: string
      summary:
        additionalProperties:
          type: integer
        description: |-
          Numbers of the vulnerabilities with different severity
          Example: {"Critical":5,"High":5}
        type: object
      total:
        description: |-
          The total number of the found vulnerabilities
          Example: 500
        type: integer
    type: object
  model.Trigger:
    properties:
      triggerSettings:
        $ref: '#/definitions/model.Settings'
        description: trigger settings
      type:
        description: The replication policy trigger type. The valid values are manual,
          event_based and scheduled.
        type: string
    type: object
  model.TriggerReq:
    properties:
      type:
        description: |-
          The replication policy trigger type. The valid values are manual, event_based and scheduled.
          镜像迁移只填写这个 manual
          实例同步可以写 manual event_base
        type: string
    type: object
  model.UpdateRepositoryRequest:
    properties:
      description:
        description: The description of the repository
        type: string
    type: object
  model.UpdateUserPasswordRequest:
    properties:
      newPassword:
        type: string
      oldPassword:
        type: string
    type: object
  model.UpdateUserSysadminRequest:
    properties:
      sysadminFlag:
        type: boolean
    type: object
  model.UserQuota:
    properties:
      chart:
        type: integer
      namespace:
        type: integer
      repo:
        type: integer
    type: object
  model.UserResult:
    properties:
      id:
        description: User ID
        type: integer
    type: object
host: ccr.baidubce.com
info:
  contact:
    email: <EMAIL>
    name: duzhanwei,wenmanxiang
  description: CCR Service 提供 RESTFUL 风格 API, 对接 Console 及 OpenAPI 请求
  title: CCR Service API
  version: 0.0.1
paths:
  /instances/{instanceId}:
    get:
      consumes:
      - application/json
      description: 获取CCR实例
      parameters:
      - description: 实例id
        in: path
        name: instanceId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.InstanceDetail'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取CCR实例
      tags:
      - instance
  /instances/{instanceId}/projects:
    post:
      consumes:
      - application/json
      description: 创建命名空间
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: create project body
        in: body
        name: project
        required: true
        schema:
          $ref: '#/definitions/model.CreateProjectRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ProjectResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建命名空间
      tags:
      - project
  /instances/{instanceId}/projects/{projectName}:
    delete:
      consumes:
      - application/json
      description: 删除命名空间
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除命名空间
      tags:
      - project
    get:
      consumes:
      - application/json
      description: 通过命名空间名称projectName查询命名空间
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ProjectResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 通过命名空间名称projectName查询命名空间
      tags:
      - project
  /instances/{instanceId}/projects/{projectName}/charts:
    delete:
      consumes:
      - application/json
      description: 批量删除helm chart
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除helm chart
      tags:
      - chart
    get:
      consumes:
      - application/json
      description: 查询 helm chart 列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称
        in: query
        name: chartName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListChartResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询 helm chart 列表
      tags:
      - chart
    post:
      consumes:
      - multipart/form-data
      description: 上传helm chart 文件
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: The chart file
        in: formData
        name: chart
        required: true
        type: file
      - description: The prov file
        in: formData
        name: prov
        type: file
      produces:
      - multipart/form-data
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 上传helm chart 文件
      tags:
      - chart
  /instances/{instanceId}/projects/{projectName}/charts/{chartName}:
    delete:
      consumes:
      - application/json
      description: 删除 helm chart
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称
        in: path
        name: chartName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            items:
              $ref: '#/definitions/model.ChartVersion'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除 helm chart
      tags:
      - chart
  /instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions:
    delete:
      consumes:
      - application/json
      description: 批量删除helm chart版本
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart 名称
        in: path
        name: chartName
        required: true
        type: string
      - description: helm chart版本名称数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除helm chart版本
      tags:
      - chart
    get:
      consumes:
      - application/json
      description: 查询chart version 列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称
        in: path
        name: chartName
        required: true
        type: string
      - description: helm chart版本
        in: query
        name: chartVersion
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListChartVersionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询chart version 列表
      tags:
      - chart
  /instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions/{chartVersion}:
    delete:
      consumes:
      - application/json
      description: 删除helm chart版本
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称
        in: path
        name: chartName
        required: true
        type: string
      - description: helm chart版本
        in: path
        name: chartVersion
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除helm chart版本
      tags:
      - chart
    get:
      consumes:
      - application/json
      description: 获取chart version 详情
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: helm chart名称
        in: path
        name: chartName
        required: true
        type: string
      - description: helm chart版本
        in: path
        name: chartVersion
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ChartVersionDetailsResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取chart version 详情
      tags:
      - chart
  /instances/{instanceId}/projects/{projectName}/members:
    get:
      consumes:
      - application/json
      description: 查询命名空间下成员
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListProjectMemberResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询命名空间下成员
      tags:
      - project
    post:
      consumes:
      - application/json
      description: 创建命名空间成员
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间ID
        in: path
        name: projectName
        required: true
        type: string
      - description: create project body
        in: body
        name: member
        required: true
        schema:
          $ref: '#/definitions/model.CreateProjectMemberRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建命名空间成员
      tags:
      - project
  /instances/{instanceId}/projects/{projectName}/members/{username}:
    delete:
      consumes:
      - application/json
      description: 创建命名空间成员
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 用户名
        in: path
        name: username
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建命名空间成员
      tags:
      - project
  /instances/{instanceId}/projects/{projectName}/repositories:
    delete:
      consumes:
      - application/json
      description: 批量删除镜像仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除镜像仓库
      tags:
      - repository
    get:
      consumes:
      - application/json
      description: 查询镜像仓库列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: query
        name: repositoryName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListRepositoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询镜像仓库列表
      tags:
      - repository
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}:
    delete:
      consumes:
      - application/json
      description: 删除单个镜像仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除单个镜像仓库
      tags:
      - repository
    get:
      consumes:
      - application/json
      description: 通过镜像仓库名称查询镜像仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.RepositoryResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 通过镜像仓库名称查询镜像仓库
      tags:
      - repository
    put:
      consumes:
      - application/json
      description: 修改单个镜像仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: update repository request
        in: body
        name: repository
        required: true
        schema:
          $ref: '#/definitions/model.UpdateRepositoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.RepositoryResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 修改单个镜像仓库
      tags:
      - repository
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags:
    delete:
      consumes:
      - application/json
      description: 批量删除镜像tag
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称数组
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/model.BatchDeleteRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 批量删除镜像tag
      tags:
      - tag
    get:
      consumes:
      - application/json
      description: 查询镜像仓库tag列表
      parameters:
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: tag名称
        in: query
        name: tagName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListTagResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询镜像仓库tag列表
      tags:
      - tag
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}:
    delete:
      consumes:
      - application/json
      description: 删除单个镜像tag
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除单个镜像tag
      tags:
      - tag
    get:
      consumes:
      - application/json
      description: 查询单个单个镜像tag
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.TagResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询单个单个镜像tag
      tags:
      - tag
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/buildhistory:
    get:
      consumes:
      - application/json
      description: 查询构建历史
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.BuildHistoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询构建历史
      tags:
      - tag
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scan:
    post:
      consumes:
      - application/json
      description: 镜像扫描
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 镜像扫描
      tags:
      - tag
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scan/{reportId}/log:
    get:
      consumes:
      - text/plain
      description: 镜像扫描日志
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      - description: 扫描日志ID
        in: path
        name: reportId
        required: true
        type: string
      produces:
      - text/plain
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 镜像扫描日志
      tags:
      - tag
  /instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scanoverview:
    get:
      consumes:
      - application/json
      description: 查询镜像漏洞
      parameters:
      - description: 命名空间名称
        in: path
        name: projectName
        required: true
        type: string
      - description: 镜像仓库名称
        in: path
        name: repositoryName
        required: true
        type: string
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: tag名称
        in: path
        name: tagName
        required: true
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ScanOverviewResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 查询镜像漏洞
      tags:
      - tag
  /instances/{instanceId}/registries:
    get:
      consumes:
      - application/json
      description: 获取远程仓库列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 远程仓库名称
        in: query
        name: registryName
        type: string
      - description: 远程仓库类型
        in: query
        name: registryType
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListRegistryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取远程仓库列表
      tags:
      - registry
    post:
      consumes:
      - application/json
      description: 创建新的远程仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: create registry body
        in: body
        name: registry
        required: true
        schema:
          $ref: '#/definitions/model.RegistryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建新的远程仓库
      tags:
      - registry
  /instances/{instanceId}/registries/{registryId}:
    delete:
      consumes:
      - application/json
      description: 删除远程仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 远程仓库ID
        in: path
        name: registryId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除远程仓库
      tags:
      - registry
    get:
      consumes:
      - application/json
      description: 通过registryId查询远程仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 镜像远程仓库ID
        in: path
        name: registryId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.RegistryResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 通过registryId查询远程仓库
      tags:
      - registry
    put:
      consumes:
      - application/json
      description: 修改单个远程仓库
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 远程仓库ID
        in: path
        name: registryId
        required: true
        type: string
      - description: update registry request
        in: body
        name: registry
        required: true
        schema:
          $ref: '#/definitions/model.RegistryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.RegistryResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 修改单个远程仓库
      tags:
      - registry
  /instances/{instanceId}/registries/ping:
    post:
      consumes:
      - application/json
      description: 检查远程仓库健康状态
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: check registry health request
        in: body
        name: registryHealth
        required: true
        schema:
          $ref: '#/definitions/model.RegistryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 检查远程仓库健康状态
      tags:
      - registry
  /instances/{instanceId}/replications:
    get:
      consumes:
      - application/json
      description: 获取策略列表
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 镜像迁移策略名称
        in: query
        name: policyName
        type: string
      - default: 1
        description: 当前页
        in: query
        name: pageNo
        required: true
        type: integer
      - default: 10
        description: 每页记录数
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.ListReplicationResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取策略列表
      tags:
      - replication
    post:
      consumes:
      - application/json
      description: 创建镜像迁移策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: create policy body
        in: body
        name: policy
        required: true
        schema:
          $ref: '#/definitions/model.PolicyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建镜像迁移策略
      tags:
      - replication
  /instances/{instanceId}/replications/{policyId}:
    delete:
      consumes:
      - application/json
      description: 删除镜像迁移策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 策略ID
        in: path
        name: policyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除镜像迁移策略
      tags:
      - replication
    get:
      consumes:
      - application/json
      description: 通过policyId查询策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 镜像迁移策略名称ID
        in: path
        name: policyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.PolicyResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 通过policyId查询策略
      tags:
      - replication
    put:
      consumes:
      - application/json
      description: 更新镜像迁移策略
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 策略ID
        in: path
        name: policyId
        required: true
        type: string
      - description: update policy body
        in: body
        name: policy
        required: true
        schema:
          $ref: '#/definitions/model.PolicyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 更新镜像迁移策略
      tags:
      - replication
  /instances/{instanceId}/users:
    post:
      consumes:
      - application/json
      description: 创建用户
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: create user body
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/model.CreateUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Success
          schema:
            $ref: '#/definitions/model.UserResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 创建用户
      tags:
      - user
  /instances/{instanceId}/users/{username}:
    delete:
      consumes:
      - application/json
      description: 删除用户
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 用户ID
        in: path
        name: username
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 删除用户
      tags:
      - user
    get:
      consumes:
      - application/json
      description: 获取用户
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 用户名称
        in: path
        name: username
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.UserResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取用户
      tags:
      - user
  /instances/{instanceId}/users/{username}/password:
    put:
      consumes:
      - application/json
      description: 更新用户密码
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 用户ID
        in: path
        name: username
        required: true
        type: string
      - description: Password to be updated, the attribute 'OldPassword' is optional
          when the API is called by the system administrator.
        in: body
        name: password
        required: true
        schema:
          $ref: '#/definitions/model.UpdateUserPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.UserResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 更新用户密码
      tags:
      - user
  /instances/{instanceId}/users/{username}/sysadmin:
    put:
      consumes:
      - application/json
      description: 设置用户为管理员
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      - description: 用户ID
        in: path
        name: username
        required: true
        type: string
      - description: Toggle a user to admin or not
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/model.UpdateUserSysadminRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            $ref: '#/definitions/model.UserResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 设置用户为管理员
      tags:
      - user
  /instances/{instanceId}/users/current:
    get:
      consumes:
      - application/json
      description: 获取当前用户
      parameters:
      - description: 实例ID
        in: path
        name: instanceId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: success
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/bce.BceServiceError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/bce.BceServiceError'
      summary: 获取当前用户
      tags:
      - user
swagger: "2.0"
