// Package docs GENERATED BY THE COMMAND ABOVE; DO NOT EDIT
// This file was generated by swaggo/swag
package docs

import (
	"bytes"
	"encoding/json"
	"strings"
	"text/template"

	"github.com/swaggo/swag"
)

var doc = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {
            "name": "duzhanwei,wenmanxiang",
            "email": "<EMAIL>"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/instances/{instanceId}": {
            "get": {
                "description": "获取CCR实例",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "instance"
                ],
                "summary": "获取CCR实例",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例id",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.InstanceDetail"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects": {
            "post": {
                "description": "创建命名空间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "project"
                ],
                "summary": "创建命名空间",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "create project body",
                        "name": "project",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.CreateProjectRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ProjectResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}": {
            "get": {
                "description": "通过命名空间名称projectName查询命名空间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "project"
                ],
                "summary": "通过命名空间名称projectName查询命名空间",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ProjectResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除命名空间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "project"
                ],
                "summary": "删除命名空间",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/charts": {
            "get": {
                "description": "查询 helm chart 列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chart"
                ],
                "summary": "查询 helm chart 列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "helm chart名称",
                        "name": "chartName",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "当前页",
                        "name": "pageNo",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页记录数",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ListChartResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "post": {
                "description": "上传helm chart 文件",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "multipart/form-data"
                ],
                "tags": [
                    "chart"
                ],
                "summary": "上传helm chart 文件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "The chart file",
                        "name": "chart",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "The prov file",
                        "name": "prov",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "批量删除helm chart",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chart"
                ],
                "summary": "批量删除helm chart",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "helm chart名称数组",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.BatchDeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/charts/{chartName}": {
            "delete": {
                "description": "删除 helm chart",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chart"
                ],
                "summary": "删除 helm chart",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "helm chart名称",
                        "name": "chartName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/model.ChartVersion"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions": {
            "get": {
                "description": "查询chart version 列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chart"
                ],
                "summary": "查询chart version 列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "helm chart名称",
                        "name": "chartName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "helm chart版本",
                        "name": "chartVersion",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "当前页",
                        "name": "pageNo",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页记录数",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ListChartVersionResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "批量删除helm chart版本",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chart"
                ],
                "summary": "批量删除helm chart版本",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "helm chart 名称",
                        "name": "chartName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "helm chart版本名称数组",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.BatchDeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/charts/{chartName}/versions/{chartVersion}": {
            "get": {
                "description": "获取chart version 详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chart"
                ],
                "summary": "获取chart version 详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "helm chart名称",
                        "name": "chartName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "helm chart版本",
                        "name": "chartVersion",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ChartVersionDetailsResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除helm chart版本",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chart"
                ],
                "summary": "删除helm chart版本",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "helm chart名称",
                        "name": "chartName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "helm chart版本",
                        "name": "chartVersion",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/members": {
            "get": {
                "description": "查询命名空间下成员",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "project"
                ],
                "summary": "查询命名空间下成员",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ListProjectMemberResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "post": {
                "description": "创建命名空间成员",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "project"
                ],
                "summary": "创建命名空间成员",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间ID",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "create project body",
                        "name": "member",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.CreateProjectMemberRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/members/{username}": {
            "delete": {
                "description": "创建命名空间成员",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "project"
                ],
                "summary": "创建命名空间成员",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/repositories": {
            "get": {
                "description": "查询镜像仓库列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "repository"
                ],
                "summary": "查询镜像仓库列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "当前页",
                        "name": "pageNo",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页记录数",
                        "name": "pageSize",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ListRepositoryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "批量删除镜像仓库",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "repository"
                ],
                "summary": "批量删除镜像仓库",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "镜像仓库名称数组",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.BatchDeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}": {
            "get": {
                "description": "通过镜像仓库名称查询镜像仓库",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "repository"
                ],
                "summary": "通过镜像仓库名称查询镜像仓库",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.RepositoryResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "put": {
                "description": "修改单个镜像仓库",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "repository"
                ],
                "summary": "修改单个镜像仓库",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "update repository request",
                        "name": "repository",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UpdateRepositoryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.RepositoryResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除单个镜像仓库",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "repository"
                ],
                "summary": "删除单个镜像仓库",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags": {
            "get": {
                "description": "查询镜像仓库tag列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "查询镜像仓库tag列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "tag名称",
                        "name": "tagName",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "当前页",
                        "name": "pageNo",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页记录数",
                        "name": "pageSize",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ListTagResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "批量删除镜像tag",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "批量删除镜像tag",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "tag名称数组",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.BatchDeleteRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}": {
            "get": {
                "description": "查询单个单个镜像tag",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "查询单个单个镜像tag",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "tag名称",
                        "name": "tagName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.TagResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除单个镜像tag",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "删除单个镜像tag",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "tag名称",
                        "name": "tagName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/buildhistory": {
            "get": {
                "description": "查询构建历史",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "查询构建历史",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "tag名称",
                        "name": "tagName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.BuildHistoryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scan": {
            "post": {
                "description": "镜像扫描",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "镜像扫描",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "tag名称",
                        "name": "tagName",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scan/{reportId}/log": {
            "get": {
                "description": "镜像扫描日志",
                "consumes": [
                    "text/plain"
                ],
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "镜像扫描日志",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "tag名称",
                        "name": "tagName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "扫描日志ID",
                        "name": "reportId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/projects/{projectName}/repositories/{repositoryName}/tags/{tagName}/scanoverview": {
            "get": {
                "description": "查询镜像漏洞",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tag"
                ],
                "summary": "查询镜像漏洞",
                "parameters": [
                    {
                        "type": "string",
                        "description": "命名空间名称",
                        "name": "projectName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像仓库名称",
                        "name": "repositoryName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "tag名称",
                        "name": "tagName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "当前页",
                        "name": "pageNo",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页记录数",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ScanOverviewResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/registries": {
            "get": {
                "description": "获取远程仓库列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "registry"
                ],
                "summary": "获取远程仓库列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "远程仓库名称",
                        "name": "registryName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "远程仓库类型",
                        "name": "registryType",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "当前页",
                        "name": "pageNo",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页记录数",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ListRegistryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新的远程仓库",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "registry"
                ],
                "summary": "创建新的远程仓库",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "create registry body",
                        "name": "registry",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.RegistryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/registries/ping": {
            "post": {
                "description": "检查远程仓库健康状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "registry"
                ],
                "summary": "检查远程仓库健康状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "check registry health request",
                        "name": "registryHealth",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.RegistryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/registries/{registryId}": {
            "get": {
                "description": "通过registryId查询远程仓库",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "registry"
                ],
                "summary": "通过registryId查询远程仓库",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像远程仓库ID",
                        "name": "registryId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.RegistryResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "put": {
                "description": "修改单个远程仓库",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "registry"
                ],
                "summary": "修改单个远程仓库",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "远程仓库ID",
                        "name": "registryId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "update registry request",
                        "name": "registry",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.RegistryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.RegistryResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除远程仓库",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "registry"
                ],
                "summary": "删除远程仓库",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "远程仓库ID",
                        "name": "registryId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/replications": {
            "get": {
                "description": "获取策略列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "replication"
                ],
                "summary": "获取策略列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像迁移策略名称",
                        "name": "policyName",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "当前页",
                        "name": "pageNo",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页记录数",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.ListReplicationResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "post": {
                "description": "创建镜像迁移策略",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "replication"
                ],
                "summary": "创建镜像迁移策略",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "create policy body",
                        "name": "policy",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.PolicyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/replications/{policyId}": {
            "get": {
                "description": "通过policyId查询策略",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "replication"
                ],
                "summary": "通过policyId查询策略",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "镜像迁移策略名称ID",
                        "name": "policyId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.PolicyResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "put": {
                "description": "更新镜像迁移策略",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "replication"
                ],
                "summary": "更新镜像迁移策略",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "策略ID",
                        "name": "policyId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "update policy body",
                        "name": "policy",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.PolicyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除镜像迁移策略",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "replication"
                ],
                "summary": "删除镜像迁移策略",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "策略ID",
                        "name": "policyId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/users": {
            "post": {
                "description": "创建用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "创建用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "create user body",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.CreateUserRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.UserResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/users/current": {
            "get": {
                "description": "获取当前用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "获取当前用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "success"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/users/{username}": {
            "get": {
                "description": "获取用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "获取用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "用户名称",
                        "name": "username",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.UserResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "删除用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "username",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/users/{username}/password": {
            "put": {
                "description": "更新用户密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "更新用户密码",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "username",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Password to be updated, the attribute 'OldPassword' is optional when the API is called by the system administrator.",
                        "name": "password",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UpdateUserPasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.UserResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        },
        "/instances/{instanceId}/users/{username}/sysadmin": {
            "put": {
                "description": "设置用户为管理员",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "user"
                ],
                "summary": "设置用户为管理员",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实例ID",
                        "name": "instanceId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "username",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Toggle a user to admin or not",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.UpdateUserSysadminRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "$ref": "#/definitions/model.UserResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/bce.BceServiceError"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "bce.BceServiceError": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "requestId": {
                    "type": "string"
                },
                "statusCode": {
                    "type": "integer"
                }
            }
        },
        "model.BatchDeleteRequest": {
            "type": "object",
            "required": [
                "items"
            ],
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "model.BuildHistoryResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.BuildHistoryResult"
                    }
                }
            }
        },
        "model.BuildHistoryResult": {
            "type": "object",
            "properties": {
                "comment": {
                    "type": "string"
                },
                "created": {
                    "type": "string"
                },
                "createdBy": {
                    "type": "string"
                },
                "emptyLayer": {
                    "type": "boolean"
                }
            }
        },
        "model.ChartInfoResult": {
            "type": "object",
            "properties": {
                "created": {
                    "description": "The created time of chart\nRequired: true",
                    "type": "string"
                },
                "deprecated": {
                    "description": "Flag to indicate if the chart is deprecated",
                    "type": "boolean"
                },
                "home": {
                    "description": "The home website of chart",
                    "type": "string"
                },
                "icon": {
                    "description": "The icon path of chart",
                    "type": "string"
                },
                "latestVersion": {
                    "description": "latest version of chart",
                    "type": "string"
                },
                "name": {
                    "description": "Name of chart\nRequired: true",
                    "type": "string"
                },
                "totalVersions": {
                    "description": "Total count of chart versions\nRequired: true",
                    "type": "integer"
                },
                "updated": {
                    "description": "The created time of chart",
                    "type": "string"
                }
            }
        },
        "model.ChartVersion": {
            "type": "object",
            "properties": {
                "apiVersion": {
                    "description": "The API version of this chart\nRequired: true",
                    "type": "string"
                },
                "appVersion": {
                    "description": "The version of the application enclosed in the chart\nRequired: true",
                    "type": "string"
                },
                "created": {
                    "description": "The created time of the chart entry",
                    "type": "string"
                },
                "deprecated": {
                    "description": "Whether or not this chart is deprecated",
                    "type": "boolean"
                },
                "description": {
                    "description": "A one-sentence description of chart",
                    "type": "string"
                },
                "digest": {
                    "description": "The digest value of the chart entry",
                    "type": "string"
                },
                "engine": {
                    "description": "The name of template engine\nRequired: true",
                    "type": "string"
                },
                "home": {
                    "description": "The URL to the relevant project page",
                    "type": "string"
                },
                "icon": {
                    "description": "The URL to an icon file\nRequired: true",
                    "type": "string"
                },
                "keywords": {
                    "description": "A list of string keywords",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "labels": {
                    "description": "labels",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.Label"
                    }
                },
                "name": {
                    "description": "The name of the chart\nRequired: true",
                    "type": "string"
                },
                "removed": {
                    "description": "A flag to indicate if the chart entry is removed",
                    "type": "boolean"
                },
                "sources": {
                    "description": "The URL to the source code of chart",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "urls": {
                    "description": "The urls of the chart entry",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "version": {
                    "description": "A SemVer 2 version of chart\nRequired: true",
                    "type": "string"
                }
            }
        },
        "model.ChartVersionDetailsResult": {
            "type": "object",
            "properties": {
                "dependencies": {
                    "description": "dependencies",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.Dependency"
                    }
                },
                "files": {
                    "description": "files",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "security": {
                    "description": "security",
                    "$ref": "#/definitions/model.SecurityReport"
                },
                "values": {
                    "description": "values",
                    "type": "object",
                    "additionalProperties": true
                }
            }
        },
        "model.ChartVersionResult": {
            "type": "object",
            "properties": {
                "apiVersion": {
                    "description": "The API version of this chart\nRequired: true",
                    "type": "string"
                },
                "appVersion": {
                    "description": "The version of the application enclosed in the chart\nRequired: true",
                    "type": "string"
                },
                "created": {
                    "description": "The created time of the chart entry",
                    "type": "string"
                },
                "deprecated": {
                    "description": "Whether or not this chart is deprecated",
                    "type": "boolean"
                },
                "description": {
                    "description": "A one-sentence description of chart",
                    "type": "string"
                },
                "digest": {
                    "description": "The digest value of the chart entry",
                    "type": "string"
                },
                "engine": {
                    "description": "The name of template engine\nRequired: true",
                    "type": "string"
                },
                "home": {
                    "description": "The URL to the relevant project page",
                    "type": "string"
                },
                "icon": {
                    "description": "The URL to an icon file\nRequired: true",
                    "type": "string"
                },
                "maintainers": {
                    "description": "maintainers",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "description": "The name of the chart\nRequired: true",
                    "type": "string"
                },
                "removed": {
                    "description": "A flag to indicate if the chart entry is removed",
                    "type": "boolean"
                },
                "sources": {
                    "description": "The URL to the source code of chart",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "urls": {
                    "description": "The urls of the chart entry",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "version": {
                    "description": "A SemVer 2 version of chart\nRequired: true",
                    "type": "string"
                }
            }
        },
        "model.CreateProjectMemberRequest": {
            "type": "object",
            "properties": {
                "memberUser": {
                    "type": "object",
                    "properties": {
                        "userID": {
                            "type": "integer"
                        },
                        "username": {
                            "type": "string"
                        }
                    }
                },
                "roleID": {
                    "type": "integer"
                }
            }
        },
        "model.CreateProjectRequest": {
            "type": "object",
            "required": [
                "projectName",
                "public"
            ],
            "properties": {
                "projectName": {
                    "description": "命名空间名称.",
                    "type": "string"
                },
                "public": {
                    "description": "The public status of the project. The valid values are \"true\", \"false\".",
                    "type": "string"
                }
            }
        },
        "model.CreateUserRequest": {
            "type": "object",
            "properties": {
                "comment": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "realname": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "model.Dependency": {
            "type": "object",
            "properties": {
                "name": {
                    "description": "The name of the chart denpendency\nRequired: true",
                    "type": "string"
                },
                "repository": {
                    "description": "The URL to the repository",
                    "type": "string"
                },
                "version": {
                    "description": "The version of the chart dependency\nRequired: true",
                    "type": "string"
                }
            }
        },
        "model.Filter": {
            "type": "object",
            "properties": {
                "type": {
                    "description": "The replication policy filter type.",
                    "type": "string"
                },
                "value": {
                    "description": "The value of replication policy filter.",
                    "type": "string"
                }
            }
        },
        "model.InstanceDetail": {
            "type": "object",
            "properties": {
                "bucket": {
                    "type": "string"
                },
                "info": {
                    "$ref": "#/definitions/model.InstanceInfo"
                },
                "quota": {
                    "$ref": "#/definitions/model.UserQuota"
                },
                "region": {
                    "type": "string"
                },
                "statistic": {
                    "$ref": "#/definitions/model.InstanceStatistic"
                }
            }
        },
        "model.InstanceInfo": {
            "type": "object",
            "properties": {
                "createTime": {
                    "type": "string"
                },
                "expireTime": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "instanceType": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "privateURL": {
                    "type": "string"
                },
                "publicURL": {
                    "type": "string"
                },
                "region": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "model.InstanceStatistic": {
            "type": "object",
            "properties": {
                "chart": {
                    "type": "integer"
                },
                "namespace": {
                    "type": "integer"
                },
                "repo": {
                    "type": "integer"
                },
                "storage": {
                    "type": "integer"
                }
            }
        },
        "model.Label": {
            "type": "object",
            "properties": {
                "color": {
                    "description": "The color of label.",
                    "type": "string"
                },
                "creation_time": {
                    "description": "The creation time of label.",
                    "type": "string"
                },
                "deleted": {
                    "description": "The label is deleted or not.",
                    "type": "boolean"
                },
                "description": {
                    "description": "The description of label.",
                    "type": "string"
                },
                "id": {
                    "description": "The ID of label.",
                    "type": "integer"
                },
                "name": {
                    "description": "The name of label.",
                    "type": "string"
                },
                "project_id": {
                    "description": "The project ID if the label is a project label.",
                    "type": "integer"
                },
                "scope": {
                    "description": "The scope of label, g for global labels and p for project labels.",
                    "type": "string"
                },
                "update_time": {
                    "description": "The update time of label.",
                    "type": "string"
                }
            }
        },
        "model.ListChartResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.ChartInfoResult"
                    }
                },
                "pageNo": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "model.ListChartVersionResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.ChartVersionResult"
                    }
                },
                "pageNo": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "model.ListProjectMemberResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.ProjectMemberResult"
                    }
                },
                "pageNo": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "model.ListRegistryResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.RegistryResult"
                    }
                },
                "pageNo": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "model.ListReplicationResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.PolicyResult"
                    }
                },
                "pageNo": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "model.ListRepositoryResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.RepositoryResult"
                    }
                },
                "pageNo": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "model.ListTagResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.TagResult"
                    }
                },
                "pageNo": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "model.PolicyRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "The description of the policy.",
                    "type": "string"
                },
                "destProjectName": {
                    "description": "The destination namespace.",
                    "type": "string"
                },
                "filters": {
                    "description": "The replication policy filter array.\nExample filters: [{\"type\": \"name\",\"value\":\"garenwen/virt-launcher\"},{\"type\":\"tag\", \"value\":\"\"}]",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.Filter"
                    }
                },
                "name": {
                    "description": "The policy name.",
                    "type": "string"
                },
                "override": {
                    "description": "Whether to override the resources on the destination registry.",
                    "type": "boolean"
                },
                "srcRegistry": {
                    "description": "The source registry.",
                    "$ref": "#/definitions/model.SrcRegistry"
                },
                "trigger": {
                    "description": "trigger",
                    "$ref": "#/definitions/model.TriggerReq"
                }
            }
        },
        "model.PolicyResult": {
            "type": "object",
            "properties": {
                "creationTime": {
                    "description": "The create time of the policy.",
                    "type": "string"
                },
                "deletion": {
                    "description": "Whether to replicate the deletion operation.",
                    "type": "boolean"
                },
                "description": {
                    "description": "The description of the policy.",
                    "type": "string"
                },
                "destProjectName": {
                    "description": "The destination namespace.",
                    "type": "string"
                },
                "destRegistry": {
                    "description": "The destination registry.",
                    "$ref": "#/definitions/model.Registry"
                },
                "enabled": {
                    "description": "Whether the policy is enabled or not.",
                    "type": "boolean"
                },
                "executionTimes": {
                    "description": "THe execution times of the policy",
                    "type": "integer"
                },
                "filters": {
                    "description": "The replication policy filter array.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.Filter"
                    }
                },
                "id": {
                    "description": "The policy ID.",
                    "type": "integer"
                },
                "name": {
                    "description": "The policy name.",
                    "type": "string"
                },
                "override": {
                    "description": "Whether to override the resources on the destination registry.",
                    "type": "boolean"
                },
                "srcRegistry": {
                    "description": "The source registry.",
                    "$ref": "#/definitions/model.Registry"
                },
                "trigger": {
                    "description": "trigger",
                    "$ref": "#/definitions/model.Trigger"
                },
                "updateTime": {
                    "description": "The update time of the policy.",
                    "type": "string"
                }
            }
        },
        "model.ProjectMemberResult": {
            "type": "object",
            "properties": {
                "entityID": {
                    "type": "integer"
                },
                "entityName": {
                    "type": "string"
                },
                "entityType": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "projectID": {
                    "type": "integer"
                },
                "roleID": {
                    "type": "integer"
                },
                "roleName": {
                    "type": "string"
                }
            }
        },
        "model.ProjectResult": {
            "type": "object",
            "properties": {
                "autoScan": {
                    "description": "Whether scan images automatically when pushing. The valid values are \"true\", \"false\".",
                    "type": "string"
                },
                "chartCount": {
                    "description": "The total number of charts under this project.",
                    "type": "integer"
                },
                "creationTime": {
                    "description": "The creation time of the project.\nFormat: date-time",
                    "type": "string"
                },
                "projectId": {
                    "description": "Project ID",
                    "type": "integer"
                },
                "projectName": {
                    "description": "The name of the project.",
                    "type": "string"
                },
                "public": {
                    "description": "The public status of the project. The valid values are \"true\", \"false\".",
                    "type": "string"
                },
                "repoCount": {
                    "description": "The number of the repositories under this project.",
                    "type": "integer"
                },
                "updateTime": {
                    "description": "The update time of the project.\nFormat: date-time",
                    "type": "string"
                }
            }
        },
        "model.Registry": {
            "type": "object",
            "properties": {
                "creationTime": {
                    "description": "The create time of the policy.",
                    "type": "string"
                },
                "credential": {
                    "description": "credential",
                    "$ref": "#/definitions/model.RegistryCredential"
                },
                "description": {
                    "description": "Description of the registry.",
                    "type": "string"
                },
                "id": {
                    "description": "The registry ID.",
                    "type": "integer"
                },
                "insecure": {
                    "description": "Whether or not the certificate will be verified when Harbor tries to access the server.",
                    "type": "boolean"
                },
                "name": {
                    "description": "The registry name.",
                    "type": "string"
                },
                "region": {
                    "description": "The region of the registry.",
                    "type": "string"
                },
                "status": {
                    "description": "Health status of the registry.",
                    "type": "string"
                },
                "type": {
                    "description": "Type of the registry, e.g. 'harbor'.",
                    "type": "string"
                },
                "updateTime": {
                    "description": "The update time of the policy.",
                    "type": "string"
                },
                "url": {
                    "description": "The registry URL string.",
                    "type": "string"
                }
            }
        },
        "model.RegistryCredential": {
            "type": "object",
            "properties": {
                "accessKey": {
                    "description": "Access key, e.g. user name when credential type is 'basic'.",
                    "type": "string"
                },
                "accessSecret": {
                    "description": "Access secret, e.g. password when credential type is 'basic'.",
                    "type": "string"
                },
                "type": {
                    "description": "Credential type, such as 'basic', 'oauth'.",
                    "type": "string"
                }
            }
        },
        "model.RegistryRequest": {
            "type": "object",
            "required": [
                "type"
            ],
            "properties": {
                "credential": {
                    "description": "credential",
                    "$ref": "#/definitions/model.RegistryCredential"
                },
                "description": {
                    "description": "Description of the registry.",
                    "type": "string"
                },
                "insecure": {
                    "description": "Whether or not the certificate will be verified when Harbor tries to access the server.",
                    "type": "boolean"
                },
                "name": {
                    "description": "The registry name.",
                    "type": "string"
                },
                "type": {
                    "description": "Type of the registry, e.g. 'harbor'.",
                    "type": "string"
                },
                "url": {
                    "description": "The registry URL string.",
                    "type": "string"
                }
            }
        },
        "model.RegistryResult": {
            "type": "object",
            "properties": {
                "creationTime": {
                    "description": "creation time",
                    "type": "string"
                },
                "credential": {
                    "description": "credential",
                    "$ref": "#/definitions/model.RegistryCredential"
                },
                "description": {
                    "description": "description",
                    "type": "string"
                },
                "id": {
                    "description": "id",
                    "type": "integer"
                },
                "insecure": {
                    "description": "insecure",
                    "type": "boolean"
                },
                "name": {
                    "description": "name",
                    "type": "string"
                },
                "status": {
                    "description": "status",
                    "type": "string"
                },
                "type": {
                    "description": "type",
                    "type": "string"
                },
                "updateTime": {
                    "description": "update time",
                    "type": "string"
                },
                "url": {
                    "description": "url",
                    "type": "string"
                }
            }
        },
        "model.RepositoryResult": {
            "type": "object",
            "properties": {
                "creationTime": {
                    "description": "The creation time of the repository\nFormat: date-time",
                    "type": "string"
                },
                "description": {
                    "description": "The description of the repository",
                    "type": "string"
                },
                "privateRepositoryPath": {
                    "description": "The path of private repository",
                    "type": "string"
                },
                "projectName": {
                    "description": "The project name the repository",
                    "type": "string"
                },
                "public": {
                    "description": "project is public or not",
                    "type": "string"
                },
                "pullCount": {
                    "description": "The count that the artifact inside the repository pulled",
                    "type": "integer"
                },
                "repositoryName": {
                    "description": "The name of the repository",
                    "type": "string"
                },
                "repositoryPath": {
                    "description": "The path of the repository",
                    "type": "string"
                },
                "tagCount": {
                    "description": "The count of the tags inside the repository",
                    "type": "integer"
                },
                "updateTime": {
                    "description": "The update time of the repository\nFormat: date-time",
                    "type": "string"
                }
            }
        },
        "model.ScanOverview": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "fixVersion": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "links": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "package": {
                    "type": "string"
                },
                "severity": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "model.ScanOverviewResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/model.ScanOverview"
                    }
                },
                "lastScanTime": {
                    "type": "string"
                },
                "pageNo": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "summary": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "model.SecurityReport": {
            "type": "object",
            "properties": {
                "prov_file": {
                    "description": "The URL of the provance file",
                    "type": "string"
                },
                "signed": {
                    "description": "A flag to indicate if the chart is signed",
                    "type": "boolean"
                }
            }
        },
        "model.Settings": {
            "type": "object",
            "properties": {
                "cron": {
                    "description": "The cron string for scheduled trigger",
                    "type": "string"
                }
            }
        },
        "model.SrcRegistry": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "The registry ID.",
                    "type": "integer"
                }
            }
        },
        "model.TagResult": {
            "type": "object",
            "properties": {
                "acceleratorStatus": {
                    "description": "The tag of repository accelerate status",
                    "type": "string"
                },
                "architecture": {
                    "description": "Architecture The architecture of repository",
                    "type": "string"
                },
                "author": {
                    "description": "Author",
                    "type": "string"
                },
                "digest": {
                    "description": "The digest of the artifact",
                    "type": "string"
                },
                "os": {
                    "description": "OS",
                    "type": "string"
                },
                "projectId": {
                    "description": "The ID of the project that the artifact belongs to",
                    "type": "integer"
                },
                "pullTime": {
                    "description": "The latest pull time of the tag\nFormat: date-time",
                    "type": "string"
                },
                "pushTime": {
                    "description": "The push time of the tag\nFormat: date-time",
                    "type": "string"
                },
                "repositoryId": {
                    "description": "The ID of the repository that the artifact belongs to",
                    "type": "integer"
                },
                "scanOverview": {
                    "description": "The scan overview",
                    "$ref": "#/definitions/model.TagScanOverview"
                },
                "size": {
                    "description": "The size of the artifact",
                    "type": "integer"
                },
                "tagName": {
                    "description": "The name of the tag",
                    "type": "string"
                },
                "type": {
                    "description": "The type of the artifact, e.g. image, chart, etc",
                    "type": "string"
                }
            }
        },
        "model.TagScanOverview": {
            "type": "object",
            "properties": {
                "endTime": {
                    "description": "The end time of the scan process that generating report\nExample: 2006-01-02T15:04:05\nFormat: date-time",
                    "type": "string"
                },
                "fixable": {
                    "description": "The number of the fixable vulnerabilities\nExample: 100",
                    "type": "integer"
                },
                "reportId": {
                    "description": "id of the native scan report\nExample: 5f62c830-f996-11e9-957f-0242c0a89008",
                    "type": "string"
                },
                "scanStatus": {
                    "description": "The status of the report generating process\nExample: Success",
                    "type": "string"
                },
                "severity": {
                    "description": "漏洞等级 Critical 危及 High 严重  Medium 中等 Low 较低",
                    "type": "string"
                },
                "startTime": {
                    "description": "The start time of the scan process that generating report\nExample: 2006-01-02T14:04:05\nFormat: date-time",
                    "type": "string"
                },
                "summary": {
                    "description": "Numbers of the vulnerabilities with different severity\nExample: {\"Critical\":5,\"High\":5}",
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "total": {
                    "description": "The total number of the found vulnerabilities\nExample: 500",
                    "type": "integer"
                }
            }
        },
        "model.Trigger": {
            "type": "object",
            "properties": {
                "triggerSettings": {
                    "description": "trigger settings",
                    "$ref": "#/definitions/model.Settings"
                },
                "type": {
                    "description": "The replication policy trigger type. The valid values are manual, event_based and scheduled.",
                    "type": "string"
                }
            }
        },
        "model.TriggerReq": {
            "type": "object",
            "properties": {
                "type": {
                    "description": "The replication policy trigger type. The valid values are manual, event_based and scheduled.\n镜像迁移只填写这个 manual\n实例同步可以写 manual event_base",
                    "type": "string"
                }
            }
        },
        "model.UpdateRepositoryRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "The description of the repository",
                    "type": "string"
                }
            }
        },
        "model.UpdateUserPasswordRequest": {
            "type": "object",
            "properties": {
                "newPassword": {
                    "type": "string"
                },
                "oldPassword": {
                    "type": "string"
                }
            }
        },
        "model.UpdateUserSysadminRequest": {
            "type": "object",
            "properties": {
                "sysadminFlag": {
                    "type": "boolean"
                }
            }
        },
        "model.UserQuota": {
            "type": "object",
            "properties": {
                "chart": {
                    "type": "integer"
                },
                "namespace": {
                    "type": "integer"
                },
                "repo": {
                    "type": "integer"
                }
            }
        },
        "model.UserResult": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "User ID",
                    "type": "integer"
                }
            }
        }
    }
}`

type swaggerInfo struct {
	Version     string
	Host        string
	BasePath    string
	Schemes     []string
	Title       string
	Description string
}

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = swaggerInfo{
	Version:     "0.0.1",
	Host:        "ccr.baidubce.com",
	BasePath:    "/v1",
	Schemes:     []string{},
	Title:       "CCR Service API",
	Description: "CCR Service 提供 RESTFUL 风格 API, 对接 Console 及 OpenAPI 请求",
}

type s struct{}

func (s *s) ReadDoc() string {
	sInfo := SwaggerInfo
	sInfo.Description = strings.Replace(sInfo.Description, "\n", "\\n", -1)

	t, err := template.New("swagger_info").Funcs(template.FuncMap{
		"marshal": func(v interface{}) string {
			a, _ := json.Marshal(v)
			return string(a)
		},
		"escape": func(v interface{}) string {
			// escape tabs
			str := strings.Replace(v.(string), "\t", "\\t", -1)
			// replace " with \", and if that results in \\", replace that with \\\"
			str = strings.Replace(str, "\"", "\\\"", -1)
			return strings.Replace(str, "\\\\\"", "\\\\\\\"", -1)
		},
	}).Parse(doc)
	if err != nil {
		return doc
	}

	var tpl bytes.Buffer
	if err := t.Execute(&tpl, sInfo); err != nil {
		return doc
	}

	return tpl.String()
}

func init() {
	swag.Register(swag.Name, &s{})
}
