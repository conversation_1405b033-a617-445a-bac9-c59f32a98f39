package config

import (
	"io/ioutil"

	"k8s.io/apimachinery/pkg/util/yaml"
)

type ServiceConfig struct {
	ListenAddress string `yaml:"listenAddress,omitempty"`

	IAMEndpoint string `yaml:"iamEndpoint,omitempty"`

	RoleName        string `yaml:"roleName,omitempty"`
	ServiceName     string `yaml:"serviceName,omitempty"`
	ServicePassword string `yaml:"servicePassword,omitempty"`

	Harbor HarborConfig `yaml:"harbor,omitempty"`
}

type HarborConfig struct {
	HarborIP            string `yaml:"harborIP,omitempty"`
	HarborHost          string `yaml:"harborHost,omitempty"`
	HarborUser          string `yaml:"harborUser,omitempty"`
	HarborPassword      string `yaml:"harborPassword,omitempty"`
	HarborDomain        string `yaml:"harborDomain,omitempty"`
	CoreSecretNamespace string `yaml:"coreSecretNamespace,omitempty"`
	CoreSecretName      string `yaml:"coreSecretNamespace,omitempty"`
}

func NewConfig(filepath string) (*ServiceConfig, error) {
	content, err := ioutil.ReadFile(filepath)
	if err != nil {
		return nil, err
	}

	var serviceConfig ServiceConfig
	err = yaml.Unmarshal(content, &serviceConfig)
	if err != nil {
		return nil, err
	}

	return &serviceConfig, nil
}
