[supervisord]
logfile=/home/<USER>/cse/resource-controller/supervisord/supervisord.log
pidfile=/home/<USER>/cse/resource-controller/supervisord/supervisord.pid

[unix_http_server]
file=/home/<USER>/cse/resource-controller/supervisord/supervisor.sock

[supervisorctl]
serverurl=unix:///home/<USER>/cse/resource-controller/supervisord/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:resource-controller]
priority=500
startsecs=5
autostart=false
autorestart=true
command=/home/<USER>/cse/resource-controller/resource-controller -leader-elect=true -features=redis,cncnetwork,gaiadb -config=/home/<USER>/cse/resource-controller/conf/config.yaml
redirect_stderr=true
stdout_logfile=/home/<USER>/cse/resource-controller/log/resource-controller.log
directory=/home/<USER>/cse/resource-controller