package clientset

import (
	"reflect"
	"testing"

	"github.com/baidubce/bce-sdk-go/services/sts/api"
	sdkiam "icode.baidu.com/baidu/bce-iam/sdk-go/iam"

	"github.com/baidubce/bce-sdk-go/services/cce"
	"github.com/baidubce/bce-sdk-go/services/eip"
	"github.com/baidubce/bce-sdk-go/services/rds"
	"github.com/goharbor/harbor/src/testing/mock"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/blb"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models/devops"
	testingsts "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/sts"
)

func TestClientSet_VpcDnsClientFromAccount(t *testing.T) {
	stsClient := &testingsts.Client{}

	type fields struct {
		stsClient      sts.ClientInterface
		rdsClient      *rds.Client
		cceClient      *cce.Client
		iamClient      *iam.Client
		eipClient      *eip.Client
		blbClient      *blb.Client
		userSetting    usersetting.ClientInterface
		sqlClient      *models.Client
		dnsClient      *dns.Client
		vpcDnsClient   *dns.VpcDnsClient
		devopsClient   *devops.Client
		resourceClient billing.ResourceClientInterface
	}
	type args struct {
		accountId string
		userId    string
		endpoint  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    dns.VpcDnsClientInterface
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				stsClient: stsClient,
			},
			args: args{
				accountId: "xxx",
				userId:    "aaa",
				endpoint:  "http://xxx.com",
			},
			want:    &dns.VpcDnsClient{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		cred := &sts.Credential{
			Credential: &api.Credential{
				AccessKeyId:     "aaa",
				SecretAccessKey: "xxx",
				SessionToken:    "sss",
			},
			Token: &sdkiam.Token{},
		}
		mock.OnAnything(tt.fields.stsClient, "GetCredential").Return(cred, nil)
		t.Run(tt.name, func(t *testing.T) {
			c := &ClientSet{
				stsClient:      tt.fields.stsClient,
				rdsClient:      tt.fields.rdsClient,
				cceClient:      tt.fields.cceClient,
				iamClient:      tt.fields.iamClient,
				eipClient:      tt.fields.eipClient,
				blbClient:      tt.fields.blbClient,
				userSetting:    tt.fields.userSetting,
				sqlClient:      tt.fields.sqlClient,
				dnsClient:      tt.fields.dnsClient,
				vpcDnsClient:   tt.fields.vpcDnsClient,
				devopsClient:   tt.fields.devopsClient,
				resourceClient: tt.fields.resourceClient,
			}

			got, err := c.VpcDnsClientFromAccount(tt.args.accountId, tt.args.userId, tt.args.endpoint)
			if (err != nil) != tt.wantErr {
				t.Errorf("VpcDnsClientFromAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, got) {
				t.Errorf("VpcDnsClientFromAccount() got = %v, want %v", got, got)
			}

			gotC, err := c.CertClientForAccount(tt.args.accountId, tt.args.userId, tt.args.endpoint)
			if (err != nil) != tt.wantErr {
				t.Errorf("CertClientForAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotC, gotC) {
				t.Errorf("CertClientForAccount() got = %v, want %v", got, got)
			}

			gotB, err := c.BcdClientForAccount(tt.args.accountId, tt.args.userId, tt.args.endpoint)
			if (err != nil) != tt.wantErr {
				t.Errorf("BcdClientForAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotB, gotB) {
				t.Errorf("BcdClientForAccount() got = %v, want %v", got, got)
			}

			gotP, err := c.PrivateZoneClientForAccount(tt.args.accountId, tt.args.userId, tt.args.endpoint)
			if (err != nil) != tt.wantErr {
				t.Errorf("PrivateZoneClientForAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotP, gotP) {
				t.Errorf("PrivateZoneClientForAccount() got = %v, want %v", got, got)
			}

		})
	}
}
