package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"time"

	"golang.org/x/time/rate"
	"gorm.io/gorm"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/util/workqueue"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/kafka"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
)

// CCRReconciler reconciles a CCR object
type CCRReconciler struct {
	options  *KafkaOptions
	sqlCli   models.Interface
	client   client.Client
	producer kafka.ProducerInterface
	region   string
	interval time.Duration
}

func NewCCRReconciler(sqlCli models.Interface, client client.Client, region string, options *KafkaOptions) *CCRReconciler {
	return &CCRReconciler{
		sqlCli:   sqlCli,
		client:   client,
		producer: kafka.NewProducer(),
		region:   region,
		interval: time.Hour,
		options:  options,
	}
}

func (r *CCRReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	// your logic here
	object := ccrv1alpha1.CCR{}
	err := r.client.Get(ctx, req.NamespacedName, &object)
	if apierrors.IsNotFound(err) {
		logger.Info("maybe deleted, ignore")
		return ctrl.Result{}, nil
	}

	if err != nil {
		logger.V(2).Error(err, "get object failed")
		return ctrl.Result{RequeueAfter: time.Second}, err
	}

	err = r.handle(ctx, &object)
	if err != nil {
		logger.Error(err, "handle object failed")
		return ctrl.Result{RequeueAfter: 2 * time.Second}, nil
	}

	return ctrl.Result{}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *CCRReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&ccrv1alpha1.CCR{}).
		WithOptions(controller.Options{
			RateLimiter: workqueue.NewMaxOfRateLimiter(
				workqueue.NewItemExponentialFailureRateLimiter(time.Second, 30*time.Second),
				// 10 qps, 100 bucket size.  This is only for retry speed and its only the overall factor (not per item)
				&workqueue.BucketRateLimiter{Limiter: rate.NewLimiter(rate.Limit(10), 100)},
			),
		}).
		Complete(r)
}

// 如果db中存在，判断是否是同一个实例
//
//	如果不是：删除旧的，新建一个实例记录
//	如果是：更新旧的实例记录
//
// 如果db中不存在，根据实例id和删除时间来进行upsert操作; important: 不能在程序侧判断time
func (r *CCRReconciler) handle(ctx context.Context, obj *ccrv1alpha1.CCR) error {
	insInDb, err := r.sqlCli.GetInstance(obj.GetName())
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("get instance failed: %w", err)
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		insInDb = nil
	}

	insConstructed := models.InstanceFromCCR(obj)

	if insInDb != nil {
		if insInDb.UUID != insConstructed.UUID {
			err = r.sqlCli.DeleteInstanceWithTimestamp(obj.GetName(), time.Now())
			if err != nil {
				return fmt.Errorf("delete instance %s from database failed: %w", obj.GetName(), err)
			}

			return r.sqlCli.InsertInstance(insConstructed)
		}

		insConstructed.ID = insInDb.ID
		if !insInDb.Equal(insConstructed) {
			return r.sqlCli.UpdateInstance(insConstructed)
		}
	}

	if insInDb == nil {
		// use upsert, in case there is instance with delete timestamp happen
		return r.sqlCli.UpsertInstance(insConstructed)
	}

	return nil
}

func (r *CCRReconciler) Start(ctx context.Context) error {
	wait.Until(func() {
		r.sync(ctx)
	}, r.interval, ctx.Done())

	return nil
}

func (r *CCRReconciler) sync(ctx context.Context) error {
	logger := log.FromContext(ctx)

	inses, err := r.sqlCli.FindExistedInstance()
	if err != nil {
		logger.Error(err, "FindExistedInstance failed")
		return err
	}

	for _, v := range inses {
		var obj ccrv1alpha1.CCR
		err = r.client.Get(ctx, client.ObjectKey{Name: v.InstanceID}, &obj)
		if err == nil {
			// object exist, skip update
			continue
		}

		if err != nil && !apierrors.IsNotFound(err) {
			logger.Error(err, "get obj failed", "objectName", v.InstanceID)
			continue
		}

		logger.Info("object is not found", "objectName", v.InstanceID)
		// object not found, delete
		err = r.sqlCli.DeleteInstanceWithTimestamp(v.InstanceID, time.Now())
		if err != nil {
			logger.Error(err, "delete instance failed", "instance id", v.InstanceID)
		}

		// send delete message to Kafka
		message := model.SyncResGroupMessages{
			SyncType: "delete",
			Resources: []model.SyncResourceDetail{
				{
					AccountID: v.AccountID,
					UserID:    v.UserID,
					Name:      v.InstanceName,
					Type:      "CCR",
					Region:    r.region,
					ID:        v.InstanceID,
					AuthID:    "",
					UUID:      v.UUID,
				},
			},
		}
		msg, err := json.Marshal(message)
		if err != nil {
			logger.Error(err, "json Marshal err")
		}
		o := kafka.NewKafkaOptions(r.options.CaPemPath, r.options.ClientKeyPath, r.options.ClientPemPath,
			r.options.ConsumerGroupID, r.options.BrokerEndpoint)
		err = r.producer.StartProducer(o)
		if err != nil {
			logger.Error(err, "start producer failed!")
			continue
		}
		_, _, err = r.producer.SendMessage(DefaultOnlineTopic, string(msg))
		if err != nil {
			logger.Error(err, "send message to Kafka failed")
			continue
		}
		r.producer.Close()
		// TODO: send delete message to MySQL
	}

	return nil
}
