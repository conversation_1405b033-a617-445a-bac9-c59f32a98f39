package controllers

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/models/mock"
)

func Test_CCRReconcile_Reconcile(t *testing.T) {
	scheme := runtime.NewScheme()
	v1alpha1.AddToScheme(scheme)

	sqlCli := mock.NewMockInterface(gomock.NewController(t))
	reconciler := NewCCRReconciler(sqlCli, nil, "test", &KafkaOptions{})
	reconciler.client = fake.NewFakeClientWithScheme(scheme)

	result, err := reconciler.Reconcile(context.Background(), reconcile.Request{NamespacedName: types.NamespacedName{Name: "non-exist"}})
	assert.Nil(t, err)
	assert.Equal(t, ctrl.Result{}, result)

	reconciler.client = fake.NewFakeClient()
	result, err = reconciler.Reconcile(context.Background(), reconcile.Request{NamespacedName: types.NamespacedName{Name: "test"}})
	assert.NotNil(t, err)
	assert.Equal(t, ctrl.Result{RequeueAfter: 1 * time.Second}, result)

	reconciler.client = fake.NewFakeClientWithScheme(scheme, &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "test"}})
	sqlCli.EXPECT().GetInstance(gomock.Eq("test")).Return(nil, fmt.Errorf("error"))
	result, err = reconciler.Reconcile(context.Background(), reconcile.Request{NamespacedName: types.NamespacedName{Name: "test"}})
	assert.Nil(t, err)
	assert.Equal(t, ctrl.Result{RequeueAfter: 2 * time.Second}, result)
}

func Test_CCRReconcile_handle(t *testing.T) {
	sqlCli := mock.NewMockInterface(gomock.NewController(t))
	reconciler := NewCCRReconciler(sqlCli, nil, "test", &KafkaOptions{})

	// database return error
	sqlCli.EXPECT().GetInstance(gomock.Eq("error")).Return(nil, fmt.Errorf("error"))
	assert.Error(t, reconciler.handle(context.Background(), &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "error"}}))

	// record not found
	sqlCli.EXPECT().GetInstance(gomock.Eq("empty")).AnyTimes().Return(nil, gorm.ErrRecordNotFound)
	sqlCli.EXPECT().UpsertInstance(gomock.Any()).AnyTimes().Return(nil)
	assert.NoError(t, reconciler.handle(context.Background(), &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "empty"}}))

	sqlCli.EXPECT().InsertInstance(gomock.Any()).AnyTimes().Return(nil)
	sqlCli.EXPECT().DeleteInstanceWithTimestamp(gomock.Any(), gomock.Any()).AnyTimes().Return(nil)

	// database has one with different uuid
	sqlCli.EXPECT().GetInstance(gomock.Eq("diffuuid")).Return(&models.Instance{UUID: "t1"}, nil)
	assert.NoError(t, reconciler.handle(context.Background(), &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "diffuuid", UID: types.UID("t2")}}))

	//
	sqlCli.EXPECT().GetInstance(gomock.Eq("diffcontent")).Return(&models.Instance{UserID: "user1"}, nil)
	sqlCli.EXPECT().UpdateInstance(gomock.Any()).AnyTimes().Return(nil)
	assert.NoError(t, reconciler.handle(context.Background(), &v1alpha1.CCR{ObjectMeta: v1.ObjectMeta{Name: "diffcontent"}, Spec: v1alpha1.CCRSpec{UserID: "user2"}}))
}

func Test_CCRReconcile_sync(t *testing.T) {
	scheme := runtime.NewScheme()
	v1alpha1.AddToScheme(scheme)
	sqlCli := mock.NewMockInterface(gomock.NewController(t))
	reconciler := NewCCRReconciler(sqlCli, nil, "test", &KafkaOptions{})
	reconciler.client = fake.NewFakeClientWithScheme(scheme)

	sqlCli.EXPECT().FindExistedInstance().Return(nil, nil)
	assert.Nil(t, reconciler.sync(context.Background()))

	sqlCli.EXPECT().FindExistedInstance().Return([]*models.Instance{{InstanceID: "test"}}, nil)
	sqlCli.EXPECT().DeleteInstanceWithTimestamp(gomock.Any(), gomock.Any()).Return(nil)
	assert.Nil(t, reconciler.sync(context.Background()))

	sqlCli.EXPECT().FindExistedInstance().Return(nil, fmt.Errorf("error"))
	assert.Error(t, reconciler.sync(context.Background()))
}
