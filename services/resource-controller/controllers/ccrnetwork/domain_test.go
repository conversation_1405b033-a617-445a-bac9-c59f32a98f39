package ccrnetwork

import (
	"context"
	"fmt"

	"testing"
	"time"

	"github.com/goharbor/harbor/src/testing/mock"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/api/extensions/v1beta1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/certificate"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/privatezone"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
	testingbcd "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/bcd"
	testingcertificate "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/certificate"
	testingprivatezone "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/privatezone"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/resource-controller/clientset"
)

// TestDomain_handleDomain test handle domain
func TestDomain_handleDomain(t *testing.T) {
	type fields struct {
		clients   clientset.ClientSetInterface
		config    *conf.Config
		k8sClient client.Client
	}
	type args struct {
		ctx    context.Context
		object *ccrv1alpha1.CNCNetwork
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "status-add1",
			fields: fields{
				clients: &testingclientset.ClientSet{},
				config:  &conf.Config{},
				k8sClient: fake.NewClientBuilder().WithRuntimeObjects(&v1beta1.Ingress{
					Spec: v1beta1.IngressSpec{
						Rules: []v1beta1.IngressRule{
							{},
						},
					},
				}).WithRuntimeObjects(&corev1.Secret{}).Build(),
			},
			args: args{
				ctx: context.Background(),
				object: &ccrv1alpha1.CNCNetwork{
					Spec: ccrv1alpha1.CNCNetworkSpec{
						CustomDomains: []ccrv1alpha1.CustomDomain{
							{
								DomainName: "test.com",
								CertID:     "cert1",
							},
						},
					},
					Status: ccrv1alpha1.CNCNetworkStatus{},
				},
			},
		},
		{
			name: "status-add2",
			fields: fields{
				clients: &testingclientset.ClientSet{},
				config:  &conf.Config{},
				k8sClient: fake.NewClientBuilder().WithRuntimeObjects(&v1beta1.Ingress{
					Spec: v1beta1.IngressSpec{
						Rules: []v1beta1.IngressRule{
							{},
						},
					},
				}).WithRuntimeObjects(&corev1.Secret{}).Build(),
			},
			args: args{
				ctx: context.Background(),
				object: &ccrv1alpha1.CNCNetwork{
					Spec: ccrv1alpha1.CNCNetworkSpec{
						CustomDomains: []ccrv1alpha1.CustomDomain{
							{
								DomainName: "test.com",
								CertID:     "cert1",
							},
						},
					},
					Status: ccrv1alpha1.CNCNetworkStatus{},
				},
			},
		},
		{
			name: "status-update",
			fields: fields{
				clients: &testingclientset.ClientSet{},
				config:  &conf.Config{},
				k8sClient: fake.NewClientBuilder().WithRuntimeObjects(&v1beta1.Ingress{
					Spec: v1beta1.IngressSpec{
						Rules: []v1beta1.IngressRule{
							{},
						},
					},
				}).WithRuntimeObjects(&corev1.Secret{}).Build(),
			},
			args: args{
				ctx: context.Background(),
				object: &ccrv1alpha1.CNCNetwork{
					Spec: ccrv1alpha1.CNCNetworkSpec{
						CustomDomains: []ccrv1alpha1.CustomDomain{
							{
								DomainName: "test.com",
								CertID:     "cert1",
							},
						},
					},
					Status: ccrv1alpha1.CNCNetworkStatus{
						CustomDomainStatus: []ccrv1alpha1.CustomDomainStatus{
							{
								DomainName: "test.com",
								CertID:     "cert0",
							},
						},
					},
				},
			},
		},
		{
			name: "status-delete",
			fields: fields{
				clients: &testingclientset.ClientSet{},
				config:  &conf.Config{},
				k8sClient: fake.NewClientBuilder().WithRuntimeObjects(&v1beta1.Ingress{
					Spec: v1beta1.IngressSpec{
						Rules: []v1beta1.IngressRule{
							{},
						},
					},
				}).WithRuntimeObjects(&corev1.Secret{}).Build(),
			},
			args: args{
				ctx: context.Background(),
				object: &ccrv1alpha1.CNCNetwork{
					Spec: ccrv1alpha1.CNCNetworkSpec{
						CustomDomains: []ccrv1alpha1.CustomDomain{
							{
								DomainName: "test.com",
								CertID:     "cert1",
							},
						},
					},
					Status: ccrv1alpha1.CNCNetworkStatus{
						CustomDomainStatus: []ccrv1alpha1.CustomDomainStatus{
							{
								DomainName: "test.com",
								CertID:     "cert1",
							},
						},
					},
				},
			},
		},
		{
			name: "check-status1",
			fields: fields{
				clients: &testingclientset.ClientSet{},
				config:  &conf.Config{},
				k8sClient: fake.NewClientBuilder().WithRuntimeObjects(&v1beta1.Ingress{
					Spec: v1beta1.IngressSpec{
						Rules: []v1beta1.IngressRule{
							{},
						},
					},
				}).WithRuntimeObjects(&corev1.Secret{}).Build(),
			},
			args: args{
				ctx: context.Background(),
				object: &ccrv1alpha1.CNCNetwork{
					Spec: ccrv1alpha1.CNCNetworkSpec{
						CustomDomains: []ccrv1alpha1.CustomDomain{
							{
								DomainName: "check.com",
								CertID:     "cert1",
							},
						},
					},
					Status: ccrv1alpha1.CNCNetworkStatus{
						CustomDomainStatus: []ccrv1alpha1.CustomDomainStatus{
							{
								DomainName:   "check.com",
								CertID:       "cert1",
								DomainStatus: ccrv1alpha1.CustomDomainStatusCreated,
							},
						},
					},
				},
			},
		},
		{
			name: "check-status2",
			fields: fields{
				clients: &testingclientset.ClientSet{},
				config:  &conf.Config{},
				k8sClient: fake.NewClientBuilder().WithRuntimeObjects(&v1beta1.Ingress{
					Spec: v1beta1.IngressSpec{
						Rules: []v1beta1.IngressRule{
							{},
						},
					},
				}).WithRuntimeObjects(&corev1.Secret{}).Build(),
			},
			args: args{
				ctx: context.Background(),
				object: &ccrv1alpha1.CNCNetwork{
					Spec: ccrv1alpha1.CNCNetworkSpec{
						CustomDomains: []ccrv1alpha1.CustomDomain{
							{
								DomainName: "check.com",
								CertID:     "cert1",
							},
						},
					},
					Status: ccrv1alpha1.CNCNetworkStatus{
						CustomDomainStatus: []ccrv1alpha1.CustomDomainStatus{
							{
								DomainName:   "check.com",
								CertID:       "cert1",
								DomainStatus: ccrv1alpha1.CustomDomainStatusCreated,
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {

		certCli := &testingcertificate.CertClient{}
		mock.OnAnything(tt.fields.clients, "CertClientForAccount").Return(certCli, nil)
		mock.OnAnything(certCli, "GetCertDetailContent").Return(&certificate.CertContent{
			CertID:         "cert1",
			CertType:       1,
			CertServerData: "CertServerData",
			CertPrivateKey: "CertServerData",
		}, nil)

		bcdCli := &testingbcd.BcdClient{}
		mock.OnAnything(tt.fields.clients, "BcdClientForAccount").Return(bcdCli, nil)
		mock.OnAnything(bcdCli, "CheckDomainICP").Return(false, nil)

		privateZoneCli := &testingprivatezone.PrivateZoneClient{}
		mock.OnAnything(tt.fields.clients, "PrivateZoneClientForAccount").Return(privateZoneCli, nil)
		mock.OnAnything(privateZoneCli, "ListZone").Return(&privatezone.ListZoneResponse{
			MaxKeys: 100,
			Zones: []privatezone.ZoneInfo{{
				ZoneName: "check.com",
			}},
		}, nil)

		if tt.name == "status-add1" {
			mock.OnAnything(certCli, "GetCertDetailContent").Return(&certificate.CertContent{
				CertID:         "cert1",
				CertType:       1,
				CertServerData: "CertServerData",
				CertPrivateKey: "CertServerData",
			}, nil)
		}
		if tt.name == "status-add2" {
			mock.OnAnything(certCli, "GetCertDetailContent").Return(nil, fmt.Errorf("error"))
		}
		if tt.name == "status-add3" {
			mock.OnAnything(certCli, "GetCertDetailContent").Return(&certificate.CertContent{
				CertID:   "cert1",
				CertType: 2,
			}, nil)
		}

		if tt.name == "check-status1" {
			mock.OnAnything(certCli, "GetCertDetail").Return(&certificate.Cert{
				CertID:       "cert1",
				CertStopTime: time.Now().AddDate(0, 0, -1),
			}, nil)
		} else if tt.name == "check-status2" {
			mock.OnAnything(certCli, "GetCertDetail").Return(&certificate.Cert{
				CertID:       "cert1",
				CertStopTime: time.Now().AddDate(0, 0, 20),
			}, nil)

		} else {
			mock.OnAnything(certCli, "GetCertDetail").Return(&certificate.Cert{
				CertID:       "cert1",
				CertStopTime: time.Now().AddDate(1, 0, 0),
			}, nil)
		}

		t.Run(tt.name, func(t *testing.T) {
			handler := &Domain{
				clients:   tt.fields.clients,
				config:    tt.fields.config,
				k8sClient: tt.fields.k8sClient,
			}

			if err := handler.handleDomain(tt.args.ctx, tt.args.object); (err != nil) != tt.wantErr {
				t.Errorf("handleDomain() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
