package ccrnetwork

import (
	"context"
	"testing"

	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"sigs.k8s.io/controller-runtime/pkg/client"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
	testingclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/resource-controller/clientset"
)

func TestNewPrivate(t *testing.T) {

}

func TestPrivate_handlePrivate(t *testing.T) {
	type fields struct {
		clients   clientset.ClientSetInterface
		config    *conf.Config
		k8sclient client.Client
	}
	type args struct {
		ctx    context.Context
		object *ccrv1alpha1.CNCNetwork
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "case1",
			fields: fields{
				clients:   &testingclientset.ClientSet{},
				config:    &conf.Config{},
				k8sclient: fake.NewClientBuilder().Build(),
			},
			args: args{
				ctx:    context.Background(),
				object: &ccrv1alpha1.CNCNetwork{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := &Private{
				clients:   tt.fields.clients,
				config:    tt.fields.config,
				k8sclient: tt.fields.k8sclient,
			}
			if err := handler.handlePrivate(tt.args.ctx, tt.args.object); (err != nil) != tt.wantErr {
				t.Errorf("handlePrivate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
