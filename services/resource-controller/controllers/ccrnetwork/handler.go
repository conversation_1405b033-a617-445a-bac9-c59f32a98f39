package ccrnetwork

import (
	"context"
	"encoding/json"
	"fmt"
	blbsdk "github.com/baidubce/bce-sdk-go/services/blb"
	"net"
	"reflect"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/blb"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

type Handler struct {
	clients clientset.ClientSetInterface

	config    *conf.Config
	k8sclient client.Client

	private *Private
	public  *Public
	domain  *Domain
}

func NewHandler(config *conf.Config, clients clientset.ClientSetInterface, cli client.Client) *Handler {
	return &Handler{
		config:    config,
		clients:   clients,
		k8sclient: cli,
		private:   NewPrivate(config, clients, cli),
		public:    NewPublic(config, clients, cli),
		domain:    NewDomain(config, clients, cli),
	}
}

func (handler *Handler) Handle(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	obj := object.DeepCopy()
	logger := log.FromContext(ctx)

	switch {
	case object.DeletionTimestamp != nil:
		return handler.handleDelete(ctx, obj)
	case object.Status.Phase == "":
		return handler.handlePendingPhase(ctx, obj)
	case object.Status.Phase == ccrv1alpha1.CNCNetworkStatusInitializing:
		return handler.createServicePublishPoint(ctx, obj)
	}

	needUpdate := false
	// whether need to update public
	err := handler.public.handlePublic(ctx, obj)
	if err != nil {
		logger.Error(err, "handler public failed")
		needUpdate = true
	}

	err = handler.private.handlePrivate(ctx, obj)
	if err != nil {
		logger.Error(err, "handler private failed")
		needUpdate = true
	}

	err = handler.domain.handleDomain(ctx, obj)
	if err != nil {
		logger.Error(err, "handler domain failed")
		needUpdate = true
	}

	if needUpdate {
		current := metav1.Now()
		obj.Status.LastProbeTime = &current
		return handler.k8sclient.Status().Update(ctx, obj)
	}

	// status changed?
	srcStatus, err := json.Marshal(object.Status)
	if err != nil {
		return fmt.Errorf("marshal origin status failed: %w", err)
	}

	destStatus, err := json.Marshal(obj.Status)
	if err != nil {
		return fmt.Errorf("marshal new status failed: %w", err)
	}

	if !reflect.DeepEqual(srcStatus, destStatus) {
		logger.Info("status has changed, update status")
		current := metav1.Now()
		obj.Status.LastProbeTime = &current
		return handler.k8sclient.Status().Update(ctx, obj)
	}

	if !handler.isStable(obj) {
		return fmt.Errorf("object is not in stable status, need more round")
	}

	return nil
}

func (handler *Handler) isStable(object *ccrv1alpha1.CNCNetwork) bool {
	if object.Status.Phase == ccrv1alpha1.CNCNetworkStatusInitializing {
		return false
	}

	if len(object.Status.LinkStatus) != 0 {
		for _, v := range object.Status.LinkStatus {
			if v.Status != ccrv1alpha1.PrivateLinkStatusCreated &&
				v.Status != ccrv1alpha1.PrivateLinkStatusDeleted &&
				v.Status != ccrv1alpha1.PrivateLinkStatusFailed {
				return false
			}
		}
	}

	if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusClosing || object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusOpening {
		return false
	}

	return true
}

func (handler *Handler) handlePendingPhase(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	// set finalizer first
	logger := log.FromContext(ctx)
	finalizerSetted, err := handler.setFinalizer(ctx, object)
	if err != nil {
		logger.V(2).Error(err, "update finalizer failed")
		return err
	}

	// finalizer setted, waitfor next round
	if !finalizerSetted {
		logger.V(2).Info("finalizer has been setted, wait for phase to be update")
		return nil
	}

	object.Status.Phase = ccrv1alpha1.CNCNetworkStatusInitializing
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime
	object.Status.BLBID = object.Spec.BLBID

	return handler.k8sclient.Status().Update(ctx, object)
}

func (handler *Handler) createServicePublishPoint(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)

	if object.Status.PublishPoint != "" {
		return nil
	}

	lbID := object.Status.BLBID

	if lbID == "" {
		if len(object.Annotations) > 0 && object.Annotations["auto-create-appblb"] == "true" {
			blb, err := handler.clients.BlbClient().CreateBlb(object.Name, handler.config.BLB.VpcID, handler.config.BLB.SubnetID)
			if err != nil {
				logger.V(2).Error(err, "create blb failed")
				handler.updateReason(ctx, object, fmt.Sprintf("create blb failed: %v", err))
				return err
			}
			lbID = blb.BlbId
			object.Status.BLBID = lbID
		}
	}

	blbInfo, err := handler.clients.BlbClient().GetBlb(lbID)
	if err != nil {
		logger.V(2).Error(err, "get blb failed")
		handler.updateReason(ctx, object, fmt.Sprintf("get blb failed: %v", err))
		return err
	}
	if blbInfo.Status != blbsdk.BLBStatusAvailable {
		err = fmt.Errorf("blb status is not available, current status: %v", blbInfo.Status)
		logger.V(2).Error(err, "blb not available")
		handler.updateReason(ctx, object, err.Error())
		return err
	}
	object.Status.BlbVip = blbInfo.UnderlayVIP

	svcPubName := utils.SimpleRandomAlphaNumberInLowerCase(10)
	if lbID == "" {
		// wait for lb is mounted for svc
		svcName := fmt.Sprintf("%s-ingress-controller", object.GetName())
		svc := corev1.Service{}
		err := handler.k8sclient.Get(ctx, client.ObjectKey{Namespace: object.Namespace, Name: svcName}, &svc)
		if err != nil {
			logger.V(2).Error(err, "get service failed")
			handler.updateReason(ctx, object, "ingressServiceNotFound")
			return err
		}

		lbAnnotation := "service.beta.kubernetes.io/cce-load-balancer-id"
		if svc.GetAnnotations() == nil || svc.GetAnnotations()[lbAnnotation] == "" {
			logger.V(2).Error(fmt.Errorf("no load balancer provided"), "no load balancer provided", "annotations", svc.GetAnnotations())
			handler.updateReason(ctx, object, "loadbalanceNotFound")
			return fmt.Errorf("no load balancer provided")
		}

		svcPubPoints, err := handler.clients.BlbClient().ListAllService()
		if err != nil {
			logger.V(2).Error(err, "list service publish point failed")
			handler.updateReason(ctx, object, ccrv1alpha1.ReasonServicePublishPointListFail)
			return err
		}

		for _, v := range svcPubPoints {
			if v.Name == object.GetName() {
				object.Status.PublishPoint = v.Service
				object.Status.Phase = ccrv1alpha1.CNCNetworkStatusReady
				return handler.updateReason(ctx, object, "")
			}
		}

		lbID = svc.GetAnnotations()[lbAnnotation]
	}
	// create pubpoint
	createReq := blb.CreateServiceArgs{
		ClientToken: string(uuid.NewUUID()),
		Name:        object.GetName(),
		ServiceName: svcPubName,
		InstanceID:  lbID,
		AuthList:    []blb.AuthItem{},
	}

	if object.Spec.AccountID != "" {
		createReq.AuthList = append(createReq.AuthList, blb.AuthItem{UID: object.Spec.AccountID, Auth: "allow"})
	}

	pubServicePoint, err := handler.clients.BlbClient().CreateService(&createReq)
	if err != nil {
		logger.V(2).Error(err, "create service publish point failed")
		handler.updateReason(ctx, object, "createServicePublishPointFailed")
		return err
	}
	object.Status.PublishPoint = pubServicePoint.Service
	object.Status.Phase = ccrv1alpha1.CNCNetworkStatusReady
	object.Status.BLBID = lbID

	return handler.updateReason(ctx, object, "")
}

func (handler *Handler) handleDelete(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	if object.Status.PublicStatus.Address != "" {
		err := handler.public.handleDeleteAndUnbindEip(ctx, object)
		if err != nil {
			return fmt.Errorf("delete and unbind eip failed: %w", err)
		}

		if object.Status.PublicStatus.Address != "" {
			return fmt.Errorf("ip is not released, need next round")
		}
	}

	if len(object.Status.LinkStatus) != 0 {
		for _, v := range object.Status.LinkStatus {
			v.Status = ccrv1alpha1.PrivateLinkStatusDeleting

			userId := object.Spec.UserID
			if v.CreatedBy != "" {
				userId = v.CreatedBy
			}

			err := handler.private.handleOnePrivate(ctx, object.Spec.AccountID, userId, object.GetName(), "", &v)
			if err != nil {
				return fmt.Errorf("delete service network %v err failed: %w", v.ServiceID, err)
			}
		}
	}

	// delete service publish point
	if object.Status.PublishPoint != "" {
		// delete service publish
		svcPoints, err := handler.clients.BlbClient().ListAllService()
		if err != nil {
			handler.updateReason(ctx, object, "listAllServicePublishPointFailed")
			return fmt.Errorf("list all service failed: %w", err)
		}

		if containService(svcPoints, object.Status.PublishPoint) {
			err = handler.clients.BlbClient().DeleteService(string(uuid.NewUUID()), object.Status.PublishPoint)
			if err != nil {
				handler.updateReason(ctx, object, ccrv1alpha1.ReasonServicePublishPointDeleteFailed)
				return fmt.Errorf("delete service publish point %v failed: %w", object.Status.PublishPoint, err)
			}
		}

		object.Status.PublishPoint = ""
	}

	if object.Status.BLBID != "" {
		if len(object.Annotations) > 0 && object.Annotations["auto-create-appblb"] == "true" {
			if err := handler.clients.BlbClient().AllowBlbDelete(object.Status.BLBID); err != nil {
				handler.updateReason(ctx, object, fmt.Sprintf("allow blb delete failed: %v", err))
				return fmt.Errorf("allow blb delete failed: %v", err)
			}
			if err := handler.clients.BlbClient().DeleteBlb(object.Status.BLBID); err != nil {
				handler.updateReason(ctx, object, fmt.Sprintf("delete blb failed: %v", err))
				return fmt.Errorf("delete blb failed: %v", err)
			}
		}
		object.Status.BLBID = ""
	}

	return handler.removeFinalizer(ctx, object)
}

func (handler *Handler) updateReason(ctx context.Context, object *ccrv1alpha1.CNCNetwork, reason string) error {
	logger := log.FromContext(ctx)
	current := metav1.Now()
	object.Status.Reason = reason
	object.Status.LastProbeTime = &current
	err := handler.k8sclient.Status().Update(ctx, object)
	if err != nil {
		logger.Error(err, "update eip reason failed")
	}

	return err
}

func (handler *Handler) setFinalizer(ctx context.Context, object *ccrv1alpha1.CNCNetwork) (bool, error) {
	finalizers := object.GetFinalizers()
	if finalizers == nil {
		finalizers = make([]string, 0)
	}

	for _, v := range finalizers {
		if v == ccrv1alpha1.ResourceFinalizer {
			return true, nil
		}
	}

	finalizers = append(finalizers, ccrv1alpha1.ResourceFinalizer)
	object.SetFinalizers(finalizers)

	return false, handler.k8sclient.Update(ctx, object)
}

func (handler *Handler) removeFinalizer(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	finalizers := object.GetFinalizers()
	if len(finalizers) == 0 {
		return nil
	}

	reservedFinalizer := make([]string, 0)
	for _, v := range finalizers {
		if v != ccrv1alpha1.ResourceFinalizer {
			reservedFinalizer = append(reservedFinalizer, v)
		}
	}

	object.SetFinalizers(reservedFinalizer)
	return handler.k8sclient.Update(ctx, object)
}

func containService(svcPoints []blb.GetServiceResult, service string) bool {
	for _, v := range svcPoints {
		if v.Service == service {
			return true
		}
	}

	return false
}

func DNSLookup(addr, dnsHost string) ([]string, error) {
	resolver := &net.Resolver{
		PreferGo: true,
		Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
			d := net.Dialer{
				Timeout: time.Millisecond * time.Duration(10000),
			}
			return d.DialContext(ctx, network, dnsHost)
		},
	}

	timeoutCtx, cancelFunc := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancelFunc()

	return resolver.LookupHost(timeoutCtx, addr)
}

func isValidDomain(domain string) bool {
	parts := strings.Split(domain, ".")
	if len(parts) != 5 {
		return false
	}

	return strings.HasSuffix(domain, ".baidubce.com")
}
