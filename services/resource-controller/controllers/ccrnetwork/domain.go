package ccrnetwork

import (
	"context"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/api/extensions/v1beta1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

type Domain struct {
	clients clientset.ClientSetInterface

	config    *conf.Config
	k8sClient client.Client
}

// NewDomain 创建 Domain 对象
func NewDomain(config *conf.Config, clients clientset.ClientSetInterface, cli client.Client) *Domain {
	return &Domain{
		clients:   clients,
		config:    config,
		k8sClient: cli,
	}
}

// handleDomain 处理自定义域名
func (handler *Domain) handleDomain(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)
	desired, current := sets.NewString(), sets.NewString()
	for _, v := range object.Spec.CustomDomains {
		desired.Insert(v.DomainName + v.CertID)
	}

	customDomainStatus := make([]ccrv1alpha1.CustomDomainStatus, 0)
	for _, v := range object.Status.CustomDomainStatus {
		if v.DomainStatus == ccrv1alpha1.CustomDomainStatusDeleted {
			continue
		}

		if !desired.Has(v.DomainName+v.CertID) && v.DomainStatus != ccrv1alpha1.CustomDomainStatusCreating &&
			v.DomainStatus != ccrv1alpha1.CustomDomainStatusDeleting && v.DomainStatus != ccrv1alpha1.CustomDomainStatusUpdating {
			v.DomainStatus = ccrv1alpha1.CustomDomainStatusDeleting
		}

		customDomainStatus = append(customDomainStatus, v)
		current.Insert(v.DomainName + v.CertID)
	}

	for _, v := range object.Spec.CustomDomains {
		//spec有域名信息,status没有,更新status
		if !current.Has(v.DomainName + v.CertID) {

			certCli, err := handler.clients.CertClientForAccount(object.Spec.AccountID, object.Spec.UserID, handler.config.CertificateEndpoint)
			if err != nil {
				logger.Error(err, "handle domain failed.")
				return err
			}
			detail, err := certCli.GetCertDetail(v.CertID)
			if err != nil {
				logger.Error(err, "handle domain failed.")
				return err
			}

			bcdCli, err := handler.clients.BcdClientForAccount(object.Spec.AccountID, object.Spec.UserID, handler.config.DomainCheckEndpoint)
			isIcp, err := bcdCli.CheckDomainICP(v.DomainName)
			if err != nil {
				logger.Error(err, "handle domain failed,with check domain icp")
			}

			s := ccrv1alpha1.CustomDomainStatus{
				DomainName:     v.DomainName,
				CertID:         v.CertID,
				CertName:       v.CertName,
				CertExpireTime: &metav1.Time{Time: detail.CertStopTime},
				DomainICPed:    isIcp,
				DomainStatus:   ccrv1alpha1.CustomDomainStatusCreating,
			}

			customDomainStatus = append(customDomainStatus, s)
		}
	}
	for idx, v := range customDomainStatus {

		err := handler.handleOneDomain(ctx, object.Spec.AccountID, object.Spec.UserID, object.Namespace, object.Spec.Domain.PublicDomain, &v)
		if err != nil {
			logger.Error(err, "handle one domain failed")
		}
		customDomainStatus[idx] = v
	}

	object.Status.CustomDomainStatus = customDomainStatus

	return nil
}

// handleOneDomain 处理单个域名
func (handler *Domain) handleOneDomain(ctx context.Context, accountId string, userId string,
	namespace string, ccrDefaultDomain string, s *ccrv1alpha1.CustomDomainStatus) error {
	logger := log.FromContext(ctx)
	domainName := s.DomainName
	certID := s.CertID
	logger.Info("start to handle domain", "domainName:", domainName, "certId:", certID)
	if s.DomainStatus == ccrv1alpha1.CustomDomainStatusCreating {
		// 1.创建证书对应的secret
		certCli, err := handler.clients.CertClientForAccount(accountId, userId, handler.config.CertificateEndpoint)
		if err != nil {
			s.DomainStatus = ccrv1alpha1.CustomDomainStatusFailed
			logger.Error(err, "create cert client failed")
			return err
		}
		certData, err := certCli.GetCertDetailContent(certID)
		if err != nil {
			logger.Error(err, "get cert data failed")
			s.DomainStatus = ccrv1alpha1.CustomDomainStatusFailed
			s.DomainStatusDesc = "get cert data failed," + err.Error()
			return err
		}
		if certData.CertType != 1 {
			// 证书类型必须是服务器证书
			logger.Error(err, "cert type not server")
			s.DomainStatus = ccrv1alpha1.CustomDomainStatusFailed
			s.DomainStatusDesc = "cert type not server"
			return fmt.Errorf("cert type not server")
		}

		var existSecret corev1.Secret
		err = handler.k8sClient.Get(ctx, client.ObjectKey{Namespace: namespace, Name: domainName}, &existSecret)

		if err != nil && !apierrors.IsNotFound(err) {
			s.DomainStatus = ccrv1alpha1.CustomDomainStatusFailed
			logger.Error(err, "create domain failed")
			s.DomainStatusDesc = "create domain failed," + err.Error()
			return err
		}
		if apierrors.IsNotFound(err) {
			// 创建之前先判断secret对象是否已存在，不存在时才会创建
			var tlsSecret corev1.Secret
			tlsSecret.Namespace = namespace
			tlsSecret.Name = domainName
			tlsSecret.Type = corev1.SecretTypeTLS
			tlsSecret.CreationTimestamp = metav1.Now()

			tlsSecret.Data = make(map[string][]byte)
			tlsSecret.Data["tls.crt"] = []byte(certData.CertServerData + "\n" + certData.CertLinkData)
			tlsSecret.Data["tls.key"] = []byte(certData.CertPrivateKey)

			err = handler.k8sClient.Create(ctx, &tlsSecret)
			if err != nil {
				s.DomainStatus = ccrv1alpha1.CustomDomainStatusFailed
				logger.Error(err, "create domain failed")
				s.DomainStatusDesc = "create domain failed," + err.Error()
				return err
			}
		}

		// 2.创建域名对应的ingress
		var existIngress v1beta1.Ingress
		err = handler.k8sClient.Get(ctx, client.ObjectKey{Namespace: namespace, Name: domainName}, &existIngress)

		if err != nil && !apierrors.IsNotFound(err) {
			s.DomainStatus = ccrv1alpha1.CustomDomainStatusFailed
			logger.Error(err, "create domain failed")
			s.DomainStatusDesc = "create domain failed," + err.Error()
			return err
		}

		if apierrors.IsNotFound(err) {
			// 创建之前先判断ingress对象是否已存在，不存在时才会创建
			var ccrDefaultIngress v1beta1.Ingress
			err = handler.k8sClient.Get(ctx, client.ObjectKey{Namespace: namespace, Name: ccrDefaultDomain}, &ccrDefaultIngress)
			if err != nil {
				return err
			}
			domainIngress := v1beta1.Ingress{}
			domainIngress.Name = domainName
			domainIngress.Namespace = namespace
			domainIngress.Annotations = ccrDefaultIngress.Annotations
			domainIngress.Spec.IngressClassName = ccrDefaultIngress.Spec.IngressClassName

			domainIngressTls := []v1beta1.IngressTLS{
				{
					SecretName: domainName,
					Hosts: []string{
						domainName,
					},
				},
			}
			domainIngress.Spec.TLS = domainIngressTls

			// 自定义域名的ingress规则内容，与默认域名的ingress保持一致
			domainIngressRule := []v1beta1.IngressRule{
				{
					Host:             domainName,
					IngressRuleValue: ccrDefaultIngress.Spec.Rules[0].IngressRuleValue,
				},
			}
			domainIngress.Spec.Rules = domainIngressRule

			err = handler.k8sClient.Create(ctx, &domainIngress)
			if err != nil {
				s.DomainStatus = ccrv1alpha1.CustomDomainStatusFailed
				logger.Error(err, "create domain failed")
				s.DomainStatusDesc = "create domain failed," + err.Error()
				return err
			}
		}

		// 3.创建成功后，状态置为已创建
		s.DomainStatus = ccrv1alpha1.CustomDomainStatusCreated
	}
	if s.DomainStatus == ccrv1alpha1.CustomDomainStatusDeleting {
		// 1.删除域名对应的ingress
		var domainIngress v1beta1.Ingress
		err := handler.k8sClient.Get(ctx, client.ObjectKey{Namespace: namespace, Name: domainName}, &domainIngress)
		if err != nil && !apierrors.IsNotFound(err) {
			logger.Error(err, "delete domain failed")
			s.DomainStatusDesc = "delete domain failed," + err.Error()
			return err
		}
		if err == nil && domainIngress.Name == domainName {
			err = handler.k8sClient.Delete(ctx, &domainIngress)
			if err != nil {
				logger.Error(err, "delete domain failed")
				s.DomainStatusDesc = "delete domain failed," + err.Error()
				return err
			}
		}

		// 2.删除域名对应的secret
		var domainSecret corev1.Secret
		err = handler.k8sClient.Get(ctx, client.ObjectKey{Namespace: namespace, Name: domainName}, &domainSecret)
		if err != nil && !apierrors.IsNotFound(err) {
			logger.Error(err, "delete domain failed")
			s.DomainStatusDesc = "delete domain failed," + err.Error()
			return err
		}

		if err == nil && domainSecret.Name == domainName {
			err = handler.k8sClient.Delete(ctx, &domainSecret)
			if err != nil {
				logger.Error(err, "delete domain failed")
				s.DomainStatusDesc = "delete domain failed," + err.Error()
				return err
			}
		}

		// 3.删除资源成功后，status置为已删除
		s.DomainStatus = ccrv1alpha1.CustomDomainStatusDeleted

	}
	if s.DomainStatus == ccrv1alpha1.CustomDomainStatusCreated {
		// 1.检查证书是否过期
		certCli, err := handler.clients.CertClientForAccount(accountId, userId, handler.config.CertificateEndpoint)
		if err != nil {
			logger.Error(err, "check domain status failed")
			s.DomainStatusDesc = "check domain status failed," + err.Error()
		}
		certDetail, err := certCli.GetCertDetail(certID)
		if err != nil {
			logger.Error(err, "check domain status failed")
			s.DomainStatusDesc = "check domain status failed," + err.Error()
		}
		certExpireTime := certDetail.CertStopTime
		if certExpireTime.Before(time.Now().AddDate(0, 1, 0)) {
			// 证书即将过期，提前30天提醒用户
			// TODO:发送告警到IAM消息中心
			expireDays := int(certExpireTime.Sub(time.Now()).Hours() / 24)
			logger.Info(fmt.Sprintf("check domain status,cert [%s] will expire in [%d] days", certID, expireDays))
			s.DomainStatusDesc = fmt.Sprintf("check domain status,cert [%s] will expire in [%d] days", certID, expireDays)

		}

		if certExpireTime.Before(time.Now()) {
			// 证书已过期, 状态置为失败
			logger.Info(fmt.Sprintf("check domain status,cert [%s] is expired", certID))
			s.DomainStatusDesc = fmt.Sprintf("check domain status,cert [%s] is expired", certID)
			s.DomainStatus = ccrv1alpha1.CustomDomainStatusFailed
			return nil
		}

		// 2.检查域名是否可用
		domainIsPrivate := false
		privateZoneClient, err := handler.clients.PrivateZoneClientForAccount(accountId, userId, handler.config.PrivateZoneEndpoint)
		zones, err := privateZoneClient.ListZone("", "", int64(1000))
		if err != nil {
			return nil
		}
		if zones != nil && len(zones.Zones) > 0 {
			for _, zone := range zones.Zones {
				if strings.HasSuffix(domainName, zone.ZoneName) {
					// 自定义域名以包含在私有域名列表，判断为私有域名
					domainIsPrivate = true
					break
				}
			}
		}
		if !domainIsPrivate {
			//2.1 只有公网域名，才检查备案状态
			bcdCli, err := handler.clients.BcdClientForAccount(accountId, userId, handler.config.DomainCheckEndpoint)
			domainICPed, err := bcdCli.CheckDomainICP(domainName)
			if err != nil {
				logger.Error(err, "handle domain failed,with check domain icp")
			}
			if !domainICPed {
				// 公网域名未备案, 状态置为失败
				logger.Info(fmt.Sprintf("check domain status,the public domain [%s] is not ICP", domainName))
				s.DomainStatusDesc = fmt.Sprintf("check domain status,the public domain [%s] is not ICP", domainName)
				s.DomainStatus = ccrv1alpha1.CustomDomainStatusFailed
				return nil
			}
		}
	}
	return nil
}
