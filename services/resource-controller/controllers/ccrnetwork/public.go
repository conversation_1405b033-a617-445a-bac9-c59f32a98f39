package ccrnetwork

import (
	"context"
	"errors"
	"fmt"
	"net"
	"reflect"
	"strings"
	"sync"

	"github.com/baidubce/bce-sdk-go/services/eip"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/dns"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

type Public struct {
	clients          clientset.ClientSetInterface
	config           *conf.Config
	k8sclient        client.Client
	eipBillingMethod string
}

func NewPublic(config *conf.Config, clients clientset.ClientSetInterface, cli client.Client) *Public {
	var eipBillingMethod string
	if config.EIPBillingMethod != "" {
		eipBillingMethod = config.EIPBillingMethod
	} else {
		eipBillingMethod = "ByTraffic"
	}
	return &Public{
		config:           config,
		clients:          clients,
		k8sclient:        cli,
		eipBillingMethod: eipBillingMethod,
	}
}

func (handler *Public) handlePublic(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	if !strings.HasSuffix(object.Spec.Domain.PublicDomain, ".baidubce.com") {
		// 域名不合法，跳过
		return nil
	}
	// check whitelist
	if err := handler.handleWhiteList(ctx, object); err != nil {
		object.Status.PublicStatus.Reason = "whitelistUpdateFailed"
		return fmt.Errorf("update white list failed: %w", err)
	}

	if object.Spec.PublicLink.EIPOn {
		if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusOpened {
			return nil
		}
		if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusClosed ||
			object.Status.PublicStatus.Status == "" {
			object.Status.PublicStatus.Status = ccrv1alpha1.EIPStatusOpening
			return nil
		}
	}

	if !object.Spec.PublicLink.EIPOn {
		if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusClosed {
			return nil
		}
		if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusOpened ||
			object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusOpening {
			object.Status.PublicStatus.Status = ccrv1alpha1.EIPStatusClosing
			return nil
		}
	}

	if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusOpening {
		// handle eip creating
		return handler.handleCreateEIPAndBinding(ctx, object)
	}

	if object.Status.PublicStatus.Status == ccrv1alpha1.EIPStatusClosing {
		// handle eip closing
		return handler.handleDeleteAndUnbindEip(ctx, object)
	}

	return nil
}

// only set status, don't update status remotely
func (handler *Public) handleWhiteList(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	// https://en.wikipedia.org/wiki/Reserved_IP_addresses
	cidr := []string{"***********/16", "10.0.0.0/8", "**********/12", "**********/10"}

	if object.Spec.PublicLink.EIPOn &&
		reflect.DeepEqual(object.Spec.PublicLink.WhiteList, object.Status.PublicStatus.WhiteList) &&
		len(object.Spec.PublicLink.WhiteList) != 0 {
		return nil
	}

	if !object.Spec.PublicLink.EIPOn &&
		len(object.Status.PublicStatus.WhiteList) == 0 {
		return nil
	}

	if object.Spec.PublicLink.EIPOn &&
		len(object.Spec.PublicLink.WhiteList) != 0 {
		for _, v := range object.Spec.PublicLink.WhiteList {
			cidr = append(cidr, v.IPCidr)
		}
	}

	var ingressCm corev1.ConfigMap
	cmName := fmt.Sprintf("%s-ingress-controller", object.GetName())
	whiteRangeName := "whitelist-source-range"
	err := handler.k8sclient.Get(ctx, client.ObjectKey{Namespace: object.GetNamespace(), Name: cmName}, &ingressCm)
	// if ingress controller configmap is not provided, will not set whitelist
	if err != nil {
		if apierrors.IsNotFound(err) {
			return nil
		}

		object.Status.PublicStatus.Reason = "getConfigMapFailed"
		return fmt.Errorf("get configmap failed: %w", err)
	}

	if ingressCm.Data[whiteRangeName] == strings.Join(cidr, ",") {
		if len(cidr) == 0 {
			object.Status.PublicStatus.WhiteList = []ccrv1alpha1.NetworkWhiteItem{}
		} else {
			object.Status.PublicStatus.WhiteList = object.Spec.PublicLink.WhiteList
		}
		object.Status.PublicStatus.Reason = ""
		return nil
	}

	ingressCm.Data[whiteRangeName] = strings.Join(cidr, ",")

	err = handler.k8sclient.Update(ctx, &ingressCm)
	if err != nil {
		object.Status.PublicStatus.Reason = "updateConfigMapFailed"
		return fmt.Errorf("update configmap failed: %w", err)
	}

	object.Status.PublicStatus.WhiteList = object.Spec.PublicLink.WhiteList
	object.Status.PublicStatus.Reason = ""
	return nil
}

// handleCreateEIPAndBinding 函数用于创建并绑定EIP
//
// 参数 ctx：上下文对象
// 参数 object：CNCNetwork 对象指针
// 返回值：错误信息（如果存在）
func (handler *Public) handleCreateEIPAndBinding(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	logger := log.FromContext(ctx)

	// TODO list all eips
	if object.Status.PublicStatus.Address == "" {
		listResult, err := handler.clients.EipClient().ListEip(&eip.ListEipArgs{Status: "available"})
		if err != nil {
			logger.V(2).Error(err, "list available eips failed")
			object.Status.PublicStatus.Reason = "listEipFailed"
			return fmt.Errorf("list availbale eip failed: %w", err)
		}

		needCreate := true
		if len(listResult.EipList) != 0 {
			for _, v := range listResult.EipList {
				if v.Name == object.GetName() {
					object.Status.PublicStatus.Address = v.Eip
					needCreate = false
					break
				}
			}
		}

		if needCreate {
			eipResult, err := handler.clients.EipClient().CreateEip(&eip.CreateEipArgs{
				Name:            object.GetName(),
				BandWidthInMbps: 100,
				Billing: &eip.Billing{
					PaymentTiming: "Postpaid",
					BillingMethod: handler.eipBillingMethod,
				},
				ClientToken: string(uuid.NewUUID()),
			})

			if err != nil {
				logger.Error(err, "create eip failed")
				object.Status.PublicStatus.Reason = "eipCreateFailed"
				return fmt.Errorf("creat eip failed: %w", err)
			}

			object.Status.PublicStatus.Address = eipResult.Eip
		}

		object.Status.PublicStatus.Reason = ""
	}

	// bind to an instance
	listResult, err := handler.clients.EipClient().ListEip(&eip.ListEipArgs{Eip: object.Status.PublicStatus.Address})
	if err != nil || len(listResult.EipList) == 0 {
		logger.V(2).Error(err, "list eip failed")
		object.Status.PublicStatus.Reason = "getEipFailed"
		return fmt.Errorf("list eip failed: %w", err)
	}

	logger.Info(fmt.Sprintf("list eip result: %v", listResult))

	switch listResult.EipList[0].Status {
	case "available":
		err = handler.clients.EipClient().BindEip(object.Status.PublicStatus.Address, &eip.BindEipArgs{
			InstanceType: "BLB",
			InstanceId:   object.Status.BLBID,
			ClientToken:  string(uuid.NewUUID()),
		})

		if err != nil {
			logger.Error(err, fmt.Sprintf("binding eip to instance %v failed", object.Status.BLBID))
			object.Status.PublicStatus.Reason = "bindEipFailed"
			return err
		}
	case "binded":
		logger.Info("eip has binded, start domain bind")
	default:
		object.Status.PublicStatus.Reason = "EIPBinding"
		logger.Info(fmt.Sprintf("eip status is %s wait for next round", listResult.EipList[0].Status))
		return fmt.Errorf("waiting for eip binded")
	}

	domain, err := utils.NewDomain(object.Spec.Domain.PublicDomain)
	if err != nil {
		object.Status.PublicStatus.Reason = err.Error()
		return err
	} else {
		err = handler.createDNSRecordParallel(ctx,
			domain,
			object.Status.PublicStatus.Address,
			object.Status.PublicStatus.Domain != "")
		if err != nil {
			logger.V(2).Error(err, "createDNSRecordParallel failed")
			object.Status.PublicStatus.Reason = err.Error()
			return nil
		}

		if object.Status.PublicStatus.Domain == "" {
			object.Status.PublicStatus.Domain = object.Spec.Domain.PublicDomain
			object.Status.PublicStatus.Reason = "createDNSRecordSuccess"
			return nil
		}

		object.Status.PublicStatus.Reason = ""
	}

	object.Status.PublicStatus.Status = ccrv1alpha1.EIPStatusOpened
	return nil
}

func (handler *Public) handleDeleteAndUnbindEip(ctx context.Context, object *ccrv1alpha1.CNCNetwork) error {
	// unbind eip first
	logger := log.FromContext(ctx)

	// list
	if object.Status.PublicStatus.Address == "" {
		object.Status.PublicStatus.Status = ccrv1alpha1.EIPStatusClosed
		object.Status.PublicStatus.Reason = ""
		return nil
	}

	// remove dns record
	if object.Status.PublicStatus.Domain != "" {
		domain, err := utils.NewDomain(object.Spec.Domain.PublicDomain)
		if err != nil {
			return err
		}

		err = handler.removeDNSRecordIfExists(ctx, domain, object.Status.PublicStatus.Address, "inet")
		if err != nil {
			return fmt.Errorf("remove inet dns record failed: %w", err)
		}

		err = handler.removeDNSRecordIfExists(ctx, domain, object.Status.PublicStatus.Address, "pnet")
		if err != nil {
			return fmt.Errorf("remove pnet dns record failed: %w", err)
		}

		object.Status.PublicStatus.Domain = ""
	}

	listResult, err := handler.clients.EipClient().ListEip(&eip.ListEipArgs{Eip: object.Status.PublicStatus.Address})
	if err != nil {
		logger.Error(err, fmt.Sprintf("list eip %v failed", object.Status.PublicStatus.Address))
		object.Status.PublicStatus.Reason = "eipListFailed"
		return err
	}

	if len(listResult.EipList) == 0 || listResult.EipList[0].Name != object.GetName() {
		object.Status.PublicStatus.Address = ""
		object.Status.PublicStatus.Status = ccrv1alpha1.EIPStatusClosed
		object.Status.PublicStatus.Reason = ""
		return nil
	}

	eipInfo := listResult.EipList[0]
	switch eipInfo.Status {
	case "binded":
		err = handler.clients.EipClient().UnBindEip(object.Status.PublicStatus.Address, string(uuid.NewUUID()))
		if err != nil {
			logger.Error(err, "unbind eip failed")
			object.Status.PublicStatus.Reason = "unbindingFailed"
			return err
		}

		logger.Info("eip is binded, wait for available")
		return nil
	case "unbinding":
		object.Status.PublicStatus.Reason = "unbinding"
		return nil
	case "available":
		err = handler.clients.EipClient().DeleteEip(object.Status.PublicStatus.Address, string(uuid.NewUUID()))
		if err != nil {
			logger.Error(err, fmt.Sprintf("delete eip %v failed", object.Status.PublicStatus.Address))
			object.Status.PublicStatus.Reason = "deleteEipFailed"
			return err
		}

		object.Status.PublicStatus.Status = ccrv1alpha1.EIPStatusClosed
		object.Status.PublicStatus.Address = ""
		object.Status.PublicStatus.Reason = ""
		return nil
	}

	return nil
}

func (handler *Public) createDNSRecordParallel(ctx context.Context, domain utils.Domain, ip string, resolved bool) error {
	var wg sync.WaitGroup

	wg.Add(2)

	var errRdns, errDns error

	go func() {
		errDns = handler.createDNSRecordIfNotExists(ctx, domain, ip, "inet", resolved)
		wg.Done()
	}()

	go func() {
		errRdns = handler.createDNSRecordIfNotExists(ctx, domain, ip, "pnet", resolved)
		wg.Done()
	}()

	wg.Wait()

	if errRdns != nil || errDns != nil {
		return fmt.Errorf("create DNSRecord failed: rdns=%s, dns=%s", errRdns, errDns)
	}

	return nil
}

func (handler *Public) createDNSRecordIfNotExists(ctx context.Context, domain utils.Domain, ip, view string, resolved bool) error {
	logger := log.FromContext(ctx)
	// try created
	var err error

	if !resolved {
		record := &dns.DNSRecord{
			Domain: domain.GetName(),
			Zone:   domain.GetZone(),
			Type:   "A",
			TTL:    600,
			Rdata:  ip,
			View:   domain.GetView(view),
		}

		switch view {
		case "inet":
			err = handler.clients.DnsClient().AddRecord(record)
		case "pnet":
			err = handler.clients.DnsClient().AddRDNSRecord(record)
		default:
			return fmt.Errorf("invalid view for dns record: %s", view)
		}

		if err != nil {
			var dnsErr *dns.DNSError
			if errors.As(err, &dnsErr) && dnsErr.IsAlreadyExist() {
				return nil
			}

			logger.V(2).Error(err, fmt.Sprintf("create dns record failed"))
			return err
		}

		return nil
	}

	var ips []string
	switch view {
	case "inet":
		ips, err = DNSLookup(domain.String(), fmt.Sprintf("%s:53", handler.config.InetDNSServer))
	case "pnet":
		ips, err = DNSLookup(domain.String(), fmt.Sprintf("%s:53", handler.config.PnetDNSServer))
	default:
		return fmt.Errorf("invalid view for dns record: %s", view)
	}

	if err != nil {
		logger.V(9).Error(err, "lookup host for view: "+view)
		var dnsErr *net.DNSError
		if errors.As(err, &dnsErr) && dnsErr.IsNotFound {
			// no dns found
			return fmt.Errorf("dns host in %s is not found", view)
		}

		return err
	}

	if len(ips) == 0 {
		return fmt.Errorf("dns record is empty")
	}

	for _, v := range ips {
		if v == ip {
			return nil
		}
	}

	return fmt.Errorf("dns record is not found")
}

func (handler *Public) removeDNSRecordIfExists(ctx context.Context, domain utils.Domain, ip, view string) error {
	logger := log.FromContext(ctx)
	// try created
	var err error

	record := &dns.DNSRecord{
		Domain: domain.GetName(),
		Zone:   domain.GetZone(),
		Type:   "A",
		TTL:    600,
		Rdata:  ip,
		View:   domain.GetView(view),
	}

	switch view {
	case "inet":
		err = handler.clients.DnsClient().DeleteRecord(record)
	case "pnet":
		err = handler.clients.DnsClient().DeleteRDNSRecord(record)
	default:
		return fmt.Errorf("invalid view for dns record: %s", view)
	}

	if err != nil {
		var dnsErr *dns.DNSError
		if errors.As(err, &dnsErr) && dnsErr.IsNotFound() {
			return nil
		}

		logger.V(2).Error(err, fmt.Sprintf("delete dns record failed"))
		return err
	}

	return nil
}
