package ccrnetwork

import (
	"reflect"
	"testing"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func TestNewHandler(t *testing.T) {
	type args struct {
		config  *conf.Config
		clients *clientset.ClientSet
		cli     client.Client
	}
	tests := []struct {
		name string
		args args
		want *Handler
	}{
		{
			name: "case1",
			args: args{
				config: &conf.Config{ServiceName: "ccr"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewHandler(tt.args.config, tt.args.clients, tt.args.cli); !reflect.DeepEqual(got, got) {
				t.<PERSON>rrorf("NewHandler() = %v, want %v", got, tt.want)
			}
		})
	}
}
