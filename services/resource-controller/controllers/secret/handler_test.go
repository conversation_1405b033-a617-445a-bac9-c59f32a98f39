package secret

import (
	"context"
	"testing"
	"time"

	"github.com/baidubce/bce-sdk-go/auth"
	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
	mockclientset "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/services/resource-controller/clientset"
)

func TestHandler_Handle(t *testing.T) {
	type fields struct {
		clients   clientset.ClientSetInterface
		config    *conf.Config
		k8sclient client.Client
	}
	type args struct {
		ctx    context.Context
		secret *corev1.Secret
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "no user",
			fields: func() fields {
				scheme := runtime.NewScheme()
				assert.NoError(t, clientgoscheme.AddToScheme(scheme))
				assert.NoError(t, v1alpha1.AddToScheme(scheme))
				k8sCli := fake.NewFakeClientWithScheme(scheme, &v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{
						Name:      "test",
						Namespace: "test",
					},
				}, &corev1.Secret{
					ObjectMeta: v1.ObjectMeta{
						Namespace: "test",
						Name:      "test",
					},
					Data: map[string][]byte{
						"test": []byte(`{"username": {}}`),
					},
				})

				csInterface := &mockclientset.ClientSet{}
				csInterface.On("StsCredentialWithExpiredAt", mock.Anything, mock.Anything).Return(nil, nil)

				return fields{
					k8sclient: k8sCli,
					clients:   csInterface,
				}
			}(),
			args: args{
				ctx: context.Background(),
				secret: &corev1.Secret{
					ObjectMeta: v1.ObjectMeta{
						Namespace: "test",
						Name:      "test",
					},
					Data: map[string][]byte{
						"test": []byte(`{"username": {}}`),
					},
				},
			},
			wantErr: false,
		},
		{
			name: "user exist",
			fields: func() fields {
				scheme := runtime.NewScheme()
				assert.NoError(t, clientgoscheme.AddToScheme(scheme))
				assert.NoError(t, v1alpha1.AddToScheme(scheme))
				k8sCli := fake.NewFakeClientWithScheme(scheme, &v1alpha1.CNCNetwork{
					ObjectMeta: v1.ObjectMeta{
						Name:      "test",
						Namespace: "test",
					},
				}, &corev1.Secret{
					ObjectMeta: v1.ObjectMeta{
						Namespace: "test",
						Name:      "test",
					},
					Data: map[string][]byte{
						"test": []byte(`{"username": {}}`),
					},
				})

				csInterface := &mockclientset.ClientSet{}
				csInterface.On("StsCredentialWithExpiredAt", mock.Anything, mock.Anything).Return(&clientset.Credentials{
					BceCredentials: &auth.BceCredentials{
						AccessKeyId:     "test",
						SecretAccessKey: "test",
					},
					ExpiredAt: time.Now(),
				}, nil)

				return fields{
					k8sclient: k8sCli,
					clients:   csInterface,
				}
			}(),
			args: args{
				ctx: context.Background(),
				secret: &corev1.Secret{
					ObjectMeta: v1.ObjectMeta{
						Namespace: "test",
						Name:      "test",
					},
					Data: map[string][]byte{
						"test": []byte(`{"username": {}}`),
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := Handler{
				clients:   tt.fields.clients,
				config:    tt.fields.config,
				k8sclient: tt.fields.k8sclient,
			}
			if err := h.Handle(tt.args.ctx, tt.args.secret); (err != nil) != tt.wantErr {
				t.Errorf("Handler.Handle() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
