package secret

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"

	"time"

	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

type Credential struct {
	Ak           string    `json:"ak"`
	Sk           string    `json:"sk"`
	SessionToken string    `json:"sessionToken"`
	ExpiredAt    time.Time `json:"expiredAt"`
}

type Handler struct {
	clients clientset.ClientSetInterface

	config    *conf.Config
	k8sclient client.Client
}

func NewHandler(config *conf.Config, clients clientset.ClientSetInterface, cli client.Client) *Handler {
	return &Handler{
		clients:   clients,
		config:    config,
		k8sclient: cli,
	}
}

func (h Handler) Handle(ctx context.Context, secret *corev1.Secret) error {
	logger := log.FromContext(ctx)
	obj := secret.DeepCopy()

	ccrnetwrok := ccrv1alpha1.CNCNetwork{}
	err := h.k8sclient.Get(ctx, client.ObjectKey{Namespace: obj.Namespace, Name: obj.Namespace}, &ccrnetwrok)
	if err != nil {
		logger.Error(err, fmt.Sprintf("get cncnetwork object failed for %v", obj.Namespace))
		return err
	}

	accountId := ccrnetwrok.Spec.AccountID

	for instanceId, usernameBytes := range obj.Data {

		var usernameMap map[string]*Credential

		if err := json.Unmarshal(usernameBytes, &usernameMap); err != nil {
			logger.Error(err, "json unmarshal usernameBytes failed", "instanceId", instanceId)
			return err
		}

		for username, credential := range usernameMap {
			if credential != nil {
				if !credential.ExpiredAt.UTC().Add(-15 * time.Minute).Before(time.Now().UTC()) {
					logger.Info("expiredAt is before now 15 minute", "expiredAt", credential.ExpiredAt)
					continue
				}
			}

			cred, err := h.clients.StsCredentialWithExpiredAt(accountId, username)
			if err != nil {
				logger.Error(err, "get credential failed", "accountId", accountId, "username", username)
				return err
			}

			if cred == nil {
				logger.Info("user maybe delete from IAM", "accountId", accountId, "username", username)
				delete(usernameMap, username)
			} else {
				usernameMap[username] = &Credential{
					Ak:           cred.AccessKeyId,
					Sk:           cred.SecretAccessKey,
					SessionToken: cred.SessionToken,
					ExpiredAt:    cred.ExpiredAt,
				}
			}
		}
		usernames, err := json.Marshal(usernameMap)
		if err != nil {
			logger.Error(err, "json marshal usernameMap failed", "instanceId", instanceId)
			return err
		}
		obj.Data[instanceId] = usernames
	}

	if !reflect.DeepEqual(obj.Data, secret.Data) {
		if err := h.k8sclient.Update(ctx, obj); err != nil {
			logger.Error(err, "update k8s secret failed", "secret", obj)
			return err
		}
	}

	return nil
}
