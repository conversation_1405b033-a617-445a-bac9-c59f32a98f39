package controllers

import (
	"context"
	"errors"
	"fmt"
	"time"

	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"golang.org/x/time/rate"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/util/workqueue"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/log"

	//ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	pkgerrors "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/errors"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	wfcontroller "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/workflow"
)

// PostgresReconciler reconciles a Postgres object
type WorkflowReconciler struct {
	client.Client
	Scheme   *runtime.Scheme
	Handler  *wfcontroller.Handler
	SqlCli   models.Interface
	Interval time.Duration
}

func (r *WorkflowReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	logger.Info("start reconcile workflow")
	// your logic here
	object := wfv1.Workflow{}
	err := r.Client.Get(ctx, req.NamespacedName, &object)
	if err != nil {
		if apierrors.IsNotFound(err) {
			logger.Info("object is not found, will not process anymore")
			return ctrl.Result{}, nil
		}

		logger.V(2).Error(err, fmt.Sprintf("get workflow object failed for %v", req.NamespacedName))
		return ctrl.Result{RequeueAfter: 2 * time.Second}, err
	}

	// filter image build
	if object.Labels["kind"] != "image-build" {
		return ctrl.Result{}, nil
	}

	logger.Info("start to process", "status", object.Status.Phase)
	err = r.Handler.Handle(ctx, &object)
	if err != nil {
		var retryErr *pkgerrors.ReconcileRetryError
		if errors.As(err, &retryErr) {
			logger.Info(err.Error())
			return ctrl.Result{
				RequeueAfter: retryErr.RetryAfter,
			}, nil
		}

		logger.V(2).Error(err, "handle workfow object failed")
		return ctrl.Result{RequeueAfter: 15 * time.Second}, err
	}

	return ctrl.Result{}, nil
}

func (r *WorkflowReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&wfv1.Workflow{}).
		WithOptions(controller.Options{
			RateLimiter: workqueue.NewMaxOfRateLimiter(
				workqueue.NewItemExponentialFailureRateLimiter(time.Second, 30*time.Second),
				// 10 qps, 100 bucket size.  This is only for retry speed and its only the overall factor (not per item)
				&workqueue.BucketRateLimiter{Limiter: rate.NewLimiter(rate.Limit(10), 100)},
			),
		}).
		Complete(r)
}

func (r *WorkflowReconciler) Start(ctx context.Context) error {
	logger := log.FromContext(ctx)
	wait.Until(func() {
		err := r.sync(ctx)
		if err != nil {
			logger.Error(err, "workflow sync failed, wait for next round")
		}
	}, r.Interval, ctx.Done())

	return nil
}

func (r *WorkflowReconciler) sync(ctx context.Context) error {
	logger := log.FromContext(ctx)

	// more time
	readyAt := time.Now().Add(-10 * time.Minute)
	// TODO: list all
	imageBuilds, _, err := r.SqlCli.ListImageBuildPaged("", map[string]interface{}{
		"status": model.ImageBuildStatusPending,
	}, 1, 1000)
	if err != nil {
		logger.Error(err, "list image build failed")
		return err
	}

	logger.Info("some image build need to sync", "len", len(imageBuilds))

	for _, v := range imageBuilds {
		imageBuildId := fmt.Sprintf("image-build-%d", v.ID)

		if v.CreatedAt.After(readyAt) {
			logger.Info("image build is not ready yet", "id", imageBuildId)
			continue
		}

		var obj wfv1.Workflow
		err = r.Get(ctx, client.ObjectKey{Namespace: v.InstanceID, Name: imageBuildId}, &obj)
		if err == nil {
			logger.Info("image build exist, skip...")
			// object exist, skip update
			continue
		}

		if err != nil && !apierrors.IsNotFound(err) {
			logger.Error(err, "get obj failed", "objectName", imageBuildId)
			continue
		}

		logger.Info("object is not found", "objectName", imageBuildId)
		// object not found, delete

		err = r.SqlCli.SetImageBuildStatusAndLog(v.ID, model.ImageBuildStatusFailed, "build environment failed")
		if err != nil {
			logger.Error(err, "set image build status failed")
		}
	}

	return nil
}
