package controllers

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models/devops"
	devopsmock "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models/devops/mock"
	modelsmock "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/models/mock"
)

func Test_Devops_run(t *testing.T) {
	modelCli := modelsmock.NewMockInterface(gomock.NewController(t))
	devopsCli := devopsmock.NewMockInterface(gomock.NewController(t))

	syncer := NewDevopsSyncer("empty", modelCli, devopsCli, 10*time.Second)
	devopsCli.EXPECT().FindInstanceByLatestUpdateTime(gomock.Eq("empty")).Return(nil, nil)
	modelCli.EXPECT().FindInstanceFromLastUpdated(gomock.Eq(time.Unix(0, 0))).Return(nil, nil)
	assert.Nil(t, syncer.run(context.Background()))

	//
	syncer.region = "12300 return"
	err := errors.New("expected")
	devopsCli.EXPECT().FindInstanceByLatestUpdateTime(gomock.Eq("12300 return")).Return(&devops.Instance{UpdatedAt: time.Unix(12300, 0)}, nil)
	modelCli.EXPECT().FindInstanceFromLastUpdated(gomock.Eq(time.Unix(12300, 0))).Return(nil, err)
	assert.Equal(t, err, syncer.run(context.Background()))

	//
	syncer.region = "12400 return"
	devopsCli.EXPECT().FindInstanceByLatestUpdateTime(gomock.Eq("12400 return")).Return(&devops.Instance{UpdatedAt: time.Unix(12400, 0)}, nil)
	modelCli.EXPECT().FindInstanceFromLastUpdated(gomock.Eq(time.Unix(12400, 0))).Return([]*models.Instance{{}}, nil)
	devopsCli.EXPECT().UpsertInstance(gomock.Eq(&devops.Instance{Region: syncer.region})).Return(nil)
	assert.Nil(t, syncer.run(context.Background()))

	//
	syncer.region = "12500 return"
	devopsCli.EXPECT().FindInstanceByLatestUpdateTime(gomock.Eq("12500 return")).Return(&devops.Instance{UpdatedAt: time.Unix(12500, 0)}, nil)
	modelCli.EXPECT().FindInstanceFromLastUpdated(gomock.Eq(time.Unix(12500, 0))).Return([]*models.Instance{{}}, nil)
	devopsCli.EXPECT().UpsertInstance(gomock.Eq(&devops.Instance{Region: syncer.region})).Return(err)
	assert.Equal(t, err, syncer.run(context.Background()))
}
