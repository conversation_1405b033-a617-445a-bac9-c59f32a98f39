package ccrquota

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/usersetting"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

type Handler struct {
	clients clientset.ClientSetInterface

	config    *conf.Config
	k8sclient client.Client
}

func NewHandler(config *conf.Config, clients clientset.ClientSetInterface, cli client.Client) *Handler {
	return &Handler{
		clients:   clients,
		config:    config,
		k8sclient: cli,
	}
}

func (handler *Handler) Handle(ctx context.Context, object *ccrv1alpha1.CCRQuota) error {
	obj := object.DeepCopy()

	now := metav1.Now()
	obj.Status.LastProbeTime = &now
	q, err := handler.getQuota(ctx, obj)
	if err != nil {
		obj.Status.Message = err.Error()
		obj.Status.Phase = ccrv1alpha1.CCRQuotaPhaseUnready
		handler.k8sclient.Status().Update(ctx, obj)
		return fmt.Errorf("get quota failed: %w", err)
	} else {
		obj.Status.ChartRepo = q.chartRepo
		obj.Status.Project = q.project
		obj.Status.ImageRepo = q.imageRepo
		obj.Status.Phase = ccrv1alpha1.CCRQuotaPhaseReady
		obj.Status.Message = ""
	}

	if handler.quotaChanged(&object.Status, q) {
		return handler.k8sclient.Status().Update(ctx, obj)
	}

	return nil
}

func (handler *Handler) quotaChanged(status *ccrv1alpha1.CCRQuotaStatus, q *quota) bool {
	if (status.ChartRepo != q.chartRepo && (status.ChartRepo == nil || q.chartRepo == nil)) ||
		(status.ImageRepo != q.imageRepo && (status.ImageRepo == nil || q.imageRepo == nil)) ||
		(status.Project != q.project && (status.Project == nil || q.project == nil)) ||
		(status.ChartRepo != nil && q.chartRepo != nil && q.chartRepo.Value() != status.ChartRepo.Value()) ||
		(status.ImageRepo != nil && q.imageRepo != nil && q.imageRepo.Value() != status.ImageRepo.Value()) ||
		(status.Project != nil && q.project != nil && q.project.Value() != status.Project.Value()) {
		return true
	}

	return false
}

type quota struct {
	imageRepo *resource.Quantity
	chartRepo *resource.Quantity
	project   *resource.Quantity
}

func (handler *Handler) getQuota(ctx context.Context, object *ccrv1alpha1.CCRQuota) (*quota, error) {
	logger := log.FromContext(ctx)

	nsQuotaName := usersetting.NSQuotaForSpec(string(object.Spec.Type))
	chartQuotaName := usersetting.ChartQuotaForSpec(string(object.Spec.Type))
	imageQuotaName := usersetting.ImageQuotaForSpec(string(object.Spec.Type))

	result, err := handler.clients.Usersetting().ListUserQuotas(
		string(uuid.NewUUID()),
		&usersetting.ListQuotasRequest{
			UserType:  usersetting.UserTypeAccountID,
			UserValue: object.Spec.AccountID,
			QuotaTypes: []usersetting.QuotaType{
				nsQuotaName,
				chartQuotaName,
				imageQuotaName,
			},
		},
	)

	if err != nil {
		logger.V(2).Error(err, "list user quota failed")
		return nil, err
	}

	if result == nil {
		logger.V(2).Error(fmt.Errorf("no response found"), "list user quota get nil response")
		return nil, fmt.Errorf("no response found")
	}

	transformFunc := func(q string) (*resource.Quantity, error) {
		if q == "" {
			return nil, nil
		}

		quan, err := resource.ParseQuantity(q)
		if err != nil {
			return nil, err
		}

		return &quan, nil
	}

	q := quota{}
	for k, v := range result.QuotaType2Quota {
		quan, err := transformFunc(v)
		if err != nil {
			logger.V(2).Error(err, fmt.Sprintf("cannot tranform %v to quantity", v))
			return nil, err
		}

		switch k {
		case nsQuotaName:
			q.project = quan
		case chartQuotaName:
			q.chartRepo = quan
		case imageQuotaName:
			q.imageRepo = quan
		}
	}

	return &q, nil
}
