package redis

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/baidubce/bce-sdk-go/services/bcc/api"

	"github.com/baidubce/bce-sdk-go/services/scs"

	"github.com/baidubce/bce-sdk-go/bce"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

type Handler struct {
	clients clientset.ClientSetInterface

	config    *conf.Config
	k8sclient client.Client
	vpcID     string
	dbs       *sync.Map

	failOverDuration time.Duration
}

// NewHandler 新建一个 Handler 对象，用于处理请求。
//
// 参数:
//   - config *conf.Config: 配置信息的指针，包含了 VPC ID、子网 ID、可用区名称等信息。
//   - clients *clientset.ClientSet: 客户端集合的指针，包含了与 Kubernetes API Server 和 AWS API Gateway 的连接。
//   - vpcID string: VPC ID，用于标识要创建的 Pod 所属的 VPC。
//   - cli client.Client: Kubernetes Client 的实例，用于与 Kubernetes API Server 进行交互。
//
// 返回值:
//   - *Handler: 一个 Handler 类型的指针，表示新建的 Handler 对象。
func NewHandler(config *conf.Config, clients clientset.ClientSetInterface, vpcID string, cli client.Client) *Handler {
	return &Handler{
		config:    config,
		clients:   clients,
		k8sclient: cli,
		vpcID:     vpcID,
		dbs:       new(sync.Map),

		failOverDuration: 10 * time.Minute,
	}
}

// Handle Handle 处理函数，用于处理 Redis 对象的各种情况。
// 如果对象已被删除，则调用 handleDelete 方法进行处理；
// 如果对象的状态为空字符串，则调用 handlePendingPhase 方法进行处理；
// 如果对象的状态为创建中，则调用 handleCreate 方法进行处理；
// 如果对象的类型与其规格中的不同，则调用 handleUpgrading 方法进行处理。
// 否则，返回 nil。
// 参数 ctx context.Context：上下文信息；
// 参数 object *ccrv1alpha1.Redis：需要处理的 Redis 对象指针；
// 返回值 error：处理过程中出现的错误，如果没有错误则返回 nil。
func (handler *Handler) Handle(ctx context.Context, object *ccrv1alpha1.Redis) error {
	obj := object.DeepCopy()
	// need to create and update
	switch {
	case obj.DeletionTimestamp != nil:
		return handler.handleDelete(ctx, obj)
	case obj.Status.Phase == "":
		return handler.handlePendingPhase(ctx, obj)
	case obj.Status.Phase == ccrv1alpha1.RedisStatusCreating:
		return handler.handleCreate(ctx, obj)
	case obj.Status.Type != obj.Spec.InstanceType:
		// TODO 升配先不实现
		//return handler.handleUpgrading(ctx, obj)
	}

	return nil
}

// handlePendingPhase 处理待处理阶段，首先设置最终化器，如果设置成功则等待下一轮更新，否则返回错误；
// 如果最终化器已经被设置过，则等待阶段的更新。
//
// 参数：
//
//	ctx context.Context                  上下文信息，包含日志记录器和请求相关值
//	object *ccrv1alpha1.Redis            需要更新状态的MySQL对象指针
//
// 返回值：
//
//	error                                nil表示操作成功，非nil表示操作失败，包含错误信息
func (handler *Handler) handlePendingPhase(ctx context.Context, object *ccrv1alpha1.Redis) error {
	// set finalizer first
	logger := log.FromContext(ctx)
	finalizerSetted, err := handler.setFinalizer(ctx, object)
	if err != nil {
		logger.V(2).Error(err, "update finalizer failed")
		return err
	}

	// finalizer setted, waitfor next round
	if !finalizerSetted {
		logger.V(2).Info("finalizer has been setted, wait for phase to be update")
		return nil
	}

	object.Status.Phase = ccrv1alpha1.RedisStatusCreating
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime
	object.Status.LastTransitionTime = &currentTime
	object.Status.Type = object.Spec.InstanceType

	return handler.k8sclient.Status().Update(ctx, object)
}

// handleDelete 处理删除操作，包括获取数据库、创建数据库和等待数据库可以访问。如果不存在实例或者实例已经被删除，则直接移除最终化器。
// ctx: 上下文对象，包含日志记录器
// object: *ccrv1alpha1.Redis，需要处理的 Redis 对象
// 返回值：error，如果发生错误则返回该错误
func (handler *Handler) handleDelete(ctx context.Context, object *ccrv1alpha1.Redis) error {
	logger := log.FromContext(ctx)
	// 1. get database
	// 2. if not exist create database
	// 3. if exist, wait database can be access
	if object.Status.InstanceID == "" {
		return handler.removeFinalizer(ctx, object)
	}

	_, err := handler.clients.ScsClient().GetInstanceDetail(object.Status.InstanceID)
	var bceError *bce.BceServiceError
	if errors.As(err, &bceError) && bceError.StatusCode == http.StatusNotFound {
		return handler.removeFinalizer(ctx, object)
	}

	if err != nil {
		logger.V(2).Error(err, fmt.Sprintf("get SCS instaince %v failed", object.Status.InstanceID))
		handler.updateReason(ctx, object, "DeleteFailed", err.Error())
		return err
	}

	err = handler.clients.ScsClient().DeleteInstance(object.Status.InstanceID, string(uuid.NewUUID()))
	if err != nil {
		bceErr := &bce.BceServiceError{}
		if errors.As(err, &bceErr) && bceErr.StatusCode == 404 {
			return nil
		}

		logger.V(2).Error(err, fmt.Sprintf("get the detail of instance %v failed", object.Status.InstanceID))
		handler.updateReason(ctx, object, "InstanceDeleteFailed", err.Error())
		return err
	}

	return handler.removeFinalizer(ctx, object)
}

// tryGetScsInstance 尝试获取指定名称的 SCS 实例，如果找到则返回该实例，否则返回 nil
func (handler *Handler) tryGetScsInstance(ctx context.Context, instanceName string) (*scs.InstanceModel, error) {
	logger := log.FromContext(ctx)
	marker, maxKeys := "", 1000
	isTruncated := true

	for isTruncated {
		res, err := handler.clients.ScsClient().ListInstances(&scs.ListInstancesArgs{
			Marker:  marker,
			MaxKeys: maxKeys,
		})

		if err != nil {
			logger.V(2).Error(err, "list SCS failed")
			return nil, err
		}

		for _, v := range res.Instances {
			if v.InstanceName == instanceName {
				return &v, nil
			}
		}

		marker = res.NextMarker
		isTruncated = res.IsTruncated
	}

	return nil, nil
}

// handleCreate 处理创建 MySQL 实例的逻辑
// ctx context.Context：上下文信息，包含日志记录器和请求相关参数
// object *ccrv1alpha1.Redis：需要创建的 MySQL 实例对象
// 返回值 error：如果发生错误则返回该错误
func (handler *Handler) handleCreate(ctx context.Context, object *ccrv1alpha1.Redis) error {
	logger := log.FromContext(ctx)
	logger.V(2).Info("start create redis loop", "instance", object.GetName())
	password, err := base64.StdEncoding.DecodeString(object.Spec.Password)
	if err != nil {
		logger.Error(err, "password is invalid")
		object.Status.Phase = ccrv1alpha1.RedisStatusFailed
		handler.updateReason(ctx, object, "ScsAccountNotReady", err.Error())
		return err
	}

	if object.Status.InstanceID == "" {

		instance, err := handler.tryGetScsInstance(ctx, object.GetName())
		if err != nil {
			logger.Error(err, "try to list SCS instance failed")
			handler.updateReason(ctx, object, "ScsNotReady", err.Error())
			return err
		}

		if instance == nil {
			// get zone
			subnets, err := handler.clients.ScsClient().ListSubnets(&scs.ListSubnetsArgs{VpcID: handler.vpcID})
			if err != nil {
				logger.V(2).Error(err, fmt.Sprintf("list subnets failed: %v", handler.vpcID))
				handler.updateReason(ctx, object, "SubnetsNotReady", err.Error())
				return err
			}

			subnetMap := make([]scs.Subnet, 0)
			zoneNameSet := make(map[string]interface{})
			for _, v := range subnets.SubnetOriginals {
				subnetMap = append(subnetMap, scs.Subnet{
					ZoneName: v.ZoneName,
					SubnetID: v.SubnetID,
				})

				zoneNameSet[v.ZoneName] = nil
			}

			zoneNames := make([]string, 0)
			for k := range zoneNameSet {
				zoneNames = append(zoneNames, k)
			}

			// create SCS
			args := &scs.CreateInstanceArgs{
				ClientToken:  string(uuid.NewUUID()),
				InstanceName: object.GetName(), // 实例名称
				Engine:       2,                // redis: 2   PegaDB: 3
				Billing: scs.Billing{
					PaymentTiming: string(api.PaymentTimingPostPaid),
				},
				PurchaseCount:  1,                                                                             // 购买个数，最大不超过10，默认1
				EngineVersion:  handler.config.Redis.EngineVersion,                                            // 引擎版本，集群：4.0、5.0、6.0 主从：4.0、5.0、6.0、7.0  当Engine为3时，可不传
				ReplicationNum: 2,                                                                             // 副本个数，单副本为1，双副本为2，多副本依此类推，副本数不能超过10
				NodeType:       handler.config.Redis.Spec[strings.ToLower(object.Spec.InstanceType)].NodeType, // 节点规格
				ClusterType:    "cluster",                                                                     // 集群类型，集群版："cluster"，主从版："master_slave"
				Port:           6379,
				VpcID:          handler.vpcID,
				// 子网信息。ZoneName：可用区，SubnetID：子网ID。
				// 如果指定了VpcID，则该字段必传；如果不指定VpcID，该字段不传
				Subnets: []scs.Subnet{
					{
						ZoneName: handler.config.Redis.ZoneName,
						SubnetID: handler.config.Redis.SubnetID,
					},
				},
				ShardNum: 1, // 分片个数
				ProxyNum: 1, // 代理节点数，主从版：0，集群版：代理节点数=分片个数
				// EnableReadOnly: 2, // EnableReadOnly: 1,
				StoreType: 0, // 存储类型 0：高性能内存 1：ssd本地磁盘 3:容量型存储（PegaDB专用）
				// 磁盘类型, normal_io:本地盘ssd磁盘, cloud_high:高性能云磁盘, cloud_nor:通用型SSD, cloud_enha:增强型SSD, 必选
				//DiskType: handler.config.Redis.DiskIoType,
			}
			if password != nil {
				args.ClientAuth = string(password) // 客户端认证，0：关闭，1：开启
			}

			createResult, err := handler.clients.ScsClient().CreateInstance(args)

			if err != nil {
				logger.V(2).Error(err, "create SCS redis failed")
				handler.updateReason(ctx, object, "ScsNotReady", err.Error())
				return err
			}

			object.Status.InstanceID = createResult.InstanceIds[0]
		} else {
			object.Status.InstanceID = instance.InstanceID
		}
	}

	if object.Status.Host == "" {
		logger.V(4).Info("start get instance detail", "instanceID", object.Status.InstanceID)
		instance, err := handler.clients.ScsClient().GetInstanceDetail(object.Status.InstanceID)
		logger.V(4).Info("get instance detail", "instance", instance)
		if err != nil {
			logger.Error(err, fmt.Sprintf("get instance %v failed", object.Status.InstanceID))
			handler.updateReason(ctx, object, "ScsNotReady", err.Error())
			return err
		}

		if instance.InstanceStatus != "Running" {
			logger.Info(fmt.Sprintf("instance is not ready: %v", instance.InstanceStatus))
			handler.updateReason(ctx, object, "ScsNotReady", fmt.Sprintf("expected instance status=%s "+
				"but got=%s", "Running", instance.InstanceStatus))
			return fmt.Errorf("instance is not ready")
		}

		object.Status.Host = instance.Domain
		object.Status.Port = fmt.Sprintf("%v", instance.Port)
	}

	object.Status.Type = object.Spec.InstanceType
	object.Status.Phase = ccrv1alpha1.RedisStatusRunning
	return handler.updateReason(ctx, object, "", "")
}

// handleUpgrading 处理升级中的状态，包括实例状态、内存大小和磁盘大小等。如果需要升级，则调用Scs client进行升级操作，并返回错误；否则更新redis对象的类型和状态为运行中，并返回nil。
// ctx: 上下文信息，context.Context类型
// object: Redis对象指针，*ccrv1alpha1.Redis类型
// 返回值：error类型，表示升级过程中出现的错误，如果没有错误则返回nil
func (handler *Handler) handleUpgrading(ctx context.Context, object *ccrv1alpha1.Redis) error {
	logger := log.FromContext(ctx)

	if object.Status.Phase != ccrv1alpha1.RedisStatusUpgrading {
		currentTime := metav1.Now()
		object.Status.Phase = ccrv1alpha1.RedisStatusUpgrading
		object.Status.LastTransitionTime = &currentTime
	}

	if object.Status.InstanceID == "" {
		logger.V(2).Error(fmt.Errorf("no instance id found"), "no instance id found")
		handler.updateReason(ctx, object, "ScsNotReady", "instance is empty")
		return fmt.Errorf("no instance id found")
	}

	instance, err := handler.clients.ScsClient().GetInstanceDetail(object.Status.InstanceID)
	if err != nil {
		logger.Error(err, fmt.Sprintf("get instance %v failed", object.Status.InstanceID))
		handler.updateReason(ctx, object, "ScsNotReady", err.Error())
		return err
	}

	if strings.ToLower(instance.InstanceStatus) != "available" {
		logger.Error(fmt.Errorf("instance status is not available"), "instance status is "+instance.InstanceStatus)
		handler.updateReason(ctx, object, "ScsNotReady", "instance is not ready")
		return fmt.Errorf("instance is %s, not ready", instance.InstanceStatus)
	}

	expectedNodeType := handler.config.Redis.Spec[strings.ToLower(object.Spec.InstanceType)].NodeType

	// 降配 暂不支持
	if instance.NodeType != expectedNodeType {

		// TODO 判定条件后面需要修改
		if true {
			// 开始升级
			err = handler.clients.ScsClient().ResizeInstance(object.Status.InstanceID, &scs.ResizeInstanceArgs{
				NodeType: expectedNodeType,
			})

			if err != nil {
				logger.V(2).Error(err, "resize SCS failed")
				handler.updateReason(ctx, object, "ScsResizeFailed", err.Error())
				return err
			}

			return fmt.Errorf("resize success, wait for redis available")
		}
	}

	object.Status.Type = object.Spec.InstanceType
	object.Status.Phase = ccrv1alpha1.RedisStatusRunning
	return handler.updateReason(ctx, object, "", "")
}

// updateReason updateReason 更新状态的原因和消息，并将当前时间设置为最后探测时间，如果当前状态不是运行中且最后转换时间超过了指定时间，则将状态设置为失败。返回错误信息，如果没有错误则返回nil
func (handler *Handler) updateReason(ctx context.Context, object *ccrv1alpha1.Redis, reason string, message string) error {
	logger := log.FromContext(ctx)
	object.Status.Reason = reason
	object.Status.Message = message
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime

	if object.Status.Phase != ccrv1alpha1.RedisStatusRunning &&
		object.Status.LastTransitionTime != nil &&
		currentTime.Sub(object.Status.LastTransitionTime.Time) > handler.failOverDuration {
		object.Status.Phase = ccrv1alpha1.RedisStatusFailed
	}

	err := handler.k8sclient.Status().Update(ctx, object)
	if err != nil {
		logger.Error(err, "update status failed")
	}

	return err
}

// setFinalizer 设置Finalizer，如果已经存在则返回true，否则将其添加到Finalizers中并更新资源状态。
// 参数：
//
//	ctx (context.Context) - 上下文对象，可用于传递请求或依赖项相关的值，例如超时和取消标记。
//	object (&ccrv1alpha1.Redis) - Redis类型的指针，表示需要操作的资源对象。
//
// 返回值：
//
//	bool (bool) - 如果Finalizer已经存在则为true，否则为false。
//	error (error) - 操作过程中发生的错误，如果没有错误则为nil。
func (handler *Handler) setFinalizer(ctx context.Context, object *ccrv1alpha1.Redis) (bool, error) {
	finalizers := object.GetFinalizers()
	if finalizers == nil {
		finalizers = make([]string, 0)
	}

	for _, v := range finalizers {
		if v == ccrv1alpha1.ResourceFinalizer {
			return true, nil
		}
	}

	finalizers = append(finalizers, ccrv1alpha1.ResourceFinalizer)
	object.SetFinalizers(finalizers)

	return false, handler.k8sclient.Update(ctx, object)
}

// removeFinalizer removeFinalizer 从对象中移除最后一个 Finalizer，如果 Finalizer 为 ccrv1alpha1.ResourceFinalizer，则不会被移除。
// 参数：
//
//	ctx (context.Context) - 上下文信息，可用于传递超时或取消信号。
//	object (&ccrv1alpha1.Redis) - Redis 类型的对象指针，包含需要修改的 Finalizer。
//
// 返回值：
//
//	error (error) - 如果移除 Finalizer 失败，则返回错误；否则返回 nil。
func (handler *Handler) removeFinalizer(ctx context.Context, object *ccrv1alpha1.Redis) error {
	finalizers := object.GetFinalizers()
	if len(finalizers) == 0 {
		return nil
	}

	reservedFinalizer := make([]string, 0)
	for _, v := range finalizers {
		if v != ccrv1alpha1.ResourceFinalizer {
			reservedFinalizer = append(reservedFinalizer, v)
		}
	}

	object.SetFinalizers(reservedFinalizer)
	return handler.k8sclient.Update(ctx, object)
}
