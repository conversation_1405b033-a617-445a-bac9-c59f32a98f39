package controllers

import (
	"context"
	"encoding/json"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/kafka"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"k8s.io/apimachinery/pkg/util/wait"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

const (
	DefaultOnlineTopic = "c42867cb52124ef4bfe51d315eea6f68__res_manager_online"
)

type ResgroupSyncer struct {
	region string
	client client.Client

	options  *KafkaOptions
	producer kafka.ProducerInterface

	interval time.Duration
}

func NewResgroupSyncer(region string, client client.Client, producer kafka.ProducerInterface, options *KafkaOptions, interval time.Duration) *ResgroupSyncer {
	return &ResgroupSyncer{
		region:   region,
		client:   client,
		options:  options,
		producer: producer,
		interval: interval,
	}
}

func (b *ResgroupSyncer) Start(ctx context.Context) error {
	wait.Until(func() {
		b.run(ctx)
	}, b.interval, ctx.Done())

	return nil
}

// NeedLeaderElection only master will run in
func (b *ResgroupSyncer) NeedLeaderElection() bool {
	return true
}

func (b *ResgroupSyncer) run(ctx context.Context) error {
	logger := log.FromContext(ctx)
	// requestID := string(uuid.NewUUID())
	logger.Info("start resource sync to resource group")
	if b.region == common.RegionGZTEST {
		b.region = common.RegionGZ
	}

	var ccrList v1alpha1.CCRList
	err := b.client.List(ctx, &ccrList)
	if err != nil {
		logger.Error(err, "list ccr instance failed")
		return err
	}

	if len(ccrList.Items) == 0 {
		logger.V(2).Info("list ccr size is zero, Skip!")
		return nil
	}

	// get all ccr instance in k8s
	instanceInk8s := make(map[string]v1alpha1.CCR)
	for _, item := range ccrList.Items {
		instanceInk8s[item.GetName()] = item
	}

	for _, ccrInfo := range instanceInk8s {
		if ccrInfo.Status.IsResGroupSynchronized == "CreatedAndSync" {
			continue
		}
		message := model.SyncResGroupMessages{
			SyncType: "update",
			Resources: []model.SyncResourceDetail{
				{
					AccountID: ccrInfo.Spec.AccountID,
					UserID:    ccrInfo.Spec.UserID,
					Name:      ccrInfo.GetLabels()["name"],
					Type:      "CCR",
					Region:    b.region,
					ID:        ccrInfo.GetName(),
					AuthID:    "",
					UUID:      ccrInfo.GetName(),
					URL: "https://console.bce.baidu.com/ccr/#/ccr/company/instance/detail?instanceId=" +
						ccrInfo.GetName() + "&instanceName=" + ccrInfo.GetLabels()["name"],
				},
			},
		}
		msg, err := json.Marshal(message)
		if err != nil {
			logger.Error(err, "json Marshal err")
			return err
		}
		o := kafka.NewKafkaOptions(b.options.CaPemPath, b.options.ClientKeyPath, b.options.ClientPemPath,
			b.options.ConsumerGroupID, b.options.BrokerEndpoint)
		err = b.producer.StartProducer(o)
		if err != nil {
			logger.Error(err, "start producer failed!")
			return err
		}
		_, _, err = b.producer.SendMessage(DefaultOnlineTopic, string(msg))
		if err != nil {
			logger.Error(err, "send message to Kafka failed")
			return err
		}
		//fmt.Println("send message to Kafka succeed, partition(%d)/offset(%d)", partition, offset)
		b.producer.Close()

		ccrInfo.Status.IsResGroupSynchronized = "CreatedAndSync"
		if err := b.client.Update(ctx, &ccrInfo); err != nil {
			logger.V(2).Error(err, "patch to resource group sync failed, Skip!")
			continue
		}
	}
	logger.Info("stop resource group sync")
	return nil

}
