package gaiadb

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

const (
	defaultPaidType       = "postpay"
	defaultStorageInGB    = 5120
	defaultInstanceAmount = 2
	defaultProxyAmount    = 2
	defaultInterfaceType  = "defaultInterface"
	primaryInterfaceType  = "primaryInterface"
	defaultAuthIP         = "%"
	defaultTemplateID     = "51f4f777-822a-6449-ce41-39efbc2afb89"

	StatusAvailable = "available"

	Remark = "resource-controller"
)

type Handler struct {
	clients clientset.ClientSetInterface

	config    *conf.Config
	k8sClient client.Client

	paidType       string
	interfaceType  string
	storageInGB    int
	instanceAmount int
	proxyAmount    int
	templateID     string

	failOverDuration time.Duration
}

// NewHandler 新建一个 Handler 对象，用于处理请求。
//
// 参数:
//   - config *conf.Config: 配置信息的指针，包含了 VPC ID、子网 ID、可用区名称等信息。
//   - clients *clientset.ClientSet: 客户端集合的指针，包含了与 Kubernetes API Server 和 AWS API Gateway 的连接。
//   - vpcID string: VPC ID，用于标识要创建的 Pod 所属的 VPC。
//   - cli client.Client: Kubernetes Client 的实例，用于与 Kubernetes API Server 进行交互。
//
// 返回值:
//   - *Handler: 一个 Handler 类型的指针，表示新建的 Handler 对象。
func NewHandler(config *conf.Config, clients clientset.ClientSetInterface, _ string, cli client.Client) *Handler {
	paidType := defaultPaidType
	if config != nil && config.GaiaDB.PaidType != "" {
		paidType = config.GaiaDB.PaidType
	}
	interfaceType := defaultInterfaceType
	if config != nil && config.GaiaDB.InterfaceType != "" {
		interfaceType = config.GaiaDB.InterfaceType
	}
	storageInGB := defaultStorageInGB
	if config != nil && config.GaiaDB.StorageInGB > 0 {
		storageInGB = config.GaiaDB.StorageInGB
	}
	instanceAmount := defaultInstanceAmount
	if config != nil && config.GaiaDB.InstanceAmount > 0 {
		instanceAmount = config.GaiaDB.InstanceAmount
	}
	proxyAmount := defaultProxyAmount
	if config != nil && config.GaiaDB.ProxyAmount > 0 {
		proxyAmount = config.GaiaDB.ProxyAmount
	}
	templateId := defaultTemplateID
	if config != nil && config.GaiaDB.TemplateID != "" {
		templateId = config.GaiaDB.TemplateID
	}
	return &Handler{
		config:    config,
		clients:   clients,
		k8sClient: cli,

		paidType:         paidType,
		interfaceType:    interfaceType,
		storageInGB:      storageInGB,
		instanceAmount:   instanceAmount,
		proxyAmount:      proxyAmount,
		templateID:       templateId,
		failOverDuration: 1 * time.Hour,
	}
}

// Handle Handle 处理函数，用于处理 GaiaDB 对象的各种情况。
// 如果对象已被删除，则调用 handleDelete 方法进行处理；
// 如果对象的状态为空字符串，则调用 handlePendingPhase 方法进行处理；
// 如果对象的状态为创建中，则调用 handleCreate 方法进行处理；
// 如果对象的类型与其规格中的不同，则调用 handleUpgrading 方法进行处理。
// 否则，返回 nil。
// 参数 ctx context.Context：上下文信息；
// 参数 object *ccrv1alpha1.GaiaDB：需要处理的 GaiaDB 对象指针；
// 返回值 error：处理过程中出现的错误，如果没有错误则返回 nil。
func (handler *Handler) Handle(ctx context.Context, object *ccrv1alpha1.GaiaDB) error {
	obj := object.DeepCopy()
	switch {
	case obj.DeletionTimestamp != nil:
		return handler.handleDelete(ctx, obj)
	case obj.Status.Phase == "":
		return handler.handlePendingPhase(ctx, obj)
	case obj.Status.Phase == ccrv1alpha1.GaiaDBStatusCreating:
		return handler.handleCreate(ctx, obj)
	case obj.Status.Phase == ccrv1alpha1.GaiaDBStatusUpdating:
		return handler.handleUpdating(ctx, obj)
	case object.Spec.Database != object.Status.Database && obj.Status.Phase == ccrv1alpha1.GaiaDBStatusRunning:
		// 仅 CRD 修改状态
		return handler.handleUpdate(ctx, obj)
	}

	return nil
}

// handlePendingPhase 处理待处理阶段，首先设置最终化器，如果设置成功则等待下一轮更新，否则返回错误；
// 如果最终化器已经被设置过，则等待阶段的更新。
//
// 参数：
//
//	ctx context.Context                  上下文信息，包含日志记录器和请求相关值
//	object *ccrv1alpha1.GaiaDB            需要更新状态的MySQL对象指针
//
// 返回值：
//
//	error                                nil表示操作成功，非nil表示操作失败，包含错误信息
func (handler *Handler) handlePendingPhase(ctx context.Context, object *ccrv1alpha1.GaiaDB) error {
	// set finalizer first
	logger := log.FromContext(ctx)
	finalizerSetted, err := handler.setFinalizer(ctx, object)
	if err != nil {
		logger.V(2).Error(err, "update finalizer failed")
		return err
	}

	// finalizer setted, wait for next round
	if !finalizerSetted {
		logger.V(2).Info("finalizer has been setted, wait for phase to be update")
		return nil
	}

	object.Status.Phase = ccrv1alpha1.GaiaDBStatusCreating
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime
	object.Status.LastTransitionTime = &currentTime

	return handler.k8sClient.Status().Update(ctx, object)
}

// handleDelete 处理删除操作，包括获取数据库、创建数据库和等待数据库可以访问。如果不存在实例或者实例已经被删除，则直接移除最终化器。
// ctx: 上下文对象，包含日志记录器
// object: *ccrv1alpha1.GaiaDB，需要处理的 GaiaDB 对象
// 返回值：error，如果发生错误则返回该错误
func (handler *Handler) handleDelete(ctx context.Context, object *ccrv1alpha1.GaiaDB) error {
	logger := log.FromContext(ctx)
	if object.Status.ClusterID == "" {
		return handler.removeFinalizer(ctx, object)
	}

	_, err := handler.clients.GaiaDBClient().GetClusterDetail(object.Status.ClusterID)
	var bceError *bce.BceServiceError
	if errors.As(err, &bceError) && (bceError.StatusCode == http.StatusNotFound || bceError.Code == "ClusterStatusInvalid") {
		return handler.removeFinalizer(ctx, object)
	}

	if err != nil {
		logger.V(2).Error(err, fmt.Sprintf("get gaiadb cluster %s failed", object.Status.ClusterID))
		_ = handler.updateReason(ctx, object, "DeleteFailed", err.Error())
		return err
	}

	err = handler.clients.GaiaDBClient().DeleteCluster(object.Status.ClusterID)
	if err != nil {
		bceErr := &bce.BceServiceError{}
		if errors.As(err, &bceErr) && bceErr.StatusCode == 404 {
			return nil
		}

		logger.V(2).Error(err, fmt.Sprintf("get the detail of cluster %v failed", object.Status.ClusterID))
		_ = handler.updateReason(ctx, object, "InstanceDeleteFailed", err.Error())
		return err
	}

	return handler.removeFinalizer(ctx, object)
}

// updateReason updateReason 更新状态的原因和消息，并将当前时间设置为最后探测时间，如果当前状态不是运行中且最后转换时间超过了指定时间，则将状态设置为失败。返回错误信息，如果没有错误则返回nil
func (handler *Handler) updateReason(ctx context.Context, object *ccrv1alpha1.GaiaDB, reason string, message string) error {
	logger := log.FromContext(ctx)
	object.Status.Reason = reason
	object.Status.Message = message
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime

	if object.Status.Phase != ccrv1alpha1.GaiaDBStatusRunning &&
		object.Status.LastTransitionTime != nil &&
		currentTime.Sub(object.Status.LastTransitionTime.Time) > handler.failOverDuration {
		object.Status.Phase = ccrv1alpha1.GaiaDBStatusFailed
	}

	err := handler.k8sClient.Status().Update(ctx, object)
	if err != nil {
		logger.Error(err, "update status failed")
	}

	return err
}

// setFinalizer 设置Finalizer，如果已经存在则返回true，否则将其添加到Finalizers中并更新资源状态。
// 参数：
//
//	ctx (context.Context) - 上下文对象，可用于传递请求或依赖项相关的值，例如超时和取消标记。
//	object (&ccrv1alpha1.GaiaDB) - GaiaDB类型的指针，表示需要操作的资源对象。
//
// 返回值：
//
//	bool (bool) - 如果Finalizer已经存在则为true，否则为false。
//	error (error) - 操作过程中发生的错误，如果没有错误则为nil。
func (handler *Handler) setFinalizer(ctx context.Context, object *ccrv1alpha1.GaiaDB) (bool, error) {
	finalizers := object.GetFinalizers()
	if finalizers == nil {
		finalizers = make([]string, 0)
	}

	for _, v := range finalizers {
		if v == ccrv1alpha1.ResourceFinalizer {
			return true, nil
		}
	}

	finalizers = append(finalizers, ccrv1alpha1.ResourceFinalizer)
	object.SetFinalizers(finalizers)

	return false, handler.k8sClient.Update(ctx, object)
}

// removeFinalizer removeFinalizer 从对象中移除最后一个 Finalizer，如果 Finalizer 为 ccrv1alpha1.ResourceFinalizer，则不会被移除。
// 参数：
//
//	ctx (context.Context) - 上下文信息，可用于传递超时或取消信号。
//	object (&ccrv1alpha1.GaiaDB) - GaiaDB 类型的对象指针，包含需要修改的 Finalizer。
//
// 返回值：
//
//	error (error) - 如果移除 Finalizer 失败，则返回错误；否则返回 nil。
func (handler *Handler) removeFinalizer(ctx context.Context, object *ccrv1alpha1.GaiaDB) error {
	finalizers := object.GetFinalizers()
	if len(finalizers) == 0 {
		return nil
	}

	reservedFinalizer := make([]string, 0)
	for _, v := range finalizers {
		if v != ccrv1alpha1.ResourceFinalizer {
			reservedFinalizer = append(reservedFinalizer, v)
		}
	}

	object.SetFinalizers(reservedFinalizer)
	return handler.k8sClient.Update(ctx, object)
}
