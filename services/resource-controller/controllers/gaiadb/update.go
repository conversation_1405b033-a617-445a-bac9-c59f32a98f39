package gaiadb

import (
	"context"
	"fmt"
	"github.com/baidubce/bce-sdk-go/services/gaiadb"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"strings"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
)

const (
	DefaultCharacterSet = "utf8mb4"
)

var (
	Privileges = []string{"SELECT", "INSERT", "UPDATE", "DELETE", "CREATE", "DROP", "REFERENCES", "INDEX", "ALTER", "CREATE TEMPORARY TABLES", "LOCK TABLES",
		"CREATE VIEW", "SHOW VIEW", "CREATE ROUTINE", "ALTER ROUTINE", "EXECUTE", "TRIGGER", "EVENT"}
)

// handleUpdate 更新状态为 updating
func (handler *Handler) handleUpdate(ctx context.Context, object *ccrv1alpha1.GaiaDB) error {
	object.Status.Phase = ccrv1alpha1.GaiaDBStatusUpdating
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime
	object.Status.LastTransitionTime = &currentTime

	return handler.k8sClient.Status().Update(ctx, object)
}

// handleUpdating 主要更新 database 信息
func (handler *Handler) handleUpdating(ctx context.Context, object *ccrv1alpha1.GaiaDB) error {
	logger := log.FromContext(ctx)
	if object == nil {
		return fmt.Errorf("object is nil")
	}
	logger.Info("handleUpdating", "gaiadb", object.GetName())
	if object.Spec.Database == object.Status.Database {
		object.Status.Phase = ccrv1alpha1.GaiaDBStatusRunning
		_ = handler.updateReason(ctx, object, "", "")
		return nil
	}

	logger.Info("update database", "gaiadb", object.GetName())
	// 1. 创建 database
	addDB := object.Spec.Database
	err := handler.ensureDatabase(ctx, object, addDB)
	if err != nil {
		logger.Error(err, "failed to ensure database", "database", addDB)
		_ = handler.updateReason(ctx, object, "DatabaseNotReady", err.Error())
		return err
	}
	// 先记录账号的 db，不记录 object.Status.Database，等旧的删除后再记录
	object.Status.AccountDetail.Database = addDB

	// 2. 删除 database
	removeDB := object.Status.Database
	err = handler.ensureDatabaseDeleted(ctx, object, removeDB)
	if err != nil {
		logger.Error(err, "failed to delete database", "database", removeDB)
		_ = handler.updateReason(ctx, object, "DatabaseNotReady", err.Error())
		return err
	}

	object.Status.Database = addDB
	object.Status.Phase = ccrv1alpha1.GaiaDBStatusRunning
	_ = handler.updateReason(ctx, object, "", "")
	return nil
}

func (handler *Handler) ensureDatabase(ctx context.Context, object *ccrv1alpha1.GaiaDB, addDBName string) error {
	if addDBName == "" {
		return nil
	}
	if object.Status.AccountDetail.Database == addDBName {
		// 账号权限已存在，说明这个 database 已经创建了
		return nil
	}

	// 1. 确保 database 存在
	err := handler.ensureDatabaseAvailable(ctx, object, addDBName)
	if err != nil {
		return err
	}

	// 2. 确保权限
	return handler.ensureDatabasePrivileges(ctx, object, addDBName)
}

// ensureDatabaseAvailable 确保 database 存在且可用
func (handler *Handler) ensureDatabaseAvailable(ctx context.Context, object *ccrv1alpha1.GaiaDB, addDBName string) error {
	// 1. 查询是否存在，存在则返回
	databaseInfo, err := handler.getDatabaseByName(ctx, object, addDBName)
	if err != nil {
		return err
	}
	if databaseInfo != nil {
		if databaseInfo.DbStatus != StatusAvailable {
			return fmt.Errorf("database not ready, current status: %s", databaseInfo.DbStatus)
		}
		return nil
	}

	// 2. 不存在，则创建
	err = handler.clients.GaiaDBClient().CreateDatabase(object.Status.ClusterID, &gaiadb.CreateDatabaseArgs{
		DbName:           object.Spec.Database,
		CharacterSetName: DefaultCharacterSet,
		Remark:           Remark,
	})
	if err != nil {
		return err
	}
	// 刚创建的 database 不可用，需要等会儿
	return fmt.Errorf("database not ready, current status: creating")
}

// getDatabaseByName 不存在返回 nil, nil
func (handler *Handler) getDatabaseByName(_ context.Context, object *ccrv1alpha1.GaiaDB, dbName string) (*gaiadb.DatabaseInfo, error) {
	result, err := handler.clients.GaiaDBClient().ListDatabase(object.Status.ClusterID)
	if err != nil {
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "NotExist") {
			return nil, nil
		}
		return nil, err
	}

	if result == nil || len(result.Databases) == 0 {
		return nil, nil
	}

	for i := range result.Databases {
		info := result.Databases[i]
		if info.DbName == dbName {
			return &info, nil
		}
	}
	return nil, nil
}

func (handler *Handler) ensureDatabasePrivileges(ctx context.Context, object *ccrv1alpha1.GaiaDB, dbName string) error {
	logger := log.FromContext(ctx)

	if object.Status.AccountDetail.Database == dbName {
		// 账号权限已存在
		return nil
	}

	// 1. 查看账号权限
	accountDetail, err := handler.clients.GaiaDBClient().GetAccountDetail(object.Status.ClusterID, object.Status.AccountDetail.Username)
	if err != nil {
		return err
	}
	if accountDetail == nil || accountDetail.Account.AccountName == "" {
		return fmt.Errorf("cluster %s account %s not found", object.Status.ClusterID, object.Status.AccountDetail.Username)
	}

	for _, pri := range accountDetail.Account.DatabasePrivileges {
		if pri.DbName == dbName {
			object.Status.AccountDetail.Database = dbName
			logger.Info("account already has privileges, skip")
			return nil
		}
	}

	// 2. 更新账号权限
	args := &gaiadb.PrivilegesArgs{
		DatabasePrivileges: []gaiadb.DatabasePrivilege{
			{
				DbName:     dbName,
				AuthType:   "definePrivilege",
				Privileges: Privileges,
			},
		},
		Etag: accountDetail.Account.Etag,
	}
	err = handler.clients.GaiaDBClient().UpdateAccountPrivileges(object.Status.ClusterID, object.Status.AccountDetail.Username, args)
	if err != nil {
		return err
	}
	return nil
}

func (handler *Handler) ensureDatabaseDeleted(_ context.Context, object *ccrv1alpha1.GaiaDB, dbName string) error {
	if dbName == "" {
		return nil
	}
	// 删除 database
	return handler.clients.GaiaDBClient().DeleteDatabase(object.Status.ClusterID, dbName)
}
