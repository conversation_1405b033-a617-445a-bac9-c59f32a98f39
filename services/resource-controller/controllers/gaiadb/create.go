package gaiadb

import (
	"context"
	"encoding/base64"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"github.com/baidubce/bce-sdk-go/services/gaiadb"
	"github.com/wxnacy/wgo/arrays"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/log"

	gaiadbSDK "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/gaiadb"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
)

// handleCreate 处理创建 GaiaDB 实例的逻辑
// ctx context.Context：上下文信息，包含日志记录器和请求相关参数
// object *ccrv1alpha1.GaiaDB：需要创建的 GaiaDB 实例对象
// 返回值 error：如果发生错误则返回该错误
func (handler *<PERSON>ler) handleCreate(ctx context.Context, object *ccrv1alpha1.GaiaDB) error {
	logger := log.FromContext(ctx)

	if object == nil {
		return fmt.Errorf("object is nil")
	}

	logger.V(2).Info("start create loop", "gaiadb", object.GetName())
	password, err := base64.StdEncoding.DecodeString(object.Spec.Password)
	if err != nil {
		logger.Error(err, "password is invalid")
		object.Status.Phase = ccrv1alpha1.GaiaDBStatusFailed
		_ = handler.updateReason(ctx, object, "InvalidPassword", err.Error())
		return err
	}
	// 1. 确保集群可用
	err = handler.ensureCluster(ctx, object)
	if err != nil {
		logger.Error(err, "ensure cluster failed")
		return err
	}

	// 2. 确保账号可用
	err = handler.ensureAccount(ctx, object, password)
	if err != nil {
		logger.Error(err, "ensure account failed")
		_ = handler.updateReason(ctx, object, "AccountNotReady", err.Error())
		return err
	}

	// 3. 创建 database
	err = handler.ensureDatabase(ctx, object, object.Spec.Database)
	if err != nil {
		logger.Error(err, "ensure database failed")
		_ = handler.updateReason(ctx, object, "DatabaseNotReady", err.Error())
		return err
	}
	object.Status.AccountDetail.Database = object.Spec.Database
	object.Status.Database = object.Spec.Database

	object.Status.Phase = ccrv1alpha1.GaiaDBStatusRunning
	return handler.updateReason(ctx, object, "", "")
}

func (handler *Handler) ensureCluster(ctx context.Context, object *ccrv1alpha1.GaiaDB) error {
	logger := log.FromContext(ctx)

	// 1. 实例信息齐全，则跳过
	if object.Status.ClusterID != "" && object.Status.ClusterDetail.Host != "" {
		return nil
	}

	// 2. 实例信息不齐全，确保实例存在且状态可用
	clusterDetail, err := handler.ensureClusterExist(ctx, object)
	if err != nil {
		logger.Error(err, "failed to ensure gaiadb cluster")
		_ = handler.updateReason(ctx, object, "GaiaDBNotReady", err.Error())
		return err
	}

	// 3. 记录实例规格
	object.Status.ClusterDetail.AllocatedMemoryInMB = clusterDetail.AllocatedMemoryInMB
	object.Status.ClusterDetail.AllocatedCpuInCore = clusterDetail.AllocatedCpuInCore

	// 4. 查询访问入口
	interfaceListResult, err := handler.clients.GaiaDBClient().GetInterfaceList(object.Status.ClusterID)
	if err != nil {
		logger.Error(err, "failed to list interface")
		_ = handler.updateReason(ctx, object, "GetInterfaceFailed", err.Error())
		return err
	}
	if interfaceListResult == nil || len(interfaceListResult.Interfaces) == 0 {
		logger.Error(err, fmt.Sprintf("cluster %s doesn't has interface", object.Status.ClusterID))
		_ = handler.updateReason(ctx, object, "InterfaceNotReady", err.Error())
		return err
	}

	// 5. 记录访问入口
	// 获取主节点地址，和默认代理地址
	var primaryHost, proxyHost string
	for _, intf := range interfaceListResult.Interfaces {
		if intf.Status != "serving" {
			continue
		}
		if intf.InterfaceType == primaryInterfaceType {
			primaryHost = intf.Access.DnsName
			continue
		}
		if intf.InterfaceType == defaultInterfaceType {
			proxyHost = intf.Access.DnsName
		}
	}

	if primaryHost == "" {
		err = fmt.Errorf("primary interface not ready")
		logger.Error(err, fmt.Sprintf("cluster %s doesn't has serving interface %s", object.Status.ClusterID, primaryInterfaceType))
		_ = handler.updateReason(ctx, object, "PrimaryInterfaceNotReady", err.Error())
		return err
	}
	if proxyHost == "" {
		err = fmt.Errorf("default interface not ready")
		logger.Error(err, fmt.Sprintf("cluster %s doesn't has serving interface %s", object.Status.ClusterID, defaultInterfaceType))
		_ = handler.updateReason(ctx, object, "DefaultInterfaceNotReady", err.Error())
		return err
	}
	object.Status.ClusterDetail.Host = fmt.Sprintf("%s,%s", primaryHost, proxyHost)
	object.Status.ClusterDetail.Port = clusterDetail.Endpoint.Port
	return nil
}

func (handler *Handler) ensureClusterExist(_ context.Context, object *ccrv1alpha1.GaiaDB) (*gaiadb.ClusterDetailResult, error) {
	if object.Status.ClusterID != "" {
		// 1. 实例存在，直接返回
		clusterDetail, err := handler.clients.GaiaDBClient().GetClusterDetail(object.Status.ClusterID)
		if err != nil {
			return nil, fmt.Errorf("get cluster %s err: %s", object.Status.ClusterID, err)
		}
		if clusterDetail.InstanceStatus != StatusAvailable {
			return nil, fmt.Errorf("cluster %s not available, please wait a moment. Current status: %s", object.Status.ClusterID, clusterDetail.InstanceStatus)
		}
		return clusterDetail, nil
	}

	// 2. 创建实例
	// 2.1 先确定哪个子网可用，因为 gaiadb 仅有部分可用区可用
	subnetID, err := handler.selectAvailableSubnet(object)
	if err != nil {
		return nil, fmt.Errorf("select available subnet failed: %s", err)
	}

	// 2.2 创建
	args := handler.genCreateClusterArgs(object, subnetID)

	result, err := handler.clients.GaiaDBClient().CreateCluster(args)
	if err != nil {
		return nil, fmt.Errorf("create cluster failed: %s", err)
	}
	if result == nil || len(result.ClusterIds) == 0 {
		return nil, fmt.Errorf("create cluster failed: no cluster id")
	}
	object.Status.ClusterID = result.ClusterIds[0]
	// 集群刚创建，肯定还没好，直接下一轮
	return nil, fmt.Errorf("cluster %s has just been created, please wait a moment", object.Status.ClusterID)
}

func (handler *Handler) selectAvailableSubnet(object *ccrv1alpha1.GaiaDB) (string, error) {
	// todo 等 sdk 发布后改成用 sdk
	subnetList, err := gaiadbSDK.GetAvailableSubnetList(handler.clients.GaiaDBClient(), object.Spec.InstanceParam.VpcID)
	if err != nil {
		return "", err
	}
	if len(subnetList) == 0 {
		return "", fmt.Errorf("no available subnet")
	}
	// 随机挑选一个子网
	rand.Seed(time.Now().UnixNano())
	randomInt := rand.Intn(len(subnetList))
	return subnetList[randomInt].SubnetID, nil
}

func (handler *Handler) genCreateClusterArgs(object *ccrv1alpha1.GaiaDB, subnetID string) *gaiadb.CreateClusterArgs {
	return &gaiadb.CreateClusterArgs{
		ClientToken: string(uuid.NewUUID()),
		ProductType: handler.paidType,
		Number:      1,
		InstanceParam: gaiadb.InstanceParam{
			ReleaseVersion:       string(object.Spec.InstanceParam.EngineVersion),
			AllocatedCpuInCore:   object.Spec.InstanceParam.AllocatedCpuInCore,
			AllocatedMemoryInMB:  object.Spec.InstanceParam.AllocatedMemoryInMB,
			AllocatedStorageInGB: handler.storageInGB,
			InstanceAmount:       handler.instanceAmount,
			ProxyAmount:          handler.proxyAmount,
			VpcId:                object.Spec.InstanceParam.VpcID,
			SubnetId:             subnetID,
			ComputeTplId:         handler.templateID,
		},
	}
}

func (handler *Handler) ensureAccount(ctx context.Context, object *ccrv1alpha1.GaiaDB, password []byte) error {
	// 1. 账号已记录，跳过
	if object.Status.AccountDetail.Username == object.Spec.Username {
		return nil
	}

	// 2. 检查集群里是否有账号
	detail, err := handler.ensureAccountExist(ctx, object, password)
	if err != nil {
		return err
	}

	if detail.Account.AccountStatus != StatusAvailable {
		err = fmt.Errorf("account not available, current status: %s", detail.Account.AccountStatus)
		return err
	}

	// 3. 更新账号白名单
	err = handler.ensureWhiteList(ctx, object)
	if err != nil {
		return err
	}

	object.Status.AccountDetail.Username = detail.Account.AccountName
	return nil
}

func (handler *Handler) ensureAccountExist(_ context.Context, object *ccrv1alpha1.GaiaDB, password []byte) (*gaiadb.AccountDetail, error) {
	// 1. 尝试获取账号
	detail, err := handler.clients.GaiaDBClient().GetAccountDetail(object.Status.ClusterID, object.Spec.Username)
	if err != nil && !strings.Contains(err.Error(), "NotExist") && !strings.Contains(err.Error(), "NotFound") {
		return nil, err
	}
	if detail != nil && detail.Account.AccountName == object.Spec.Username {
		// 上述接口默认返回不空，只有 AccountName 非空时，才能说明账号存在
		return detail, nil
	}

	// 2. 账号不存在，创建账号
	args := &gaiadb.CreateAccountArgs{
		AccountName: object.Spec.Username,
		Password:    string(password),
		AccountType: "common",
		Remark:      Remark,
	}
	err = handler.clients.GaiaDBClient().CreateAccount(object.Status.ClusterID, args)
	if err != nil {
		return nil, err
	}

	// 3. 返回创建好的账号
	return handler.clients.GaiaDBClient().GetAccountDetail(object.Status.ClusterID, object.Spec.Username)
}

func (handler *Handler) ensureWhiteList(_ context.Context, object *ccrv1alpha1.GaiaDB) error {
	whiteList, err := handler.clients.GaiaDBClient().GetWhiteList(object.Status.ClusterID)
	if err != nil {
		return err
	}
	index := arrays.ContainsString(whiteList.AuthIps, defaultAuthIP)
	if whiteList != nil && index >= 0 {
		// ip 已在白名单里
		return nil
	}
	return handler.clients.GaiaDBClient().UpdateWhiteList(object.Status.ClusterID, &gaiadb.WhiteList{
		AuthIps: []string{defaultAuthIP},
		Etag:    whiteList.Etag,
	})
}
