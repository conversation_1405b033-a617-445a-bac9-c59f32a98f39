package controllers

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
	"k8s.io/apimachinery/pkg/util/wait"
	"sigs.k8s.io/controller-runtime/pkg/log"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models/devops"
)

// DevopsSyncer
// Used to sync data to center
type DevopsSyncer struct {
	region    string
	srcSQLCli models.Interface
	devopsCli devops.Interface

	interval time.Duration
}

func NewDevopsSyncer(region string, srcSQLCli models.Interface, devopsCli devops.Interface, interval time.Duration) *DevopsSyncer {
	return &DevopsSyncer{
		region:    region,
		srcSQLCli: srcSQLCli,
		devopsCli: devopsCli,
		interval:  interval,
	}
}

func (d *DevopsSyncer) Start(ctx context.Context) error {
	wait.Until(func() {
		d.run(ctx)
	}, d.interval, ctx.Done())

	return nil
}

// only master will run in
func (d *DevopsSyncer) NeedLeaderElection() bool {
	return true
}

func (d *DevopsSyncer) run(ctx context.Context) error {
	logger := log.FromContext(ctx)

	logger.Info("start devops sync")
	// get last updated instance
	lastInstance, err := d.devopsCli.FindInstanceByLatestUpdateTime(d.region)
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			logger.Error(err, "get last update updated instance failed")
			return err
		}
	}

	lastUpdate := time.Unix(0, 0)
	if lastInstance != nil {
		lastUpdate = lastInstance.UpdatedAt
	}

	instanceSet, err := d.srcSQLCli.FindInstanceFromLastUpdated(lastUpdate)
	if err != nil {
		logger.Error(err, fmt.Sprintf("find all instances from last updated time %v failed", lastInstance.UpdatedAt))
		return err
	}

	for _, v := range instanceSet {
		err = d.devopsCli.UpsertInstance(&devops.Instance{
			Region:       d.region,
			OriginID:     v.ID,
			UUID:         v.UUID,
			StartAt:      v.StartAt,
			AccountID:    v.AccountID,
			UserID:       v.UserID,
			InstanceID:   v.InstanceID,
			InstanceName: v.InstanceName,
			DeletedAt:    v.DeletedAt,
			Status:       v.Status,
			Type:         v.Type,
			Description:  v.Description,
			CreatedAt:    v.CreatedAt,
			UpdatedAt:    v.UpdatedAt,
		})

		if err != nil {
			logger.Error(err, fmt.Sprintf("upsert instance failed, stop and wait for retry"))
			return err
		}
	}

	return nil
}
