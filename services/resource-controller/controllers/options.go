package controllers

type KafkaOptions struct {
	BrokerEndpoint string

	CaPemPath     string
	ClientKeyPath string
	ClientPemPath string

	ConsumerGroupID string
}

func NewKafkaOptions(caPemPath, clientKeyPath, clientPemPath, consumerGroupID, brokersEndpoint string) *KafkaOptions {
	return &KafkaOptions{
		BrokerEndpoint:  brokersEndpoint,
		ClientPemPath:   clientPemPath,
		ClientKeyPath:   clientKeyPath,
		CaPemPath:       caPemPath,
		ConsumerGroupID: consumerGroupID,
	}
}
