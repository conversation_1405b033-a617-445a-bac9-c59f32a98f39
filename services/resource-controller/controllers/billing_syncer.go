package controllers

import (
	"context"
	"strings"
	"time"

	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/uuid"
	"k8s.io/apimachinery/pkg/util/wait"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/common"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
)

type BillingSyncer struct {
	region string
	client client.Client

	resourceClient billing.ResourceClientInterface

	interval time.Duration
}

func NewBillingSyncer(region string, client client.Client, resourceClient billing.ResourceClientInterface, interval time.Duration) *BillingSyncer {
	return &BillingSyncer{
		region:         region,
		client:         client,
		resourceClient: resourceClient,
		interval:       interval,
	}
}

func (b *BillingSyncer) Start(ctx context.Context) error {
	wait.Until(func() {
		b.run(ctx)
	}, b.interval, ctx.Done())

	return nil
}

// NeedLeaderElection only master will run in
func (b *BillingSyncer) NeedLeaderElection() bool {
	return true
}

func (b *BillingSyncer) run(ctx context.Context) error {
	logger := log.FromContext(ctx)
	requestID := string(uuid.NewUUID())
	logger.Info("start billing sync")
	if b.region == common.RegionGZTEST {
		b.region = common.RegionGZ
	}

	var ccrList v1alpha1.CCRList
	err := b.client.List(ctx, &ccrList)
	if err != nil {
		logger.Error(err, "list ccr instance failed")
		return err
	}

	if len(ccrList.Items) == 0 {
		logger.V(2).Info("list ccr size is zero, Skip!")
		return nil
	}

	instanceIds := make([]string, 0)
	instanceInk8s := make(map[string]v1alpha1.CCR)
	for _, item := range ccrList.Items {
		instanceIds = append(instanceIds, item.GetName())
		instanceInk8s[item.GetName()] = item
	}

	now := time.Now().UTC()
	resp, err := b.resourceClient.ListResourceDetail(requestID, &billing.ListResourceDetailRequest{
		Region:                   b.region,
		ServiceType:              billing.ServiceType,
		ProductType:              strings.ToUpper(billing.ProductTypePrepay),
		Status:                   []string{"RUNNING", "STOPPED"},
		NameOrShortIds:           instanceIds,
		PageNo:                   1,
		PageSize:                 200, // TODO 后面单个region实例数量接近200时需要重新考虑这里的实现
		ExpireTimeAfterOrEquals:  now.AddDate(0, 0, -8).Format(time.RFC3339),
		ExpireTimeBeforeOrEquals: now.AddDate(10, 0, 0).Format(time.RFC3339),
	})

	if err != nil {
		logger.Error(err, "get billing resources failed, stop and wait for retry")
		return err
	}

	for _, resourceInfo := range resp.Result {
		ccr := instanceInk8s[resourceInfo.Name]

		if ccr.Spec.ExpireTime != nil && !resourceInfo.ExpireTime.IsZero() {
			if resourceInfo.ExpireTime.Unix() == ccr.Spec.ExpireTime.Time.Unix() {
				logger.V(2).Info("ccr expire time equal billing resource expire time, Skip!")
				continue
			}
		}

		expireTime := v1.NewTime(resourceInfo.ExpireTime)

		ccr.Spec.ExpireTime = &expireTime
		ccr.GetLabels()[v1alpha1.LastOrderIdKey] = resourceInfo.OrderId
		ccr.GetLabels()[v1alpha1.BillingResourceUuidKey] = resourceInfo.Uuid

		if err := b.client.Update(ctx, &ccr); err != nil {
			logger.V(2).Error(err, "patch to billing info failed, Skip!")
			continue
		}
	}
	logger.Info("stop billing sync")
	return nil
}
