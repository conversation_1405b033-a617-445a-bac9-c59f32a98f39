package workflow

import (
	"context"
	"fmt"
	"testing"

	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client"
	fakeruntime "sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	mockmodels "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/models/mock"
)

func TestHandler_getImageBuildStatus(t *testing.T) {
	type fields struct {
		legacyClient  kubernetes.Interface
		generalClient client.Client
		sqlCli        models.Interface
	}
	type args struct {
		ctx    context.Context
		object *wfv1.Workflow
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		// TODO: Add test cases.
		{
			name:   "pending",
			fields: fields{},
			args: args{
				object: &wfv1.Workflow{
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowPending,
					},
				},
			},
			want: "pending",
		},
		{
			name: "initilizing",
			fields: func() fields {
				cli := fake.NewSimpleClientset()
				return fields{
					legacyClient: cli,
				}
			}(),
			args: args{
				object: &wfv1.Workflow{
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowRunning,
					},
				},
			},
			want: "initializing",
		},
		{
			name: "running",
			fields: func() fields {
				cli := fake.NewSimpleClientset(&corev1.Pod{
					Status: corev1.PodStatus{
						ContainerStatuses: []corev1.ContainerStatus{
							{
								Name:        "main",
								ContainerID: "test",
							},
						},
					},
				})
				return fields{
					legacyClient: cli,
				}
			}(),
			args: args{
				object: &wfv1.Workflow{
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowRunning,
					},
				},
			},
			want: "running",
		},
		{
			name:   "failed",
			fields: fields{},
			args: args{
				object: &wfv1.Workflow{
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowFailed,
					},
				},
			},
			want: "failed",
		},
		{
			name:   "success",
			fields: fields{},
			args: args{
				object: &wfv1.Workflow{
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowSucceeded,
					},
				},
			},
			want: "success",
		},
		{
			name:   "unknown",
			fields: fields{},
			args: args{
				object: &wfv1.Workflow{
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowPhase("test"),
					},
				},
			},
			want: "unknown status test",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &Handler{
				legacyClient:  tt.fields.legacyClient,
				generalClient: tt.fields.generalClient,
				sqlCli:        tt.fields.sqlCli,
			}
			if got := h.getImageBuildStatus(tt.args.ctx, tt.args.object); got != tt.want {
				t.Errorf("Handler.getImageBuildStatus() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestHandler_ensureContainerStarted(t *testing.T) {
	type fields struct {
		legacyClient  kubernetes.Interface
		generalClient client.Client
		sqlCli        models.Interface
	}
	type args struct {
		ctx    context.Context
		object *wfv1.Workflow
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "pod not found",
			fields: fields{
				legacyClient: fake.NewSimpleClientset(),
			},
			args: args{
				ctx: context.Background(),
				object: &wfv1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "container not found",
			fields: fields{
				legacyClient: fake.NewSimpleClientset(
					&corev1.Pod{
						Status: corev1.PodStatus{
							ContainerStatuses: []corev1.ContainerStatus{
								{
									Name:        "ttt",
									ContainerID: "test",
								},
							},
						},
					},
				),
			},
			args: args{
				ctx: context.Background(),
				object: &wfv1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "normal",
			fields: fields{
				legacyClient: fake.NewSimpleClientset(
					&corev1.Pod{
						ObjectMeta: metav1.ObjectMeta{
							Name: "test",
						},
						Status: corev1.PodStatus{
							ContainerStatuses: []corev1.ContainerStatus{
								{
									Name:        "main",
									ContainerID: "test",
								},
							},
						},
					},
				),
			},
			args: args{
				ctx: context.Background(),
				object: &wfv1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &Handler{
				legacyClient:  tt.fields.legacyClient,
				generalClient: tt.fields.generalClient,
				sqlCli:        tt.fields.sqlCli,
			}
			if err := h.ensureContainerStarted(tt.args.ctx, tt.args.object); (err != nil) != tt.wantErr {
				t.Errorf("Handler.ensureContainerStarted() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestHandler_handleRunningPhase(t *testing.T) {
	type fields struct {
		legacyClient  kubernetes.Interface
		generalClient client.Client
		sqlCli        models.Interface
	}
	type args struct {
		ctx    context.Context
		object *wfv1.Workflow
		id     int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "update db failed",
			fields: func() fields {
				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().UpdateImageBuildStatusByID(gomock.Any(), gomock.Any()).Return(fmt.Errorf("error"))
				return fields{
					sqlCli: sqlCli,
				}
			}(),
			args: args{
				ctx: context.Background(),
				object: &wfv1.Workflow{
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowSucceeded,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "retry error",
			fields: func() fields {
				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().UpdateImageBuildStatusByID(gomock.Any(), gomock.Any()).Return(nil)
				return fields{
					sqlCli: sqlCli,
				}
			}(),
			args: args{
				ctx: context.Background(),
				object: &wfv1.Workflow{
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowSucceeded,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "normal",
			fields: func() fields {
				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().UpdateImageBuildStatusByID(gomock.Any(), gomock.Any()).Return(nil)
				return fields{
					sqlCli: sqlCli,
					legacyClient: fake.NewSimpleClientset(
						&corev1.Pod{
							Status: corev1.PodStatus{
								ContainerStatuses: []corev1.ContainerStatus{
									{
										Name:        "main",
										ContainerID: "test",
									},
								},
							},
						},
					),
				}
			}(),
			args: args{
				ctx: context.Background(),
				object: &wfv1.Workflow{
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowRunning,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &Handler{
				legacyClient:  tt.fields.legacyClient,
				generalClient: tt.fields.generalClient,
				sqlCli:        tt.fields.sqlCli,
			}
			if err := h.handleRunningPhase(tt.args.ctx, tt.args.object, tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("Handler.handleRunningPhase() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestHandler_handleDelete(t *testing.T) {
	type fields struct {
		legacyClient  kubernetes.Interface
		generalClient client.Client
		sqlCli        models.Interface
	}
	type args struct {
		ctx    context.Context
		object *wfv1.Workflow
		id     int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "container not found",
			fields: func() fields {
				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().SetImageBuildStatusAndLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				scheme := runtime.NewScheme()
				assert.NoError(t, wfv1.AddToScheme(scheme))
				assert.NoError(t, clientgoscheme.AddToScheme(scheme))
				fakeCli := fakeruntime.NewFakeClientWithScheme(scheme)
				return fields{
					legacyClient:  fake.NewSimpleClientset(),
					sqlCli:        sqlCli,
					generalClient: fakeCli,
				}
			}(),
			args: args{
				ctx:    context.Background(),
				object: &wfv1.Workflow{},
				id:     1,
			},
			wantErr: false,
		},
		{
			name: "container found",
			fields: func() fields {
				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().SetImageBuildStatusAndLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				scheme := runtime.NewScheme()
				assert.NoError(t, wfv1.AddToScheme(scheme))
				assert.NoError(t, clientgoscheme.AddToScheme(scheme))
				fakeCli := fakeruntime.NewFakeClientWithScheme(scheme)
				return fields{
					legacyClient: fake.NewSimpleClientset(
						&corev1.Pod{
							Status: corev1.PodStatus{
								ContainerStatuses: []corev1.ContainerStatus{
									{
										Name:        "main",
										ContainerID: "test",
									},
								},
							},
						},
					),
					sqlCli:        sqlCli,
					generalClient: fakeCli,
				}
			}(),
			args: args{
				ctx:    context.Background(),
				object: &wfv1.Workflow{},
				id:     1,
			},
			wantErr: false,
		},
		{
			name: "update failed",
			fields: func() fields {
				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().SetImageBuildStatusAndLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("error"))
				scheme := runtime.NewScheme()
				assert.NoError(t, wfv1.AddToScheme(scheme))
				assert.NoError(t, clientgoscheme.AddToScheme(scheme))
				fakeCli := fakeruntime.NewFakeClientWithScheme(scheme)
				return fields{
					legacyClient:  fake.NewSimpleClientset(),
					sqlCli:        sqlCli,
					generalClient: fakeCli,
				}
			}(),
			args: args{
				ctx:    context.Background(),
				object: &wfv1.Workflow{},
				id:     1,
			},
			wantErr: true,
		},
		{
			name: "delete failed",
			fields: func() fields {
				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().SetImageBuildStatusAndLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				fakeCli := fakeruntime.NewFakeClient()
				return fields{
					legacyClient:  fake.NewSimpleClientset(),
					sqlCli:        sqlCli,
					generalClient: fakeCli,
				}
			}(),
			args: args{
				ctx:    context.Background(),
				object: &wfv1.Workflow{},
				id:     1,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &Handler{
				legacyClient:  tt.fields.legacyClient,
				generalClient: tt.fields.generalClient,
				sqlCli:        tt.fields.sqlCli,
			}
			if err := h.handleDelete(tt.args.ctx, tt.args.object, tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("Handler.handleDelete() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestHandler_Handle(t *testing.T) {
	type fields struct {
		legacyClient  kubernetes.Interface
		generalClient client.Client
		sqlCli        models.Interface
	}
	type args struct {
		ctx    context.Context
		object *wfv1.Workflow
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:   "invalid build name",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				object: &wfv1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Name: "test",
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "deleted object",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				object: &wfv1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Name:              "image-build-1",
						DeletionTimestamp: &metav1.Time{},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "running",
			fields: func() fields {
				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().UpdateImageBuildStatusByID(gomock.Any(), gomock.Any()).Return(nil)
				return fields{
					sqlCli: sqlCli,
					legacyClient: fake.NewSimpleClientset(
						&corev1.Pod{
							Status: corev1.PodStatus{
								ContainerStatuses: []corev1.ContainerStatus{
									{
										Name:        "main",
										ContainerID: "test",
									},
								},
							},
						},
					),
				}
			}(),
			args: args{
				ctx: context.Background(),
				object: &wfv1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Name: "image-build-1",
					},
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowRunning,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "failed phase",
			fields: func() fields {
				sqlCli := mockmodels.NewMockInterface(gomock.NewController(t))
				sqlCli.EXPECT().SetImageBuildStatusAndLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				scheme := runtime.NewScheme()
				assert.NoError(t, wfv1.AddToScheme(scheme))
				assert.NoError(t, clientgoscheme.AddToScheme(scheme))
				fakeCli := fakeruntime.NewFakeClientWithScheme(scheme)
				return fields{
					legacyClient:  fake.NewSimpleClientset(),
					sqlCli:        sqlCli,
					generalClient: fakeCli,
				}
			}(),
			args: args{
				ctx: context.Background(),
				object: &wfv1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Name: "image-build-1",
					},
					Status: wfv1.WorkflowStatus{
						Phase: wfv1.WorkflowFailed,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &Handler{
				legacyClient:  tt.fields.legacyClient,
				generalClient: tt.fields.generalClient,
				sqlCli:        tt.fields.sqlCli,
			}
			if err := h.Handle(tt.args.ctx, tt.args.object); (err != nil) != tt.wantErr {
				t.Errorf("Handler.Handle() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewHandler(t *testing.T) {
	type args struct {
		legacyCli  kubernetes.Interface
		generalCli client.Client
		sqlCli     models.Interface
	}
	tests := []struct {
		name    string
		args    args
		isEmpty bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				legacyCli: fake.NewSimpleClientset(),
			},
			isEmpty: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewHandler(tt.args.legacyCli, tt.args.generalCli, tt.args.sqlCli); (got != nil) == tt.isEmpty {
				t.Errorf("NewHandler() = %v, want %v", got, tt.isEmpty)
			}
		})
	}
}
