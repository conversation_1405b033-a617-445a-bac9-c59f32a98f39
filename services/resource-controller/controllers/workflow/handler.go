package workflow

import (
	"context"
	"strconv"
	"strings"
	"time"

	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/errors"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/model"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/utils"
)

type Handler struct {
	legacyClient  kubernetes.Interface
	generalClient client.Client
	sqlCli        models.Interface
}

func NewHandler(legacyCli kubernetes.Interface, generalCli client.Client, sqlCli models.Interface) *Handler {
	return &Handler{
		legacyClient:  legacyCli,
		generalClient: generalCli,
		sqlCli:        sqlCli,
	}
}

func (h *Handler) Handle(ctx context.Context, object *wfv1.Workflow) error {
	obj := object.DeepCopy()
	logger := log.FromContext(ctx)

	name := obj.GetName()
	idStr := strings.TrimPrefix(name, "image-build-")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logger.Info("invalid name, skip", "name", name)
		return nil
	}

	if obj.DeletionTimestamp != nil {
		return nil
	}

	// need to create and update
	switch obj.Status.Phase {
	case wfv1.WorkflowRunning:
		return h.handleRunningPhase(ctx, obj, id)
	case wfv1.WorkflowSucceeded, wfv1.WorkflowFailed, wfv1.WorkflowError:
		return h.handleDelete(ctx, obj, id)
	}

	return nil
}

func (h *Handler) handleDelete(ctx context.Context, object *wfv1.Workflow, id int64) error {
	// write log back
	logger := log.FromContext(ctx)

	// delete secret first
	err := h.generalClient.Delete(ctx, &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      object.GetName(),
			Namespace: object.GetNamespace(),
		},
	})
	if err != nil {
		logger.Error(err, "delete secret failed")
		if !apierrors.IsNotFound(err) {
			return err
		}
	}

	// check containerd exist
	logStr := ""
	mainContainerStarted := true
	if err := h.ensureContainerStarted(ctx, object); err != nil {
		if !apierrors.IsNotFound(err) {
			logger.Error(err, "ensure containerd started failed")
			return err
		}

		logger.Info("container not found")
		logStr = "build message: " + object.Status.Message
		mainContainerStarted = false
	}

	if mainContainerStarted {
		logger.Info("container started, start to get log")
		raw, err := h.legacyClient.CoreV1().
			Pods(object.Namespace).
			GetLogs(object.GetName(), &v1.PodLogOptions{
				Container: "main",
				TailLines: utils.Int64Ptr(100),
			}).Do(ctx).Raw()

		if err != nil {
			logger.Error(err, "get log failed")
			return err
		}
		logStr = string(raw)
	}

	// update status, finish time and log
	err = h.sqlCli.SetImageBuildStatusAndLog(id, h.getImageBuildStatus(ctx, object), logStr)
	if err != nil {
		logger.Error(err, "set image log to db failed")
		return err
	}

	// delete
	err = h.generalClient.Delete(ctx, object)
	if err != nil && !apierrors.IsNotFound(err) {
		logger.Error(err, "delete object failed")
		return err
	}

	return nil
}

func (h *Handler) handleRunningPhase(ctx context.Context, object *wfv1.Workflow, id int64) error {
	// write status to database
	logger := log.FromContext(ctx)

	status := h.getImageBuildStatus(ctx, object)

	err := h.sqlCli.UpdateImageBuildStatusByID(id, status)
	if err != nil {
		logger.Error(err, "update image build status failed")
		return err
	}

	if status != model.ImageBuildStatusRunning {
		return &errors.ReconcileRetryError{
			RetryAfter: 10 * time.Second,
		}
	}

	return nil
}

func (h *Handler) ensureContainerStarted(ctx context.Context, object *wfv1.Workflow) error {
	logger := log.FromContext(ctx)

	if podObj, err := h.legacyClient.CoreV1().Pods(object.Namespace).Get(ctx, object.GetName(), metav1.GetOptions{}); err != nil {
		logger.Error(err, "get pod object failed")
		return err
	} else {
		logger.Info("container status log", "num", len(podObj.Status.ContainerStatuses))
		for _, v := range podObj.Status.ContainerStatuses {
			logger.Info("container status", "name", v.Name, "container-id", v.ContainerID)
			if v.Name == "main" && v.ContainerID != "" {
				return nil
			}
		}
	}

	return apierrors.NewNotFound(schema.GroupResource{Group: "", Resource: "pods"}, object.GetName())
}

func (h *Handler) getImageBuildStatus(ctx context.Context, object *wfv1.Workflow) string {
	switch object.Status.Phase {
	case wfv1.WorkflowPending:
		return model.ImageBuildStatusPending
	case wfv1.WorkflowRunning:
		if err := h.ensureContainerStarted(ctx, object); err != nil {
			return model.ImageBuildStatusInitializing
		}
		return model.ImageBuildStatusRunning
	case wfv1.WorkflowError, wfv1.WorkflowFailed:
		return model.ImageBuildStatusFailed
	case wfv1.WorkflowSucceeded:
		return model.ImageBuildStatusSuccess
	}

	return "unknown status " + string(object.Status.Phase)
}
