package bos

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos/api"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

const (
	bosAclFormat = `{"accessControlList":[{"grantee":[{"id":"%s"}],"permission":["FULL_CONTROL"]}]}`
)

type Handler struct {
	clients clientset.ClientSetInterface

	config    *conf.Config
	k8sclient client.Client

	failOverDuration time.Duration
}

func NewHandler(config *conf.Config, clients clientset.ClientSetInterface, cli client.Client) *Handler {
	return &Handler{
		config:    config,
		clients:   clients,
		k8sclient: cli,

		failOverDuration: 3 * time.Minute,
	}
}

func (handler *Handler) Handle(ctx context.Context, object *ccrv1alpha1.Bos) error {
	obj := object.DeepCopy()
	// need to create and update
	switch {
	case obj.Status.Phase == "":
		return handler.handleCreate(ctx, obj)
	}

	return nil
}

// handleCreate 处理创建操作，如果bucket不存在则创建，如果acl不存在则设置，如果需要加密则开启加密等。
// ctx context.Context: 上下文信息，包含日志记录器和请求跟踪ID
// object *ccrv1alpha1.Bos: Bos对象，包含外部账号、用户ID、需要创建的bucket名称等信息
// 返回值 error: 错误信息，如果为nil表示处理成功
func (handler *Handler) handleCreate(ctx context.Context, object *ccrv1alpha1.Bos) error {
	logger := log.FromContext(ctx)
	bosCli, err := handler.clients.BosClientForAccount(object.Spec.ExternalAccountID, object.Spec.ExternalUserID, handler.config.BOSEndpoint)
	if err != nil {
		logger.V(2).Error(err, fmt.Sprintf("get bos client for %v failed", object.Spec.ExternalAccountID))
		handler.updateReason(ctx, object, "ClientNotReady", err.Error())
		return err
	}

	// TODO: 区分bucket已经存在，bucket没有权限
	// 注意case：创建成功，但是更新数据库失败
	needCreated := false
	if object.Status.Bucket == "" {
		err := bosCli.HeadBucket(object.GetName())
		var bceError *bce.BceServiceError
		if err != nil {
			if errors.As(err, &bceError) {
				switch bceError.StatusCode {
				case http.StatusForbidden:
					logger.V(2).Error(fmt.Errorf("have no permission to access the bucket"), "have no permission")
					object.Status.Phase = ccrv1alpha1.BosStatusPhaseFailed
					handler.updateReason(ctx, object, "BucketNotReady", "no permission")
					return fmt.Errorf("has no permission to access the bucket %v", object.GetName())
				case http.StatusNotFound:
					needCreated = true
				default:
					logger.V(2).Error(err, "head bucket failed")
					handler.updateReason(ctx, object, "BucketNotReady", err.Error())
					return err
				}

			} else {
				logger.V(2).Error(err, "head bucket failed")
				handler.updateReason(ctx, object, "BucketNotReady", err.Error())
				return err
			}
		}

		if needCreated {
			_, err = bosCli.PutBucket(object.GetName())
			if err != nil {
				logger.V(2).Error(err, "create bucket failed")
				handler.updateReason(ctx, object, "BucketNotReady", err.Error())
				return err
			}
		}
		object.Status.Bucket = object.GetName()
	}

	if object.Status.Acl == "" {
		aclStr := fmt.Sprintf(bosAclFormat, object.Spec.UserID)
		err = bosCli.PutBucketAclFromString(object.Status.Bucket, aclStr)
		if err != nil {
			logger.V(2).Error(err, "put acl to bucket failed")
			handler.updateReason(ctx, object, "BucketAclNotReady", err.Error())
			return err
		}

		object.Status.Acl = aclStr
	}

	// bosCli.PutBucketEncryption()
	if object.Spec.NeedEncrypt {
		var alg string
		alg, err = bosCli.GetBucketEncryption(object.Status.Bucket)
		if err != nil {
			logger.V(2).Error(err, "get bucket encrypt failed")
			handler.updateReason(ctx, object, "GetBucketEncryptFailed", err.Error())
			return err
		}

		logger.Info(fmt.Sprintf("bucket is encrypted: %s", alg))
		if alg == "none" {
			err = bosCli.PutBucketEncryption(object.Status.Bucket, "AES256")
			if err != nil {
				logger.V(2).Error(err, "encrypt bucket failed")
				handler.updateReason(ctx, object, "BucketEncryptFailed", err.Error())
				return err
			}
		}
	}

	if object.Spec.NeedTrash {
		bucketTrash, err := bosCli.GetBucketTrash(object.Status.Bucket)
		var bceError *bce.BceServiceError
		needOpenTrash := false
		if err != nil {
			if errors.As(err, &bceError) {
				switch bceError.StatusCode {
				case http.StatusForbidden:
					logger.V(2).Error(fmt.Errorf("have no permission to open the bucket trash"), "have no permission")
					object.Status.Phase = ccrv1alpha1.BosStatusPhaseFailed
					handler.updateReason(ctx, object, "BucketTrashFailed", "no bucket trash permission")
					return fmt.Errorf("has no permission to open the bucket trash %v", object.GetName())
				case http.StatusNotFound:
					switch bceError.Code {
					case "NoSuchTrashDirectory":
						needOpenTrash = true
					default:
						logger.V(2).Error(err, "get bucket trash failed")
						handler.updateReason(ctx, object, "BucketTrashFailed", err.Error())
						return err
					}
				default:
					logger.V(2).Error(err, "get bucket trash failed")
					handler.updateReason(ctx, object, "BucketTrashFailed", err.Error())
					return err
				}
			} else {
				logger.V(2).Error(err, "get bucket encrypt failed")
				handler.updateReason(ctx, object, "BucketTrashFailed", err.Error())
				return err
			}
		}

		logger.Info(fmt.Sprintf("bucket is trash: %s", bucketTrash.TrashDir))
		if needOpenTrash {
			if err := bosCli.PutBucketTrash(object.Status.Bucket, api.PutBucketTrashReq{
				TrashDir: ".trash",
			}); err != nil {
				logger.V(2).Error(err, "open bucket trash failed")
				handler.updateReason(ctx, object, "BucketTrashFailed", err.Error())
				return err
			}
		}
	}

	if object.Spec.NeedLifeCycle {
		lifecycleResult, err := bosCli.GetBucketLifecycle(object.Status.Bucket)
		var bceError *bce.BceServiceError
		needOpenLifecycle := false
		if err != nil {
			if errors.As(err, &bceError) {
				switch bceError.StatusCode {
				case http.StatusForbidden:
					logger.V(2).Error(fmt.Errorf("have no permission to open the bucket lifecycle "), "have no permission")
					object.Status.Phase = ccrv1alpha1.BosStatusPhaseFailed
					handler.updateReason(ctx, object, "BucketLifecycleFailed", "no bucket lifecycle permission")
					return fmt.Errorf("has no permission to open the bucket lifecycle %v", object.GetName())
				case http.StatusNotFound:
					switch bceError.Code {
					case "NoLifecycleConfiguration":
						needOpenLifecycle = true
					default:
						logger.V(2).Error(err, "get bucket lifecycle failed")
						handler.updateReason(ctx, object, "BucketLifecycleFailed", err.Error())
						return err
					}
				default:
					logger.V(2).Error(err, "get bucket lifecycle failed")
					handler.updateReason(ctx, object, "BucketLifecycleFailed", err.Error())
					return err
				}
			} else {
				logger.V(2).Error(err, "get bucket lifecycle failed")
				handler.updateReason(ctx, object, "BucketLifecycleFailed", err.Error())
				return err
			}
		}

		//logger.Info(fmt.Sprintf("bucket is lifecycle: %s", lifecycleResult.Rule))
		if needOpenLifecycle {

			lifecycleArgs := api.PutBucketLifecycleArgs{}
			for _, ruleType := range lifecycleResult.Rule {
				lifecycleArgs.Rule = append(lifecycleArgs.Rule, ruleType)
			}

			lifecycleArgs.Rule = append(lifecycleArgs.Rule, api.LifecycleRuleType{
				Id:       fmt.Sprintf("%s/.trash/*-DeleteObject", object.Status.Bucket),
				Status:   "enabled",
				Resource: []string{fmt.Sprintf("%s/.trash/*", object.Status.Bucket)},
				Condition: api.LifecycleConditionType{
					Time: api.LifecycleConditionTimeType{
						DateGreaterThan: "$(lastModified)+P15D",
					},
				},
				Action: api.LifecycleActionType{
					Name: "DeleteObject",
				},
			})

			lifecycleString, err := json.Marshal(lifecycleArgs)
			if err != nil {
				fmt.Printf("marshal lifecycle failed: %v", err)
				return err
			}

			if err := bosCli.PutBucketLifecycleFromString(object.Status.Bucket, string(lifecycleString)); err != nil {
				logger.V(2).Error(err, "open bucket lifecycle failed")
				handler.updateReason(ctx, object, "BucketLifecycleFailed", err.Error())
				return err
			}
		}
	}

	object.Status.Phase = ccrv1alpha1.BosStatusPhaseReady
	return handler.updateReason(ctx, object, "", "")
}

func (handler *Handler) updateReason(ctx context.Context, object *ccrv1alpha1.Bos, reason string, message string) error {
	logger := log.FromContext(ctx)
	object.Status.Reason = reason
	object.Status.Message = message
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime

	if object.Status.Phase != ccrv1alpha1.BosStatusPhaseReady &&
		currentTime.Sub(object.CreationTimestamp.Time) > handler.failOverDuration {
		object.Status.Phase = ccrv1alpha1.BosStatusPhaseFailed
	}

	err := handler.k8sclient.Status().Update(ctx, object)
	if err != nil {
		logger.Error(err, "update status failed")
	}

	return err
}
