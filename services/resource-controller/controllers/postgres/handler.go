package postgres

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/rds"
	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/database/postgres"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

type Handler struct {
	clients clientset.ClientSetInterface

	config    *conf.Config
	k8sclient client.Client
	vpcId     string
	dbs       *sync.Map

	failOverDuration time.Duration
}

func NewHandler(config *conf.Config, clients clientset.ClientSetInterface, vpcID string, cli client.Client) *Handler {
	return &Handler{
		config:    config,
		clients:   clients,
		k8sclient: cli,
		vpcId:     vpcID,
		dbs:       new(sync.Map),

		failOverDuration: 10 * time.Minute,
	}
}

func (handler *Handler) Handle(ctx context.Context, object *ccrv1alpha1.Postgres) error {
	obj := object.DeepCopy()
	// need to create and update
	switch {
	case obj.DeletionTimestamp != nil:
		return handler.handleDelete(ctx, obj)
	case obj.Status.Phase == "":
		return handler.handlePendingPhase(ctx, obj)
	case obj.Status.Phase == ccrv1alpha1.PostgresStatusCreating:
		return handler.handleCreate(ctx, obj)
	case obj.Status.Type != obj.Spec.InstanceType:
		return handler.handleUpgrading(ctx, obj)
	}

	return nil
}

func (handler *Handler) handlePendingPhase(ctx context.Context, object *ccrv1alpha1.Postgres) error {
	// set finalizer first
	logger := log.FromContext(ctx)
	finalizerSetted, err := handler.setFinalizer(ctx, object)
	if err != nil {
		logger.V(2).Error(err, "update finalizer failed")
		return err
	}

	// finalizer setted, waitfor next round
	if !finalizerSetted {
		logger.V(2).Info("finalizer has been setted, wait for phase to be update")
		return nil
	}

	object.Status.Phase = ccrv1alpha1.PostgresStatusCreating
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime
	object.Status.LastTransitionTime = &currentTime
	object.Status.Type = object.Spec.InstanceType

	return handler.k8sclient.Status().Update(ctx, object)
}

func (handler *Handler) handleDelete(ctx context.Context, object *ccrv1alpha1.Postgres) error {
	logger := log.FromContext(ctx)
	// 1. get database
	// 2. if not exist create database
	// 3. if exist, wait database can be access
	if object.Status.InstanceID == "" {
		return handler.removeFinalizer(ctx, object)
	}

	_, err := handler.clients.RdsClient().GetDetail(object.Status.InstanceID)
	var bceError *bce.BceServiceError
	if errors.As(err, &bceError) && bceError.StatusCode == http.StatusNotFound {
		return handler.removeFinalizer(ctx, object)
	}

	if err != nil {
		logger.V(2).Error(err, fmt.Sprintf("get rds instaince %v failed", object.Status.InstanceID))
		handler.updateReason(ctx, object, "DeleteFailed", err.Error())
		return err
	}

	err = handler.clients.RdsClient().DeleteRds(object.Status.InstanceID)
	if err != nil {
		bceErr := &bce.BceServiceError{}
		if errors.As(err, &bceErr) && bceErr.StatusCode == 404 {
			return nil
		}

		logger.V(2).Error(err, fmt.Sprintf("get the detail of instance %v failed", object.Status.InstanceID))
		handler.updateReason(ctx, object, "InstanceDeleteFailed", err.Error())
		return err
	}

	return handler.removeFinalizer(ctx, object)
}

func (handler *Handler) tryGetRdsInstance(ctx context.Context, instanceName string) (*rds.Instance, error) {
	logger := log.FromContext(ctx)
	marker, maxKeys := "", 1000
	isTruncated := true

	for isTruncated {
		res, err := handler.clients.RdsClient().ListRds(&rds.ListRdsArgs{
			Marker:  marker,
			MaxKeys: maxKeys,
		})
		if err != nil {
			logger.V(2).Error(err, "list rds failed")
			return nil, err
		}

		for _, v := range res.Instances {
			if v.InstanceName == instanceName {
				return &v, nil
			}
		}

		marker = res.NextMarker
		isTruncated = res.IsTruncated
	}

	return nil, nil
}

func (handler *Handler) handleCreate(ctx context.Context, object *ccrv1alpha1.Postgres) error {
	logger := log.FromContext(ctx)
	logger.V(2).Info("start create loop", "instance", object.GetName())
	password, err := base64.StdEncoding.DecodeString(object.Spec.Password)
	if err != nil {
		logger.Error(err, "password is invalid")
		object.Status.Phase = ccrv1alpha1.PostgresStatusFailed
		handler.updateReason(ctx, object, "RdsAccountNotReady", err.Error())
		return err
	}

	if object.Status.InstanceID == "" {
		// xxx
		instance, err := handler.tryGetRdsInstance(ctx, object.GetName())
		if err != nil {
			logger.Error(err, "try to list rds instance failed")
			handler.updateReason(ctx, object, "RdsNotReady", err.Error())
			return err
		}

		if instance == nil {
			// get zone
			subnets, err := handler.clients.RdsClient().ListSubnets(&rds.ListSubnetsArgs{VpcId: handler.vpcId})
			if err != nil {
				logger.V(2).Error(err, fmt.Sprintf("list subnets failed: %v", handler.vpcId))
				handler.updateReason(ctx, object, "SubnetsNotReady", err.Error())
				return err
			}

			subnetMap := make([]rds.SubnetMap, 0)
			zoneNameSet := make(map[string]interface{})
			for _, v := range subnets.Subnets {
				subnetMap = append(subnetMap, rds.SubnetMap{
					ZoneName: v.ZoneName,
					SubnetId: v.SubnetId,
				})

				zoneNameSet[v.ZoneName] = nil
			}

			zoneNames := make([]string, 0)
			for k := range zoneNameSet {
				zoneNames = append(zoneNames, k)
			}

			// create rds
			createResult, err := handler.clients.RdsClient().CreateRds(&rds.CreateRdsArgs{
				ClientToken: string(uuid.NewUUID()),
				Billing: rds.Billing{
					PaymentTiming: "Postpaid",
				},
				PurchaseCount:  1,
				InstanceName:   object.GetName(),
				Engine:         "PostgreSQL",
				EngineVersion:  handler.config.Postgresql.EngineVersion,
				Category:       "Standard",
				CpuCount:       handler.config.Postgresql.Spec[strings.ToLower(object.Spec.InstanceType)].CPU,
				MemoryCapacity: float64(handler.config.Postgresql.Spec[strings.ToLower(object.Spec.InstanceType)].Memory),
				VolumeCapacity: handler.config.Postgresql.Spec[strings.ToLower(object.Spec.InstanceType)].VolumeCapacity,
				IsDirectPay:    true,
				ZoneNames:      []string{handler.config.Postgresql.ZoneName},
				VpcId:          handler.vpcId,
				Subnets: []rds.SubnetMap{
					{
						ZoneName: handler.config.Postgresql.ZoneName,
						SubnetId: handler.config.Postgresql.SubnetID,
					},
				},
				// 磁盘类型, normal_io:本地盘ssd磁盘, cloud_high:高性能云磁盘, cloud_nor:通用型SSD, cloud_enha:增强型SSD, 必选
				DiskIoType: handler.config.Postgresql.DiskIoType,
			})

			if err != nil {
				logger.V(2).Error(err, "create rds failed")
				handler.updateReason(ctx, object, "RdsNotReady", err.Error())
				return err
			}

			object.Status.InstanceID = createResult.InstanceIds[0]
		} else {
			object.Status.InstanceID = instance.InstanceId
		}
	}

	if object.Status.Host == "" {
		instance, err := handler.clients.RdsClient().GetDetail(object.Status.InstanceID)
		if err != nil {
			logger.Error(err, fmt.Sprintf("get instance %v failed", object.Status.InstanceID))
			handler.updateReason(ctx, object, "RdsNotReady", err.Error())
			return err
		}

		if instance.InstanceStatus != "Available" {
			logger.Info(fmt.Sprintf("instance is not ready: %v", instance.InstanceStatus))
			handler.updateReason(ctx, object, "RdsNotReady", fmt.Sprintf("expected instance status=%s "+
				"but got=%s", "Available", instance.InstanceStatus))
			return fmt.Errorf("instance is not ready")
		}

		object.Status.Host = instance.Endpoint.Address
		object.Status.Port = fmt.Sprintf("%v", instance.Endpoint.Port)
	}

	if object.Status.UserName == "" {
		rdsAccounts, err := handler.clients.RdsClient().ListAccount(object.Status.InstanceID)
		if err != nil {
			logger.V(2).Error(err, "list account failed for instance "+object.Status.InstanceID)
			handler.updateReason(ctx, object, "RdsAccountNotReady", err.Error())
			return err
		}

		// TODO 返回值不为空，暂时认为账户已经创建完成，取第一个用户
		if len(rdsAccounts.Accounts) != 0 {
			object.Status.UserName = rdsAccounts.Accounts[0].AccountName
		} else {
			err = handler.clients.RdsClient().CreateAccount(
				object.Status.InstanceID,
				&rds.CreateAccountArgs{
					ClientToken: string(uuid.NewUUID()),
					AccountName: object.Spec.Username,
					Password:    string(password),
					AccountType: "Super",
					Type:        "OnlyMaster",
					Desc:        "Automatically created by CCR resource controller",
				})
			if err != nil {
				logger.V(2).Error(err, "create account failed")
				handler.updateReason(ctx, object, "RdsAccountNotReady", err.Error())
				return err
			}
			object.Status.UserName = object.Spec.Username
		}
	}

	// wait for user is ready
	user, err := handler.clients.RdsClient().GetAccount(object.Status.InstanceID, object.Status.UserName)
	if err != nil {
		logger.V(2).Error(err, fmt.Sprintf("get account %v of instance %v failed", object.Status.UserName, object.Status.InstanceID))
		handler.updateReason(ctx, object, "RdsAccountNotReady", fmt.Sprintf("get user failed: %s", err))
		return err
	}

	if user.Status != "Available" {
		logger.V(2).Info(fmt.Sprintf("account %v of instance %v is not ready", object.Status.UserName, object.Status.InstanceID))
		handler.updateReason(ctx, object, "RdsAccountNotReady", "user is not ready")
		return fmt.Errorf("user is not ready")
	}

	object.Status.Type = object.Spec.InstanceType
	object.Status.Phase = ccrv1alpha1.PostgresStatusRunning
	return handler.updateReason(ctx, object, "", "")
}

func (handler *Handler) handleUpgrading(ctx context.Context, object *ccrv1alpha1.Postgres) error {
	logger := log.FromContext(ctx)

	if object.Status.Phase != ccrv1alpha1.PostgresStatusUpgrading {
		currentTime := metav1.Now()
		object.Status.Phase = ccrv1alpha1.PostgresStatusUpgrading
		object.Status.LastTransitionTime = &currentTime
	}

	if object.Status.InstanceID == "" {
		logger.V(2).Error(fmt.Errorf("no instance id found"), "no instance id found")
		handler.updateReason(ctx, object, "RdsNotReady", "instance is empty")
		return fmt.Errorf("no instance id found")
	}

	instance, err := handler.clients.RdsClient().GetDetail(object.Status.InstanceID)
	if err != nil {
		logger.Error(err, fmt.Sprintf("get instance %v failed", object.Status.InstanceID))
		handler.updateReason(ctx, object, "RdsNotReady", err.Error())
		return err
	}

	if strings.ToLower(instance.InstanceStatus) != "available" {
		logger.Error(fmt.Errorf("instance status is not available"), "instance status is "+instance.InstanceStatus)
		handler.updateReason(ctx, object, "RdsNotReady", "instance is not ready")
		return fmt.Errorf("instance is %s, not ready", instance.InstanceStatus)
	}

	expectedCPU := handler.config.Postgresql.Spec[strings.ToLower(object.Spec.InstanceType)].CPU
	expectedMemory := handler.config.Postgresql.Spec[strings.ToLower(object.Spec.InstanceType)].Memory
	expectedVolume := handler.config.Postgresql.Spec[strings.ToLower(object.Spec.InstanceType)].VolumeCapacity

	// 降配 暂不支持
	if instance.CpuCount <= expectedCPU &&
		instance.VolumeCapacity <= expectedVolume &&
		instance.MemoryCapacity-float64(expectedMemory) <= 1 {

		if instance.CpuCount != expectedCPU ||
			instance.VolumeCapacity != expectedVolume ||
			math.Abs(instance.MemoryCapacity-float64(expectedMemory)) > 1 {
			// 开始升级
			err = handler.clients.RdsClient().ResizeRds(object.Status.InstanceID, &rds.ResizeRdsArgs{
				CpuCount:       expectedCPU,
				MemoryCapacity: float64(expectedMemory),
				VolumeCapacity: expectedVolume,
				IsDirectPay:    true,
			})

			if err != nil {
				logger.V(2).Error(err, "resize rds failed")
				handler.updateReason(ctx, object, "RdsResizeFailed", err.Error())
				return err
			}

			return fmt.Errorf("resize success, wait for postgres available")
		}
	}

	object.Status.Type = object.Spec.InstanceType
	object.Status.Phase = ccrv1alpha1.PostgresStatusRunning
	return handler.updateReason(ctx, object, "", "")
}

// createDatabases 创建数据库，如果不存在则创建。
// 参数：
//
//	ctx context.Context - 上下文信息，包含日志记录器等功能。
//	object *ccrv1alpha1.Postgres - Postgres CR对象指针。
//	password string - 密码字符串。
//	database string - 数据库名称字符串。
//
// 返回值：
//
//	error - 错误信息，如果成功则为nil。
func (handler *Handler) createDatabases(ctx context.Context, object *ccrv1alpha1.Postgres, password, database string) error {
	logger := log.FromContext(ctx)
	port, _ := strconv.Atoi(object.Status.Port)
	var err error

	db, dbConnExisted := handler.dbs.Load(object.GetName())
	if !dbConnExisted {
		db, err = postgres.NewPostgres(object.Status.Host,
			port,
			object.Status.UserName,
			password, "postgres")
		if err != nil {
			logger.Error(err, fmt.Sprintf("open postgres failed: %v", object.Status.Host))
			handler.updateReason(ctx, object, "DatabaseNotReady", err.Error())
			return err
		}

		handler.dbs.Store(object.GetName(), db)
	}

	pg := db.(*postgres.Postgres)
	dbExisted, err := pg.ExistDB(database)
	if err != nil {
		logger.Error(err, "get database failed")
		handler.updateReason(ctx, object, "DatabaseNotReady", err.Error())
		return err
	}

	if !dbExisted {
		err = pg.CreateDB(database)
		if err != nil {
			logger.Error(err, "create database failed")
			handler.updateReason(ctx, object, "DatabaseNotReady", err.Error())
			return err
		}
	}

	return nil
}

func (handler *Handler) updateReason(ctx context.Context, object *ccrv1alpha1.Postgres, reason string, message string) error {
	logger := log.FromContext(ctx)
	object.Status.Reason = reason
	object.Status.Message = message
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime

	if object.Status.Phase != ccrv1alpha1.PostgresStatusRunning &&
		object.Status.LastTransitionTime != nil &&
		currentTime.Sub(object.Status.LastTransitionTime.Time) > handler.failOverDuration {
		object.Status.Phase = ccrv1alpha1.PostgresStatusFailed
	}

	err := handler.k8sclient.Status().Update(ctx, object)
	if err != nil {
		logger.Error(err, "update status failed")
	}

	return err
}

func (handler *Handler) setFinalizer(ctx context.Context, object *ccrv1alpha1.Postgres) (bool, error) {
	finalizers := object.GetFinalizers()
	if finalizers == nil {
		finalizers = make([]string, 0)
	}

	for _, v := range finalizers {
		if v == ccrv1alpha1.ResourceFinalizer {
			return true, nil
		}
	}

	finalizers = append(finalizers, ccrv1alpha1.ResourceFinalizer)
	object.SetFinalizers(finalizers)

	return false, handler.k8sclient.Update(ctx, object)
}

func (handler *Handler) removeFinalizer(ctx context.Context, object *ccrv1alpha1.Postgres) error {
	finalizers := object.GetFinalizers()
	if len(finalizers) == 0 {
		return nil
	}

	reservedFinalizer := make([]string, 0)
	for _, v := range finalizers {
		if v != ccrv1alpha1.ResourceFinalizer {
			reservedFinalizer = append(reservedFinalizer, v)
		}
	}

	object.SetFinalizers(reservedFinalizer)
	return handler.k8sclient.Update(ctx, object)
}
