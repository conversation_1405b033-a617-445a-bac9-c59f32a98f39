package controllers

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/kafka"
	testingkafka "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/kafka"
)

func TestResGroupSyncer_run(t *testing.T) {

	type fields struct {
		region   string
		client   client.Client
		producer kafka.ProducerInterface
		interval time.Duration
		options  *KafkaOptions
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "case1",
			fields: fields{
				region:   "gztest",
				client:   fake.NewClientBuilder().Build(),
				producer: &testingkafka.KafkaClient{},
				interval: time.Hour,
				options:  NewKafkaOptions("", "", "", "", ""),
			},
			args: args{
				context.Background(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "case2",
			fields: fields{
				region:   "gztest",
				client:   fake.NewClientBuilder().Build(),
				producer: &testingkafka.KafkaClient{},
				interval: time.Hour,
				options:  NewKafkaOptions("", "", "", "", ""),
			},
			args: args{
				context.Background(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "case3",
			fields: fields{
				region:   "gz",
				client:   fake.NewClientBuilder().Build(),
				producer: &testingkafka.KafkaClient{},
				interval: time.Hour,
				options:  NewKafkaOptions("", "", "", "", ""),
			},
			args: args{
				context.Background(),
			},
			wantErr: assert.Error,
		},
		{
			name: "case4",
			fields: fields{
				region:   "gz",
				client:   fake.NewClientBuilder().Build(),
				producer: &testingkafka.KafkaClient{},
				interval: time.Hour,
				options:  NewKafkaOptions("", "", "", "", ""),
			},
			args: args{
				context.Background(),
			},
			wantErr: assert.Error,
		},
	}
	for _, tt := range tests {

		if tt.name == "case1" {
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)
			tt.fields.client = fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects(
				&v1alpha1.CCR{
					ObjectMeta: v1.ObjectMeta{Name: "test"},
					Status:     v1alpha1.CCRStatus{IsResGroupSynchronized: "CreatedAndSync"}}).Build()
			mock.OnAnything(tt.fields.producer, "StartProducer").Return(nil)
			mock.OnAnything(tt.fields.producer, "SendMessage").Return(int32(3), int64(0x32), nil)
			mock.OnAnything(tt.fields.producer, "Close").Return(nil)
		}

		if tt.name == "case2" {
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)
			tt.fields.client = fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects(
				&v1alpha1.CCR{
					ObjectMeta: v1.ObjectMeta{Name: "test"},
					Status:     v1alpha1.CCRStatus{IsResGroupSynchronized: "CreatedNotSync"}}).Build()
			mock.OnAnything(tt.fields.producer, "StartProducer").Return(nil)
			mock.OnAnything(tt.fields.producer, "SendMessage").Return(int32(3), int64(0x32), nil)
			mock.OnAnything(tt.fields.producer, "Close").Return(nil)
		}

		if tt.name == "case3" {
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)

			tt.fields.client = fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects(
				&v1alpha1.CCR{
					ObjectMeta: v1.ObjectMeta{Name: "test"},

					Status: v1alpha1.CCRStatus{IsResGroupSynchronized: "CreatedNotSync"}}).Build()
			mock.OnAnything(tt.fields.producer, "StartProducer").Return(errors.New("StartProducer failed"))
		}

		if tt.name == "case4" {
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)

			tt.fields.client = fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects(
				&v1alpha1.CCR{
					ObjectMeta: v1.ObjectMeta{Name: "test"},

					Status: v1alpha1.CCRStatus{IsResGroupSynchronized: "CreatedNotSync"}}).Build()
			mock.OnAnything(tt.fields.producer, "StartProducer").Return(nil)
			mock.OnAnything(tt.fields.producer, "SendMessage").Return(int32(3), int64(0x32), errors.New("sendMessage failed"))
		}

		t.Run(tt.name, func(t *testing.T) {
			b := &ResgroupSyncer{
				region:   tt.fields.region,
				client:   tt.fields.client,
				producer: tt.fields.producer,
				interval: tt.fields.interval,
				options:  tt.fields.options,
			}
			tt.wantErr(t, b.run(tt.args.ctx), fmt.Sprintf("run(%v)", tt.args.ctx))
		})
	}
}

func TestResGroupSyncer(t *testing.T) {
	type args struct {
		region   string
		client   client.Client
		producer kafka.ProducerInterface
		interval time.Duration
		options  *KafkaOptions
	}
	tests := []struct {
		name string
		args args
		want *ResgroupSyncer
	}{
		{
			name: "case1",
			args: args{
				region:   "gz",
				client:   fake.NewClientBuilder().Build(),
				producer: &testingkafka.KafkaClient{},
				interval: time.Hour,
				options:  NewKafkaOptions("", "", "", "", ""),
			},
			want: NewResgroupSyncer("gz", fake.NewClientBuilder().Build(), &testingkafka.KafkaClient{}, NewKafkaOptions("", "", "", "", ""), time.Hour),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want,
				NewResgroupSyncer(tt.args.region, tt.args.client, tt.args.producer, tt.args.options, tt.args.interval),
				"NewResgroupSyncer(%v, %v, %v, %v)", tt.args.region, tt.args.client, tt.args.producer, tt.args.interval)
		})
	}
}
