/*
Copyright 2021.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controllers

import (
	"context"
	"fmt"
	"time"

	"golang.org/x/time/rate"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/util/workqueue"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	networkctl "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/ccrnetwork"
)

// CCRNetworkReconciler reconciles a CNCNetwork object
type CCRNetworkReconciler struct {
	client.Client
	Scheme  *runtime.Scheme
	Handler *networkctl.Handler
}

//+kubebuilder:rbac:groups=cce.baidubce.com,resources=cncnetworks,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=cce.baidubce.com,resources=cncnetworks/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=cce.baidubce.com,resources=cncnetworks/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the CNCNetwork object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.8.3/pkg/reconcile
func (r *CCRNetworkReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// your logic here
	logger.Info("start reconcile postgres")
	object := ccrv1alpha1.CNCNetwork{}
	err := r.Client.Get(ctx, req.NamespacedName, &object)
	if err != nil {
		if apierrors.IsNotFound(err) {
			logger.Info("object is not found, will not process anymore")
			return ctrl.Result{}, nil
		}

		logger.V(2).Error(err, fmt.Sprintf("get cncnetwork object failed for %v", req.NamespacedName))
		return ctrl.Result{Requeue: true, RequeueAfter: time.Second}, err
	}

	logger.Info("start to process", "status", object.Status.Phase)
	err = r.Handler.Handle(ctx, &object)
	if err != nil {
		logger.V(2).Error(err, "handle cncnetwork object failed")
		return ctrl.Result{Requeue: true, RequeueAfter: time.Second}, err
	}

	return ctrl.Result{}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *CCRNetworkReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&ccrv1alpha1.CNCNetwork{}).
		WithOptions(controller.Options{
			RateLimiter: workqueue.NewMaxOfRateLimiter(
				workqueue.NewItemExponentialFailureRateLimiter(time.Second, 30*time.Second),
				// 10 qps, 100 bucket size.  This is only for retry speed and its only the overall factor (not per item)
				&workqueue.BucketRateLimiter{Limiter: rate.NewLimiter(rate.Limit(10), 100)},
			),
		}).
		Complete(r)
}
