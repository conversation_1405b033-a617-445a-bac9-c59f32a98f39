package controllers

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/goharbor/harbor/src/testing/mock"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/bcesdk/billing"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	testingbilling "icode.baidu.com/baidu/jpaas-caas/ccr-stack/testing/pkg/bcesdk/billing"
)

func TestBillingSyncer_run(t *testing.T) {

	now := time.Now()

	type fields struct {
		region         string
		client         client.Client
		resourceClient billing.ResourceClientInterface
		interval       time.Duration
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "case1",
			fields: fields{
				region:         "gz",
				client:         fake.NewClientBuilder().Build(),
				resourceClient: &testingbilling.ResourceClient{},
				interval:       time.Hour,
			},
			args: args{
				context.Background(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "case2",
			fields: fields{
				region:         "gz",
				client:         fake.NewClientBuilder().Build(),
				resourceClient: &testingbilling.ResourceClient{},
				interval:       time.Hour,
			},
			args: args{
				context.Background(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "case3",
			fields: fields{
				region:         "gz",
				client:         fake.NewClientBuilder().Build(),
				resourceClient: &testingbilling.ResourceClient{},
				interval:       time.Hour,
			},
			args: args{
				context.Background(),
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {

		metaNow := v1.NewTime(now)

		if tt.name == "case1" {
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)
			tt.fields.client = fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects(
				&v1alpha1.CCR{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CCRSpec{ExpireTime: &metaNow}}).Build()
		}

		if tt.name == "case2" {
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)
			beforeNow := v1.NewTime(now.Add(-time.Minute))
			tt.fields.client = fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects(
				&v1alpha1.CCR{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CCRSpec{ExpireTime: &beforeNow}}).Build()
		}

		if tt.name == "case3" {
			scheme := runtime.NewScheme()
			v1alpha1.AddToScheme(scheme)
			tt.fields.client = fake.NewClientBuilder().WithScheme(scheme).WithRuntimeObjects(
				&v1alpha1.CCR{
					ObjectMeta: v1.ObjectMeta{Name: "test", Labels: map[string]string{v1alpha1.LastOrderIdKey: "xxx"}},
					Spec:       v1alpha1.CCRSpec{AccountID: "XXX"}}).Build()
		}

		mock.OnAnything(tt.fields.resourceClient, "ListResourceDetail").Return(&billing.ListResourceDetailResponse{Result: []*billing.ResourceInfo{{Uuid: "xxxx", ExpireTime: now, Name: "test"}}}, nil)

		t.Run(tt.name, func(t *testing.T) {
			b := &BillingSyncer{
				region:         tt.fields.region,
				client:         tt.fields.client,
				resourceClient: tt.fields.resourceClient,
				interval:       tt.fields.interval,
			}
			tt.wantErr(t, b.run(tt.args.ctx), fmt.Sprintf("run(%v)", tt.args.ctx))
		})
	}
}

func TestNewBillingSyncer(t *testing.T) {
	type args struct {
		region         string
		client         client.Client
		resourceClient billing.ResourceClientInterface
		interval       time.Duration
	}
	tests := []struct {
		name string
		args args
		want *BillingSyncer
	}{
		{
			name: "case1",
			args: args{
				region:         "gz",
				client:         fake.NewClientBuilder().Build(),
				resourceClient: &testingbilling.ResourceClient{},
				interval:       time.Hour,
			},
			want: NewBillingSyncer("gz", fake.NewClientBuilder().Build(), &testingbilling.ResourceClient{}, time.Hour),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, NewBillingSyncer(tt.args.region, tt.args.client, tt.args.resourceClient, tt.args.interval), "NewBillingSyncer(%v, %v, %v, %v)", tt.args.region, tt.args.client, tt.args.resourceClient, tt.args.interval)
		})
	}
}
