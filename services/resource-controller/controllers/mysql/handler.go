package mysql

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"math"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/rds"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/uuid"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
)

type Handler struct {
	clients clientset.ClientSetInterface

	config    *conf.Config
	k8sclient client.Client
	vpcId     string

	dbs *sync.Map

	failOverDuration time.Duration
}

// NewHandler 新建一个 Handler 对象，用于处理请求。
//
// 参数:
//   - config *conf.Config: 配置信息的指针，包含了 VPC ID、子网 ID、可用区名称等信息。
//   - clients *clientset.ClientSet: 客户端集合的指针，包含了与 Kubernetes API Server 和 AWS API Gateway 的连接。
//   - vpcID string: VPC ID，用于标识要创建的 Pod 所属的 VPC。
//   - cli client.Client: Kubernetes Client 的实例，用于与 Kubernetes API Server 进行交互。
//
// 返回值:
//   - *Handler: 一个 Handler 类型的指针，表示新建的 Handler 对象。
func NewHandler(config *conf.Config, clients clientset.ClientSetInterface, vpcID string, cli client.Client) *Handler {
	return &Handler{
		config:    config,
		clients:   clients,
		k8sclient: cli,
		vpcId:     vpcID,
		dbs:       new(sync.Map),

		failOverDuration: 10 * time.Minute,
	}
}

// Handle Handle 处理函数，用于处理 Mysql 对象的各种情况。
// 如果对象已被删除，则调用 handleDelete 方法进行处理；
// 如果对象的状态为空字符串，则调用 handlePendingPhase 方法进行处理；
// 如果对象的状态为创建中，则调用 handleCreate 方法进行处理；
// 如果对象的类型与其规格中的不同，则调用 handleUpgrading 方法进行处理。
// 否则，返回 nil。
// 参数 ctx context.Context：上下文信息；
// 参数 object *ccrv1alpha1.Mysql：需要处理的 Mysql 对象指针；
// 返回值 error：处理过程中出现的错误，如果没有错误则返回 nil。
func (handler *Handler) Handle(ctx context.Context, object *ccrv1alpha1.Mysql) error {
	obj := object.DeepCopy()
	// need to create and update
	switch {
	case obj.DeletionTimestamp != nil:
		return handler.handleDelete(ctx, obj)
	case obj.Status.Phase == "":
		return handler.handlePendingPhase(ctx, obj)
	case obj.Status.Phase == ccrv1alpha1.MysqlStatusCreating:
		return handler.handleCreate(ctx, obj)
	case obj.Status.Type != obj.Spec.InstanceType:
		return handler.handleUpgrading(ctx, obj)
	}

	return nil
}

// handlePendingPhase 处理待处理阶段，首先设置最终化器，如果设置成功则等待下一轮更新，否则返回错误；
// 如果最终化器已经被设置过，则等待阶段的更新。
//
// 参数：
//
//	ctx context.Context                  上下文信息，包含日志记录器和请求相关值
//	object *ccrv1alpha1.Mysql            需要更新状态的MySQL对象指针
//
// 返回值：
//
//	error                                nil表示操作成功，非nil表示操作失败，包含错误信息
func (handler *Handler) handlePendingPhase(ctx context.Context, object *ccrv1alpha1.Mysql) error {
	// set finalizer first
	logger := log.FromContext(ctx)
	finalizerSetted, err := handler.setFinalizer(ctx, object)
	if err != nil {
		logger.V(2).Error(err, "update finalizer failed")
		return err
	}

	// finalizer setted, waitfor next round
	if !finalizerSetted {
		logger.V(2).Info("finalizer has been setted, wait for phase to be update")
		return nil
	}

	object.Status.Phase = ccrv1alpha1.MysqlStatusCreating
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime
	object.Status.LastTransitionTime = &currentTime
	object.Status.Type = object.Spec.InstanceType

	return handler.k8sclient.Status().Update(ctx, object)
}

// handleDelete 处理删除操作，包括获取数据库、创建数据库和等待数据库可以访问。如果不存在实例或者实例已经被删除，则直接移除最终化器。
// ctx: 上下文对象，包含日志记录器
// object: *ccrv1alpha1.Mysql，需要处理的 Mysql 对象
// 返回值：error，如果发生错误则返回该错误
func (handler *Handler) handleDelete(ctx context.Context, object *ccrv1alpha1.Mysql) error {
	logger := log.FromContext(ctx)
	// 1. get database
	// 2. if not exist create database
	// 3. if exist, wait database can be access
	if object.Status.InstanceID == "" {
		return handler.removeFinalizer(ctx, object)
	}

	_, err := handler.clients.RdsClient().GetDetail(object.Status.InstanceID)
	var bceError *bce.BceServiceError
	if errors.As(err, &bceError) && bceError.StatusCode == http.StatusNotFound {
		return handler.removeFinalizer(ctx, object)
	}

	if err != nil {
		logger.V(2).Error(err, fmt.Sprintf("get rds instaince %v failed", object.Status.InstanceID))
		handler.updateReason(ctx, object, "DeleteFailed", err.Error())
		return err
	}

	err = handler.clients.RdsClient().DeleteRds(object.Status.InstanceID)
	if err != nil {
		bceErr := &bce.BceServiceError{}
		if errors.As(err, &bceErr) && bceErr.StatusCode == 404 {
			return nil
		}

		logger.V(2).Error(err, fmt.Sprintf("get the detail of instance %v failed", object.Status.InstanceID))
		handler.updateReason(ctx, object, "InstanceDeleteFailed", err.Error())
		return err
	}

	return handler.removeFinalizer(ctx, object)
}

// tryGetRdsInstance 尝试获取指定名称的 RDS 实例，如果找到则返回该实例，否则返回 nil
func (handler *Handler) tryGetRdsInstance(ctx context.Context, instanceName string) (*rds.Instance, error) {
	logger := log.FromContext(ctx)
	marker, maxKeys := "", 1000
	isTruncated := true

	for isTruncated {
		res, err := handler.clients.RdsClient().ListRds(&rds.ListRdsArgs{
			Marker:  marker,
			MaxKeys: maxKeys,
		})
		if err != nil {
			logger.V(2).Error(err, "list rds failed")
			return nil, err
		}

		for _, v := range res.Instances {
			if v.InstanceName == instanceName {
				return &v, nil
			}
		}

		marker = res.NextMarker
		isTruncated = res.IsTruncated
	}

	return nil, nil
}

// handleCreate 处理创建 MySQL 实例的逻辑
// ctx context.Context：上下文信息，包含日志记录器和请求相关参数
// object *ccrv1alpha1.Mysql：需要创建的 MySQL 实例对象
// 返回值 error：如果发生错误则返回该错误
func (handler *Handler) handleCreate(ctx context.Context, object *ccrv1alpha1.Mysql) error {
	logger := log.FromContext(ctx)
	logger.V(2).Info("start create loop", "instance", object.GetName())
	password, err := base64.StdEncoding.DecodeString(object.Spec.Password)
	if err != nil {
		logger.Error(err, "password is invalid")
		object.Status.Phase = ccrv1alpha1.MysqlStatusFailed
		handler.updateReason(ctx, object, "RdsAccountNotReady", err.Error())
		return err
	}

	if object.Status.InstanceID == "" {
		// xxx
		instance, err := handler.tryGetRdsInstance(ctx, object.GetName())
		if err != nil {
			logger.Error(err, "try to list rds instance failed")
			handler.updateReason(ctx, object, "RdsNotReady", err.Error())
			return err
		}

		if instance == nil {
			// get zone
			subnets, err := handler.clients.RdsClient().ListSubnets(&rds.ListSubnetsArgs{VpcId: handler.vpcId})
			if err != nil {
				logger.V(2).Error(err, fmt.Sprintf("list subnets failed: %v", handler.vpcId))
				handler.updateReason(ctx, object, "SubnetsNotReady", err.Error())
				return err
			}

			subnetMap := make([]rds.SubnetMap, 0)
			zoneNameSet := make(map[string]interface{})
			for _, v := range subnets.Subnets {
				subnetMap = append(subnetMap, rds.SubnetMap{
					ZoneName: v.ZoneName,
					SubnetId: v.SubnetId,
				})

				zoneNameSet[v.ZoneName] = nil
			}

			zoneNames := make([]string, 0)
			for k := range zoneNameSet {
				zoneNames = append(zoneNames, k)
			}

			// create rds
			createResult, err := handler.clients.RdsClient().CreateRds(&rds.CreateRdsArgs{
				ClientToken: string(uuid.NewUUID()),
				Billing: rds.Billing{
					PaymentTiming: "Postpaid",
				},
				PurchaseCount:  1,
				InstanceName:   object.GetName(),
				Engine:         "MySQL",
				EngineVersion:  handler.config.Mysql.EngineVersion,
				Category:       "Standard",
				CpuCount:       handler.config.Mysql.Spec[strings.ToLower(object.Spec.InstanceType)].CPU,
				MemoryCapacity: float64(handler.config.Mysql.Spec[strings.ToLower(object.Spec.InstanceType)].Memory),
				VolumeCapacity: handler.config.Mysql.Spec[strings.ToLower(object.Spec.InstanceType)].VolumeCapacity,
				IsDirectPay:    true,
				ZoneNames:      []string{handler.config.Mysql.ZoneName},
				VpcId:          handler.vpcId,
				Subnets: []rds.SubnetMap{
					{
						ZoneName: handler.config.Mysql.ZoneName,
						SubnetId: handler.config.Mysql.SubnetID,
					},
				},
				// 磁盘类型, normal_io:本地盘ssd磁盘, cloud_high:高性能云磁盘, cloud_nor:通用型SSD, cloud_enha:增强型SSD, 必选
				DiskIoType: handler.config.Mysql.DiskIoType,
			})

			if err != nil {
				logger.V(2).Error(err, "create rds mysql failed")
				handler.updateReason(ctx, object, "RdsNotReady", err.Error())
				return err
			}

			object.Status.InstanceID = createResult.InstanceIds[0]
		} else {
			object.Status.InstanceID = instance.InstanceId
		}
	}

	if object.Status.Host == "" {
		instance, err := handler.clients.RdsClient().GetDetail(object.Status.InstanceID)
		if err != nil {
			logger.Error(err, fmt.Sprintf("get instance %v failed", object.Status.InstanceID))
			handler.updateReason(ctx, object, "RdsNotReady", err.Error())
			return err
		}

		if instance.InstanceStatus != "Available" {
			logger.Info(fmt.Sprintf("instance is not ready: %v", instance.InstanceStatus))
			handler.updateReason(ctx, object, "RdsNotReady", fmt.Sprintf("expected instance status=%s "+
				"but got=%s", "Available", instance.InstanceStatus))
			return fmt.Errorf("instance is not ready")
		}

		object.Status.Host = instance.Endpoint.Address
		object.Status.Port = fmt.Sprintf("%v", instance.Endpoint.Port)
	}

	if object.Status.UserName == "" {
		rdsAccounts, err := handler.clients.RdsClient().ListAccount(object.Status.InstanceID)
		if err != nil {
			logger.V(2).Error(err, "list account failed for instance "+object.Status.InstanceID)
			handler.updateReason(ctx, object, "RdsAccountNotReady", err.Error())
			return err
		}

		// TODO 返回值不为空，暂时认为账户已经创建完成，取第一个用户
		if len(rdsAccounts.Accounts) != 0 {
			object.Status.UserName = rdsAccounts.Accounts[0].AccountName
		} else {
			err = handler.clients.RdsClient().CreateAccount(
				object.Status.InstanceID,
				&rds.CreateAccountArgs{
					ClientToken: string(uuid.NewUUID()),
					AccountName: object.Spec.Username,
					Password:    string(password),
					AccountType: "Super",
					Type:        "OnlyMaster",
					Desc:        "Automatically created by resource controller",
				})
			if err != nil {
				logger.V(2).Error(err, "create account failed")
				handler.updateReason(ctx, object, "RdsAccountNotReady", err.Error())
				return err
			}
			object.Status.UserName = object.Spec.Username
		}
	}

	// wait for user is ready
	user, err := handler.clients.RdsClient().GetAccount(object.Status.InstanceID, object.Status.UserName)
	if err != nil {
		logger.V(2).Error(err, fmt.Sprintf("get account %v of instance %v failed", object.Status.UserName, object.Status.InstanceID))
		handler.updateReason(ctx, object, "RdsAccountNotReady", fmt.Sprintf("get user failed: %s", err))
		return err
	}

	if user.Status != "Available" {
		logger.V(2).Info(fmt.Sprintf("account %v of instance %v is not ready", object.Status.UserName, object.Status.InstanceID))
		handler.updateReason(ctx, object, "RdsAccountNotReady", "user is not ready")
		return fmt.Errorf("user is not ready")
	}

	object.Status.Type = object.Spec.InstanceType
	object.Status.Phase = ccrv1alpha1.MysqlStatusRunning
	return handler.updateReason(ctx, object, "", "")
}

// handleUpgrading 处理升级中的状态，包括实例状态、内存大小和磁盘大小等。如果需要升级，则调用rds client进行升级操作，并返回错误；否则更新mysql对象的类型和状态为运行中，并返回nil。
// ctx: 上下文信息，context.Context类型
// object: Mysql对象指针，*ccrv1alpha1.Mysql类型
// 返回值：error类型，表示升级过程中出现的错误，如果没有错误则返回nil
func (handler *Handler) handleUpgrading(ctx context.Context, object *ccrv1alpha1.Mysql) error {
	logger := log.FromContext(ctx)

	if object.Status.Phase != ccrv1alpha1.MysqlStatusUpgrading {
		currentTime := metav1.Now()
		object.Status.Phase = ccrv1alpha1.MysqlStatusUpgrading
		object.Status.LastTransitionTime = &currentTime
	}

	if object.Status.InstanceID == "" {
		logger.V(2).Error(fmt.Errorf("no instance id found"), "no instance id found")
		handler.updateReason(ctx, object, "RdsNotReady", "instance is empty")
		return fmt.Errorf("no instance id found")
	}

	instance, err := handler.clients.RdsClient().GetDetail(object.Status.InstanceID)
	if err != nil {
		logger.Error(err, fmt.Sprintf("get instance %v failed", object.Status.InstanceID))
		handler.updateReason(ctx, object, "RdsNotReady", err.Error())
		return err
	}

	if strings.ToLower(instance.InstanceStatus) != "available" {
		logger.Error(fmt.Errorf("instance status is not available"), "instance status is "+instance.InstanceStatus)
		handler.updateReason(ctx, object, "RdsNotReady", "instance is not ready")
		return fmt.Errorf("instance is %s, not ready", instance.InstanceStatus)
	}

	expectedCPU := handler.config.Mysql.Spec[strings.ToLower(object.Spec.InstanceType)].CPU
	expectedMemory := handler.config.Mysql.Spec[strings.ToLower(object.Spec.InstanceType)].Memory
	expectedVolume := handler.config.Mysql.Spec[strings.ToLower(object.Spec.InstanceType)].VolumeCapacity

	// 降配 暂不支持
	if instance.CpuCount <= expectedCPU &&
		instance.VolumeCapacity <= expectedVolume &&
		instance.MemoryCapacity-float64(expectedMemory) <= 1 {

		if instance.CpuCount != expectedCPU ||
			instance.VolumeCapacity != expectedVolume ||
			math.Abs(instance.MemoryCapacity-float64(expectedMemory)) > 1 {
			// 开始升级
			err = handler.clients.RdsClient().ResizeRds(object.Status.InstanceID, &rds.ResizeRdsArgs{
				CpuCount:       expectedCPU,
				MemoryCapacity: float64(expectedMemory),
				VolumeCapacity: expectedVolume,
				IsDirectPay:    true,
			})

			if err != nil {
				logger.V(2).Error(err, "resize rds failed")
				handler.updateReason(ctx, object, "RdsResizeFailed", err.Error())
				return err
			}

			return fmt.Errorf("resize success, wait for mysql available")
		}
	}

	object.Status.Type = object.Spec.InstanceType
	object.Status.Phase = ccrv1alpha1.MysqlStatusRunning
	return handler.updateReason(ctx, object, "", "")
}

// updateReason updateReason 更新状态的原因和消息，并将当前时间设置为最后探测时间，如果当前状态不是运行中且最后转换时间超过了指定时间，则将状态设置为失败。返回错误信息，如果没有错误则返回nil
func (handler *Handler) updateReason(ctx context.Context, object *ccrv1alpha1.Mysql, reason string, message string) error {
	logger := log.FromContext(ctx)
	object.Status.Reason = reason
	object.Status.Message = message
	currentTime := metav1.Now()
	object.Status.LastProbeTime = &currentTime

	if object.Status.Phase != ccrv1alpha1.MysqlStatusRunning &&
		object.Status.LastTransitionTime != nil &&
		currentTime.Sub(object.Status.LastTransitionTime.Time) > handler.failOverDuration {
		object.Status.Phase = ccrv1alpha1.MysqlStatusFailed
	}

	err := handler.k8sclient.Status().Update(ctx, object)
	if err != nil {
		logger.Error(err, "update status failed")
	}

	return err
}

// setFinalizer 设置Finalizer，如果已经存在则返回true，否则将其添加到Finalizers中并更新资源状态。
// 参数：
//
//	ctx (context.Context) - 上下文对象，可用于传递请求或依赖项相关的值，例如超时和取消标记。
//	object (&ccrv1alpha1.Mysql) - Mysql类型的指针，表示需要操作的资源对象。
//
// 返回值：
//
//	bool (bool) - 如果Finalizer已经存在则为true，否则为false。
//	error (error) - 操作过程中发生的错误，如果没有错误则为nil。
func (handler *Handler) setFinalizer(ctx context.Context, object *ccrv1alpha1.Mysql) (bool, error) {
	finalizers := object.GetFinalizers()
	if finalizers == nil {
		finalizers = make([]string, 0)
	}

	for _, v := range finalizers {
		if v == ccrv1alpha1.ResourceFinalizer {
			return true, nil
		}
	}

	finalizers = append(finalizers, ccrv1alpha1.ResourceFinalizer)
	object.SetFinalizers(finalizers)

	return false, handler.k8sclient.Update(ctx, object)
}

// removeFinalizer removeFinalizer 从对象中移除最后一个 Finalizer，如果 Finalizer 为 ccrv1alpha1.ResourceFinalizer，则不会被移除。
// 参数：
//
//	ctx (context.Context) - 上下文信息，可用于传递超时或取消信号。
//	object (&ccrv1alpha1.Mysql) - Mysql 类型的对象指针，包含需要修改的 Finalizer。
//
// 返回值：
//
//	error (error) - 如果移除 Finalizer 失败，则返回错误；否则返回 nil。
func (handler *Handler) removeFinalizer(ctx context.Context, object *ccrv1alpha1.Mysql) error {
	finalizers := object.GetFinalizers()
	if len(finalizers) == 0 {
		return nil
	}

	reservedFinalizer := make([]string, 0)
	for _, v := range finalizers {
		if v != ccrv1alpha1.ResourceFinalizer {
			reservedFinalizer = append(reservedFinalizer, v)
		}
	}

	object.SetFinalizers(reservedFinalizer)
	return handler.k8sclient.Update(ctx, object)
}
