package controllers

import (
	"context"
	"fmt"
	"time"

	"golang.org/x/time/rate"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/util/workqueue"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/log"

	secretctl "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/secret"
)

// SecretReconciler reconciles a Secret object
type SecretReconciler struct {
	cache.Cache
	client.Client
	Scheme *runtime.Scheme

	Handler *secretctl.Handler
}

func (r *SecretReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	logger.Info("start reconcile secret")

	if req.Name != "temporary-credential" {
		return ctrl.Result{}, nil
	}

	secret := v1.Secret{}
	if err := r.Client.Get(ctx, req.NamespacedName, &secret); err != nil {
		if apierrors.IsNotFound(err) {
			logger.Info("object is not found, will not process anymore")
			return ctrl.Result{}, nil
		}

		logger.V(2).Error(err, fmt.Sprintf("get secret object failed for %v", req.NamespacedName))
		return ctrl.Result{RequeueAfter: 5 * time.Second}, err
	}

	if err := r.Handler.Handle(ctx, &secret); err != nil {
		logger.V(2).Error(err, "handle secret object failed")
		return ctrl.Result{RequeueAfter: 5 * time.Second}, err
	}

	return ctrl.Result{RequeueAfter: 5 * time.Minute}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *SecretReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&v1.Secret{}).
		WithOptions(controller.Options{
			RateLimiter: workqueue.NewMaxOfRateLimiter(
				workqueue.NewItemExponentialFailureRateLimiter(time.Second, 30*time.Second),
				// 10 qps, 100 bucket size.  This is only for retry speed and its only the overall factor (not per item)
				&workqueue.BucketRateLimiter{Limiter: rate.NewLimiter(rate.Limit(10), 100)},
			),
		}).
		Complete(r)
}
