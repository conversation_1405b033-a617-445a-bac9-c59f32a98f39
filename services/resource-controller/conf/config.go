package conf

import (
	"fmt"
	"io/ioutil"

	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/apimachinery/pkg/util/yaml"
)

type ConfigInterface interface {
	MustSTS() bool
	MustRds() bool
	MustEIP() bool
	MustBLB() bool
	MustUserSetting() bool
	MustSQL() bool
	MustBilling() bool
	MustResgroup() bool
	FeatureSupported(f string) bool
	BindFeatures(features sets.String) error
}

const (
	FeatureCNCNetwork = "cncnetwork"
	FeatureGaiaDB     = "gaiadb"
	FeaturePostgres   = "postgres"
	FeatureMysql      = "mysql"
	FeatureRedis      = "redis"
	FeatureBos        = "bos"
	FeatureCCRQuota   = "ccrquota"
	FeatureCCRSync    = "ccrsync"
	FeatureCCRSecret  = "ccrsecret"
	FeatureWorkflow   = "workflow"
)

type Config struct {
	AccessKey string `yaml:"accessKey,omitempty"`
	SecretKey string `yaml:"secretKey,omitempty"`
	ClusterID string `yaml:"clusterId,omitempty"`
	UserID    string `yaml:"userId,omitempty"` // 拥有bos访问权限

	VpcDNSHexKey            string `yaml:"vpcDnsHexKey,omitempty"`
	VpcServiceNetworkHexKey string `yaml:"vpcServiceNetworkHexKey,omitempty"`

	GaiaDBEndpoint      string `yaml:"gaiaDBEndpoint,omitempty"`
	RDSEndpoint         string `yaml:"rdsEndpoint,omitempty"`
	SCSEndpoint         string `yaml:"scsEndpoint,omitempty"`
	STSEndpoint         string `yaml:"stsEndpoint,omitempty"`
	CCEEndpoint         string `yaml:"cceEndpoint,omitempty"`
	EIPEndpoint         string `yaml:"eipEndpoint,omitempty"`
	BLBEndpoint         string `yaml:"blbEndpoint,omitempty"`
	VPCEndpoint         string `yaml:"vpcEndpoint,omitempty"`
	IAMEndpoint         string `yaml:"iamEndpoint,omitempty"`
	BOSEndpoint         string `yaml:"bosEndpoint,omitempty"`
	UserSettingEndpoint string `yaml:"userSettingEndpoint,omitempty"`
	PrivateZoneEndpoint string `yaml:"privateZoneEndpoint,omitempty"`
	CertificateEndpoint string `yaml:"certificateEndpoint,omitempty"`
	DomainCheckEndpoint string `yaml:"domainCheckEndpoint,omitempty"`

	ResourceManageEndpoint string `yaml:"resourceManageEndpoint,omitempty"`
	DNSEndpoint            string `yaml:"dnsEndpoint,omitempty"`
	RDNSEndpoint           string `yaml:"rdnsEndpoint,omitempty"`
	VPCDNSEndpoint         string `yaml:"vpcDnsEndpoint,omitempty"`
	DNSToken               string `yaml:"dnsToken,omitempty"`
	EIPBillingMethod       string `yaml:"eipBillingMethod,omitempty"`

	MysqlConnection  string `yaml:"mysqlConnection,omitempty"`
	DevopsConnection string `yaml:"devopsConnection,omitempty"`

	Billing Billing `yaml:"billing"`

	Postgresql Postgresql `yaml:"postgresql"`
	Mysql      Mysql      `yaml:"mysql"`
	Redis      Redis      `yaml:"redis"`
	GaiaDB     GaiaDB     `yaml:"gaiadb"`
	BLB        BLB        `yaml:"blb"`

	Region          string `yaml:"region,omitempty"`
	RoleName        string `yaml:"roleName,omitempty"`
	ServiceName     string `yaml:"serviceName,omitempty"`
	ServicePassword string `yaml:"servicePassword,omitempty"`

	InetDNSServer string `yaml:"inetDNSServer,omitempty"`
	PnetDNSServer string `yaml:"pnetDNSServer,omitempty"`

	features sets.String

	KafkaOptions KafkaOptions `yaml:"kafkaOptions"`
}

type KafkaOptions struct {
	BrokerEndpoint string `yaml:"brokerEndpoint"`

	CaPemPath     string `yaml:"caPemPath"`
	ClientKeyPath string `yaml:"clientKeyPath"`
	ClientPemPath string `yaml:"clientPemPath"`

	ConsumerGroupID string `yaml:"consumerGroupID"`
}

type Billing struct {
	RoleName        string `yaml:"roleName,omitempty"`
	ServiceName     string `yaml:"serviceName,omitempty"`
	ServicePassword string `yaml:"servicePassword,omitempty"`
	AccessKey       string `yaml:"accessKey,omitempty"`
	SecretKey       string `yaml:"secretKey,omitempty"`
}

type ZoneAndSubnet struct {
	ZoneName string `yaml:"zoneName"`
	SubnetID string `yaml:"subnetID"`
}

type Postgresql struct {
	EngineVersion string `yaml:"engineVersion,omitempty"`
	// 磁盘类型, normal_io:本地盘ssd磁盘, cloud_high:高性能云磁盘, cloud_nor:通用型SSD, cloud_enha:增强型SSD, 必选
	DiskIoType string                    `yaml:"diskIoType,omitempty"`
	Spec       map[string]PostgresqlSpec `yaml:"spec"`
	ZoneAndSubnet
}

type Mysql struct {
	EngineVersion string `yaml:"engineVersion,omitempty"`
	// 磁盘类型, normal_io:本地盘ssd磁盘, cloud_high:高性能云磁盘, cloud_nor:通用型SSD, cloud_enha:增强型SSD, 必选
	DiskIoType string               `yaml:"diskIoType,omitempty"`
	Spec       map[string]MysqlSpec `yaml:"spec"`
	ZoneAndSubnet
}

type Redis struct {
	EngineVersion string               `yaml:"engineVersion,omitempty"`
	Spec          map[string]RedisSpec `yaml:"spec"`
	ZoneAndSubnet
}

type PostgresqlSpec struct {
	CPU            int `yaml:"CPU"`
	Memory         int `yaml:"memory"`
	VolumeCapacity int `yaml:"volumeCapacity"` // in GB
}

type MysqlSpec struct {
	CPU            int `yaml:"CPU"`
	Memory         int `yaml:"memory"`
	VolumeCapacity int `yaml:"volumeCapacity"` // in GB
}

type RedisSpec struct {
	NodeType string `yaml:"nodeType"`
}

type GaiaDB struct {
	InterfaceType  string `yaml:"interfaceType"`
	PaidType       string `yaml:"paidType"`
	StorageInGB    int    `yaml:"storageInGB"`
	InstanceAmount int    `yaml:"instanceAmount"`
	ProxyAmount    int    `yaml:"proxyAmount"`
	TemplateID     string `yaml:"templateID"`
}

type BLB struct {
	VpcID    string `json:"vpcId"`
	SubnetID string `json:"subnetId"`
}

func NewConfigFromFile(filename string) (*Config, error) {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("read config file %v failed: %v", filename, err)
	}

	var conf Config
	err = yaml.Unmarshal(content, &conf)
	if err != nil {
		return nil, fmt.Errorf("file content cannot be parsed: %s", err)
	}

	return &conf, nil
}

func (c *Config) MustSTS() bool {
	return c.features.HasAny(FeatureBos, FeatureCCRSecret, FeatureCNCNetwork)
}

func (c *Config) MustGaiaDB() bool {
	return c.features.HasAny(FeatureGaiaDB)
}

// MustRds 返回值类型：bool
// 功能：判断是否需要使用RDS，如果包含了Postgres或者Mysql则返回true，否则返回false
func (c *Config) MustRds() bool {
	return c.features.HasAny(FeaturePostgres, FeatureMysql)
}

func (c *Config) MustSCS() bool {
	return c.features.Has(FeatureRedis)
}

func (c *Config) MustEIP() bool {
	return c.features.Has(FeatureCNCNetwork)
}

func (c *Config) MustBLB() bool {
	return c.features.Has(FeatureCNCNetwork)
}

func (c *Config) MustUserSetting() bool {
	return c.features.Has(FeatureCCRQuota)
}

func (c *Config) MustSQL() bool {
	return c.features.Has(FeatureCCRSync)
}

func (c *Config) MustBilling() bool {
	return c.features.Has(FeatureCCRSync)
}

func (c *Config) MustResgroup() bool {
	return c.features.Has(FeatureCCRSync)
}

func (c *Config) FeatureSupported(f string) bool {
	return c.features.Has(f)
}

// BindFeatures 功能：绑定特性集合，并检查是否满足所有必要条件。
// 参数：
//
//	features sets.String - 特性集合，类型为sets.String
//
// 返回值：
//
//	error - 如果不满足任何特性的必要条件，则返回错误；否则返回nil
func (c *Config) BindFeatures(features sets.String) error {
	c.features = features
	// must have
	supported := c.Region != "" &&
		c.AccessKey != "" &&
		c.SecretKey != "" &&
		c.ClusterID != "" &&
		c.CCEEndpoint != ""

	if !supported {
		return fmt.Errorf("necessary filed is not provided")
	}

	for k := range features {
		switch k {
		case FeatureCNCNetwork:
			supported = c.RoleName != "" &&
				c.ServiceName != "" &&
				c.ServicePassword != "" &&
				c.STSEndpoint != "" &&
				c.IAMEndpoint != "" &&
				c.EIPEndpoint != "" &&
				c.BLBEndpoint != "" &&
				c.VPCEndpoint != "" &&
				c.RDNSEndpoint != "" &&
				c.DNSEndpoint != "" &&
				c.DNSToken != ""
		case FeatureBos:
			supported = c.RoleName != "" &&
				c.ServiceName != "" &&
				c.ServicePassword != "" &&
				c.STSEndpoint != "" &&
				c.IAMEndpoint != ""
		case FeaturePostgres, FeatureMysql:
			supported = (c.Postgresql.SubnetID != "" &&
				c.Postgresql.ZoneName != "") || (c.Postgresql.SubnetID != "" &&
				c.Postgresql.ZoneName != "") &&
				c.RDSEndpoint != ""
		case FeatureRedis:
			supported = c.Redis.SubnetID != "" &&
				c.Redis.ZoneName != "" &&
				c.SCSEndpoint != ""
		case FeatureCCRQuota:
			supported = c.UserSettingEndpoint != ""
		case FeatureCCRSync:
			supported = c.MysqlConnection != "" &&
				c.DevopsConnection != "" &&
				c.ResourceManageEndpoint != ""
		case FeatureCCRSecret:
			supported = c.RoleName != "" &&
				c.ServiceName != "" &&
				c.ServicePassword != "" &&
				c.STSEndpoint != "" &&
				c.IAMEndpoint != ""
		case FeatureWorkflow:
			supported = c.MysqlConnection != ""
		case FeatureGaiaDB:
			supported = c.GaiaDBEndpoint != ""
		default:
			return fmt.Errorf("unsupported feature: %v", k)
		}

		if !supported {
			return fmt.Errorf("feature %v is not satisfied", k)
		}
	}

	return nil
}
