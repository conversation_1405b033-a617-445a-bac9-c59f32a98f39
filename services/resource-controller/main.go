/*
Copyright 2021.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"flag"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"os"
	"strings"
	"time"

	// Import all Kubernetes client auth plugins (e.g. Azure, GCP, OIDC, etc.)
	// to ensure that exec-entrypoint and run can make use of them.
	"go.uber.org/zap/zapcore"
	"k8s.io/client-go/kubernetes"
	_ "k8s.io/client-go/plugin/pkg/client/auth"

	wfv1 "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/sets"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"

	ccrv1alpha1 "icode.baidu.com/baidu/jpaas-caas/ccr-stack/pkg/crd/apis/ccr/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/clientset"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/conf"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers"
	bosctl "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/bos"
	networkctl "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/ccrnetwork"
	quotactl "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/ccrquota"
	gaiadbctl "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/gaiadb"
	mysqlctl "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/mysql"
	postgresctl "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/postgres"
	redisctl "icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/redis"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/secret"
	"icode.baidu.com/baidu/jpaas-caas/ccr-stack/services/resource-controller/controllers/workflow"
	//+kubebuilder:scaffold:imports
)

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("setup")
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))

	utilruntime.Must(ccrv1alpha1.AddToScheme(scheme))
	//+kubebuilder:scaffold:scheme
	utilruntime.Must(wfv1.AddToScheme(scheme))
}

// main 是一个函数，它会执行控制器管理器的初始化和运行。
// 该函数首先调用了 ctrl.SetLogger 来设置日志记录器，然后从配置文件中读取配置信息，并将其绑定到特性集合中。
// 接下来，创建了一个客户端集合，用于处理 Kubernetes API 请求，并根据配置信息创建了一个 RestConfig。
// 如果没有错误，则创建了一个控制器管理器，并使用该 RestConfig 进行初始化。
// 之后，创建了一个客户端，用于处理 Kubernetes API 请求，并创建了一个 legacyClient，用于处理 Kubernetes API 请求。
// 遍历特性集合，根据配置信息创建相应的控制器，并将其添加到控制器管理器中。
// 最后，设置健康检查、就绪检查，并启动控制器管理器。
func main() {
	//+kubebuilder:scaffold:builder

	var metricsAddr string
	var enableLeaderElection bool
	var probeAddr string
	var configFile string
	var features string

	flag.StringVar(&metricsAddr, "metrics-bind-address", ":8080", "The address the metric endpoint binds to.")
	flag.StringVar(&probeAddr, "health-probe-bind-address", ":8081", "The address the probe endpoint binds to.")
	flag.BoolVar(&enableLeaderElection, "leader-elect", false,
		"Enable leader election for controller manager. "+
			"Enabling this will ensure there is only one active controller manager.")
	flag.StringVar(&configFile, "config", "", "path of configuration file")
	flag.StringVar(&features, "features",
		"bos,redis,mysql,postgres,cncnetwork,ccrquota,ccrsecret,ccrsync,workflow",
		"feature list resource controller support, splited by ,")
	opts := zap.Options{
		Development: true,
		TimeEncoder: zapcore.ISO8601TimeEncoder,
	}
	opts.BindFlags(flag.CommandLine)
	flag.Parse()

	ctrl.SetLogger(zap.New(zap.UseFlagOptions(&opts)))
	config, err := conf.NewConfigFromFile(configFile)
	if err != nil {
		panic(err)
	}

	setupLog.Info(fmt.Sprintf("config %s", spew.Sdump(config)))
	if err := config.BindFeatures(getFeatureSet(features)); err != nil {
		panic(fmt.Sprintf("feature and config is not match: %v", err))
	}

	resourceClients, err := clientset.NewClientSet(config)
	if err != nil {
		panic(fmt.Sprintf("create client sets failed: %s", err))
	}

	restConfig, err := resourceClients.RestConfig(config.ClusterID)
	if err != nil {
		panic(fmt.Errorf("get rest config of k8s failed: %s", err))
	}

	vpcId, err := resourceClients.VpcID(config.ClusterID)
	if err != nil {
		panic(fmt.Errorf("cannot get vpc of cluster: %v", config.ClusterID))
	}

	syncPeriod := 15 * time.Minute
	mgr, err := ctrl.NewManager(restConfig, ctrl.Options{
		Scheme:                  scheme,
		MetricsBindAddress:      metricsAddr,
		Port:                    9443,
		HealthProbeBindAddress:  probeAddr,
		LeaderElection:          enableLeaderElection,
		LeaderElectionNamespace: "kube-system", // 默认指定为选主命名空间为 kube-system,与所部署在 namespace 无关
		LeaderElectionID:        "bb0770e7.baidubce.com",
		SyncPeriod:              &syncPeriod,
	})
	if err != nil {
		setupLog.Error(err, "unable to start manager")
		os.Exit(1)
	}

	client, err := client.New(restConfig, client.Options{
		Scheme: scheme,
	})
	if err != nil {
		setupLog.Error(err, "unable to create cluster client")
		os.Exit(1)
	}

	legacyClient, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		setupLog.Error(err, "unable to create legacy client")
		os.Exit(1)
	}

	if config.FeatureSupported(conf.FeatureBos) {
		setupLog.Info("setup bos feature")
		if err = (&controllers.BosReconciler{
			Client:  mgr.GetClient(),
			Scheme:  mgr.GetScheme(),
			Handler: bosctl.NewHandler(config, resourceClients, client),
		}).SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "unable to create controller", "controller", "Bos")
			os.Exit(1)
		}
	}

	if config.FeatureSupported(conf.FeaturePostgres) {
		setupLog.Info("setup postgres feature")
		if err = (&controllers.PostgresReconciler{
			Client:  mgr.GetClient(),
			Scheme:  mgr.GetScheme(),
			Handler: postgresctl.NewHandler(config, resourceClients, vpcId, client),
		}).SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "unable to create controller", "controller", "Postgres")
			os.Exit(1)
		}
	}

	if config.FeatureSupported(conf.FeatureMysql) {
		setupLog.Info("setup mysql feature")
		if err = (&controllers.MysqlReconciler{
			Client:  mgr.GetClient(),
			Scheme:  mgr.GetScheme(),
			Handler: mysqlctl.NewHandler(config, resourceClients, vpcId, client),
		}).SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "unable to create controller", "controller", "Mysql")
			os.Exit(1)
		}
	}

	if config.FeatureSupported(conf.FeatureGaiaDB) {
		setupLog.Info("setup gaiadb feature")
		if err = (&controllers.GaiaDBReconciler{
			Client:  mgr.GetClient(),
			Scheme:  mgr.GetScheme(),
			Handler: gaiadbctl.NewHandler(config, resourceClients, vpcId, client),
		}).SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "unable to create controller", "controller", "GaiaDB")
			os.Exit(1)
		}
	}

	if config.FeatureSupported(conf.FeatureRedis) {
		setupLog.Info("setup redis feature")
		if err = (&controllers.RedisReconciler{
			Client:  mgr.GetClient(),
			Scheme:  mgr.GetScheme(),
			Handler: redisctl.NewHandler(config, resourceClients, vpcId, client),
		}).SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "unable to create controller", "controller", "Redis")
			os.Exit(1)
		}
	}

	if config.FeatureSupported(conf.FeatureCNCNetwork) {
		setupLog.Info("setup cncnetwork feature")
		if err = (&controllers.CCRNetworkReconciler{
			Client:  mgr.GetClient(),
			Scheme:  mgr.GetScheme(),
			Handler: networkctl.NewHandler(config, resourceClients, client),
		}).SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "unable to create controller", "controller", "CNCNetwork")
			os.Exit(1)
		}
	}

	if config.FeatureSupported(conf.FeatureCCRQuota) {
		setupLog.Info("setup ccrquota feature")
		if err = (&controllers.CCRQuotaReconciler{
			Client:  mgr.GetClient(),
			Scheme:  mgr.GetScheme(),
			Handler: quotactl.NewHandler(config, resourceClients, client),
		}).SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "unable to create controller", "controller", "CCRQuota")
			os.Exit(1)
		}
	}

	if config.FeatureSupported(conf.FeatureCCRSecret) {
		setupLog.Info("setup ccrsecret feature")
		if err = (&controllers.SecretReconciler{
			Cache:   mgr.GetCache(),
			Client:  mgr.GetClient(),
			Scheme:  mgr.GetScheme(),
			Handler: secret.NewHandler(config, resourceClients, client),
		}).SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "unable to create controller", "controller", "Secret")
			os.Exit(1)
		}
	}

	if config.FeatureSupported(conf.FeatureCCRSync) {

		kafkaOptions := controllers.NewKafkaOptions(config.KafkaOptions.CaPemPath, config.KafkaOptions.ClientKeyPath,
			config.KafkaOptions.ClientPemPath, config.KafkaOptions.ConsumerGroupID, config.KafkaOptions.BrokerEndpoint)

		setupLog.Info("setup ccrsync feature")
		ccrReconcile := controllers.NewCCRReconciler(resourceClients.SqlClient(), mgr.GetClient(), config.Region,
			kafkaOptions)
		if err = ccrReconcile.SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "unable to create controller", "controller", "CCR")
			os.Exit(1)
		}

		setupLog.Info("enable ccrsync")
		if err = mgr.Add(ccrReconcile); err != nil {
			setupLog.Error(err, "unable to start ccr sync", "controller", "CCR Sync")
			os.Exit(1)
		}

		setupLog.Info("enable devops sync")
		if err = mgr.Add(controllers.NewDevopsSyncer(config.Region, resourceClients.SqlClient(), resourceClients.DevopsClient(), time.Hour)); err != nil {
			setupLog.Error(err, "unable to start devops syncer", "controller", "CCR")
			os.Exit(1)
		}

		setupLog.Info("enable billing sync")
		if err = mgr.Add(controllers.NewBillingSyncer(config.Region, mgr.GetClient(), resourceClients.ResourceClient(), time.Minute*15)); err != nil {
			setupLog.Error(err, "unable to start billing syncer", "controller", "CCR")
			os.Exit(1)
		}

		setupLog.Info("enable resource group sync")
		if err = mgr.Add(controllers.NewResgroupSyncer(config.Region, mgr.GetClient(), resourceClients.Producer(),
			kafkaOptions, time.Minute*15)); err != nil {
			setupLog.Error(err, "unable to start resgroup syncer", "controller", "CCR")
			os.Exit(1)
		}
	}

	if config.FeatureSupported(conf.FeatureWorkflow) {
		setupLog.Info("setup workflow feature")
		workflowReconcile := &controllers.WorkflowReconciler{
			Client:   mgr.GetClient(),
			Scheme:   mgr.GetScheme(),
			SqlCli:   resourceClients.SqlClient(),
			Interval: time.Hour,
			Handler:  workflow.NewHandler(legacyClient, mgr.GetClient(), resourceClients.SqlClient()),
		}

		if err = workflowReconcile.SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "unable to create controller", "controller", "Workflow")
			os.Exit(1)
		}

		setupLog.Info("setup workflow sync")
		if err = mgr.Add(workflowReconcile); err != nil {
			setupLog.Error(err, "unable to start workflow sync", "controller", "Workflow")
			os.Exit(1)
		}
	}

	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up health check")
		os.Exit(1)
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up ready check")
		os.Exit(1)
	}

	setupLog.Info("starting manager")
	if err := mgr.Start(ctrl.SetupSignalHandler()); err != nil {
		setupLog.Error(err, "problem running manager")
		os.Exit(1)
	}
}

func getFeatureSet(fs string) sets.String {
	features := strings.Split(fs, ",")
	if features == nil {
		return sets.NewString()
	}

	return sets.NewString(features...)
}
