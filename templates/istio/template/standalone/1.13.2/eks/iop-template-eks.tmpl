apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: iop-{{.Namespace}}
  namespace: {{.Namespace}}
spec:
  tag: {{.Tag}}
  hub: {{.Hub}}
  namespace: {{.Namespace}}
  meshConfig: {{if .DiscoverySelectorLabels}}
    discoverySelectors:
      - matchLabels:{{ range $key, $value := .DiscoverySelectorLabels}}
          {{ $key }}: {{ $value }}{{end}}{{ with .MatchExpressions }}
      - matchExpressions:
      {{- range . }}
        - key: {{ .Key }}
          operator: {{ .Operator }}
          values:{{range $index, $value := .Values }}
          - {{ $value }} {{- end}}
          {{- end}}
    {{- else}}{{- end}}
    {{- else}}{{- end}}{{if .AccessLogFile}}
    accessLogFile: /dev/stdout {{- end}}
    defaultConfig:
      tracing:
        sampling: {{.SamplingRate}}
        zipkin:
          address: {{.Address}}
      holdApplicationUntilProxyStarts: true
  values:
    global:
      meshID: {{.MeshInstanceId}}
      multiCluster:
        clusterName: {{.ClusterName}}
      istioNamespace: {{.Namespace}}
      network: {{.Network}}
      proxy:
        resources:
          limits:{{ range $key, $value := .Proxy.Limits}}
            {{ $key }}: "{{ $value }}"{{end}}
          requests:{{ range $key, $value := .Proxy.Requests}}
            {{ $key }}: "{{ $value }}"{{end}}
      proxy_init:
        resources:
          limits:{{ range $key, $value := .InitProxy.Limits}}
            {{ $key }}: "{{ $value }}"{{end}}
          requests:{{ range $key, $value := .InitProxy.Requests}}
            {{ $key }}: "{{ $value }}"{{end}}{{if .DiscoveryAddress}}
      remotePilotAddress: {{.DiscoveryAddress}}
      {{- else}}
      {{- end}}
    sidecarInjectorWebhook:
      rewriteAppHTTPProbe: false
  components:
    pilot:
      k8s:
        hpaSpec:
          minReplicas: 1
        overlays:
          - kind: Deployment
            name: istiod
            patches:
              - path: spec.template.spec.containers.[name:discovery].resources.requests.cpu
                value: "4"
              - path: spec.template.spec.containers.[name:discovery].resources.requests.eks\.baidu-int\.com/cpu
                value: "60"
              - path: spec.template.spec.containers.[name:discovery].resources.requests.ephemeral-storage
                value: "50Gi"
              - path: spec.template.spec.containers.[name:discovery].resources.requests.memory
                value: "8Gi"
              - path: spec.template.spec.containers.[name:discovery].resources.limits.cpu
                value: "4"
              - path: spec.template.spec.containers.[name:discovery].resources.limits.eks\.baidu-int\.com/cpu
                value: "60"
              - path: spec.template.spec.containers.[name:discovery].resources.limits.ephemeral-storage
                value: "50Gi"
              - path: spec.template.spec.containers.[name:discovery].resources.limits.memory
                value: "8Gi"
    ingressGateways:
      - name: istio-ingressgateway
        enabled: false