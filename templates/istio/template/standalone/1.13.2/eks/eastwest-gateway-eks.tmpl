apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: iop-{{.EastWestGatewayIstioLabel}}
  namespace: {{.Namespace}}
spec:
  profile: empty
  namespace: {{.Namespace}}
  components:
    ingressGateways:
      - name: istio-eastwestgateway
        label:
          istio: {{.EastWestGatewayIstioLabel}}
          app: istio-eastwestgateway
          topology.istio.io/network: {{.Network}}
        enabled: true
        k8s:
          env:
            # traffic through this gateway should be routed inside the network
            - name: ISTIO_META_REQUESTED_NETWORK_VIEW
              value: {{.Network}}
          service:
            ports:
              - name: status-port
                port: 15021
                targetPort: 15021
              - name: tls
                port: 15443
                targetPort: 15443
              - name: tls-istiod
                port: 15012
                targetPort: 15012
              - name: tls-webhook
                port: 15017
                targetPort: 15017
          overlays:
            - kind: Deployment
              name: istio-eastwestgateway
              patches:
                - path: spec.template.spec.containers.[name:istio-proxy].resources.requests.cpu
                  value: "2"
                - path: spec.template.spec.containers.[name:istio-proxy].resources.requests.eks\.baidu-int\.com/cpu
                  value: "30"
                - path: spec.template.spec.containers.[name:istio-proxy].resources.requests.ephemeral-storage
                  value: "10Gi"
                - path: spec.template.spec.containers.[name:istio-proxy].resources.requests.memory
                  value: "4Gi"
                - path: spec.template.spec.containers.[name:istio-proxy].resources.limits.cpu
                  value: "2"
                - path: spec.template.spec.containers.[name:istio-proxy].resources.limits.eks\.baidu-int\.com/cpu
                  value: "30"
                - path: spec.template.spec.containers.[name:istio-proxy].resources.limits.ephemeral-storage
                  value: "10Gi"
                - path: spec.template.spec.containers.[name:istio-proxy].resources.limits.memory
                  value: "4Gi"
            # https://cloud.baidu.com/doc/CCE/s/ijwvy107d
            - kind: Service
              name: istio-eastwestgateway
              patches:
                - path: metadata.annotations.service\.beta\.kubernetes\.io/cce-load-balancer-internal-vpc
                  value: "true"
                # 开启的话性能更优，可以节省一跳，但部分 node 节点硬件或者软件可能不支持，暂不开启
                #- path: metadata.annotations.service\.beta\.kubernetes\.io/cce-load-balancer-backend-type
                #  value: "eni"
  values:
    gateways:
      istio-ingressgateway:
        injectionTemplate: gateway
    global:
      network: {{.Network}}
      istioNamespace: {{.Namespace}}