apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: {{.IstiodGatewayName}}
  namespace: {{.Namespace}}
spec:
  selector:
    istio: {{.EastWestGatewayIstioLabel}}
  servers:
    - port:
        name: tls-istiod
        number: 15012
        protocol: tls
      tls:
        mode: PASSTHROUGH
      hosts:
        - "*"
    - port:
        name: tls-istiodwebhook
        number: 15017
        protocol: tls
      tls:
        mode: PASSTHROUGH
      hosts:
        - "*"
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{.IstiodVsName}}
  namespace: {{.Namespace}}
spec:
  hosts:
  - "*"
  gateways:
  - {{.IstiodGatewayName}}
  tls:
  - match:
    - port: 15012
      sniHosts:
      - "*"
    route:
    - destination:
        host: istiod.{{.Namespace}}.svc.cluster.local
        port:
          number: 15012
  - match:
    - port: 15017
      sniHosts:
      - "*"
    route:
    - destination:
        host: istiod.{{.Namespace}}.svc.cluster.local
        port:
          number: 443
