apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: iop-{{.EastWestGatewayIstioLabel}}
  namespace: {{.Namespace}}
spec:
  tag: {{.Tag}}
  hub: {{.Hub}}
  profile: empty
  namespace: {{.Namespace}}
  components:
    ingressGateways:
      - name: istio-eastwestgateway
        label:
          istio: {{.EastWestGatewayIstioLabel}}
          app: istio-eastwestgateway
          topology.istio.io/network: {{.Network}}
        enabled: true
        k8s:
          env:
            # traffic through this gateway should be routed inside the network
            - name: ISTIO_META_REQUESTED_NETWORK_VIEW
              value: {{.Network}}
          service:
            ports:
              - name: status-port
                port: 15021
                targetPort: 15021
              - name: tls
                port: 15443
                targetPort: 15443
              - name: tls-istiod
                port: 15012
                targetPort: 15012
              - name: tls-webhook
                port: 15017
                targetPort: 15017
          overlays:
          # https://cloud.baidu.com/doc/CCE/s/ijwvy107d
          - kind: Service
            name: istio-eastwestgateway
            patches:
            - path: metadata.annotations.service\.beta\.kubernetes\.io/cce-load-balancer-internal-vpc
              value: "true"
            # customize the name of istio-eastwestgateway with annotation
            - path: metadata.annotations.service\.beta\.kubernetes\.io/cce-load-balancer-lb-name
              value: "istio-eastwestgateway-{{.MeshInstanceId}}"
  values:
    gateways:
      istio-ingressgateway:
        injectionTemplate: gateway
    global:
      network: {{.Network}}
      istioNamespace: {{.Namespace}}