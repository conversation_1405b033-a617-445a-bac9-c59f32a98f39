apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: iop-{{.Namespace}}
  namespace: {{.Namespace}}
spec:
  tag: {{.Tag}}
  hub: {{.Hub}}
  namespace: {{.Namespace}}
  meshConfig: {{if .DiscoverySelectorLabels}}
    discoverySelectors:
      - matchLabels:{{ range $key, $value := .DiscoverySelectorLabels}}
          {{ $key }}: {{ $value }}{{end}}{{ with .MatchExpressions }}
      - matchExpressions:
      {{- range . }}
        - key: {{ .Key }}
          operator: {{ .Operator }}
          values:{{range $index, $value := .Values }}
          - {{ $value }} {{- end}}
          {{- end}}
    {{- else}}{{- end}}
    {{- else}}{{- end}}{{if .AccessLogFile}}
    accessLogFile: /dev/stdout {{- end}}
    defaultConfig:
      tracing:
        sampling: {{.SamplingRate}}
        zipkin:
          address: {{.Address}}
      holdApplicationUntilProxyStarts: true
      proxyMetadata:
      # 开启智能 DNS
        ISTIO_META_DNS_CAPTURE: "true"
        ISTIO_META_DNS_AUTO_ALLOCATE: "true"
      # 支持多协议
      proxyStatsMatcher:
        inclusionPrefixes:
        - thrift
        - dubbo
        - kafka
        - meta_protocol
        inclusionRegexps:
        - .*dubbo.*
        - .*thrift.*
        - .*kafka.*
        - .*zookeeper.*
        - .*meta_protocol.*
  values:
    global:
      meshID: {{.MeshInstanceId}}
      multiCluster:
        clusterName: {{.ClusterName}}
      istioNamespace: {{.Namespace}}
      proxy:
        image: {{.ProxyImage}}
        resources:
          limits:{{ range $key, $value := .Proxy.Limits}}
            {{ $key }}: "{{ $value }}"{{end}}
          requests:{{ range $key, $value := .Proxy.Requests}}
            {{ $key }}: "{{ $value }}"{{end}}
      proxy_init:
        image: {{.ProxyImage}}
        resources:
          limits:{{ range $key, $value := .InitProxy.Limits}}
            {{ $key }}: "{{ $value }}"{{end}}
          requests:{{ range $key, $value := .InitProxy.Requests}}
            {{ $key }}: "{{ $value }}"{{end}}
      network: {{.Network}}{{if .DiscoveryAddress}}
      remotePilotAddress: {{.DiscoveryAddress}}
      {{- else}}
      {{- end}}
    sidecarInjectorWebhook:
      rewriteAppHTTPProbe: false
  components:
    pilot:
      k8s:
        hpaSpec:
          minReplicas: 1
        overlays:
          - kind: Deployment
            name: istiod
            patches:
              - path: spec.template.spec.containers.[name:discovery].resources.requests.cpu
                value: "0.5"
              - path: spec.template.spec.containers.[name:discovery].resources.requests.memory
                value: "1Gi"
              - path: spec.template.spec.containers.[name:discovery].resources.limits.cpu
                value: "2"
              - path: spec.template.spec.containers.[name:discovery].resources.limits.memory
                value: "4Gi"
    ingressGateways:
      - name: istio-ingressgateway
        enabled: false