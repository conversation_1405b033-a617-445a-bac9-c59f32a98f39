apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: remote-iop-{{.Namespace}}
  namespace: {{.Namespace}}
spec:
  meshConfig:
    defaultConfig:
      holdApplicationUntilProxyStarts: true
  profile: external
  values:
    global:
      istioNamespace: {{.Namespace}}
    istiodRemote:
      injectionURL: https://{{.HostingIstiodAddress}}:443/inject/:ENV:cluster={{.IopRemoteCluster}}:ENV:net={{.NetWork}}