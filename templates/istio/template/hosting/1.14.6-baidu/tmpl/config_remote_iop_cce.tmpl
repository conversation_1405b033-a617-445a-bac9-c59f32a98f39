apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: remote-iop-{{.Namespace}}
  namespace: {{.Namespace}}
spec:
  profile: external
  values:
    global:
      istioNamespace: {{.Namespace}}
      remotePilotAddress: {{.HostingIstiodAddress}}
      configCluster: true
    pilot:
      configMap: true
    istiodRemote:
      injectionURL: https://{{.HostingIstiodAddress}}:443/inject/:ENV:cluster={{.IopRemoteCluster}}:ENV:net={{.NetWork}}