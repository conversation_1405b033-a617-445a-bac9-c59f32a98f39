apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: iop-{{.Namespace}}
  namespace: {{.Namespace}}
spec:
  tag: {{.Tag}}
  hub: {{.Hub}}
  namespace: {{.Namespace}}
  meshConfig: {{if .DiscoverySelectorLabels}}
    discoverySelectors:
      - matchLabels:{{ range $key, $value := .DiscoverySelectorLabels}}
          {{ $key }}: {{ $value }}{{end}}{{ with .MatchExpressions }}
      - matchExpressions:
        {{- range . }}
          - key: {{ .Key }}
            operator: {{ .Operator }}
            values:{{range $index, $value := .Values }}
            - {{ $value }} {{- end}}
            {{- end}}
        {{- else}}{{- end}}
        {{- else}}{{- end}}{{if .AccessLogFile}}
        accessLogFile: /dev/stdout {{- end}}
    rootNamespace: {{.Namespace}}
    defaultConfig:
      holdApplicationUntilProxyStarts: true
      discoveryAddress: {{.ExternalIstiodAddress}}:15012
  components:
    ingressGateways:
      - name: istio-ingressgateway
        enabled: false
    pilot:
      enabled: true
      k8s:
        hpaSpec:
          minReplicas: 1
        env:
        - name: INJECTION_WEBHOOK_CONFIG_NAME
          value: "istio-sidecar-injector-{{.Namespace}}"
        - name: VALIDATION_WEBHOOK_CONFIG_NAME
          value: "istio-validator-{{.Namespace}}"
        - name: EXTERNAL_ISTIOD
          value: "true"
        - name: ISTIO_CRD_NAMESPACE_NAME
          value: "{{.Namespace}}"
        podAnnotations:
          # 必选项，对端 VPC 的用户 ID
          cross-vpc-eni.cce.io/userID: "{{.VpcEniTemplates.AccountID}}"
          # 必选项，跨 VPC 弹性网卡所属的子网
          cross-vpc-eni.cce.io/subnetID: "{{.VpcEniTemplates.SubnetID}}"
          # 必选项，跨 VPC 弹性网卡绑定的安全组合集，多个安全组使用逗号分隔
          cross-vpc-eni.cce.io/securityGroupIDs: "{{.VpcEniTemplates.SecurityGroupIds}}"
          # 必选项，跨 VPC 弹性网卡所属 VPC 的网段
          cross-vpc-eni.cce.io/vpcCidr: "{{.VpcEniTemplates.VpcCidr}}"
          # 可选项，跨 VPC 弹性网卡的主 IP 地址。若不指定，由 VPC 自动分配
          cross-vpc-eni.cce.io/privateIPAddress: ""
          # 可选项，CCE 老集群的托管 master 是特殊的，需要通过弹性网卡访问
          cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: "************/24"
        overlays:
        - kind: Deployment
          name: istiod
          patches:
          - path: spec.template.spec.containers[0].resources.limits.cross-vpc-eni\.cce\.io/eni
            value: {{.VpcEniTemplates.EniAnnotation.EniRequests.Eni.Num}}
          - path: spec.template.spec.containers[0].resources.requests.cross-vpc-eni\.cce\.io/eni
            value: {{.VpcEniTemplates.EniAnnotation.EniRequests.Eni.Num}}
  values:
    global:
      proxy:
        resources:
          limits:{{ range $key, $value := .Proxy.Limits}}
            {{ $key }}: "{{ $value }}"{{end}}
          requests:{{ range $key, $value := .Proxy.Requests}}
            {{ $key }}: "{{ $value }}"{{end}}
      proxy_init:
        resources:
          limits:{{ range $key, $value := .InitProxy.Limits}}
            {{ $key }}: "{{ $value }}"{{end}}
          requests:{{ range $key, $value := .InitProxy.Requests}}
            {{ $key }}: "{{ $value }}"{{end}}
      network: {{.Network}}
      meshID: {{.InstanceId}}
      caAddress: {{.ExternalIstiodAddress}}:15012
      istioNamespace: {{.Namespace}}
      operatorManageWebhooks: true
      configValidation: false
