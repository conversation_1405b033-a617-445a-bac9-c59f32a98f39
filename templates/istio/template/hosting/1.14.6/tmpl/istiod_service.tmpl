apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
    service.beta.kubernetes.io/cce-load-balancer-lb-name: "istiod-{{.Namespace}}"
  name: istiod
  namespace: {{.Namespace}}
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    app: istiod
    istio: pilot
    release: istio
spec:
  type: LoadBalancer
  ports:
    - port: 15010
      name: grpc-xds # plaintext
      protocol: TCP
    - port: 15012
      name: https-dns # mTLS with k8s-signed cert
      protocol: TCP
    - port: 443
      name: https-webhook # validation and injection
      targetPort: 15017
      protocol: TCP
    - port: 15014
      name: http-monitoring # prometheus stats
      protocol: TCP
  selector:
    app: istiod
    # Label used by the 'default' service. For versioned deployments we match with app and version.
    # This avoids default deployment picking the canary
    istio: pilot