apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "controller.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "controller.labels" . | nindent 4 }}
spec:
  {{- if not .Values.controller.autoscaling.enabled }}
  replicas: {{ .Values.controller.replicas }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "controller.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.controller.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "controller.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.controller.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "controller.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.controller.podSecurityContext | nindent 8 }}
      containers:
{{- if not .Values.global.enableMesh }}
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.controller.securityContext | nindent 12 }}
          image: "{{ .Values.hub }}/{{ .Values.controller.image }}:{{ .Values.controller.tag | default .Chart.AppVersion }}"
          args:
          - "serve"
          - --gatewaySelectorKey=app
          - --gatewaySelectorValue=istio-ingressgateway
          {{- if not .Values.enableStatus }}
          - --enableStatus={{ .Values.enableStatus }}
          {{- end }}
          - --ingressClass={{ .Values.ingressClass }}
          {{- if .Values.watchNamespace }}
          - --watchNamespace={{ .Values.watchNamespace }}
          {{- end }}
          command:
          - /higress
          env:
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.serviceAccountName
          {{- if .Values.controller.env }}
          {{- range $key, $val := .Values.controller.env }}
          - name: {{ $key }}
            value: "{{ $val }}"
          {{- end }}
          {{- end }}
          ports:
            {{- range $idx, $port := .Values.controller.ports }}
            - name: {{ $port.name }}
              containerPort: {{ $port.port }}
              protocol: {{ $port.protocol }}
            {{- end }}
          readinessProbe:
            {{- toYaml .Values.controller.probe | nindent 12 }}
          {{- if not .Values.global.kind  }}
          resources:
            {{- toYaml .Values.controller.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
          - name: log
            mountPath: /var/log
            {{- end }}
      {{- with .Values.controller.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
      - name: log
        emptyDir: {}
      {{- if not .Values.global.enableMesh }}
      - name: config
        configMap:
          name: higress-config
      # Technically not needed on this pod - but it helps debugging/testing SDS
      # Should be removed after everything works.
      - emptyDir:
          medium: Memory
        name: local-certs
      {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
      - name: istio-token
        projected:
          sources:
            - serviceAccountToken:
                audience: {{ .Values.global.sds.token.aud }}
                expirationSeconds: 43200
                path: istio-token
      # Optional: user-generated root
      - name: cacerts
        secret:
          secretName: cacerts
          optional: true
      - name: istio-kubeconfig
        secret:
          secretName: istio-kubeconfig
          optional: true
  {{- if .Values.pilot.jwksResolverExtraRootCA }}
      - name: extracacerts
        configMap:
          name: pilot-jwks-extra-cacerts{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  {{- end }}
      {{- end }}
      {{- end }}
