apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "controller.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "controller.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "controller.serviceAccountName" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "controller.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
