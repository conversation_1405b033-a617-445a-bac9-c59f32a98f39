---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "controller.serviceAccountName" . }}-{{ .Release.Namespace }}
  labels:
    {{- include "controller.labels" . | nindent 4 }}    
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "controller.serviceAccountName" . }}-{{ .Release.Namespace }}
subjects:
  - kind: ServiceAccount
    name: {{ include "controller.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
