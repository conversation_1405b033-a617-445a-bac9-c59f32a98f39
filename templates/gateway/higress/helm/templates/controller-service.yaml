apiVersion: v1
kind: Service
metadata:
  name: {{ include "controller.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "controller.labels" . | nindent 4 }}
spec:
  type: {{ .Values.controller.service.type }}
  ports:
    {{- toYaml .Values.controller.ports | nindent 4 }}
    {{- if not .Values.global.enableMesh }}
    - port: 15010
      name: grpc-xds # plaintext
      protocol: TCP
    - port: 15012
      name: https-dns # mTLS with k8s-signed cert
      protocol: TCP
    - port: 443
      name: https-webhook # validation and injection
      targetPort: 15017
      protocol: TCP
    - port: 15014
      name: http-monitoring # prometheus stats
      protocol: TCP
    {{- end }}
  selector:
    {{- include "controller.selectorLabels" . | nindent 4 }}
