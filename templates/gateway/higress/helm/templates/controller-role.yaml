apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "controller.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "controller.labels" . | nindent 4 }}
rules:
# For storing CA secret
- apiGroups: [""]
  resources: ["secrets"]
  # TODO lock this down to istio-ca-cert if not using the DNS cert mesh config
  verbs: ["create", "get", "watch", "list", "update", "delete"]
