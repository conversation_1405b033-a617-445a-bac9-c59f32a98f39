apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: {{.GatewayName}}
  namespace: {{.Namespace}}
spec:
  tag: {{.Tag}}
  hub: {{.Hub}}
  profile: empty
  components:
    ingressGateways:
    - name: istio-ingressgateway
      namespace: {{.Namespace}}
      enabled: true
      k8s:
        resources:
          requests:
            memory: {{.Resources.Requests.Memory}}
            cpu: {{.Resources.Requests.Cpu}}
          limits:
            memory: {{.Resources.Limits.Memory}}
            cpu: {{.Resources.Limits.Cpu}}
        replicaCount: {{.Replicas}}
        podAnnotations:
          # 必选项，对端 VPC 的用户 ID
          cross-vpc-eni.cce.io/userID: "{{.AccountID}}"
          # 必选项，跨 VPC 弹性网卡所属的子网
          cross-vpc-eni.cce.io/subnetID: "{{.SubnetID}}"
          # 必选项，跨 VPC 弹性网卡绑定的安全组合集，多个安全组使用逗号分隔
          cross-vpc-eni.cce.io/securityGroupIDs: "{{.SecurityGroupIDs}}"
          # 必选项，跨 VPC 弹性网卡所属 VPC 的网段
          cross-vpc-eni.cce.io/vpcCidr: "{{.VpcCidr}}"
          # 可选项，跨 VPC 弹性网卡的主 IP 地址。若不指定，由 VPC 自动分配
          cross-vpc-eni.cce.io/privateIPAddress: ""
          # 必选项，托管网关使用 ENI 作为默认路由网卡，并排除自身特殊网段的流量
          cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: eni
          cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: "*******/8"
          # 开启 TLS 加速
          proxy.istio.io/config: {{- .TLSAccelerationAnnotation | indent 14 }}
        overlays:
        - kind: Deployment
          name: istio-ingressgateway
          patches:
          - path: spec.template.spec.containers[0].resources.limits.cross-vpc-eni\.cce\.io/eni
            value: {{.Resources.Requests.EniNum}}
          - path: spec.template.spec.containers[0].resources.requests.cross-vpc-eni\.cce\.io/eni
            value: {{.Resources.Limits.EniNum}}
        # Prevent Service from generating LoadBalancer automatically and adapt to lb controller
        - kind: Service
          name: istio-ingressgateway
          patches:
          - path: spec.type
            value:
          - path: spec.loadBalancerIP
            value: {{.BindedEip}}
          - path: metadata.annotations.service\.beta\.kubernetes\.io/cce-cluster-master-blb-service
            value: "true"
          - path: metadata.annotations.service\.beta\.kubernetes\.io/cce-load-balancer-backend-type
            value: eni
          - path: metadata.annotations.service\.beta\.kubernetes\.io/cce-load-balancer-id
            value: "{{.BlbID}}"
          - path: metadata.annotations.service\.beta\.kubernetes\.io/cce-load-balancer-internal-vpc
            value: "{{.OnlyInternalAccess}}"
          - path: metadata.annotations.service\.beta\.kubernetes\.io/cce-master-blb-account-id
            value: "{{.AccountID}}"
          - path: metadata.annotations.service\.beta\.kubernetes\.io/csm-prometheus-instance-id
            value: "{{.CPromInstanceID}}"
          - path: metadata.annotations.service\.beta\.kubernetes\.io/csm-prometheus-scrape-job-id
            value: "{{.CPromScrapeJobID}}"
          - path: metadata.annotations.service\.beta\.kubernetes\.io/csm-prometheus-agent-id
            value: "{{.CPromAgentID}}"
          - path: spec.ports
            value:
            - name: status-port
              port: 15021
              protocol: TCP
              targetPort: 15021
  values:
    gateways:
      istio-ingressgateway:
        # injectionTemplate: gateway  # Disable gateway injection template override
        autoscaleEnabled: false
    global:
      istioNamespace: {{.Namespace}}