apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "registry2istio.name" . }}
  labels:
    {{- include "registry2istio.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "registry2istio.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "registry2istio.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "registry2istio.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.hub }}/{{ .Values.image.name }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          args:
            - /usr/local/bin/registry2istio
            - --enableDefaultPort={{ .Values.registry2istio.enableDefaultPort }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: registry2istio
              containerPort: {{ .Values.service.port }}
              protocol: TCP
#          livenessProbe:
#            httpGet:
#              path: /
#              port: http
#          readinessProbe:
#            httpGet:
#              path: /
#              port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: consulAddress
              value: {{ .Values.registry2istio.consulAddress }}
            - name: token
              value: {{ .Values.registry2istio.token }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
