apiVersion: {{.ApiVersion}}
kind: VirtualService
metadata:
  name: {{.Name}}
  namespace: {{.Namespace}}
spec:
  hosts:
    - {{.Host}}
  http:
  {{- range .HTTPDelegateRoute }}
  - match:
      - headers:
            {{.HeaderName}}:
          {{- if eq .MatchingMode "exact" }}
              exact: {{.MatchingContent}}
          {{- else if eq .MatchingMode "prefix" }}
              prefix: {{.MatchingContent}}
          {{- else if eq .MatchingMode "regex" }}
              regex: {{.MatchingContent}}
          {{- end }}
    delegate:
      name: {{.DelegateName}}
      namespace: {{.DelegateNamespace}}
  {{- end }}
  - route:
      - destination:
          host: {{.Host}}
