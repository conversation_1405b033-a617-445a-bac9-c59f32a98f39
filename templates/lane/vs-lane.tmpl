apiVersion: {{.ApiVersion}}
kind: VirtualService
metadata:
  name: {{.Name}}
  namespace: {{.Namespace}}
spec:
  http:
    - match: {{- range .HTTPMatch}}
        {{- if .Headers}}
        - headers:
            {{- range .Headers}}
            {{.HeaderName}}:
              {{- if eq .MatchingMode "exact"}}
              exact: {{.MatchingContent}}
              {{- else if eq .MatchingMode "prefix"}}
              prefix: {{.MatchingContent}}
              {{- else if eq .MatchingMode "regex"}}
              regex: {{.MatchingContent}}
              {{- end}}
              {{- end}}
          {{- end}}
          {{- if .HttpMatchName}}
          name: {{.HttpMatchName}}
          {{- end}}
          {{- if .Uri}}{{- if eq .Uri.MatchingMode "exact"}}
          uri:
            exact: {{.Uri.MatchingContent}}
          {{- else if eq .Uri.MatchingMode "prefix"}}
          uri:
            prefix: {{.Uri.MatchingContent}}
          {{- else if eq .Uri.MatchingMode "regex"}}
          uri:
            regex: {{.Uri.MatchingContent}}
          {{- end}}
        {{- end}}
      {{- end}}
      route:
        - destination:
            host: {{.Host}}
            subset: {{.SubsetName}}