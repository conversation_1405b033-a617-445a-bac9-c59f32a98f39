apiVersion: {{.ApiVersion}}
kind: EnvoyFilter
metadata:
  name: {{.Name}}
  namespace: {{.Namespace}}
spec:
  workloadSelector:
    labels:{{ range $key, $value := .Labels }}
      {{$key}}: {{$value}}{{end}}
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: {{.PatchContext}}
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: {{.Operation}} # INSERT_BEFORE or INSERT_FIRST
        value:
          name: envoy.filters.http.lane
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/lane.Decoder
            value:
              traceheader: {{.TraceHeader}}
              routeheader: {{.RouteHeader}}